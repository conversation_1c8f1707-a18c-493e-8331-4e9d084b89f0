# Generated by Django 4.2.23 on 2025-07-06 09:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0002_newslettersubscription_newslettercampaign'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('action_type', models.CharField(choices=[('user_login', 'Connexion utilisateur'), ('user_logout', 'Déconnexion utilisateur'), ('user_register', 'Inscription utilisateur'), ('user_update', 'Mise à jour profil utilisateur'), ('product_create', 'Création produit'), ('product_update', 'Mise à jour produit'), ('product_delete', 'Suppression produit'), ('promotion_create', 'Création promotion'), ('promotion_activate', 'Activation promotion'), ('promotion_deactivate', 'Désactivation promotion'), ('order_create', 'Création commande'), ('order_update', 'Mise à jour commande'), ('order_cancel', 'Annulation commande'), ('email_sent', 'Email envoyé'), ('email_failed', 'Échec envoi email'), ('cms_page_create', 'Création page CMS'), ('cms_page_update', 'Mise à jour page CMS'), ('cms_blog_create', 'Création article blog'), ('cms_blog_update', 'Mise à jour article blog'), ('admin_action', 'Action administrateur'), ('system_error', 'Erreur système'), ('security_alert', 'Alerte sécurité')], max_length=50)),
                ('severity', models.CharField(choices=[('info', 'Information'), ('warning', 'Avertissement'), ('error', 'Erreur'), ('critical', 'Critique')], default='info', max_length=20)),
                ('user_email', models.EmailField(blank=True, max_length=254)),
                ('description', models.TextField()),
                ('details', models.JSONField(blank=True, default=dict)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('request_path', models.CharField(blank=True, max_length=500)),
                ('object_type', models.CharField(blank=True, max_length=100)),
                ('object_id', models.CharField(blank=True, max_length=100)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': "Journal d'activité",
                'verbose_name_plural': "Journal d'activités",
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['timestamp'], name='core_activi_timesta_44c73d_idx'), models.Index(fields=['action_type'], name='core_activi_action__ba020b_idx'), models.Index(fields=['user'], name='core_activi_user_id_ae0b38_idx'), models.Index(fields=['severity'], name='core_activi_severit_b6114c_idx')],
            },
        ),
    ]
