#!/usr/bin/env python3
"""
Script pour tester le système complet de notifications avec emails
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from notifications.models import NotificationPreference
from orders.models import Order

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access')
    return None

def test_notification_preferences(token):
    """Tester les préférences de notification"""
    print("⚙️ Test des préférences de notification...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Activer toutes les préférences
    try:
        # Récupérer les préférences actuelles
        response = requests.get(f"{API_BASE_URL}/notifications/preferences/", headers=headers)
        if response.status_code == 200:
            preferences = response.json()
            pref_id = preferences.get('id')
            
            # Activer toutes les notifications email
            update_data = {
                "email_promotions": True,
                "email_orders": True,
                "email_system": True
            }
            
            update_response = requests.patch(
                f"{API_BASE_URL}/notifications/preferences/{pref_id}/", 
                json=update_data, 
                headers=headers
            )
            
            if update_response.status_code == 200:
                print("   ✅ Toutes les préférences email activées")
                return True
            else:
                print(f"   ❌ Erreur de mise à jour: {update_response.status_code}")
                return False
        else:
            print(f"   ❌ Erreur de récupération: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_email_sending(token):
    """Tester l'envoi d'emails via l'API"""
    print("\n📧 Test d'envoi d'emails...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    tests = [
        {
            "name": "Email de promotion",
            "data": {
                "title": "🎉 Promotion Test Complète",
                "message": "Profitez de 30% de réduction sur tous nos produits ! Offre limitée dans le temps.",
                "type": "promotion",
                "link": "http://localhost:8080/promotions/test-complete"
            }
        },
        {
            "name": "Email de commande",
            "data": {
                "title": "📦 Mise à jour de commande",
                "message": "Votre commande #TEST-COMPLETE a été mise à jour et sera bientôt expédiée.",
                "type": "order"
            }
        },
        {
            "name": "Email système",
            "data": {
                "title": "🔧 Maintenance programmée",
                "message": "Une maintenance est programmée ce weekend. Le service pourrait être temporairement indisponible.",
                "type": "system"
            }
        }
    ]
    
    success_count = 0
    
    for test in tests:
        print(f"   Test: {test['name']}...")
        try:
            response = requests.post(
                f"{API_BASE_URL}/notifications/notifications/test_email/", 
                json=test['data'], 
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"      ✅ {result.get('message', 'Envoyé')}")
                success_count += 1
            else:
                print(f"      ❌ Erreur: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Erreur: {e}")
    
    print(f"   📊 Résultat: {success_count}/{len(tests)} emails envoyés")
    return success_count == len(tests)

def test_order_email_automation():
    """Tester l'envoi automatique d'emails lors des changements de commande"""
    print("\n🛒 Test d'automatisation des emails de commande...")
    
    try:
        # Récupérer l'utilisateur de test
        user = User.objects.get(email=TEST_EMAIL)
        
        # Créer une commande de test
        order = Order.objects.create(
            user=user,
            email=user.email,
            status='pending',
            subtotal=75000,
            total=75000,
            currency='BIF'
        )
        
        print(f"   ✅ Commande créée: #{order.id}")
        print(f"   📧 Email de création envoyé automatiquement")
        
        # Changer le statut pour tester l'email de mise à jour
        order.status = 'confirmed'
        order.save()
        
        print(f"   ✅ Statut changé vers 'confirmed'")
        print(f"   📧 Email de mise à jour envoyé automatiquement")
        
        # Changer encore le statut
        order.status = 'shipped'
        order.save()
        
        print(f"   ✅ Statut changé vers 'shipped'")
        print(f"   📧 Email d'expédition envoyé automatiquement")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def check_email_configuration():
    """Vérifier la configuration email"""
    print("🔧 Vérification de la configuration email...")
    
    from django.conf import settings
    
    print(f"   Backend: {settings.EMAIL_BACKEND}")
    print(f"   Host: {settings.EMAIL_HOST}")
    print(f"   Port: {settings.EMAIL_PORT}")
    print(f"   TLS: {settings.EMAIL_USE_TLS}")
    print(f"   From: {settings.DEFAULT_FROM_EMAIL}")
    print(f"   Site URL: {settings.SITE_URL}")
    
    return True

def main():
    print("🧪 TEST COMPLET DU SYSTÈME DE NOTIFICATIONS")
    print("=" * 60)
    
    # 1. Vérifier la configuration
    config_ok = check_email_configuration()
    
    # 2. Authentification
    print(f"\n🔐 Authentification...")
    token = get_auth_token()
    if not token:
        print("❌ Impossible de continuer sans authentification")
        return
    print("✅ Authentification réussie")
    
    # 3. Tester les préférences
    prefs_ok = test_notification_preferences(token)
    
    # 4. Tester l'envoi d'emails
    emails_ok = test_email_sending(token)
    
    # 5. Tester l'automatisation des emails de commande
    automation_ok = test_order_email_automation()
    
    print(f"\n📊 RÉSUMÉ FINAL:")
    print(f"   Configuration: {'✅' if config_ok else '❌'}")
    print(f"   Préférences: {'✅' if prefs_ok else '❌'}")
    print(f"   Envoi d'emails: {'✅' if emails_ok else '❌'}")
    print(f"   Automatisation: {'✅' if automation_ok else '❌'}")
    
    if all([config_ok, prefs_ok, emails_ok, automation_ok]):
        print(f"\n🎉 SYSTÈME COMPLET FONCTIONNEL!")
        print(f"   ✅ Les préférences de notification sont fonctionnelles")
        print(f"   ✅ Les emails sont envoyés selon les préférences")
        print(f"   ✅ L'automatisation fonctionne pour les commandes")
        print(f"   📧 Vérifiez votre boîte email: {TEST_EMAIL}")
        print(f"   🌐 Testez l'interface: http://localhost:8080/account/notifications")
        
        print(f"\n💡 FONCTIONNALITÉS DISPONIBLES:")
        print(f"   • Préférences de notification personnalisables")
        print(f"   • Emails automatiques pour les commandes")
        print(f"   • Emails de promotion avec templates HTML")
        print(f"   • Emails système pour les notifications importantes")
        print(f"   • Boutons de test dans l'interface utilisateur")
        
    else:
        print(f"\n❌ CERTAINES FONCTIONNALITÉS NE MARCHENT PAS")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
