from rest_framework import serializers
from .models import PaymentMethod, Transaction, Invoice, CustomerPaymentMethod
from orders.serializers import OrderListSerializer

class PaymentMethodSerializer(serializers.ModelSerializer):
    """Serializer for the PaymentMethod model."""

    class Meta:
        model = PaymentMethod
        fields = [
            'id', 'name', 'provider', 'is_active', 'is_default', 'icon',
            'description', 'position', 'fee_fixed', 'fee_percentage',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PaymentMethodDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for the PaymentMethod model including config."""

    class Meta:
        model = PaymentMethod
        fields = [
            'id', 'name', 'provider', 'is_active', 'is_default', 'config',
            'icon', 'description', 'position', 'fee_fixed', 'fee_percentage',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class TransactionSerializer(serializers.ModelSerializer):
    """Serializer for the Transaction model."""

    payment_method_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = [
            'id', 'order', 'payment_method', 'payment_method_name', 'transaction_id',
            'amount', 'currency', 'status', 'status_display', 'error_message',
            'created_at', 'updated_at', 'completed_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'completed_at']

    def get_payment_method_name(self, obj):
        return obj.payment_method.name if obj.payment_method else None

    def get_status_display(self, obj):
        return obj.get_status_display()


class TransactionDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for the Transaction model including provider response."""

    payment_method_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    order_details = OrderListSerializer(source='order', read_only=True)

    class Meta:
        model = Transaction
        fields = [
            'id', 'order', 'order_details', 'payment_method', 'payment_method_name',
            'transaction_id', 'amount', 'currency', 'status', 'status_display',
            'provider_response', 'error_message', 'created_at', 'updated_at', 'completed_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'completed_at']

    def get_payment_method_name(self, obj):
        return obj.payment_method.name if obj.payment_method else None

    def get_status_display(self, obj):
        return obj.get_status_display()


class InvoiceSerializer(serializers.ModelSerializer):
    """Serializer for the Invoice model."""

    status_display = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = [
            'id', 'order', 'invoice_number', 'status', 'status_display',
            'issue_date', 'due_date', 'paid_date', 'subtotal', 'tax_amount',
            'shipping_amount', 'discount_amount', 'total', 'pdf_file',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'invoice_number']

    def get_status_display(self, obj):
        return obj.get_status_display()


class InvoiceDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for the Invoice model including order details."""

    status_display = serializers.SerializerMethodField()
    order_details = OrderListSerializer(source='order', read_only=True)
    customer_info = serializers.SerializerMethodField()
    payment_method = serializers.SerializerMethodField()
    payment_id = serializers.SerializerMethodField()
    order_id = serializers.CharField(source='order.order_number')

    class Meta:
        model = Invoice
        fields = [
            'id', 'order', 'order_id', 'order_details', 'invoice_number', 'status', 'status_display',
            'issue_date', 'due_date', 'paid_date', 'subtotal', 'tax_amount',
            'shipping_amount', 'discount_amount', 'total', 'pdf_file', 'notes',
            'created_at', 'updated_at', 'customer_info', 'payment_method', 'payment_id'
        ]
        read_only_fields = ['created_at', 'updated_at', 'invoice_number']

    def get_status_display(self, obj):
        return obj.get_status_display()

    def get_customer_info(self, obj):
        # Get customer information from the order
        order = obj.order
        return {
            'name': f"{order.billing_first_name} {order.billing_last_name}",
            'email': order.email,
            'address': order.billing_address_line1,
            'city': order.billing_city,
            'postalCode': order.billing_postal_code,
            'country': order.billing_country
        }

    def get_payment_method(self, obj):
        # Get the payment method from the order
        return obj.order.get_payment_method_display()

    def get_payment_id(self, obj):
        # Get the transaction ID from the most recent successful transaction
        transaction = Transaction.objects.filter(
            order=obj.order,
            status__in=['completed', 'refunded', 'partially_refunded']
        ).first()

        if transaction:
            return transaction.transaction_id
        return None


class CustomerPaymentMethodSerializer(serializers.ModelSerializer):
    """Serializer for the CustomerPaymentMethod model."""

    payment_method_name = serializers.SerializerMethodField()

    class Meta:
        model = CustomerPaymentMethod
        fields = [
            'id', 'user', 'payment_method', 'payment_method_name', 'is_default',
            'card_type', 'last_four', 'expiry_month', 'expiry_year',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'token']

    def get_payment_method_name(self, obj):
        return obj.payment_method.name if obj.payment_method else None


class CustomerPaymentMethodCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating a CustomerPaymentMethod."""

    class Meta:
        model = CustomerPaymentMethod
        fields = [
            'id', 'user', 'payment_method', 'token', 'is_default',
            'card_type', 'last_four', 'expiry_month', 'expiry_year'
        ]

    def create(self, validated_data):
        # If this is set as default, unset default on all other methods for this user
        if validated_data.get('is_default', False):
            CustomerPaymentMethod.objects.filter(
                user=validated_data['user'],
                is_default=True
            ).update(is_default=False)

        return super().create(validated_data)
