import React, { ReactNode } from 'react';

interface InventoryStatsCardProps {
  title: string;
  value: number;
  icon: ReactNode;
  description: string;
  color?: 'default' | 'green' | 'amber' | 'red';
}

const InventoryStatsCard: React.FC<InventoryStatsCardProps> = ({
  title,
  value,
  icon,
  description,
  color = 'default'
}) => {
  // Define color classes based on the color prop
  const getColorClasses = () => {
    switch (color) {
      case 'green':
        return {
          icon: 'bg-green-100 text-green-600',
          value: 'text-green-600'
        };
      case 'amber':
        return {
          icon: 'bg-amber-100 text-amber-600',
          value: 'text-amber-600'
        };
      case 'red':
        return {
          icon: 'bg-red-100 text-red-600',
          value: 'text-red-600'
        };
      default:
        return {
          icon: 'bg-blue-100 text-blue-600',
          value: 'text-blue-600'
        };
    }
  };

  const colorClasses = getColorClasses();

  return (
    <div className="bg-white p-6 rounded-lg border shadow-sm">
      <div className="flex items-center gap-4">
        <div className={`p-2 rounded-full ${colorClasses.icon}`}>
          {icon}
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className={`text-2xl font-bold ${colorClasses.value}`}>{value.toLocaleString()}</p>
          <p className="text-xs text-gray-500">{description}</p>
        </div>
      </div>
    </div>
  );
};

export default InventoryStatsCard;
