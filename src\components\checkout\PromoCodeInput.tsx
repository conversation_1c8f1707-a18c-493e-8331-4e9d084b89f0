import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import promotionApi, { PromoCodeValidationResponse } from '@/services/promotionApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle2, XCircle, Loader2 } from 'lucide-react';
import { formatPrice } from '@/lib/utils';

interface PromoCodeInputProps {
  cartTotal: number;
  onApplyPromoCode: (discount: number, promoId: number) => void;
  onRemovePromoCode: () => void;
}

const PromoCodeInput: React.FC<PromoCodeInputProps> = ({
  cartTotal,
  onApplyPromoCode,
  onRemovePromoCode
}) => {
  const [promoCode, setPromoCode] = useState('');
  const [appliedCode, setAppliedCode] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [discount, setDiscount] = useState(0);
  const [promoId, setPromoId] = useState<number | null>(null);
  
  const queryClient = useQueryClient();
  
  // Mutation pour vérifier et appliquer le code promo
  const { mutate, isPending: isLoading } = useMutation({
    mutationFn: async (code: string) => {
      // Utiliser l'API publique pour vérifier le code promo
      const response = await promotionApi.validatePromoCode(code, cartTotal);
      return response;
    },
    onSuccess: (data) => {
      if (data.valid) {
        setAppliedCode(promoCode);
        setError(null);
        setDiscount(data.discount_amount);
        setPromoId(data.promotion_id);
        onApplyPromoCode(data.discount_amount, data.promotion_id);
        
        // Invalider le cache des promotions actives pour le mettre à jour
        queryClient.invalidateQueries({ queryKey: ['active-promotions'] });
      } else {
        setError(data.message || 'Code promo invalide');
        setAppliedCode(null);
        setDiscount(0);
        setPromoId(null);
        onRemovePromoCode();
      }
    },
    onError: (error: any) => {
      setError(error.message || 'Erreur lors de la vérification du code promo');
      setAppliedCode(null);
      setDiscount(0);
      setPromoId(null);
      onRemovePromoCode();
    }
  });
  
  // Gérer la soumission du formulaire
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!promoCode.trim()) return;
    
    setError(null);
    mutate(promoCode.trim().toUpperCase());
  };
  
  // Supprimer le code promo appliqué
  const handleRemovePromoCode = () => {
    setAppliedCode(null);
    setPromoCode('');
    setDiscount(0);
    setPromoId(null);
    onRemovePromoCode();
  };
  
  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">Code promo</h3>
      
      {appliedCode ? (
        <div className="flex items-center justify-between p-3 border rounded-md bg-green-50">
          <div className="flex items-center">
            <CheckCircle2 className="h-5 w-5 text-green-600 mr-2" />
            <div>
              <p className="font-medium">{appliedCode}</p>
              <p className="text-sm text-green-700">
                Remise appliquée : {formatPrice(discount)}
              </p>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleRemovePromoCode}
            className="text-gray-500 hover:text-red-500"
          >
            <XCircle className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <Input
            type="text"
            value={promoCode}
            onChange={(e) => setPromoCode(e.target.value)}
            placeholder="Entrez votre code promo"
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={isLoading || !promoCode.trim()}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              'Appliquer'
            )}
          </Button>
        </form>
      )}
      
      {error && (
        <Alert variant="destructive" className="py-2">
          <AlertDescription className="text-sm">
            {error}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default PromoCodeInput;
