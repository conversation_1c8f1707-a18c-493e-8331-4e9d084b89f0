
import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Menu, Search, ShoppingCart, X, UserCircle, ChevronDown, LogOut } from "lucide-react";
import { useCart } from "@/contexts/CartContext";
import { useAuth } from "@/contexts/AuthContext";
import { useUserSync } from "@/hooks/useUserSync";
import { toast } from "@/hooks/use-toast";
import NotificationBell from "@/components/notifications/NotificationBell";
import { LanguageSelector } from "@/components/common/LanguageSelector";
import { useTranslation } from "@/contexts/TranslationContext";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils";
import Logo from '@/components/ui/Logo';

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { cartCount } = useCart();
  const { user, isAuthenticated, logout } = useAuth();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // Synchroniser automatiquement les données utilisateur
  useUserSync();

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Déconnexion réussie",
        description: "À bientôt !",
      });
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: "Erreur lors de la déconnexion",
        description: "Veuillez réessayer.",
        variant: "destructive",
      });
    }
  };

  // Handle scroll effect for sticky header
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const navLinkClass = (path: string) =>
    `transition-colors duration-200 ${location.pathname === path
      ? "text-primary font-medium"
      : "text-gray-700 hover:text-primary"} relative group`;

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300",
        isScrolled
          ? "bg-white/95 backdrop-blur-sm shadow-md"
          : "bg-white border-b border-gray-200"
      )}
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Logo variant="compact" size="md" linkTo="/" />
          </div>

          {/* Desktop Navigation */}
          <NavigationMenu className="hidden lg:flex">
            <NavigationMenuList>
              <NavigationMenuItem>
                <Link to="/" className={navLinkClass("/")}>
                  <span className="py-3 block">{t('nav.home', 'Accueil')}</span>
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem className="px-4">
                <NavigationMenuTrigger className="bg-transparent hover:bg-transparent">
                  <span className={location.pathname.includes("/products") ? "text-primary" : "text-gray-700"}>
                    {t('nav.products', 'Produits')}
                  </span>
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2">
                    <li className="row-span-3">
                      <NavigationMenuLink asChild>
                        <Link
                          to="/products"
                          className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-primary/50 to-primary p-6 no-underline outline-none focus:shadow-md"
                        >
                          <div className="mb-2 mt-4 text-lg font-medium text-white">
                            {t('nav.all_products', 'Tous nos Produits')}
                          </div>
                          <p className="text-sm leading-tight text-white/90">
                            {t('nav.products_description', 'Découvrez notre catalogue complet de produits réseau et sécurité.')}
                          </p>
                        </Link>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <Link
                        to="/products?category=router"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                      >
                        <div className="text-sm font-medium leading-none">Routeurs</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Routeurs professionnels pour PME et entreprises
                        </p>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/products?category=switch"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                      >
                        <div className="text-sm font-medium leading-none">Commutateurs</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Commutateurs réseau performants et fiables
                        </p>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/products?category=security"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                      >
                        <div className="text-sm font-medium leading-none">Sécurité</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Solutions de sécurité réseau avancées
                        </p>
                      </Link>
                    </li>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Link to="/blog" className={navLinkClass("/blog")}>
                  <span className="py-3 block">Blog</span>
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem className="px-4">
                <Link to="/faq" className={navLinkClass("/faq")}>
                  <span className="py-3 block">FAQ</span>
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Link to="/contact" className={navLinkClass("/contact")}>
                  <span className="py-3 block">{t('nav.contact', 'Contact')}</span>
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </NavigationMenuItem>


            </NavigationMenuList>
          </NavigationMenu>

          {/* Search, Cart and Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex relative">
              <div className="relative">
                <Input
                  type="text"
                  placeholder={t('common.search', 'Rechercher...')}
                  className="w-64 pr-8 border-primary/20 focus-within:border-primary focus-within:ring-1 focus-within:ring-primary/30"
                />
                <Search className="absolute right-2 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>

            <div className="hidden md:flex items-center gap-2">
              {isAuthenticated ? (
                <div className="flex items-center gap-4">
                  <NotificationBell />
                  <Link
                    to={user?.role === 'admin' || user?.role === 'staff' ? "/admin/dashboard" : "/account"}
                    className="text-gray-700 hover:text-primary transition-colors flex items-center gap-1.5"
                  >
                    <UserCircle className="h-5 w-5" />
                    <span className="font-medium">
                      {user?.first_name && user?.last_name
                        ? `${user.first_name} ${user.last_name}`
                        : user?.first_name
                          ? `${user.first_name}`
                          : 'Mon compte'
                      }
                    </span>
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="text-gray-700 hover:text-red-600 transition-colors flex items-center gap-1.5"
                  >
                    <LogOut className="h-5 w-5" />
                    <span className="font-medium">Déconnexion</span>
                  </button>
                </div>
              ) : (
                <Link
                  to="/login"
                  className="text-gray-700 hover:text-primary transition-colors flex items-center gap-1.5"
                >
                  <UserCircle className="h-5 w-5" />
                  <span className="font-medium">{t('nav.account', 'Compte')}</span>
                </Link>
              )}
            </div>

            {/* Language Selector */}
            <div className="hidden md:block">
              <LanguageSelector variant="compact" />
            </div>

            <Link to="/cart" className="relative p-1.5 hover:bg-gray-100 rounded-full transition-colors">
              <ShoppingCart className="h-5 w-5 text-gray-700 hover:text-primary transition-colors" />
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-accent text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartCount > 9 ? '9+' : cartCount}
                </span>
              )}
            </Link>

            <button
              className="lg:hidden p-1.5 hover:bg-gray-100 rounded-full transition-colors"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label={isMobileMenuOpen ? "Fermer le menu" : "Ouvrir le menu"}
            >
              {isMobileMenuOpen ?
                <X className="h-5 w-5 text-gray-700" /> :
                <Menu className="h-5 w-5 text-gray-700" />
              }
            </button>
          </div>
        </div>

        {/* Mobile Search */}
        <div className="mt-4 lg:hidden">
          <div className="relative">
            <Input
              type="text"
              placeholder="Rechercher..."
              className="w-full pr-8 border-primary/20"
            />
            <Search className="absolute right-2 top-2.5 h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <nav className="lg:hidden mt-4 pb-4 animate-fade-in border-t border-gray-200 pt-4">
            <ul className="flex flex-col space-y-4">
              <li><Link to="/" className="text-gray-700 hover:text-primary font-medium block">{t('nav.home', 'Accueil')}</Link></li>
              <li>
                <div className="flex items-center justify-between">
                  <Link to="/products" className="text-gray-700 hover:text-primary font-medium block">{t('nav.products', 'Produits')}</Link>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
                <div className="pl-4 mt-2 space-y-2 border-l-2 border-gray-100">
                  <Link to="/products?category=router" className="text-gray-600 hover:text-primary block text-sm py-1">Routeurs</Link>
                  <Link to="/products?category=switch" className="text-gray-600 hover:text-primary block text-sm py-1">Commutateurs</Link>
                  <Link to="/products?category=security" className="text-gray-600 hover:text-primary block text-sm py-1">Sécurité</Link>
                </div>
              </li>
              <li><Link to="/blog" className="text-gray-700 hover:text-primary font-medium block">Blog</Link></li>
              <li><Link to="/faq" className="text-gray-700 hover:text-primary font-medium block">FAQ</Link></li>
              <li><Link to="/contact" className="text-gray-700 hover:text-primary font-medium block">Contact</Link></li>
              {isAuthenticated ? (
                <>
                  <li>
                    <Link
                      to={user?.role === 'admin' || user?.role === 'staff' ? "/admin/dashboard" : "/account"}
                      className="text-gray-700 hover:text-primary font-medium block"
                    >
                      Mon compte
                    </Link>
                  </li>
                  <li>
                    <button
                      onClick={handleLogout}
                      className="text-gray-700 hover:text-red-600 font-medium block w-full text-left"
                    >
                      Déconnexion
                    </button>
                  </li>
                </>
              ) : (
                <>
                  <li><Link to="/login" className="text-gray-700 hover:text-primary font-medium block">{t('auth.login', 'Connexion')}</Link></li>
                  <li><Link to="/register" className="text-gray-700 hover:text-primary font-medium block">{t('auth.register', 'Créer un compte')}</Link></li>
                </>
              )}
              <li><Link to="/cart" className="text-gray-700 hover:text-primary font-medium block">{t('nav.cart', 'Panier')}</Link></li>
              <li><Link to="/order-tracking" className="text-gray-700 hover:text-primary font-medium block">{t('nav.order_tracking', 'Suivi de commande')}</Link></li>
              <li>
                <div className="py-2">
                  <LanguageSelector variant="default" />
                </div>
              </li>
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Navbar;
