from django.db.models import Count, Sum, Avg, F, Q
from rest_framework import viewsets, status, filters
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.pagination import PageNumberPagination
from datetime import datetime, timedelta
from .models import ProductView, CartAction, WishlistAction, SalesData, SystemLog
from .serializers import (
    ProductViewSerializer, CartActionSerializer, WishlistActionSerializer,
    SalesDataSerializer, ProductSalesStatsSerializer, CustomerProductInsightSerializer,
    ProductDashboardDataSerializer, SystemLogSerializer
)
from products.models import Product, ProductVariant, Category
from orders.models import Order
from authentication.models import User
from authentication.permissions import IsStaffOrAdmin

class ProductSalesStatsView(APIView):
    """
    API endpoint for product sales statistics.
    """
    permission_classes = [IsStaffOrAdmin]

    def get(self, request, pk):
        """
        Get sales statistics for a product.

        Parameters:
        - pk: Product ID
        - period: Time period for data aggregation (day, week, month)
        """
        period = request.query_params.get('period', 'month')

        try:
            product = Product.objects.get(pk=pk)
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get sales data by period
        sales_data = SalesData.get_sales_by_period(pk, period)

        # Calculate overall statistics
        total_sales = SalesData.objects.filter(product_id=pk)
        total_orders = total_sales.values('order_id').distinct().count()
        total_quantity = total_sales.aggregate(total=Sum('quantity'))['total'] or 0
        total_revenue = total_sales.aggregate(total=Sum('total_price'))['total'] or 0
        avg_order_value = total_revenue / total_orders if total_orders > 0 else 0

        # Get last order date
        last_order = total_sales.order_by('-date').first()
        last_order_date = last_order.date if last_order else None

        # Calculate sales trend
        now = datetime.now()
        if period == 'day':
            current_period_start = now - timedelta(days=14)
            previous_period_start = current_period_start - timedelta(days=14)
        elif period == 'week':
            current_period_start = now - timedelta(weeks=12)
            previous_period_start = current_period_start - timedelta(weeks=12)
        else:  # month
            current_period_start = now - timedelta(days=365)
            previous_period_start = current_period_start - timedelta(days=365)

        current_period_sales = SalesData.objects.filter(
            product_id=pk,
            date__gte=current_period_start.date()
        ).aggregate(total=Sum('total_price'))['total'] or 0

        previous_period_sales = SalesData.objects.filter(
            product_id=pk,
            date__gte=previous_period_start.date(),
            date__lt=current_period_start.date()
        ).aggregate(total=Sum('total_price'))['total'] or 0

        sales_trend = 0
        if previous_period_sales > 0:
            sales_trend = ((current_period_sales - previous_period_sales) / previous_period_sales) * 100

        # Get sales by variant
        sales_by_variant = []
        variants = ProductVariant.objects.filter(product_id=pk)
        for variant in variants:
            variant_sales = SalesData.objects.filter(variant_id=variant.id)
            variant_quantity = variant_sales.aggregate(total=Sum('quantity'))['total'] or 0
            variant_revenue = variant_sales.aggregate(total=Sum('total_price'))['total'] or 0

            sales_by_variant.append({
                'variant_id': variant.id,
                'variant_name': variant.name,
                'quantity_sold': variant_quantity,
                'revenue': variant_revenue
            })

        # Get top customers
        top_customers = SalesData.objects.filter(product_id=pk).values('customer_id').annotate(
            total_orders=Count('order_id', distinct=True),
            total_quantity=Sum('quantity'),
            total_spent=Sum('total_price')
        ).order_by('-total_spent')[:5]

        # Get related products sales
        related_products_sales = []
        related_products = Product.objects.filter(
            related_to_products__product_id=pk
        ).values('id', 'name')

        for related_product in related_products:
            related_sales = SalesData.objects.filter(product_id=related_product['id'])
            related_quantity = related_sales.aggregate(total=Sum('quantity'))['total'] or 0
            related_revenue = related_sales.aggregate(total=Sum('total_price'))['total'] or 0

            related_products_sales.append({
                'product_id': related_product['id'],
                'product_name': related_product['name'],
                'quantity_sold': related_quantity,
                'revenue': related_revenue
            })

        # Prepare response data
        data = {
            'product_id': product.id,
            'product_name': product.name,
            'total_orders': total_orders,
            'total_quantity_sold': total_quantity,
            'total_revenue': total_revenue,
            'average_order_value': avg_order_value,
            'last_order_date': last_order_date,
            'sales_trend': sales_trend,
            'sales_by_period': list(sales_data),
            'sales_by_variant': sales_by_variant,
            'top_customers': list(top_customers),
            'related_products_sales': related_products_sales
        }

        serializer = ProductSalesStatsSerializer(data)
        return Response(serializer.data)

class CustomerProductInsightView(APIView):
    """
    API endpoint for customer insights about a product.
    """
    permission_classes = [IsStaffOrAdmin]

    def get(self, request, pk):
        """
        Get customer insights for a product.

        Parameters:
        - pk: Product ID
        """
        try:
            product = Product.objects.get(pk=pk)
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get view statistics
        views = ProductView.objects.filter(product_id=pk)
        views_count = views.count()
        unique_viewers = views.values('user_id', 'session_id').distinct().count()

        # Get cart statistics
        cart_actions = CartAction.objects.filter(product_id=pk)
        add_to_cart_count = cart_actions.filter(action='add').count()

        # Get wishlist statistics
        wishlist_actions = WishlistAction.objects.filter(product_id=pk)
        add_to_wishlist_count = wishlist_actions.filter(action='add').count()

        # Get purchase statistics
        sales = SalesData.objects.filter(product_id=pk)
        purchase_count = sales.values('order_id').distinct().count()

        # Calculate conversion rates
        conversion_rate = (purchase_count / views_count) * 100 if views_count > 0 else 0
        cart_abandonment_rate = ((add_to_cart_count - purchase_count) / add_to_cart_count) * 100 if add_to_cart_count > 0 else 0

        # Get review statistics
        reviews = product.reviews.all()
        reviews_count = reviews.count()
        average_rating = reviews.aggregate(avg=Avg('rating'))['avg'] or 0

        # Prepare response data
        data = {
            'product_id': product.id,
            'product_name': product.name,
            'views_count': views_count,
            'unique_viewers_count': unique_viewers,
            'add_to_cart_count': add_to_cart_count,
            'add_to_wishlist_count': add_to_wishlist_count,
            'purchase_count': purchase_count,
            'conversion_rate': conversion_rate,
            'cart_abandonment_rate': cart_abandonment_rate,
            'average_rating': average_rating,
            'reviews_count': reviews_count,
            'demographic_data': {
                'age_groups': [
                    {'group': '18-24', 'percentage': 15},
                    {'group': '25-34', 'percentage': 35},
                    {'group': '35-44', 'percentage': 25},
                    {'group': '45-54', 'percentage': 15},
                    {'group': '55+', 'percentage': 10}
                ],
                'gender': [
                    {'gender': 'Homme', 'percentage': 55},
                    {'gender': 'Femme', 'percentage': 45}
                ],
                'locations': [
                    {'location': 'Paris', 'percentage': 30},
                    {'location': 'Lyon', 'percentage': 15},
                    {'location': 'Marseille', 'percentage': 10},
                    {'location': 'Bordeaux', 'percentage': 8},
                    {'location': 'Autres', 'percentage': 37}
                ]
            },
            'customer_segments': [
                {'segment': 'Nouveaux clients', 'percentage': 40},
                {'segment': 'Clients fidèles', 'percentage': 35},
                {'segment': 'Clients occasionnels', 'percentage': 25}
            ],
            'similar_interests': [
                {'category': 'Électronique', 'percentage': 75},
                {'category': 'Informatique', 'percentage': 60},
                {'category': 'Gadgets', 'percentage': 45}
            ]
        }

        serializer = CustomerProductInsightSerializer(data)
        return Response(serializer.data)

class ProductDashboardDataView(APIView):
    """
    API endpoint for product dashboard data.
    """
    permission_classes = [IsStaffOrAdmin]

    def get(self, request, pk):
        """
        Get dashboard data for a product.

        Parameters:
        - pk: Product ID
        - period: Time period (7d, 30d, 90d, 1y)
        """
        period = request.query_params.get('period', '30d')

        try:
            product = Product.objects.get(pk=pk)
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Determine the period
        now = datetime.now()
        if period == '7d':
            start_date = now - timedelta(days=7)
        elif period == '30d':
            start_date = now - timedelta(days=30)
        elif period == '90d':
            start_date = now - timedelta(days=90)
        elif period == '1y':
            start_date = now - timedelta(days=365)
        else:
            start_date = now - timedelta(days=30)  # Default: 30 days

        # Get key metrics
        views = ProductView.objects.filter(product_id=pk, created_at__gte=start_date)
        views_count = views.count()
        unique_viewers = views.values('user_id', 'session_id').distinct().count()

        cart_actions = CartAction.objects.filter(product_id=pk, created_at__gte=start_date)
        add_to_cart_count = cart_actions.filter(action='add').count()

        sales = SalesData.objects.filter(product_id=pk, date__gte=start_date.date())
        orders_count = sales.values('order_id').distinct().count()
        revenue = sales.aggregate(total=Sum('total_price'))['total'] or 0
        units_sold = sales.aggregate(total=Sum('quantity'))['total'] or 0

        conversion_rate = (orders_count / views_count) * 100 if views_count > 0 else 0

        # Prepare response data
        data = {
            'metrics': {
                'views': views_count,
                'unique_viewers': unique_viewers,
                'add_to_cart': add_to_cart_count,
                'orders': orders_count,
                'revenue': revenue,
                'units_sold': units_sold,
                'conversion_rate': conversion_rate
            },
            'sales_over_time': [],  # To be implemented with real data
            'views_over_time': [],  # To be implemented with real data
            'conversion_over_time': [],  # To be implemented with real data
            'funnel_data': [
                {'stage': 'Vues', 'count': views_count},
                {'stage': 'Ajouts au panier', 'count': add_to_cart_count},
                {'stage': 'Commandes', 'count': orders_count}
            ],
            'top_referrers': [
                {'referrer': 'Google', 'count': 120, 'percentage': 45},
                {'referrer': 'Direct', 'count': 80, 'percentage': 30},
                {'referrer': 'Facebook', 'count': 40, 'percentage': 15},
                {'referrer': 'Instagram', 'count': 20, 'percentage': 7},
                {'referrer': 'Autres', 'count': 8, 'percentage': 3}
            ],
            'device_breakdown': [
                {'device': 'Desktop', 'count': 150, 'percentage': 55},
                {'device': 'Mobile', 'count': 100, 'percentage': 37},
                {'device': 'Tablet', 'count': 20, 'percentage': 8}
            ]
        }

        serializer = ProductDashboardDataSerializer(data)
        return Response(serializer.data)

class StatisticsView(APIView):
    """
    API endpoint for global store statistics.
    """
    permission_classes = [IsStaffOrAdmin]

    def get(self, request):
        """
        Get global store statistics.

        Parameters:
        - time_range: Time range for data (30jours, 3mois, 6mois, 1an)
        """
        try:
            print(f"StatisticsView.get called with params: {request.query_params}")
            time_range = request.query_params.get('time_range', '6mois')

            # Déterminer la période
            now = datetime.now()
            if time_range == '30jours':
                start_date = now - timedelta(days=30)
                period_label = 'jour'
            elif time_range == '3mois':
                start_date = now - timedelta(days=90)
                period_label = 'mois'
            elif time_range == '1an':
                start_date = now - timedelta(days=365)
                period_label = 'mois'
            else:  # 6mois (default)
                start_date = now - timedelta(days=180)
                period_label = 'mois'

            # Récupérer les statistiques de ventes
            # Utiliser directement les commandes au lieu de SalesData
            all_orders = Order.objects.filter(status__in=['delivered', 'processing', 'shipped'])
            total_sales = all_orders.aggregate(total=Sum('total'))['total'] or 0
            total_orders = Order.objects.filter(created_at__gte=start_date).count()
            total_customers = User.objects.filter(date_joined__gte=start_date).count()

            # Calculer les tendances par rapport à la période précédente
            previous_start_date = start_date - (now - start_date)
            previous_sales_data = SalesData.objects.filter(
                date__gte=previous_start_date.date(),
                date__lt=start_date.date()
            )
            previous_total_sales = previous_sales_data.aggregate(total=Sum('total_price'))['total'] or 0
            previous_total_orders = Order.objects.filter(
                created_at__gte=previous_start_date,
                created_at__lt=start_date
            ).count()
            previous_total_customers = User.objects.filter(
                date_joined__gte=previous_start_date,
                date_joined__lt=start_date
            ).count()

            # Calculer les pourcentages de changement
            if previous_total_sales > 0:
                change_sales = ((total_sales - previous_total_sales) / previous_total_sales) * 100
                change_sales_str = f"{'+' if change_sales >= 0 else ''}{change_sales:.0f}%"
                change_type_sales = "positive" if change_sales >= 0 else "negative"
            else:
                change_sales_str = "+100%"
                change_type_sales = "positive"

            if previous_total_orders > 0:
                change_orders = ((total_orders - previous_total_orders) / previous_total_orders) * 100
                change_orders_str = f"{'+' if change_orders >= 0 else ''}{change_orders:.0f}%"
                change_type_orders = "positive" if change_orders >= 0 else "negative"
            else:
                change_orders_str = "+100%"
                change_type_orders = "positive"

            if previous_total_customers > 0:
                change_customers = ((total_customers - previous_total_customers) / previous_total_customers) * 100
                change_customers_str = f"{'+' if change_customers >= 0 else ''}{change_customers:.0f}%"
                change_type_customers = "positive" if change_customers >= 0 else "negative"
            else:
                change_customers_str = "+100%"
                change_type_customers = "positive"

            # Préparer les données de ventes par période
            if period_label == 'jour':
                # Données quotidiennes pour 30 jours
                sales_by_period = []
                for i in range(30):
                    day_date = now - timedelta(days=30-i)
                    day_sales = SalesData.objects.filter(date=day_date.date())
                    revenue = day_sales.aggregate(total=Sum('total_price'))['total'] or 0
                    orders = day_sales.values('order_id').distinct().count()
                    sales_by_period.append({
                        'month': str(i+1),  # Utiliser le jour du mois
                        'revenue': float(revenue),
                        'orders': orders
                    })
            else:
                # Données mensuelles
                months_to_include = 12 if time_range == '1an' else (3 if time_range == '3mois' else 6)
                sales_by_period = []

                for i in range(months_to_include):
                    month_date = now - timedelta(days=30 * (months_to_include - i))
                    month_name = month_date.strftime('%b')  # Nom abrégé du mois
                    month_sales = SalesData.objects.filter(
                        date__year=month_date.year,
                        date__month=month_date.month
                    )
                    revenue = month_sales.aggregate(total=Sum('total_price'))['total'] or 0
                    orders = month_sales.values('order_id').distinct().count()
                    sales_by_period.append({
                        'month': month_name,
                        'revenue': float(revenue),
                        'orders': orders
                    })

            # Récupérer les données de ventes par catégorie
            categories = Category.objects.all()
            category_data = []

            for category in categories:
                category_products = Product.objects.filter(categories=category)
                product_ids = category_products.values_list('id', flat=True)
                category_sales = SalesData.objects.filter(product_id__in=product_ids)
                category_value = category_sales.aggregate(total=Sum('quantity'))['total'] or 0

                if category_value > 0:
                    category_data.append({
                        'name': category.name,
                        'value': category_value
                    })

            # Limiter à 5 catégories maximum
            category_data = sorted(category_data, key=lambda x: x['value'], reverse=True)[:5]

            # Données de trafic (simulées pour l'exemple)
            if period_label == 'jour':
                traffic_data = [
                    {"day": "Lun", "visits": 520},
                    {"day": "Mar", "visits": 610},
                    {"day": "Mer", "visits": 590},
                    {"day": "Jeu", "visits": 780},
                    {"day": "Ven", "visits": 850},
                    {"day": "Sam", "visits": 690},
                    {"day": "Dim", "visits": 450}
                ]
            else:
                # Données mensuelles de trafic
                traffic_data = []
                for i in range(months_to_include):
                    month_date = now - timedelta(days=30 * (months_to_include - i))
                    month_name = month_date.strftime('%b')
                    # Simuler des données de trafic
                    visits = 1500 + (i * 100) + (hash(month_name) % 500)
                    traffic_data.append({
                        'day': month_name,
                        'visits': visits
                    })

            # Conversion en Francs Burundais (1 EUR ≈ 2200 BIF)
            #exchange_rate = 2200

            # Préparer la réponse
            response_data = {
                'sales_statistics': {
                    'total_sales': total_sales,
                    'total_orders': total_orders,
                    'total_customers': total_customers,
                    'change_sales': change_sales_str,
                    'change_orders': change_orders_str,
                    'change_customers': change_customers_str,
                    'change_type_sales': change_type_sales,
                    'change_type_orders': change_type_orders,
                    'change_type_customers': change_type_customers
                },
                'sales_data': [
                    {
                        'month': item['month'],
                        'revenue': float(item['revenue']),
                        'orders': item['orders']
                    } for item in sales_by_period
                ],
                'category_data': category_data,
                'traffic_data': traffic_data
            }

            return Response(response_data)
        except Exception as e:
            import traceback
            print(f"ERROR in StatisticsView.get: {str(e)}")
            print(traceback.format_exc())
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class DashboardView(APIView):
    """
    API endpoint for dashboard data.
    """
    permission_classes = [AllowAny]  # Permettre l'accès temporairement

    def get(self, request):
        """
        Get dashboard data.
        """
        try:
            print(f"DashboardView.get called with params: {request.query_params}")

            # Récupérer les statistiques de ventes
            now = datetime.now()
            start_date = now - timedelta(days=30)  # Données du dernier mois

            # Statistiques de ventes
            # Vérifier d'abord si nous avons des données SalesData
            sales_data = SalesData.objects.all()
            sales_data_count = sales_data.count()
            print(f"Nombre d'entrées SalesData trouvées: {sales_data_count}")
            
            # Calculer le total des ventes à partir de SalesData
            sales_data_total = sales_data.aggregate(total=Sum('total_price'))['total'] or 0
            print(f"Total des ventes calculé à partir de SalesData: {sales_data_total}")
            
            # Calculer également à partir des commandes pour comparaison
            all_orders = Order.objects.filter(status__in=['delivered', 'processing', 'shipped'])
            orders_total = all_orders.aggregate(total=Sum('total'))['total'] or 0
            print(f"Total des ventes calculé à partir des commandes: {orders_total}")
            
            # Utiliser SalesData si disponible, sinon utiliser les commandes
            total_sales = sales_data_total if sales_data_count > 0 else orders_total
            print(f"Total des ventes final utilisé: {total_sales}")
            
            total_orders = Order.objects.filter(created_at__gte=start_date).count()
            total_customers = User.objects.filter(date_joined__gte=start_date).count()
            total_products = Product.objects.count()

            # Calculer les tendances par rapport au mois précédent
            previous_start_date = start_date - timedelta(days=30)
            previous_sales_data = SalesData.objects.filter(
                date__gte=previous_start_date.date(),
                date__lt=start_date.date()
            )
            previous_total_sales = previous_sales_data.aggregate(total=Sum('total_price'))['total'] or 0
            previous_total_orders = Order.objects.filter(
                created_at__gte=previous_start_date,
                created_at__lt=start_date
            ).count()
            previous_total_customers = User.objects.filter(
                date_joined__gte=previous_start_date,
                date_joined__lt=start_date
            ).count()
            previous_total_products = Product.objects.filter(
                created_at__lt=start_date
            ).count()

            # Calculer les pourcentages de changement
            if previous_total_sales > 0:
                change_sales = ((total_sales - previous_total_sales) / previous_total_sales) * 100
                change_sales_str = f"{'+' if change_sales >= 0 else ''}{change_sales:.0f}%"
                change_type_sales = "positive" if change_sales >= 0 else "negative"
            else:
                change_sales_str = "+100%"
                change_type_sales = "positive"

            if previous_total_orders > 0:
                change_orders = ((total_orders - previous_total_orders) / previous_total_orders) * 100
                change_orders_str = f"{'+' if change_orders >= 0 else ''}{change_orders:.0f}%"
                change_type_orders = "positive" if change_orders >= 0 else "negative"
            else:
                change_orders_str = "+100%"
                change_type_orders = "positive"

            if previous_total_customers > 0:
                change_customers = ((total_customers - previous_total_customers) / previous_total_customers) * 100
                change_customers_str = f"{'+' if change_customers >= 0 else ''}{change_customers:.0f}%"
                change_type_customers = "positive" if change_customers >= 0 else "negative"
            else:
                change_customers_str = "+100%"
                change_type_customers = "positive"

            # Changement du nombre de produits (valeur absolue)
            change_products = total_products - previous_total_products
            change_products_str = f"{'+' if change_products >= 0 else ''}{change_products}"
            change_type_products = "positive" if change_products >= 0 else "negative"

            # Récupérer les commandes récentes
            recent_orders = Order.objects.all().order_by('-created_at')[:5]
            recent_orders_data = []

            for order in recent_orders:
                # Formater le montant
                amount = f"FBu {int(round(order.total))}".replace('.', ' ')

                # Déterminer le statut
                if order.status == 'delivered':
                    order_status = 'Livré'
                elif order.status == 'processing':
                    order_status = 'En cours'
                elif order.status == 'pending':
                    order_status = 'En attente'
                elif order.status == 'cancelled':
                    order_status = 'Annulé'
                else:
                    order_status = order.status

                # Formater la date
                date = order.created_at.strftime('%d/%m/%Y')

                # Gérer le cas où l'utilisateur est None
                if order.user:
                    customer_name = f"{order.user.first_name} {order.user.last_name}"
                else:
                    customer_name = "Client inconnu"

                recent_orders_data.append({
                    'id': f"#ORD-{order.id}",
                    'customer': customer_name,
                    'date': date,
                    'status': order_status,
                    'amount': amount
                })

            # Récupérer les produits à faible stock
            low_stock_items = []
            inventory_items = Product.objects.filter(inventory__quantity__gt=0)

            for product in inventory_items:
                if hasattr(product, 'inventory') and product.inventory.quantity <= product.inventory.low_stock_threshold:
                    low_stock_items.append({
                        'id': product.sku or f"PRD-{product.id}",
                        'name': product.name,
                        'stock': product.inventory.quantity,
                        'threshold': product.inventory.low_stock_threshold
                    })

            # Limiter à 4 produits
            low_stock_items = low_stock_items[:4]

            # Tâches en attente (simulées pour l'exemple)
            pending_tasks = [
                {'id': 1, 'task': "Mise à jour des descriptions produits", 'priority': "Haute"},
                {'id': 2, 'task': "Validation des retours clients", 'priority': "Moyenne"},
                {'id': 3, 'task': "Répondre aux avis clients récents", 'priority': "Basse"},
                {'id': 4, 'task': "Préparer la promotion de mai", 'priority': "Haute"}
            ]

            # Ventes mensuelles
            monthly_sales = []
            for i in range(6):
                month_date = now - timedelta(days=30 * (5 - i))
                month_name = month_date.strftime('%b')  # Nom abrégé du mois
                
                # Vérifier si nous avons des données SalesData pour ce mois
                month_sales = SalesData.objects.filter(
                    date__year=month_date.year,
                    date__month=month_date.month
                )
                sales_value = month_sales.aggregate(total=Sum('total_price'))['total'] or 0
                
                # Si pas de données SalesData, utiliser les commandes comme fallback
                if sales_value == 0:
                    month_orders = Order.objects.filter(
                        created_at__year=month_date.year,
                        created_at__month=month_date.month,
                        status__in=['delivered', 'processing', 'shipped']
                    )
                    sales_value = month_orders.aggregate(total=Sum('total'))['total'] or 0
                
                print(f"Ventes pour {month_name}: {sales_value}")
                monthly_sales.append({
                    'month': month_name,
                    'value': float(sales_value)
                })

            # Conversion en Francs Burundais (1 EUR ≈ 2200 BIF)
            #exchange_rate = 2200

            # Fonction pour formater les montants en Francs Burundais
            def format_bif(amount):
                return f"{int(amount):,} BIF".replace(',', ' ')

            # Convertir les montants des commandes récentes
            for order in recent_orders_data:
                # Extraire le montant numérique (supprimer le symbole FBu et les espaces)
                amount_str = order['amount'].replace('FBu ', '').replace(' ', '')
                amount = float(amount_str)
                # Remplacer par le montant en BIF
                order['amount'] = format_bif(amount)

            # Préparer la réponse
            response_data = {
                'stats': {
                    'total_sales': total_sales,
                    'total_orders': total_orders,
                    'total_customers': total_customers,
                    'total_products': total_products,
                    'change_sales': change_sales_str,
                    'change_orders': change_orders_str,
                    'change_customers': change_customers_str,
                    'change_products': change_products_str,
                    'change_type_sales': change_type_sales,
                    'change_type_orders': change_type_orders,
                    'change_type_customers': change_type_customers,
                    'change_type_products': change_type_products
                },
                'recent_orders': recent_orders_data,
                'low_stock_items': low_stock_items,
                'pending_tasks': pending_tasks,
                'monthly_sales': [
                    {
                        'month': item['month'],
                        'value': float(item['value'])
                    } for item in monthly_sales
                ]
            }

            return Response(response_data)
        except Exception as e:
            import traceback
            print(f"ERROR in DashboardView.get: {str(e)}")
            print(traceback.format_exc())
            
            # Vérifier si c'est une erreur d'authentification
            if hasattr(e, 'status_code') and e.status_code == 401:
                print("Erreur d'authentification détectée - vérifiez les tokens JWT")
            
            # Vérifier si c'est une erreur de base de données
            if 'DatabaseError' in str(e) or 'OperationalError' in str(e):
                print("Erreur de base de données détectée - vérifiez la structure des tables")

            # Retourner une réponse d'erreur au lieu de None
            return Response(
                {
                    'error': f'Erreur lors de la récupération des données du dashboard: {str(e)}',
                    'message': 'Une erreur interne s\'est produite'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
                
    max_page_size = 200

# Définition de la classe de pagination pour les logs
class LogsPagination(PageNumberPagination):
    """
    Pagination pour les logs système.
    """
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100

class SystemLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for system logs.
    Provides list and retrieve actions for logs.
    """
    queryset = SystemLog.objects.all()
    serializer_class = SystemLogSerializer
    permission_classes = [AllowAny]  # Permettre l'accès temporairement
    pagination_class = LogsPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['message', 'user_email', 'log_id']
    ordering_fields = ['timestamp', 'type', 'module']
    ordering = ['-timestamp']

    def get_queryset(self):
        """
        Filter logs based on query parameters.
        """
        queryset = SystemLog.objects.all()

        # Filter by type
        log_type = self.request.query_params.get('type', None)
        if log_type:
            queryset = queryset.filter(type=log_type)

        # Filter by module
        module = self.request.query_params.get('module', None)
        if module:
            queryset = queryset.filter(module=module)

        # Filter by date range
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
                queryset = queryset.filter(timestamp__gte=start_date)
            except ValueError:
                pass

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
                # Add one day to include the entire end date
                end_date = end_date + timedelta(days=1)
                queryset = queryset.filter(timestamp__lt=end_date)
            except ValueError:
                pass

        return queryset

class LogsStatisticsView(APIView):
    """
    API endpoint for logs statistics.
    """
    permission_classes = [AllowAny]  # Permettre l'accès temporairement

    def get(self, request):
        """
        Get logs statistics.
        """
        try:
            # Get total counts by type
            type_counts = {}
            for log_type, _ in SystemLog.LOG_TYPES:
                type_counts[log_type] = SystemLog.objects.filter(type=log_type).count()

            # Get total counts by module
            module_counts = {}
            for module, _ in SystemLog.MODULE_CHOICES:
                module_counts[module] = SystemLog.objects.filter(module=module).count()

            # Get recent activity (last 24 hours)
            last_24h = datetime.now() - timedelta(hours=24)
            recent_logs_count = SystemLog.objects.filter(timestamp__gte=last_24h).count()

            # Get error logs count (last 7 days)
            last_7d = datetime.now() - timedelta(days=7)
            error_logs_count = SystemLog.objects.filter(
                type='error',
                timestamp__gte=last_7d
            ).count()

            return Response({
                'total_logs': SystemLog.objects.count(),
                'type_counts': type_counts,
                'module_counts': module_counts,
                'recent_activity_24h': recent_logs_count,
                'error_logs_7d': error_logs_count
            })
        except Exception as e:
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )