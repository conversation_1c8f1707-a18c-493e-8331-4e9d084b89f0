import React, { useEffect, useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Bell,
  MessageSquare,
  X,
  Check,
  CheckCheck,
  Clock,
  AlertCircle,
  Volume2,
  VolumeX
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { API_BASE_URL } from '@/config/api';

interface MessageNotification {
  id: string;
  type: 'new_message' | 'status_change' | 'assignment' | 'priority_change';
  title: string;
  message: string;
  conversation_id: number;
  sender?: {
    id: number;
    full_name: string;
    email: string;
    profile_picture?: string;
    role: string;
  };
  timestamp: string;
  is_read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  action_url?: string;
}

interface MessageNotificationsProps {
  className?: string;
}

const MessageNotifications: React.FC<MessageNotificationsProps> = ({ className }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<MessageNotification[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const audioRef = useRef<HTMLAudioElement>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Initialize WebSocket connection for real-time notifications
  useEffect(() => {
    if (!user) return;

    const connectWebSocket = () => {
            const url = new URL(API_BASE_URL);
      const wsUrl = `ws://${url.host}/ws/notifications/${user.id}/`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('📡 WebSocket connecté pour les notifications');
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleNewNotification(data);
        } catch (error) {
          console.error('Erreur parsing notification WebSocket:', error);
        }
      };

      ws.onclose = () => {
        console.log('📡 WebSocket fermé, tentative de reconnexion...');
        // Reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };

      ws.onerror = (error) => {
        console.error('Erreur WebSocket:', error);
      };

      wsRef.current = ws;
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [user]);

  // Handle new notification
  const handleNewNotification = (notification: MessageNotification) => {
    setNotifications(prev => [notification, ...prev.slice(0, 9)]); // Keep only 10 most recent
    setUnreadCount(prev => prev + 1);

    // Play sound if enabled
    if (soundEnabled && audioRef.current) {
      audioRef.current.play().catch(console.error);
    }

    // Show toast notification
    toast({
      title: notification.title,
      description: notification.message,
      duration: 5000,
    });

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: notification.sender?.profile_picture || '/favicon.ico',
        tag: `message-${notification.id}`,
      });
    }
  };

  // Request notification permission
  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Mark notification as read
  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId
          ? { ...notif, is_read: true }
          : notif
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  // Mark all as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, is_read: true }))
    );
    setUnreadCount(0);
  };

  // Clear all notifications
  const clearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  // Get notification icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_message':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'status_change':
        return <CheckCheck className="h-4 w-4 text-green-500" />;
      case 'assignment':
        return <Bell className="h-4 w-4 text-purple-500" />;
      case 'priority_change':
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-l-red-500 bg-red-50';
      case 'high': return 'border-l-orange-500 bg-orange-50';
      case 'medium': return 'border-l-blue-500 bg-blue-50';
      case 'low': return 'border-l-gray-500 bg-gray-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  // Format time
  const formatTime = (timestamp: string) => {
    return format(parseISO(timestamp), 'HH:mm', { locale: fr });
  };

  return (
    <>
      {/* Notification Bell */}
      <div className={cn("relative", className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(!isVisible)}
          className="relative"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>

        {/* Notifications Panel */}
        <AnimatePresence>
          {isVisible && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              className="absolute right-0 top-full mt-2 w-96 bg-white border rounded-lg shadow-lg z-50"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold">Notifications</h3>
                  {unreadCount > 0 && (
                    <Badge variant="secondary">{unreadCount} non lues</Badge>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSoundEnabled(!soundEnabled)}
                    title={soundEnabled ? 'Désactiver le son' : 'Activer le son'}
                  >
                    {soundEnabled ? (
                      <Volume2 className="h-4 w-4" />
                    ) : (
                      <VolumeX className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsVisible(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Actions */}
              {notifications.length > 0 && (
                <div className="flex items-center justify-between p-3 border-b bg-gray-50">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    disabled={unreadCount === 0}
                  >
                    <Check className="h-4 w-4 mr-1" />
                    Tout marquer comme lu
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAll}
                  >
                    Effacer tout
                  </Button>
                </div>
              )}

              {/* Notifications List */}
              <div className="max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <Bell className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p>Aucune notification</p>
                  </div>
                ) : (
                  <AnimatePresence>
                    {notifications.map((notification) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        className={cn(
                          "border-l-4 p-4 border-b hover:bg-gray-50 cursor-pointer transition-colors",
                          getPriorityColor(notification.priority),
                          !notification.is_read && "bg-blue-50/50"
                        )}
                        onClick={() => {
                          markAsRead(notification.id);
                          if (notification.action_url) {
                            window.location.href = notification.action_url;
                          }
                        }}
                      >
                        <div className="flex items-start space-x-3">
                          {/* Icon */}
                          <div className="flex-shrink-0 mt-1">
                            {getNotificationIcon(notification.type)}
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className={cn(
                                "text-sm font-medium truncate",
                                !notification.is_read && "font-semibold"
                              )}>
                                {notification.title}
                              </h4>
                              <span className="text-xs text-gray-500 ml-2">
                                {formatTime(notification.timestamp)}
                              </span>
                            </div>

                            <p className="text-sm text-gray-600 mb-2">
                              {notification.message}
                            </p>

                            {/* Sender info */}
                            {notification.sender && (
                              <div className="flex items-center space-x-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={notification.sender.profile_picture} />
                                  <AvatarFallback className="text-xs">
                                    {notification.sender.full_name.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-xs text-gray-500">
                                  {notification.sender.full_name}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {notification.sender.role}
                                </Badge>
                              </div>
                            )}
                          </div>

                          {/* Read indicator */}
                          {!notification.is_read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2" />
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                )}
              </div>

              {/* Footer */}
              {notifications.length > 0 && (
                <div className="p-3 border-t bg-gray-50 text-center">
                  <Button variant="ghost" size="sm" className="text-xs">
                    Voir toutes les notifications
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Audio element for notification sounds */}
      <audio
        ref={audioRef}
        preload="auto"
        src="/sounds/notification.mp3" // You'll need to add this sound file
      />
    </>
  );
};

export default MessageNotifications;
