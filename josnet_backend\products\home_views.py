from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Count, Avg, Q
from .models import Product, Category, ProductReview
from orders.models import Order
from authentication.models import User
from .serializers import ProductListSerializer
import logging

logger = logging.getLogger(__name__)

class HomeDataView(APIView):
    """
    API endpoint for home page data including featured products, stats, and testimonials.
    """
    permission_classes = []  # Public endpoint

    def get(self, request):
        """
        Get all data needed for the home page.
        """
        try:
            # Get featured products
            featured_products = Product.objects.filter(
                status='published',
                is_featured=True
            ).prefetch_related(
                'categories',
                'images',
                'reviews',
                'inventory'
            ).annotate(
                average_rating=Avg('reviews__rating'),
                reviews_count=Count('reviews')
            )[:8]  # Limit to 8 featured products

            # Serialize featured products
            featured_serializer = ProductListSerializer(featured_products, many=True, context={'request': request})
            
            # Transform featured products data
            featured_data = []
            for product_data in featured_serializer.data:
                # Get primary image
                image_url = "/placeholder.svg"
                if product_data.get('images') and len(product_data['images']) > 0:
                    image_url = product_data['images'][0].get('image', "/placeholder.svg")
                
                # Get primary category
                category_name = "Général"
                if product_data.get('categories') and len(product_data['categories']) > 0:
                    category_name = product_data['categories'][0].get('name', 'Général')
                
                # Get stock status
                in_stock = True
                if product_data.get('inventory'):
                    in_stock = product_data['inventory'].get('quantity', 0) > 0
                
                featured_product = {
                    'id': product_data['id'],
                    'name': product_data['name'],
                    'price': float(product_data['price']),
                    'image': image_url,
                    'category': category_name,
                    'description': product_data.get('description', '') or product_data.get('short_description', ''),
                    'is_featured': product_data.get('is_featured', False),
                    'in_stock': in_stock,
                    'rating': round(product_data.get('average_rating') or 4.0, 1),
                    'reviews_count': product_data.get('reviews_count', 0)
                }
                featured_data.append(featured_product)

            # Get statistics
            stats = self.get_home_stats()
            
            # Get testimonials (mock data for now)
            testimonials = self.get_testimonials()
            
            # Get categories
            categories = self.get_categories()

            response_data = {
                'featured_products': featured_data,
                'stats': stats,
                'testimonials': testimonials,
                'categories': categories
            }

            logger.info(f"Home page data retrieved: {len(featured_data)} products, {len(testimonials)} testimonials")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error retrieving home page data: {str(e)}")
            
            # Return minimal fallback data
            return Response({
                'featured_products': [],
                'stats': {
                    'total_customers': 1000,
                    'total_projects': 500,
                    'years_experience': 10,
                    'total_products': 150
                },
                'testimonials': [],
                'categories': ['Tous']
            }, status=status.HTTP_200_OK)

    def get_home_stats(self):
        """Get statistics for the home page."""
        try:
            # Count customers (users who have made orders)
            total_customers = User.objects.filter(
                orders__isnull=False
            ).distinct().count()
            
            # Count completed projects (delivered orders)
            total_projects = Order.objects.filter(
                status='delivered'
            ).count()
            
            # Years of experience (hardcoded for now)
            years_experience = 10
            
            # Total products
            total_products = Product.objects.filter(
                status='published'
            ).count()
            
            return {
                'total_customers': max(total_customers, 1000),  # Minimum 1000 for marketing
                'total_projects': max(total_projects, 500),    # Minimum 500 for marketing
                'years_experience': years_experience,
                'total_products': max(total_products, 150)     # Minimum 150 for marketing
            }
        except Exception as e:
            logger.error(f"Error calculating home stats: {str(e)}")
            return {
                'total_customers': 1000,
                'total_projects': 500,
                'years_experience': 10,
                'total_products': 150
            }

    def get_testimonials(self):
        """Get testimonials for the home page."""
        # For now, return mock testimonials
        # In the future, this could come from a Testimonial model
        return [
            {
                'id': 1,
                'name': 'Marie Uwimana',
                'role': 'Directrice IT',
                'company': 'TechCorp Burundi',
                'content': 'JOSNET NETWORK nous a fourni des solutions exceptionnelles pour notre infrastructure réseau. Leur expertise et leur service client sont remarquables.',
                'rating': 5,
                'image_url': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
                'is_featured': True
            },
            {
                'id': 2,
                'name': 'Jean-Baptiste Niyonzima',
                'role': 'Entrepreneur',
                'company': 'StartupBDI',
                'content': 'Grâce à JOSNET, nous avons pu moderniser complètement notre système informatique. Les résultats sont impressionnants !',
                'rating': 5,
                'image_url': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
                'is_featured': True
            },
            {
                'id': 3,
                'name': 'Aline Ndayisenga',
                'role': 'Responsable Technique',
                'company': 'EduTech Solutions',
                'content': 'L\'équipe de JOSNET est très professionnelle. Ils ont su comprendre nos besoins et proposer des solutions adaptées.',
                'rating': 4,
                'image_url': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
                'is_featured': True
            }
        ]

    def get_categories(self):
        """Get product categories."""
        try:
            categories = Category.objects.filter(
                is_active=True
            ).values_list('name', flat=True)
            
            # Add "Tous" at the beginning
            category_list = ['Tous'] + list(categories)
            return category_list
        except Exception as e:
            logger.error(f"Error retrieving categories: {str(e)}")
            return ['Tous', 'Réseaux', 'Ordinateurs', 'Téléphones', 'Audio', 'Périphériques']


class HomeStatsView(APIView):
    """
    API endpoint for home page statistics only.
    """
    permission_classes = []  # Public endpoint

    def get(self, request):
        """Get home page statistics."""
        try:
            home_data_view = HomeDataView()
            stats = home_data_view.get_home_stats()
            
            return Response(stats, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error retrieving home stats: {str(e)}")
            
            return Response({
                'total_customers': 1000,
                'total_projects': 500,
                'years_experience': 10,
                'total_products': 150
            }, status=status.HTTP_200_OK)


class FeaturedProductsView(APIView):
    """
    API endpoint for featured products only.
    """
    permission_classes = []  # Public endpoint

    def get(self, request):
        """Get featured products."""
        try:
            featured_products = Product.objects.filter(
                status='published',
                is_featured=True
            ).prefetch_related(
                'categories',
                'images',
                'reviews',
                'inventory'
            ).annotate(
                average_rating=Avg('reviews__rating'),
                reviews_count=Count('reviews')
            )[:12]  # Limit to 12 featured products

            # Serialize products
            serializer = ProductListSerializer(featured_products, many=True, context={'request': request})
            
            # Transform data
            featured_data = []
            for product_data in serializer.data:
                # Get primary image
                image_url = "/placeholder.svg"
                if product_data.get('images') and len(product_data['images']) > 0:
                    image_url = product_data['images'][0].get('image', "/placeholder.svg")
                
                # Get primary category
                category_name = "Général"
                if product_data.get('categories') and len(product_data['categories']) > 0:
                    category_name = product_data['categories'][0].get('name', 'Général')
                
                # Get stock status
                in_stock = True
                if product_data.get('inventory'):
                    in_stock = product_data['inventory'].get('quantity', 0) > 0
                
                featured_product = {
                    'id': product_data['id'],
                    'name': product_data['name'],
                    'price': float(product_data['price']),
                    'image': image_url,
                    'category': category_name,
                    'description': product_data.get('description', '') or product_data.get('short_description', ''),
                    'is_featured': product_data.get('is_featured', False),
                    'in_stock': in_stock,
                    'rating': round(product_data.get('average_rating') or 4.0, 1),
                    'reviews_count': product_data.get('reviews_count', 0)
                }
                featured_data.append(featured_product)

            logger.info(f"Featured products retrieved: {len(featured_data)} products")
            
            return Response(featured_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error retrieving featured products: {str(e)}")
            return Response([], status=status.HTTP_200_OK)
