import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Gift, Award, TrendingUp, Calendar, Info } from "lucide-react";
import userApi, { LoyaltyTransaction } from "@/services/userApi";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

// Loyalty tiers configuration (should match backend)
const LOYALTY_TIERS = {
  standard: {
    name: 'Standard',
    min_points: 0,
    next_tier: 'silver',
    next_tier_points: 1000,
    color: 'bg-slate-500',
    benefits: ['Accès aux offres membres', 'Notifications de réduction']
  },
  silver: {
    name: 'Silver',
    min_points: 1000,
    next_tier: 'gold',
    next_tier_points: 5000,
    color: 'bg-slate-400',
    benefits: ['Livraison gratuite sur les commandes > 50€', 'Accès anticipé aux ventes', 'Support prioritaire']
  },
  gold: {
    name: 'Gold',
    min_points: 5000,
    next_tier: 'platinum',
    next_tier_points: 10000,
    color: 'bg-amber-500',
    benefits: ['Livraison gratuite sur toutes les commandes', '5% de réduction sur tous les produits', 'Retours gratuits']
  },
  platinum: {
    name: 'Platinum',
    min_points: 10000,
    next_tier: null,
    next_tier_points: null,
    color: 'bg-slate-800',
    benefits: ['Livraison gratuite express', '10% de réduction sur tous les produits', 'Support VIP 24/7', 'Cadeaux exclusifs']
  }
};

const LoyaltyDashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  
  // Fetch loyalty info
  const { 
    data: loyaltyInfo, 
    isLoading: isLoadingInfo 
  } = useQuery({
    queryKey: ['userLoyalty'],
    queryFn: userApi.getLoyaltyInfo
  });
  
  // Fetch loyalty transactions
  const { 
    data: transactions, 
    isLoading: isLoadingTransactions 
  } = useQuery({
    queryKey: ['loyaltyTransactions'],
    queryFn: userApi.getLoyaltyTransactions
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMMM yyyy', { locale: fr });
  };
  
  // Get transaction type display
  const getTransactionTypeDisplay = (type: string) => {
    switch (type) {
      case 'earn':
        return 'Points gagnés';
      case 'redeem':
        return 'Points utilisés';
      case 'expire':
        return 'Points expirés';
      case 'adjust':
        return 'Ajustement';
      default:
        return type;
    }
  };
  
  // Get transaction badge color
  const getTransactionBadgeColor = (type: string) => {
    switch (type) {
      case 'earn':
        return 'bg-green-500';
      case 'redeem':
        return 'bg-blue-500';
      case 'expire':
        return 'bg-red-500';
      case 'adjust':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };
  
  // Calculate progress to next tier
  const calculateProgress = () => {
    if (!loyaltyInfo) return 0;
    
    const currentTier = LOYALTY_TIERS[loyaltyInfo.loyalty_tier as keyof typeof LOYALTY_TIERS];
    if (!currentTier.next_tier) return 100; // Already at highest tier
    
    const nextTierPoints = currentTier.next_tier_points as number;
    const currentPoints = loyaltyInfo.loyalty_points;
    const tierStartPoints = currentTier.min_points;
    
    const pointsInTier = nextTierPoints - tierStartPoints;
    const pointsEarned = currentPoints - tierStartPoints;
    
    return Math.min(Math.round((pointsEarned / pointsInTier) * 100), 100);
  };
  
  // Get points needed for next tier
  const getPointsNeededForNextTier = () => {
    if (!loyaltyInfo) return 0;
    
    const currentTier = LOYALTY_TIERS[loyaltyInfo.loyalty_tier as keyof typeof LOYALTY_TIERS];
    if (!currentTier.next_tier) return 0; // Already at highest tier
    
    const nextTierPoints = currentTier.next_tier_points as number;
    return nextTierPoints - loyaltyInfo.loyalty_points;
  };
  
  // Get next tier name
  const getNextTierName = () => {
    if (!loyaltyInfo) return '';
    
    const currentTier = LOYALTY_TIERS[loyaltyInfo.loyalty_tier as keyof typeof LOYALTY_TIERS];
    if (!currentTier.next_tier) return ''; // Already at highest tier
    
    return LOYALTY_TIERS[currentTier.next_tier as keyof typeof LOYALTY_TIERS].name;
  };
  
  // Loading state
  if (isLoadingInfo) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // No loyalty info
  if (!loyaltyInfo) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <p className="text-muted-foreground">Informations de fidélité non disponibles.</p>
        </CardContent>
      </Card>
    );
  }
  
  const currentTier = LOYALTY_TIERS[loyaltyInfo.loyalty_tier as keyof typeof LOYALTY_TIERS];
  
  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Aperçu</TabsTrigger>
          <TabsTrigger value="history">Historique</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6 mt-6">
          {/* Points Summary Card */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Vos points de fidélité</CardTitle>
              <CardDescription>
                Gagnez des points à chaque achat et utilisez-les pour des réductions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                <div className="text-center md:text-left">
                  <p className="text-sm text-muted-foreground">Points actuels</p>
                  <p className="text-4xl font-bold">{loyaltyInfo.loyalty_points}</p>
                </div>
                
                <div className="text-center">
                  <Badge className={`${currentTier.color} hover:${currentTier.color}`}>
                    Niveau {currentTier.name}
                  </Badge>
                  <p className="text-sm text-muted-foreground mt-2">Votre niveau de fidélité</p>
                </div>
                
                {currentTier.next_tier && (
                  <div className="text-center md:text-right">
                    <p className="text-sm text-muted-foreground">
                      {getPointsNeededForNextTier()} points pour atteindre {getNextTierName()}
                    </p>
                    <div className="w-full mt-2">
                      <Progress value={calculateProgress()} className="h-2" />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          {/* Benefits Card */}
          <Card>
            <CardHeader>
              <CardTitle>Avantages du niveau {currentTier.name}</CardTitle>
              <CardDescription>
                Profitez de ces avantages exclusifs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {currentTier.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <Award className="h-5 w-5 mr-2 text-primary flex-shrink-0 mt-0.5" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            {currentTier.next_tier && (
              <CardFooter className="border-t pt-4">
                <div className="w-full">
                  <p className="text-sm font-medium mb-2">
                    Prochain niveau : {getNextTierName()} ({getPointsNeededForNextTier()} points restants)
                  </p>
                  <Progress value={calculateProgress()} className="h-2" />
                </div>
              </CardFooter>
            )}
          </Card>
          
          {/* How to earn points */}
          <Card>
            <CardHeader>
              <CardTitle>Comment gagner des points</CardTitle>
              <CardDescription>
                Plusieurs façons d'augmenter votre solde de points
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <TrendingUp className="h-5 w-5 mr-2 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">Achats</p>
                    <p className="text-sm text-muted-foreground">Gagnez 10 points pour chaque euro dépensé</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Gift className="h-5 w-5 mr-2 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">Parrainage</p>
                    <p className="text-sm text-muted-foreground">Gagnez 200 points pour chaque ami parrainé qui effectue un achat</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Info className="h-5 w-5 mr-2 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">Avis</p>
                    <p className="text-sm text-muted-foreground">Gagnez 50 points pour chaque avis publié sur un produit acheté</p>
                  </div>
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Historique des transactions</CardTitle>
              <CardDescription>
                Toutes vos transactions de points de fidélité
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingTransactions ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : transactions && transactions.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Expiration</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction: LoyaltyTransaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>{formatDate(transaction.created_at)}</TableCell>
                        <TableCell>
                          <Badge className={getTransactionBadgeColor(transaction.transaction_type)}>
                            {getTransactionTypeDisplay(transaction.transaction_type)}
                          </Badge>
                        </TableCell>
                        <TableCell className={transaction.points > 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                          {transaction.points > 0 ? `+${transaction.points}` : transaction.points}
                        </TableCell>
                        <TableCell>{transaction.description}</TableCell>
                        <TableCell>
                          {transaction.expires_at ? (
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                              <span className="text-sm">{formatDate(transaction.expires_at)}</span>
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">N/A</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Aucune transaction trouvée.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LoyaltyDashboard;
