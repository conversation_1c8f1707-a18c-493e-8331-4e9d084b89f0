
import { But<PERSON> } from "@/components/ui/button";
import { Check<PERSON>ir<PERSON>, ArrowR<PERSON> } from "lucide-react";

const AboutSection = () => {
  const features = [
    "Services IT et télécom de qualité",
    "Accompagnement dans la transformation digitale",
    "Formation et développement des compétences",
    "Service client excellent",
    "Solutions innovantes",
    "Présence sur tout le marché"
  ];

  return (
    <section className="py-16 bg-secondary">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Image Column */}
          <div className="relative">
            <div className="rounded-lg overflow-hidden shadow-xl">
              <img 
                src="https://images.unsplash.com/photo-1523961131990-5ea7c61b2107?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80" 
                alt="JOSNET NETWORK Team" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 bg-accent p-6 rounded-lg shadow-lg hidden md:block">
              <p className="text-white font-bold text-4xl">10+</p>
              <p className="text-white text-sm">Années d'expérience</p>
            </div>
          </div>

          {/* Content Column */}
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">À propos de JOSNET NETWORK</h2>
            <p className="text-gray-700 mb-6">
              JOSNET a été fondée pour répondre aux besoins croissants en connectivité et services numériques au Burundi. 
              Depuis, elle a élargi ses activités aux solutions IT et télécoms, devenant un acteur clé pour 
              entreprises et particuliers.
            </p>
            <p className="text-gray-700 mb-8">
              Notre mission est de fournir des services de qualité dans le domaine des télécommunications et 
              des technologies de l'information, avec un fort accent sur l'innovation et la satisfaction client.
            </p>

            <div className="grid md:grid-cols-2 gap-3 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>

            <Button 
              className="bg-primary hover:bg-primary-dark text-white flex items-center gap-2"
            >
              En savoir plus
              <ArrowRight size={16} />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
