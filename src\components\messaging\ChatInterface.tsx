import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Send,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Info,
  Search,
  Pin,
  Archive,
  Trash2,
  Download,
  Eye,
  Clock,
  CheckCheck,
  Mic,
  Image as ImageIcon,
  File,
  X
} from 'lucide-react';
import { format, parseISO, isToday, isYesterday } from 'date-fns';
import { fr } from 'date-fns/locale';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Message {
  id: number;
  content: string;
  sender: {
    id: number;
    full_name: string;
    email: string;
    role: string;
    profile_picture?: string;
  };
  created_at: string;
  is_from_admin: boolean;
  attachments?: Array<{
    id: number;
    file_name: string;
    file_url: string;
    file_type: string;
    file_size: number;
  }>;
  is_read?: boolean;
  is_edited?: boolean;
}

interface Conversation {
  id: number;
  subject: string;
  status: string;
  priority: string;
  customer: {
    id: number;
    full_name: string;
    email: string;
    profile_picture?: string;
  };
  assigned_to?: {
    id: number;
    full_name: string;
    email: string;
  };
  messages: Message[];
  created_at: string;
  updated_at: string;
  last_message_at: string;
  is_read_by_admin: boolean;
  is_read_by_customer: boolean;
}

interface ChatInterfaceProps {
  conversation: Conversation;
  onSendMessage: (content: string, files?: File[]) => void;
  onUpdateStatus: (status: string) => void;
  onUpdatePriority: (priority: string) => void;
  isLoading?: boolean;
  className?: string;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversation,
  onSendMessage,
  onUpdateStatus,
  onUpdatePriority,
  isLoading = false,
  className
}) => {
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation.messages]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const formatMessageTime = (dateString: string) => {
    const date = parseISO(dateString);
    if (isToday(date)) {
      return format(date, 'HH:mm');
    } else if (isYesterday(date)) {
      return `Hier ${format(date, 'HH:mm')}`;
    } else {
      return format(date, 'dd/MM HH:mm', { locale: fr });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-yellow-100 text-yellow-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-600';
      case 'medium': return 'bg-blue-100 text-blue-600';
      case 'high': return 'bg-orange-100 text-orange-600';
      case 'urgent': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const handleSendMessage = () => {
    if (message.trim() || selectedFiles.length > 0) {
      onSendMessage(message.trim(), selectedFiles);
      setMessage('');
      setSelectedFiles([]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setSelectedFiles(prev => [...prev, ...files]);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <ImageIcon className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  const isCurrentUser = (senderId: number) => {
    return user?.id === senderId;
  };

  return (
    <div className={cn("flex flex-col h-full bg-white", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={conversation.customer.profile_picture} />
            <AvatarFallback>
              {conversation.customer.full_name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-gray-900">{conversation.subject}</h3>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>{conversation.customer.full_name}</span>
              <span>•</span>
              <Badge className={getStatusColor(conversation.status)} variant="secondary">
                {conversation.status}
              </Badge>
              <Badge className={getPriorityColor(conversation.priority)} variant="secondary">
                {conversation.priority}
              </Badge>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Video className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Search className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
        <AnimatePresence>
          {conversation.messages.map((msg, index) => {
            const isOwn = isCurrentUser(msg.sender.id);
            const showAvatar = index === 0 || 
              conversation.messages[index - 1].sender.id !== msg.sender.id;
            
            return (
              <motion.div
                key={msg.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={cn(
                  "flex items-end space-x-2",
                  isOwn ? "justify-end" : "justify-start"
                )}
              >
                {!isOwn && showAvatar && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={msg.sender.profile_picture} />
                    <AvatarFallback className="text-xs">
                      {msg.sender.full_name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                )}
                
                {!isOwn && !showAvatar && <div className="w-8" />}
                
                <div className={cn(
                  "max-w-[70%] rounded-2xl px-4 py-2 relative group",
                  isOwn 
                    ? "bg-blue-500 text-white" 
                    : "bg-white border shadow-sm"
                )}>
                  {!isOwn && showAvatar && (
                    <div className="text-xs font-medium text-gray-600 mb-1">
                      {msg.sender.full_name}
                    </div>
                  )}
                  
                  <div className="text-sm whitespace-pre-wrap">
                    {msg.content}
                  </div>
                  
                  {/* Attachments */}
                  {msg.attachments && msg.attachments.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {msg.attachments.map(attachment => (
                        <div
                          key={attachment.id}
                          className={cn(
                            "flex items-center space-x-2 p-2 rounded-lg text-xs",
                            isOwn ? "bg-blue-400/20" : "bg-gray-100"
                          )}
                        >
                          {getFileIcon(attachment.file_type)}
                          <span className="flex-1 truncate">{attachment.file_name}</span>
                          <span className="text-xs opacity-70">
                            {formatFileSize(attachment.file_size)}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => window.open(attachment.file_url, '_blank')}
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  <div className={cn(
                    "flex items-center justify-end space-x-1 mt-1 text-xs",
                    isOwn ? "text-blue-100" : "text-gray-500"
                  )}>
                    <span>{formatMessageTime(msg.created_at)}</span>
                    {isOwn && (
                      <CheckCheck className={cn(
                        "h-3 w-3",
                        msg.is_read ? "text-blue-200" : "text-blue-300"
                      )} />
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
        
        {/* Typing indicator */}
        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2"
          >
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {conversation.customer.full_name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="bg-white border rounded-2xl px-4 py-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* File preview */}
      {selectedFiles.length > 0 && (
        <div className="border-t bg-gray-50 p-3">
          <div className="flex flex-wrap gap-2">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center space-x-2 bg-white border rounded-lg p-2">
                {getFileIcon(file.type)}
                <span className="text-sm truncate max-w-[150px]">{file.name}</span>
                <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => removeFile(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="border-t bg-white p-4">
        <div className="flex items-end space-x-2">
          <div className="flex space-x-1">
            <input
              type="file"
              multiple
              className="hidden"
              ref={fileInputRef}
              onChange={handleFileSelect}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Tapez votre message..."
              className="min-h-[40px] max-h-[120px] resize-none border-0 focus:ring-0 bg-gray-100 rounded-2xl"
              disabled={isLoading}
            />
          </div>
          
          <Button
            onClick={handleSendMessage}
            disabled={(!message.trim() && selectedFiles.length === 0) || isLoading}
            className="rounded-full h-10 w-10 p-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
