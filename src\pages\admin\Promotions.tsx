
import AdminLayout from "@/components/admin/AdminLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Tag,
  Search,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  CalendarClock,
  AlertCircle,
  Loader2,
  RefreshCw
} from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import promotionApi, { Promotion } from "@/services/promotionApi";
import PromotionStatusBadge from '@/components/admin/promotions/PromotionStatusBadge';
import PromotionUsageStats from '@/components/admin/promotions/PromotionUsageStats';
import CreatePromotionNotification from '@/components/admin/promotions/CreatePromotionNotification';
import { format, parseISO } from "date-fns";
import { fr } from "date-fns/locale";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import PromotionForm from "@/components/admin/promotions/PromotionForm";
import { PromotionsRefreshButton } from '@/components/ui/refresh-button';

const Promotions = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPromotion, setSelectedPromotion] = useState<Promotion | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isClientSide, setIsClientSide] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();
  const pageSize = 10;

  // Vérifier si le code s'exécute côté client
  useEffect(() => {
    setIsClientSide(true);
  }, []);

  // Récupérer les promotions avec React Query
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useQuery({
    queryKey: ['promotions', currentPage],
    queryFn: () => promotionApi.getPromotions({
      page: currentPage,
      page_size: pageSize,
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutations pour les opérations CRUD
  const createMutation = useMutation({
    mutationFn: (newPromotion: any) => promotionApi.createPromotion(newPromotion),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: "Promotion créée",
        description: "La promotion a été créée avec succès",
      });
      setIsFormOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la création de la promotion",
        variant: "destructive",
      });
    }
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: any }) =>
      promotionApi.updatePromotion(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: "Promotion mise à jour",
        description: "La promotion a été mise à jour avec succès",
      });
      setIsFormOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour de la promotion",
        variant: "destructive",
      });
    }
  });

  const deleteMutation = useMutation({
    mutationFn: (id: number) => promotionApi.deletePromotion(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: "Promotion supprimée",
        description: "La promotion a été supprimée avec succès",
      });
      setIsDeleteDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression de la promotion",
        variant: "destructive",
      });
    }
  });

  const toggleActiveMutation = useMutation({
    mutationFn: ({ id, isActive }: { id: number, isActive: boolean }) =>
      promotionApi.updatePromotion(id, { is_active: isActive }),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: variables.isActive ? "Promotion activée" : "Promotion désactivée",
        description: `La promotion a été ${variables.isActive ? "activée" : "désactivée"} avec succès`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la modification de l'état de la promotion",
        variant: "destructive",
      });
    }
  });

  // Fonction de filtrage pour la recherche
  const promotions = data?.results || [];
  const filteredPromotions = promotions.filter(promo =>
    (promo.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    promo.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    promo.discount_code?.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleEditPromotion = (promotion: Promotion) => {
    setSelectedPromotion(promotion);
    setIsFormOpen(true);
  };

  const handleDeletePromotion = (promotion: Promotion) => {
    setSelectedPromotion(promotion);
    setIsDeleteDialogOpen(true);
  };

  const handleAddPromotion = () => {
    setSelectedPromotion(null);
    setIsFormOpen(true);
  };

  const togglePromoActive = (promotion: Promotion) => {
    toggleActiveMutation.mutate({
      id: promotion.id,
      isActive: !promotion.is_active
    });
  };

  const handleFormSubmit = async (formData: any) => {
    setIsSubmitting(true);
    try {
      if (selectedPromotion) {
        await updateMutation.mutateAsync({ id: selectedPromotion.id, data: formData });
      } else {
        await createMutation.mutateAsync(formData);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
  };

  const confirmDelete = () => {
    if (selectedPromotion) {
      deleteMutation.mutate(selectedPromotion.id);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Promotions</h1>
          <p className="text-gray-500">Gérez vos codes promotionnels et réductions</p>
        </div>

        {/* Message d'état */}
        {isFetching && !isLoading && (
          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 rounded-md mb-4 flex items-center">
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Actualisation des données en cours...
          </div>
        )}

        {isError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4 flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            <div>
              <p className="font-medium">Erreur lors du chargement des promotions</p>
              <p className="text-sm">{error instanceof Error ? error.message : 'Veuillez réessayer plus tard'}</p>
            </div>
          </div>
        )}

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher une promotion..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => refetch()}
              className="flex items-center gap-1"
              disabled={isLoading || isFetching}
            >
              {(isLoading || isFetching) ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : (
                <span className="h-4 w-4 mr-1 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                    <path d="M21 3v5h-5"/>
                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                    <path d="M8 16H3v5"/>
                  </svg>
                </span>
              )}
              Actualiser
            </Button>
            <Button onClick={handleAddPromotion} className="flex items-center gap-1">
              <Plus size={16} />
              Nouvelle Promotion
            </Button>
          </div>
        </div>

        {/* État de chargement */}
        {isLoading && (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        )}

        {/* État d'erreur */}
        {isError && (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">Erreur lors du chargement des promotions</h3>
            <p className="text-gray-500 mb-4">
              {(error as any)?.message || "Une erreur s'est produite lors de la récupération des promotions."}
            </p>
            <Button onClick={() => refetch()}>Réessayer</Button>
          </div>
        )}

        {/* Tableau des promotions */}
        {!isLoading && !isError && (
          <>
            {filteredPromotions.length === 0 ? (
              <div className="text-center p-8 border rounded-lg">
                <p className="text-gray-500">Aucune promotion trouvée</p>
                {searchQuery && (
                  <p className="text-sm text-gray-400 mt-2">
                    Essayez de modifier vos critères de recherche
                  </p>
                )}
              </div>
            ) : (
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Code</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Valeur</TableHead>
                      <TableHead>Période</TableHead>
                      <TableHead>Cible</TableHead>
                      <TableHead>Actif</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPromotions.map((promo) => (
                      <TableRow key={promo.id}>
                        <TableCell className="font-medium">
                          {promo.name}
                        </TableCell>
                        <TableCell>
                          {promo.discount_code ? (
                            <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">
                              {promo.discount_code}
                            </code>
                          ) : (
                            <span className="text-gray-400 text-xs">Aucun code</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {promo.discount_type === 'percentage' ? 'Pourcentage' : 'Montant fixe'}
                        </TableCell>
                        <TableCell>
                          {promo.discount_type === 'percentage'
                            ? `${promo.discount_value}%`
                            : `${promo.discount_value} BIF`}
                        </TableCell>
                        <TableCell className="whitespace-nowrap">
                          <div className="flex items-center gap-1">
                            <CalendarClock size={14} className="text-gray-500" />
                            <span>
                              {format(parseISO(promo.start_date), 'dd/MM/yyyy')}
                              {promo.end_date && ` - ${format(parseISO(promo.end_date), 'dd/MM/yyyy')}`}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {promo.applies_to === 'cart' ? (
                            <span>Panier</span>
                          ) : promo.applies_to === 'product' ? (
                            <span>Produit: {promo.target_name || 'N/A'}</span>
                          ) : (
                            <span>Catégorie: {promo.target_name || 'N/A'}</span>
                          )}
                        </TableCell>
                        <TableCell className="px-4 py-4 text-sm">
                          <div className="flex flex-col space-y-1">
                            <PromotionStatusBadge 
                              isActive={promo.is_active}
                              startDate={new Date(promo.start_date)}
                              endDate={promo.end_date ? new Date(promo.end_date) : undefined}
                            />
                            {(promo.total_usage_limit > 0 || promo.usage_limit_per_customer > 0) && (
                              <PromotionUsageStats 
                                totalUsage={promo.total_usage || 0}
                                totalUsageLimit={promo.total_usage_limit || 0}
                                customerUsage={1} // Exemple pour démonstration
                                customerUsageLimit={promo.usage_limit_per_customer || 0}
                              />
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            {promo.is_active && (
                              <CreatePromotionNotification promotion={promo} />
                            )}
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs"
                              onClick={() => togglePromoActive(promo)}
                              disabled={toggleActiveMutation.isPending}
                            >
                              {promo.is_active ? "Désactiver" : "Activer"}
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditPromotion(promo)}
                            >
                              <Edit size={16} className="text-blue-500" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeletePromotion(promo)}
                            >
                              <Trash2 size={16} className="text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Indication du nombre de promotions */}
            <div className="text-sm text-gray-500">
              Affichage de {filteredPromotions.length} promotions sur {data?.count || 0}
            </div>
          </>
        )}

        {/* Formulaire de création/édition de promotion */}
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedPromotion ? 'Modifier la promotion' : 'Créer une nouvelle promotion'}
              </DialogTitle>
              <DialogDescription>
                {selectedPromotion
                  ? 'Modifiez les détails de la promotion existante'
                  : 'Remplissez les informations pour créer une nouvelle promotion'}
              </DialogDescription>
            </DialogHeader>
            <PromotionForm
              promotion={selectedPromotion || undefined}
              onSubmit={handleFormSubmit}
              onCancel={handleFormCancel}
              isSubmitting={isSubmitting}
            />
          </DialogContent>
        </Dialog>

        {/* Dialogue de confirmation de suppression */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer cette promotion ?</AlertDialogTitle>
              <AlertDialogDescription>
                Cette action est irréversible. La promotion sera définitivement supprimée.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Annuler</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-red-500 hover:bg-red-600"
              >
                {deleteMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Suppression...
                  </>
                ) : (
                  'Supprimer'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AdminLayout>
  );
};

export default Promotions;
