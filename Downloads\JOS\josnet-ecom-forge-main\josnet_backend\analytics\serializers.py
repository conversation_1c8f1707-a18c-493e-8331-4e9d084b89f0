from rest_framework import serializers
from .models import ProductView, CartAction, WishlistAction, SalesData

class ProductViewSerializer(serializers.ModelSerializer):
    """Serializer for the ProductView model."""
    
    class Meta:
        model = ProductView
        fields = [
            'id', 'product', 'user', 'session_id', 'ip_address',
            'user_agent', 'referrer', 'device_type', 'created_at'
        ]
        read_only_fields = ['created_at']

class CartActionSerializer(serializers.ModelSerializer):
    """Serializer for the CartAction model."""
    
    class Meta:
        model = CartAction
        fields = [
            'id', 'product', 'variant', 'user', 'session_id',
            'action', 'quantity', 'created_at'
        ]
        read_only_fields = ['created_at']

class WishlistActionSerializer(serializers.ModelSerializer):
    """Serializer for the WishlistAction model."""
    
    class Meta:
        model = WishlistAction
        fields = [
            'id', 'product', 'user', 'action', 'created_at'
        ]
        read_only_fields = ['created_at']

class SalesDataSerializer(serializers.ModelSerializer):
    """Serializer for the SalesData model."""
    
    class Meta:
        model = SalesData
        fields = [
            'id', 'product', 'variant', 'order_id', 'customer_id',
            'quantity', 'unit_price', 'total_price', 'date', 'created_at'
        ]
        read_only_fields = ['created_at']

class ProductSalesStatsSerializer(serializers.Serializer):
    """Serializer for product sales statistics."""
    
    product_id = serializers.IntegerField()
    product_name = serializers.CharField()
    total_orders = serializers.IntegerField()
    total_quantity_sold = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    last_order_date = serializers.DateTimeField(allow_null=True)
    sales_trend = serializers.FloatField()
    sales_by_period = serializers.ListField(child=serializers.DictField())
    sales_by_variant = serializers.ListField(child=serializers.DictField(), required=False)
    top_customers = serializers.ListField(child=serializers.DictField())
    related_products_sales = serializers.ListField(child=serializers.DictField())

class CustomerProductInsightSerializer(serializers.Serializer):
    """Serializer for customer insights about a product."""
    
    product_id = serializers.IntegerField()
    product_name = serializers.CharField()
    views_count = serializers.IntegerField()
    unique_viewers_count = serializers.IntegerField()
    add_to_cart_count = serializers.IntegerField()
    add_to_wishlist_count = serializers.IntegerField()
    purchase_count = serializers.IntegerField()
    conversion_rate = serializers.FloatField()
    cart_abandonment_rate = serializers.FloatField()
    average_rating = serializers.FloatField()
    reviews_count = serializers.IntegerField()
    demographic_data = serializers.DictField()
    customer_segments = serializers.ListField(child=serializers.DictField())
    similar_interests = serializers.ListField(child=serializers.DictField())

class ProductDashboardDataSerializer(serializers.Serializer):
    """Serializer for product dashboard data."""
    
    metrics = serializers.DictField()
    sales_over_time = serializers.ListField(child=serializers.DictField())
    views_over_time = serializers.ListField(child=serializers.DictField())
    conversion_over_time = serializers.ListField(child=serializers.DictField())
    funnel_data = serializers.ListField(child=serializers.DictField())
    top_referrers = serializers.ListField(child=serializers.DictField())
    device_breakdown = serializers.ListField(child=serializers.DictField())
