import axios from 'axios';
import { API_BASE_URL } from '@/config/api';
import { getAuthToken } from '@/utils/auth';

export interface EmailNotificationRequest {
  recipient_email: string;
  recipient_name?: string;
  subject: string;
  message: string;
  conversation_id?: number;
  template_type?: 'new_conversation' | 'message_reply' | 'conversation_update';
  sender_name?: string;
  sender_email?: string;
}

export interface SMSNotificationRequest {
  recipient_phone: string;
  recipient_name?: string;
  message: string;
  conversation_id?: number;
}

export interface NotificationResponse {
  success: boolean;
  message: string;
  notification_id?: string;
  sent_at?: string;
}

const notificationService = {
  // Envoyer une notification email
  sendEmailNotification: async (data: EmailNotificationRequest): Promise<NotificationResponse> => {
    try {
      const token = getAuthToken();
      
      // Si pas de token, simuler l'envoi (mode développement)
      if (!token) {
        console.log('📧 SIMULATION - Email notification envoyée:', {
          to: data.recipient_email,
          subject: data.subject,
          message: data.message.substring(0, 100) + '...'
        });
        
        return {
          success: true,
          message: 'Email notification envoyée (simulation)',
          notification_id: `sim_${Date.now()}`,
          sent_at: new Date().toISOString()
        };
      }

      const response = await axios.post(`${API_BASE_URL}/notifications/email/`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error: any) {
      console.error('Erreur envoi email notification:', error);
      
      // Fallback en mode simulation
      console.log('📧 FALLBACK - Email notification (simulation):', {
        to: data.recipient_email,
        subject: data.subject,
        error: error.message
      });
      
      return {
        success: false,
        message: `Erreur envoi email: ${error.response?.data?.message || error.message}`
      };
    }
  },

  // Envoyer une notification SMS
  sendSMSNotification: async (data: SMSNotificationRequest): Promise<NotificationResponse> => {
    try {
      const token = getAuthToken();
      
      // Si pas de token, simuler l'envoi
      if (!token) {
        console.log('📱 SIMULATION - SMS notification envoyée:', {
          to: data.recipient_phone,
          message: data.message.substring(0, 50) + '...'
        });
        
        return {
          success: true,
          message: 'SMS notification envoyée (simulation)',
          notification_id: `sms_sim_${Date.now()}`,
          sent_at: new Date().toISOString()
        };
      }

      const response = await axios.post(`${API_BASE_URL}/notifications/sms/`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error: any) {
      console.error('Erreur envoi SMS notification:', error);
      
      return {
        success: false,
        message: `Erreur envoi SMS: ${error.response?.data?.message || error.message}`
      };
    }
  },

  // Envoyer une notification de nouvelle conversation au client
  notifyNewConversation: async (
    customerEmail: string, 
    customerName: string, 
    subject: string, 
    message: string,
    conversationId: number,
    adminName: string = 'Équipe Support'
  ): Promise<NotificationResponse> => {
    const emailData: EmailNotificationRequest = {
      recipient_email: customerEmail,
      recipient_name: customerName,
      subject: `Nouvelle conversation: ${subject}`,
      message: `
Bonjour ${customerName},

Une nouvelle conversation a été créée pour vous par notre équipe support.

**Sujet:** ${subject}

**Message:**
${message}

Vous pouvez répondre à ce message ou vous connecter à votre espace client pour continuer la conversation.

Cordialement,
${adminName}
JosNet Network - Équipe Support

---
Cette conversation a été créée le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}.
      `.trim(),
      conversation_id: conversationId,
      template_type: 'new_conversation',
      sender_name: adminName,
      sender_email: '<EMAIL>'
    };

    return await notificationService.sendEmailNotification(emailData);
  },

  // Envoyer une notification de réponse dans une conversation
  notifyConversationReply: async (
    customerEmail: string,
    customerName: string,
    conversationSubject: string,
    replyMessage: string,
    conversationId: number,
    adminName: string = 'Équipe Support'
  ): Promise<NotificationResponse> => {
    const emailData: EmailNotificationRequest = {
      recipient_email: customerEmail,
      recipient_name: customerName,
      subject: `Réponse à votre conversation: ${conversationSubject}`,
      message: `
Bonjour ${customerName},

Vous avez reçu une nouvelle réponse dans votre conversation "${conversationSubject}".

**Nouvelle réponse:**
${replyMessage}

Vous pouvez répondre à ce message ou vous connecter à votre espace client pour continuer la conversation.

Cordialement,
${adminName}
JosNet Network - Équipe Support

---
Réponse envoyée le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}.
      `.trim(),
      conversation_id: conversationId,
      template_type: 'message_reply',
      sender_name: adminName,
      sender_email: '<EMAIL>'
    };

    return await notificationService.sendEmailNotification(emailData);
  },

  // Envoyer une notification de mise à jour de conversation
  notifyConversationUpdate: async (
    customerEmail: string,
    customerName: string,
    conversationSubject: string,
    updateMessage: string,
    conversationId: number,
    adminName: string = 'Équipe Support'
  ): Promise<NotificationResponse> => {
    const emailData: EmailNotificationRequest = {
      recipient_email: customerEmail,
      recipient_name: customerName,
      subject: `Mise à jour de votre conversation: ${conversationSubject}`,
      message: `
Bonjour ${customerName},

Votre conversation "${conversationSubject}" a été mise à jour.

**Mise à jour:**
${updateMessage}

Vous pouvez vous connecter à votre espace client pour voir les détails complets.

Cordialement,
${adminName}
JosNet Network - Équipe Support

---
Mise à jour effectuée le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}.
      `.trim(),
      conversation_id: conversationId,
      template_type: 'conversation_update',
      sender_name: adminName,
      sender_email: '<EMAIL>'
    };

    return await notificationService.sendEmailNotification(emailData);
  },

  // Envoyer une notification combinée (email + SMS si numéro disponible)
  notifyCustomerMultiChannel: async (
    customerEmail: string,
    customerName: string,
    customerPhone: string | undefined,
    subject: string,
    message: string,
    conversationId: number,
    adminName: string = 'Équipe Support'
  ): Promise<{ email: NotificationResponse; sms?: NotificationResponse }> => {
    // Envoyer l'email
    const emailResult = await notificationService.notifyNewConversation(
      customerEmail,
      customerName,
      subject,
      message,
      conversationId,
      adminName
    );

    const result: { email: NotificationResponse; sms?: NotificationResponse } = {
      email: emailResult
    };

    // Envoyer SMS si numéro disponible
    if (customerPhone) {
      const smsMessage = `Nouvelle conversation JosNet: "${subject}". Consultez votre email ${customerEmail} pour plus de détails.`;
      
      const smsResult = await notificationService.sendSMSNotification({
        recipient_phone: customerPhone,
        recipient_name: customerName,
        message: smsMessage,
        conversation_id: conversationId
      });

      result.sms = smsResult;
    }

    return result;
  }
};

export default notificationService;
