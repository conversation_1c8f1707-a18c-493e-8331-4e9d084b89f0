from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from .models import Promotion, Campaign
from .serializers import PromotionSerializer, CampaignSerializer
from products.models import Product
from authentication.permissions import IsStaffOrAdmin

class PromotionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for promotions.
    """
    queryset = Promotion.objects.all()
    serializer_class = PromotionSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active', 'applies_to', 'target_id']
    permission_classes = [IsStaffOrAdmin]

class CampaignViewSet(viewsets.ModelViewSet):
    """
    API endpoint for marketing campaigns.
    """
    queryset = Campaign.objects.all()
    serializer_class = CampaignSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active']
    permission_classes = [IsStaffOrAdmin]
    
    @action(detail=True, methods=['post'])
    def add_product(self, request, pk=None):
        """
        Add a product to the campaign.
        
        Example request:
        {
            "product_id": 1
        }
        """
        campaign = self.get_object()
        product_id = request.data.get('product_id')
        
        if not product_id:
            return Response(
                {"error": "product_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            product = Product.objects.get(pk=product_id)
            campaign.products.add(product)
            return Response({"status": "product added to campaign"})
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['post'])
    def remove_product(self, request, pk=None):
        """
        Remove a product from the campaign.
        
        Example request:
        {
            "product_id": 1
        }
        """
        campaign = self.get_object()
        product_id = request.data.get('product_id')
        
        if not product_id:
            return Response(
                {"error": "product_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            product = Product.objects.get(pk=product_id)
            campaign.products.remove(product)
            return Response({"status": "product removed from campaign"})
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """
        Get campaigns for a specific product.
        
        Example request:
        GET /api/v1/marketing/campaigns/by_product/?product_id=1
        """
        product_id = request.query_params.get('product_id')
        
        if not product_id:
            return Response(
                {"error": "product_id query parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        campaigns = self.get_queryset().filter(products__id=product_id)
        serializer = self.get_serializer(campaigns, many=True)
        return Response(serializer.data)
