from django.urls import path, include
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework.routers import DefaultRouter
from .views import (
    RegisterView,
    LoginView,
    LogoutView,
    UserProfileView,
    ProfilePictureUploadView,
    PasswordChangeView,
    PasswordResetRequestView,
    PasswordResetConfirmView,
    EmailVerificationView,
    ResendVerificationView,
    UserAddressViewSet,
    LoyaltyTransactionViewSet,
    UserLoyaltyView,
    AdminUserViewSet,
    DashboardStatsView
)
from .views.suggestions_views import SuggestionsView
from products.views_user_suggestions import ProductSuggestionViewSet, ProductSuggestionCommentViewSet
from .views.account_api import returns_api, return_detail_api, claims_api, claim_detail_api
from .views_activation import (
    AccountActivationView,
    ResendActivationView,
    ActivationStatusView,
    check_activation_token
)

# Create a router for ViewSets
router = DefaultRouter()
router.register(r'addresses', UserAddressViewSet, basename='user-address')
router.register(r'loyalty/transactions', LoyaltyTransactionViewSet, basename='loyalty-transaction')
router.register(r'product-suggestions', ProductSuggestionViewSet, basename='product-suggestion')

# Create a router for admin ViewSets
admin_router = DefaultRouter()
admin_router.register(r'users', AdminUserViewSet, basename='admin-user')

urlpatterns = [
    # Authentication endpoints
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # User profile
    path('account/upload-picture/', ProfilePictureUploadView.as_view(), name='profile_picture_upload'),
    path('profile/', UserProfileView.as_view(), name='profile'),

    # Password management
    path('password/change/', PasswordChangeView.as_view(), name='password_change'),
    path('password/reset/request/', PasswordResetRequestView.as_view(), name='password_reset_request'),
    path('password/reset/confirm/', PasswordResetConfirmView.as_view(), name='password_reset_confirm'),

    # Email verification
    path('email/verify/', EmailVerificationView.as_view(), name='email_verify'),
    path('email/verify/resend/', ResendVerificationView.as_view(), name='email_verify_resend'),

    # Account activation
    path('account/activate/', AccountActivationView.as_view(), name='account_activate'),
    path('account/activation/resend/', ResendActivationView.as_view(), name='resend_activation'),
    path('account/activation/status/', ActivationStatusView.as_view(), name='activation_status'),
    path('account/activation/check/<uuid:token>/', check_activation_token, name='check_activation_token'),

    # User addresses and loyalty
    path('', include(router.urls)),
    path('loyalty/', UserLoyaltyView.as_view(), name='user-loyalty'),

    # Dashboard
    path('dashboard/', DashboardStatsView.as_view(), name='dashboard-stats'),

    # Suggestions
    path('suggestions/', SuggestionsView.as_view(), name='suggestions'),

    # Admin endpoints
    path('admin/', include(admin_router.urls)),
    
    # Account returns and claims endpoints
    path('account/returns/', returns_api, name='account-returns'),
    path('account/returns/<uuid:pk>/', return_detail_api, name='account-return-detail'),
    path('account/claims/', claims_api, name='account-claims'),
    path('account/claims/<uuid:pk>/', claim_detail_api, name='account-claim-detail'),
]
