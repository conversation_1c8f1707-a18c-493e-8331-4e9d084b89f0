from rest_framework import status, generics, permissions, viewsets, filters
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON>PartParser, FormParser
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Sum
from datetime import timed<PERSON>ta
from django.conf import settings
import uuid

from .serializers import (
    UserSerializer,
    UserRegistrationSerializer,
    UserProfileSerializer,
    UserProfileExtendedSerializer,
    LoginSerializer,
    PasswordChangeSerializer,
    PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer,
    EmailVerificationSerializer,
    UserAddressSerializer,
    UserAddressListSerializer,
    LoyaltyTransactionSerializer,
    UserLoyaltySerializer,
    ProfilePictureUploadSerializer
)
from .models import UserVerification, PasswordReset, UserAddress, LoyaltyTransaction

User = get_user_model()

class RegisterView(generics.CreateAPIView):
    """
    API endpoint for user registration with email verification and auto-login.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = UserRegistrationSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            # Créer l'utilisateur (non vérifié)
            user = serializer.save()
            user.is_verified = False  # S'assurer que l'utilisateur n'est pas vérifié
            user.save()

            # Envoyer l'email de vérification
            try:
                from .utils import send_verification_email
                send_verification_email(user)
                email_sent = True
                email_message = "Un email de vérification a été envoyé à votre adresse."
            except Exception as e:
                email_sent = False
                email_message = f"Erreur lors de l'envoi de l'email: {str(e)}"
                print(f"Erreur envoi email: {e}")

            # Générer les tokens JWT pour connexion automatique
            refresh = RefreshToken.for_user(user)

            # Retourner la réponse avec tokens et données réelles
            return Response({
                'user': UserProfileSerializer(user).data,
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'message': 'Inscription réussie! Vous êtes maintenant connecté.',
                'email_verification': {
                    'sent': email_sent,
                    'message': email_message,
                    'required': True
                },
                'auto_login': True  # Indique au frontend qu'il peut connecter automatiquement
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LoginView(APIView):
    """
    API endpoint for user login.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = LoginSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']
        refresh = RefreshToken.for_user(user)

        return Response({
            'user': UserSerializer(user).data,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }, status=status.HTTP_200_OK)

class LogoutView(APIView):
    """
    API endpoint for user logout.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        try:
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            return Response({'message': 'Logout successful'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class UserProfileView(generics.RetrieveUpdateAPIView):
    """
    API endpoint for retrieving and updating user profile.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserProfileExtendedSerializer

    def get_object(self):
        return self.request.user

class ProfilePictureUploadView(APIView):
    """API endpoint for uploading a profile picture."""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = ProfilePictureUploadSerializer(data=request.data)
        if serializer.is_valid():
            user.profile_picture = serializer.validated_data['profile_picture']
            user.save()
            return Response(UserProfileSerializer(user).data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordChangeView(APIView):
    """
    API endpoint for changing password.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = PasswordChangeSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user = request.user

            # Check old password
            if not user.check_password(serializer.validated_data['old_password']):
                return Response({'old_password': ['Wrong password.']}, status=status.HTTP_400_BAD_REQUEST)

            # Set new password
            user.set_password(serializer.validated_data['new_password'])
            user.save()

            return Response({'message': 'Password changed successfully.'}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetRequestView(APIView):
    """
    API endpoint for requesting password reset.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = PasswordResetRequestSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']

            try:
                user = User.objects.get(email=email)

                # Generate reset token
                token = str(uuid.uuid4())
                expires_at = timezone.now() + timedelta(hours=24)

                # Save reset token
                PasswordReset.objects.create(
                    user=user,
                    token=token,
                    expires_at=expires_at
                )

                # In a real application, send an email with the reset link
                # For now, just return the token in the response (for testing)
                reset_link = f"{settings.FRONTEND_URL}/reset-password/{token}"

                return Response({
                    'message': 'Password reset email sent.',
                    'token': token,  # Remove this in production
                    'reset_link': reset_link  # Remove this in production
                }, status=status.HTTP_200_OK)

            except User.DoesNotExist:
                # Don't reveal that the user doesn't exist
                pass

            # Always return success to prevent email enumeration
            return Response({'message': 'If your email is registered, you will receive a password reset link.'},
                           status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetConfirmView(APIView):
    """
    API endpoint for confirming password reset.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = PasswordResetConfirmSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            new_password = serializer.validated_data['new_password']
            reset = serializer.validated_data['reset_object']

            # Reset password
            user = reset.user
            user.set_password(new_password)
            user.save()

            # Mark token as used
            reset.is_used = True
            reset.save()

            return Response({'message': 'Password reset successful.'}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class EmailVerificationView(APIView):
    """
    API endpoint for email verification with welcome email.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = EmailVerificationSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            token = serializer.validated_data['token']

            try:
                # Décoder le token au format uid-token
                from django.utils.http import urlsafe_base64_decode
                from django.utils.encoding import force_str
                from django.contrib.auth.tokens import default_token_generator

                uid, token_part = token.split('-', 1)
                uid = force_str(urlsafe_base64_decode(uid))
                user = User.objects.get(pk=uid)

                # Vérifier le token
                if not default_token_generator.check_token(user, token_part):
                    return Response({'error': 'Token invalide ou expiré'}, status=status.HTTP_400_BAD_REQUEST)

                # Vérifier si l'utilisateur n'est pas déjà vérifié
                if user.is_verified:
                    return Response({'message': 'Email déjà vérifié'}, status=status.HTTP_200_OK)

                # Marquer l'utilisateur comme vérifié
                user.is_verified = True
                user.save()

                # Envoyer l'email de bienvenue
                try:
                    from .utils import send_welcome_email
                    send_welcome_email(user)
                except Exception as e:
                    print(f"Erreur envoi email de bienvenue: {e}")

                return Response({
                    'message': 'Email vérifié avec succès! Bienvenue sur JOSNET NETWORK.',
                    'user': UserProfileSerializer(user).data
                }, status=status.HTTP_200_OK)

            except (TypeError, ValueError, OverflowError, User.DoesNotExist):
                return Response({'error': 'Token invalide'}, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ResendVerificationView(APIView):
    """
    API endpoint for resending verification email.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user

        if user.is_verified:
            return Response({'message': 'Email already verified.'}, status=status.HTTP_400_BAD_REQUEST)

        # Delete existing verification if any
        UserVerification.objects.filter(user=user).delete()

        # Create new verification token
        token = str(uuid.uuid4())
        expires_at = timezone.now() + timedelta(days=1)
        UserVerification.objects.create(user=user, token=token, expires_at=expires_at)

        # In a real application, send an email with the verification link
        # For now, just return the token in the response (for testing)
        verification_link = f"{settings.FRONTEND_URL}/verify-email/{token}"

        return Response({
            'message': 'Verification email sent.',
            'token': token,  # Remove this in production
            'verification_link': verification_link  # Remove this in production
        }, status=status.HTTP_200_OK)

class UserAddressViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user addresses.
    """
    serializer_class = UserAddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Return addresses for the current user only."""
        return UserAddress.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Save the user when creating a new address."""
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set an address as the default for its type."""
        address = self.get_object()
        address.is_default = True
        address.save()
        return Response({'status': 'Address set as default'})

class LoyaltyTransactionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing loyalty transactions.
    """
    serializer_class = LoyaltyTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Return transactions for the current user only."""
        return LoyaltyTransaction.objects.filter(user=self.request.user)

class UserLoyaltyView(generics.RetrieveAPIView):
    """
    API endpoint for retrieving user loyalty information.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserLoyaltySerializer

    def get_object(self):
        return self.request.user


class EmailVerificationStatusView(APIView):
    """
    Endpoint pour vérifier le statut de vérification d'email d'un utilisateur
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        user = request.user

        return Response({
            'user_id': user.id,
            'email': user.email,
            'is_verified': getattr(user, 'is_verified', False),
            'can_access_dashboard': getattr(user, 'is_verified', False),
            'verification_required': not getattr(user, 'is_verified', False),
            'message': 'Email vérifié' if getattr(user, 'is_verified', False) else 'Vérification d\'email requise'
        })


class ResendVerificationEmailView(APIView):
    """
    Endpoint pour renvoyer l'email de vérification
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        user = request.user

        # Vérifier si déjà vérifié
        if getattr(user, 'is_verified', False):
            return Response({
                'message': 'Votre email est déjà vérifié.',
                'is_verified': True
            }, status=status.HTTP_200_OK)

        # Renvoyer l'email de vérification
        try:
            from .utils import send_verification_email
            send_verification_email(user)

            return Response({
                'message': 'Email de vérification renvoyé avec succès.',
                'email_sent': True,
                'next_step': 'Consultez votre boîte email Gmail.'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Erreur lors de l\'envoi de l\'email',
                'message': str(e),
                'email_sent': False
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
