import React, { useEffect, useState } from 'react';
import { useSearchPara<PERSON>, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Loader2, Mail } from 'lucide-react';
import { useTranslation } from '@/contexts/TranslationContext';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';

const VerifyEmail: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'invalid'>('loading');
  const [message, setMessage] = useState('');
  
  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setStatus('invalid');
      setMessage(t('auth.invalid_verification_link', 'Lien de vérification invalide'));
      return;
    }

    verifyEmail(token);
  }, [token, t]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await fetch('/api/v1/email/verify/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        setMessage(data.message || t('auth.email_verified_success', 'Email vérifié avec succès'));
      } else {
        setStatus('error');

        // Messages d'erreur plus précis
        if (response.status === 400 && data.message && data.message.includes('déjà vérifié')) {
          setMessage('Votre email est déjà vérifié ! Vous pouvez vous connecter.');
        } else if (response.status === 400 && data.error && data.error.includes('expiré')) {
          setMessage('Le lien de vérification a expiré. Demandez un nouveau lien.');
        } else if (response.status === 400 && data.error && data.error.includes('invalide')) {
          setMessage('Lien de vérification invalide. Vérifiez le lien ou demandez-en un nouveau.');
        } else {
          setMessage(data.error || data.message || data.token?.[0] || t('auth.verification_failed', 'Échec de la vérification'));
        }
      }
    } catch (error) {
      setStatus('error');
      setMessage(t('auth.verification_error', 'Erreur lors de la vérification'));
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-16 w-16 text-green-500" />;
      case 'error':
      case 'invalid':
        return <XCircle className="h-16 w-16 text-red-500" />;
      default:
        return <Mail className="h-16 w-16 text-gray-400" />;
    }
  };

  const getStatusTitle = () => {
    switch (status) {
      case 'loading':
        return t('auth.verifying_email', 'Vérification en cours...');
      case 'success':
        return t('auth.email_verified', 'Email vérifié !');
      case 'error':
        return t('auth.verification_failed', 'Vérification échouée');
      case 'invalid':
        return t('auth.invalid_link', 'Lien invalide');
      default:
        return t('auth.email_verification', 'Vérification d\'email');
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
      case 'invalid':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {getStatusIcon()}
              </div>
              <CardTitle className={`text-2xl ${getStatusColor()}`}>
                {getStatusTitle()}
              </CardTitle>
              <CardDescription>
                {status === 'loading' && t('auth.please_wait', 'Veuillez patienter pendant que nous vérifions votre email...')}
                {status === 'success' && t('auth.email_verified_desc', 'Votre adresse email a été vérifiée avec succès. Vous pouvez maintenant vous connecter.')}
                {status === 'error' && t('auth.verification_failed_desc', 'La vérification de votre email a échoué. Le lien peut être expiré ou invalide.')}
                {status === 'invalid' && t('auth.invalid_link_desc', 'Le lien de vérification est invalide ou manquant.')}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600">
                {message}
              </p>
              
              <div className="space-y-2">
                {status === 'success' && (
                  <Button asChild className="w-full">
                    <Link to="/login">
                      {t('auth.login', 'Se connecter')}
                    </Link>
                  </Button>
                )}
                
                {(status === 'error' || status === 'invalid') && (
                  <div className="space-y-2">
                    <Button asChild variant="outline" className="w-full">
                      <Link to="/register">
                        {t('auth.register_again', 'S\'inscrire à nouveau')}
                      </Link>
                    </Button>
                    <Button asChild variant="outline" className="w-full">
                      <Link to="/login">
                        {t('auth.try_login', 'Essayer de se connecter')}
                      </Link>
                    </Button>
                  </div>
                )}
                
                <Button asChild variant="ghost" className="w-full">
                  <Link to="/">
                    {t('nav.home', 'Retour à l\'accueil')}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
          
          {/* Informations supplémentaires */}
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>
              {t('auth.verification_help', 'Besoin d\'aide ?')}{' '}
              <Link to="/contact" className="text-blue-600 hover:underline">
                {t('nav.contact', 'Contactez-nous')}
              </Link>
            </p>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default VerifyEmail;
