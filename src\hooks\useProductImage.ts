import { useState, useEffect } from 'react';
import { ProductDetail, ProductListItem } from '@/services/productApi';
import { API_BASE_URL } from '@/services/apiConfig';

interface UseProductImageResult {
  imageSrc: string | null;
  isLoading: boolean;
  hasError: boolean;
  retry: () => void;
}

/**
 * Hook personnalisé pour gérer les images de produits
 * Optimisé pour les performances et la fiabilité
 */
export const useProductImage = (product: ProductDetail | ProductListItem): UseProductImageResult => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const processImageUrl = (url: string): string => {
    // Si l'URL est déjà absolue, la retourner telle quelle
    if (url.startsWith('http')) {
      return url;
    }
    
    // Si l'URL commence par /media/, ajouter le base URL
    if (url.startsWith('/media/')) {
      return `${API_BASE_URL}${url}`;
    }
    
    // Sinon, construire l'URL complète
    return `${API_BASE_URL}/media/${url}`;
  };

  const findBestImageUrl = (): string | null => {
    // 1. Priorité absolue : primary_image de l'API
    if (product.primary_image?.image) {
      console.log('🖼️ Primary image trouvée:', product.primary_image.image);
      return processImageUrl(product.primary_image.image);
    }

    // 2. Deuxième priorité : première image de la liste
    if ('images' in product && Array.isArray(product.images) && product.images.length > 0) {
      console.log('🖼️ Première image de la liste trouvée:', product.images[0].image);
      return processImageUrl(product.images[0].image);
    }

    // 3. Fallback : propriété image directe (si elle existe)
    if ('image' in product && typeof product.image === 'string' && product.image !== '/placeholder.svg') {
      console.log('🖼️ Image directe trouvée:', product.image);
      return processImageUrl(product.image);
    }

    console.warn('⚠️ Aucune image trouvée pour le produit:', product.id, product.name);
    return null;
  };

  const loadImage = async () => {
    setIsLoading(true);
    setHasError(false);

    const imageUrl = findBestImageUrl();
    
    if (!imageUrl) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    // Tester si l'image est accessible
    try {
      const img = new Image();
      
      img.onload = () => {
        console.log('✅ Image chargée avec succès:', imageUrl);
        setImageSrc(imageUrl);
        setIsLoading(false);
        setHasError(false);
      };
      
      img.onerror = () => {
        console.error('❌ Erreur de chargement de l\'image:', imageUrl);
        setHasError(true);
        setIsLoading(false);
      };
      
      img.src = imageUrl;
      
    } catch (error) {
      console.error('❌ Erreur lors du test de l\'image:', error);
      setHasError(true);
      setIsLoading(false);
    }
  };

  const retry = () => {
    loadImage();
  };

  useEffect(() => {
    if (!product) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    loadImage();
  }, [product]);

  return {
    imageSrc,
    isLoading,
    hasError,
    retry
  };
};

/**
 * Hook simplifié pour obtenir directement l'URL d'image sans chargement
 */
export const useProductImageUrl = (product: ProductDetail | ProductListItem): string | null => {
  const processImageUrl = (url: string): string => {
    if (url.startsWith('http')) return url;
    if (url.startsWith('/media/')) return `${API_BASE_URL}${url}`;
    return `${API_BASE_URL}/media/${url}`;
  };

  // Priorité : primary_image > première image > image directe
  if (product.primary_image?.image) {
    return processImageUrl(product.primary_image.image);
  }

  if ('images' in product && Array.isArray(product.images) && product.images.length > 0) {
    return processImageUrl(product.images[0].image);
  }

  if ('image' in product && typeof product.image === 'string' && product.image !== '/placeholder.svg') {
    return processImageUrl(product.image);
  }

  return null;
};

export default useProductImage;
