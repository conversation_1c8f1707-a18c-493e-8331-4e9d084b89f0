import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Save, Image, AlertCircle } from 'lucide-react';
import cmsApi from '@/services/cmsApi';

// Schéma de validation pour les paramètres du site
const siteSettingsSchema = z.object({
  site_title: z.string().min(1, 'Le titre du site est requis'),
  site_description: z.string().min(1, 'La description du site est requise'),
  contact_email: z.string().email('Email invalide'),
  contact_phone: z.string().min(1, 'Le téléphone est requis'),
  contact_address: z.string().min(1, 'L\'adresse est requise'),
  facebook_url: z.string().url('URL Facebook invalide').optional().or(z.literal('')),
  twitter_url: z.string().url('URL Twitter invalide').optional().or(z.literal('')),
  instagram_url: z.string().url('URL Instagram invalide').optional().or(z.literal('')),
  linkedin_url: z.string().url('URL LinkedIn invalide').optional().or(z.literal('')),
});

type SiteSettingsFormValues = z.infer<typeof siteSettingsSchema>;

const SiteSettingsForm = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Récupérer les paramètres actuels
  const {
    data: currentSettings,
    isLoading: isLoadingSettings,
    isError: isSettingsError
  } = useQuery({
    queryKey: ['siteSettings'],
    queryFn: cmsApi.getCurrentSiteSettings,
    retry: 2
  });

  // Formulaire avec React Hook Form
  const form = useForm<SiteSettingsFormValues>({
    resolver: zodResolver(siteSettingsSchema),
    defaultValues: {
      site_title: '',
      site_description: '',
      contact_email: '',
      contact_phone: '',
      contact_address: '',
      facebook_url: '',
      twitter_url: '',
      instagram_url: '',
      linkedin_url: '',
    },
  });

  // Mettre à jour les valeurs par défaut quand les données sont chargées
  React.useEffect(() => {
    if (currentSettings) {
      form.reset({
        site_title: currentSettings.site_title || '',
        site_description: currentSettings.site_description || '',
        contact_email: currentSettings.contact_email || '',
        contact_phone: currentSettings.contact_phone || '',
        contact_address: currentSettings.contact_address || '',
        facebook_url: currentSettings.facebook_url || '',
        twitter_url: currentSettings.twitter_url || '',
        instagram_url: currentSettings.instagram_url || '',
        linkedin_url: currentSettings.linkedin_url || '',
      });
    }
  }, [currentSettings, form]);

  // Mutation pour mettre à jour les paramètres
  const updateSettings = useMutation({
    mutationFn: (data: SiteSettingsFormValues) => {
      console.log('🔄 Mise à jour des paramètres du site:', data);
      return cmsApi.updateSiteSettings(currentSettings?.id || 1, data);
    },
    onSuccess: (data) => {
      console.log('✅ Paramètres mis à jour avec succès:', data);
      
      // Invalider les caches pour forcer le rafraîchissement
      queryClient.invalidateQueries({ queryKey: ['siteSettings'] });
      queryClient.invalidateQueries({ queryKey: ['footerData'] });
      queryClient.invalidateQueries({ queryKey: ['homePageCms'] });
      queryClient.invalidateQueries({ queryKey: ['siteConfiguration'] });
      
      toast({
        title: 'Paramètres mis à jour',
        description: 'Les paramètres du site ont été mis à jour avec succès. Les changements apparaîtront sur la page d\'accueil.',
      });
    },
    onError: (error: any) => {
      console.error('❌ Erreur lors de la mise à jour des paramètres:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de mettre à jour les paramètres du site. Vérifiez votre connexion.',
        variant: 'destructive',
      });
    },
  });

  // Gérer la soumission du formulaire
  const onSubmit = (data: SiteSettingsFormValues) => {
    console.log('📝 Soumission du formulaire des paramètres:', data);
    updateSettings.mutate(data);
  };

  if (isLoadingSettings) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>Chargement des paramètres...</span>
      </div>
    );
  }

  if (isSettingsError) {
    return (
      <div className="flex items-center justify-center py-8 text-amber-600">
        <AlertCircle className="h-6 w-6 mr-2" />
        <span>Erreur lors du chargement des paramètres. Utilisation des valeurs par défaut.</span>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Paramètres généraux */}
          <Card>
            <CardHeader>
              <CardTitle>Paramètres généraux</CardTitle>
              <CardDescription>Configuration globale du site</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="site_title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Titre du site</FormLabel>
                    <FormControl>
                      <Input placeholder="JOSNET NETWORK" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="site_description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description du site</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Solutions IT et télécommunications au Burundi"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <Label>Logo du site</Label>
                <div className="flex items-center gap-2 mt-1">
                  <div className="h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
                    <Image size={16} className="text-gray-500" />
                  </div>
                  <Button type="button" variant="outline" size="sm">
                    Changer le logo
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Fonctionnalité d'upload de logo à venir
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Informations de contact */}
          <Card>
            <CardHeader>
              <CardTitle>Informations de contact</CardTitle>
              <CardDescription>Coordonnées affichées sur le site</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="contact_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email de contact</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Téléphone</FormLabel>
                    <FormControl>
                      <Input placeholder="+257 12 345 678" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact_address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Adresse</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="123 Avenue Principale, Bujumbura, Burundi"
                        rows={2}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Réseaux sociaux */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Réseaux sociaux</CardTitle>
              <CardDescription>Liens vers vos profils de réseaux sociaux</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="facebook_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Facebook</FormLabel>
                      <FormControl>
                        <Input placeholder="https://facebook.com/josnetnetwork" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="twitter_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Twitter</FormLabel>
                      <FormControl>
                        <Input placeholder="https://twitter.com/josnetnetwork" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="instagram_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Instagram</FormLabel>
                      <FormControl>
                        <Input placeholder="https://instagram.com/josnetnetwork" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="linkedin_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>LinkedIn</FormLabel>
                      <FormControl>
                        <Input placeholder="https://linkedin.com/company/josnetnetwork" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bouton de sauvegarde */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={updateSettings.isPending}
            className="flex items-center gap-2"
          >
            {updateSettings.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            {updateSettings.isPending ? 'Sauvegarde...' : 'Enregistrer les paramètres'}
          </Button>
        </div>

        {/* Indicateur de statut */}
        <div className="text-center text-sm text-gray-500">
          <p>Les modifications seront visibles immédiatement sur la page d'accueil et le footer.</p>
        </div>
      </form>
    </Form>
  );
};

export default SiteSettingsForm;
