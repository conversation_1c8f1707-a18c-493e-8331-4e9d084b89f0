
import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

def validate_gmail_email(email):
    """
    Valide que l'email est un email Gmail valide
    """
    if not email:
        raise ValidationError(_("L'email est requis."))
    
    # Vérifier le format email de base
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_regex, email):
        raise ValidationError(_("Format d'email invalide."))
    
    # Vérifier que c'est un email Gmail
    gmail_domains = ['gmail.com', 'googlemail.com']
    domain = email.split('@')[1].lower()
    
    if domain not in gmail_domains:
        raise ValidationError(
            _("Seuls les emails Gmail (@gmail.com) sont acceptés pour des raisons de sécurité.")
        )
    
    # Vérifications supplémentaires pour Gmail
    local_part = email.split('@')[0]
    
    # Gmail ne permet pas les points consécutifs
    if '..' in local_part:
        raise ValidationError(_("L'email Gmail ne peut pas contenir des points consécutifs."))
    
    # Gmail ne permet pas de commencer ou finir par un point
    if local_part.startswith('.') or local_part.endswith('.'):
        raise ValidationError(_("L'email Gmail ne peut pas commencer ou finir par un point."))
    
    return email

def is_gmail_email(email):
    """
    Vérifie si un email est un email Gmail (sans lever d'exception)
    """
    try:
        validate_gmail_email(email)
        return True
    except ValidationError:
        return False
