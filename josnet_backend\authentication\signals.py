"""
Signal handlers for authentication app.
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from orders.models import Order
from .models import User, LoyaltyTransaction
from .loyalty import add_points, calculate_order_points, SIGNUP_BONUS_POINTS
from .utils import send_welcome_email

@receiver(post_save, sender=User)
def create_user_loyalty_bonus(sender, instance, created, **kwargs):
    """
    Award signup bonus points to new users.
    """
    if created and SIGNUP_BONUS_POINTS > 0:
        add_points(
            user=instance,
            points=SIGNUP_BONUS_POINTS,
            description="Signup bonus",
            expires_in_days=365
        )


@receiver(post_save, sender=User)
def send_welcome_email_on_signup(sender, instance, created, **kwargs):
    """
    Envoie automatiquement un email de bienvenue aux nouveaux utilisateurs.
    """
    if created:
        # Envoyer l'email de bienvenue de manière asynchrone pour ne pas bloquer l'inscription
        try:
            send_welcome_email(instance)
        except Exception as e:
            # Logger l'erreur mais ne pas faire échouer l'inscription
            print(f"Erreur lors de l'envoi de l'email de bienvenue pour {instance.email}: {e}")

@receiver(post_save, sender=Order)
def process_order_loyalty_points(sender, instance, **kwargs):
    """
    Award loyalty points when an order is delivered.
    """
    # Only award points when the order is delivered and has a user
    if instance.status == 'delivered' and instance.user and instance.delivered_at:
        # Check if points have already been awarded for this order
        existing_transaction = LoyaltyTransaction.objects.filter(
            user=instance.user,
            order=instance,
            transaction_type='earn'
        ).exists()
        
        if not existing_transaction:
            # Calculate points based on order total
            points = calculate_order_points(float(instance.total))
            
            # Award points
            add_points(
                user=instance.user,
                points=points,
                description=f"Points for order #{instance.order_number}",
                order=instance,
                expires_in_days=365
            )
