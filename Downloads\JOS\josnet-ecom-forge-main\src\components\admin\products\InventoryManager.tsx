import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  Minus, 
  AlertCircle, 
  Package, 
  History, 
  RefreshCw, 
  Calendar, 
  Clock, 
  User, 
  Tag, 
  FileText, 
  AlertTriangle 
} from 'lucide-react';
import { 
  productApi, 
  Inventory, 
  StockMovement, 
  StockReservation 
} from '@/services/productApi';
import { formatPrice } from '@/lib/utils';

interface InventoryManagerProps {
  productId?: number;
  variantId?: number;
  inventoryId?: number;
}

const InventoryManager: React.FC<InventoryManagerProps> = ({ 
  productId, 
  variantId, 
  inventoryId 
}) => {
  const [isAdjustDialogOpen, setIsAdjustDialogOpen] = useState(false);
  const [isReserveDialogOpen, setIsReserveDialogOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch inventory data
  const { 
    data: inventory, 
    isLoading: isLoadingInventory, 
    isError: isInventoryError 
  } = useQuery({
    queryKey: ['inventory', { productId, variantId, inventoryId }],
    queryFn: () => {
      if (inventoryId) {
        return productApi.getInventory(inventoryId);
      } else if (productId) {
        return productApi.getProductInventory(productId);
      } else if (variantId) {
        return productApi.getVariantInventory(variantId);
      }
      throw new Error('Either productId, variantId, or inventoryId must be provided');
    },
    enabled: !!(productId || variantId || inventoryId),
  });
  
  // Adjust inventory mutation
  const adjustInventoryMutation = useMutation({
    mutationFn: (data: any) => productApi.adjustInventory(inventory!.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['inventory', { productId, variantId, inventoryId }] 
      });
      toast({
        title: 'Inventory adjusted',
        description: 'The inventory has been adjusted successfully.',
      });
      setIsAdjustDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to adjust inventory',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Reserve inventory mutation
  const reserveInventoryMutation = useMutation({
    mutationFn: (data: any) => productApi.reserveInventory(inventory!.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['inventory', { productId, variantId, inventoryId }] 
      });
      toast({
        title: 'Stock reserved',
        description: 'The stock has been reserved successfully.',
      });
      setIsReserveDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to reserve stock',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Release reservation mutation
  const releaseReservationMutation = useMutation({
    mutationFn: (data: { inventoryId: number, reservationId: number }) => 
      productApi.releaseReservation(data.inventoryId, { reservation_id: data.reservationId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['inventory', { productId, variantId, inventoryId }] 
      });
      toast({
        title: 'Reservation released',
        description: 'The reservation has been released successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to release reservation',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  // Get movement reason label
  const getReasonLabel = (reason: string) => {
    const reasons: Record<string, string> = {
      'purchase': 'Purchase',
      'sale': 'Sale',
      'return': 'Return',
      'adjustment': 'Adjustment',
      'loss': 'Loss/Damage',
      'transfer': 'Transfer',
      'other': 'Other'
    };
    return reasons[reason] || reason;
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Inventory Management</CardTitle>
        <CardDescription>
          Manage product inventory, stock movements, and reservations.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {isLoadingInventory ? (
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : isInventoryError || !inventory ? (
          <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span>Failed to load inventory data. Please try again.</span>
          </div>
        ) : (
          <>
            {/* Inventory Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Package className="h-5 w-5 text-blue-500" />
                  <h3 className="text-sm font-medium">Total Stock</h3>
                </div>
                <p className="text-2xl font-bold">{inventory.quantity}</p>
                <p className="text-sm text-gray-500">units</p>
              </div>
              
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Tag className="h-5 w-5 text-amber-500" />
                  <h3 className="text-sm font-medium">Reserved</h3>
                </div>
                <p className="text-2xl font-bold">{inventory.reserved_quantity}</p>
                <p className="text-sm text-gray-500">units</p>
              </div>
              
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Package className="h-5 w-5 text-green-500" />
                  <h3 className="text-sm font-medium">Available</h3>
                </div>
                <p className="text-2xl font-bold">{inventory.available_quantity}</p>
                <p className="text-sm text-gray-500">units</p>
              </div>
            </div>
            
            {/* Status and Actions */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between bg-gray-50 p-4 rounded-md">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Status:</span>
                  {inventory.is_in_stock ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      In Stock
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Out of Stock
                    </span>
                  )}
                  
                  {inventory.is_low_stock && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                      Low Stock
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  Low stock threshold: {inventory.low_stock_threshold} units
                </p>
                <p className="text-xs text-gray-500">
                  Last updated: {formatDate(inventory.last_checked)}
                </p>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={() => setIsAdjustDialogOpen(true)}
                  className="gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Adjust Stock</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={() => setIsReserveDialogOpen(true)}
                  className="gap-2"
                  disabled={inventory.available_quantity <= 0}
                >
                  <Tag className="h-4 w-4" />
                  <span>Reserve Stock</span>
                </Button>
              </div>
            </div>
            
            {/* Tabs for Movements and Reservations */}
            <Tabs defaultValue="movements">
              <TabsList>
                <TabsTrigger value="movements">Stock Movements</TabsTrigger>
                <TabsTrigger value="reservations">Reservations</TabsTrigger>
              </TabsList>
              
              {/* Stock Movements Tab */}
              <TabsContent value="movements" className="space-y-4">
                {inventory.movements && inventory.movements.length > 0 ? (
                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Reason</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Reference</TableHead>
                          <TableHead>User</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {inventory.movements.map((movement: StockMovement) => (
                          <TableRow key={movement.id}>
                            <TableCell>{formatDate(movement.created_at)}</TableCell>
                            <TableCell>{getReasonLabel(movement.reason)}</TableCell>
                            <TableCell className={`font-medium ${movement.quantity_changed > 0 ? 'text-green-600' : movement.quantity_changed < 0 ? 'text-red-600' : ''}`}>
                              {movement.quantity_changed > 0 ? '+' : ''}{movement.quantity_changed}
                            </TableCell>
                            <TableCell>{movement.reference || '-'}</TableCell>
                            <TableCell>{movement.created_by_name || '-'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center p-8 bg-gray-50 border border-dashed rounded-md">
                    <History className="h-10 w-10 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500">No stock movements yet.</p>
                  </div>
                )}
              </TabsContent>
              
              {/* Reservations Tab */}
              <TabsContent value="reservations" className="space-y-4">
                {inventory.reservations && inventory.reservations.length > 0 ? (
                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Reference</TableHead>
                          <TableHead>Expires</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {inventory.reservations.map((reservation: StockReservation) => (
                          <TableRow key={reservation.id}>
                            <TableCell>{formatDate(reservation.created_at)}</TableCell>
                            <TableCell className="font-medium">{reservation.quantity}</TableCell>
                            <TableCell>{reservation.reference || '-'}</TableCell>
                            <TableCell>
                              {reservation.expires_at ? formatDate(reservation.expires_at) : 'Never'}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => releaseReservationMutation.mutate({
                                  inventoryId: inventory.id,
                                  reservationId: reservation.id
                                })}
                                disabled={releaseReservationMutation.isPending}
                              >
                                Release
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center p-8 bg-gray-50 border border-dashed rounded-md">
                    <Tag className="h-10 w-10 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500">No active reservations.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
      
      {/* Adjust Stock Dialog */}
      {inventory && (
        <AdjustStockDialog
          isOpen={isAdjustDialogOpen}
          onOpenChange={setIsAdjustDialogOpen}
          inventory={inventory}
          onSubmit={(data) => adjustInventoryMutation.mutate(data)}
          isLoading={adjustInventoryMutation.isPending}
        />
      )}
      
      {/* Reserve Stock Dialog */}
      {inventory && (
        <ReserveStockDialog
          isOpen={isReserveDialogOpen}
          onOpenChange={setIsReserveDialogOpen}
          inventory={inventory}
          onSubmit={(data) => reserveInventoryMutation.mutate(data)}
          isLoading={reserveInventoryMutation.isPending}
        />
      )}
    </Card>
  );
};

interface AdjustStockDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  inventory: Inventory;
  onSubmit: (data: any) => void;
  isLoading: boolean;
}

const AdjustStockDialog: React.FC<AdjustStockDialogProps> = ({
  isOpen,
  onOpenChange,
  inventory,
  onSubmit,
  isLoading
}) => {
  const [quantity, setQuantity] = useState('0');
  const [reason, setReason] = useState('adjustment');
  const [reference, setReference] = useState('');
  const [notes, setNotes] = useState('');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    onSubmit({
      quantity: parseInt(quantity),
      reason,
      reference: reference || undefined,
      notes: notes || undefined
    });
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Adjust Stock</DialogTitle>
          <DialogDescription>
            Add or remove stock from inventory. Use positive numbers to add stock and negative numbers to remove stock.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="quantity" className="text-right">
                Quantity
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => setQuantity((parseInt(quantity) - 1).toString())}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <Input
                  id="quantity"
                  type="number"
                  value={quantity}
                  onChange={(e) => setQuantity(e.target.value)}
                  className="text-center"
                  required
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => setQuantity((parseInt(quantity) + 1).toString())}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reason" className="text-right">
                Reason
              </Label>
              <Select value={reason} onValueChange={setReason}>
                <SelectTrigger id="reason" className="col-span-3">
                  <SelectValue placeholder="Select a reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="purchase">Purchase</SelectItem>
                  <SelectItem value="sale">Sale</SelectItem>
                  <SelectItem value="return">Return</SelectItem>
                  <SelectItem value="adjustment">Adjustment</SelectItem>
                  <SelectItem value="loss">Loss/Damage</SelectItem>
                  <SelectItem value="transfer">Transfer</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reference" className="text-right">
                Reference
              </Label>
              <Input
                id="reference"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
                className="col-span-3"
                placeholder="e.g. Order #12345"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Input
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="col-span-3"
                placeholder="Additional information"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Adjusting...
                </>
              ) : (
                'Adjust Stock'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

interface ReserveStockDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  inventory: Inventory;
  onSubmit: (data: any) => void;
  isLoading: boolean;
}

const ReserveStockDialog: React.FC<ReserveStockDialogProps> = ({
  isOpen,
  onOpenChange,
  inventory,
  onSubmit,
  isLoading
}) => {
  const [quantity, setQuantity] = useState('1');
  const [reference, setReference] = useState('');
  const [notes, setNotes] = useState('');
  const [expiresAt, setExpiresAt] = useState('');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    onSubmit({
      quantity: parseInt(quantity),
      reference: reference || undefined,
      notes: notes || undefined,
      expires_at: expiresAt || undefined
    });
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Reserve Stock</DialogTitle>
          <DialogDescription>
            Reserve stock for a specific purpose, such as an order or a customer.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="quantity" className="text-right">
                Quantity
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  max={inventory.available_quantity}
                  value={quantity}
                  onChange={(e) => setQuantity(e.target.value)}
                  required
                />
                <div className="text-sm text-gray-500">
                  Max: {inventory.available_quantity}
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reference" className="text-right">
                Reference
              </Label>
              <Input
                id="reference"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
                className="col-span-3"
                placeholder="e.g. Order #12345"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="expires-at" className="text-right">
                Expires At
              </Label>
              <Input
                id="expires-at"
                type="datetime-local"
                value={expiresAt}
                onChange={(e) => setExpiresAt(e.target.value)}
                className="col-span-3"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Input
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="col-span-3"
                placeholder="Additional information"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || parseInt(quantity) < 1 || parseInt(quantity) > inventory.available_quantity}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Reserving...
                </>
              ) : (
                'Reserve Stock'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryManager;
