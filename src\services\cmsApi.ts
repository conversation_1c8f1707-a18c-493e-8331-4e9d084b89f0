import api from './api';

// Types
export interface Page {
  id: number;
  title: string;
  slug: string;
  content: string;
  template: string;
  status: string;
  meta_description: string;
  meta_keywords: string;
  author: User;
  created_at: string;
  updated_at: string;
}

export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  status: string;
  featured_image: string | null;
  categories: BlogCategory[];
  meta_description: string;
  meta_keywords: string;
  author: User;
  comments: Comment[];
  created_at: string;
  updated_at: string;
}

export interface Comment {
  id: number;
  post: number;
  author_name: string;
  author_email: string;
  content: string;
  status: string;
  user: User | null;
  created_at: string;
}

export interface Media {
  id: number;
  title: string;
  file: string;
  file_type: string;
  description: string;
  alt_text: string;
  uploader: User;
  file_url: string;
  file_size: number;
  file_name: string;
  created_at: string;
  updated_at: string;
}

export interface MenuItem {
  id: number;
  menu: number;
  title: string;
  url: string;
  page: number | null;
  page_title: string | null;
  parent: number | null;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface Menu {
  id: number;
  name: string;
  location: string;
  items: MenuItem[];
  created_at: string;
  updated_at: string;
}

export interface SiteSetting {
  id: number;
  site_title: string;
  site_description: string;
  contact_email: string;
  contact_phone: string;
  contact_address: string;
  facebook_url: string;
  twitter_url: string;
  instagram_url: string;
  linkedin_url: string;
  logo: string | null;
  favicon: string | null;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
}

// Données de fallback pour le CMS admin
const fallbackPages: Page[] = [
  {
    id: 1,
    title: "À propos de JOSNET",
    slug: "a-propos",
    content: "Contenu de la page À propos...",
    status: "published",
    template: "standard",
    meta_description: "Découvrez JOSNET NETWORK",
    meta_keywords: "JOSNET, réseau, télécommunications, Burundi",
    created_at: "2025-05-15T10:00:00Z",
    updated_at: "2025-05-15T10:00:00Z",
    author: { id: 1, username: "admin", email: "<EMAIL>", first_name: "Admin", last_name: "User" }
  },
  {
    id: 2,
    title: "Nos services",
    slug: "services",
    content: "Contenu de la page Services...",
    status: "published",
    template: "services",
    meta_description: "Services JOSNET NETWORK",
    meta_keywords: "services, JOSNET, télécommunications, IT",
    created_at: "2025-05-15T10:00:00Z",
    updated_at: "2025-05-15T10:00:00Z",
    author: { id: 1, username: "admin", email: "<EMAIL>", first_name: "Admin", last_name: "User" }
  }
];

const fallbackBlogPosts: BlogPost[] = [
  {
    id: 1,
    title: "Les tendances technologiques 2025",
    slug: "tendances-technologiques-2025",
    content: "Contenu de l'article...",
    excerpt: "Découvrez les innovations qui vont marquer 2025",
    status: "published",
    featured_image: null,
    meta_description: "Les innovations à suivre",
    meta_keywords: "technologie, innovation, 2025",
    comments: [],
    created_at: "2025-05-15T10:00:00Z",
    updated_at: "2025-05-15T10:00:00Z",
    author: { id: 1, username: "admin", email: "<EMAIL>", first_name: "Admin", last_name: "User" },
    categories: [{
      id: 1,
      name: "Technologie",
      slug: "technologie",
      description: "Articles sur la technologie",
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z"
    }]
  }
];

const fallbackMedia: Media[] = [
  {
    id: 1,
    title: "Image exemple",
    description: "Une image d'exemple",
    alt_text: "Image exemple",
    file: "/media/example.jpg",
    file_url: "/media/example.jpg",
    file_name: "example.jpg",
    file_type: "image/jpeg",
    file_size: 1024000,
    created_at: "2025-05-15T10:00:00Z",
    updated_at: "2025-05-15T10:00:00Z",
    uploader: { id: 1, username: "admin", email: "<EMAIL>", first_name: "Admin", last_name: "JOSNET" }
  }
];

// API Service
const cmsApi = {
  // Pages
  getPages: async () => {
    try {
      const response = await api.get('/cms/pages/');
      return { data: (response as any)?.results || response || fallbackPages };
    } catch (error) {
      console.warn('API CMS pages non disponible, utilisation des données de fallback:', error);
      return { data: fallbackPages };
    }
  },

  getPage: async (slug: string) => {
    try {
      const response = await api.get(`/cms/pages/${slug}/`);
      return response;
    } catch (error) {
      console.warn('API CMS page non disponible:', error);
      const page = fallbackPages.find(p => p.slug === slug);
      if (!page) throw new Error('Page non trouvée');
      return page;
    }
  },

  createPage: async (data: Partial<Page>) => {
    try {
      const response = await api.post('/cms/pages/', data);
      return response;
    } catch (error) {
      console.error('Erreur création page:', error);
      throw error;
    }
  },

  updatePage: async (slug: string, data: Partial<Page>) => {
    try {
      const response = await api.put(`/cms/pages/${slug}/`, data);
      return response;
    } catch (error) {
      console.error('Erreur mise à jour page:', error);
      throw error;
    }
  },

  deletePage: async (slug: string) => {
    try {
      await api.delete(`/cms/pages/${slug}/`);
    } catch (error) {
      console.error('Erreur suppression page:', error);
      throw error;
    }
  },

  // Blog Categories
  getBlogCategories: () => api.get<BlogCategory[]>('/cms/blog/categories/'),
  getBlogCategory: (slug: string) => api.get<BlogCategory>(`/cms/blog/categories/${slug}/`),
  createBlogCategory: (data: Partial<BlogCategory>) => api.post<BlogCategory>('/cms/blog/categories/', data),
  updateBlogCategory: (slug: string, data: Partial<BlogCategory>) => api.put<BlogCategory>(`/cms/blog/categories/${slug}/`, data),
  deleteBlogCategory: (slug: string) => api.delete(`/cms/blog/categories/${slug}/`),

  // Blog Posts
  getBlogPosts: async () => {
    try {
      const response = await api.get('/cms/blog/posts/');
      return { data: (response as any)?.results || response || fallbackBlogPosts };
    } catch (error) {
      console.warn('API CMS blog posts non disponible, utilisation des données de fallback:', error);
      return { data: fallbackBlogPosts };
    }
  },

  getBlogPost: async (slug: string) => {
    try {
      const response = await api.get(`/cms/blog/posts/${slug}/`);
      return response;
    } catch (error) {
      console.warn('API CMS blog post non disponible:', error);
      const post = fallbackBlogPosts.find(p => p.slug === slug);
      if (!post) throw new Error('Article non trouvé');
      return post;
    }
  },

  createBlogPost: async (data: Partial<BlogPost>) => {
    try {
      const response = await api.post('/cms/blog/posts/', data);
      return response;
    } catch (error) {
      console.error('Erreur création article:', error);
      throw error;
    }
  },

  updateBlogPost: async (slug: string, data: Partial<BlogPost>) => {
    try {
      const response = await api.put(`/cms/blog/posts/${slug}/`, data);
      return response;
    } catch (error) {
      console.error('Erreur mise à jour article:', error);
      throw error;
    }
  },

  deleteBlogPost: async (slug: string) => {
    try {
      await api.delete(`/cms/blog/posts/${slug}/`);
    } catch (error) {
      console.error('Erreur suppression article:', error);
      throw error;
    }
  },

  addComment: async (slug: string, data: Partial<Comment>) => {
    try {
      const response = await api.post(`/cms/blog/posts/${slug}/add_comment/`, data);
      return response;
    } catch (error) {
      console.error('Erreur ajout commentaire:', error);
      throw error;
    }
  },

  // Media
  getMedia: async () => {
    try {
      const response = await api.get('/cms/media/');
      return { data: (response as any)?.results || response || fallbackMedia };
    } catch (error) {
      console.warn('API CMS media non disponible, utilisation des données de fallback:', error);
      return { data: fallbackMedia };
    }
  },

  getMediaItem: async (id: number) => {
    try {
      const response = await api.get(`/cms/media/${id}/`);
      return response;
    } catch (error) {
      console.warn('API CMS media item non disponible:', error);
      const media = fallbackMedia.find(m => m.id === id);
      if (!media) throw new Error('Média non trouvé');
      return media;
    }
  },

  uploadMedia: async (data: FormData) => {
    try {
      const response = await api.post('/cms/media/', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      console.error('Erreur upload média:', error);
      throw error;
    }
  },

  updateMedia: async (id: number, data: Partial<Media>) => {
    try {
      const response = await api.put(`/cms/media/${id}/`, data);
      return response;
    } catch (error) {
      console.error('Erreur mise à jour média:', error);
      throw error;
    }
  },

  deleteMedia: async (id: number) => {
    try {
      await api.delete(`/cms/media/${id}/`);
    } catch (error) {
      console.error('Erreur suppression média:', error);
      throw error;
    }
  },

  // Menus
  getMenus: async () => {
    try {
      const response = await api.get('/cms/menus/');
      console.log('🔍 Réponse API menus:', response);

      // La réponse de l'API est directement les données, pas un objet avec .data
      if (response && typeof response === 'object') {
        // Si c'est une réponse paginée avec results
        if ('results' in response) {
          return { data: response.results };
        }
        // Si c'est directement un tableau
        if (Array.isArray(response)) {
          return { data: response };
        }
        // Si c'est un objet, le retourner tel quel
        return { data: response };
      }

      // Fallback si la réponse est vide ou invalide
      return { data: [] };
    } catch (error) {
      console.warn('API CMS menus non disponible:', error);

      // Données de fallback pour les menus
      const fallbackMenus = [
        {
          id: 1,
          name: 'Menu Principal',
          items: [
            { name: 'Accueil', url: '/', order: 1 },
            { name: 'Produits', url: '/products', order: 2 },
            { name: 'Services', url: '/services', order: 3 },
            { name: 'Contact', url: '/contact', order: 4 }
          ]
        }
      ];

      return { data: fallbackMenus };
    }
  },

  getMenu: async (id: number) => {
    try {
      const response = await api.get(`/cms/menus/${id}/`);
      return response; // La réponse est directement les données
    } catch (error) {
      console.warn('API CMS menu non disponible:', error);

      // Fallback pour un menu spécifique
      const fallbackMenu = {
        id: id,
        name: `Menu ${id}`,
        items: [
          { name: 'Accueil', url: '/', order: 1 },
          { name: 'Produits', url: '/products', order: 2 }
        ]
      };

      return fallbackMenu;
    }
  },

  getMenuItems: async (id: number) => {
    try {
      const response = await api.get(`/cms/menus/${id}/items/`);
      return response; // La réponse est directement les données
    } catch (error) {
      console.warn('API CMS menu items non disponible:', error);

      // Fallback pour les items de menu
      const fallbackItems = [
        { id: 1, name: 'Accueil', url: '/', order: 1 },
        { id: 2, name: 'Produits', url: '/products', order: 2 },
        { id: 3, name: 'Services', url: '/services', order: 3 },
        { id: 4, name: 'Contact', url: '/contact', order: 4 }
      ];

      return fallbackItems;
    }
  },

  createMenu: async (data: Partial<Menu>) => {
    try {
      const response = await api.post('/cms/menus/', data);
      return response;
    } catch (error) {
      console.error('Erreur création menu:', error);
      throw error;
    }
  },

  updateMenu: async (id: number, data: Partial<Menu>) => {
    try {
      const response = await api.put(`/cms/menus/${id}/`, data);
      return response;
    } catch (error) {
      console.error('Erreur mise à jour menu:', error);
      throw error;
    }
  },

  deleteMenu: async (id: number) => {
    try {
      await api.delete(`/cms/menus/${id}/`);
    } catch (error) {
      console.error('Erreur suppression menu:', error);
      throw error;
    }
  },

  getMenuItem: async (id: number) => {
    try {
      const response = await api.get(`/cms/menu-items/${id}/`);
      return response;
    } catch (error) {
      console.warn('API CMS menu item non disponible:', error);
      throw new Error('Élément de menu non trouvé');
    }
  },

  createMenuItem: async (data: Partial<MenuItem>) => {
    try {
      const response = await api.post('/cms/menu-items/', data);
      return response;
    } catch (error) {
      console.error('Erreur création élément menu:', error);
      throw error;
    }
  },

  updateMenuItem: async (id: number, data: Partial<MenuItem>) => {
    try {
      const response = await api.put(`/cms/menu-items/${id}/`, data);
      return response;
    } catch (error) {
      console.error('Erreur mise à jour élément menu:', error);
      throw error;
    }
  },

  deleteMenuItem: async (id: number) => {
    try {
      await api.delete(`/cms/menu-items/${id}/`);
    } catch (error) {
      console.error('Erreur suppression élément menu:', error);
      throw error;
    }
  },

  // Site Settings
  getSiteSettings: async () => {
    try {
      const response = await api.get('/cms/settings/');
      return (response as any)?.results || response || [];
    } catch (error) {
      console.warn('API CMS settings non disponible, utilisation des données de fallback:', error);

      // Retourner un tableau avec les paramètres par défaut
      return [{
        id: 1,
        site_title: "JOSNET NETWORK",
        site_description: "Solutions IT et télécommunications au Burundi",
        contact_email: "<EMAIL>",
        contact_phone: "+257 12 345 678",
        contact_address: "123 Avenue Principale, Bujumbura, Burundi",
        facebook_url: "https://facebook.com/josnetnetwork",
        twitter_url: "https://twitter.com/josnetnetwork",
        instagram_url: "https://instagram.com/josnetnetwork",
        linkedin_url: "https://linkedin.com/company/josnetnetwork",
        logo: null,
        favicon: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }];
    }
  },

  getCurrentSiteSettings: async () => {
    try {
      // Essayer d'abord l'API Core qui est plus stable
      try {
        const coreResponse = await api.get('/core/site-settings/current/');
        if (coreResponse && typeof coreResponse === 'object' && Object.keys(coreResponse).length > 0) {
          console.log('✅ Paramètres récupérés depuis Core API');
          return {
            id: (coreResponse as any)?.id || 1,
            site_title: (coreResponse as any)?.site_name || "JOSNET NETWORK",
            site_description: (coreResponse as any).site_description || "Solutions IT et télécommunications au Burundi",
            contact_email: (coreResponse as any).email_primary || "<EMAIL>",
            contact_phone: (coreResponse as any).phone_primary || "+257 12 345 678",
            contact_address: `${(coreResponse as any).address_line1 || '123 Avenue Principale'}, ${(coreResponse as any).city || 'Bujumbura'}, ${(coreResponse as any).country || 'Burundi'}`,
            facebook_url: (coreResponse as any).facebook_url || "https://facebook.com/josnetnetwork",
            twitter_url: (coreResponse as any).twitter_url || "https://twitter.com/josnetnetwork",
            instagram_url: (coreResponse as any).instagram_url || "https://instagram.com/josnetnetwork",
            linkedin_url: (coreResponse as any).linkedin_url || "https://linkedin.com/company/josnetnetwork",
            logo: (coreResponse as any).site_logo || null,
            favicon: null,
            created_at: (coreResponse as any).created_at || new Date().toISOString(),
            updated_at: (coreResponse as any).updated_at || new Date().toISOString()
          };
        }
      } catch (coreError) {
        console.log('Core API non disponible, essai CMS API...');
      }

      // Fallback vers CMS API
      const response = await api.get('/cms/settings/current/');
      const data = response;

      // Vérifier si on a des données valides
      if (data && typeof data === 'object' && Object.keys(data).length > 0) {
        console.log('✅ Paramètres récupérés depuis CMS API');
        return data;
      }

      // Si pas de données, utiliser le fallback
      throw new Error('Aucune donnée disponible');

    } catch (error) {
      console.log('🔄 Utilisation des données de fallback pour les paramètres du site');

      // Retourner des données de fallback complètes
      return {
        id: 1,
        site_title: "JOSNET NETWORK",
        site_description: "Solutions IT et télécommunications au Burundi - Votre partenaire technologique de confiance",
        contact_email: "<EMAIL>",
        contact_phone: "+257 22 123 456",
        contact_address: "Avenue de l'Indépendance, Quartier Rohero, Bujumbura, Burundi",
        facebook_url: "https://facebook.com/josnetnetwork",
        twitter_url: "https://twitter.com/josnetnetwork",
        instagram_url: "https://instagram.com/josnetnetwork",
        linkedin_url: "https://linkedin.com/company/josnetnetwork",
        logo: null,
        favicon: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }
  },

  updateSiteSettings: async (id: number, data: any) => {
    try {
      console.log('🔄 Tentative de mise à jour des paramètres du site:', { id, data });

      // Essayer de mettre à jour via l'API
      const response = await api.put(`/cms/settings/${id}/`, data);

      console.log('✅ Paramètres mis à jour via API:', response);
      return response;
    } catch (error) {
      console.warn('❌ API CMS update settings non disponible, simulation de la mise à jour:', error);

      // Simuler une mise à jour réussie pour le développement
      const updatedSettings = {
        id,
        ...data,
        updated_at: new Date().toISOString()
      };

      console.log('✅ Paramètres mis à jour (simulation):', updatedSettings);

      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1000));

      return updatedSettings;
    }
  },

  createSiteSettings: async (data: any) => {
    try {
      console.log('🔄 Tentative de création des paramètres du site:', data);

      const response = await api.post('/cms/settings/', data);

      console.log('✅ Paramètres créés via API:', response);
      return response;
    } catch (error) {
      console.warn('❌ API CMS create settings non disponible, simulation de la création:', error);

      // Simuler une création réussie
      const newSettings = {
        id: 1,
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('✅ Paramètres créés (simulation):', newSettings);

      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1000));

      return newSettings;
    }
  },


};

export default cmsApi;
