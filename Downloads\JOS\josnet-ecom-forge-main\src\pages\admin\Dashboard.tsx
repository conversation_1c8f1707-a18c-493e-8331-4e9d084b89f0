
import { Card } from "@/components/ui/card";
import AdminLayout from "@/components/admin/AdminLayout";
import { 
  BarChart, 
  Package, 
  ShoppingCart, 
  Users, 
  TrendingUp, 
  Clock, 
  AlertTriangle 
} from "lucide-react";

const Dashboard = () => {
  // Fake data for the dashboard
  const stats = [
    {
      title: "Ventes totales",
      value: "24,950 €",
      change: "+12%",
      changeType: "positive",
      icon: <TrendingUp size={24} className="text-green-500" />,
    },
    {
      title: "Commandes",
      value: "432",
      change: "+8%",
      changeType: "positive",
      icon: <ShoppingCart size={24} className="text-blue-500" />,
    },
    {
      title: "Clients",
      value: "1,805",
      change: "+16%",
      changeType: "positive",
      icon: <Users size={24} className="text-violet-500" />,
    },
    {
      title: "Produits",
      value: "142",
      change: "+3",
      changeType: "positive",
      icon: <Package size={24} className="text-orange-500" />,
    },
  ];

  const recentOrders = [
    { id: "#ORD-12345", customer: "<PERSON>", date: "15/05/2025", status: "Livré", amount: "349,99 €" },
    { id: "#ORD-12344", customer: "Sophie <PERSON>", date: "14/05/2025", status: "En cours", amount: "129,95 €" },
    { id: "#ORD-12343", customer: "Pierre Durand", date: "14/05/2025", status: "En attente", amount: "89,50 €" },
    { id: "#ORD-12342", customer: "Marie Lambert", date: "13/05/2025", status: "Livré", amount: "199,00 €" },
    { id: "#ORD-12341", customer: "Thomas Bernard", date: "13/05/2025", status: "Annulé", amount: "459,90 €" },
  ];

  const lowStockItems = [
    { id: "PRD-001", name: "Routeur Wi-Fi 6", stock: 3, threshold: 10 },
    { id: "PRD-015", name: "Câble HDMI 2.1", stock: 5, threshold: 15 },
    { id: "PRD-023", name: "Disque SSD 1TB", stock: 2, threshold: 8 },
    { id: "PRD-042", name: "Adaptateur USB-C", stock: 4, threshold: 12 },
  ];

  const pendingTasks = [
    { id: 1, task: "Mise à jour des descriptions produits", priority: "Haute" },
    { id: 2, task: "Validation des retours clients", priority: "Moyenne" },
    { id: 3, task: "Répondre aux avis clients récents", priority: "Basse" },
    { id: 4, task: "Préparer la promotion de mai", priority: "Haute" },
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Tableau de bord</h1>
          <p className="text-gray-500">Bienvenue sur votre tableau de bord administrateur JOSNET</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                  <h3 className="text-2xl font-bold mt-1">{stat.value}</h3>
                  <p className={`text-sm mt-1 ${
                    stat.changeType === "positive" ? "text-green-600" : "text-red-600"
                  }`}>
                    {stat.change} depuis le mois dernier
                  </p>
                </div>
                <div className="bg-gray-100 p-3 rounded-full">
                  {stat.icon}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Orders */}
          <Card className="col-span-1 lg:col-span-2">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-medium flex items-center">
                  <ShoppingCart size={18} className="mr-2 text-gray-500" /> 
                  Commandes récentes
                </h2>
                <a href="/admin/orders" className="text-sm text-primary hover:underline">
                  Voir toutes les commandes
                </a>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {recentOrders.map((order, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm font-medium text-primary">
                          {order.id}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-700">
                          {order.customer}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {order.date}
                        </td>
                        <td className="px-4 py-3">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            order.status === "Livré"
                              ? "bg-green-100 text-green-800"
                              : order.status === "En cours"
                              ? "bg-blue-100 text-blue-800"
                              : order.status === "En attente"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}>
                            {order.status}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-700">
                          {order.amount}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>

          {/* Quick Actions Column */}
          <div className="col-span-1 space-y-6">
            {/* Low Stock Items */}
            <Card>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-medium flex items-center">
                    <AlertTriangle size={18} className="mr-2 text-amber-500" /> 
                    Stock faible
                  </h2>
                </div>
                <ul className="space-y-3">
                  {lowStockItems.map((item, index) => (
                    <li key={index} className="flex items-center justify-between py-2 border-b last:border-0">
                      <div>
                        <p className="text-sm font-medium">{item.name}</p>
                        <p className="text-xs text-gray-500">ID: {item.id}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold text-red-600">{item.stock} restants</p>
                        <p className="text-xs text-gray-500">Seuil: {item.threshold}</p>
                      </div>
                    </li>
                  ))}
                </ul>
                <div className="mt-4">
                  <a href="/admin/inventory" className="text-sm text-primary hover:underline">
                    Gérer l'inventaire
                  </a>
                </div>
              </div>
            </Card>

            {/* Pending Tasks */}
            <Card>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-medium flex items-center">
                    <Clock size={18} className="mr-2 text-blue-500" /> 
                    Tâches en attente
                  </h2>
                </div>
                <ul className="space-y-2">
                  {pendingTasks.map((task, index) => (
                    <li key={index} className="flex items-center justify-between py-2 border-b last:border-0">
                      <div className="flex items-start">
                        <input 
                          type="checkbox" 
                          className="mt-1 mr-3" 
                          id={`task-${task.id}`}
                        />
                        <label htmlFor={`task-${task.id}`} className="text-sm">{task.task}</label>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        task.priority === "Haute" 
                          ? "bg-red-100 text-red-800" 
                          : task.priority === "Moyenne"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-green-100 text-green-800"
                      }`}>
                        {task.priority}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </Card>

            {/* Sales Graph */}
            <Card>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-medium flex items-center">
                    <BarChart size={18} className="mr-2 text-purple-500" /> 
                    Ventes mensuelles
                  </h2>
                </div>
                <div className="h-52 flex items-end justify-between px-2">
                  {/* Simple bar chart representation */}
                  {[60, 45, 75, 50, 80, 65].map((height, i) => (
                    <div key={i} className="w-8 group relative">
                      <div 
                        className="bg-primary/80 hover:bg-primary rounded-t transition-all" 
                        style={{height: `${height}%`}}
                      />
                      <span className="text-xs mt-1 block text-center text-gray-600">
                        {["Jan", "Fév", "Mar", "Avr", "Mai", "Juin"][i]}
                      </span>
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                        {[12500, 9800, 16200, 10900, 17500, 14200][i]} €
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <a href="/admin/statistics" className="text-sm text-primary hover:underline">
                    Voir les statistiques détaillées
                  </a>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Dashboard;
