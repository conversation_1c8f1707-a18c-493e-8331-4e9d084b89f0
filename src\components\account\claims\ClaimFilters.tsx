import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, X } from "lucide-react";

export interface ClaimFiltersState {
  orderId: string;
  status: string;
}

interface ClaimFiltersProps {
  filters?: ClaimFiltersState;
  onFilterChange: (filters: ClaimFiltersState) => void;
  onReset: () => void;
}

export const ClaimFilters = ({ onFilterChange, onReset }: ClaimFiltersProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState<ClaimFiltersState>({
    orderId: '',
    status: 'all',
  });

  // Appliquer les filtres lorsqu'ils changent
  useEffect(() => {
    onFilterChange(localFilters);
  }, [localFilters, onFilterChange]);

  // Gérer les changements de valeur dans les champs de filtre
  const handleInputChange = (field: keyof ClaimFiltersState, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSelectChange = (field: keyof ClaimFiltersState, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Réinitialiser les filtres
  const handleReset = () => {
    setLocalFilters({
      orderId: '',
      status: 'all',
    });
    onReset();
  };

  // Vérifier si des filtres sont actifs
  const hasActiveFilters = localFilters.orderId || (localFilters.status !== 'all');

  return (
    <div className="mb-6 border rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-500" />
          <h3 className="font-medium">Filtres</h3>
        </div>
        <div className="flex gap-2">
          {hasActiveFilters && (
            <Button variant="outline" size="sm" onClick={handleReset} className="flex items-center gap-1">
              <X className="h-3.5 w-3.5" />
              Réinitialiser
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? "Masquer" : "Afficher"}
          </Button>
        </div>
      </div>

      {hasActiveFilters && !isExpanded && (
        <div className="flex flex-wrap gap-2 mb-2">
          {localFilters.orderId && (
            <div className="bg-gray-100 text-sm px-2 py-1 rounded-full flex items-center gap-1">
              N° Commande: {localFilters.orderId}
              <button onClick={() => handleInputChange('orderId', '')} className="text-gray-500 hover:text-gray-700">
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
          {localFilters.status && (
            <div className="bg-gray-100 text-sm px-2 py-1 rounded-full flex items-center gap-1">
              Statut: {localFilters.status === "resolved" ? "Résolu" : 
                      localFilters.status === "in_progress" ? "En cours" : 
                      localFilters.status === "new" ? "Nouveau" : 
                      localFilters.status === "cancelled" ? "Annulé" : 
                      localFilters.status}
              <button onClick={() => handleSelectChange('status', 'all')} className="text-gray-500 hover:text-gray-700">
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
        </div>
      )}

      {isExpanded && (
        <div className="grid gap-4 mt-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="orderId">N° Commande</Label>
              <Input
                id="orderId"
                placeholder="Entrez le numéro de commande"
                value={localFilters.orderId}
                onChange={(e) => handleInputChange('orderId', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status">Statut</Label>
              <Select
                value={localFilters.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="resolved">Résolu</SelectItem>
                  <SelectItem value="in_progress">En cours</SelectItem>
                  <SelectItem value="new">Nouveau</SelectItem>
                  <SelectItem value="cancelled">Annulé</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
