from rest_framework import serializers
from .models import Promotion, Campaign
from products.serializers import ProductListSerializer

class PromotionSerializer(serializers.ModelSerializer):
    """Serializer for the Promotion model."""
    
    target_name = serializers.ReadOnlyField()
    
    class Meta:
        model = Promotion
        fields = [
            'id', 'name', 'description', 'discount_type', 'discount_value', 
            'discount_code', 'applies_to', 'target_id', 'target_name',
            'min_purchase_amount', 'start_date', 'end_date', 'is_active', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class CampaignSerializer(serializers.ModelSerializer):
    """Serializer for the Campaign model."""
    
    product_count = serializers.ReadOnlyField()
    products_details = ProductListSerializer(source='products', many=True, read_only=True)
    
    class Meta:
        model = Campaign
        fields = [
            'id', 'name', 'description', 'start_date', 'end_date', 
            'discount_percentage', 'discount_amount', 'discount_code',
            'products', 'products_details', 'product_count', 'is_active', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
