import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessageSquare, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import ReviewStats from '@/components/reviews/ReviewStats';
import ReviewList from '@/components/reviews/ReviewList';
import ReviewForm from '@/components/reviews/ReviewForm';
import { useAuth } from '@/contexts/AuthContext';

interface ProductReviewsProps {
  productId: number;
  productName: string;
}

const ProductReviews: React.FC<ProductReviewsProps> = ({
  productId,
  productName
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);

  const handleCreateReview = () => {
    if (!user) {
      navigate('/login');
      return;
    }
    setShowCreateForm(true);
  };

  const handleFormSuccess = () => {
    setShowCreateForm(false);
  };

  const handleViewAllReviews = () => {
    navigate(`/product/${productId}/reviews`);
  };

  return (
    <div className="space-y-6">
      {/* En-tête avec actions */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold flex items-center">
          <MessageSquare className="h-5 w-5 mr-2" />
          Avis clients
        </h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleViewAllReviews}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Voir tous les avis
          </Button>
          <Button onClick={handleCreateReview}>
            Écrire un avis
          </Button>
        </div>
      </div>

      {/* Statistiques des avis */}
      <ReviewStats productId={productId} />

      {/* Aperçu des avis récents */}
      <div>
        <h4 className="font-medium mb-4">Avis récents</h4>
        <ReviewList
          productId={productId}
          showCreateButton={false}
        />
      </div>

      {/* Dialog pour créer un avis */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Écrire un avis</DialogTitle>
            <DialogDescription>
              Partagez votre expérience avec ce produit pour aider d'autres clients.
            </DialogDescription>
          </DialogHeader>
          <ReviewForm
            productId={productId}
            productName={productName}
            onSuccess={handleFormSuccess}
            onCancel={() => setShowCreateForm(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductReviews;
