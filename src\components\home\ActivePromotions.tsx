import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Tag, ArrowRight, Clock } from "lucide-react";
import promotionApi, { Promotion } from "@/services/promotionApi";
import { format, parseISO, isAfter } from "date-fns";
import { fr } from "date-fns/locale";
import { formatCurrency } from "@/utils/formatters";

const ActivePromotions = () => {
  // Récupérer les promotions actives sans authentification
  const {
    data,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['homePromotions'],
    queryFn: () => promotionApi.getActivePromotions({
      page_size: 5
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Filtrer les promotions qui sont encore valides (date de fin > aujourd'hui)
  const activePromotions = data?.results.filter(promo => 
    !promo.end_date || isAfter(parseISO(promo.end_date), new Date())
  ) || [];

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col space-y-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-24 w-full rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-red-500">Impossible de charger les promotions</p>
        <Button onClick={() => refetch()} className="mt-2">
          Réessayer
        </Button>
      </div>
    );
  }

  if (activePromotions.length === 0) {
    return null; // Ne rien afficher s'il n'y a pas de promotions actives
  }

  return (
    <section className="py-10 bg-gradient-to-r from-blue-50 to-indigo-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold flex items-center">
              <Tag className="mr-2 h-6 w-6 text-primary" />
              Promotions en cours
            </h2>
            <p className="text-gray-600">Profitez de nos offres spéciales pour un temps limité</p>
          </div>
          <Link to="/products">
            <Button variant="outline" className="flex items-center gap-1">
              Voir tous les produits
              <ArrowRight size={16} />
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {activePromotions.map((promotion) => (
            <Link 
              to={`/promotions/${promotion.id}`}
              key={promotion.id} 
              className="bg-white rounded-lg shadow-md overflow-hidden border border-primary/20 hover:shadow-lg transition-all duration-300 block"
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-xl font-semibold text-gray-800">{promotion.name}</h3>
                  <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
                    {promotion.discount_type === 'percentage' 
                      ? `${promotion.discount_value}% de réduction` 
                      : `${formatCurrency(promotion.discount_value)} de réduction`}
                  </div>
                </div>
                
                <p className="text-gray-600 mb-4 line-clamp-2">{promotion.description}</p>
                
                {promotion.discount_code && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-1">Code promo :</p>
                    <div className="bg-gray-100 p-2 rounded font-mono text-center font-medium">
                      {promotion.discount_code}
                    </div>
                  </div>
                )}
                
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1" />
                    {promotion.end_date 
                      ? `Expire le ${format(parseISO(promotion.end_date), 'dd MMMM yyyy', { locale: fr })}` 
                      : "Sans date d'expiration"}
                  </div>
                  
                  <Button size="sm" onClick={(e) => {
                    e.preventDefault();
                    window.location.href = `/promotions/${promotion.id}`;
                  }}>
                    Voir détails
                  </Button>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ActivePromotions;
