
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import AdminLayout from "@/components/admin/AdminLayout";
import { Card } from "@/components/ui/card";
import {
  BarChart as BarChartIcon,
  TrendingUp,
  ShoppingCart,
  Users,
  ChevronDown,
  Loader2,
  RefreshCw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";
import statisticsApi, { TimeRange } from "@/services/statisticsApi";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const Statistics = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>("6mois");

  // Récupérer les données statistiques via React Query
  const {
    data: statisticsData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['statistics', timeRange],
    queryFn: () => statisticsApi.getStatistics(timeRange),
  });

  // Formater le montant des ventes en Francs Burundais
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-BI', {
      style: 'currency',
      currency: 'BIF',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Statistiques globales
  const stats = statisticsData ? [
    {
      title: "Ventes totales",
      value: formatCurrency(statisticsData.sales_statistics.total_sales),
      change: statisticsData.sales_statistics.change_sales,
      changeType: statisticsData.sales_statistics.change_type_sales,
      icon: <TrendingUp size={24} className="text-green-500" />,
    },
    {
      title: "Commandes",
      value: statisticsData.sales_statistics.total_orders.toString(),
      change: statisticsData.sales_statistics.change_orders,
      changeType: statisticsData.sales_statistics.change_type_orders,
      icon: <ShoppingCart size={24} className="text-blue-500" />,
    },
    {
      title: "Clients",
      value: statisticsData.sales_statistics.total_customers.toString(),
      change: statisticsData.sales_statistics.change_customers,
      changeType: statisticsData.sales_statistics.change_type_customers,
      icon: <Users size={24} className="text-violet-500" />,
    },
  ] : [];

  const handleTimeRangeChange = (range: TimeRange) => {
    setTimeRange(range);
    // Les données seront automatiquement rechargées grâce à React Query
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Statistiques de Vente</h1>
          <p className="text-gray-500">Analysez les performances de votre boutique</p>
          {statisticsData?.is_mock_data && (
            <div className="mt-2 p-2 bg-amber-50 border border-amber-200 text-amber-700 rounded-md text-sm">
              ⚠️ Affichage de données simulées - Le backend n'est pas disponible ou n'est pas configuré pour cet endpoint
            </div>
          )}
        </div>

        {/* Filtres de période */}
        <div className="flex items-center justify-end gap-2">
          <Button
            variant={timeRange === "30jours" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeRangeChange("30jours")}
          >
            30 jours
          </Button>
          <Button
            variant={timeRange === "3mois" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeRangeChange("3mois")}
          >
            3 mois
          </Button>
          <Button
            variant={timeRange === "6mois" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeRangeChange("6mois")}
          >
            6 mois
          </Button>
          <Button
            variant={timeRange === "1an" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeRangeChange("1an")}
          >
            1 an
          </Button>
        </div>

        {isLoading ? (
          // Affichage des squelettes pendant le chargement
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="w-full">
                      <Skeleton className="h-4 w-24 mb-2" />
                      <Skeleton className="h-8 w-32 mb-2" />
                      <Skeleton className="h-4 w-40" />
                    </div>
                    <Skeleton className="h-12 w-12 rounded-full" />
                  </div>
                </Card>
              ))}
            </div>
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-8 w-24" />
              </div>
              <Skeleton className="h-80 w-full" />
            </Card>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <Skeleton className="h-6 w-40 mb-6" />
                <Skeleton className="h-64 w-full" />
              </Card>
              <Card className="p-6">
                <Skeleton className="h-6 w-40 mb-6" />
                <Skeleton className="h-64 w-full" />
              </Card>
            </div>
          </>
        ) : isError ? (
          // Affichage d'une erreur
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            Une erreur est survenue lors du chargement des statistiques.
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => refetch()}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Réessayer
            </Button>
          </div>
        ) : (
          // Affichage des données
          <>
            {/* Statistiques générales */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {stats.map((stat, index) => (
                <Card key={index} className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                      <h3 className="text-2xl font-bold mt-1">{stat.value}</h3>
                      <p className={`text-sm mt-1 ${
                        stat.changeType === "positive" ? "text-green-600" : "text-red-600"
                      }`}>
                        {stat.change} depuis la période précédente
                      </p>
                    </div>
                    <div className="bg-gray-100 p-3 rounded-full">
                      {stat.icon}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Graphique des ventes */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-medium flex items-center">
                  <BarChartIcon size={18} className="mr-2 text-gray-500" />
                  Évolution des ventes
                </h2>
                <Button variant="outline" className="flex items-center gap-1 text-sm">
                  Mensuel
                  <ChevronDown size={14} />
                </Button>
              </div>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={statisticsData.sales_data}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="revenue" name="Revenus (BIF)" fill="#8884d8" radius={[4, 4, 0, 0]} />
                    <Bar yAxisId="right" dataKey="orders" name="Commandes" fill="#82ca9d" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Card>

            {/* Graphiques secondaires */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Répartition des ventes par catégorie */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium">Ventes par catégorie</h2>
                </div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={statisticsData.category_data}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {statisticsData.category_data.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value, name) => [`${value} produits`, name]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </Card>

              {/* Trafic du site */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium">Trafic du site</h2>
                </div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={statisticsData.traffic_data}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="visits" stroke="#8884d8" activeDot={{ r: 8 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default Statistics;
