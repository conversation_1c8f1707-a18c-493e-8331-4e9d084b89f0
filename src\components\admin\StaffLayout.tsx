import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import StaffSidebar from './StaffSidebar';
import AdminSidebar from './AdminSidebar';
import { Toaster } from '@/components/ui/toaster';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Bell,
  Search,
  Settings,
  User,
  LogOut,
  Shield,
  ChevronDown,
  Moon,
  Sun,
  HelpCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';

interface StaffLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const StaffLayout: React.FC<StaffLayoutProps> = ({ children, title }) => {
  const { user, logout } = useAuth();

  // Determine which sidebar to show based on user role
  const SidebarComponent = user?.role === 'admin' ? AdminSidebar : StaffSidebar;

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 flex-shrink-0">
        <SidebarComponent />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Title */}
            <div className="flex items-center space-x-4">
              {title && (
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge 
                      className={
                        user?.role === 'admin' 
                          ? "bg-purple-100 text-purple-800" 
                          : "bg-blue-100 text-blue-800"
                      }
                    >
                      <Shield className="h-3 w-3 mr-1" />
                      {user?.role === 'admin' ? 'Administrateur' : 'Staff'}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      Connecté en tant que {user?.role}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Right Side */}
            <div className="flex items-center space-x-4">
              {/* Search */}
              <Button variant="ghost" size="sm">
                <Search className="h-4 w-4" />
              </Button>

              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-4 w-4" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </Button>

              {/* User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2 px-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.profile_picture} />
                      <AvatarFallback>
                        {user?.first_name?.[0]}{user?.last_name?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="hidden md:block text-left">
                      <p className="text-sm font-medium">{user?.full_name}</p>
                      <p className="text-xs text-gray-500">{user?.email}</p>
                    </div>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{user?.full_name}</p>
                      <p className="text-xs text-gray-500">{user?.email}</p>
                      <Badge 
                        className={
                          user?.role === 'admin' 
                            ? "bg-purple-100 text-purple-800 w-fit" 
                            : "bg-blue-100 text-blue-800 w-fit"
                        }
                        variant="secondary"
                      >
                        <Shield className="h-3 w-3 mr-1" />
                        {user?.role === 'admin' ? 'Administrateur' : 'Staff'}
                      </Badge>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  
                  {/* Profile Link */}
                  <DropdownMenuItem asChild>
                    <Link 
                      to={user?.role === 'admin' ? '/admin/profile' : '/admin/staff-profile'}
                      className="flex items-center"
                    >
                      <User className="mr-2 h-4 w-4" />
                      Mon Profil
                    </Link>
                  </DropdownMenuItem>

                  {/* Settings (Admin only) */}
                  {user?.role === 'admin' && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin/settings" className="flex items-center">
                        <Settings className="mr-2 h-4 w-4" />
                        Paramètres
                      </Link>
                    </DropdownMenuItem>
                  )}

                  {/* Help */}
                  <DropdownMenuItem asChild>
                    <Link to="/help" className="flex items-center">
                      <HelpCircle className="mr-2 h-4 w-4" />
                      Aide
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  {/* Theme Toggle */}
                  <DropdownMenuItem>
                    <Sun className="mr-2 h-4 w-4" />
                    Thème clair
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  {/* Logout */}
                  <DropdownMenuItem 
                    onClick={logout}
                    className="text-red-600 focus:text-red-600"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Déconnexion
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto">
          <div className="p-6">
            {children}
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200 px-6 py-3">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span>© 2024 JosNet Platform</span>
              <span>•</span>
              <span>Version 2.1.0</span>
            </div>
            <div className="flex items-center space-x-4">
              <span>
                Connecté en tant que{' '}
                <span className="font-medium text-gray-700">
                  {user?.role === 'admin' ? 'Administrateur' : 'Staff'}
                </span>
              </span>
              <span>•</span>
              <span>Dernière connexion: {new Date().toLocaleTimeString('fr-FR')}</span>
            </div>
          </div>
        </footer>
      </div>

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
};

export default StaffLayout;
