
import { Facebook, Instagram, Linkedin, Mail, MapPin, Phone, Twitter, Loader2, AlertCircle } from "lucide-react";
import { Link } from "react-router-dom";
import useFooterData from "@/hooks/useFooterData";
import Logo from '@/components/ui/Logo';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  // Récupérer les données du footer depuis le CMS admin
  const {
    footerData,
    isLoading,
    isError,
    contactInfo,
    socialLinks,
    companyInfo,
    footerPages
  } = useFooterData();

  // Afficher un loader pendant le chargement
  if (isLoading) {
    return (
      <footer className="bg-gray-900 text-white pt-16 pb-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>Chargement des informations...</span>
          </div>
        </div>
      </footer>
    );
  }

  // Afficher un message d'erreur si nécessaire
  if (isError) {
    return (
      <footer className="bg-gray-900 text-white pt-16 pb-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center py-8 text-amber-400">
            <AlertCircle className="h-6 w-6 mr-2" />
            <span>Utilisation des données de fallback (CMS admin non disponible)</span>
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className="bg-gray-900 text-white pt-12 md:pt-16 pb-6 md:pb-8">
      <div className="container mx-auto px-4 md:px-6 lg:px-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-12">
          {/* Company Info - Section principale */}
          <div className="sm:col-span-2 lg:col-span-1">
            <div className="mb-4">
              <Logo variant="compact" size="md" className="mb-2" />
            </div>
            <p className="text-gray-300 mb-6 text-sm md:text-base leading-relaxed">
              {companyInfo?.description || 'Leader dans le secteur des technologies de l\'information et des communications, offrant des services de haute qualité et restant à la pointe de l\'innovation.'}
            </p>
            <div className="flex space-x-3 md:space-x-4">
              {socialLinks?.facebook && socialLinks.facebook !== '#' && (
                <a
                  href={socialLinks.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                  title="Suivez-nous sur Facebook"
                  aria-label="Facebook"
                >
                  <Facebook size={18} />
                </a>
              )}
              {socialLinks?.twitter && socialLinks.twitter !== '#' && (
                <a
                  href={socialLinks.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-400 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                  title="Suivez-nous sur Twitter"
                  aria-label="Twitter"
                >
                  <Twitter size={18} />
                </a>
              )}
              {socialLinks?.instagram && socialLinks.instagram !== '#' && (
                <a
                  href={socialLinks.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 hover:bg-pink-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                  title="Suivez-nous sur Instagram"
                  aria-label="Instagram"
                >
                  <Instagram size={18} />
                </a>
              )}
              {socialLinks?.linkedin && socialLinks.linkedin !== '#' && (
                <a
                  href={socialLinks.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                  title="Suivez-nous sur LinkedIn"
                  aria-label="LinkedIn"
                >
                  <Linkedin size={18} />
                </a>
              )}
            </div>
            {/* Indicateur discret de mise à jour */}
            <div className="mt-4 text-xs text-gray-500">
              <span title="Informations mises à jour automatiquement">🔄 Mis à jour</span>
            </div>
          </div>

          {/* Quick Links - Navigation principale */}
          <div>
            <h3 className="text-lg md:text-xl font-bold mb-4 md:mb-6 text-white">Navigation</h3>
            <ul className="space-y-2 md:space-y-3">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  Accueil
                </Link>
              </li>
              <li>
                <Link to="/products" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  Nos Produits
                </Link>
              </li>
              <li>
                <Link to="/blog" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  Blog
                </Link>
              </li>
              {/* Pages dynamiques du CMS */}
              {footerPages && footerPages.length > 0 && footerPages.map(page => (
                <li key={page.id}>
                  <Link
                    to={`/${page.slug}`}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {page.title}
                  </Link>
                </li>
              ))}
              {/* Pages par défaut si pas de pages CMS */}
              {(!footerPages || footerPages.length === 0) && (
                <>
                  <li>
                    <Link to="/about" className="text-gray-300 hover:text-white transition-colors">À Propos</Link>
                  </li>
                  <li>
                    <Link to="/contact" className="text-gray-300 hover:text-white transition-colors">Contact</Link>
                  </li>
                </>
              )}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg md:text-xl font-bold mb-4 md:mb-6 text-white">Support</h3>
            <ul className="space-y-2 md:space-y-3">
              <li>
                <a href="/faq" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  FAQ
                </a>
              </li>
              <li>
                <a href="/shipping" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  Livraison
                </a>
              </li>
              <li>
                <a href="/returns" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  Retours
                </a>
              </li>
              <li>
                <a href="/terms" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  Conditions
                </a>
              </li>
              <li>
                <a href="/privacy" className="text-gray-300 hover:text-white transition-colors text-sm md:text-base flex items-center group">
                  <span className="w-1 h-1 bg-accent rounded-full mr-2 group-hover:w-2 transition-all"></span>
                  Confidentialité
                </a>
              </li>
            </ul>
          </div>

          {/* Contact - Informations de contact */}
          <div>
            <h3 className="text-lg md:text-xl font-bold mb-4 md:mb-6 text-white">Contactez-nous</h3>
            <ul className="space-y-3 md:space-y-4">
              <li className="flex items-start group">
                <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-accent transition-colors">
                  <MapPin className="h-4 w-4 text-accent group-hover:text-white" />
                </div>
                <div>
                  <span className="text-gray-300 text-sm md:text-base leading-relaxed">
                    {contactInfo?.address || '123 Avenue Principale, Bujumbura, Burundi'}
                  </span>
                </div>
              </li>
              <li className="flex items-start group">
                <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-accent transition-colors">
                  <Phone className="h-4 w-4 text-accent group-hover:text-white" />
                </div>
                <div>
                  <a
                    href={`tel:${contactInfo?.phone || '+257 12 345 678'}`}
                    className="text-gray-300 hover:text-white transition-colors text-sm md:text-base"
                  >
                    {contactInfo?.phone || '+257 12 345 678'}
                  </a>
                </div>
              </li>
              <li className="flex items-start group">
                <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-accent transition-colors">
                  <Mail className="h-4 w-4 text-accent group-hover:text-white" />
                </div>
                <div>
                  <a
                    href={`mailto:${contactInfo?.email || '<EMAIL>'}`}
                    className="text-gray-300 hover:text-white transition-colors text-sm md:text-base break-all"
                  >
                    {contactInfo?.email || '<EMAIL>'}
                  </a>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-center md:text-left text-gray-400 text-sm">
              &copy; {currentYear} {companyInfo?.name || 'JOSNET NETWORK'}. Tous droits réservés.
            </p>
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4 text-xs text-gray-500">
              <span className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2" title="Site à jour"></span>
                Informations à jour
              </span>
              <span className="hidden md:inline">•</span>
              <span>Conçu avec ❤️ au Burundi</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
