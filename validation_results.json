{"analysis": {"total_files": 205, "files_with_old_buttons": 36, "files_with_new_buttons": 10, "files_with_refresh_hooks": 2, "files_with_imports": 8, "problematic_files": [{"file": "src\\components\\admin\\products\\DynamicFilters.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\components\\admin\\products\\FacetedSearch.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\components\\admin\\products\\ImportExportManager.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\components\\admin\\products\\InventoryManager.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\components\\admin\\products\\ProductCampaignManager.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\components\\admin\\products\\ProductVariantManager.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\components\\admin\\products\\PromotionManager.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\components\\admin\\products\\RelatedProductsManager.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\pages\\admin\\Dashboard.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\pages\\admin\\Messages.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\pages\\admin\\MessageSettings.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\pages\\admin\\ResponseTemplates.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\pages\\admin\\Statistics.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}, {"file": "src\\pages\\admin\\cms\\MediaManager.tsx", "reason": "Utilise encore les anciens boutons d'actualisation"}], "good_files": [{"file": "src\\pages\\admin\\AdminProducts.tsx", "new_buttons": 3, "has_imports": true}, {"file": "src\\pages\\admin\\Categories.tsx", "new_buttons": 3, "has_imports": true}, {"file": "src\\pages\\admin\\CMS.tsx", "new_buttons": 2, "has_imports": true}, {"file": "src\\pages\\admin\\Inventory.tsx", "new_buttons": 3, "has_imports": true}, {"file": "src\\pages\\admin\\Logs.tsx", "new_buttons": 3, "has_imports": true}, {"file": "src\\pages\\admin\\Promotions.tsx", "new_buttons": 3, "has_imports": true}, {"file": "src\\pages\\admin\\Users.tsx", "new_buttons": 3, "has_imports": true}, {"file": "src\\pages\\admin\\products\\InventoryManagement.tsx", "new_buttons": 3, "has_imports": true}], "summary": {}}, "detailed_results": {"src\\App.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\config.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\main.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\vite-env.d.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\mui-compatibility.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\AccountLayout.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\AccountSidebar.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\AddressManager.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\LoyaltyDashboard.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\claims\\ClaimDetails.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\claims\\ClaimMessages.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\components\\account\\claims\\CreateClaimForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\returns\\CreateReturnForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\account\\returns\\ReturnFilters.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\AdminLayout.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\categories\\CategoryForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\categories\\DeleteCategoryDialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\AdvancedSearch.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\CustomerProductInsights.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\DynamicFilters.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\FacetedSearch.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ImportExportManager.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\InventoryAdjustmentDialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\InventoryBulkActionsDialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\InventoryFilterDialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\InventoryManager.tsx": {"old_refresh_button": 3, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\InventoryStatsCard.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductCampaignManager.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductDashboard.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductFilters.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductImageManager.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductPreview.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductSalesStats.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductsTable.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\ProductVariantManager.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\PromotionManager.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\RelatedProductsManager.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\RichTextEditor.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\SavedFiltersManager.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\products\\VirtualizedProductList.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\promotions\\PromotionForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\admin\\users\\UserForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\auth\\ProtectedRoute.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\common\\PaginationControls.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\common\\ProductImage.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\editor\\Editor.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\home\\AboutSection.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\home\\FeaturedProducts.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\home\\Hero.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\home\\Testimonials.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\layout\\Footer.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\layout\\Navbar.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\messaging\\ConversationNotifier.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\orders\\OrderDashboard.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\orders\\OrderDetail.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\orders\\OrderForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\orders\\OrderList.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\payments\\InvoiceDetail.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\payments\\InvoiceList.tsx": {"old_refresh_button": 3, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\components\\payments\\PaymentMethodForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\payments\\PaymentMethodList.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\payments\\TransactionDetail.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\payments\\TransactionList.tsx": {"old_refresh_button": 4, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\components\\products\\ProductCard.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\accordion.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\alert-dialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\alert.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\aspect-ratio.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\avatar.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\badge.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\breadcrumb.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\button.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\calendar.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\card.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\carousel.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\CartIcon.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\chart.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\checkbox.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\collapsible.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\command.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\context-menu.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\dialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\drawer.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\dropdown-menu.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\form.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\hover-card.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\input-otp.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\input.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\label.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\menubar.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\navigation-menu.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\optimized-image.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\pagination.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\popover.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\progress.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\radio-group.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\refresh-button.tsx": {"old_refresh_button": 2, "new_refresh_button": 35, "useRefresh_hook": 3, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\resizable.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\scroll-area.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\select.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\separator.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\sheet.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\sidebar.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\skeleton.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\slider.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\sonner.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\switch.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\table.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\tabs.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\textarea.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\toast.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\toaster.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\toggle-group.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\toggle.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\tooltip.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\components\\ui\\use-toast.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\config\\api.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\contexts\\AuthContext.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\contexts\\CartContext.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\hooks\\use-mobile.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\hooks\\use-toast.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\hooks\\usePaginatedProducts.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\hooks\\useRefresh.ts": {"old_refresh_button": 1, "new_refresh_button": 1, "useRefresh_hook": 15, "react_query_refetch": 1, "refresh_import": 0}, "src\\lib\\auth.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\lib\\queryClient.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\lib\\utils.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Blog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\BlogPost.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Cart.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Checkout.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Contact.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\FAQ.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Index.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Login.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\NotFound.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\OrderConfirmation.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\OrderTracking.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Privacy.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\ProductDetail.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Products.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Register.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Reviews.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\Terms.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\about\\About.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\account\\ClaimDetail.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\account\\Dashboard.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\pages\\account\\InvoiceDetail.tsx": {"old_refresh_button": 1, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\pages\\account\\Invoices.tsx": {"old_refresh_button": 3, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\pages\\account\\Messages.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\account\\OrderHistory.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\account\\Profile.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\account\\Returns.tsx": {"old_refresh_button": 6, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\account\\Suggestions.tsx": {"old_refresh_button": 3, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\pages\\admin\\AdminProducts.tsx": {"old_refresh_button": 1, "new_refresh_button": 3, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 1}, "src\\pages\\admin\\Categories.tsx": {"old_refresh_button": 2, "new_refresh_button": 3, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 1}, "src\\pages\\admin\\CMS.tsx": {"old_refresh_button": 0, "new_refresh_button": 2, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 1}, "src\\pages\\admin\\Dashboard.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\pages\\admin\\Inventory.tsx": {"old_refresh_button": 3, "new_refresh_button": 3, "useRefresh_hook": 0, "react_query_refetch": 4, "refresh_import": 1}, "src\\pages\\admin\\InvoiceDetail.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\Invoices.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\Logs.tsx": {"old_refresh_button": 0, "new_refresh_button": 3, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 1}, "src\\pages\\admin\\Messages.tsx": {"old_refresh_button": 4, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\MessageSettings.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\OrderCreate.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\OrderDashboard.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\OrderDetail.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\OrderEdit.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\Orders.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\PaymentMethodForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\PaymentMethods.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\ProductForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\Products.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\Promotions.tsx": {"old_refresh_button": 1, "new_refresh_button": 3, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 1}, "src\\pages\\admin\\ResponseTemplates.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\Statistics.tsx": {"old_refresh_button": 2, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 1, "refresh_import": 0}, "src\\pages\\admin\\TransactionDetail.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\Transactions.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\UserDetail.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 3, "refresh_import": 0}, "src\\pages\\admin\\Users.tsx": {"old_refresh_button": 2, "new_refresh_button": 3, "useRefresh_hook": 0, "react_query_refetch": 3, "refresh_import": 1}, "src\\pages\\admin\\cms\\MediaManager.tsx": {"old_refresh_button": 3, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 2, "refresh_import": 0}, "src\\pages\\admin\\cms\\PageForm.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\cms\\PreviewDialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\cms\\SchedulePublishDialog.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\admin\\products\\InventoryManagement.tsx": {"old_refresh_button": 0, "new_refresh_button": 3, "useRefresh_hook": 0, "react_query_refetch": 2, "refresh_import": 1}, "src\\pages\\returns\\Returns.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\pages\\shipping\\Shipping.tsx": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\accountApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\adminUserApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\api.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\categoryApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\cmsApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\dashboardApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\discountService.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\logsApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\messagingApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\orderApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\paymentApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\productApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\promotionApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\statisticsApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\services\\userApi.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\utils\\apiUtils.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\utils\\auth.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\utils\\data.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}, "src\\utils\\formatters.ts": {"old_refresh_button": 0, "new_refresh_button": 0, "useRefresh_hook": 0, "react_query_refetch": 0, "refresh_import": 0}}}