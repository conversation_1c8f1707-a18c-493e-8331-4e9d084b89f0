import { getAuthToken } from '@/utils/auth';
import axios from 'axios';
import { API_URL } from '../config';

// Types
export interface SalesStatistics {
  total_sales: number;
  total_orders: number;
  total_customers: number;
  change_sales: string;
  change_orders: string;
  change_customers: string;
  change_type_sales: 'positive' | 'negative' | 'neutral';
  change_type_orders: 'positive' | 'negative' | 'neutral';
  change_type_customers: 'positive' | 'negative' | 'neutral';
}

export interface SalesData {
  month: string;
  revenue: number;
  orders: number;
}

export interface CategoryData {
  name: string;
  value: number;
}

export interface TrafficData {
  day: string;
  visits: number;
}

export interface StatisticsData {
  sales_statistics: SalesStatistics;
  sales_data: SalesData[];
  category_data: CategoryData[];
  traffic_data: TrafficData[];
  is_mock_data?: boolean;
}

export type TimeRange = '30jours' | '3mois' | '6mois' | '1an';

// Statistics API service
const statisticsApi = {
  // Get statistics data
  getStatistics: async (timeRange: TimeRange = '6mois'): Promise<StatisticsData> => {
    try {
      const url = `${API_URL}/analytics/statistics/`;
      console.log(`Fetching statistics from ${url} with time_range=${timeRange}`);
      console.log('Auth token present:', !!getAuthToken());

      const response = await axios.get(url, {
        params: { time_range: timeRange },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      console.log('Successfully fetched statistics from API:', response.status);
      return response.data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      console.error('Error details:', error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      } : 'No response details');

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('⚠️ USING MOCK DATA AS FALLBACK - Backend API not available or not configured');
      console.warn('This is expected during development if the backend is not running');

      // Données simulées pour le développement frontend
      const mockData = statisticsApi.getMockStatistics(timeRange);

      // Ajouter un indicateur pour montrer que ce sont des données simulées
      mockData.is_mock_data = true;

      return mockData;
    }
  },

  // Helper function to generate mock statistics data
  getMockStatistics: (timeRange: TimeRange): StatisticsData => {
    // Générer des données de ventes en fonction de la période
    let salesData: SalesData[] = [];
    let trafficData: TrafficData[] = [];

    // Conversion en Francs Burundais (1 EUR ≈ 2200 BIF)
    const exchangeRate = 2200;

    // Configurer les données en fonction de la période sélectionnée
    switch (timeRange) {
      case '30jours':
        salesData = [
          { month: "1", revenue: 2500 * exchangeRate, orders: 35 },
          { month: "5", revenue: 3800 * exchangeRate, orders: 42 },
          { month: "10", revenue: 3200 * exchangeRate, orders: 38 },
          { month: "15", revenue: 4900 * exchangeRate, orders: 51 },
          { month: "20", revenue: 3500 * exchangeRate, orders: 45 },
          { month: "25", revenue: 5200 * exchangeRate, orders: 56 },
          { month: "30", revenue: 4200 * exchangeRate, orders: 49 }
        ];
        trafficData = [
          { day: "1", visits: 120 },
          { day: "5", visits: 210 },
          { day: "10", visits: 190 },
          { day: "15", visits: 280 },
          { day: "20", visits: 250 },
          { day: "25", visits: 290 },
          { day: "30", visits: 150 }
        ];
        break;
      case '3mois':
        salesData = [
          { month: "Jan", revenue: 8500 * exchangeRate, orders: 95 },
          { month: "Fév", revenue: 7800 * exchangeRate, orders: 82 },
          { month: "Mar", revenue: 10200 * exchangeRate, orders: 117 }
        ];
        trafficData = [
          { day: "Sem 1", visits: 420 },
          { day: "Sem 2", visits: 510 },
          { day: "Sem 3", visits: 490 },
          { day: "Sem 4", visits: 580 },
          { day: "Sem 5", visits: 650 },
          { day: "Sem 6", visits: 590 },
          { day: "Sem 7", visits: 450 },
          { day: "Sem 8", visits: 520 },
          { day: "Sem 9", visits: 610 },
          { day: "Sem 10", visits: 590 },
          { day: "Sem 11", visits: 680 },
          { day: "Sem 12", visits: 750 }
        ];
        break;
      case '1an':
        salesData = [
          { month: "Jan", revenue: 12500 * exchangeRate, orders: 145 },
          { month: "Fév", revenue: 9800 * exchangeRate, orders: 132 },
          { month: "Mar", revenue: 16200 * exchangeRate, orders: 187 },
          { month: "Avr", revenue: 10900 * exchangeRate, orders: 151 },
          { month: "Mai", revenue: 17500 * exchangeRate, orders: 205 },
          { month: "Juin", revenue: 14200 * exchangeRate, orders: 176 },
          { month: "Juil", revenue: 18500 * exchangeRate, orders: 215 },
          { month: "Août", revenue: 15800 * exchangeRate, orders: 192 },
          { month: "Sep", revenue: 19200 * exchangeRate, orders: 227 },
          { month: "Oct", revenue: 21900 * exchangeRate, orders: 251 },
          { month: "Nov", revenue: 23500 * exchangeRate, orders: 275 },
          { month: "Déc", revenue: 28200 * exchangeRate, orders: 316 }
        ];
        trafficData = [
          { day: "Jan", visits: 1520 },
          { day: "Fév", visits: 1610 },
          { day: "Mar", visits: 1590 },
          { day: "Avr", visits: 1780 },
          { day: "Mai", visits: 1850 },
          { day: "Juin", visits: 1690 },
          { day: "Juil", visits: 1450 },
          { day: "Août", visits: 1320 },
          { day: "Sep", visits: 1580 },
          { day: "Oct", visits: 1720 },
          { day: "Nov", visits: 1890 },
          { day: "Déc", visits: 2150 }
        ];
        break;
      case '6mois':
      default:
        salesData = [
          { month: "Jan", revenue: 12500 * exchangeRate, orders: 145 },
          { month: "Fév", revenue: 9800 * exchangeRate, orders: 132 },
          { month: "Mar", revenue: 16200 * exchangeRate, orders: 187 },
          { month: "Avr", revenue: 10900 * exchangeRate, orders: 151 },
          { month: "Mai", revenue: 17500 * exchangeRate, orders: 205 },
          { month: "Juin", revenue: 14200 * exchangeRate, orders: 176 }
        ];
        trafficData = [
          { day: "Lun", visits: 520 },
          { day: "Mar", visits: 610 },
          { day: "Mer", visits: 590 },
          { day: "Jeu", visits: 780 },
          { day: "Ven", visits: 850 },
          { day: "Sam", visits: 690 },
          { day: "Dim", visits: 450 }
        ];
        break;
    }

    // Données de catégories (constantes pour toutes les périodes)
    const categoryData: CategoryData[] = [
      { name: "Réseaux", value: 28 },
      { name: "Composants", value: 64 },
      { name: "Périphériques", value: 42 },
      { name: "Smartphones", value: 35 },
      { name: "Logiciels", value: 19 }
    ];

    // Calculer le total des ventes pour la période
    const totalSales = salesData.reduce((sum, item) => sum + item.revenue, 0);
    const totalOrders = salesData.reduce((sum, item) => sum + item.orders, 0);

    // Statistiques globales avec des valeurs légèrement différentes pour montrer que ce sont des données simulées
    // Utilisation du taux de change défini plus haut
    const salesStatistics: SalesStatistics = {
      total_sales: (totalSales + 1234) * exchangeRate, // Conversion en Francs Burundais
      total_orders: totalOrders + 42, // Ajout d'un nombre pour différencier des données statiques originales
      total_customers: 2042, // Différent de la valeur statique originale (1805)
      change_sales: "+17%", // Différent de la valeur statique originale (+14%)
      change_orders: "+12%", // Différent de la valeur statique originale (+8%)
      change_customers: "+21%", // Différent de la valeur statique originale (+16%)
      change_type_sales: "positive",
      change_type_orders: "positive",
      change_type_customers: "positive"
    };

    return {
      sales_statistics: salesStatistics,
      sales_data: salesData,
      category_data: categoryData,
      traffic_data: trafficData
    };
  }
};

export default statisticsApi;
