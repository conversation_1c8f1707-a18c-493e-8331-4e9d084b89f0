"""
Service d'envoi d'emails pour les notifications
"""

from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
from .models import NotificationPreference
import logging

logger = logging.getLogger(__name__)


class EmailNotificationService:
    """Service pour envoyer des notifications par email"""
    
    @staticmethod
    def send_notification_email(user, notification_type, subject, message, template_name=None, context=None):
        """
        Envoie un email de notification à un utilisateur selon ses préférences
        
        Args:
            user: L'utilisateur destinataire
            notification_type: Type de notification ('promotion', 'order', 'system')
            subject: Sujet de l'email
            message: Message de l'email
            template_name: Nom du template HTML (optionnel)
            context: Contexte pour le template (optionnel)
        """
        try:
            # Vérifier les préférences de l'utilisateur
            try:
                preferences = NotificationPreference.objects.get(user=user)
            except NotificationPreference.DoesNotExist:
                # Créer des préférences par défaut si elles n'existent pas
                preferences = NotificationPreference.objects.create(user=user)
            
            # Vérifier si l'utilisateur souhaite recevoir ce type d'email
            should_send = False
            if notification_type == 'promotion' and preferences.email_promotions:
                should_send = True
            elif notification_type == 'order' and preferences.email_orders:
                should_send = True
            elif notification_type == 'system' and preferences.email_system:
                should_send = True
            
            if not should_send:
                logger.info(f"Email non envoyé à {user.email} - préférences désactivées pour {notification_type}")
                return False
            
            # Préparer le contexte par défaut
            default_context = {
                'user': user,
                'user_name': f"{user.first_name} {user.last_name}".strip() or user.email,
                'site_url': settings.SITE_URL,
                'site_name': 'JosNet',
                'message': message,
                'notification_type': notification_type
            }
            
            if context:
                default_context.update(context)
            
            # Utiliser un template HTML si fourni
            if template_name:
                try:
                    html_content = render_to_string(template_name, default_context)
                    text_content = strip_tags(html_content)
                    
                    # Créer un email avec contenu HTML et texte
                    email = EmailMultiAlternatives(
                        subject=subject,
                        body=text_content,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        to=[user.email]
                    )
                    email.attach_alternative(html_content, "text/html")
                    result = email.send()
                    
                except Exception as e:
                    logger.error(f"Erreur lors du rendu du template {template_name}: {e}")
                    # Fallback vers un email simple
                    result = send_mail(
                        subject=subject,
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[user.email],
                        fail_silently=False
                    )
            else:
                # Email simple sans template
                result = send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=False
                )
            
            if result:
                logger.info(f"Email envoyé avec succès à {user.email} - Type: {notification_type}")
                return True
            else:
                logger.error(f"Échec de l'envoi d'email à {user.email}")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi d'email à {user.email}: {e}")
            return False
    
    @staticmethod
    def send_promotion_email(user, promotion_title, promotion_description, promotion_link=None):
        """Envoie un email de promotion"""
        subject = f"🎉 Nouvelle promotion: {promotion_title}"
        
        message = f"""
Bonjour {user.first_name or user.email},

Nous avons une nouvelle promotion spécialement pour vous !

{promotion_title}

{promotion_description}
"""
        
        if promotion_link:
            message += f"\n\nDécouvrez cette offre: {promotion_link}"
        
        message += f"""

Cordialement,
L'équipe JosNet

---
Vous recevez cet email car vous êtes abonné(e) aux notifications de promotions.
Pour modifier vos préférences: {settings.SITE_URL}/account/notifications
"""
        
        context = {
            'promotion_title': promotion_title,
            'promotion_description': promotion_description,
            'promotion_link': promotion_link
        }
        
        return EmailNotificationService.send_notification_email(
            user=user,
            notification_type='promotion',
            subject=subject,
            message=message,
            template_name='notifications/promotion_email.html',
            context=context
        )
    
    @staticmethod
    def send_order_email(user, order, status_message):
        """Envoie un email de mise à jour de commande"""
        subject = f"Mise à jour de votre commande #{order.id}"
        
        message = f"""
Bonjour {user.first_name or user.email},

Votre commande #{order.id} a été mise à jour.

{status_message}

Détails de la commande:
- Numéro: #{order.id}
- Montant: {order.total} {order.currency}
- Statut: {order.get_status_display()}

Vous pouvez suivre votre commande ici: {settings.SITE_URL}/account/orders/{order.id}

Cordialement,
L'équipe JosNet

---
Pour modifier vos préférences de notification: {settings.SITE_URL}/account/notifications
"""
        
        context = {
            'order': order,
            'status_message': status_message,
            'order_url': f"{settings.SITE_URL}/account/orders/{order.id}"
        }
        
        return EmailNotificationService.send_notification_email(
            user=user,
            notification_type='order',
            subject=subject,
            message=message,
            template_name='notifications/order_email.html',
            context=context
        )
    
    @staticmethod
    def send_system_email(user, title, message_content):
        """Envoie un email système"""
        subject = f"JosNet - {title}"
        
        message = f"""
Bonjour {user.first_name or user.email},

{message_content}

Cordialement,
L'équipe JosNet

---
Pour modifier vos préférences de notification: {settings.SITE_URL}/account/notifications
"""
        
        context = {
            'title': title,
            'message_content': message_content
        }
        
        return EmailNotificationService.send_notification_email(
            user=user,
            notification_type='system',
            subject=subject,
            message=message,
            template_name='notifications/system_email.html',
            context=context
        )
