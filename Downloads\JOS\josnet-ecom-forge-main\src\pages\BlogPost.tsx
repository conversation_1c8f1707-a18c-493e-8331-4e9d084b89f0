
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Calendar, User, Tag, Share2, MessageSquare, ArrowLeft, Bookmark, ThumbsUp, Search } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";

// Mock blog posts data
const blogPosts = [
  {
    id: 1,
    title: "Les tendances technologiques à surveiller en 2025",
    excerpt: "Découvrez les innovations qui vont façonner l'avenir de la technologie et transformer nos vies quotidiennes.",
    content: `
      <p>Dans un monde en constante évolution technologique, il est essentiel de rester à jour avec les dernières tendances qui façonnent notre avenir numérique. L'année 2025 promet d'être riche en innovations disruptives et en avancées significatives dans plusieurs domaines clés.</p>
      
      <h2>L'intelligence artificielle générative</h2>
      <p>L'IA générative continue sa progression fulgurante, transformant profondément nos interactions avec la technologie. Les modèles de langage avancés sont désormais capables de générer du contenu créatif indiscernable de celui produit par des humains, révolutionnant des secteurs entiers comme le marketing, le design et même le développement logiciel.</p>
      <p>Les entreprises qui sauront intégrer ces technologies à leur flux de travail gagneront un avantage compétitif considérable, tandis que de nouvelles questions éthiques et réglementaires se poseront quant à l'authenticité et la propriété intellectuelle.</p>
      
      <h2>La connectivité 5G et au-delà</h2>
      <p>La 5G atteint enfin sa maturité en 2025, avec une couverture quasi-universelle dans les zones urbaines. Cette technologie transforme non seulement nos smartphones, mais aussi l'Internet des Objets, rendant possible de nouvelles applications dans la ville intelligente, la télémédecine et l'industrie connectée.</p>
      <p>Plus impressionnant encore, les premiers tests de la 6G commencent à émerger dans les laboratoires de recherche, promettant des vitesses jusqu'à 100 fois supérieures à la 5G et ouvrant la voie à des innovations encore inimaginables aujourd'hui.</p>
      
      <h2>L'informatique quantique</h2>
      <p>Après des années de développement théorique, l'informatique quantique commence à trouver des applications pratiques en 2025. Si les ordinateurs quantiques universels restent encore hors de portée, des systèmes quantiques spécialisés résolvent déjà des problèmes spécifiques dans des domaines comme la chimie moléculaire, l'optimisation logistique et la cryptographie.</p>
      <p>Cette révolution silencieuse pourrait transformer radicalement des industries entières, de la découverte de médicaments à la finance, en résolvant des problèmes jusqu'alors insolubles avec les ordinateurs classiques.</p>
      
      <h2>La réalité augmentée au quotidien</h2>
      <p>2025 marque enfin l'adoption massive de la réalité augmentée au-delà des domaines du jeu et du divertissement. Les lunettes AR légères et élégantes deviennent des accessoires quotidiens, superposant des informations utiles sur notre environnement réel.</p>
      <p>Navigation urbaine, assistance technique, éducation immersive... les cas d'usage se multiplient tandis que les interfaces deviennent plus naturelles et intuitives, combinant reconnaissance gestuelle, commandes vocales et suivi oculaire.</p>
      
      <h2>La durabilité numérique</h2>
      <p>Face à l'urgence climatique, 2025 voit émerger une nouvelle priorité : la durabilité numérique. Les centres de données adoptent massivement des solutions d'efficacité énergétique et d'énergie renouvelable, tandis que les fabricants de matériel s'engagent dans des démarches d'écoconception et de réparabilité.</p>
      <p>Les consommateurs et les entreprises deviennent plus conscients de leur empreinte numérique, privilégiant des solutions technologiques responsables et durables.</p>
      
      <h2>Conclusion</h2>
      <p>Ces tendances ne sont que la partie émergée de l'iceberg technologique qui se profile à l'horizon 2025. Dans ce paysage en rapide évolution, entreprises et particuliers doivent rester informés et agiles pour saisir les opportunités offertes par ces innovations tout en naviguant prudemment parmi les défis qu'elles soulèvent.</p>
      <p>JOSNET NETWORK s'engage à vous accompagner dans cette transformation numérique, en vous proposant des solutions adaptées aux dernières avancées technologiques tout en respectant des principes de durabilité et d'éthique.</p>
    `,
    image: "https://images.unsplash.com/photo-1607799279861-4dd421887fb3?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8dGVjaCUyMHRyZW5kc3xlbnwwfHwwfHx8MA%3D%3D",
    date: "12 mai 2025",
    author: "Jean Dupont",
    authorTitle: "Directeur Technique",
    authorImage: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    category: "Technologie",
    tags: ["IA", "IoT", "5G", "Tendances"],
    relatedPosts: [2, 5, 6],
    comments: 8,
    likes: 24
  },
  {
    id: 2,
    title: "Comment renforcer la sécurité de votre réseau domestique",
    excerpt: "Protégez vos appareils et vos données personnelles avec ces conseils pratiques pour sécuriser votre réseau Wi-Fi.",
    content: `<p>À venir...</p>`,
    image: "https://images.unsplash.com/photo-1614064641938-3bbee52942c7?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8bmV0d29yayUyMHNlY3VyaXR5fGVufDB8fDB8fHww",
    date: "5 mai 2025",
    author: "Marie Lambert",
    authorTitle: "Experte en Cybersécurité",
    authorImage: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    category: "Cybersécurité",
    tags: ["Sécurité", "Wi-Fi", "Réseau", "Protection"],
    relatedPosts: [1, 3, 4],
    comments: 12,
    likes: 31
  },
  {
    id: 3,
    title: "Les meilleurs outils bureautiques pour le télétravail",
    excerpt: "Maximisez votre productivité à distance avec cette sélection d'outils de collaboration et de gestion de projet.",
    content: `<p>À venir...</p>`,
    image: "https://images.unsplash.com/photo-1598257006458-087169a1f08d?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8cmVtb3RlJTIwd29ya3xlbnwwfHwwfHx8MA%3D%3D",
    date: "28 avril 2025",
    author: "Thomas Martin",
    authorTitle: "Consultant IT",
    authorImage: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    category: "Productivité",
    tags: ["Télétravail", "Collaboration", "Outils", "Productivité"],
    relatedPosts: [1, 2, 6],
    comments: 5,
    likes: 18
  },
  {
    id: 4,
    title: "L'importance des solutions de sauvegarde pour votre entreprise",
    excerpt: "Protégez les données critiques de votre entreprise contre les pertes accidentelles et les cyberattaques.",
    content: `<p>À venir...</p>`,
    image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8ZGF0YSUyMGJhY2t1cHxlbnwwfHwwfHx8MA%3D%3D",
    date: "20 avril 2025",
    author: "Sophie Dubois",
    authorTitle: "Spécialiste Solutions Entreprises",
    authorImage: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    category: "Entreprise",
    tags: ["Sauvegarde", "Données", "Entreprise", "Sécurité"],
    relatedPosts: [2, 5, 6],
    comments: 3,
    likes: 14
  },
  {
    id: 5,
    title: "Le futur des réseaux 5G au Burundi",
    excerpt: "Analyse des opportunités et défis du déploiement de la 5G pour l'économie numérique du Burundi.",
    content: `<p>À venir...</p>`,
    image: "https://images.unsplash.com/photo-1621346003993-d95cc9a356cd?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8NWd8ZW58MHx8MHx8fDA%3D",
    date: "15 avril 2025",
    author: "Jean Dupont",
    authorTitle: "Directeur Technique",
    authorImage: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    category: "Télécommunications",
    tags: ["5G", "Burundi", "Télécoms", "Innovation"],
    relatedPosts: [1, 4, 6],
    comments: 7,
    likes: 26
  },
  {
    id: 6,
    title: "Comment choisir le bon ordinateur portable pour vos besoins",
    excerpt: "Guide pratique pour vous aider à faire le meilleur choix en fonction de votre utilisation et de votre budget.",
    content: `<p>À venir...</p>`,
    image: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8bGFwdG9wfGVufDB8fDB8fHww",
    date: "8 avril 2025",
    author: "Marie Lambert",
    authorTitle: "Experte en Cybersécurité",
    authorImage: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    category: "Conseils d'achat",
    tags: ["Ordinateur", "Achat", "Conseils", "Technologie"],
    relatedPosts: [1, 2, 3],
    comments: 15,
    likes: 42
  }
];

const BlogPost = () => {
  const { id } = useParams<{ id: string }>();
  const [post, setPost] = useState<typeof blogPosts[0] | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<typeof blogPosts>([]);
  const [comment, setComment] = useState("");
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  
  useEffect(() => {
    // Find the post matching the ID
    const currentPost = blogPosts.find(p => p.id === Number(id));
    
    if (currentPost) {
      setPost(currentPost);
      
      // Get related posts
      const related = blogPosts
        .filter(p => currentPost.relatedPosts.includes(p.id))
        .slice(0, 3);
      
      setRelatedPosts(related);
    }
    
    // Reset interaction states
    setIsLiked(false);
    setIsBookmarked(false);
    
    // Scroll to top when post changes
    window.scrollTo(0, 0);
  }, [id]);

  // Handle comment submission
  const handleSubmitComment = (e: React.FormEvent) => {
    e.preventDefault();
    alert(`Commentaire soumis: ${comment}`);
    setComment("");
  };

  // If post not found
  if (!post) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-12">
          <div className="text-center py-16">
            <h2 className="text-2xl font-bold mb-4">Article non trouvé</h2>
            <p className="text-gray-600 mb-8">
              L'article que vous recherchez n'existe pas ou a été déplacé.
            </p>
            <Button asChild>
              <Link to="/blog">Retour au blog</Link>
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero section with image */}
        <div className="w-full h-64 md:h-96 bg-gray-800 relative">
          <img 
            src={post.image} 
            alt={post.title}
            className="w-full h-full object-cover opacity-70" 
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
          <div className="absolute bottom-0 left-0 right-0 p-6 md:p-12 text-white">
            <div className="container mx-auto">
              <Link to="/blog" className="inline-flex items-center text-white/80 hover:text-white mb-4">
                <ArrowLeft size={16} className="mr-2" />
                Retour au blog
              </Link>
              <h1 className="text-3xl md:text-4xl font-bold mb-4 max-w-4xl">
                {post.title}
              </h1>
              <div className="flex flex-wrap items-center text-white/80 gap-4">
                <span className="flex items-center">
                  <Calendar size={16} className="mr-2" />
                  {post.date}
                </span>
                <span className="flex items-center">
                  <User size={16} className="mr-2" />
                  {post.author}
                </span>
                <span className="flex items-center">
                  <Tag size={16} className="mr-2" />
                  {post.category}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Article content */}
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main content */}
            <div className="lg:col-span-2">
              {/* Social sharing and interactions */}
              <div className="flex justify-between items-center mb-8">
                <div className="flex items-center space-x-4">
                  <button 
                    onClick={() => setIsLiked(!isLiked)}
                    className={`flex items-center ${isLiked ? 'text-red-500' : 'text-gray-500'}`}
                  >
                    <ThumbsUp size={18} className="mr-1" />
                    <span>{isLiked ? post.likes + 1 : post.likes}</span>
                  </button>
                  <button className="flex items-center text-gray-500">
                    <MessageSquare size={18} className="mr-1" />
                    <span>{post.comments}</span>
                  </button>
                </div>
                <div className="flex items-center space-x-4">
                  <button 
                    onClick={() => setIsBookmarked(!isBookmarked)}
                    className={`${isBookmarked ? 'text-primary' : 'text-gray-500'}`}
                  >
                    <Bookmark size={18} />
                  </button>
                  <button className="text-gray-500">
                    <Share2 size={18} />
                  </button>
                </div>
              </div>
              
              {/* Article content */}
              <div className="prose prose-slate max-w-none mb-12">
                <div dangerouslySetInnerHTML={{ __html: post.content }} />
              </div>
              
              {/* Tags */}
              <div className="mb-8">
                <h3 className="font-medium mb-3">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map(tag => (
                    <Link 
                      key={tag}
                      to={`/blog?tag=${tag}`}
                      className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-full text-sm"
                    >
                      {tag}
                    </Link>
                  ))}
                </div>
              </div>
              
              <Separator className="my-12" />
              
              {/* Author info */}
              <div className="flex items-center mb-12">
                <img 
                  src={post.authorImage}
                  alt={post.author} 
                  className="w-16 h-16 rounded-full mr-4 object-cover"
                />
                <div>
                  <h3 className="font-medium text-lg">{post.author}</h3>
                  <p className="text-gray-500 text-sm">{post.authorTitle}</p>
                  <p className="text-gray-700 mt-2">
                    Expert en technologies de l'information avec plus de 10 ans d'expérience dans le secteur.
                  </p>
                </div>
              </div>
              
              {/* Comments section */}
              <div>
                <h3 className="font-bold text-xl mb-6">Commentaires ({post.comments})</h3>
                
                {/* Comment form */}
                <form onSubmit={handleSubmitComment} className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
                  <h4 className="font-medium mb-4">Laisser un commentaire</h4>
                  <textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="w-full border border-gray-300 rounded-md p-3 min-h-[120px] mb-4"
                    placeholder="Partagez vos pensées..."
                    required
                  ></textarea>
                  <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
                    <div className="sm:flex-grow grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <Input 
                        type="text" 
                        placeholder="Nom*" 
                        required
                      />
                      <Input 
                        type="email" 
                        placeholder="Email*"
                        required
                      />
                    </div>
                    <Button type="submit">Publier</Button>
                  </div>
                </form>
                
                {/* Sample comments */}
                <div className="space-y-6">
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start">
                      <img 
                        src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt="Commentateur" 
                        className="w-10 h-10 rounded-full mr-4 object-cover"
                      />
                      <div className="flex-grow">
                        <div className="flex flex-wrap justify-between mb-1">
                          <h4 className="font-medium">Robert Ngendakumana</h4>
                          <span className="text-gray-500 text-sm">10 mai 2025</span>
                        </div>
                        <p className="text-gray-700">
                          Article très intéressant qui donne un bon aperçu des technologies émergentes. 
                          J'aimerais en savoir plus sur l'impact de ces technologies au Burundi, 
                          particulièrement dans le secteur de l'éducation.
                        </p>
                        <div className="flex items-center mt-2">
                          <button className="text-sm text-gray-500 hover:text-gray-700 mr-4">Répondre</button>
                          <button className="text-sm text-gray-500 hover:text-gray-700 flex items-center">
                            <ThumbsUp size={14} className="mr-1" /> 3
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start">
                      <img 
                        src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt="Commentateur" 
                        className="w-10 h-10 rounded-full mr-4 object-cover"
                      />
                      <div className="flex-grow">
                        <div className="flex flex-wrap justify-between mb-1">
                          <h4 className="font-medium">Claire Nimbona</h4>
                          <span className="text-gray-500 text-sm">9 mai 2025</span>
                        </div>
                        <p className="text-gray-700">
                          La partie sur la durabilité numérique est particulièrement pertinente. 
                          Il est temps que les entreprises technologiques prennent au sérieux leur 
                          impact environnemental. Merci pour cet article enrichissant !
                        </p>
                        <div className="flex items-center mt-2">
                          <button className="text-sm text-gray-500 hover:text-gray-700 mr-4">Répondre</button>
                          <button className="text-sm text-gray-500 hover:text-gray-700 flex items-center">
                            <ThumbsUp size={14} className="mr-1" /> 5
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* View more button */}
                  <Button variant="outline" className="w-full">
                    Voir plus de commentaires
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Sidebar */}
            <div className="lg:col-span-1">
              {/* Search */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
                <h3 className="font-medium mb-4">Rechercher</h3>
                <div className="relative">
                  <Input 
                    placeholder="Rechercher..."
                    className="pl-10"
                  />
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                </div>
              </div>
              
              {/* Categories */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
                <h3 className="font-medium mb-4">Catégories</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/blog?category=Technologie" className="text-gray-700 hover:text-primary flex justify-between">
                      <span>Technologie</span>
                      <span className="text-gray-500">8</span>
                    </Link>
                  </li>
                  <li>
                    <Link to="/blog?category=Cybersécurité" className="text-gray-700 hover:text-primary flex justify-between">
                      <span>Cybersécurité</span>
                      <span className="text-gray-500">5</span>
                    </Link>
                  </li>
                  <li>
                    <Link to="/blog?category=Productivité" className="text-gray-700 hover:text-primary flex justify-between">
                      <span>Productivité</span>
                      <span className="text-gray-500">3</span>
                    </Link>
                  </li>
                  <li>
                    <Link to="/blog?category=Entreprise" className="text-gray-700 hover:text-primary flex justify-between">
                      <span>Entreprise</span>
                      <span className="text-gray-500">6</span>
                    </Link>
                  </li>
                  <li>
                    <Link to="/blog?category=Télécommunications" className="text-gray-700 hover:text-primary flex justify-between">
                      <span>Télécommunications</span>
                      <span className="text-gray-500">4</span>
                    </Link>
                  </li>
                </ul>
              </div>
              
              {/* Related posts */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
                <h3 className="font-medium mb-4">Articles liés</h3>
                <div className="space-y-4">
                  {relatedPosts.map((related) => (
                    <Link key={related.id} to={`/blog/${related.id}`} className="flex group">
                      <img 
                        src={related.image} 
                        alt={related.title}
                        className="w-16 h-16 object-cover rounded-md mr-3 flex-shrink-0" 
                      />
                      <div>
                        <h4 className="font-medium text-sm group-hover:text-primary transition-colors line-clamp-2">
                          {related.title}
                        </h4>
                        <span className="text-gray-500 text-xs">{related.date}</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
              
              {/* Tags cloud */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="font-medium mb-4">Tags populaires</h3>
                <div className="flex flex-wrap gap-2">
                  <Link 
                    to="/blog?tag=Technologie"
                    className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-full text-sm"
                  >
                    Technologie
                  </Link>
                  <Link 
                    to="/blog?tag=Sécurité"
                    className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-full text-sm"
                  >
                    Sécurité
                  </Link>
                  <Link 
                    to="/blog?tag=Cloud"
                    className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-full text-sm"
                  >
                    Cloud
                  </Link>
                  <Link 
                    to="/blog?tag=5G"
                    className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-full text-sm"
                  >
                    5G
                  </Link>
                  <Link 
                    to="/blog?tag=IA"
                    className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-full text-sm"
                  >
                    IA
                  </Link>
                  <Link 
                    to="/blog?tag=Innovation"
                    className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-full text-sm"
                  >
                    Innovation
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Newsletter section */}
        <section className="bg-gray-100 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-4">Abonnez-vous à notre newsletter</h2>
              <p className="text-gray-600 mb-6">
                Recevez nos derniers articles et actualités directement dans votre boîte mail.
              </p>
              <form className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="email"
                  placeholder="Votre adresse email"
                  className="flex-grow"
                  required
                />
                <Button type="submit">S'abonner</Button>
              </form>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default BlogPost;
