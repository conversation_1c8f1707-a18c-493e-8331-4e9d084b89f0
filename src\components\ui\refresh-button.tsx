import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, Loader2 } from 'lucide-react';
import { useRefreshButton, RefreshOptions } from '@/hooks/useRefresh';
import { cn } from '@/lib/utils';

export interface RefreshButtonProps extends RefreshOptions {
  /**
   * Button variant
   */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  
  /**
   * Button size
   */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Button text (if not icon-only)
   */
  children?: React.ReactNode;
  
  /**
   * Whether to show only icon
   */
  iconOnly?: boolean;
  
  /**
   * Custom icon
   */
  icon?: React.ReactNode;
  
  /**
   * Custom loading icon
   */
  loadingIcon?: React.ReactNode;
}

/**
 * Standardized refresh button component
 */
export const RefreshButton: React.FC<RefreshButtonProps> = ({
  variant = 'outline',
  size = 'default',
  className,
  children,
  iconOnly = false,
  icon,
  loadingIcon,
  ...refreshOptions
}) => {
  const { onClick, disabled, isLoading } = useRefreshButton(refreshOptions);
  
  const defaultIcon = icon || <RefreshCw className="h-4 w-4" />;
  const defaultLoadingIcon = loadingIcon || <Loader2 className="h-4 w-4 animate-spin" />;
  
  if (iconOnly) {
    return (
      <Button
        variant={variant}
        size="icon"
        onClick={onClick}
        disabled={disabled}
        className={cn("flex items-center justify-center", className)}
        title="Actualiser"
      >
        {isLoading ? defaultLoadingIcon : defaultIcon}
      </Button>
    );
  }
  
  return (
    <Button
      variant={variant}
      size={size}
      onClick={onClick}
      disabled={disabled}
      className={cn("flex items-center gap-2", className)}
    >
      {isLoading ? defaultLoadingIcon : defaultIcon}
      {children || "Actualiser"}
    </Button>
  );
};

/**
 * Specialized refresh buttons for common use cases
 */
export const ProductsRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['products']]}
    successMessage="Liste des produits mise à jour"
    {...props}
  />
);

export const CategoriesRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['categories']]}
    successMessage="Liste des catégories mise à jour"
    {...props}
  />
);

export const InventoryRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['inventory']]}
    successMessage="Données d'inventaire mises à jour"
    {...props}
  />
);

export const PromotionsRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['promotions']]}
    successMessage="Liste des promotions mise à jour"
    {...props}
  />
);

export const LogsRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['logs'], ['logs-statistics']]}
    successMessage="Logs mis à jour"
    {...props}
  />
);

export const CMSRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['cms-pages'], ['cms-blog'], ['cms-media'], ['cms-menus'], ['cms-settings']]}
    successMessage="Contenu CMS mis à jour"
    {...props}
  />
);

export const OrdersRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['orders']]}
    successMessage="Liste des commandes mise à jour"
    {...props}
  />
);

export const UsersRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['users']]}
    successMessage="Liste des utilisateurs mise à jour"
    {...props}
  />
);

export const InvoicesRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['invoices']]}
    successMessage="Liste des factures mise à jour"
    {...props}
  />
);

export const TransactionsRefreshButton: React.FC<Omit<RefreshButtonProps, 'queryKeys'>> = (props) => (
  <RefreshButton
    queryKeys={[['transactions']]}
    successMessage="Liste des transactions mise à jour"
    {...props}
  />
);
