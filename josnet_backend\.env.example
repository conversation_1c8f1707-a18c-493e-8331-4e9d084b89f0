# Configuration de base Django
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Base de données
DATABASE_URL=sqlite:///db.sqlite3

# Configuration Email
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre-mot-de-passe-app
DEFAULT_FROM_EMAIL=<EMAIL>

# URL du site (pour les liens dans les emails)
SITE_URL=http://localhost:8000

# Configuration pour développement (utilise la console au lieu d'envoyer de vrais emails)
# EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Configuration pour production avec Gmail
# 1. Activez l'authentification à 2 facteurs sur votre compte Gmail
# 2. Gén<PERSON>rez un mot de passe d'application : https://myaccount.google.com/apppasswords
# 3. Utilisez ce mot de passe d'application dans EMAIL_HOST_PASSWORD

# Configuration pour production avec un serveur SMTP personnalisé
# EMAIL_HOST=mail.votre-domaine.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=votre-mot-de-passe
# DEFAULT_FROM_EMAIL=<EMAIL>
