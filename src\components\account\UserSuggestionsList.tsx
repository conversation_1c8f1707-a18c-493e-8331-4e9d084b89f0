import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Edit, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ProductSuggestion } from '@/services/accountApi';

interface UserSuggestionsListProps {
  suggestions: ProductSuggestion[];
  onDelete?: (id: number) => void;
  onEdit?: (suggestion: ProductSuggestion) => void;
}

const UserSuggestionsList: React.FC<UserSuggestionsListProps> = ({ 
  suggestions, 
  onDelete,
  onEdit 
}) => {
  const { toast } = useToast();

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'en attente':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
      case 'approuvé':
        return 'bg-green-100 text-green-800';
      case 'rejected':
      case 'refusé':
      case 'rejeté':
        return 'bg-red-100 text-red-800';
      case 'implemented':
      case 'implémenté':
        return 'bg-blue-100 text-blue-800';
      case 'in review':
      case 'en cours d\'examen':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplayName = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'En attente';
      case 'approved':
        return 'Approuvé';
      case 'rejected':
        return 'Rejeté';
      case 'implemented':
        return 'Implémenté';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (suggestions.length === 0) {
    return (
      <Card className="text-center p-6">
        <CardContent className="pt-6">
          <p className="text-gray-500 mb-4">Vous n'avez pas encore soumis de suggestions de produits.</p>
          <p className="text-sm text-gray-400">
            Utilisez le formulaire ci-dessus pour suggérer un produit que vous aimeriez voir dans notre catalogue.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Vos suggestions</h3>
      {suggestions.map((suggestion) => (
        <Card key={suggestion.id} className="overflow-hidden">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">{suggestion.name}</CardTitle>
                <CardDescription className="flex items-center mt-1">
                  <Calendar className="h-3.5 w-3.5 mr-1" />
                  Soumis le {formatDate(suggestion.created_at)}
                </CardDescription>
              </div>
              <Badge className={getStatusColor(suggestion.status)}>
                {getStatusDisplayName(suggestion.status)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Catégorie</p>
                <p>{suggestion.category}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Description</p>
                <p className="text-sm">{suggestion.description}</p>
              </div>
              
              {suggestion.estimated_price && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Prix estimé</p>
                  <p>{parseFloat(suggestion.estimated_price).toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}</p>
                </div>
              )}
              
              <div>
                <p className="text-sm font-medium text-gray-500">Raison</p>
                <p className="text-sm">{suggestion.reason}</p>
              </div>
              
              {suggestion.admin_response && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                  <p className="text-sm font-medium text-gray-500 mb-1">Réponse de l'administrateur</p>
                  <p className="text-sm">{suggestion.admin_response}</p>
                  <p className="text-xs text-gray-400 mt-1 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {suggestion.response_date ? formatDate(suggestion.response_date) : 'Date non disponible'}
                  </p>
                </div>
              )}
              
              {suggestion.status.toLowerCase() === 'en attente' || suggestion.status.toLowerCase() === 'pending' ? (
                <div className="flex justify-end space-x-2 mt-4">
                  {onEdit && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex items-center"
                      onClick={() => onEdit(suggestion)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Modifier
                    </Button>
                  )}
                  
                  {onDelete && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="flex items-center text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Supprimer
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Cette action ne peut pas être annulée. Cela supprimera définitivement votre suggestion de produit.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Annuler</AlertDialogCancel>
                          <AlertDialogAction 
                            onClick={() => {
                              onDelete(suggestion.id);
                              toast({
                                description: "Suggestion supprimée avec succès",
                              });
                            }}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Supprimer
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </div>
              ) : null}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default UserSuggestionsList;
