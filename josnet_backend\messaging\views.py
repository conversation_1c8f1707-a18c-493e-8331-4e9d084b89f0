from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Q
from django.shortcuts import get_object_or_404
from collections import defaultdict

from .models import Conversation, Message, Category, Tag, ResponseTemplate
from .serializers import (
    ConversationListSerializer,
    ConversationDetailSerializer,
    ConversationCreateSerializer,
    MessageSerializer,
    CategorySerializer,
    TagSerializer,
    ResponseTemplateSerializer,
    TemplateGroupSerializer
)


class IsAdminOrOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object or admins to access it.
    """
    def has_object_permission(self, request, view, obj):
        # Admin permissions
        if request.user.is_staff or request.user.is_superuser:
            return True

        # Check if the object has a customer attribute (Conversation)
        if hasattr(obj, 'customer'):
            return obj.customer == request.user

        # Check if the object has a conversation attribute (Message)
        if hasattr(obj, 'conversation'):
            return obj.conversation.customer == request.user

        # Default deny
        return False


class ConversationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for conversations.
    """
    permission_classes = [permissions.IsAuthenticated, IsAdminOrOwner]

    def get_queryset(self):
        """
        Get the list of conversations for the current user.
        For customers: only their own conversations
        For staff/admin: all conversations with additional filtering options
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            user = self.request.user

            # Annotate with message count
            queryset = Conversation.objects.annotate(
                message_count=Count('messages')
            ).select_related('customer', 'category', 'assigned_to')

            # Filter based on user role
            if user.is_staff or user.is_superuser:
                return queryset
            else:
                return queryset.filter(customer=user)
        except Exception as e:
            logger.error(f"Error in get_queryset: {e}")
            # Return an empty queryset instead of crashing
            return Conversation.objects.none()

    def get_serializer_class(self):
        """
        Return appropriate serializer class based on action.
        """
        if self.action == 'create':
            return ConversationCreateSerializer
        elif self.action in ['retrieve', 'update', 'partial_update']:
            return ConversationDetailSerializer
        return ConversationListSerializer

    def retrieve(self, request, *args, **kwargs):
        """
        Mark conversation as read when retrieved.
        """
        instance = self.get_object()

        # Mark as read based on user role
        if request.user.is_staff or request.user.is_superuser:
            instance.mark_as_read_by_admin()
        else:
            instance.mark_as_read_by_customer()

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """
        Update the status of a conversation.
        """
        conversation = self.get_object()
        status_value = request.data.get('status')

        if status_value not in [s[0] for s in Conversation.STATUSES]:
            return Response(
                {'error': 'Invalid status value'},
                status=status.HTTP_400_BAD_REQUEST
            )

        conversation.update_status(status_value)
        serializer = ConversationDetailSerializer(conversation, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """
        Get the count of unread conversations.
        """
        user = request.user

        if user.is_staff or user.is_superuser:
            count = Conversation.objects.filter(is_read_by_admin=False).count()
        else:
            count = Conversation.objects.filter(
                customer=user,
                is_read_by_customer=False
            ).count()

        return Response({'unread_count': count})

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        Get conversation statistics.
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"Getting conversation statistics")

            # Get query parameters
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            # Base queryset
            queryset = Conversation.objects.all()

            # Apply date filters if provided
            if start_date:
                queryset = queryset.filter(created_at__gte=start_date)
            if end_date:
                queryset = queryset.filter(created_at__lte=end_date)

            # Calculate statistics
            total_conversations = queryset.count()

            # Group by status
            status_counts = {}
            for status_choice, _ in Conversation.STATUSES:
                status_counts[status_choice] = queryset.filter(status=status_choice).count()

            # Group by priority
            priority_counts = {}
            for priority_choice, _ in Conversation.PRIORITIES:
                priority_counts[priority_choice] = queryset.filter(priority=priority_choice).count()

            # Group by category
            category_counts = {}
            categories = Category.objects.all()
            for category in categories:
                category_counts[category.id] = queryset.filter(category=category).count()

            # Average response time calculation would require more complex logic
            # For now, we'll return null
            avg_response_time = None

            statistics = {
                'total_conversations': total_conversations,
                'by_status': status_counts,
                'by_priority': priority_counts,
                'by_category': category_counts,
                'avg_response_time_hours': avg_response_time
            }

            logger.info(f"Statistics calculated successfully")
            return Response(statistics)
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for conversation categories.
    """
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Only admins can create, update or delete categories.
        """
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get only active categories.
        """
        categories = Category.objects.filter(is_active=True)
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data)


class TagViewSet(viewsets.ModelViewSet):
    """
    API endpoint for conversation tags.
    """
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Only admins can create, update or delete tags.
        """
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get only active tags.
        """
        tags = Tag.objects.filter(is_active=True)
        serializer = self.get_serializer(tags, many=True)
        return Response(serializer.data)


class ResponseTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint for response templates.
    """
    queryset = ResponseTemplate.objects.all()
    serializer_class = ResponseTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """
        Get response templates grouped by category.
        """
        # Get all active templates
        templates = ResponseTemplate.objects.filter(is_active=True).select_related('category')

        # Group templates by category
        grouped_templates = {}

        # First, handle templates without a category
        no_category_templates = templates.filter(category__isnull=True)
        if no_category_templates.exists():
            grouped_templates[None] = {
                'category': None,
                'templates': list(no_category_templates)
            }

        # Then, handle templates with categories
        categories = Category.objects.filter(is_active=True)
        for category in categories:
            category_templates = templates.filter(category=category)
            if category_templates.exists():
                grouped_templates[category.id] = {
                    'category': category,
                    'templates': list(category_templates)
                }

        # Serialize the grouped templates
        serialized_groups = []
        for group_data in grouped_templates.values():
            serializer = TemplateGroupSerializer(group_data)
            serialized_groups.append(serializer.data)

        return Response(serialized_groups)


class MessageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for messages.
    """
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminOrOwner]

    def get_queryset(self):
        """
        Get the list of messages for a specific conversation.
        """
        conversation_id = self.kwargs.get('conversation_pk')
        return Message.objects.filter(conversation_id=conversation_id)

    def create(self, request, *args, **kwargs):
        """
        Create a new message in a conversation.
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            conversation_id = self.kwargs.get('conversation_pk')
            logger.info(f"Creating message for conversation {conversation_id}")

            try:
                conversation = get_object_or_404(Conversation, id=conversation_id)
            except Exception as e:
                logger.error(f"Error finding conversation {conversation_id}: {e}")
                return Response(
                    {"error": f"Conversation not found: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check permissions
            if not IsAdminOrOwner().has_object_permission(request, self, conversation):
                logger.warning(f"Permission denied for user {request.user.id} on conversation {conversation_id}")
                return Response(
                    {"error": "You don't have permission to add messages to this conversation"},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Add conversation to data
            data = request.data.copy()
            data['conversation'] = conversation_id

            # Log request data for debugging (excluding file content)
            safe_data = {k: v for k, v in data.items() if k != 'uploaded_files'}
            logger.info(f"Message data: {safe_data}")

            serializer = self.get_serializer(data=data)

            if not serializer.is_valid():
                logger.error(f"Serializer validation errors: {serializer.errors}")
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

            self.perform_create(serializer)
            logger.info(f"Message created successfully with ID {serializer.data.get('id')}")

            headers = self.get_success_headers(serializer.data)
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED,
                headers=headers
            )
        except Exception as e:
            logger.error(f"Unexpected error creating message: {e}", exc_info=True)
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
