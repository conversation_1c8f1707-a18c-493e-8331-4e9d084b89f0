from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache
import json


class TranslationKey(models.Model):
    """
    Modèle pour stocker les clés de traduction dynamiques
    """
    key = models.CharField(max_length=255, unique=True, verbose_name=_("Clé"))
    description = models.TextField(blank=True, null=True, verbose_name=_("Description"))
    category = models.CharField(max_length=100, blank=True, null=True, verbose_name=_("Catégorie"))
    is_active = models.BooleanField(default=True, verbose_name=_("Actif"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Créé le"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Modifié le"))

    class Meta:
        verbose_name = _("Clé de traduction")
        verbose_name_plural = _("Clés de traduction")
        ordering = ['category', 'key']

    def __str__(self):
        return self.key

    def get_translation(self, language_code):
        """Récupérer la traduction pour une langue donnée"""
        try:
            translation = self.translations.get(language_code=language_code)
            return translation.value
        except Translation.DoesNotExist:
            # Fallback vers le français si la traduction n'existe pas
            try:
                fallback = self.translations.get(language_code='fr')
                return fallback.value
            except Translation.DoesNotExist:
                return self.key


class Translation(models.Model):
    """
    Modèle pour stocker les traductions
    """
    LANGUAGE_CHOICES = [
        ('fr', 'Français'),
        ('en', 'English'),
        ('sw', 'Kiswahili'),
        ('rn', 'Kirundi'),
    ]

    key = models.ForeignKey(
        TranslationKey, 
        on_delete=models.CASCADE, 
        related_name='translations',
        verbose_name=_("Clé")
    )
    language_code = models.CharField(
        max_length=5, 
        choices=LANGUAGE_CHOICES,
        verbose_name=_("Langue")
    )
    value = models.TextField(verbose_name=_("Traduction"))
    is_approved = models.BooleanField(default=False, verbose_name=_("Approuvé"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Créé le"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Modifié le"))

    class Meta:
        verbose_name = _("Traduction")
        verbose_name_plural = _("Traductions")
        unique_together = ['key', 'language_code']
        ordering = ['key__category', 'key__key', 'language_code']

    def __str__(self):
        return f"{self.key.key} ({self.language_code}): {self.value[:50]}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Invalider le cache des traductions
        cache_key = f"translation_{self.key.key}_{self.language_code}"
        cache.delete(cache_key)


class TranslationCache(models.Model):
    """
    Modèle pour mettre en cache les traductions fréquemment utilisées
    """
    language_code = models.CharField(max_length=5, verbose_name=_("Langue"))
    translations_json = models.TextField(verbose_name=_("Traductions JSON"))
    last_updated = models.DateTimeField(auto_now=True, verbose_name=_("Dernière mise à jour"))

    class Meta:
        verbose_name = _("Cache de traduction")
        verbose_name_plural = _("Caches de traduction")
        unique_together = ['language_code']

    def __str__(self):
        return f"Cache {self.language_code}"

    def get_translations(self):
        """Récupérer les traductions depuis le JSON"""
        try:
            return json.loads(self.translations_json)
        except json.JSONDecodeError:
            return {}

    def set_translations(self, translations_dict):
        """Définir les traductions en JSON"""
        self.translations_json = json.dumps(translations_dict, ensure_ascii=False, indent=2)
        self.save()


class UserLanguagePreference(models.Model):
    """
    Modèle pour stocker les préférences de langue des utilisateurs
    """
    user = models.OneToOneField(
        'authentication.User',
        on_delete=models.CASCADE,
        related_name='language_preference',
        verbose_name=_("Utilisateur")
    )
    language_code = models.CharField(
        max_length=5,
        choices=Translation.LANGUAGE_CHOICES,
        default='fr',
        verbose_name=_("Langue préférée")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Créé le"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Modifié le"))

    class Meta:
        verbose_name = _("Préférence de langue")
        verbose_name_plural = _("Préférences de langue")

    def __str__(self):
        return f"{self.user.email} - {self.get_language_code_display()}"
