
import { useState, useEffect } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import ProductCard from "@/components/products/ProductCard";
import { Product, products, categories } from "@/utils/data";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Search, Filter, SlidersHorizontal } from "lucide-react";
import { Pagination, PaginationContent, PaginationItem, PaginationLink } from "@/components/ui/pagination";

const Products = () => {
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);
  const [activeCategory, setActiveCategory] = useState("Tous");
  const [searchTerm, setSearchTerm] = useState("");
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [inStockOnly, setInStockOnly] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Logic to handle filtering products based on multiple criteria
  useEffect(() => {
    let result = [...products];

    // Filter by category
    if (activeCategory !== "Tous") {
      result = result.filter(product => product.category === activeCategory);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(product => 
        product.name.toLowerCase().includes(term) || 
        product.description.toLowerCase().includes(term)
      );
    }

    // Filter by price range
    result = result.filter(product => 
      product.price >= priceRange[0] && product.price <= priceRange[1]
    );

    // Filter by stock
    if (inStockOnly) {
      result = result.filter(product => product.inStock);
    }

    setFilteredProducts(result);
  }, [activeCategory, searchTerm, priceRange, inStockOnly]);

  // Find max price for slider
  const maxPrice = Math.max(...products.map(product => product.price));

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Catalogue de produits</h1>
          <p className="text-gray-600">
            Découvrez notre large gamme de produits informatiques et de télécommunication.
          </p>
        </div>

        {/* Search and Filter Bar */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-grow">
            <Input 
              type="text" 
              placeholder="Rechercher des produits..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          </div>
          <Button 
            onClick={() => setShowFilters(!showFilters)}
            variant="outline" 
            className="flex items-center gap-2"
          >
            <SlidersHorizontal size={18} />
            Filtres
          </Button>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="bg-gray-50 p-6 rounded-lg mb-8 animate-fade-in shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Categories */}
              <div>
                <h3 className="font-medium mb-3 text-gray-700">Catégories</h3>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      size="sm"
                      variant={activeCategory === category ? "default" : "outline"}
                      onClick={() => setActiveCategory(category)}
                      className="transition-all duration-200"
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Price Range */}
              <div className="col-span-1 md:col-span-2">
                <h3 className="font-medium mb-3 text-gray-700">Prix</h3>
                <div className="space-y-6">
                  <Slider
                    defaultValue={[0, maxPrice]}
                    max={maxPrice}
                    step={10}
                    value={priceRange}
                    onValueChange={(value) => setPriceRange(value as [number, number])}
                    className="my-6"
                  />
                  <div className="flex items-center justify-between">
                    <div className="px-4 py-2 bg-white rounded-md border border-gray-200 shadow-sm">
                      <span className="text-sm text-gray-500">Min</span>
                      <p className="font-medium">{priceRange[0]} €</p>
                    </div>
                    <div className="px-4 py-2 bg-white rounded-md border border-gray-200 shadow-sm">
                      <span className="text-sm text-gray-500">Max</span>
                      <p className="font-medium">{priceRange[1]} €</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Stock Filter */}
              <div>
                <h3 className="font-medium mb-3 text-gray-700">Disponibilité</h3>
                <div className="flex items-center bg-white p-3 rounded-md border border-gray-200">
                  <input
                    type="checkbox"
                    id="inStock"
                    checked={inStockOnly}
                    onChange={() => setInStockOnly(!inStockOnly)}
                    className="mr-3 h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="inStock" className="text-gray-700">En stock uniquement</label>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Category Tabs - Simplified version for mobile */}
        <div className="flex overflow-x-auto pb-2 mb-6 hide-scrollbar">
          {categories.map((category) => (
            <Button
              key={category}
              variant="ghost"
              className={`whitespace-nowrap ${
                activeCategory === category ? "text-primary border-b-2 border-primary" : ""
              }`}
              onClick={() => setActiveCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Products Grid */}
        {filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium mb-2">Aucun produit trouvé</h3>
            <p className="text-gray-600">Veuillez modifier vos filtres pour voir plus de produits.</p>
          </div>
        )}

        {/* Pagination */}
        <div className="mt-12">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationLink href="#" aria-label="Previous page">
                  «
                </PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#" isActive>
                  1
                </PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">2</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">3</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#" aria-label="Next page">
                  »
                </PaginationLink>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Products;
