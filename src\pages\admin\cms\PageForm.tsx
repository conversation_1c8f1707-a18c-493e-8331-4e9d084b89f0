import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Save, ArrowLeft } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import cmsApi, { Page } from "@/services/cmsApi";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Editor } from "@/components/editor/Editor";

// Schéma de validation pour le formulaire
const pageSchema = z.object({
  title: z.string().min(1, "Le titre est requis"),
  slug: z.string().min(1, "Le slug est requis"),
  content: z.string().min(1, "Le contenu est requis"),
  template: z.string().min(1, "Le template est requis"),
  status: z.string().min(1, "Le statut est requis"),
  meta_description: z.string().optional(),
  meta_keywords: z.string().optional(),
});

type PageFormValues = z.infer<typeof pageSchema>;

const PageForm = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isEditMode = !!slug;

  // Récupérer les données de la page si on est en mode édition
  const { data: pageData, isLoading: isLoadingPage } = useQuery({
    queryKey: ["page", slug],
    queryFn: () => cmsApi.getPage(slug!),
    enabled: isEditMode,
  });

  // Formulaire avec React Hook Form
  const form = useForm<PageFormValues>({
    resolver: zodResolver(pageSchema),
    defaultValues: {
      title: "",
      slug: "",
      content: "",
      template: "standard",
      status: "draft",
      meta_description: "",
      meta_keywords: "",
    },
  });

  // Mettre à jour les valeurs du formulaire quand les données sont chargées
  useEffect(() => {
    if (pageData && isEditMode) {
      form.reset({
        title: pageData.title,
        slug: pageData.slug,
        content: pageData.content,
        template: pageData.template,
        status: pageData.status,
        meta_description: pageData.meta_description || "",
        meta_keywords: pageData.meta_keywords || "",
      });
    }
  }, [pageData, form, isEditMode]);

  // Mutation pour créer une page
  const createPage = useMutation({
    mutationFn: (data: PageFormValues) => cmsApi.createPage(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      toast({
        title: "Page créée",
        description: "La page a été créée avec succès",
      });
      navigate("/admin/cms");
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de créer la page",
        variant: "destructive",
      });
    },
  });

  // Mutation pour mettre à jour une page
  const updatePage = useMutation({
    mutationFn: (data: PageFormValues) => cmsApi.updatePage(slug!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      queryClient.invalidateQueries({ queryKey: ["page", slug] });
      toast({
        title: "Page mise à jour",
        description: "La page a été mise à jour avec succès",
      });
      navigate("/admin/cms");
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour la page",
        variant: "destructive",
      });
    },
  });

  // Gérer la soumission du formulaire
  const onSubmit = (data: PageFormValues) => {
    if (isEditMode) {
      updatePage.mutate(data);
    } else {
      createPage.mutate(data);
    }
  };

  // Générer un slug à partir du titre
  const generateSlug = (title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-");
    form.setValue("slug", slug);
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">
              {isEditMode ? "Modifier la page" : "Créer une page"}
            </h1>
            <p className="text-gray-500">
              {isEditMode
                ? "Modifiez les détails de la page"
                : "Créez une nouvelle page"}
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate("/admin/cms")}
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            Retour
          </Button>
        </div>

        {isLoadingPage && isEditMode ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs defaultValue="content" className="w-full">
                <TabsList>
                  <TabsTrigger value="content">Contenu</TabsTrigger>
                  <TabsTrigger value="seo">SEO</TabsTrigger>
                  <TabsTrigger value="settings">Paramètres</TabsTrigger>
                </TabsList>

                <Card className="mt-4">
                  <TabsContent value="content" className="p-0">
                    <CardContent className="pt-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <FormField
                          control={form.control}
                          name="title"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Titre</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Titre de la page"
                                  {...field}
                                  onChange={(e) => {
                                    field.onChange(e);
                                    if (!isEditMode && !form.getValues("slug")) {
                                      generateSlug(e.target.value);
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="slug"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Slug</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="slug-de-la-page"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                L'URL de la page (ex: /a-propos)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="content"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contenu</FormLabel>
                            <FormControl>
                              <Editor
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </TabsContent>

                  <TabsContent value="seo" className="p-0">
                    <CardContent className="pt-6 space-y-6">
                      <FormField
                        control={form.control}
                        name="meta_description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Meta Description</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Description pour les moteurs de recherche"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Une brève description de la page pour les moteurs
                              de recherche (max 160 caractères)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="meta_keywords"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Meta Keywords</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="mot-clé1, mot-clé2, mot-clé3"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Mots-clés séparés par des virgules
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </TabsContent>

                  <TabsContent value="settings" className="p-0">
                    <CardContent className="pt-6 space-y-6">
                      <FormField
                        control={form.control}
                        name="template"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Template</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner un template" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="standard">Standard</SelectItem>
                                <SelectItem value="homepage">Page d'accueil</SelectItem>
                                <SelectItem value="services">Services</SelectItem>
                                <SelectItem value="legal">Légal</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Le template détermine l'apparence de la page
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Statut</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner un statut" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="draft">Brouillon</SelectItem>
                                <SelectItem value="published">Publié</SelectItem>
                                <SelectItem value="archived">Archivé</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Seules les pages publiées sont visibles par les visiteurs
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </TabsContent>

                  <CardFooter className="flex justify-end gap-2 pt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate("/admin/cms")}
                    >
                      Annuler
                    </Button>
                    <Button
                      type="submit"
                      disabled={
                        createPage.isPending || updatePage.isPending
                      }
                      className="flex items-center gap-2"
                    >
                      {createPage.isPending || updatePage.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      {isEditMode ? "Mettre à jour" : "Créer"}
                    </Button>
                  </CardFooter>
                </Card>
              </Tabs>
            </form>
          </Form>
        )}
      </div>
    </AdminLayout>
  );
};

export default PageForm;
