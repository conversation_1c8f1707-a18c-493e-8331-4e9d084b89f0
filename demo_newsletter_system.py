#!/usr/bin/env python
"""
Script de démonstration du système de newsletter.
Ce script montre comment le système fonctionne en créant des données de test
et en déclenchant les notifications automatiques.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
sys.path.append('josnet_backend')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category, Promotion

User = get_user_model()


def create_demo_data():
    """Créer des données de démonstration."""
    print("🔧 Création des données de démonstration...")
    
    # Créer un utilisateur admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'demopass123',
            'first_name': 'Admin',
            'last_name': 'Demo',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('demopass123')
        admin_user.save()
        print(f"   ✅ Utilisateur admin créé: {admin_user.email}")
    else:
        print(f"   ✅ Utilisateur admin existant: {admin_user.email}")
    
    # Créer des abonnés de démonstration
    subscribers_data = [
        ('<EMAIL>', 'Alice', 'Martin', True, False),
        ('<EMAIL>', 'Bob', 'Dupont', False, True),
        ('<EMAIL>', 'Charlie', 'Bernard', True, True),
    ]
    
    subscribers = []
    for email, first_name, last_name, promo_pref, product_pref in subscribers_data:
        user, created = User.objects.get_or_create(
            email=email,
            defaults={
                'first_name': first_name,
                'last_name': last_name,
                'password': 'demopass123'
            }
        )
        if created:
            user.set_password('demopass123')
            user.save()
        
        subscription, created = NewsletterSubscription.objects.get_or_create(
            user=user,
            email=email,
            defaults={
                'preferences_promotions': promo_pref,
                'preferences_new_products': product_pref,
                'preferences_newsletters': True,
                'preferences_order_updates': True
            }
        )
        subscribers.append(subscription)
        
        if created:
            print(f"   ✅ Abonné créé: {email} (Promos: {promo_pref}, Produits: {product_pref})")
        else:
            print(f"   ✅ Abonné existant: {email}")
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Électronique Demo',
        defaults={'slug': 'electronique-demo'}
    )
    if created:
        print(f"   ✅ Catégorie créée: {category.name}")
    else:
        print(f"   ✅ Catégorie existante: {category.name}")
    
    return admin_user, subscribers, category


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def demo_promotion_notification():
    """Démonstration de notification de promotion."""
    print("\n🎉 DÉMONSTRATION: Notification de Promotion")
    print("=" * 50)
    
    admin_user, subscribers, category = create_demo_data()
    
    # Créer une promotion
    promotion = Promotion.objects.create(
        title='🎉 MÉGA SOLDES - 50% de réduction !',
        description='Profitez de notre méga promotion avec 50% de réduction sur tous les produits électroniques. Offre limitée dans le temps !',
        promotion_type='percentage',
        discount_percentage=Decimal('50.00'),
        start_date=timezone.now(),
        end_date=timezone.now() + timedelta(days=7),
        status='draft',
        created_by=admin_user,
        send_email_notification=True
    )
    
    print(f"✅ Promotion créée: {promotion.title}")
    print(f"   📅 Début: {promotion.start_date.strftime('%d/%m/%Y %H:%M')}")
    print(f"   📅 Fin: {promotion.end_date.strftime('%d/%m/%Y %H:%M')}")
    print(f"   💰 Réduction: {promotion.discount_percentage}%")
    
    # Compter les abonnés aux promotions
    promo_subscribers = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_promotions=True
    ).count()
    
    print(f"\n📊 Abonnés qui recevront l'email: {promo_subscribers}")
    
    # Activer la promotion (cela déclenche l'envoi automatique)
    print("\n🚀 Activation de la promotion...")
    promotion.status = 'active'
    promotion.save()
    
    # Vérifier si l'email a été envoyé
    promotion.refresh_from_db()
    if promotion.email_sent:
        print(f"✅ Email envoyé automatiquement à {promotion.email_sent_at}")
    else:
        print("⚠️  Email pas encore envoyé")
    
    # Vérifier les campagnes créées
    campaigns = NewsletterCampaign.objects.filter(
        campaign_type='promotion',
        title__contains='MÉGA SOLDES'
    )
    
    if campaigns.exists():
        campaign = campaigns.first()
        print(f"\n📧 Campagne email créée:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   ❌ Échecs: {campaign.failed_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
    
    return promotion


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def demo_product_notification():
    """Démonstration de notification de nouveau produit."""
    print("\n✨ DÉMONSTRATION: Notification Nouveau Produit")
    print("=" * 50)
    
    admin_user, subscribers, category = create_demo_data()
    
    # Créer un nouveau produit
    product = Product.objects.create(
        name='🚀 iPhone 15 Pro Max - Révolutionnaire',
        slug='iphone-15-pro-max-revolutionnaire',
        sku='IP15PM001',
        description='Le nouveau iPhone 15 Pro Max avec des fonctionnalités révolutionnaires qui vont changer votre façon d\'utiliser votre smartphone. Écran Super Retina XDR, puce A17 Pro, appareil photo professionnel.',
        short_description='Le smartphone le plus avancé jamais créé par Apple',
        price=Decimal('1299.99'),
        sale_price=Decimal('1199.99'),
        status='draft',
        created_by=admin_user
    )
    product.categories.add(category)
    
    print(f"✅ Produit créé: {product.name}")
    print(f"   💰 Prix: {product.price}€ (Promo: {product.sale_price}€)")
    print(f"   📱 SKU: {product.sku}")
    print(f"   📂 Catégorie: {category.name}")
    
    # Compter les abonnés aux nouveaux produits
    product_subscribers = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_new_products=True
    ).count()
    
    print(f"\n📊 Abonnés qui recevront l'email: {product_subscribers}")
    
    # Publier le produit (cela déclenche l'envoi automatique)
    print("\n🚀 Publication du produit...")
    product.status = 'published'
    product.save()
    
    # Vérifier les campagnes créées
    campaigns = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='iPhone 15'
    )
    
    if campaigns.exists():
        campaign = campaigns.first()
        print(f"\n📧 Campagne email créée:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   ❌ Échecs: {campaign.failed_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
    
    return product


def show_statistics():
    """Afficher les statistiques du système."""
    print("\n📊 STATISTIQUES DU SYSTÈME")
    print("=" * 50)
    
    # Statistiques des abonnements
    total_subs = NewsletterSubscription.objects.count()
    active_subs = NewsletterSubscription.objects.filter(is_active=True).count()
    promo_subs = NewsletterSubscription.objects.filter(is_active=True, preferences_promotions=True).count()
    product_subs = NewsletterSubscription.objects.filter(is_active=True, preferences_new_products=True).count()
    
    print(f"📧 Abonnements:")
    print(f"   Total: {total_subs}")
    print(f"   Actifs: {active_subs}")
    print(f"   Promotions: {promo_subs}")
    print(f"   Nouveaux produits: {product_subs}")
    
    # Statistiques des campagnes
    total_campaigns = NewsletterCampaign.objects.count()
    promo_campaigns = NewsletterCampaign.objects.filter(campaign_type='promotion').count()
    product_campaigns = NewsletterCampaign.objects.filter(campaign_type='new_product').count()
    
    print(f"\n📨 Campagnes:")
    print(f"   Total: {total_campaigns}")
    print(f"   Promotions: {promo_campaigns}")
    print(f"   Nouveaux produits: {product_campaigns}")
    
    # Dernières campagnes
    recent_campaigns = NewsletterCampaign.objects.order_by('-created_at')[:3]
    if recent_campaigns:
        print(f"\n📋 Dernières campagnes:")
        for campaign in recent_campaigns:
            print(f"   • {campaign.title} ({campaign.get_campaign_type_display()}) - {campaign.sent_count} envoyés")


def main():
    """Fonction principale de démonstration."""
    print("🚀 DÉMONSTRATION DU SYSTÈME DE NEWSLETTER")
    print("=" * 60)
    print("Ce script démontre le fonctionnement automatique du système de newsletter.")
    print("Les emails seront affichés dans la console.\n")
    
    try:
        # Démonstration 1: Promotion
        promotion = demo_promotion_notification()
        
        # Démonstration 2: Nouveau produit
        product = demo_product_notification()
        
        # Afficher les statistiques
        show_statistics()
        
        print("\n🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS !")
        print("=" * 60)
        print("✅ Le système de newsletter fonctionne parfaitement")
        print("✅ Les emails automatiques sont envoyés lors de la publication")
        print("✅ Les abonnés reçoivent les notifications selon leurs préférences")
        print("✅ Les templates HTML et texte sont générés correctement")
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DE LA DÉMONSTRATION:")
        print(f"   {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
