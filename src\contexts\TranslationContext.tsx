import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Types pour le système de traduction
export interface Language {
  code: string;
  name: string;
  is_default: boolean;
}

export interface TranslationContextType {
  currentLanguage: string;
  availableLanguages: Language[];
  translations: Record<string, string>;
  isLoading: boolean;
  error: string | null;
  
  // Méthodes
  changeLanguage: (languageCode: string) => Promise<void>;
  translate: (key: string, fallback?: string, params?: Record<string, any>) => string;
  t: (key: string, fallback?: string, params?: Record<string, any>) => string; // Alias court
  loadTranslations: (languageCode?: string) => Promise<void>;
  reportMissingTranslation: (key: string) => void;
}

// Contexte de traduction
const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';
const DEFAULT_LANGUAGE = 'fr';
const STORAGE_KEY = 'josnet_language_preference';

// Provider de traduction
interface TranslationProviderProps {
  children: ReactNode;
}

export const TranslationProvider: React.FC<TranslationProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<string>(DEFAULT_LANGUAGE);
  const [availableLanguages, setAvailableLanguages] = useState<Language[]>([]);
  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [missingKeys, setMissingKeys] = useState<Set<string>>(new Set());

  // Charger les langues disponibles
  const loadAvailableLanguages = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/translations/languages/`);
      if (response.ok) {
        const data = await response.json();
        setAvailableLanguages(data.languages);
        return data.languages;
      }
    } catch (error) {
      console.error('Erreur lors du chargement des langues:', error);
    }
    
    // Fallback si l'API n'est pas disponible
    return [
      { code: 'fr', name: 'Français', is_default: true },
      { code: 'en', name: 'English', is_default: false },
      { code: 'sw', name: 'Kiswahili', is_default: false },
      { code: 'rn', name: 'Kirundi', is_default: false },
    ];
  };

  // Charger les traductions pour une langue
  const loadTranslations = async (languageCode?: string) => {
    const lang = languageCode || currentLanguage;
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/translations/translations/?lang=${lang}`);
      
      if (response.ok) {
        const data = await response.json();
        setTranslations(data.translations || {});
        console.log(`Traductions chargées pour ${lang}:`, Object.keys(data.translations || {}).length, 'clés');
      } else {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des traductions:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
      
      // Fallback vers les traductions par défaut
      setTranslations(getDefaultTranslations(lang));
    } finally {
      setIsLoading(false);
    }
  };

  // Traductions par défaut (fallback)
  const getDefaultTranslations = (lang: string): Record<string, string> => {
    const defaults: Record<string, Record<string, string>> = {
      fr: {
        'common.loading': 'Chargement...',
        'common.error': 'Erreur',
        'common.success': 'Succès',
        'common.cancel': 'Annuler',
        'common.save': 'Enregistrer',
        'common.delete': 'Supprimer',
        'common.edit': 'Modifier',
        'common.view': 'Voir',
        'common.back': 'Retour',
        'common.next': 'Suivant',
        'common.previous': 'Précédent',
        'common.search': 'Rechercher',
        'common.filter': 'Filtrer',
        'common.sort': 'Trier',
        'common.actions': 'Actions',
        'common.status': 'Statut',
        'common.date': 'Date',
        'common.name': 'Nom',
        'common.description': 'Description',
        'common.price': 'Prix',
        'common.quantity': 'Quantité',
        'common.total': 'Total',
      },
      en: {
        'common.loading': 'Loading...',
        'common.error': 'Error',
        'common.success': 'Success',
        'common.cancel': 'Cancel',
        'common.save': 'Save',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.view': 'View',
        'common.back': 'Back',
        'common.next': 'Next',
        'common.previous': 'Previous',
        'common.search': 'Search',
        'common.filter': 'Filter',
        'common.sort': 'Sort',
        'common.actions': 'Actions',
        'common.status': 'Status',
        'common.date': 'Date',
        'common.name': 'Name',
        'common.description': 'Description',
        'common.price': 'Price',
        'common.quantity': 'Quantity',
        'common.total': 'Total',
      }
    };

    return defaults[lang] || defaults.fr;
  };

  // Changer de langue
  const changeLanguage = async (languageCode: string) => {
    if (languageCode === currentLanguage) return;

    setCurrentLanguage(languageCode);
    localStorage.setItem(STORAGE_KEY, languageCode);
    
    await loadTranslations(languageCode);

    // Sauvegarder la préférence utilisateur si connecté
    try {
      const token = localStorage.getItem('access_token');
      if (token) {
        await fetch(`${API_BASE_URL}/translations/user/language/set/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({ language_code: languageCode }),
        });
      }
    } catch (error) {
      console.warn('Impossible de sauvegarder la préférence de langue:', error);
    }
  };

  // Fonction de traduction principale
  const translate = (key: string, fallback?: string, params?: Record<string, any>): string => {
    let translation = translations[key] || fallback || key;

    // Remplacer les paramètres dans la traduction
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        const placeholder = `{${paramKey}}`;
        translation = translation.replace(new RegExp(placeholder, 'g'), String(paramValue));
      });
    }

    // Signaler les traductions manquantes
    if (!translations[key] && !fallback) {
      reportMissingTranslation(key);
    }

    return translation;
  };

  // Alias court pour la fonction de traduction
  const t = translate;

  // Signaler une traduction manquante
  const reportMissingTranslation = (key: string) => {
    if (!missingKeys.has(key)) {
      setMissingKeys(prev => new Set([...prev, key]));
      console.warn(`Traduction manquante: ${key} (${currentLanguage})`);
    }
  };

  // Envoyer les traductions manquantes au backend (debounced)
  useEffect(() => {
    if (missingKeys.size === 0) return;

    const timer = setTimeout(async () => {
      try {
        await fetch(`${API_BASE_URL}/translations/detect-missing/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            keys: Array.from(missingKeys),
            language: currentLanguage,
          }),
        });
        
        console.log(`${missingKeys.size} traductions manquantes signalées au backend`);
        setMissingKeys(new Set());
      } catch (error) {
        console.error('Erreur lors du signalement des traductions manquantes:', error);
      }
    }, 5000); // Attendre 5 secondes avant d'envoyer

    return () => clearTimeout(timer);
  }, [missingKeys, currentLanguage]);

  // Initialisation
  useEffect(() => {
    const initializeTranslations = async () => {
      // Charger les langues disponibles
      const languages = await loadAvailableLanguages();
      
      // Déterminer la langue à utiliser
      let initialLanguage = DEFAULT_LANGUAGE;
      
      // 1. Préférence stockée localement
      const storedLanguage = localStorage.getItem(STORAGE_KEY);
      if (storedLanguage && languages.some(lang => lang.code === storedLanguage)) {
        initialLanguage = storedLanguage;
      }
      
      // 2. Préférence utilisateur depuis l'API (si connecté)
      try {
        const token = localStorage.getItem('access_token');
        if (token) {
          const response = await fetch(`${API_BASE_URL}/translations/user/language/`, {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });
          
          if (response.ok) {
            const data = await response.json();
            if (data.language_code && languages.some(lang => lang.code === data.language_code)) {
              initialLanguage = data.language_code;
            }
          }
        }
      } catch (error) {
        console.warn('Impossible de récupérer la préférence de langue utilisateur:', error);
      }
      
      // 3. Langue du navigateur
      if (!storedLanguage) {
        const browserLanguage = navigator.language.split('-')[0];
        if (languages.some(lang => lang.code === browserLanguage)) {
          initialLanguage = browserLanguage;
        }
      }
      
      setCurrentLanguage(initialLanguage);
      await loadTranslations(initialLanguage);
    };

    initializeTranslations();
  }, []);

  const value: TranslationContextType = {
    currentLanguage,
    availableLanguages,
    translations,
    isLoading,
    error,
    changeLanguage,
    translate,
    t,
    loadTranslations,
    reportMissingTranslation,
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
};

// Hook pour utiliser le contexte de traduction
export const useTranslation = (): TranslationContextType => {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
};
