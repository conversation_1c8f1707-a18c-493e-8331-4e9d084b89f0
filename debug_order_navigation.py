#!/usr/bin/env python3
"""
Script pour diagnostiquer le problème de navigation vers les commandes depuis les retours
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_order_navigation_issue():
    """Tester le problème de navigation vers les commandes"""
    print("🔍 DIAGNOSTIC: PROBLÈME DE NAVIGATION VERS LES COMMANDES")
    print("=" * 70)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data, timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            print("✅ Authentification réussie")
            
            # 1. Récupérer un retour pour analyser
            print(f"\n📋 1. RÉCUPÉRATION D'UN RETOUR:")
            returns_response = requests.get(f"{API_BASE_URL}/orders/returns/", headers=headers, timeout=10)
            
            if returns_response.status_code == 200:
                returns_data = returns_response.json()
                returns = returns_data.get('results', [])
                
                if returns:
                    return_item = returns[0]
                    return_id = return_item['id']
                    return_number = return_item['return_number']
                    
                    print(f"   ✅ Retour trouvé: {return_number} (ID: {return_id})")
                    
                    # 2. Récupérer les détails du retour
                    print(f"\n📋 2. DÉTAILS DU RETOUR:")
                    return_detail_response = requests.get(f"{API_BASE_URL}/account/returns/{return_id}/", headers=headers, timeout=10)
                    
                    if return_detail_response.status_code == 200:
                        return_detail = return_detail_response.json()
                        
                        # Analyser les champs liés à la commande
                        order_id = return_detail.get('order')
                        order_details = return_detail.get('order_details', {})
                        
                        print(f"   ✅ Détails récupérés")
                        print(f"   📊 Champ 'order': {order_id} (type: {type(order_id)})")
                        print(f"   📊 Champ 'order_details': {bool(order_details)}")
                        
                        if order_details:
                            print(f"      ID commande: {order_details.get('id')}")
                            print(f"      Numéro commande: {order_details.get('order_number')}")
                            print(f"      Clés disponibles: {list(order_details.keys())}")
                        
                        # 3. Tester l'accès à la commande avec différents IDs
                        print(f"\n📋 3. TEST D'ACCÈS À LA COMMANDE:")
                        
                        # Test avec l'ID direct
                        if order_id:
                            print(f"   🧪 Test avec order_id: {order_id}")
                            order_response = requests.get(f"{API_BASE_URL}/orders/orders/{order_id}/", headers=headers, timeout=10)
                            print(f"      Statut: {order_response.status_code}")
                            
                            if order_response.status_code == 200:
                                print(f"      ✅ Commande accessible avec ID: {order_id}")
                                order_data = order_response.json()
                                print(f"      📝 Numéro: {order_data.get('order_number')}")
                                return {
                                    'return_id': return_id,
                                    'order_id': order_id,
                                    'order_accessible': True,
                                    'order_number': order_data.get('order_number')
                                }
                            elif order_response.status_code == 404:
                                print(f"      ❌ Commande non trouvée avec ID: {order_id}")
                            elif order_response.status_code == 403:
                                print(f"      ❌ Accès refusé à la commande ID: {order_id}")
                            else:
                                print(f"      ❌ Erreur {order_response.status_code}")
                        
                        # Test avec l'ID des détails
                        if order_details and order_details.get('id'):
                            detail_order_id = order_details.get('id')
                            print(f"   🧪 Test avec order_details.id: {detail_order_id}")
                            order_response2 = requests.get(f"{API_BASE_URL}/orders/orders/{detail_order_id}/", headers=headers, timeout=10)
                            print(f"      Statut: {order_response2.status_code}")
                            
                            if order_response2.status_code == 200:
                                print(f"      ✅ Commande accessible avec ID détails: {detail_order_id}")
                                return {
                                    'return_id': return_id,
                                    'order_id': detail_order_id,
                                    'order_accessible': True,
                                    'order_number': order_details.get('order_number')
                                }
                        
                        # 4. Vérifier les permissions utilisateur
                        print(f"\n📋 4. VÉRIFICATION DES PERMISSIONS:")
                        user_orders_response = requests.get(f"{API_BASE_URL}/orders/orders/", headers=headers, timeout=10)
                        
                        if user_orders_response.status_code == 200:
                            user_orders_data = user_orders_response.json()
                            user_orders = user_orders_data.get('results', [])
                            
                            print(f"   📊 Commandes accessibles à l'utilisateur: {len(user_orders)}")
                            
                            # Chercher la commande dans la liste
                            target_order = None
                            for order in user_orders:
                                if order.get('id') == order_id or order.get('id') == order_details.get('id'):
                                    target_order = order
                                    break
                            
                            if target_order:
                                print(f"   ✅ Commande trouvée dans la liste utilisateur")
                                print(f"      ID: {target_order.get('id')}")
                                print(f"      Numéro: {target_order.get('order_number')}")
                            else:
                                print(f"   ❌ Commande NON trouvée dans la liste utilisateur")
                                print(f"   🔍 IDs recherchés: {order_id}, {order_details.get('id') if order_details else 'N/A'}")
                                print(f"   📋 IDs disponibles: {[o.get('id') for o in user_orders[:5]]}")
                        
                        return {
                            'return_id': return_id,
                            'order_id': order_id,
                            'order_accessible': False,
                            'permission_issue': True
                        }
                    else:
                        print(f"   ❌ Erreur détails retour: {return_detail_response.status_code}")
                        return None
                else:
                    print(f"   ⚠️ Aucun retour trouvé")
                    return None
            else:
                print(f"   ❌ Erreur liste retours: {returns_response.status_code}")
                return None
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ SERVEUR NON ACCESSIBLE")
        return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def suggest_solutions():
    """Suggérer des solutions"""
    print(f"\n💡 SOLUTIONS POSSIBLES:")
    print("=" * 50)
    
    print(f"🔧 SOLUTION 1: Problème de mapping ID")
    print(f"   • Le backend retourne 'order' mais le frontend cherche 'orderId'")
    print(f"   • Corriger le mapping dans accountApi.ts")
    print(f"   • Vérifier que l'ID est au bon format")
    
    print(f"\n🔧 SOLUTION 2: Problème de permissions")
    print(f"   • L'utilisateur n'a pas accès à cette commande")
    print(f"   • Vérifier les permissions backend")
    print(f"   • Utiliser l'endpoint account au lieu de orders")
    
    print(f"\n🔧 SOLUTION 3: Problème de format d'ID")
    print(f"   • L'ID est peut-être un UUID au lieu d'un entier")
    print(f"   • Adapter la navigation pour gérer les deux formats")
    print(f"   • Utiliser l'order_number au lieu de l'ID")

def main():
    print("🧪 DIAGNOSTIC: NAVIGATION VERS LES COMMANDES DEPUIS LES RETOURS")
    print("=" * 80)
    
    # 1. Diagnostiquer le problème
    result = test_order_navigation_issue()
    
    # 2. Suggérer des solutions
    suggest_solutions()
    
    print(f"\n📊 RÉSUMÉ:")
    if result:
        print(f"   Retour testé: {result['return_id']}")
        print(f"   ID commande: {result['order_id']}")
        print(f"   Commande accessible: {'✅' if result['order_accessible'] else '❌'}")
        
        if result['order_accessible']:
            print(f"\n🎉 COMMANDE ACCESSIBLE!")
            print(f"   Le problème vient du frontend")
            print(f"   Vérifiez le mapping orderId dans accountApi.ts")
        elif result.get('permission_issue'):
            print(f"\n⚠️ PROBLÈME DE PERMISSIONS!")
            print(f"   L'utilisateur ne peut pas accéder à cette commande")
            print(f"   Utilisez l'endpoint /account/orders/ au lieu de /orders/orders/")
        else:
            print(f"\n❌ COMMANDE NON TROUVÉE!")
            print(f"   L'ID de commande est invalide ou la commande n'existe pas")
    else:
        print(f"   ❌ Impossible de diagnostiquer")
        print(f"   Vérifiez que le serveur Django fonctionne")

if __name__ == "__main__":
    main()
