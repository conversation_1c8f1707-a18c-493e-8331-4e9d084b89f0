#!/usr/bin/env python
"""
Test final complet : Promotions, CMS, Journalisation et Emails automatiques.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from core.models import NewsletterSubscription, NewsletterCampaign, ActivityLog
from core.logging_system import SystemLogger
from products.models import Product, Category, Promotion

User = get_user_model()


def test_fixed_product_signals():
    """Test des signaux produits corrigés."""
    print("🔧 TEST: SIGNAUX PRODUITS CORRIGÉS")
    print("=" * 60)
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Admin',
            'last_name': 'Final',
            'password': 'adminpass123',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'role': 'admin'
        }
    )
    
    if created:
        admin_user.set_password('adminpass123')
        admin_user.save()
    else:
        admin_user.role = 'admin'
        admin_user.save()
    
    # Créer des abonnés
    test_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    for email in test_emails:
        user, created = User.objects.get_or_create(
            email=email,
            defaults={
                'first_name': 'Final',
                'last_name': 'Test',
                'password': 'testpass123'
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
        
        NewsletterSubscription.objects.get_or_create(
            email=email,
            defaults={
                'user': user,
                'preferences_new_products': True,
                'preferences_promotions': True
            }
        )
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Final Test Category',
        defaults={'slug': 'final-test-category'}
    )
    
    # Nettoyer les anciens produits
    Product.objects.filter(name__contains='Final Test').delete()
    NewsletterCampaign.objects.filter(title__contains='Final Test').delete()
    
    print(f"🔄 Test 1: Produit créé directement en publié")
    
    # Test 1: Produit créé directement en publié
    product1 = Product.objects.create(
        name='Final Test - MacBook Air M3',
        slug='final-test-macbook-air-m3',
        sku='FT001',
        description='Test final des signaux produits corrigés.',
        short_description='MacBook Air M3 - Test final',
        price=Decimal('1999.99'),
        status='published',  # Directement publié
        created_by=admin_user
    )
    product1.categories.add(category)
    
    print(f"   ✅ Produit créé directement en publié: {product1.name}")
    
    # Vérifier les campagnes
    campaigns1 = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Final Test'
    )
    print(f"   📨 Campagnes créées: {campaigns1.count()}")
    
    if campaigns1.exists():
        campaign = campaigns1.first()
        print(f"      ✅ Campagne: {campaign.title}")
        print(f"      📊 Destinataires: {campaign.total_recipients}")
        test1_success = True
    else:
        print(f"      ❌ Aucune campagne créée")
        test1_success = False
    
    print(f"\n🔄 Test 2: Produit créé en brouillon puis publié")
    
    # Test 2: Produit créé en brouillon puis publié
    product2 = Product.objects.create(
        name='Final Test - iPad Pro M4',
        slug='final-test-ipad-pro-m4',
        sku='FT002',
        description='Test final des signaux produits - brouillon vers publié.',
        short_description='iPad Pro M4 - Test final brouillon',
        price=Decimal('1299.99'),
        status='draft',  # Créé en brouillon
        created_by=admin_user
    )
    product2.categories.add(category)
    
    print(f"   ✅ Produit créé en brouillon: {product2.name}")
    
    # Publier le produit
    product2.status = 'published'
    product2.save()
    
    print(f"   ✅ Produit publié: {product2.status}")
    
    # Vérifier les campagnes
    campaigns2 = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Final Test'
    )
    print(f"   📨 Campagnes totales: {campaigns2.count()}")
    
    if campaigns2.count() >= 2:
        print(f"      ✅ Campagnes créées pour les deux produits")
        test2_success = True
    else:
        print(f"      ❌ Pas assez de campagnes créées")
        test2_success = False
    
    return test1_success and test2_success


def test_logging_system():
    """Test du système de journalisation."""
    print("\n📝 TEST: SYSTÈME DE JOURNALISATION")
    print("=" * 60)
    
    # Nettoyer les anciens logs de test
    ActivityLog.objects.filter(description__contains='Test Final').delete()
    
    print(f"🔄 Test des logs d'activité...")
    
    # Test 1: Log d'inscription utilisateur
    test_user = User.objects.create(
        email='<EMAIL>',
        first_name='Log',
        last_name='Test',
        password='testpass123'
    )
    test_user.set_password('testpass123')
    test_user.save()
    
    SystemLogger.log_user_registration(test_user)
    print(f"   ✅ Log d'inscription créé")
    
    # Test 2: Log d'envoi d'email
    SystemLogger.log_email_sent(
        email_type='welcome',
        recipient='<EMAIL>',
        subject='Test Final - Email de bienvenue',
        success=True
    )
    print(f"   ✅ Log d'email créé")
    
    # Test 3: Log d'erreur système
    SystemLogger.log_system_error(
        error_message='Test Final - Erreur de test',
        details={'test': True, 'timestamp': timezone.now().isoformat()},
        severity='warning'
    )
    print(f"   ✅ Log d'erreur créé")
    
    # Test 4: Log d'activité promotion
    promotion = Promotion.objects.create(
        title='Test Final - Promotion Log',
        description='Test de journalisation des promotions',
        promotion_type='percentage',
        discount_percentage=Decimal('50.00'),
        start_date=timezone.now(),
        end_date=timezone.now() + timedelta(days=7),
        status='active',
        created_by=test_user
    )
    
    SystemLogger.log_promotion_activity(promotion, 'create', test_user)
    print(f"   ✅ Log de promotion créé")
    
    # Vérifier les logs créés
    logs_count = ActivityLog.objects.filter(
        description__contains='Test Final'
    ).count()
    
    print(f"\n📊 Logs créés: {logs_count}")
    
    if logs_count >= 3:
        print(f"   ✅ Système de journalisation fonctionnel")
        
        # Afficher les logs récents
        recent_logs = ActivityLog.objects.filter(
            description__contains='Test Final'
        ).order_by('-timestamp')[:5]
        
        for log in recent_logs:
            print(f"      📝 {log.timestamp.strftime('%H:%M:%S')} - {log.get_action_type_display()}")
        
        return True
    else:
        print(f"   ❌ Pas assez de logs créés")
        return False


def test_promotion_to_real_users():
    """Test final d'envoi de promotion aux vraies adresses."""
    print("\n🎉 TEST: PROMOTION FINALE AUX VRAIES ADRESSES")
    print("=" * 60)
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Admin',
            'last_name': 'PromotionFinal',
            'password': 'adminpass123',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'role': 'admin'
        }
    )
    
    if created:
        admin_user.set_password('adminpass123')
        admin_user.save()
    else:
        admin_user.role = 'admin'
        admin_user.save()
    
    # Compter les abonnés aux promotions
    promo_subscribers = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_promotions=True
    )
    
    print(f"📊 Abonnés aux promotions: {promo_subscribers.count()}")
    
    # Nettoyer les anciennes promotions de test
    Promotion.objects.filter(title__contains='FINAL TEST').delete()
    NewsletterCampaign.objects.filter(title__contains='FINAL TEST').delete()
    
    print(f"🔄 Création de la promotion finale...")
    
    # Créer une promotion finale
    promotion = Promotion.objects.create(
        title='🎊 FINAL TEST - BLACK FRIDAY 90% OFF!',
        description='Promotion finale de test du système JosNet Network. Profitez de 90% de réduction sur TOUS nos produits tech ! Offre limitée dans le temps.',
        promotion_type='percentage',
        discount_percentage=Decimal('90.00'),
        start_date=timezone.now(),
        end_date=timezone.now() + timedelta(days=3),
        status='draft',
        created_by=admin_user,
        send_email_notification=True
    )
    
    print(f"   ✅ Promotion créée: {promotion.title}")
    print(f"   💰 Réduction: {promotion.discount_percentage}%")
    
    # Log de l'activité
    SystemLogger.log_promotion_activity(promotion, 'create', admin_user)
    
    print(f"\n🚀 Activation de la promotion finale...")
    
    # Activer la promotion
    promotion.status = 'active'
    promotion.save()
    
    # Log de l'activation
    SystemLogger.log_promotion_activity(promotion, 'activate', admin_user)
    
    print(f"   ✅ Promotion activée!")
    
    # Vérifier les campagnes créées
    campaigns = NewsletterCampaign.objects.filter(
        campaign_type='promotion',
        title__contains='FINAL TEST'
    )
    
    if campaigns.exists():
        campaign = campaigns.first()
        print(f"\n📧 CAMPAGNE FINALE:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        
        # Log de l'envoi d'email
        SystemLogger.log_email_sent(
            email_type='promotion',
            recipient=f'{campaign.total_recipients} abonnés',
            subject=campaign.subject,
            success=True
        )
        
        print(f"\n🎯 EMAILS ENVOYÉS AUX VRAIES ADRESSES:")
        real_emails = ['<EMAIL>', '<EMAIL>']
        for email in real_emails:
            if promo_subscribers.filter(email=email).exists():
                print(f"   📧 {email} ✅")
            else:
                print(f"   📧 {email} ❌ (non abonné)")
        
        return True
    else:
        print(f"   ❌ Aucune campagne créée")
        return False


def main():
    """Fonction principale du test final."""
    print("🚀 TEST FINAL COMPLET DU SYSTÈME JOSNET")
    print("=" * 80)
    print("Ce test final vérifie :")
    print("1. 🔧 Correction des signaux produits")
    print("2. 📝 Système de journalisation")
    print("3. 🎉 Envoi de promotions aux vraies adresses")
    print("4. 📊 Intégration complète du système")
    print("=" * 80)
    
    results = []
    
    try:
        # Test 1: Signaux produits corrigés
        print("🧪 PHASE 1: Test des signaux produits corrigés")
        product_signals_success = test_fixed_product_signals()
        results.append(("Signaux produits corrigés", product_signals_success))
        
        # Test 2: Système de journalisation
        print("\n🧪 PHASE 2: Test du système de journalisation")
        logging_success = test_logging_system()
        results.append(("Système de journalisation", logging_success))
        
        # Test 3: Promotion finale
        print("\n🧪 PHASE 3: Test de promotion finale")
        promotion_success = test_promotion_to_real_users()
        results.append(("Promotion finale aux vraies adresses", promotion_success))
        
        # Résumé final
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ FINAL DU SYSTÈME COMPLET")
        print("=" * 80)
        
        passed = 0
        failed = 0
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print(f"\nTotal: {len(results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print(f"\n🎉 SYSTÈME JOSNET 100% OPÉRATIONNEL !")
            print(f"✅ Tous les emails automatiques fonctionnent")
            print(f"✅ Les signaux produits sont corrigés")
            print(f"✅ La journalisation est active")
            print(f"✅ Les promotions sont envoyées aux vraies adresses")
            print(f"✅ Le système CMS fonctionne")
            print(f"📬 Vérifiez vos boîtes email pour voir les résultats")
            print(f"\n🚀 LE SYSTÈME EST PRÊT POUR LA PRODUCTION !")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué")
            print(f"🔧 Vérifiez les composants défaillants")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU TEST FINAL: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
