#!/usr/bin/env python3
"""
Script pour tester la synchronisation automatique du profil avec l'adresse par défaut
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access')
    return None

def get_user_profile(token):
    """Récupérer le profil utilisateur"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{API_BASE_URL}/auth/profile/", headers=headers)
    if response.status_code == 200:
        return response.json()
    return None

def create_test_address(token, is_default=False):
    """Créer une adresse de test"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    address_data = {
        "address_type": "both",
        "is_default": is_default,
        "first_name": "Sync",
        "last_name": "Test",
        "address_line1": "123 Avenue de la Synchronisation",
        "city": "Gitega",
        "postal_code": "4000",
        "country": "Burundi",
        "phone": "+257 11 22 33 44"
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/addresses/", json=address_data, headers=headers)
    if response.status_code == 201:
        return response.json()
    return None

def set_address_as_default(token, address_id):
    """Définir une adresse comme par défaut"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/addresses/{address_id}/set_default/", headers=headers)
    return response.status_code == 200

def test_profile_sync():
    """Tester la synchronisation du profil avec l'adresse par défaut"""
    print("🔄 Test de synchronisation profil-adresse")
    print("=" * 50)
    
    # 1. Authentification
    print("🔐 Authentification...")
    token = get_auth_token()
    if not token:
        print("❌ Échec de l'authentification")
        return False
    print("✅ Authentification réussie")
    
    # 2. Récupérer le profil initial
    print("\n👤 Profil initial...")
    initial_profile = get_user_profile(token)
    if not initial_profile:
        print("❌ Impossible de récupérer le profil")
        return False
    
    print(f"   Nom: {initial_profile.get('first_name')} {initial_profile.get('last_name')}")
    print(f"   Téléphone: {initial_profile.get('phone_number', 'N/A')}")
    
    # 3. Créer une nouvelle adresse (non par défaut)
    print("\n➕ Création d'une nouvelle adresse...")
    new_address = create_test_address(token, is_default=False)
    if not new_address:
        print("❌ Échec de la création d'adresse")
        return False
    
    print(f"✅ Adresse créée (ID: {new_address['id']})")
    print(f"   Nom: {new_address['first_name']} {new_address['last_name']}")
    print(f"   Téléphone: {new_address.get('phone', 'N/A')}")
    
    # 4. Vérifier que le profil n'a pas changé
    print("\n🔍 Vérification du profil après création...")
    profile_after_create = get_user_profile(token)
    
    if (profile_after_create['first_name'] == initial_profile['first_name'] and 
        profile_after_create['last_name'] == initial_profile['last_name']):
        print("✅ Le profil n'a pas changé (normal car adresse non par défaut)")
    else:
        print("⚠️ Le profil a changé de manière inattendue")
    
    # 5. Définir l'adresse comme par défaut
    print(f"\n⭐ Définition de l'adresse {new_address['id']} comme par défaut...")
    success = set_address_as_default(token, new_address['id'])
    
    if success:
        print("✅ Adresse définie comme par défaut")
    else:
        print("❌ Échec de la définition par défaut")
        return False
    
    # 6. Vérifier la synchronisation du profil
    print("\n🔄 Vérification de la synchronisation du profil...")
    final_profile = get_user_profile(token)
    
    print(f"   Profil avant: {initial_profile.get('first_name')} {initial_profile.get('last_name')}")
    print(f"   Profil après: {final_profile.get('first_name')} {final_profile.get('last_name')}")
    print(f"   Adresse: {new_address['first_name']} {new_address['last_name']}")
    
    # Vérifier si la synchronisation a fonctionné
    sync_success = (
        final_profile['first_name'] == new_address['first_name'] and
        final_profile['last_name'] == new_address['last_name']
    )
    
    if sync_success:
        print("✅ SYNCHRONISATION RÉUSSIE!")
        print("   Le profil a été mis à jour avec les informations de l'adresse par défaut")
    else:
        print("❌ SYNCHRONISATION ÉCHOUÉE")
        print("   Le profil n'a pas été mis à jour")
    
    # 7. Vérifier la synchronisation du téléphone
    if new_address.get('phone') and final_profile.get('phone_number'):
        if final_profile['phone_number'] == new_address['phone']:
            print("✅ Téléphone synchronisé")
        else:
            print("⚠️ Téléphone non synchronisé")
    
    return sync_success

if __name__ == "__main__":
    print("🧪 TEST DE SYNCHRONISATION PROFIL-ADRESSE")
    print("=" * 60)
    
    success = test_profile_sync()
    
    print(f"\n📊 RÉSULTAT FINAL:")
    if success:
        print("🎉 TEST RÉUSSI!")
        print("   La synchronisation automatique fonctionne correctement.")
        print("   Quand un utilisateur définit une adresse par défaut,")
        print("   son profil est automatiquement mis à jour.")
    else:
        print("❌ TEST ÉCHOUÉ")
        print("   La synchronisation automatique ne fonctionne pas.")
        print("   Vérifiez l'implémentation dans le frontend.")
