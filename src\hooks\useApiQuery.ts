import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import { withErrorHandling, ApiErrorHandlerOptions } from '@/services/apiErrorHandler';

/**
 * Hook personnalisé pour les requêtes API avec gestion d'erreur améliorée
 */

export interface UseApiQueryOptions<T> extends Omit<UseQueryOptions<T>, 'queryFn'> {
  queryFn: () => Promise<T>;
  fallbackData?: T;
  errorHandling?: ApiErrorHandlerOptions;
  context?: string;
}

export function useApiQuery<T>(
  options: UseApiQueryOptions<T>
): UseQueryResult<T> {
  const {
    queryFn,
    fallbackData,
    errorHandling = {},
    context,
    ...queryOptions
  } = options;

  return useQuery({
    ...queryOptions,
    queryFn: async () => {
      if (fallbackData !== undefined) {
        return withErrorHandling(queryFn, {
          fallbackData,
          context: context || String(queryOptions.queryKey),
          showToast: false, // Pas de toast pour les requêtes avec fallback
          ...errorHandling
        });
      } else {
        return queryFn();
      }
    },
    retry: (failureCount, error: any) => {
      // Ne pas retry sur les erreurs d'authentification
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      
      // Retry jusqu'à 2 fois pour les autres erreurs
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook spécialisé pour les données de compte utilisateur
 */
export function useAccountQuery<T>(
  queryKey: any[],
  queryFn: () => Promise<T>,
  fallbackData: T,
  options: Partial<UseApiQueryOptions<T>> = {}
) {
  return useApiQuery({
    queryKey,
    queryFn,
    fallbackData,
    context: `Account - ${queryKey.join('/')}`,
    errorHandling: {
      showToast: false, // Pas de toast pour les données de compte
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    ...options
  });
}

/**
 * Hook spécialisé pour les données de produits
 */
export function useProductQuery<T>(
  queryKey: any[],
  queryFn: () => Promise<T>,
  fallbackData: T,
  options: Partial<UseApiQueryOptions<T>> = {}
) {
  return useApiQuery({
    queryKey,
    queryFn,
    fallbackData,
    context: `Products - ${queryKey.join('/')}`,
    errorHandling: {
      showToast: false,
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    ...options
  });
}

/**
 * Hook spécialisé pour les notifications
 */
export function useNotificationQuery<T>(
  queryKey: any[],
  queryFn: () => Promise<T>,
  fallbackData: T,
  options: Partial<UseApiQueryOptions<T>> = {}
) {
  return useApiQuery({
    queryKey,
    queryFn,
    fallbackData,
    context: `Notifications - ${queryKey.join('/')}`,
    errorHandling: {
      showToast: false,
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 2 * 60 * 1000, // Refetch toutes les 2 minutes
    ...options
  });
}

export default {
  useApiQuery,
  useAccountQuery,
  useProductQuery,
  useNotificationQuery
};
