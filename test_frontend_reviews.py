#!/usr/bin/env python3
"""
Script pour tester l'interface frontend des avis
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from products.models import Product, ProductReview
from orders.models import Order, OrderItem

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access')
    return None

def create_sample_reviews():
    """Créer des avis d'exemple pour tester l'interface"""
    print("📝 Création d'avis d'exemple...")

    try:
        # Récupérer le produit
        product = Product.objects.first()

        if not product:
            print("   ❌ Aucun produit trouvé")
            return False

        # Créer des utilisateurs de test pour les avis
        test_users = [
            {'email': '<EMAIL>', 'first_name': 'Marie', 'last_name': 'Dupont'},
            {'email': '<EMAIL>', 'first_name': 'Jean', 'last_name': 'Martin'},
            {'email': '<EMAIL>', 'first_name': 'Sophie', 'last_name': 'Bernard'},
            {'email': '<EMAIL>', 'first_name': 'Pierre', 'last_name': 'Durand'},
            {'email': '<EMAIL>', 'first_name': 'Claire', 'last_name': 'Moreau'},
        ]

        users = []
        for user_data in test_users:
            user, created = User.objects.get_or_create(
                email=user_data['email'],
                defaults={
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_active': True
                }
            )
            users.append(user)
            if created:
                print(f"   ✅ Utilisateur créé: {user.first_name} {user.last_name}")
        
        # Créer plusieurs avis avec des notes différentes
        sample_reviews = [
            {
                'rating': 5,
                'title': 'Excellent produit !',
                'comment': 'Je recommande vivement ce produit. La qualité est exceptionnelle et la livraison a été très rapide.',
                'pros': 'Qualité excellente, livraison rapide, bon rapport qualité-prix',
                'cons': 'Rien à redire'
            },
            {
                'rating': 4,
                'title': 'Très bon achat',
                'comment': 'Produit conforme à mes attentes. Quelques petits détails à améliorer mais globalement satisfait.',
                'pros': 'Bonne qualité, prix correct',
                'cons': 'Emballage pourrait être amélioré'
            },
            {
                'rating': 3,
                'title': 'Correct sans plus',
                'comment': 'Le produit fait le travail mais sans plus. Pour le prix, on peut s\'attendre à mieux.',
                'pros': 'Fonctionne comme prévu',
                'cons': 'Qualité moyenne, prix un peu élevé'
            },
            {
                'rating': 5,
                'title': 'Parfait !',
                'comment': 'Exactement ce que je cherchais. Service client au top et produit de qualité.',
                'pros': 'Qualité premium, service client excellent',
                'cons': 'Aucun'
            },
            {
                'rating': 2,
                'title': 'Décevant',
                'comment': 'Le produit ne correspond pas à la description. Qualité en dessous de mes attentes.',
                'pros': 'Livraison dans les temps',
                'cons': 'Qualité décevante, ne correspond pas à la description'
            }
        ]
        
        # Supprimer les anciens avis de test pour ce produit
        ProductReview.objects.filter(product=product, user__email__in=[u['email'] for u in test_users]).delete()

        # Créer les nouveaux avis avec différents utilisateurs
        for i, (user, review_data) in enumerate(zip(users, sample_reviews)):
            # Créer une commande pour les avis vérifiés (pour les 3 premiers)
            order = None
            if i < 3:
                order, created = Order.objects.get_or_create(
                    user=user,
                    status='delivered',
                    defaults={
                        'email': user.email,
                        'subtotal': 50000,
                        'total': 50000,
                        'currency': 'BIF'
                    }
                )

                if created:
                    OrderItem.objects.create(
                        order=order,
                        product=product,
                        quantity=1,
                        price=50000
                    )

            review = ProductReview.objects.create(
                user=user,
                product=product,
                order=order,
                is_approved=True,  # Approuver automatiquement pour les tests
                **review_data
            )
            print(f"   ✅ Avis créé: {review.title} ({review.rating}⭐) par {user.first_name}")
        
        print(f"   📊 Total: {len(sample_reviews)} avis créés pour {product.name}")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_review_stats_api(product_id):
    """Tester l'API des statistiques d'avis"""
    print(f"\n📊 Test de l'API des statistiques pour le produit {product_id}...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/stats/", 
            params={'product': product_id}
        )
        
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Statistiques récupérées")
            print(f"   📈 Total avis: {stats.get('total_reviews', 0)}")
            print(f"   ⭐ Note moyenne: {stats.get('average_rating', 0)}")
            print(f"   🛡️ Achats vérifiés: {stats.get('verified_purchases_count', 0)}")
            
            # Afficher la distribution des notes
            distribution = stats.get('rating_distribution', {})
            print(f"   📊 Distribution des notes:")
            for rating in [5, 4, 3, 2, 1]:
                count = distribution.get(str(rating), 0)
                print(f"      {rating}⭐: {count} avis")
            
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_review_list_api(product_id):
    """Tester l'API de liste des avis"""
    print(f"\n📋 Test de l'API de liste des avis pour le produit {product_id}...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/", 
            params={'product': product_id, 'page_size': 10}
        )
        
        if response.status_code == 200:
            data = response.json()
            reviews = data.get('results', [])
            print(f"   ✅ Avis récupérés: {len(reviews)} avis")
            
            for review in reviews[:3]:  # Afficher les 3 premiers
                print(f"      - {review.get('title')} ({review.get('rating')}⭐)")
                print(f"        Par: {review.get('user_name')}")
                print(f"        Vérifié: {'✅' if review.get('is_verified_purchase') else '❌'}")
                print(f"        Utile: {review.get('helpful_count', 0)} votes")
            
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🧪 TEST DE L'INTERFACE FRONTEND DES AVIS")
    print("=" * 50)
    
    # 1. Créer des avis d'exemple
    reviews_created = create_sample_reviews()
    
    if not reviews_created:
        print("❌ Impossible de créer les avis d'exemple")
        return
    
    # Récupérer le produit pour les tests
    product = Product.objects.first()
    if not product:
        print("❌ Aucun produit trouvé")
        return
    
    # 2. Tester les APIs
    stats_ok = test_review_stats_api(product.id)
    list_ok = test_review_list_api(product.id)
    
    print(f"\n📊 RÉSUMÉ DES TESTS:")
    print(f"   Création d'avis: {'✅' if reviews_created else '❌'}")
    print(f"   API statistiques: {'✅' if stats_ok else '❌'}")
    print(f"   API liste avis: {'✅' if list_ok else '❌'}")
    
    if all([reviews_created, stats_ok, list_ok]):
        print(f"\n🎉 INTERFACE FRONTEND PRÊTE!")
        print(f"   ✅ Les avis d'exemple sont créés")
        print(f"   ✅ Les APIs fonctionnent correctement")
        print(f"   🌐 Testez l'interface:")
        print(f"      • Page produit: http://localhost:8080/product/{product.id}")
        print(f"      • Onglet 'Avis' dans la page produit")
        print(f"      • Page avis dédiée: http://localhost:8080/product/{product.id}/reviews")
        
        print(f"\n💡 FONCTIONNALITÉS À TESTER:")
        print(f"   • Affichage des statistiques d'avis")
        print(f"   • Liste des avis avec pagination")
        print(f"   • Tri des avis (récents, mieux notés, plus utiles)")
        print(f"   • Votes d'utilité sur les avis")
        print(f"   • Création d'un nouvel avis")
        print(f"   • Distinction des achats vérifiés")
    else:
        print(f"\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
