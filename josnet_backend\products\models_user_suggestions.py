from django.db import models
from django.conf import settings
from django.utils import timezone


class ProductSuggestion(models.Model):
    """
    Modèle pour les suggestions de produits soumises par les utilisateurs.
    """
    
    STATUS_CHOICES = [
        ('pending', 'En attente'),
        ('approved', 'Approuvé'),
        ('rejected', 'Rejeté'),
        ('implemented', 'Implémenté'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Basse'),
        ('medium', 'Moyenne'),
        ('high', 'Haute'),
        ('urgent', 'Urgente'),
    ]
    
    # Informations de base
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='product_suggestions',
        verbose_name='Utilisateur'
    )
    name = models.CharField(
        max_length=255,
        verbose_name='Nom du produit suggéré'
    )
    category = models.CharField(
        max_length=100,
        verbose_name='Catégorie'
    )
    description = models.TextField(
        verbose_name='Description détaillée'
    )
    estimated_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='Prix estimé (BIF)'
    )
    reason = models.TextField(
        verbose_name='Raison de la suggestion'
    )
    
    # Statut et gestion
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='Statut'
    )
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name='Priorité'
    )
    
    # Réponse admin
    admin_response = models.TextField(
        blank=True,
        null=True,
        verbose_name='Réponse de l\'administrateur'
    )
    response_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='Date de réponse'
    )
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_suggestions',
        verbose_name='Examiné par'
    )
    
    # Informations supplémentaires
    votes_count = models.PositiveIntegerField(
        default=0,
        verbose_name='Nombre de votes'
    )
    implementation_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='Notes d\'implémentation'
    )
    estimated_implementation_time = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text='Temps estimé en jours',
        verbose_name='Temps d\'implémentation estimé (jours)'
    )
    
    # Métadonnées
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Date de création'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Date de modification'
    )
    
    class Meta:
        verbose_name = 'Suggestion de produit'
        verbose_name_plural = 'Suggestions de produits'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['category', 'status']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.user.email} ({self.get_status_display()})"
    
    def save(self, *args, **kwargs):
        # Mettre à jour la date de réponse si le statut change
        if self.pk:
            old_instance = ProductSuggestion.objects.get(pk=self.pk)
            if old_instance.status != self.status and self.status in ['approved', 'rejected']:
                if not self.response_date:
                    self.response_date = timezone.now()
        
        super().save(*args, **kwargs)
    
    @property
    def is_pending(self):
        return self.status == 'pending'
    
    @property
    def is_approved(self):
        return self.status == 'approved'
    
    @property
    def is_rejected(self):
        return self.status == 'rejected'
    
    @property
    def is_implemented(self):
        return self.status == 'implemented'
    
    @property
    def days_since_creation(self):
        return (timezone.now() - self.created_at).days
    
    @property
    def response_time_days(self):
        if self.response_date:
            return (self.response_date - self.created_at).days
        return None


class ProductSuggestionVote(models.Model):
    """
    Modèle pour les votes sur les suggestions de produits.
    """
    
    suggestion = models.ForeignKey(
        ProductSuggestion,
        on_delete=models.CASCADE,
        related_name='votes',
        verbose_name='Suggestion'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='suggestion_votes',
        verbose_name='Utilisateur'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Date de vote'
    )
    
    class Meta:
        verbose_name = 'Vote de suggestion'
        verbose_name_plural = 'Votes de suggestions'
        unique_together = ['suggestion', 'user']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Vote de {self.user.email} pour {self.suggestion.name}"


class ProductSuggestionComment(models.Model):
    """
    Modèle pour les commentaires sur les suggestions de produits.
    """
    
    suggestion = models.ForeignKey(
        ProductSuggestion,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='Suggestion'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='suggestion_comments',
        verbose_name='Utilisateur'
    )
    content = models.TextField(
        verbose_name='Contenu du commentaire'
    )
    is_admin_comment = models.BooleanField(
        default=False,
        verbose_name='Commentaire administrateur'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Date de création'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Date de modification'
    )
    
    class Meta:
        verbose_name = 'Commentaire de suggestion'
        verbose_name_plural = 'Commentaires de suggestions'
        ordering = ['created_at']
    
    def __str__(self):
        return f"Commentaire de {self.user.email} sur {self.suggestion.name}"


class ProductSuggestionAttachment(models.Model):
    """
    Modèle pour les pièces jointes des suggestions de produits.
    """
    
    suggestion = models.ForeignKey(
        ProductSuggestion,
        on_delete=models.CASCADE,
        related_name='attachments',
        verbose_name='Suggestion'
    )
    file = models.FileField(
        upload_to='suggestions/attachments/%Y/%m/',
        verbose_name='Fichier'
    )
    filename = models.CharField(
        max_length=255,
        verbose_name='Nom du fichier'
    )
    file_size = models.PositiveIntegerField(
        verbose_name='Taille du fichier (bytes)'
    )
    content_type = models.CharField(
        max_length=100,
        verbose_name='Type de contenu'
    )
    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='Téléchargé par'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Date de téléchargement'
    )
    
    class Meta:
        verbose_name = 'Pièce jointe de suggestion'
        verbose_name_plural = 'Pièces jointes de suggestions'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.filename} - {self.suggestion.name}"
    
    @property
    def file_size_mb(self):
        return round(self.file_size / (1024 * 1024), 2)
