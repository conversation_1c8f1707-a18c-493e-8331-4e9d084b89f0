import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ist, 
  TabsTrigger 
} from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON>hart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { 
  Heart, 
  ShoppingCart, 
  Eye, 
  Star, 
  MessageSquare, 
  AlertCircle, 
  Users, 
  UserPlus, 
  ArrowRight 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';
import { OptimizedImage } from '@/components/ui/optimized-image';

// Types
interface CustomerProductInsight {
  product_id: number;
  product_name: string;
  views_count: number;
  unique_viewers_count: number;
  add_to_cart_count: number;
  add_to_wishlist_count: number;
  purchase_count: number;
  conversion_rate: number;
  cart_abandonment_rate: number;
  average_rating: number;
  reviews_count: number;
  demographic_data: {
    age_groups: {
      label: string;
      value: number;
    }[];
    genders: {
      label: string;
      value: number;
    }[];
    locations: {
      label: string;
      value: number;
    }[];
  };
  customer_segments: {
    segment: string;
    count: number;
    conversion_rate: number;
  }[];
  similar_interests: {
    product_id: number;
    product_name: string;
    product_image?: string;
    interest_score: number;
  }[];
}

// Mock API service (replace with actual API calls)
const customerApi = {
  getCustomerProductInsights: async (productId: number): Promise<CustomerProductInsight> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Generate mock data
    return {
      product_id: productId,
      product_name: 'Product Name',
      views_count: 1250,
      unique_viewers_count: 875,
      add_to_cart_count: 320,
      add_to_wishlist_count: 180,
      purchase_count: 95,
      conversion_rate: 10.9, // percentage
      cart_abandonment_rate: 70.3, // percentage
      average_rating: 4.2,
      reviews_count: 48,
      demographic_data: {
        age_groups: [
          { label: '18-24', value: 15 },
          { label: '25-34', value: 35 },
          { label: '35-44', value: 25 },
          { label: '45-54', value: 15 },
          { label: '55+', value: 10 }
        ],
        genders: [
          { label: 'Homme', value: 45 },
          { label: 'Femme', value: 52 },
          { label: 'Autre', value: 3 }
        ],
        locations: [
          { label: 'Paris', value: 30 },
          { label: 'Lyon', value: 15 },
          { label: 'Marseille', value: 12 },
          { label: 'Bordeaux', value: 8 },
          { label: 'Autres', value: 35 }
        ]
      },
      customer_segments: [
        { segment: 'Nouveaux clients', count: 35, conversion_rate: 8.5 },
        { segment: 'Clients fidèles', count: 42, conversion_rate: 15.2 },
        { segment: 'Clients occasionnels', count: 18, conversion_rate: 7.8 }
      ],
      similar_interests: [
        { product_id: 101, product_name: 'Product A', interest_score: 85 },
        { product_id: 102, product_name: 'Product B', interest_score: 72 },
        { product_id: 103, product_name: 'Product C', interest_score: 68 },
        { product_id: 104, product_name: 'Product D', interest_score: 65 },
        { product_id: 105, product_name: 'Product E', interest_score: 60 }
      ]
    };
  },
  
  getCustomerFavoriteProducts: async (customerId: number) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock data
    return [
      { id: 1, name: 'Product 1', price: 29.99 },
      { id: 2, name: 'Product 2', price: 49.99 },
      { id: 3, name: 'Product 3', price: 19.99 }
    ];
  }
};

interface CustomerProductInsightsProps {
  productId: number;
}

const CustomerProductInsights: React.FC<CustomerProductInsightsProps> = ({ productId }) => {
  // Fetch customer insights
  const { 
    data: insights, 
    isLoading, 
    isError 
  } = useQuery({
    queryKey: ['customerProductInsights', productId],
    queryFn: () => customerApi.getCustomerProductInsights(productId),
  });
  
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Insights clients</CardTitle>
        <CardDescription>
          Analyse du comportement et des préférences des clients pour ce produit
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
            <Skeleton className="h-80 w-full" />
          </div>
        ) : isError ? (
          <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span>Une erreur est survenue lors du chargement des insights clients.</span>
          </div>
        ) : (
          <>
            {/* Key metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Vues</p>
                      <h3 className="text-2xl font-bold">{insights.views_count}</h3>
                    </div>
                    <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                      <Eye className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-gray-500">{insights.unique_viewers_count} visiteurs uniques</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Ajouts au panier</p>
                      <h3 className="text-2xl font-bold">{insights.add_to_cart_count}</h3>
                    </div>
                    <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center text-green-600">
                      <ShoppingCart className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-gray-500">Taux d'abandon: {insights.cart_abandonment_rate}%</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Favoris</p>
                      <h3 className="text-2xl font-bold">{insights.add_to_wishlist_count}</h3>
                    </div>
                    <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center text-red-600">
                      <Heart className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-gray-500">{(insights.add_to_wishlist_count / insights.views_count * 100).toFixed(1)}% des visiteurs</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Avis</p>
                      <h3 className="text-2xl font-bold">{insights.reviews_count}</h3>
                    </div>
                    <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center text-amber-600">
                      <MessageSquare className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                      <span className="ml-1">{insights.average_rating.toFixed(1)}/5</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Conversion funnel */}
            <div>
              <h3 className="text-lg font-medium mb-4">Entonnoir de conversion</h3>
              <div className="h-20">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    layout="vertical"
                    data={[
                      { name: 'Vues', value: insights.views_count },
                      { name: 'Ajouts au panier', value: insights.add_to_cart_count },
                      { name: 'Achats', value: insights.purchase_count }
                    ]}
                    margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip />
                    <Bar dataKey="value" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-sm font-medium text-gray-500">Taux de clic</p>
                  <p className="text-lg font-bold">
                    {(insights.add_to_cart_count / insights.views_count * 100).toFixed(1)}%
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Taux de conversion</p>
                  <p className="text-lg font-bold">
                    {insights.conversion_rate.toFixed(1)}%
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Taux d'abandon</p>
                  <p className="text-lg font-bold">
                    {insights.cart_abandonment_rate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
            
            {/* Demographics */}
            <Tabs defaultValue="age">
              <TabsList>
                <TabsTrigger value="age">Âge</TabsTrigger>
                <TabsTrigger value="gender">Genre</TabsTrigger>
                <TabsTrigger value="location">Localisation</TabsTrigger>
                <TabsTrigger value="segments">Segments</TabsTrigger>
              </TabsList>
              
              <TabsContent value="age" className="pt-4">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={insights.demographic_data.age_groups}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="label"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {insights.demographic_data.age_groups.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, 'Pourcentage']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
              
              <TabsContent value="gender" className="pt-4">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={insights.demographic_data.genders}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="label"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {insights.demographic_data.genders.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, 'Pourcentage']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
              
              <TabsContent value="location" className="pt-4">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={insights.demographic_data.locations}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="label" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value}%`, 'Pourcentage']} />
                      <Bar dataKey="value" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
              
              <TabsContent value="segments" className="pt-4">
                <div className="border rounded-md overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Segment</TableHead>
                        <TableHead className="text-right">Clients</TableHead>
                        <TableHead className="text-right">Taux de conversion</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {insights.customer_segments.map((segment) => (
                        <TableRow key={segment.segment}>
                          <TableCell>{segment.segment}</TableCell>
                          <TableCell className="text-right">{segment.count}</TableCell>
                          <TableCell className="text-right">{segment.conversion_rate.toFixed(1)}%</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
            
            {/* Similar interests */}
            <div>
              <h3 className="text-lg font-medium mb-4">Produits d'intérêt similaire</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {insights.similar_interests.map((product) => (
                  <Card key={product.product_id}>
                    <CardContent className="p-4">
                      <div className="aspect-square bg-gray-100 rounded-md mb-2 flex items-center justify-center">
                        {product.product_image ? (
                          <OptimizedImage
                            src={product.product_image}
                            alt={product.product_name}
                            width={100}
                            height={100}
                            objectFit="contain"
                          />
                        ) : (
                          <Package className="h-10 w-10 text-gray-400" />
                        )}
                      </div>
                      <h4 className="font-medium text-sm truncate" title={product.product_name}>
                        {product.product_name}
                      </h4>
                      <div className="mt-1 flex items-center justify-between">
                        <Badge variant="outline">
                          Score: {product.interest_score}%
                        </Badge>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
      
      <CardFooter className="border-t pt-4">
        <Button variant="outline" className="ml-auto">
          <Users className="mr-2 h-4 w-4" />
          Voir tous les insights clients
        </Button>
      </CardFooter>
    </Card>
  );
};

export default CustomerProductInsights;
