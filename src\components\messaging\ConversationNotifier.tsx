import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import messagingApi from '@/services/messagingApi';

interface ConversationNotifierProps {
  userId: number;
}

/**
 * Component that handles real-time notifications for conversations
 * This component doesn't render anything, it just subscribes to WebSocket events
 */
const ConversationNotifier = ({ userId }: ConversationNotifierProps) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const socketRef = useRef<{ close: () => void } | null>(null);

  useEffect(() => {
    // Subscribe to conversation updates
    socketRef.current = messagingApi.subscribeToConversationUpdates((data) => {
      // Handle different types of events
      switch (data.type) {
        case 'new_message':
          // Invalidate conversation queries to refresh the data
          queryClient.invalidateQueries({ queryKey: ['conversations'] });
          
          // If the message is for a specific conversation, invalidate that conversation's data
          if (data.conversation_id) {
            queryClient.invalidateQueries({ queryKey: ['conversation', data.conversation_id] });
          }
          
          // Show a notification if the message is not from the current user
          if (data.sender_id !== userId) {
            toast({
              title: 'Nouveau message',
              description: `${data.sender_name}: ${data.preview}`,
              duration: 5000,
            });
          }
          break;
          
        case 'status_update':
          // Invalidate conversation queries
          queryClient.invalidateQueries({ queryKey: ['conversations'] });
          
          if (data.conversation_id) {
            queryClient.invalidateQueries({ queryKey: ['conversation', data.conversation_id] });
          }
          
          // Show a notification for status updates
          toast({
            title: 'Statut mis à jour',
            description: `Conversation #${data.conversation_id}: ${data.status}`,
            duration: 3000,
          });
          break;
          
        case 'assignment':
          // Invalidate conversation queries
          queryClient.invalidateQueries({ queryKey: ['conversations'] });
          
          if (data.conversation_id) {
            queryClient.invalidateQueries({ queryKey: ['conversation', data.conversation_id] });
          }
          
          // Show a notification if the conversation was assigned to the current user
          if (data.assigned_to_id === userId) {
            toast({
              title: 'Conversation assignée',
              description: `La conversation #${data.conversation_id} vous a été assignée`,
              duration: 5000,
            });
          }
          break;
          
        default:
          // For any other event, just invalidate the queries
          queryClient.invalidateQueries({ queryKey: ['conversations'] });
          break;
      }
    });
    
    // Clean up the WebSocket connection when the component unmounts
    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, [queryClient, toast, userId]);
  
  // This component doesn't render anything
  return null;
};

export default ConversationNotifier;
