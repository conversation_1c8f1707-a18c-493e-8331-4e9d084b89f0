import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Save, 
  Clock, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Copy, 
  Share2, 
  BookmarkPlus, 
  Bookmark, 
  Filter, 
  X 
} from 'lucide-react';

// Types
interface FilterValue {
  field: string;
  operator: string;
  value: string | number | boolean | string[] | number[];
  displayValue?: string;
}

interface SavedFilter {
  id: string;
  name: string;
  description?: string;
  filters: FilterValue[];
  isDefault?: boolean;
  isShared?: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    id: number;
    name: string;
  };
}

interface SavedFiltersManagerProps {
  currentFilters?: FilterValue[];
  onFilterSelect: (filter: SavedFilter) => void;
  entityType?: string;
  showCreateButton?: boolean;
}

const SavedFiltersManager: React.FC<SavedFiltersManagerProps> = ({
  currentFilters = [],
  onFilterSelect,
  entityType = 'produits',
  showCreateButton = true,
}) => {
  // State
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<SavedFilter | null>(null);
  const [newFilterName, setNewFilterName] = useState('');
  const [newFilterDescription, setNewFilterDescription] = useState('');
  const [isDefault, setIsDefault] = useState(false);
  const [isShared, setIsShared] = useState(false);
  
  const { toast } = useToast();
  
  // Load saved filters from localStorage on mount
  useEffect(() => {
    const storedFilters = localStorage.getItem(`savedFilters_${entityType}`);
    if (storedFilters) {
      try {
        setSavedFilters(JSON.parse(storedFilters));
      } catch (error) {
        console.error('Failed to parse saved filters:', error);
      }
    } else {
      // Initialize with example filters if none exist
      const exampleFilters: SavedFilter[] = [
        {
          id: '1',
          name: 'Produits en promotion',
          filters: [
            { field: 'on_sale', operator: 'equals', value: true, displayValue: 'En promotion' }
          ],
          isDefault: false,
          isShared: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'Stock faible',
          description: 'Produits avec un stock inférieur à 10',
          filters: [
            { field: 'stock', operator: 'less_than', value: 10, displayValue: 'Stock < 10' }
          ],
          isDefault: true,
          isShared: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      ];
      setSavedFilters(exampleFilters);
      localStorage.setItem(`savedFilters_${entityType}`, JSON.stringify(exampleFilters));
    }
  }, [entityType]);
  
  // Save filters to localStorage when they change
  useEffect(() => {
    localStorage.setItem(`savedFilters_${entityType}`, JSON.stringify(savedFilters));
  }, [savedFilters, entityType]);
  
  // Create a new filter
  const createFilter = () => {
    if (!newFilterName.trim()) {
      toast({
        title: 'Nom requis',
        description: 'Veuillez donner un nom à votre filtre.',
        variant: 'destructive',
      });
      return;
    }
    
    if (currentFilters.length === 0) {
      toast({
        title: 'Aucun filtre actif',
        description: 'Veuillez appliquer au moins un filtre avant de sauvegarder.',
        variant: 'destructive',
      });
      return;
    }
    
    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name: newFilterName,
      description: newFilterDescription || undefined,
      filters: [...currentFilters],
      isDefault,
      isShared,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // If this is set as default, remove default from others
    let updatedFilters = [...savedFilters];
    if (isDefault) {
      updatedFilters = updatedFilters.map(filter => ({
        ...filter,
        isDefault: false
      }));
    }
    
    setSavedFilters([...updatedFilters, newFilter]);
    setIsCreateDialogOpen(false);
    resetForm();
    
    toast({
      title: 'Filtre sauvegardé',
      description: 'Votre filtre a été sauvegardé avec succès.',
    });
  };
  
  // Update an existing filter
  const updateFilter = () => {
    if (!selectedFilter) return;
    
    if (!newFilterName.trim()) {
      toast({
        title: 'Nom requis',
        description: 'Veuillez donner un nom à votre filtre.',
        variant: 'destructive',
      });
      return;
    }
    
    // If this is set as default, remove default from others
    let updatedFilters = savedFilters.map(filter => 
      filter.id === selectedFilter.id
        ? {
            ...filter,
            name: newFilterName,
            description: newFilterDescription || undefined,
            isDefault,
            isShared,
            updatedAt: new Date().toISOString(),
          }
        : isDefault ? { ...filter, isDefault: false } : filter
    );
    
    setSavedFilters(updatedFilters);
    setIsEditDialogOpen(false);
    resetForm();
    
    toast({
      title: 'Filtre mis à jour',
      description: 'Votre filtre a été mis à jour avec succès.',
    });
  };
  
  // Delete a filter
  const deleteFilter = () => {
    if (!selectedFilter) return;
    
    const updatedFilters = savedFilters.filter(filter => filter.id !== selectedFilter.id);
    setSavedFilters(updatedFilters);
    setIsDeleteDialogOpen(false);
    setSelectedFilter(null);
    
    toast({
      title: 'Filtre supprimé',
      description: 'Le filtre a été supprimé avec succès.',
    });
  };
  
  // Set a filter as default
  const setFilterAsDefault = (filter: SavedFilter) => {
    const updatedFilters = savedFilters.map(f => ({
      ...f,
      isDefault: f.id === filter.id
    }));
    
    setSavedFilters(updatedFilters);
    
    toast({
      title: 'Filtre par défaut',
      description: `"${filter.name}" est maintenant votre filtre par défaut.`,
    });
  };
  
  // Reset form fields
  const resetForm = () => {
    setNewFilterName('');
    setNewFilterDescription('');
    setIsDefault(false);
    setIsShared(false);
    setSelectedFilter(null);
  };
  
  // Open edit dialog
  const openEditDialog = (filter: SavedFilter) => {
    setSelectedFilter(filter);
    setNewFilterName(filter.name);
    setNewFilterDescription(filter.description || '');
    setIsDefault(!!filter.isDefault);
    setIsShared(!!filter.isShared);
    setIsEditDialogOpen(true);
  };
  
  // Open delete dialog
  const openDeleteDialog = (filter: SavedFilter) => {
    setSelectedFilter(filter);
    setIsDeleteDialogOpen(true);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };
  
  // Get filter count
  const getFilterCount = (filter: SavedFilter) => {
    return filter.filters.length;
  };
  
  // Get filter summary
  const getFilterSummary = (filter: SavedFilter) => {
    if (filter.filters.length === 0) return 'Aucun filtre';
    
    return filter.filters
      .slice(0, 2)
      .map(f => f.displayValue || `${f.field}: ${f.value}`)
      .join(', ') + 
      (filter.filters.length > 2 ? ` et ${filter.filters.length - 2} autre(s)` : '');
  };
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Filtres sauvegardés</CardTitle>
            <CardDescription>
              Accédez rapidement à vos filtres fréquemment utilisés
            </CardDescription>
          </div>
          {showCreateButton && (
            <Button 
              onClick={() => setIsCreateDialogOpen(true)}
              disabled={currentFilters.length === 0}
            >
              <BookmarkPlus className="mr-2 h-4 w-4" />
              Sauvegarder
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {savedFilters.length === 0 ? (
          <div className="text-center py-8 text-gray-500 border border-dashed rounded-md">
            <Bookmark className="h-12 w-12 mx-auto mb-2 text-gray-400" />
            <p>Aucun filtre sauvegardé.</p>
            <p className="text-sm">Sauvegardez vos filtres pour y accéder rapidement.</p>
          </div>
        ) : (
          <div className="space-y-2">
            {savedFilters.map(filter => (
              <div 
                key={filter.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
              >
                <div 
                  className="flex-1 cursor-pointer"
                  onClick={() => onFilterSelect(filter)}
                >
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{filter.name}</span>
                    {filter.isDefault && (
                      <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                        Par défaut
                      </Badge>
                    )}
                    {filter.isShared && (
                      <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                        Partagé
                      </Badge>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-500 mt-1">
                    <div className="flex items-center gap-1">
                      <Filter className="h-3 w-3" />
                      <span>{getFilterCount(filter)} filtre{getFilterCount(filter) !== 1 ? 's' : ''}</span>
                      <span className="mx-1">•</span>
                      <Clock className="h-3 w-3" />
                      <span>{formatDate(filter.updatedAt)}</span>
                    </div>
                    {filter.description && (
                      <div className="mt-1 text-xs">{filter.description}</div>
                    )}
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onFilterSelect(filter)}>
                      <Filter className="mr-2 h-4 w-4" />
                      <span>Appliquer</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => openEditDialog(filter)}>
                      <Edit className="mr-2 h-4 w-4" />
                      <span>Modifier</span>
                    </DropdownMenuItem>
                    {!filter.isDefault && (
                      <DropdownMenuItem onClick={() => setFilterAsDefault(filter)}>
                        <Bookmark className="mr-2 h-4 w-4" />
                        <span>Définir par défaut</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={() => {
                      // Copy filter configuration to clipboard
                      navigator.clipboard.writeText(JSON.stringify(filter.filters));
                      toast({
                        title: 'Copié',
                        description: 'Configuration du filtre copiée dans le presse-papier.',
                      });
                    }}>
                      <Copy className="mr-2 h-4 w-4" />
                      <span>Copier</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => openDeleteDialog(filter)}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      <span>Supprimer</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      
      {/* Create Filter Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sauvegarder le filtre</DialogTitle>
            <DialogDescription>
              Sauvegardez votre configuration de filtres actuelle pour y accéder rapidement plus tard.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="filter-name" className="text-sm font-medium">
                Nom du filtre
              </label>
              <Input
                id="filter-name"
                placeholder="Ex: Produits en promotion"
                value={newFilterName}
                onChange={(e) => setNewFilterName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="filter-description" className="text-sm font-medium">
                Description (optionnelle)
              </label>
              <Input
                id="filter-description"
                placeholder="Description du filtre"
                value={newFilterDescription}
                onChange={(e) => setNewFilterDescription(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Filtres actifs
              </label>
              <div className="p-2 bg-gray-50 rounded-md">
                {currentFilters.length === 0 ? (
                  <div className="text-sm text-gray-500 text-center py-2">
                    Aucun filtre actif
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {currentFilters.map((filter, index) => (
                      <Badge key={index} variant="secondary">
                        {filter.displayValue || `${filter.field}: ${filter.value}`}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is-default"
                checked={isDefault}
                onChange={(e) => setIsDefault(e.target.checked)}
                className="rounded border-gray-300"
              />
              <label htmlFor="is-default" className="text-sm">
                Définir comme filtre par défaut
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is-shared"
                checked={isShared}
                onChange={(e) => setIsShared(e.target.checked)}
                className="rounded border-gray-300"
              />
              <label htmlFor="is-shared" className="text-sm">
                Partager avec l'équipe
              </label>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Annuler
            </Button>
            <Button onClick={createFilter}>
              <Save className="mr-2 h-4 w-4" />
              Sauvegarder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Filter Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Modifier le filtre</DialogTitle>
            <DialogDescription>
              Modifiez les détails de votre filtre sauvegardé.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="edit-filter-name" className="text-sm font-medium">
                Nom du filtre
              </label>
              <Input
                id="edit-filter-name"
                placeholder="Ex: Produits en promotion"
                value={newFilterName}
                onChange={(e) => setNewFilterName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="edit-filter-description" className="text-sm font-medium">
                Description (optionnelle)
              </label>
              <Input
                id="edit-filter-description"
                placeholder="Description du filtre"
                value={newFilterDescription}
                onChange={(e) => setNewFilterDescription(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Filtres sauvegardés
              </label>
              <div className="p-2 bg-gray-50 rounded-md">
                {selectedFilter && selectedFilter.filters.length === 0 ? (
                  <div className="text-sm text-gray-500 text-center py-2">
                    Aucun filtre
                  </div>
                ) : selectedFilter && (
                  <div className="flex flex-wrap gap-2">
                    {selectedFilter.filters.map((filter, index) => (
                      <Badge key={index} variant="secondary">
                        {filter.displayValue || `${filter.field}: ${filter.value}`}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit-is-default"
                checked={isDefault}
                onChange={(e) => setIsDefault(e.target.checked)}
                className="rounded border-gray-300"
              />
              <label htmlFor="edit-is-default" className="text-sm">
                Définir comme filtre par défaut
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit-is-shared"
                checked={isShared}
                onChange={(e) => setIsShared(e.target.checked)}
                className="rounded border-gray-300"
              />
              <label htmlFor="edit-is-shared" className="text-sm">
                Partager avec l'équipe
              </label>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Annuler
            </Button>
            <Button onClick={updateFilter}>
              <Save className="mr-2 h-4 w-4" />
              Mettre à jour
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer le filtre "{selectedFilter?.name}" ?
              Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={deleteFilter}>
              <Trash2 className="mr-2 h-4 w-4" />
              Supprimer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default SavedFiltersManager;
