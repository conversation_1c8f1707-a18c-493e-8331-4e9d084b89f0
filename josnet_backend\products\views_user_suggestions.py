from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import timedelta
from django.shortcuts import get_object_or_404

from .models_user_suggestions import (
    ProductSuggestion, 
    ProductSuggestionVote, 
    ProductSuggestionComment, 
    ProductSuggestionAttachment
)
from .serializers_user_suggestions import (
    ProductSuggestionListSerializer,
    ProductSuggestionDetailSerializer,
    ProductSuggestionCreateSerializer,
    ProductSuggestionUpdateSerializer,
    ProductSuggestionAdminUpdateSerializer,
    ProductSuggestionCommentSerializer,
    ProductSuggestionStatsSerializer,
    ProductSuggestionVoteSerializer
)
from authentication.permissions import IsOwnerOrStaff, IsStaffMember


class ProductSuggestionViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les suggestions de produits des utilisateurs.
    """
    
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """<PERSON><PERSON>ne les suggestions selon les permissions de l'utilisateur."""
        user = self.request.user
        
        if user.is_staff_member or user.is_admin:
            # Les admins voient toutes les suggestions
            return ProductSuggestion.objects.all().select_related(
                'user', 'reviewed_by'
            ).prefetch_related(
                'votes', 'comments', 'attachments'
            )
        else:
            # Les utilisateurs voient seulement leurs suggestions
            return ProductSuggestion.objects.filter(
                user=user
            ).select_related(
                'user', 'reviewed_by'
            ).prefetch_related(
                'votes', 'comments', 'attachments'
            )
    
    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action."""
        if self.action == 'list':
            return ProductSuggestionListSerializer
        elif self.action == 'create':
            return ProductSuggestionCreateSerializer
        elif self.action in ['update', 'partial_update']:
            user = self.request.user
            if user.is_staff_member or user.is_admin:
                return ProductSuggestionAdminUpdateSerializer
            else:
                return ProductSuggestionUpdateSerializer
        else:
            return ProductSuggestionDetailSerializer
    
    def get_permissions(self):
        """Retourne les permissions selon l'action."""
        if self.action in ['update', 'partial_update', 'destroy']:
            return [IsOwnerOrStaff()]
        return [IsAuthenticated()]
    
    def perform_create(self, serializer):
        """Assigne l'utilisateur actuel lors de la création."""
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def vote(self, request, pk=None):
        """Permet à un utilisateur de voter pour une suggestion."""
        suggestion = self.get_object()
        user = request.user
        
        # Vérifier si l'utilisateur a déjà voté
        existing_vote = ProductSuggestionVote.objects.filter(
            suggestion=suggestion, user=user
        ).first()
        
        if existing_vote:
            # Retirer le vote
            existing_vote.delete()
            suggestion.votes_count = suggestion.votes.count()
            suggestion.save()
            
            return Response({
                'message': 'Vote retiré',
                'voted': False,
                'votes_count': suggestion.votes_count
            })
        else:
            # Ajouter le vote
            ProductSuggestionVote.objects.create(
                suggestion=suggestion, user=user
            )
            suggestion.votes_count = suggestion.votes.count()
            suggestion.save()
            
            return Response({
                'message': 'Vote ajouté',
                'voted': True,
                'votes_count': suggestion.votes_count
            })
    
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def add_comment(self, request, pk=None):
        """Ajoute un commentaire à une suggestion."""
        suggestion = self.get_object()
        
        serializer = ProductSuggestionCommentSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(
                suggestion=suggestion,
                user=request.user,
                is_admin_comment=request.user.is_staff_member or request.user.is_admin
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def my_suggestions(self, request):
        """Retourne les suggestions de l'utilisateur actuel."""
        suggestions = ProductSuggestion.objects.filter(
            user=request.user
        ).select_related('reviewed_by')
        
        serializer = ProductSuggestionListSerializer(suggestions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], permission_classes=[IsStaffMember])
    def pending(self, request):
        """Retourne les suggestions en attente (admin seulement)."""
        suggestions = ProductSuggestion.objects.filter(
            status='pending'
        ).select_related('user').order_by('-created_at')
        
        serializer = ProductSuggestionListSerializer(suggestions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], permission_classes=[IsStaffMember])
    def stats(self, request):
        """Retourne les statistiques des suggestions (admin seulement)."""
        now = timezone.now()
        this_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_week = now - timedelta(days=7)
        
        # Statistiques générales
        total_suggestions = ProductSuggestion.objects.count()
        pending_suggestions = ProductSuggestion.objects.filter(status='pending').count()
        approved_suggestions = ProductSuggestion.objects.filter(status='approved').count()
        rejected_suggestions = ProductSuggestion.objects.filter(status='rejected').count()
        implemented_suggestions = ProductSuggestion.objects.filter(status='implemented').count()
        
        # Suggestions récentes
        suggestions_this_month = ProductSuggestion.objects.filter(
            created_at__gte=this_month
        ).count()
        suggestions_this_week = ProductSuggestion.objects.filter(
            created_at__gte=this_week
        ).count()
        
        # Top catégories
        top_categories = list(
            ProductSuggestion.objects.values('category')
            .annotate(count=Count('id'))
            .order_by('-count')[:5]
        )
        
        # Temps de réponse moyen (compatible SQLite)
        from django.db.models import F, Avg, Case, When, IntegerField
        from django.db.models.functions import Cast

        # Calculer manuellement la différence en jours (compatible SQLite)
        suggestions_with_response = ProductSuggestion.objects.filter(
            response_date__isnull=False
        )

        if suggestions_with_response.exists():
            total_days = 0
            count = 0
            for suggestion in suggestions_with_response:
                if suggestion.response_date and suggestion.created_at:
                    days = (suggestion.response_date - suggestion.created_at).days
                    total_days += days
                    count += 1
            avg_response_time = total_days / count if count > 0 else 0
        else:
            avg_response_time = 0
        
        # Suggestions les plus votées
        most_voted = ProductSuggestion.objects.filter(
            votes_count__gt=0
        ).order_by('-votes_count')[:5]
        
        stats_data = {
            'total_suggestions': total_suggestions,
            'pending_suggestions': pending_suggestions,
            'approved_suggestions': approved_suggestions,
            'rejected_suggestions': rejected_suggestions,
            'implemented_suggestions': implemented_suggestions,
            'suggestions_this_month': suggestions_this_month,
            'suggestions_this_week': suggestions_this_week,
            'top_categories': top_categories,
            'avg_response_time_days': round(avg_response_time, 1),
            'most_voted_suggestions': ProductSuggestionListSerializer(
                most_voted, many=True
            ).data
        }
        
        serializer = ProductSuggestionStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'], permission_classes=[IsStaffMember])
    def approve(self, request, pk=None):
        """Approuve une suggestion (admin seulement)."""
        suggestion = self.get_object()
        admin_response = request.data.get('admin_response', '')
        
        if not admin_response:
            return Response(
                {'error': 'Une réponse administrateur est requise'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        suggestion.status = 'approved'
        suggestion.admin_response = admin_response
        suggestion.reviewed_by = request.user
        suggestion.response_date = timezone.now()
        suggestion.save()
        
        serializer = ProductSuggestionDetailSerializer(suggestion)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'], permission_classes=[IsStaffMember])
    def reject(self, request, pk=None):
        """Rejette une suggestion (admin seulement)."""
        suggestion = self.get_object()
        admin_response = request.data.get('admin_response', '')
        
        if not admin_response:
            return Response(
                {'error': 'Une réponse administrateur est requise'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        suggestion.status = 'rejected'
        suggestion.admin_response = admin_response
        suggestion.reviewed_by = request.user
        suggestion.response_date = timezone.now()
        suggestion.save()
        
        serializer = ProductSuggestionDetailSerializer(suggestion)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'], permission_classes=[IsStaffMember])
    def mark_implemented(self, request, pk=None):
        """Marque une suggestion comme implémentée (admin seulement)."""
        suggestion = self.get_object()
        implementation_notes = request.data.get('implementation_notes', '')
        
        suggestion.status = 'implemented'
        if implementation_notes:
            suggestion.implementation_notes = implementation_notes
        suggestion.save()
        
        serializer = ProductSuggestionDetailSerializer(suggestion)
        return Response(serializer.data)


class ProductSuggestionCommentViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les commentaires sur les suggestions.
    """
    
    serializer_class = ProductSuggestionCommentSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Retourne les commentaires selon les permissions."""
        suggestion_id = self.kwargs.get('suggestion_pk')
        if suggestion_id:
            return ProductSuggestionComment.objects.filter(
                suggestion_id=suggestion_id
            ).select_related('user')
        return ProductSuggestionComment.objects.none()
    
    def perform_create(self, serializer):
        """Crée un commentaire."""
        suggestion_id = self.kwargs.get('suggestion_pk')
        suggestion = get_object_or_404(ProductSuggestion, id=suggestion_id)
        
        serializer.save(
            suggestion=suggestion,
            user=self.request.user,
            is_admin_comment=self.request.user.is_staff_member or self.request.user.is_admin
        )
