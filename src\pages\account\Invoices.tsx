
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import AccountLayout from "@/components/account/AccountLayout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Search, Download, Eye, FileText, Loader2, AlertCircle, RefreshCw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { accountApi } from "@/services/accountApi";
import { useToast } from "@/hooks/use-toast";

const Invoices = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [isDownloading, setIsDownloading] = useState(false);

  // Fetch invoices data
  const {
    data: invoicesData = [],
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['invoices'],
    queryFn: () => accountApi.getInvoices(),
    retry: 1,
    retryDelay: 1000,
  });

  const filteredInvoices = invoicesData.filter(invoice =>
    invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    invoice.orderId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Fonction pour télécharger une facture individuelle
  const handleDownload = async (invoiceId: string) => {
    try {
      toast({
        title: "Téléchargement démarré",
        description: `La facture ${invoiceId} est en cours de téléchargement`,
      });
      
      // Télécharger le PDF de la facture
      const blob = await accountApi.downloadInvoice(invoiceId);
      
      // Créer un URL pour le blob et déclencher le téléchargement
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `facture-${invoiceId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Téléchargement terminé",
        description: `La facture ${invoiceId} a été téléchargée avec succès`,
      });
    } catch (error) {
      console.error('Erreur lors du téléchargement de la facture:', error);
      toast({
        title: "Erreur de téléchargement",
        description: `Une erreur est survenue lors du téléchargement de la facture ${invoiceId}`,
        variant: "destructive",
      });
    }
  };
  
  // Fonction pour télécharger toutes les factures
  const handleDownloadAll = async () => {
    if (isDownloading) return;
    
    try {
      setIsDownloading(true);
      toast({
        title: "Téléchargement démarré",
        description: "Téléchargement de toutes les factures en cours",
      });
      
      // Télécharger le ZIP de toutes les factures
      const blob = await accountApi.downloadAllInvoices();
      
      // Créer un URL pour le blob et déclencher le téléchargement
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `factures.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Téléchargement terminé",
        description: "Toutes les factures ont été téléchargées avec succès",
      });
    } catch (error) {
      console.error('Erreur lors du téléchargement des factures:', error);
      toast({
        title: "Erreur de téléchargement",
        description: "Une erreur est survenue lors du téléchargement des factures",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };
  
  // Fonction pour voir les détails d'une facture
  const handleViewInvoice = (invoiceId: string) => {
    navigate(`/account/invoices/${invoiceId}`);
  };

  const handleRefresh = () => {
    refetch();
    toast({
      title: "Rafraîchissement",
      description: "Liste des factures mise à jour",
    });
  };

  return (
    <AccountLayout title="Factures">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="relative w-full sm:max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Rechercher une facture..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Actualiser
            </Button>
            <Button 
              variant="outline" 
              className="flex items-center gap-2" 
              onClick={handleDownloadAll}
              disabled={isDownloading || isLoading || filteredInvoices.length === 0}
            >
              {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              Télécharger toutes les factures
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="border rounded-md flex flex-col items-center justify-center py-12 text-center">
            <Loader2 className="h-12 w-12 text-gray-400 mb-4 animate-spin" />
            <h3 className="text-lg font-medium mb-2">Chargement des factures...</h3>
            <p className="text-gray-500 max-w-sm">
              Veuillez patienter pendant que nous récupérons vos factures.
            </p>
          </div>
        ) : isError ? (
          <div className="border rounded-md flex flex-col items-center justify-center py-12 text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
            <p className="text-gray-500 max-w-sm mb-4">
              Une erreur est survenue lors du chargement de vos factures. Veuillez réessayer.
            </p>
            <Button variant="outline" onClick={handleRefresh}>
              Réessayer
            </Button>
          </div>
        ) : filteredInvoices.length > 0 ? (
          <div className="border rounded-md overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>N° Facture</TableHead>
                  <TableHead>N° Commande</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Montant</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">{invoice.id}</TableCell>
                    <TableCell>{invoice.orderId}</TableCell>
                    <TableCell>{invoice.date}</TableCell>
                    <TableCell>{invoice.amount}</TableCell>
                    <TableCell>
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                        {invoice.status}
                      </span>
                    </TableCell>
                    <TableCell className="text-right space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="inline-flex items-center"
                        onClick={() => handleViewInvoice(invoice.id)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        <span className="hidden sm:inline">Voir</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="inline-flex items-center"
                        onClick={() => handleDownload(invoice.id)}
                        disabled={isDownloading}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        <span className="hidden sm:inline">Télécharger</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="border rounded-md flex flex-col items-center justify-center py-12 text-center">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Aucune facture trouvée</h3>
            <p className="text-gray-500 max-w-sm">
              {searchTerm
                ? "Aucune facture ne correspond à votre recherche"
                : "Vous n'avez pas encore de factures associées à votre compte"}
            </p>
          </div>
        )}
      </div>
    </AccountLayout>
  );
};

export default Invoices;
