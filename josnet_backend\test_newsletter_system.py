#!/usr/bin/env python
"""
Script de test pour le système de newsletter et notifications automatiques.
Ce script teste le fonctionnement complet du système d'abonnements et d'envoi d'emails.

Usage:
    python test_newsletter_system.py
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from core.models import NewsletterSubscription, NewsletterCampaign
from core.services.newsletter_service import NewsletterService
from products.models import Product, Category, Promotion

User = get_user_model()


class NewsletterSystemTester:
    """Classe pour tester le système de newsletter."""
    
    def __init__(self):
        self.test_results = []
        self.setup_test_data()
    
    def setup_test_data(self):
        """Créer les données de test."""
        print("🔧 Configuration des données de test...")
        
        # Créer un utilisateur admin
        self.admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': 'testpass123',
                'first_name': 'Admin',
                'last_name': 'Test',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            self.admin_user.set_password('testpass123')
            self.admin_user.save()
        
        # Créer des utilisateurs de test
        self.test_users = []
        for i in range(3):
            user, created = User.objects.get_or_create(
                email=f'user{i+1}@test.com',
                defaults={
                    'password': 'testpass123',
                    'first_name': f'User{i+1}',
                    'last_name': 'Test'
                }
            )
            if created:
                user.set_password('testpass123')
                user.save()
            self.test_users.append(user)
        
        # Créer une catégorie de test
        self.category, created = Category.objects.get_or_create(
            name='Test Category',
            defaults={'slug': 'test-category'}
        )
        
        print("✅ Données de test configurées")
    
    def test_subscription_creation(self):
        """Test de création d'abonnements."""
        print("\n📧 Test de création d'abonnements...")
        
        try:
            # Supprimer les abonnements existants pour ce test
            NewsletterSubscription.objects.filter(
                email__in=[user.email for user in self.test_users]
            ).delete()
            
            # Créer des abonnements avec différentes préférences
            subscriptions = []
            preferences_configs = [
                {'promotions': True, 'new_products': False, 'newsletters': True, 'order_updates': True},
                {'promotions': False, 'new_products': True, 'newsletters': False, 'order_updates': True},
                {'promotions': True, 'new_products': True, 'newsletters': True, 'order_updates': False},
            ]
            
            for i, user in enumerate(self.test_users):
                prefs = preferences_configs[i]
                subscription = NewsletterSubscription.objects.create(
                    user=user,
                    email=user.email,
                    preferences_promotions=prefs['promotions'],
                    preferences_new_products=prefs['new_products'],
                    preferences_newsletters=prefs['newsletters'],
                    preferences_order_updates=prefs['order_updates']
                )
                subscriptions.append(subscription)
            
            # Vérifier les abonnements
            total_subscriptions = NewsletterSubscription.objects.filter(is_active=True).count()
            promo_subscriptions = NewsletterSubscription.objects.filter(
                is_active=True, preferences_promotions=True
            ).count()
            product_subscriptions = NewsletterSubscription.objects.filter(
                is_active=True, preferences_new_products=True
            ).count()
            
            print(f"   ✅ {total_subscriptions} abonnements créés")
            print(f"   ✅ {promo_subscriptions} abonnés aux promotions")
            print(f"   ✅ {product_subscriptions} abonnés aux nouveaux produits")
            
            self.test_results.append(("Création d'abonnements", True, f"{total_subscriptions} abonnements"))
            return subscriptions
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Création d'abonnements", False, str(e)))
            return []
    
    def test_newsletter_service(self):
        """Test du service de newsletter."""
        print("\n📨 Test du service de newsletter...")
        
        try:
            # Test de récupération des abonnés
            all_subscribers = NewsletterService.get_active_subscribers()
            promo_subscribers = NewsletterService.get_active_subscribers(['promotions'])
            product_subscribers = NewsletterService.get_active_subscribers(['new_products'])
            
            print(f"   ✅ {len(all_subscribers)} abonnés actifs trouvés")
            print(f"   ✅ {len(promo_subscribers)} abonnés aux promotions")
            print(f"   ✅ {len(product_subscribers)} abonnés aux nouveaux produits")
            
            # Test de création de campagne
            campaign = NewsletterService.create_campaign(
                title='Test Campaign',
                subject='Test de campagne newsletter',
                content='<p>Ceci est un test de campagne newsletter.</p>',
                campaign_type='newsletter',
                target_all=True,
                created_by=self.admin_user
            )
            
            print(f"   ✅ Campagne créée: {campaign.title}")
            
            self.test_results.append(("Service de newsletter", True, f"Campagne {campaign.id} créée"))
            return campaign
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Service de newsletter", False, str(e)))
            return None
    
    @override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
    def test_promotion_notification(self):
        """Test de notification de promotion."""
        print("\n🎉 Test de notification de promotion...")
        
        try:
            # Créer une promotion
            promotion = Promotion.objects.create(
                title='Super Promotion Test',
                description='Une promotion incroyable avec 50% de réduction sur tous les produits !',
                promotion_type='percentage',
                discount_percentage=Decimal('50.00'),
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=7),
                status='draft',
                created_by=self.admin_user,
                send_email_notification=True
            )
            
            print(f"   ✅ Promotion créée: {promotion.title}")
            
            # Activer la promotion (cela devrait déclencher l'envoi d'email)
            promotion.status = 'active'
            promotion.save()
            
            print(f"   ✅ Promotion activée")
            
            # Vérifier si l'email a été marqué comme envoyé
            promotion.refresh_from_db()
            if promotion.email_sent:
                print(f"   ✅ Email marqué comme envoyé à {promotion.email_sent_at}")
            else:
                print(f"   ⚠️  Email pas encore marqué comme envoyé")
            
            # Vérifier les campagnes créées
            campaigns = NewsletterCampaign.objects.filter(campaign_type='promotion')
            print(f"   ✅ {campaigns.count()} campagne(s) de promotion trouvée(s)")
            
            self.test_results.append(("Notification de promotion", True, f"Promotion {promotion.id} activée"))
            return promotion
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Notification de promotion", False, str(e)))
            return None
    
    @override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
    def test_new_product_notification(self):
        """Test de notification de nouveau produit."""
        print("\n✨ Test de notification de nouveau produit...")
        
        try:
            # Créer un produit en brouillon
            product = Product.objects.create(
                name='Produit Test Automatique',
                slug='produit-test-automatique',
                sku='PTA001',
                description='Un produit de test pour vérifier les notifications automatiques.',
                short_description='Produit de test pour notifications',
                price=Decimal('99.99'),
                status='draft',
                created_by=self.admin_user
            )
            product.categories.add(self.category)
            
            print(f"   ✅ Produit créé en brouillon: {product.name}")
            
            # Publier le produit (cela devrait déclencher l'envoi d'email)
            product.status = 'published'
            product.save()
            
            print(f"   ✅ Produit publié")
            
            # Vérifier les campagnes créées
            campaigns = NewsletterCampaign.objects.filter(campaign_type='new_product')
            print(f"   ✅ {campaigns.count()} campagne(s) de nouveau produit trouvée(s)")
            
            self.test_results.append(("Notification nouveau produit", True, f"Produit {product.id} publié"))
            return product
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Notification nouveau produit", False, str(e)))
            return None
    
    def test_subscription_management(self):
        """Test de gestion des abonnements."""
        print("\n⚙️ Test de gestion des abonnements...")
        
        try:
            # Tester le désabonnement
            subscription = NewsletterSubscription.objects.filter(is_active=True).first()
            if subscription:
                original_email = subscription.email
                subscription.unsubscribe()
                
                print(f"   ✅ Désabonnement de {original_email}")
                
                # Tester la réactivation
                subscription.reactivate()
                print(f"   ✅ Réactivation de {original_email}")
                
                # Tester la mise à jour des préférences
                subscription.preferences_promotions = False
                subscription.preferences_newsletters = True
                subscription.save()
                
                print(f"   ✅ Préférences mises à jour pour {original_email}")
                
                self.test_results.append(("Gestion des abonnements", True, "Désabonnement/réactivation OK"))
            else:
                self.test_results.append(("Gestion des abonnements", False, "Aucun abonnement trouvé"))
                
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Gestion des abonnements", False, str(e)))
    
    def run_all_tests(self):
        """Exécuter tous les tests."""
        print("🚀 Démarrage des tests du système de newsletter\n")
        
        # Exécuter les tests
        subscriptions = self.test_subscription_creation()
        campaign = self.test_newsletter_service()
        promotion = self.test_promotion_notification()
        product = self.test_new_product_notification()
        self.test_subscription_management()
        
        # Afficher le résumé
        self.print_summary()
        
        return self.test_results
    
    def print_summary(self):
        """Afficher le résumé des tests."""
        print("\n" + "="*60)
        print("📊 RÉSUMÉ DES TESTS")
        print("="*60)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}: {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("\n" + "-"*60)
        print(f"Total: {len(self.test_results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print("\n🎉 Tous les tests sont passés avec succès !")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué. Vérifiez la configuration.")
        
        print("="*60)


def main():
    """Fonction principale."""
    tester = NewsletterSystemTester()
    results = tester.run_all_tests()
    
    # Code de sortie
    failed_tests = [r for r in results if not r[1]]
    sys.exit(len(failed_tests))


if __name__ == '__main__':
    main()
