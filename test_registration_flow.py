#!/usr/bin/env python3
"""
Script pour tester le flux d'inscription complet
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
import json

User = get_user_model()

def test_registration_with_gmail():
    """Tester l'inscription avec Gmail"""
    print("🔐 TEST D'INSCRIPTION AVEC GMAIL")
    print("=" * 50)
    
    # Nettoyer les utilisateurs de test existants
    User.objects.filter(email='<EMAIL>').delete()
    
    client = Client()
    
    # Données d'inscription
    registration_data = {
        'first_name': 'Test',
        'last_name': 'Registration',
        'email': '<EMAIL>',
        'password': 'SecurePass123!',
        'confirm_password': 'SecurePass123!'
    }
    
    print(f"📝 Inscription avec: {registration_data['email']}")
    
    response = client.post(
        '/api/v1/auth/register/',
        data=json.dumps(registration_data),
        content_type='application/json'
    )
    
    print(f"📡 Status: {response.status_code}")
    
    if response.status_code == 201:
        data = response.json()
        print("✅ Inscription réussie!")
        print(f"   👤 Utilisateur: {data['user']['first_name']} {data['user']['last_name']}")
        print(f"   📧 Email: {data['user']['email']}")
        print(f"   ✅ Vérifié: {data['user']['is_verified']}")
        print(f"   🔑 Token access: {'Oui' if data.get('access') else 'Non'}")
        print(f"   🔄 Auto-login: {data.get('auto_login', False)}")
        
        email_verification = data.get('email_verification', {})
        print(f"   📬 Email envoyé: {email_verification.get('sent', 'N/A')}")
        print(f"   💬 Message: {email_verification.get('message', 'N/A')}")
        
        return True, data
    else:
        print(f"❌ Échec inscription:")
        try:
            error_data = response.json()
            print(f"   Erreurs: {error_data}")
        except:
            print(f"   Réponse: {response.content.decode()}")
        return False, None

def test_registration_with_non_gmail():
    """Tester l'inscription avec un email non-Gmail (doit échouer)"""
    print(f"\n❌ TEST D'INSCRIPTION AVEC EMAIL NON-GMAIL")
    print("=" * 50)
    
    client = Client()
    
    # Données d'inscription avec email non-Gmail
    registration_data = {
        'first_name': 'Test',
        'last_name': 'NonGmail',
        'email': '<EMAIL>',  # Non-Gmail
        'password': 'SecurePass123!',
        'confirm_password': 'SecurePass123!'
    }
    
    print(f"📝 Tentative d'inscription avec: {registration_data['email']}")
    
    response = client.post(
        '/api/v1/auth/register/',
        data=json.dumps(registration_data),
        content_type='application/json'
    )
    
    print(f"📡 Status: {response.status_code}")
    
    if response.status_code == 400:
        data = response.json()
        print("✅ Rejet correct de l'email non-Gmail!")
        print(f"   Erreurs: {data}")
        return True
    else:
        print(f"❌ L'email non-Gmail a été accepté (problème!)")
        return False

def test_email_verification_flow():
    """Tester le flux de vérification d'email"""
    print(f"\n✉️ TEST DU FLUX DE VÉRIFICATION D'EMAIL")
    print("=" * 50)
    
    # Créer un utilisateur non vérifié
    User.objects.filter(email='<EMAIL>').delete()
    
    user = User.objects.create_user(
        email='<EMAIL>',
        password='VerifyPass123!',
        first_name='Verify',
        last_name='Flow',
        is_verified=False
    )
    
    print(f"✅ Utilisateur créé: {user.email} (vérifié: {user.is_verified})")
    
    # Générer un token de vérification
    from django.utils.http import urlsafe_base64_encode
    from django.utils.encoding import force_bytes
    from django.contrib.auth.tokens import default_token_generator
    
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)
    
    if isinstance(uid, bytes):
        uid_str = uid.decode()
    else:
        uid_str = uid
        
    verification_token = f"{uid_str}-{token}"
    
    print(f"🔑 Token généré: {verification_token[:30]}...")
    
    # Tester la vérification
    client = Client()
    response = client.post(
        '/api/v1/auth/email/verify/',
        data=json.dumps({'token': verification_token}),
        content_type='application/json'
    )
    
    print(f"📡 Status vérification: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Vérification réussie!")
        print(f"   💬 Message: {data.get('message', 'N/A')}")
        
        # Vérifier que l'utilisateur est maintenant vérifié
        user.refresh_from_db()
        print(f"   ✅ Utilisateur maintenant vérifié: {user.is_verified}")
        
        return True
    else:
        print(f"❌ Échec vérification:")
        try:
            error_data = response.json()
            print(f"   Erreurs: {error_data}")
        except:
            print(f"   Réponse: {response.content.decode()}")
        return False

def create_demo_user():
    """Créer un utilisateur de démonstration pour les tests frontend"""
    print(f"\n👤 CRÉATION D'UTILISATEUR DE DÉMONSTRATION")
    print("=" * 50)
    
    # Supprimer l'utilisateur s'il existe
    User.objects.filter(email='<EMAIL>').delete()
    
    user = User.objects.create_user(
        email='<EMAIL>',
        password='DemoPass123!',
        first_name='Demo',
        last_name='User',
        is_verified=False
    )
    
    print(f"✅ Utilisateur de démonstration créé:")
    print(f"   📧 Email: {user.email}")
    print(f"   🔑 Mot de passe: DemoPass123!")
    print(f"   ✅ Vérifié: {user.is_verified}")
    
    # Générer un lien de vérification
    from django.utils.http import urlsafe_base64_encode
    from django.utils.encoding import force_bytes
    from django.contrib.auth.tokens import default_token_generator
    from django.conf import settings
    
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)
    
    if isinstance(uid, bytes):
        uid_str = uid.decode()
    else:
        uid_str = uid
        
    verification_token = f"{uid_str}-{token}"
    verification_link = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
    
    print(f"\n🔗 Lien de vérification pour test:")
    print(f"   {verification_link}")
    
    return user, verification_link

def show_summary():
    """Afficher le résumé des fonctionnalités"""
    print(f"\n🎯 RÉSUMÉ DES FONCTIONNALITÉS IMPLÉMENTÉES")
    print("=" * 60)
    
    print(f"✅ SÉCURITÉ D'AUTHENTIFICATION:")
    print(f"   • Validation Gmail obligatoire")
    print(f"   • Rejet des emails non-Gmail")
    print(f"   • Validation du format Gmail (points, etc.)")
    
    print(f"\n✅ VÉRIFICATION D'EMAIL:")
    print(f"   • Envoi d'emails HTML professionnels")
    print(f"   • Tokens sécurisés avec expiration")
    print(f"   • Email de bienvenue après vérification")
    
    print(f"\n✅ CONNEXION AUTOMATIQUE:")
    print(f"   • Tokens JWT générés à l'inscription")
    print(f"   • Connexion immédiate après inscription")
    print(f"   • Données réelles (pas mockées)")
    print(f"   • Redirection automatique vers /account")
    
    print(f"\n📋 TESTS À EFFECTUER DANS LE FRONTEND:")
    print(f"   1. <NAME_EMAIL> → Doit échouer")
    print(f"   2. <NAME_EMAIL> → Doit réussir")
    print(f"   3. Connexion automatique après inscription")
    print(f"   4. Vérification que les données sont réelles")
    print(f"   5. Clic sur le lien de vérification d'email")
    print(f"   6. Réception de l'email de bienvenue")

def main():
    """Fonction principale"""
    print("🔐 TEST COMPLET DU FLUX D'INSCRIPTION SÉCURISÉ")
    print("=" * 70)
    
    # Tests
    tests = [
        ("Inscription avec Gmail", lambda: test_registration_with_gmail()[0]),
        ("Rejet email non-Gmail", test_registration_with_non_gmail),
        ("Vérification d'email", test_email_verification_flow),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name.upper()} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results[test_name] = False
    
    # Créer un utilisateur de démonstration
    print(f"\n{'='*20} UTILISATEUR DE DÉMONSTRATION {'='*20}")
    demo_user, demo_link = create_demo_user()
    
    # Afficher le résumé
    show_summary()
    
    # Résumé final
    print(f"\n🎯 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    print(f"\n📊 Score: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 SYSTÈME D'AUTHENTIFICATION SÉCURISÉ FONCTIONNEL!")
        print("\n🚀 PRÊT POUR LES TESTS FRONTEND!")
        print(f"\n📱 TESTEZ MAINTENANT:")
        print(f"   • Inscription: http://localhost:8080/register")
        print(f"   • Utilisateur demo: <EMAIL> / DemoPass123!")
        print(f"   • Lien de vérification: {demo_link}")
    else:
        print("\n⚠️ PROBLÈMES DÉTECTÉS")
        print("   Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
