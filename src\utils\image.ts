/**
 * Utilitaires pour la gestion des images
 */

/**
 * Construit une URL d'image valide à partir d'un chemin d'image
 * Gère les cas où l'image est undefined, null, ou une URL relative/absolue
 */
export const getImageUrl = (imagePath?: string): string => {
  if (!imagePath) return '';
  
  // Si l'image est déjà une URL complète (commence par http ou https)
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // Si l'image est un chemin relatif, construire l'URL complète
  // Utiliser l'URL de base de l'API ou du CDN selon la configuration
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.example.com';
  
  // Nettoyer le chemin d'image pour éviter les doubles slashes
  const cleanImagePath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
  
  return `${baseUrl}/media/${cleanImagePath}`;
};

/**
 * Vérifie si une URL d'image est valide
 */
export const isValidImageUrl = (url?: string): boolean => {
  if (!url) return false;
  return url.length > 0;
};

/**
 * Renvoie une URL d'image de remplacement en cas d'erreur
 */
export const getFallbackImageUrl = (category?: string): string => {
  // Utiliser différentes images de remplacement selon la catégorie
  switch (category?.toLowerCase()) {
    case 'clothing':
    case 'vêtements':
      return '/images/fallbacks/clothing.png';
    case 'electronics':
    case 'électronique':
      return '/images/fallbacks/electronics.png';
    case 'furniture':
    case 'mobilier':
      return '/images/fallbacks/furniture.png';
    default:
      return '/images/fallbacks/product.png';
  }
};
