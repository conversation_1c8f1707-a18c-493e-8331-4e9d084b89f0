"""
Modèles pour les données dynamiques du site.
"""

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.conf import settings
import uuid


class SiteSettings(models.Model):
    """Configuration générale du site."""

    # Informations de base
    site_name = models.CharField(max_length=100, default="JOSNET NETWORK")
    site_description = models.TextField(default="Votre partenaire IT & Télécom de confiance au Burundi")
    site_logo = models.ImageField(upload_to='site/', blank=True, null=True)

    # Contact
    phone_primary = models.CharField(max_length=20, default="+257 12 345 678")
    phone_secondary = models.CharField(max_length=20, blank=True)
    email_primary = models.EmailField(default="<EMAIL>")
    email_secondary = models.EmailField(blank=True)

    # Adresse
    address_line1 = models.CharField(max_length=200, default="123 Avenue Principale")
    address_line2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100, default="Bujumbura")
    country = models.CharField(max_length=100, default="Burundi")

    # Horaires
    business_hours = models.TextField(default="Lun-Ven: 8h00-18h00, Sam: 8h00-13h00")

    # Réseaux sociaux
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)

    # SEO
    meta_description = models.TextField(max_length=160, blank=True)
    meta_keywords = models.TextField(blank=True)

    # Paramètres
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Configuration du site"
        verbose_name_plural = "Configurations du site"

    def __str__(self):
        return self.site_name


class HomePageStats(models.Model):
    """Statistiques affichées sur la page d'accueil."""

    # Statistiques principales
    clients_count = models.PositiveIntegerField(default=1000, help_text="Nombre de clients satisfaits")
    projects_count = models.PositiveIntegerField(default=5000, help_text="Nombre de projets réalisés")
    experience_years = models.PositiveIntegerField(default=10, help_text="Années d'expérience")
    team_members = models.PositiveIntegerField(default=25, help_text="Membres de l'équipe")

    # Statistiques secondaires
    products_sold = models.PositiveIntegerField(default=10000, help_text="Produits vendus")
    cities_served = models.PositiveIntegerField(default=18, help_text="Villes desservies")
    support_hours = models.CharField(max_length=10, default="24/7", help_text="Heures de support")
    satisfaction_rate = models.PositiveIntegerField(
        default=98,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Taux de satisfaction (%)"
    )

    # Métadonnées
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Statistiques de la page d'accueil"
        verbose_name_plural = "Statistiques de la page d'accueil"
        ordering = ['-created_at']

    def __str__(self):
        return f"Stats du {self.created_at.strftime('%d/%m/%Y')}"


class Service(models.Model):
    """Services offerts par l'entreprise."""

    name = models.CharField(max_length=100)
    description = models.TextField()
    short_description = models.CharField(max_length=200, help_text="Description courte pour la page d'accueil")
    icon = models.CharField(max_length=50, help_text="Nom de l'icône Lucide React")
    color = models.CharField(max_length=20, default="blue", help_text="Couleur du thème (blue, green, purple, etc.)")

    # SEO et affichage
    slug = models.SlugField(unique=True)
    featured = models.BooleanField(default=False, help_text="Afficher sur la page d'accueil")
    order = models.PositiveIntegerField(default=0, help_text="Ordre d'affichage")

    # Métadonnées
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Service"
        verbose_name_plural = "Services"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name


class Testimonial(models.Model):
    """Témoignages clients."""

    client_name = models.CharField(max_length=100)
    client_title = models.CharField(max_length=100, blank=True, help_text="Poste ou titre du client")
    client_company = models.CharField(max_length=100, blank=True)
    client_photo = models.ImageField(upload_to='testimonials/', blank=True, null=True)

    content = models.TextField(help_text="Contenu du témoignage")
    rating = models.PositiveIntegerField(
        default=5,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Note sur 5"
    )

    # Affichage
    featured = models.BooleanField(default=False, help_text="Afficher sur la page d'accueil")
    order = models.PositiveIntegerField(default=0)

    # Métadonnées
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Témoignage"
        verbose_name_plural = "Témoignages"
        ordering = ['order', '-created_at']

    def __str__(self):
        return f"{self.client_name} - {self.client_company}"


class NewsletterSubscriber(models.Model):
    """Abonnés à la newsletter."""

    email = models.EmailField(unique=True)
    name = models.CharField(max_length=100, blank=True)

    # Statut
    is_active = models.BooleanField(default=True)
    confirmed = models.BooleanField(default=False)
    confirmation_token = models.CharField(max_length=100, blank=True)

    # Préférences
    frequency = models.CharField(
        max_length=20,
        choices=[
            ('weekly', 'Hebdomadaire'),
            ('monthly', 'Mensuel'),
            ('quarterly', 'Trimestriel'),
        ],
        default='monthly'
    )

    # Métadonnées
    subscribed_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    unsubscribed_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = "Abonné newsletter"
        verbose_name_plural = "Abonnés newsletter"
        ordering = ['-subscribed_at']

    def __str__(self):
        return self.email


class ContactMessage(models.Model):
    """Messages de contact."""

    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True)
    company = models.CharField(max_length=100, blank=True)
    subject = models.CharField(max_length=200)
    message = models.TextField()

    # Service demandé
    service_requested = models.CharField(
        max_length=50,
        choices=[
            ('reseaux', 'Réseaux & Connectivité'),
            ('materiel', 'Matériel Informatique'),
            ('mobile', 'Solutions Mobiles'),
            ('infrastructure', 'Infrastructure IT'),
            ('maintenance', 'Maintenance'),
            ('formation', 'Formation'),
            ('autre', 'Autre'),
        ],
        blank=True
    )

    # Statut
    status = models.CharField(
        max_length=20,
        choices=[
            ('new', 'Nouveau'),
            ('in_progress', 'En cours'),
            ('replied', 'Répondu'),
            ('closed', 'Fermé'),
        ],
        default='new'
    )

    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    replied_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = "Message de contact"
        verbose_name_plural = "Messages de contact"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.subject}"


class FAQ(models.Model):
    """Questions fréquemment posées."""

    question = models.CharField(max_length=200)
    answer = models.TextField()

    # Catégorie
    category = models.CharField(
        max_length=50,
        choices=[
            ('general', 'Général'),
            ('products', 'Produits'),
            ('orders', 'Commandes'),
            ('shipping', 'Livraison'),
            ('support', 'Support'),
            ('technical', 'Technique'),
        ],
        default='general'
    )

    # Affichage
    featured = models.BooleanField(default=False, help_text="Afficher en priorité")
    order = models.PositiveIntegerField(default=0)

    # Métadonnées
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "FAQ"
        verbose_name_plural = "FAQ"
        ordering = ['order', 'question']

    def __str__(self):
        return self.question


class Banner(models.Model):
    """Bannières promotionnelles."""

    title = models.CharField(max_length=100)
    subtitle = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='banners/', blank=True, null=True)

    # Liens
    button_text = models.CharField(max_length=50, blank=True)
    button_url = models.URLField(blank=True)

    # Affichage
    position = models.CharField(
        max_length=20,
        choices=[
            ('hero', 'Hero principal'),
            ('secondary', 'Secondaire'),
            ('sidebar', 'Barre latérale'),
        ],
        default='secondary'
    )

    # Planification
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(blank=True, null=True)

    # Métadonnées
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Bannière"
        verbose_name_plural = "Bannières"
        ordering = ['order', '-created_at']

    def __str__(self):
        return self.title

    def is_visible(self):
        """Vérifie si la bannière doit être affichée."""
        now = timezone.now()
        if not self.is_active:
            return False
        if self.start_date > now:
            return False
        if self.end_date and self.end_date < now:
            return False
        return True


class BlogCategory(models.Model):
    """Catégories d'articles de blog."""

    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=20, default="blue", help_text="Couleur du thème")

    # Affichage
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Catégorie de blog"
        verbose_name_plural = "Catégories de blog"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name


class BlogPost(models.Model):
    """Articles de blog."""

    STATUS_CHOICES = [
        ('draft', 'Brouillon'),
        ('published', 'Publié'),
        ('archived', 'Archivé'),
    ]

    # Contenu principal
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    excerpt = models.TextField(max_length=300, help_text="Résumé de l'article")
    content = models.TextField()

    # Médias
    featured_image = models.ImageField(upload_to='blog/', blank=True, null=True)
    featured_image_alt = models.CharField(max_length=200, blank=True)

    # Classification
    category = models.ForeignKey(BlogCategory, on_delete=models.CASCADE, related_name='posts')
    tags = models.CharField(max_length=500, blank=True, help_text="Tags séparés par des virgules")

    # Auteur
    author_name = models.CharField(max_length=100)
    author_email = models.EmailField(blank=True)
    author_bio = models.TextField(blank=True)
    author_avatar = models.ImageField(upload_to='authors/', blank=True, null=True)

    # Publication
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    featured = models.BooleanField(default=False, help_text="Article mis en avant")

    # Dates
    published_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # SEO
    meta_description = models.CharField(max_length=160, blank=True)
    meta_keywords = models.CharField(max_length=500, blank=True)

    # Statistiques
    views_count = models.PositiveIntegerField(default=0)
    likes_count = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = "Article de blog"
        verbose_name_plural = "Articles de blog"
        ordering = ['-published_at', '-created_at']

    def __str__(self):
        return self.title

    def get_tags_list(self):
        """Retourne la liste des tags."""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',')]
        return []

    def increment_views(self):
        """Incrémente le nombre de vues."""
        self.views_count += 1
        self.save(update_fields=['views_count'])


class BlogComment(models.Model):
    """Commentaires sur les articles de blog."""

    STATUS_CHOICES = [
        ('pending', 'En attente'),
        ('approved', 'Approuvé'),
        ('rejected', 'Rejeté'),
    ]

    # Contenu
    post = models.ForeignKey(BlogPost, on_delete=models.CASCADE, related_name='comments')
    author_name = models.CharField(max_length=100)
    author_email = models.EmailField()
    content = models.TextField()

    # Modération
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)

    class Meta:
        verbose_name = "Commentaire"
        verbose_name_plural = "Commentaires"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.author_name} sur {self.post.title}"


class NewsletterSubscription(models.Model):
    """Modèle pour gérer les abonnements à la newsletter."""

    # Utilisateur (optionnel pour les abonnements anonymes)
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='newsletter_subscription'
    )

    # Email (obligatoire)
    email = models.EmailField(unique=True)

    # Token pour désabonnement sécurisé
    unsubscribe_token = models.UUIDField(default=uuid.uuid4, unique=True)

    # Statut
    is_active = models.BooleanField(default=True)

    # Dates
    subscribed_at = models.DateTimeField(auto_now_add=True)
    unsubscribed_at = models.DateTimeField(null=True, blank=True)

    # Préférences
    preferences_promotions = models.BooleanField(default=True)
    preferences_new_products = models.BooleanField(default=True)
    preferences_newsletters = models.BooleanField(default=True)
    preferences_order_updates = models.BooleanField(default=True)

    # Métadonnées
    source = models.CharField(max_length=50, default='website')  # website, admin, api, etc.
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        verbose_name = "Abonnement Newsletter"
        verbose_name_plural = "Abonnements Newsletter"
        ordering = ['-subscribed_at']

    def __str__(self):
        return f"{self.email} ({'Actif' if self.is_active else 'Inactif'})"

    def unsubscribe(self):
        """Désabonner l'utilisateur."""
        self.is_active = False
        self.unsubscribed_at = timezone.now()
        self.save()

    def reactivate(self):
        """Réactiver l'abonnement."""
        self.is_active = True
        self.unsubscribed_at = None
        self.save()

    @property
    def preferences_dict(self):
        """Retourne les préférences sous forme de dictionnaire."""
        return {
            'promotions': self.preferences_promotions,
            'new_products': self.preferences_new_products,
            'newsletters': self.preferences_newsletters,
            'order_updates': self.preferences_order_updates,
        }


class NewsletterCampaign(models.Model):
    """Modèle pour les campagnes de newsletter."""

    CAMPAIGN_TYPES = [
        ('promotion', 'Promotion'),
        ('newsletter', 'Newsletter'),
        ('announcement', 'Annonce'),
        ('new_product', 'Nouveau Produit'),
    ]

    CAMPAIGN_STATUS = [
        ('draft', 'Brouillon'),
        ('scheduled', 'Programmée'),
        ('sending', 'En cours d\'envoi'),
        ('sent', 'Envoyée'),
        ('failed', 'Échec'),
    ]

    # Informations de base
    title = models.CharField(max_length=200)
    subject = models.CharField(max_length=200)
    content = models.TextField()
    campaign_type = models.CharField(max_length=20, choices=CAMPAIGN_TYPES)
    status = models.CharField(max_length=20, choices=CAMPAIGN_STATUS, default='draft')

    # Ciblage
    target_preferences = models.JSONField(default=list, help_text="Liste des préférences ciblées")
    target_all = models.BooleanField(default=False, help_text="Envoyer à tous les abonnés actifs")

    # Programmation
    scheduled_at = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)

    # Statistiques
    total_recipients = models.PositiveIntegerField(default=0)
    sent_count = models.PositiveIntegerField(default=0)
    failed_count = models.PositiveIntegerField(default=0)
    open_count = models.PositiveIntegerField(default=0)
    click_count = models.PositiveIntegerField(default=0)

    # Métadonnées
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_campaigns'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Campagne Newsletter"
        verbose_name_plural = "Campagnes Newsletter"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.get_status_display()})"

    @property
    def open_rate(self):
        """Calcule le taux d'ouverture."""
        if self.sent_count == 0:
            return 0
        return round((self.open_count / self.sent_count) * 100, 2)

    @property
    def click_rate(self):
        """Calcule le taux de clic."""
        if self.sent_count == 0:
            return 0
        return round((self.click_count / self.sent_count) * 100, 2)


class ActivityLog(models.Model):
    """
    Modèle pour enregistrer toutes les activités du système.
    """

    ACTION_TYPES = [
        ('user_login', 'Connexion utilisateur'),
        ('user_logout', 'Déconnexion utilisateur'),
        ('user_register', 'Inscription utilisateur'),
        ('user_update', 'Mise à jour profil utilisateur'),
        ('product_create', 'Création produit'),
        ('product_update', 'Mise à jour produit'),
        ('product_delete', 'Suppression produit'),
        ('promotion_create', 'Création promotion'),
        ('promotion_activate', 'Activation promotion'),
        ('promotion_deactivate', 'Désactivation promotion'),
        ('order_create', 'Création commande'),
        ('order_update', 'Mise à jour commande'),
        ('order_cancel', 'Annulation commande'),
        ('email_sent', 'Email envoyé'),
        ('email_failed', 'Échec envoi email'),
        ('cms_page_create', 'Création page CMS'),
        ('cms_page_update', 'Mise à jour page CMS'),
        ('cms_blog_create', 'Création article blog'),
        ('cms_blog_update', 'Mise à jour article blog'),
        ('admin_action', 'Action administrateur'),
        ('system_error', 'Erreur système'),
        ('security_alert', 'Alerte sécurité'),
    ]

    SEVERITY_LEVELS = [
        ('info', 'Information'),
        ('warning', 'Avertissement'),
        ('error', 'Erreur'),
        ('critical', 'Critique'),
    ]

    # Informations de base
    timestamp = models.DateTimeField(auto_now_add=True)
    action_type = models.CharField(max_length=50, choices=ACTION_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='info')

    # Utilisateur concerné
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    user_email = models.EmailField(blank=True)  # Backup si user est supprimé

    # Détails de l'action
    description = models.TextField()
    details = models.JSONField(default=dict, blank=True)

    # Informations techniques
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    request_path = models.CharField(max_length=500, blank=True)

    # Métadonnées
    object_type = models.CharField(max_length=100, blank=True)  # Type d'objet concerné
    object_id = models.CharField(max_length=100, blank=True)   # ID de l'objet concerné

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp']),
            models.Index(fields=['action_type']),
            models.Index(fields=['user']),
            models.Index(fields=['severity']),
        ]
        verbose_name = "Journal d'activité"
        verbose_name_plural = "Journal d'activités"

    def __str__(self):
        return f"{self.timestamp} - {self.get_action_type_display()} - {self.user_email or 'Système'}"
