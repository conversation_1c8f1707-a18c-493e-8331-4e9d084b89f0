import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Mail, Settings, Check, X, Bell, Globe } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import subscriptionApi, { SubscriptionPreferences } from '@/services/subscriptionApi';
import { useAuth } from '@/contexts/AuthContext';

const SubscriptionManager: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Récupérer l'abonnement actuel
  const { data: subscription, isLoading } = useQuery({
    queryKey: ['newsletter-subscription'],
    queryFn: () => subscriptionApi.getSubscription(),
    enabled: isAuthenticated,
  });

  // Mutation pour mettre à jour les préférences
  const updatePreferencesMutation = useMutation({
    mutationFn: (preferences: Partial<SubscriptionPreferences>) =>
      subscriptionApi.updatePreferences(preferences),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['newsletter-subscription'] });
      toast({
        title: 'Préférences mises à jour',
        description: 'Vos préférences d\'abonnement ont été sauvegardées.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Impossible de mettre à jour les préférences.',
        variant: 'destructive',
      });
    },
  });

  // Mutation pour s'abonner
  const subscribeMutation = useMutation({
    mutationFn: () => subscriptionApi.subscribe({ 
      email: user?.email || '',
      preferences: {
        promotions: true,
        new_products: true,
        newsletters: true,
        order_updates: true,
      }
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['newsletter-subscription'] });
      toast({
        title: 'Abonnement activé',
        description: 'Vous êtes maintenant abonné à notre newsletter !',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Impossible de s\'abonner.',
        variant: 'destructive',
      });
    },
  });

  // Mutation pour se désabonner
  const unsubscribeMutation = useMutation({
    mutationFn: () => subscriptionApi.unsubscribe({ email: user?.email || '' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['newsletter-subscription'] });
      toast({
        title: 'Désabonnement effectué',
        description: 'Vous ne recevrez plus nos newsletters.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Impossible de se désabonner.',
        variant: 'destructive',
      });
    },
  });

  // Gérer le changement de préférence
  const handlePreferenceChange = (key: keyof SubscriptionPreferences, value: boolean) => {
    if (!subscription) return;
    
    const newPreferences = {
      ...subscription.preferences,
      [key]: value,
    };
    
    updatePreferencesMutation.mutate(newPreferences);
  };

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-center justify-between">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-6 w-12" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Abonnement Newsletter
        </CardTitle>
        <CardDescription>
          Gérez vos préférences d'abonnement aux emails marketing et notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Statut de l'abonnement */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${subscription?.is_active ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}>
                {subscription?.is_active ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
              </div>
              <div>
                <p className="font-medium">
                  {subscription?.is_active ? 'Abonné' : 'Non abonné'}
                </p>
                <p className="text-sm text-gray-600">
                  {subscription?.is_active 
                    ? `Abonné depuis le ${new Date(subscription.subscribed_at).toLocaleDateString('fr-FR')}`
                    : 'Vous ne recevez pas nos newsletters'
                  }
                </p>
              </div>
            </div>
            
            {subscription?.is_active ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => unsubscribeMutation.mutate()}
                disabled={unsubscribeMutation.isPending}
              >
                {unsubscribeMutation.isPending ? 'Désabonnement...' : 'Se désabonner'}
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={() => subscribeMutation.mutate()}
                disabled={subscribeMutation.isPending}
              >
                {subscribeMutation.isPending ? 'Abonnement...' : 'S\'abonner'}
              </Button>
            )}
          </div>

          {/* Préférences détaillées */}
          {subscription?.is_active && (
            <div className="space-y-4">
              <h4 className="font-medium flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Préférences de contenu
              </h4>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="promotions" className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-red-100 text-red-700">
                        🎉 Promotions
                      </Badge>
                    </Label>
                    <p className="text-sm text-gray-600">
                      Offres spéciales, codes promo et ventes flash
                    </p>
                  </div>
                  <Switch
                    id="promotions"
                    checked={subscription.preferences.promotions}
                    onCheckedChange={(checked) => handlePreferenceChange('promotions', checked)}
                    disabled={updatePreferencesMutation.isPending}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="new_products" className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                        ✨ Nouveautés
                      </Badge>
                    </Label>
                    <p className="text-sm text-gray-600">
                      Nouveaux produits et mises à jour du catalogue
                    </p>
                  </div>
                  <Switch
                    id="new_products"
                    checked={subscription.preferences.new_products}
                    onCheckedChange={(checked) => handlePreferenceChange('new_products', checked)}
                    disabled={updatePreferencesMutation.isPending}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="newsletters" className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-green-100 text-green-700">
                        📰 Newsletter
                      </Badge>
                    </Label>
                    <p className="text-sm text-gray-600">
                      Actualités, conseils et contenu éditorial
                    </p>
                  </div>
                  <Switch
                    id="newsletters"
                    checked={subscription.preferences.newsletters}
                    onCheckedChange={(checked) => handlePreferenceChange('newsletters', checked)}
                    disabled={updatePreferencesMutation.isPending}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="order_updates" className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                        📦 Commandes
                      </Badge>
                    </Label>
                    <p className="text-sm text-gray-600">
                      Mises à jour sur vos commandes et livraisons
                    </p>
                  </div>
                  <Switch
                    id="order_updates"
                    checked={subscription.preferences.order_updates}
                    onCheckedChange={(checked) => handlePreferenceChange('order_updates', checked)}
                    disabled={updatePreferencesMutation.isPending}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Informations supplémentaires */}
          <div className="text-xs text-gray-500 space-y-1 pt-4 border-t">
            <p>• Vous pouvez modifier ces préférences à tout moment</p>
            <p>• Les emails de commande sont toujours envoyés pour vos achats</p>
            <p>• Nous respectons votre vie privée et ne partageons jamais vos données</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubscriptionManager;
