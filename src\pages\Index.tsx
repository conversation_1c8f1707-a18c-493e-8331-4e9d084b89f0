
import { useState } from "react";
import { motion } from "framer-motion";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import Hero from "@/components/home/<USER>";
import FeaturedProducts from "@/components/home/<USER>";
import AboutSection from "@/components/home/<USER>";
import DynamicTestimonials from "@/components/home/<USER>";
import DynamicStats from "@/components/home/<USER>";
import DynamicServices from "@/components/home/<USER>";
import ActivePromotions from "@/components/home/<USER>";
import BlogSection from "@/components/home/<USER>";
import NewsletterSubscribe from "@/components/home/<USER>";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "@/contexts/TranslationContext";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Truck, Shield, RefreshCw, Award, CheckCircle, Clock, Users, Globe, MapPin, ArrowRight, ChevronRight, Database, Sparkles } from "lucide-react";
import { Link } from "react-router-dom";
import coreApi from "@/services/coreApi";
import { useHomePageCms } from "@/hooks/useHomePageCms";

const Index = () => {
  const { isAuthenticated } = useAuth();
  const { t } = useTranslation();

  // Récupérer les données du CMS admin pour la page d'accueil
  const {
    featuredPages
  } = useHomePageCms();

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-white via-blue-50/30 to-white">
      <Navbar />

      <main className="flex-grow">
        {/* Hero Section */}
        <Hero />

        {/* Features Section - Enhanced */}
        <section className="py-16 lg:py-24 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="container mx-auto px-4 relative">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Pourquoi choisir JOSNET ?
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto mb-6"></div>
              <p className="text-gray-600 max-w-3xl mx-auto text-lg lg:text-xl leading-relaxed">
                Votre partenaire technologique de confiance au Burundi depuis plus de 10 ans
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
              {[
                {
                  icon: <Truck className="w-8 h-8" />,
                  title: "Livraison Rapide",
                  description: "Expédition sous 24h et livraison sécurisée dans tout le Burundi avec suivi en temps réel",
                  color: "from-blue-500 to-blue-600",
                  bgColor: "from-blue-50 to-blue-100"
                },
                {
                  icon: <Shield className="w-8 h-8" />,
                  title: "Paiement Sécurisé",
                  description: "Transactions 100% sécurisées avec cryptage SSL et protection des données personnelles",
                  color: "from-green-500 to-green-600",
                  bgColor: "from-green-50 to-green-100"
                },
                {
                  icon: <RefreshCw className="w-8 h-8" />,
                  title: "Retour Facile",
                  description: "Politique de retour flexible sous 30 jours avec remboursement intégral garanti",
                  color: "from-purple-500 to-purple-600",
                  bgColor: "from-purple-50 to-purple-100"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.2, duration: 0.6 }}
                  className="group relative"
                >
                  <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 transform hover:-translate-y-2">
                    <div className="flex flex-col items-center text-center space-y-6">
                      <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.bgColor} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <div className={`text-transparent bg-gradient-to-br ${feature.color} bg-clip-text`}>
                          {feature.icon}
                        </div>
                      </div>
                      <div className="space-y-3">
                        <h3 className="text-xl lg:text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {feature.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {feature.description}
                        </p>
                      </div>
                    </div>

                    {/* Hover Effect */}
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-600/5 to-indigo-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Additional Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
            >
              {[
                { value: "10K+", label: "Clients satisfaits" },
                { value: "5K+", label: "Produits disponibles" },
                { value: "24/7", label: "Support client" },
                { value: "98%", label: "Taux de satisfaction" }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Active Promotions Section */}
        <ActivePromotions />

        {/* Services Overview Section - Dynamic */}
        <DynamicServices />

        {/* Features Section - Enhanced with CMS data */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Pourquoi choisir JOSNET ?
              </h2>
              <div className="w-24 h-1 bg-blue-600 mx-auto mb-6"></div>
              <p className="text-gray-600 max-w-3xl mx-auto text-lg">
                Depuis plus de 10 ans, nous nous engageons à fournir des solutions technologiques de qualité avec un service client exceptionnel au Burundi.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Feature 1 - Livraison */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-xl transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-2">
                <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200 rounded-full mb-6 group-hover:from-blue-200 group-hover:to-blue-300 transition-all duration-300">
                  <Truck className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">Livraison Nationale</h3>
                <p className="text-gray-600 text-center leading-relaxed">Livraison rapide et sécurisée dans tout le Burundi avec suivi en temps réel de vos commandes</p>
              </div>

              {/* Feature 2 - Qualité */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-xl transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-2">
                <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-green-100 to-green-200 rounded-full mb-6 group-hover:from-green-200 group-hover:to-green-300 transition-all duration-300">
                  <Award className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">Qualité Garantie</h3>
                <p className="text-gray-600 text-center leading-relaxed">Produits authentiques et certifiés avec garantie constructeur et service après-vente professionnel</p>
              </div>

              {/* Feature 3 - Expérience */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-xl transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-2">
                <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-purple-100 to-purple-200 rounded-full mb-6 group-hover:from-purple-200 group-hover:to-purple-300 transition-all duration-300">
                  <Clock className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">10+ Ans d'Expérience</h3>
                <p className="text-gray-600 text-center leading-relaxed">Une décennie d'expertise au service de nos clients avec des milliers de projets réalisés</p>
              </div>

              {/* Feature 4 - Support */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-xl transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-2">
                <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-100 to-orange-200 rounded-full mb-6 group-hover:from-orange-200 group-hover:to-orange-300 transition-all duration-300">
                  <Users className="h-8 w-8 text-orange-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">Support Dédié</h3>
                <p className="text-gray-600 text-center leading-relaxed">Équipe technique qualifiée disponible pour vous accompagner avant, pendant et après votre achat</p>
              </div>
            </div>

            {/* Additional Features Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
              {/* Feature 5 - Installation */}
              <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Installation Professionnelle</h4>
                  <p className="text-gray-600 text-sm">Service d'installation et de configuration sur site par nos techniciens certifiés</p>
                </div>
              </div>

              {/* Feature 6 - Formation */}
              <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Globe className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Formation & Conseil</h4>
                  <p className="text-gray-600 text-sm">Formation de vos équipes et conseils personnalisés pour optimiser vos investissements</p>
                </div>
              </div>

              {/* Feature 7 - Maintenance */}
              <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <RefreshCw className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Maintenance Préventive</h4>
                  <p className="text-gray-600 text-sm">Contrats de maintenance pour assurer la performance optimale de vos équipements</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Products */}
        <FeaturedProducts />

        {/* Why Choose Us Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">Pourquoi choisir JOSNET NETWORK?</h2>
                <div className="w-24 h-1 bg-accent mb-8"></div>
                <p className="text-gray-700 mb-8">
                  Depuis plus de 10 ans, nous fournissons des solutions technologiques innovantes
                  à nos clients au Burundi. Notre engagement envers l'excellence et la satisfaction
                  client nous distingue de la concurrence.
                </p>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-accent mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-1">Expertise technique</h3>
                      <p className="text-gray-600">Notre équipe possède une expertise approfondie dans tous les domaines IT et télécom.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-accent mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-1">Solutions personnalisées</h3>
                      <p className="text-gray-600">Nous adaptons nos services pour répondre précisément à vos besoins spécifiques.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-accent mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-1">Service après-vente</h3>
                      <p className="text-gray-600">Un suivi continu pour assurer la performance optimale de vos systèmes.</p>
                    </div>
                  </div>
                </div>

                <Button
                  className="mt-8 bg-accent hover:bg-accent/90 text-white px-6 py-3 rounded-md flex items-center gap-2"
                >
                  Nos services
                  <ArrowRight size={16} />
                </Button>
              </div>

              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1573164713988-8665fc963095?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80"
                  alt="JOSNET Services"
                  className="rounded-lg shadow-xl object-cover w-full h-[500px]"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent rounded-lg flex items-end">
                  <div className="p-8 text-white">
                    <p className="text-2xl font-semibold">Technologies de pointe</p>
                    <p>Découvrez nos solutions innovantes</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section - Dynamic */}
        <DynamicTestimonials />

        {/* About Section */}
        <AboutSection />

        {/* Stats Section - Dynamic */}
        <DynamicStats />

        {/* CMS Pages Section */}
        {featuredPages && featuredPages.length > 0 && (
          <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100">
            <div className="container mx-auto px-4">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Pages du CMS Admin</h2>
                <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Découvrez les pages créées et gérées via notre CMS admin
                </p>
                <div className="mt-4">
                  <span className="inline-flex items-center gap-2 text-sm text-blue-600 bg-blue-100 px-3 py-1 rounded-full">
                    <Database className="h-4 w-4" />
                    {featuredPages.length} pages récupérées depuis le CMS admin
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {featuredPages.map((page: any) => (
                  <div key={page.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                    <div className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                      <div className="text-white text-center">
                        <div className="text-4xl mb-2">📄</div>
                        <div className="text-sm opacity-80">{page.template || 'Standard'}</div>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">
                          {page.title}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          page.status === 'published'
                            ? 'bg-green-100 text-green-700'
                            : 'bg-amber-100 text-amber-700'
                        }`}>
                          {page.status === 'published' ? 'Publié' : 'Brouillon'}
                        </span>
                      </div>
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {page.meta_description || page.content?.substring(0, 150) + '...' || 'Aucune description disponible'}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-gray-500">
                          <span className="bg-gray-100 px-2 py-1 rounded">/{page.slug}</span>
                        </div>
                        <div className="text-xs text-gray-400">
                          {page.updated_at ? new Date(page.updated_at).toLocaleDateString('fr-FR') : 'N/A'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="text-center mt-8">
                <Link to="/admin/cms">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 mx-auto">
                    <Database className="h-4 w-4" />
                    Gérer les pages dans le CMS admin
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </section>
        )}

        {/* Blog Section - Dynamic from CMS */}
        <BlogSection />

        {/* Locations Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Nos Bureaux</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Retrouvez-nous dans nos différents bureaux à travers le Burundi
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all group">
                <div className="h-48 overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1531273420860-7c755ee4a16a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
                    alt="Bujumbura"
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <MapPin className="h-6 w-6 text-accent mr-2" />
                    <h3 className="text-xl font-bold">Bureau Principal</h3>
                  </div>
                  <p className="mb-2">123 Avenue Principale</p>
                  <p className="mb-2">Bujumbura, Burundi</p>
                  <p>+257 12 345 678</p>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all group">
                <div className="h-48 overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1595841696677-6489ff3f8cd1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
                    alt="Gitega"
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <MapPin className="h-6 w-6 text-accent mr-2" />
                    <h3 className="text-xl font-bold">Gitega</h3>
                  </div>
                  <p className="mb-2">45 Rue du Commerce</p>
                  <p className="mb-2">Gitega, Burundi</p>
                  <p>+257 23 456 789</p>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all group">
                <div className="h-48 overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                    alt="Ngozi"
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <MapPin className="h-6 w-6 text-accent mr-2" />
                    <h3 className="text-xl font-bold">Ngozi</h3>
                  </div>
                  <p className="mb-2">78 Boulevard Central</p>
                  <p className="mb-2">Ngozi, Burundi</p>
                  <p>+257 34 567 890</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="container mx-auto px-4 text-center relative">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Transformez votre vision technologique en réalité
            </h2>
            <p className="text-xl max-w-3xl mx-auto mb-8 text-gray-300 leading-relaxed">
              Rejoignez plus de <span className="text-blue-400 font-semibold">1000+ entreprises</span> qui nous font confiance pour leurs solutions IT au Burundi.
              Découvrez comment JOSNET peut propulser votre business vers l'avenir.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link to="/contact">
                <Button
                  size="lg"
                  className="bg-primary hover:bg-primary/90 text-white px-8 py-4 text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Demander un devis gratuit
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/products">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-medium transition-all duration-300"
                >
                  Explorer nos produits
                  <ChevronRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                  <Users className="h-8 w-8 text-primary" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">1000+</div>
                <div className="text-gray-400">Clients satisfaits</div>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="h-8 w-8 text-primary" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">5000+</div>
                <div className="text-gray-400">Projets réalisés</div>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                  <Award className="h-8 w-8 text-primary" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">10+</div>
                <div className="text-gray-400">Années d'expérience</div>
              </div>
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-16 lg:py-24 bg-gray-50">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="max-w-2xl mx-auto"
            >
              <NewsletterSubscribe />
            </motion.div>
          </div>
        </section>


      </main>

      <Footer />
    </div>
  );
};

export default Index;
