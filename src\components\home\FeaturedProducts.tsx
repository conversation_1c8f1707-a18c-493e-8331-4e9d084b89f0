
import { useEffect, useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { ProductListItem, productApi } from "@/services/productApi";
import ProductCard from "../products/ProductCard";
import { Button } from "@/components/ui/button";
import { ArrowRight, RefreshCw } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

const FeaturedProducts = () => {
  const [activeCategory, setActiveCategory] = useState("Tous");

  // Fetch products using the SAME API as ProductDetail (this works!)
  const {
    data: productsResponse,
    isLoading: isLoadingProducts,
    isError: isProductsError,
    refetch: refetchProducts
  } = useQuery({
    queryKey: ['products-featured'],
    queryFn: () => productApi.getProducts({
      is_featured: true,
      page_size: 12,
      status: 'published'
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Extract products from response
  const featuredProducts = productsResponse?.results || [];

  // Get unique categories from products
  const categories = useMemo(() => {
    const uniqueCategories = new Set<string>();
    uniqueCategories.add("Tous");

    featuredProducts.forEach(product => {
      if (product.categories && product.categories.length > 0) {
        product.categories.forEach(cat => uniqueCategories.add(cat.name));
      }
    });

    return Array.from(uniqueCategories);
  }, [featuredProducts]);

  // Filter products by category - NO TRANSFORMATION NEEDED!
  // Products are already in the correct ProductListItem format from productApi
  const filteredProducts: ProductListItem[] = useMemo(() => {
    if (activeCategory === "Tous") {
      return featuredProducts;
    } else {
      return featuredProducts.filter(product =>
        product.categories && product.categories.some(cat => cat.name === activeCategory)
      );
    }
  }, [activeCategory, featuredProducts]);

  // Debug logging
  useEffect(() => {
    if (featuredProducts.length > 0) {
      console.log('🏠 Featured Products (using productApi):', featuredProducts[0]);
      console.log('🖼️ First product primary_image:', featuredProducts[0].primary_image);
      // console.log('🖼️ First product images:', featuredProducts[0].images); // This would cause a TS error as `images` is not in ProductListItem
    }
  }, [featuredProducts]);

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Nos produits populaires</h2>
          <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Découvrez notre sélection de produits technologiques de haute qualité, choisis pour
            leur performance exceptionnelle et leur fiabilité.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap items-center justify-center gap-3 mb-12">
          {categories.map((category) => (
            <Button
              key={category}
              variant={activeCategory === category ? "default" : "outline"}
              className={
                activeCategory === category
                ? "bg-primary hover:bg-primary-dark text-white"
                : "text-gray-700 hover:text-primary border-gray-200"
              }
              onClick={() => setActiveCategory(category)}
              disabled={isLoadingProducts}
            >
              {category}
            </Button>
          ))}

          {/* Refresh button */}
          <Button
            variant="outline"
            size="icon"
            onClick={() => refetchProducts()}
            disabled={isLoadingProducts}
            className="ml-2"
            title="Actualiser les produits"
          >
            <RefreshCw className={`h-4 w-4 ${isLoadingProducts ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Products Grid */}
        {isLoadingProducts ? (
          <Carousel
            opts={{ align: "start", loop: true }}
            className="w-full"
          >
            <CarouselContent>
              {[...Array(8)].map((_, i) => (
                <CarouselItem key={i} className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4">
                  <div className="p-1">
                    <div className="space-y-4">
                      <Skeleton className="h-48 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                      <Skeleton className="h-8 w-full" />
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white" />
            <CarouselNext className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white" />
          </Carousel>
        ) : isProductsError ? (
          <div className="text-center py-12">
            <div className="text-red-500 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur de chargement</h3>
            <p className="text-gray-600 mb-4">Impossible de charger les produits en vedette.</p>
            <Button onClick={() => refetchProducts()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Réessayer
            </Button>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun produit trouvé</h3>
            <p className="text-gray-600">
              {activeCategory === "Tous"
                ? "Aucun produit en vedette disponible pour le moment."
                : `Aucun produit trouvé dans la catégorie "${activeCategory}".`
              }
            </p>
          </div>
        ) : (
          <Carousel
            opts={{ align: "start", loop: filteredProducts.length > 4 }}
            className="w-full"
          >
            <CarouselContent className="-ml-4">
              {filteredProducts.map((product) => (
                <CarouselItem key={product.id} className="pl-4 md:basis-1/2 lg:basis-1/3 xl:basis-1/4">
                  <ProductCard product={product} />
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white" />
            <CarouselNext className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white" />
          </Carousel>
        )}

        {/* View All Button */}
        <div className="text-center mt-16">
          <Button
            size="lg"
            className="bg-primary hover:bg-primary-dark text-white px-8 py-6 rounded-md flex items-center gap-2 mx-auto"
          >
            Voir tous les produits
            <ArrowRight size={18} />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
