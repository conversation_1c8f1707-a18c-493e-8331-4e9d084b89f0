from django.db import models
from django.conf import settings
from django.db.models import F, Sum, Count, Avg
from products.models import Product, ProductVariant
from datetime import datetime, timedelta

class ProductView(models.Model):
    """
    Model for tracking product views.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='views')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    referrer = models.URLField(blank=True, null=True)
    device_type = models.CharField(max_length=20, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"View of {self.product.name} at {self.created_at}"

class CartAction(models.Model):
    """
    Model for tracking cart actions (add, remove, update).
    """
    ACTION_CHOICES = [
        ('add', 'Add to Cart'),
        ('remove', 'Remove from Cart'),
        ('update', 'Update Quantity'),
    ]
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='cart_actions')
    variant = models.ForeignKey(ProductVariant, on_delete=models.SET_NULL, null=True, blank=True, related_name='cart_actions')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True, null=True)
    action = models.CharField(max_length=10, choices=ACTION_CHOICES)
    quantity = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.get_action_display()} {self.quantity} of {self.product.name}"

class WishlistAction(models.Model):
    """
    Model for tracking wishlist actions (add, remove).
    """
    ACTION_CHOICES = [
        ('add', 'Add to Wishlist'),
        ('remove', 'Remove from Wishlist'),
    ]
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='wishlist_actions')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    action = models.CharField(max_length=10, choices=ACTION_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.get_action_display()} {self.product.name}"

class SalesData(models.Model):
    """
    Model for tracking sales data.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='sales_data')
    variant = models.ForeignKey(ProductVariant, on_delete=models.SET_NULL, null=True, blank=True, related_name='sales_data')
    order_id = models.CharField(max_length=100)
    customer_id = models.PositiveIntegerField()
    quantity = models.PositiveIntegerField()
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['product', 'date']),
            models.Index(fields=['customer_id', 'date']),
        ]
    
    def __str__(self):
        return f"Sale of {self.quantity} {self.product.name} on {self.date}"
    
    @classmethod
    def get_sales_by_period(cls, product_id, period='month', start_date=None, end_date=None):
        """
        Get sales data aggregated by period (day, week, month).
        """
        if not start_date:
            if period == 'day':
                start_date = datetime.now() - timedelta(days=14)
            elif period == 'week':
                start_date = datetime.now() - timedelta(weeks=12)
            else:  # month
                start_date = datetime.now() - timedelta(days=365)
        
        if not end_date:
            end_date = datetime.now()
        
        queryset = cls.objects.filter(
            product_id=product_id,
            date__gte=start_date.date(),
            date__lte=end_date.date()
        )
        
        if period == 'day':
            return queryset.values('date').annotate(
                period=F('date'),
                quantity=Sum('quantity'),
                revenue=Sum('total_price')
            ).order_by('date')
        elif period == 'week':
            return queryset.extra(select={'week': "EXTRACT(WEEK FROM date)"}).values('week').annotate(
                period=F('week'),
                quantity=Sum('quantity'),
                revenue=Sum('total_price')
            ).order_by('week')
        else:  # month
            return queryset.extra(select={'month': "EXTRACT(YEAR FROM date) || '-' || EXTRACT(MONTH FROM date)"}).values('month').annotate(
                period=F('month'),
                quantity=Sum('quantity'),
                revenue=Sum('total_price')
            ).order_by('month')
