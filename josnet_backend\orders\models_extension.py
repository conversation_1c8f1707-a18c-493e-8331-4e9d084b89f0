from django.db import models
from django.conf import settings
from .models import Order, OrderItem
import uuid

class ReturnRequest(models.Model):
    """
    Model for customer return requests.
    """
    STATUS_CHOICES = [
        ('pending', 'En attente'),
        ('approved', 'Approuvé'),
        ('rejected', 'Rejeté'),
        ('processing', 'En traitement'),
        ('completed', 'Terminé'),
    ]

    REASON_CHOICES = [
        ('defective', 'Article défectueux'),
        ('wrong_item', 'Article incorrect'),
        ('size_issue', 'Problème de taille'),
        ('not_as_described', 'Ne correspond pas à la description'),
        ('damaged', 'Endommagé à la livraison'),
        ('other', 'Autre raison'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='return_requests')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='return_requests')
    return_number = models.CharField(max_length=50, unique=True, editable=False)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reason = models.CharField(max_length=50, choices=REASON_CHOICES)
    additional_info = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Return Request'
        verbose_name_plural = 'Return Requests'

    def __str__(self):
        return f"Return {self.return_number} - {self.get_status_display()}"

    def save(self, *args, **kwargs):
        if not self.return_number:
            self.return_number = self.generate_return_number()
        super().save(*args, **kwargs)

    def generate_return_number(self):
        """Generate a unique return number."""
        prefix = 'RET'
        unique_id = str(uuid.uuid4().int)[:8]
        return f"{prefix}-{unique_id}"

class ReturnItem(models.Model):
    """
    Model for items in a return request.
    """
    return_request = models.ForeignKey(ReturnRequest, on_delete=models.CASCADE, related_name='items')
    order_item = models.ForeignKey(OrderItem, on_delete=models.SET_NULL, null=True, related_name='return_items')
    product_name = models.CharField(max_length=255)
    quantity = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.CharField(max_length=50, choices=ReturnRequest.REASON_CHOICES)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product_name} ({self.quantity}) - {self.get_reason_display()}"

class Claim(models.Model):
    """
    Model for customer claims/support tickets.
    """
    STATUS_CHOICES = [
        ('new', 'Nouveau'),
        ('in_progress', 'En cours'),
        ('waiting_customer', 'En attente du client'),
        ('waiting_staff', 'En attente du support'),
        ('resolved', 'Résolu'),
        ('closed', 'Fermé'),
    ]

    SUBJECT_CHOICES = [
        ('order_issue', 'Problème avec une commande'),
        ('product_issue', 'Problème avec un produit'),
        ('shipping_issue', 'Problème de livraison'),
        ('account_issue', 'Problème de compte'),
        ('website_issue', 'Problème avec le site web'),
        ('other', 'Autre'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    claim_number = models.CharField(max_length=50, unique=True, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='claims')
    order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, blank=True, related_name='claims')
    subject = models.CharField(max_length=50, choices=SUBJECT_CHOICES)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_update = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Claim'
        verbose_name_plural = 'Claims'

    def __str__(self):
        return f"Claim {self.claim_number} - {self.get_status_display()}"

    def save(self, *args, **kwargs):
        if not self.claim_number:
            self.claim_number = self.generate_claim_number()
        super().save(*args, **kwargs)

    def generate_claim_number(self):
        """Generate a unique claim number."""
        prefix = 'REC'
        unique_id = str(uuid.uuid4().int)[:8]
        return f"{prefix}-{unique_id}"

class ClaimMessage(models.Model):
    """
    Model for messages in a claim.
    """
    SENDER_CHOICES = [
        ('customer', 'Client'),
        ('support', 'Support'),
        ('system', 'Système'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    claim = models.ForeignKey(Claim, on_delete=models.CASCADE, related_name='messages')
    sender = models.CharField(max_length=10, choices=SENDER_CHOICES)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.get_sender_display()} on {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class ClaimAttachment(models.Model):
    """
    Model for attachments in claim messages.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(ClaimMessage, on_delete=models.CASCADE, related_name='attachments')
    file = models.FileField(upload_to='message_attachments/')
    file_name = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()  # Size in bytes
    content_type = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file_name

class ClaimStatusHistory(models.Model):
    """
    Model for tracking claim status changes.
    """
    claim = models.ForeignKey(Claim, on_delete=models.CASCADE, related_name='status_history')
    old_status = models.CharField(max_length=20, choices=Claim.STATUS_CHOICES)
    new_status = models.CharField(max_length=20, choices=Claim.STATUS_CHOICES)
    comment = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = 'Claim status histories'

    def __str__(self):
        return f"{self.claim.claim_number}: {self.get_old_status_display()} → {self.get_new_status_display()}"
