import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Mail, Users, TrendingUp, Send, Eye, MousePointer, Calendar, BarChart3 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import subscriptionApi from '@/services/subscriptionApi';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

const NewsletterDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('stats');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Récupérer les statistiques
  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['newsletter-stats'],
    queryFn: () => subscriptionApi.getStats(),
  });

  // Récupérer l'historique des newsletters
  const { data: history, isLoading: isLoadingHistory } = useQuery({
    queryKey: ['newsletter-history'],
    queryFn: () => subscriptionApi.getNewsletterHistory(),
  });

  // État pour l'envoi de newsletter
  const [newsletterForm, setNewsletterForm] = useState({
    subject: '',
    content: '',
    type: 'newsletter' as 'promotion' | 'newsletter' | 'announcement',
    target_preferences: [] as string[],
  });

  // Mutation pour envoyer une newsletter
  const sendNewsletterMutation = useMutation({
    mutationFn: (data: typeof newsletterForm) => subscriptionApi.sendNewsletter(data),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['newsletter-history'] });
      queryClient.invalidateQueries({ queryKey: ['newsletter-stats'] });
      toast({
        title: 'Newsletter envoyée !',
        description: `Newsletter envoyée à ${result.sent_count} abonnés.`,
      });
      setNewsletterForm({
        subject: '',
        content: '',
        type: 'newsletter',
        target_preferences: [],
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Impossible d\'envoyer la newsletter.',
        variant: 'destructive',
      });
    },
  });

  // Gérer l'envoi de newsletter
  const handleSendNewsletter = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newsletterForm.subject || !newsletterForm.content) {
      toast({
        title: 'Champs requis',
        description: 'Veuillez remplir le sujet et le contenu.',
        variant: 'destructive',
      });
      return;
    }

    sendNewsletterMutation.mutate(newsletterForm);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Gestion Newsletter</h1>
        <p className="text-gray-600">Gérez les abonnements et envoyez des newsletters</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="stats">Statistiques</TabsTrigger>
          <TabsTrigger value="send">Envoyer Newsletter</TabsTrigger>
          <TabsTrigger value="history">Historique</TabsTrigger>
        </TabsList>

        <TabsContent value="stats" className="space-y-6">
          {/* Statistiques principales */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {isLoadingStats ? (
              Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <Skeleton className="h-8 w-8 mb-2" />
                    <Skeleton className="h-6 w-20 mb-1" />
                    <Skeleton className="h-4 w-32" />
                  </CardContent>
                </Card>
              ))
            ) : (
              <>
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Abonnés</p>
                        <p className="text-2xl font-bold">{stats?.total_subscribers || 0}</p>
                      </div>
                      <Users className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Abonnés Actifs</p>
                        <p className="text-2xl font-bold">{stats?.active_subscribers || 0}</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Nouveaux (7j)</p>
                        <p className="text-2xl font-bold">{stats?.recent_subscriptions || 0}</p>
                      </div>
                      <Calendar className="h-8 w-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Taux Désabonnement</p>
                        <p className="text-2xl font-bold">{stats?.unsubscribe_rate || 0}%</p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>

          {/* Répartition par préférences */}
          {stats && (
            <Card>
              <CardHeader>
                <CardTitle>Répartition par Préférences</CardTitle>
                <CardDescription>
                  Nombre d'abonnés par type de contenu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {stats.preferences_stats?.promotions || 0}
                    </div>
                    <div className="text-sm text-red-700">Promotions</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {stats.preferences_stats?.new_products || 0}
                    </div>
                    <div className="text-sm text-blue-700">Nouveaux Produits</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {stats.preferences_stats?.newsletters || 0}
                    </div>
                    <div className="text-sm text-green-700">Newsletters</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {stats.preferences_stats?.order_updates || 0}
                    </div>
                    <div className="text-sm text-purple-700">Commandes</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="send" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Envoyer une Newsletter</CardTitle>
              <CardDescription>
                Créez et envoyez une newsletter à vos abonnés
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSendNewsletter} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject">Sujet</Label>
                    <Input
                      id="subject"
                      placeholder="Sujet de la newsletter"
                      value={newsletterForm.subject}
                      onChange={(e) => setNewsletterForm(prev => ({ ...prev, subject: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Type</Label>
                    <Select
                      value={newsletterForm.type}
                      onValueChange={(value: 'promotion' | 'newsletter' | 'announcement') => 
                        setNewsletterForm(prev => ({ ...prev, type: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="newsletter">Newsletter</SelectItem>
                        <SelectItem value="promotion">Promotion</SelectItem>
                        <SelectItem value="announcement">Annonce</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="content">Contenu</Label>
                  <Textarea
                    id="content"
                    placeholder="Contenu de la newsletter (HTML supporté)"
                    rows={10}
                    value={newsletterForm.content}
                    onChange={(e) => setNewsletterForm(prev => ({ ...prev, content: e.target.value }))}
                    required
                  />
                </div>

                <Button 
                  type="submit" 
                  disabled={sendNewsletterMutation.isPending}
                  className="w-full"
                >
                  {sendNewsletterMutation.isPending ? (
                    <>Envoi en cours...</>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Envoyer la Newsletter
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Historique des Newsletters</CardTitle>
              <CardDescription>
                Historique des newsletters envoyées
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingHistory ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <Skeleton className="h-12 w-12 rounded" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-[250px]" />
                        <Skeleton className="h-4 w-[200px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : history && history.length > 0 ? (
                <div className="space-y-4">
                  {history.map((newsletter) => (
                    <div key={newsletter.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Mail className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">{newsletter.subject}</h4>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>
                              {formatDistanceToNow(new Date(newsletter.sent_at), { 
                                addSuffix: true, 
                                locale: fr 
                              })}
                            </span>
                            <Badge variant="secondary">{newsletter.type}</Badge>
                            <span>{newsletter.sent_count} destinataires</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        {newsletter.open_rate && (
                          <div className="flex items-center space-x-1">
                            <Eye className="h-4 w-4" />
                            <span>{newsletter.open_rate}%</span>
                          </div>
                        )}
                        {newsletter.click_rate && (
                          <div className="flex items-center space-x-1">
                            <MousePointer className="h-4 w-4" />
                            <span>{newsletter.click_rate}%</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Mail className="h-12 w-12 mx-auto text-gray-300 mb-3" />
                  <h3 className="text-lg font-medium mb-1">Aucune newsletter envoyée</h3>
                  <p className="text-gray-500">
                    Commencez par envoyer votre première newsletter !
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NewsletterDashboard;
