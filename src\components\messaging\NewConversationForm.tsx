import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  MessageSquare,
  User,
  Tag,
  FolderOpen,
  AlertTriangle,
  Package,
  Upload,
  X,
  Plus,
  Search,
  Mail,
  Phone,
  UserPlus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import messagingApi, { CreateConversationRequest, Category, Tag as TagType } from '@/services/messagingApi';
import customerA<PERSON>, { CustomerSearchResult } from '@/services/customerApi';
import notificationService from '@/services/notificationService';
import FileUpload from './FileUpload';
import ConversationFormHelp from './ConversationFormHelp';

interface NewConversationFormProps {
  onSuccess?: (conversation: any) => void;
  onCancel?: () => void;
}



const NewConversationForm: React.FC<NewConversationFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Form state
  const [formData, setFormData] = useState<CreateConversationRequest>({
    subject: '',
    initial_message: '',
    customer_email: '', // Email du client (sera rempli automatiquement)
    uploaded_files: [],
    category_id: undefined,
    tag_ids: [],
    order_id: undefined,
    priority: 'medium'
  });

  // Customer search state
  const [customerEmail, setCustomerEmail] = useState('');
  const [customerSearchResults, setCustomerSearchResults] = useState<CustomerSearchResult[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerSearchResult | null>(null);
  const [isSearchingCustomer, setIsSearchingCustomer] = useState(false);
  const [showNewCustomerForm, setShowNewCustomerForm] = useState(false);

  // New customer form state
  const [newCustomerData, setNewCustomerData] = useState({
    email: '',
    full_name: '',
    phone: ''
  });

  // Fetch categories
  const { data: categories = [] } = useQuery({
    queryKey: ['categories'],
    queryFn: messagingApi.getCategories
  });

  // Fetch tags
  const { data: tags = [] } = useQuery({
    queryKey: ['tags'],
    queryFn: messagingApi.getTags
  });

  // Create customer mutation
  const createCustomerMutation = useMutation({
    mutationFn: customerApi.createCustomer,
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Impossible de créer le client.",
        variant: "destructive",
      });
    }
  });

  // Create conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: async (data: CreateConversationRequest) => {
      // Déterminer l'email du client
      let clientEmail = '';
      let clientName = '';
      let clientPhone = '';

      if (showNewCustomerForm && newCustomerData.email) {
        // Nouveau client
        clientEmail = newCustomerData.email;
        clientName = newCustomerData.full_name;
        clientPhone = newCustomerData.phone;

        // Créer le client d'abord
        try {
          await createCustomerMutation.mutateAsync({
            email: newCustomerData.email,
            full_name: newCustomerData.full_name,
            phone: newCustomerData.phone
          });
        } catch (error) {
          console.warn('Customer creation failed, proceeding with conversation creation');
        }
      } else if (selectedCustomer) {
        // Client existant sélectionné
        clientEmail = selectedCustomer.email;
        clientName = selectedCustomer.full_name;
        clientPhone = selectedCustomer.phone || '';
      } else if (customerEmail) {
        // Email saisi manuellement
        clientEmail = customerEmail;
      }

      // Vérifier que nous avons un email client
      if (!clientEmail) {
        throw new Error('Email du client requis');
      }

      // Créer la conversation avec toutes les informations
      const conversationData: CreateConversationRequest = {
        ...data,
        customer_email: clientEmail,
        customer_name: clientName,
        customer_phone: clientPhone
      };

      return messagingApi.createConversation(conversationData);
    },
    onSuccess: async (conversation) => {
      // Envoyer une notification email au client
      try {
        const clientEmail = selectedCustomer?.email || newCustomerData.email || customerEmail;
        const clientName = selectedCustomer?.full_name || newCustomerData.full_name || 'Client';
        const clientPhone = selectedCustomer?.phone || newCustomerData.phone;

        if (clientEmail) {
          console.log('📧 Envoi de notification email au client:', clientEmail);

          // Envoyer notification multi-canal (email + SMS si disponible)
          const notificationResult = await notificationService.notifyCustomerMultiChannel(
            clientEmail,
            clientName,
            clientPhone,
            formData.subject,
            formData.initial_message,
            conversation.id,
            'Équipe Support JosNet'
          );

          if (notificationResult.email.success) {
            console.log('✅ Email envoyé avec succès au client');
          } else {
            console.warn('⚠️ Échec envoi email:', notificationResult.email.message);
          }

          if (notificationResult.sms?.success) {
            console.log('✅ SMS envoyé avec succès au client');
          }
        }
      } catch (error) {
        console.error('❌ Erreur lors de l\'envoi de notification:', error);
        // Ne pas bloquer le processus si la notification échoue
      }

      toast({
        title: "Conversation créée",
        description: "La nouvelle conversation a été créée avec succès. Le client a été notifié par email.",
      });

      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      onSuccess?.(conversation);
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Impossible de créer la conversation.",
        variant: "destructive",
      });
    }
  });

  // Customer search function using the API
  const searchCustomers = async (query: string) => {
    setIsSearchingCustomer(true);
    try {
      const results = await customerApi.searchCustomers(query);
      setCustomerSearchResults(results);
    } catch (error) {
      console.error('Error searching customers:', error);
      toast({
        title: "Erreur",
        description: "Impossible de rechercher les clients.",
        variant: "destructive",
      });
    } finally {
      setIsSearchingCustomer(false);
    }
  };

  // Handle customer email change
  const handleCustomerEmailChange = (value: string) => {
    setCustomerEmail(value);
    setSelectedCustomer(null);
    
    if (value.length >= 3) {
      searchCustomers(value);
    } else {
      setCustomerSearchResults([]);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.subject.trim()) {
      toast({
        title: "Erreur",
        description: "Le sujet est requis.",
        variant: "destructive",
      });
      return;
    }
    
    if (!formData.initial_message.trim()) {
      toast({
        title: "Erreur",
        description: "Le message initial est requis.",
        variant: "destructive",
      });
      return;
    }
    
    // Validation de l'email client
    const hasValidCustomer = selectedCustomer ||
                            (showNewCustomerForm && newCustomerData.email) ||
                            customerEmail;

    if (!hasValidCustomer) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un client existant ou créer un nouveau client avec un email valide.",
        variant: "destructive",
      });
      return;
    }

    // Validation spécifique pour nouveau client
    if (showNewCustomerForm) {
      if (!newCustomerData.email || !newCustomerData.full_name) {
        toast({
          title: "Erreur",
          description: "L'email et le nom complet sont requis pour créer un nouveau client.",
          variant: "destructive",
        });
        return;
      }
    }
    
    createConversationMutation.mutate(formData);
  };

  // Handle tag selection
  const handleTagToggle = (tagId: number) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids?.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...(prev.tag_ids || []), tagId]
    }));
  };

  // Handle file upload
  const handleFileUpload = (files: File[]) => {
    setFormData(prev => ({
      ...prev,
      uploaded_files: files
    }));
  };

  // Handle file remove
  const handleFileRemove = (_fileId: string) => {
    // This is handled internally by the FileUpload component
    // We'll update our files when onFilesSelected is called again
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <User className="h-5 w-5 mr-2" />
            Client
          </CardTitle>
          <CardDescription>
            Recherchez un client existant ou créez-en un nouveau
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!showNewCustomerForm ? (
            <>
              {/* Customer Search */}
              <div className="space-y-2">
                <Label htmlFor="customer-email">Email du client</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="customer-email"
                    type="email"
                    placeholder="Rechercher par email ou nom..."
                    value={customerEmail}
                    onChange={(e) => handleCustomerEmailChange(e.target.value)}
                    className="pl-10 pr-10"
                  />
                  {isSearchingCustomer && (
                    <div className="absolute right-3 top-3">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    </div>
                  )}
                </div>
              </div>

              {/* Search Results */}
              {customerSearchResults.length > 0 && (
                <div className="space-y-2">
                  <Label>Clients trouvés</Label>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {customerSearchResults.map((customer) => (
                      <div
                        key={customer.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedCustomer?.id === customer.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => {
                          setSelectedCustomer(customer);
                          setCustomerEmail(customer.email);
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{customer.full_name}</div>
                            <div className="text-sm text-gray-500">{customer.email}</div>
                            {customer.phone && (
                              <div className="text-sm text-gray-500 flex items-center">
                                <Phone className="h-3 w-3 mr-1" />
                                {customer.phone}
                              </div>
                            )}
                          </div>
                          <div className="text-right text-sm text-gray-500">
                            <div>{customer.total_orders} commandes</div>
                            <div>Total: {customer.total_spent} FBU</div>
                            {customer.last_order && (
                              <div>Dernière: {customer.last_order}</div>
                            )}
                            <div className="flex items-center mt-1">
                              <div
                                className={`w-2 h-2 rounded-full mr-1 ${
                                  customer.is_active ? 'bg-green-500' : 'bg-gray-400'
                                }`}
                              ></div>
                              <span className="text-xs">
                                {customer.is_active ? 'Actif' : 'Inactif'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* New Customer Button */}
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowNewCustomerForm(true)}
                className="w-full"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Créer un nouveau client
              </Button>
            </>
          ) : (
            <>
              {/* New Customer Form */}
              <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Nouveau client</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowNewCustomerForm(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-customer-email">Email *</Label>
                    <Input
                      id="new-customer-email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newCustomerData.email}
                      onChange={(e) => setNewCustomerData(prev => ({
                        ...prev,
                        email: e.target.value
                      }))}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="new-customer-name">Nom complet *</Label>
                    <Input
                      id="new-customer-name"
                      placeholder="Jean Dupont"
                      value={newCustomerData.full_name}
                      onChange={(e) => setNewCustomerData(prev => ({
                        ...prev,
                        full_name: e.target.value
                      }))}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="new-customer-phone">Téléphone</Label>
                    <Input
                      id="new-customer-phone"
                      placeholder="+257 79 123 456"
                      value={newCustomerData.phone}
                      onChange={(e) => setNewCustomerData(prev => ({
                        ...prev,
                        phone: e.target.value
                      }))}
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Conversation Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <MessageSquare className="h-5 w-5 mr-2" />
            Détails de la conversation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">Sujet *</Label>
            <Input
              id="subject"
              placeholder="Ex: Question sur la commande #12345"
              value={formData.subject}
              onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
              required
            />
          </div>

          {/* Initial Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Message initial *</Label>
            <Textarea
              id="message"
              placeholder="Tapez le message initial de la conversation..."
              value={formData.initial_message}
              onChange={(e) => setFormData(prev => ({ ...prev, initial_message: e.target.value }))}
              rows={4}
              required
            />
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priorité</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner une priorité" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                    Faible
                  </div>
                </SelectItem>
                <SelectItem value="medium">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                    Moyenne
                  </div>
                </SelectItem>
                <SelectItem value="high">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-orange-500 mr-2"></div>
                    Élevée
                  </div>
                </SelectItem>
                <SelectItem value="urgent">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                    Urgente
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Category and Tags */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <FolderOpen className="h-5 w-5 mr-2" />
            Catégorie et étiquettes
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Catégorie</Label>
            <Select
              value={formData.category_id?.toString()}
              onValueChange={(value) => setFormData(prev => ({ 
                ...prev, 
                category_id: value ? parseInt(value) : undefined 
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner une catégorie" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category: Category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    <div className="flex items-center">
                      <div 
                        className="w-3 h-3 rounded-full mr-2" 
                        style={{ backgroundColor: category.color }}
                      ></div>
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Étiquettes</Label>
            <div className="flex flex-wrap gap-2">
              {tags.map((tag: TagType) => (
                <Badge
                  key={tag.id}
                  variant={formData.tag_ids?.includes(tag.id) ? "default" : "outline"}
                  className="cursor-pointer"
                  style={{
                    backgroundColor: formData.tag_ids?.includes(tag.id) ? tag.color : 'transparent',
                    borderColor: tag.color,
                    color: formData.tag_ids?.includes(tag.id) ? 'white' : tag.color
                  }}
                  onClick={() => handleTagToggle(tag.id)}
                >
                  <Tag className="h-3 w-3 mr-1" />
                  {tag.name}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Upload className="h-5 w-5 mr-2" />
            Pièces jointes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <FileUpload
            onFilesSelected={handleFileUpload}
            onFileRemove={handleFileRemove}
            maxFiles={5}
            maxFileSize={10} // 10MB
            acceptedTypes={['image/*', 'application/pdf', '.doc', '.docx', '.txt', '.zip']}
          />
        </CardContent>
      </Card>

      {/* Order Reference */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Package className="h-5 w-5 mr-2" />
            Référence commande (optionnel)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="order-id">Numéro de commande</Label>
            <Input
              id="order-id"
              type="number"
              placeholder="Ex: 12345"
              value={formData.order_id || ''}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                order_id: e.target.value ? parseInt(e.target.value) : undefined 
              }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Summary Preview */}
      {(selectedCustomer || newCustomerData.email || customerEmail) && formData.subject && (
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-lg text-blue-800">Résumé de la conversation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-blue-700">Client:</span>
                <div className="text-blue-600">
                  {selectedCustomer ? (
                    <>
                      <div>{selectedCustomer.full_name}</div>
                      <div>{selectedCustomer.email}</div>
                    </>
                  ) : newCustomerData.email ? (
                    <>
                      <div>{newCustomerData.full_name || 'Nouveau client'}</div>
                      <div>{newCustomerData.email}</div>
                    </>
                  ) : (
                    <div>{customerEmail}</div>
                  )}
                </div>
              </div>

              <div>
                <span className="font-medium text-blue-700">Sujet:</span>
                <div className="text-blue-600">{formData.subject}</div>
              </div>

              {formData.category_id && (
                <div>
                  <span className="font-medium text-blue-700">Catégorie:</span>
                  <div className="text-blue-600">
                    {categories.find(c => c.id === formData.category_id)?.name}
                  </div>
                </div>
              )}

              <div>
                <span className="font-medium text-blue-700">Priorité:</span>
                <div className="text-blue-600 capitalize">{formData.priority}</div>
              </div>
            </div>

            {formData.tag_ids && formData.tag_ids.length > 0 && (
              <div>
                <span className="font-medium text-blue-700">Étiquettes:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {formData.tag_ids.map(tagId => {
                    const tag = tags.find(t => t.id === tagId);
                    return tag ? (
                      <Badge key={tag.id} variant="outline" className="text-xs">
                        {tag.name}
                      </Badge>
                    ) : null;
                  })}
                </div>
              </div>
            )}

            {formData.uploaded_files && formData.uploaded_files.length > 0 && (
              <div>
                <span className="font-medium text-blue-700">Pièces jointes:</span>
                <div className="text-blue-600">{formData.uploaded_files.length} fichier(s)</div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={createConversationMutation.isPending || createCustomerMutation.isPending}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={createConversationMutation.isPending || createCustomerMutation.isPending}
        >
          {createConversationMutation.isPending || createCustomerMutation.isPending ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {createCustomerMutation.isPending ? 'Création du client...' : 'Création...'}
            </>
          ) : (
            <>
              <Plus className="h-4 w-4 mr-2" />
              Créer la conversation
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

export default NewConversationForm;
