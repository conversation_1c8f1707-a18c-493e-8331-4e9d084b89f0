import { useState, useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import AccountLayout from "@/components/account/AccountLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Package2,
  FileCheck,
  Clock,
  AlertCircle,
  Loader2,
  RefreshCw,
  Filter,
  MessageSquare,
  ExternalLink
} from "lucide-react";
import { accountApi } from "@/services/accountApi";
import { useToast } from "@/hooks/use-toast";
import { ReturnFilters, ReturnFiltersState } from "@/components/account/returns/ReturnFilters";
import { ClaimFilters, ClaimFiltersState } from "@/components/account/claims/ClaimFilters";
import { CreateReturnForm } from "@/components/account/returns/CreateReturnForm";
import { CreateClaimForm } from "@/components/account/claims/CreateClaimForm";
import { PaginationControls } from "@/components/common/PaginationControls";

const Returns = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient(); // Initialiser queryClient pour l'invalidation du cache
  const [activeTab, setActiveTab] = useState("returns");
  const [isCreateReturnOpen, setIsCreateReturnOpen] = useState(false);
  const [isCreateClaimOpen, setIsCreateClaimOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [claimsCurrentPage, setClaimsCurrentPage] = useState(1);
  const [returnFilters, setReturnFilters] = useState<ReturnFiltersState>({
    orderId: '',
    status: 'all',
    dateFrom: undefined,
    dateTo: undefined,
  });
  
  const [claimFilters, setClaimFilters] = useState<ClaimFiltersState>({
    orderId: '',
    status: 'all',
  });

  // Fonction pour vérifier l'authentification de manière cohérente
  const getAuthToken = () => {
    return localStorage.getItem('accessToken') || 
           localStorage.getItem('authToken') || 
           localStorage.getItem('token') || 
           sessionStorage.getItem('accessToken') || 
           sessionStorage.getItem('authToken');
  };
  
  // Vérifier si l'utilisateur est authentifié
  const isAuthenticated = !!getAuthToken();
  
  console.log('Statut d\'authentification dans Returns.tsx:', isAuthenticated ? 'Authentifié' : 'Non authentifié');

  // Fetch returns data
  const {
    data: returnsData = [],
    isLoading: isLoadingReturns,
    isError: isErrorReturns,
    refetch: refetchReturns
  } = useQuery({
    queryKey: ['returns', returnFilters],
    queryFn: () => accountApi.getReturns(returnFilters.status),
    retry: 1,
    retryDelay: 1000,
    enabled: isAuthenticated, // Désactiver la requête si l'utilisateur n'est pas authentifié
  });

  // Fetch claims data
  const {
    data: claimsData = [],
    isLoading: isLoadingClaims,
    isError: isErrorClaims,
    refetch: refetchClaims
  } = useQuery({
    enabled: isAuthenticated, // Désactiver la requête si l'utilisateur n'est pas authentifié
    queryKey: ['claims', claimFilters],
    queryFn: () => accountApi.getClaims(claimFilters.status),
    retry: 1,
    retryDelay: 1000,
  });

  // Pagination settings
  const itemsPerPage = 5;
  
  // Filtrer et paginer les données de réclamations
  const filteredClaims = useMemo(() => {
    return claimsData.filter(item => {
      // Filtrer par ID de commande
      if (claimFilters.orderId && !item.orderId.toLowerCase().includes(claimFilters.orderId.toLowerCase())) {
        return false;
      }

      // Filtrer par statut (ignorer si 'all' est sélectionné)
      if (claimFilters.status && claimFilters.status !== 'all' && item.status !== claimFilters.status) {
        return false;
      }

      return true;
    });
  }, [claimsData, claimFilters]);
  
  // Calculer la pagination pour les réclamations
  const totalClaimPages = Math.max(1, Math.ceil(filteredClaims.length / itemsPerPage));
  const paginatedClaims = useMemo(() => {
    const startIndex = (claimsCurrentPage - 1) * itemsPerPage;
    return filteredClaims.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredClaims, claimsCurrentPage, itemsPerPage]);

  // Filter and paginate returns data
  const filteredReturns = useMemo(() => {
    return returnsData.filter(item => {
      // Filter by order ID
      if (returnFilters.orderId && !item.orderId.toLowerCase().includes(returnFilters.orderId.toLowerCase())) {
        return false;
      }

      // Filter by status (ignorer si 'all' est sélectionné)
      if (returnFilters.status && returnFilters.status !== 'all' && item.status !== returnFilters.status) {
        return false;
      }

      // Filter by date range (simplified for demo)
      // In a real app, you would parse the dates and compare them properly

      return true;
    });
  }, [returnsData, returnFilters]);

  // Calculate pagination
  const totalPages = Math.max(1, Math.ceil(filteredReturns.length / itemsPerPage));
  const paginatedReturns = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredReturns.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredReturns, currentPage, itemsPerPage]);

  // Reset pagination when return filters change
  const handleReturnFilterChange = (newFilters: ReturnFiltersState) => {
    setReturnFilters(newFilters);
    setCurrentPage(1);
  };

  // Reset return filters
  const handleResetReturnFilters = () => {
    setReturnFilters({
      orderId: '',
      status: 'all',
      dateFrom: undefined,
      dateTo: undefined,
    });
    setCurrentPage(1);
  };
  
  // Reset pagination when claim filters change
  const handleClaimFilterChange = (newFilters: ClaimFiltersState) => {
    setClaimFilters(newFilters);
    setClaimsCurrentPage(1);
  };

  // Reset claim filters
  const handleResetClaimFilters = () => {
    setClaimFilters({
      orderId: '',
      status: 'all',
    });
    setClaimsCurrentPage(1);
  };

  const handleRefresh = () => {
    // Vérification d'authentification en temps réel avec la fonction cohérente
    const token = getAuthToken();
    
    if (!token) {
      toast({
        title: "Erreur d'authentification",
        description: "Veuillez vous connecter pour accéder à vos données",
        variant: "destructive"
      });
      return;
    }
    
    if (activeTab === "returns") {
      refetchReturns();
      toast({
        title: "Rafraîchissement",
        description: "Liste des retours mise à jour",
      });
    } else {
      refetchClaims();
      toast({
        title: "Rafraîchissement",
        description: "Liste des réclamations mise à jour",
      });
    }
  };

  const handleCreateReturnSuccess = () => {
    // Vérification d'authentification en temps réel avec la fonction cohérente
    const token = getAuthToken();
    
    if (!token) {
      toast({
        title: "Erreur d'authentification",
        description: "Veuillez vous connecter pour accéder à vos données",
        variant: "destructive"
      });
      return;
    }
    
    console.log('Succès de la création du retour, mise à jour de l\'interface...');
    
    // Fermer le formulaire de création
    setIsCreateReturnOpen(false);
    
    // Forcer un rafraîchissement complet des données
    console.log('Rafraîchissement des données après création de retour');
    
    // Réinitialiser les filtres pour s'assurer que le nouveau retour est visible
    setReturnFilters({
      orderId: '',
      status: 'all',
      dateFrom: undefined,
      dateTo: undefined,
    });
    
    // Revenir à la première page pour s'assurer que le nouveau retour est visible
    setCurrentPage(1);
    
    // Vider le cache React Query pour forcer un rechargement complet
    queryClient.invalidateQueries({ queryKey: ['userReturns'] });
    console.log('Cache React Query invalidé pour userReturns');
    
    // Attendre un court délai pour s'assurer que le backend a bien traité la demande
    // et augmenter le délai pour donner plus de temps au backend
    setTimeout(() => {
      console.log('Exécution du refetchReturns après délai');
      // Forcer le rafraîchissement des données
      refetchReturns();
      
      // Recharger les données depuis le backend après un délai supplémentaire
      setTimeout(() => {
        console.log('Second refetch pour garantir les données à jour');
        refetchReturns();
      }, 1000);
    }, 1000);
    
    toast({
      title: "Succès",
      description: "Votre demande de retour a été créée avec succès",
    });
  };

  const handleCreateClaimSuccess = () => {
    // Vérification d'authentification en temps réel avec la fonction cohérente
    const token = getAuthToken();
    
    if (!token) {
      toast({
        title: "Erreur d'authentification",
        description: "Veuillez vous connecter pour accéder à vos données",
        variant: "destructive"
      });
      return;
    }
    
    console.log('Succès de la création de la réclamation, mise à jour de l\'interface...');
    
    // Fermer le formulaire de création
    setIsCreateClaimOpen(false);
    
    // Forcer un rafraîchissement complet des données
    console.log('Rafraîchissement des données après création de réclamation');
    
    // Réinitialiser les filtres pour s'assurer que la nouvelle réclamation est visible
    setClaimFilters({
      orderId: '',
      status: 'all',
    });
    
    // Revenir à la première page pour s'assurer que la nouvelle réclamation est visible
    setClaimsCurrentPage(1);
    
    // Vider le cache React Query pour forcer un rechargement complet
    queryClient.invalidateQueries({ queryKey: ['userClaims'] });
    console.log('Cache React Query invalidé pour userClaims');
    
    // Attendre un délai plus long pour s'assurer que le backend a bien traité la demande
    setTimeout(() => {
      console.log('Exécution du refetchClaims après délai');
      // Forcer le rafraîchissement des données
      refetchClaims();
      
      // Recharger les données depuis le backend après un délai supplémentaire
      setTimeout(() => {
        console.log('Second refetch pour garantir les données à jour');
        refetchClaims();
        
        // Vider le localStorage et recharger depuis le serveur
        try {
          localStorage.removeItem('userClaims');
          console.log('LocalStorage userClaims vidé pour forcer un rechargement depuis le serveur');
        } catch (error) {
          console.error('Erreur lors de la suppression du cache localStorage:', error);
        }
      }, 1000);
    }, 1000);
    
    toast({
      title: "Succès",
      description: "Votre réclamation a été créée avec succès",
    });
  };

  return (
    <AccountLayout title="Mes retours et ru00e9clamations">
      <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Mes retours et réclamations</h1>
          <Button onClick={handleRefresh} variant="outline" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>

        <Tabs defaultValue="returns" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="returns" className="flex items-center gap-2">
              <Package2 className="h-4 w-4" />
              Retours
            </TabsTrigger>
            <TabsTrigger value="claims" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Réclamations
            </TabsTrigger>
          </TabsList>

          {/* Tab Content: Returns */}
          <TabsContent value="returns">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Mes retours</CardTitle>
                    <CardDescription>Historique de vos demandes de retour</CardDescription>
                  </div>
                  <Button onClick={() => setIsCreateReturnOpen(true)}>
                    Créer un retour
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <ReturnFilters 
                    filters={returnFilters}
                    onFilterChange={handleReturnFilterChange}
                    onReset={handleResetReturnFilters}
                  />
                </div>

                {isLoadingReturns ? (
                  <div className="flex justify-center items-center py-10">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                    <span className="ml-2 text-gray-500">Chargement des retours...</span>
                  </div>
                ) : isErrorReturns ? (
                  <div className="flex justify-center items-center py-10">
                    <AlertCircle className="h-8 w-8 text-red-500" />
                    <span className="ml-2 text-red-500">Erreur lors du chargement des retours</span>
                  </div>
                ) : (
                  <div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="font-medium">ID</TableHead>
                          <TableHead>Commande</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead>Articles</TableHead>
                          <TableHead>Motif</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginatedReturns.map((returnItem) => (
                          <TableRow key={returnItem.id}>
                            <TableCell className="font-medium">{returnItem.id}</TableCell>
                            <TableCell>{returnItem.orderId}</TableCell>
                            <TableCell>{returnItem.date}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 ${returnItem.status === "completed" || returnItem.status === "Terminé" 
                                ? "bg-green-100 text-green-800" 
                                : returnItem.status === "processing" || returnItem.status === "En cours" 
                                  ? "bg-yellow-100 text-yellow-800" 
                                  : returnItem.status === "pending" || returnItem.status === "En attente" 
                                    ? "bg-blue-100 text-blue-800" 
                                    : "bg-gray-100 text-gray-800"
                                } rounded text-xs font-medium`}>
                                {returnItem.status === "completed" ? "Terminé" : 
                                 returnItem.status === "processing" ? "En cours" : 
                                 returnItem.status === "pending" ? "En attente" : 
                                 returnItem.status === "cancelled" ? "Annulé" : 
                                 returnItem.status}
                              </span>
                            </TableCell>
                            <TableCell>{returnItem.items}</TableCell>
                            <TableCell>
                              <div className="max-w-[200px] truncate" title={returnItem.reason}>
                                {returnItem.reason}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button variant="outline" size="sm" asChild>
                                <Link to={`/account/returns/${returnItem.id}`} className="flex items-center gap-1">
                                  Détails
                                  <ExternalLink className="h-3 w-3 ml-1" />
                                </Link>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                        {paginatedReturns.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-6">
                              <p className="text-gray-500">Aucun retour trouvé</p>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}

                {filteredReturns.length > itemsPerPage && (
                  <div className="flex justify-center mt-6">
                    <PaginationControls
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={setCurrentPage}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Content: Claims */}
          <TabsContent value="claims">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Mes réclamations</CardTitle>
                    <CardDescription>Historique de vos réclamations</CardDescription>
                  </div>
                  <Button onClick={() => setIsCreateClaimOpen(true)}>
                    Créer une réclamation
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <ClaimFilters 
                    filters={claimFilters}
                    onFilterChange={handleClaimFilterChange}
                    onReset={handleResetClaimFilters}
                  />
                </div>

                {isLoadingClaims ? (
                  <div className="flex justify-center items-center py-10">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                    <span className="ml-2 text-gray-500">Chargement des réclamations...</span>
                  </div>
                ) : isErrorClaims ? (
                  <div className="flex justify-center items-center py-10">
                    <AlertCircle className="h-8 w-8 text-red-500" />
                    <span className="ml-2 text-red-500">Erreur lors du chargement des réclamations</span>
                  </div>
                ) : (
                  <div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="font-medium">ID</TableHead>
                          <TableHead>Commande</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead>Sujet</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginatedClaims.map((claim) => (
                          <TableRow key={claim.id}>
                            <TableCell className="font-medium">{claim.id}</TableCell>
                            <TableCell>{claim.orderId}</TableCell>
                            <TableCell>{claim.date}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 ${claim.status === "resolved" || claim.status === "Résolu"
                                ? "bg-green-100 text-green-800"
                                : claim.status === "in_progress" || claim.status === "En cours"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : claim.status === "new" || claim.status === "Nouveau"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-gray-100 text-gray-800"
                                } rounded text-xs font-medium`}>
                                {claim.status === "resolved" ? "Résolu" : 
                                 claim.status === "in_progress" ? "En cours" : 
                                 claim.status === "new" ? "Nouveau" : 
                                 claim.status === "cancelled" ? "Annulé" : 
                                 claim.status}
                              </span>
                            </TableCell>
                            <TableCell>
                              <div className="max-w-[200px] truncate" title={claim.subject}>
                                {claim.subject}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button variant="outline" size="sm" asChild>
                                <Link to={`/account/claims/${claim.id}`} className="flex items-center gap-1">
                                  Détails
                                  <ExternalLink className="h-3 w-3 ml-1" />
                                </Link>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                        {paginatedClaims.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-6">
                              <p className="text-gray-500">Aucune réclamation trouvée</p>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}

                {filteredClaims.length > itemsPerPage && (
                  <div className="flex justify-center mt-6">
                    <PaginationControls
                      currentPage={claimsCurrentPage}
                      totalPages={totalClaimPages}
                      onPageChange={setClaimsCurrentPage}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <CreateReturnForm
          isOpen={isCreateReturnOpen}
          onOpenChange={setIsCreateReturnOpen}
          onSuccess={handleCreateReturnSuccess}
        />

        <CreateClaimForm
          isOpen={isCreateClaimOpen}
          onOpenChange={setIsCreateClaimOpen}
          onSuccess={handleCreateClaimSuccess}
        />
      </div>
    </AccountLayout>
  );
};

export default Returns;
