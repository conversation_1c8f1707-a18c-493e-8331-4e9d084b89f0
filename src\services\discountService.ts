import { Promotion } from './promotionApi';
import { Product } from './productApi';
import { CartItem } from './cartApi';

/**
 * Service pour calculer les remises et appliquer les promotions
 */
const discountService = {
  /**
   * Calcule le prix après remise pour un produit en fonction des promotions applicables
   * @param product Le produit
   * @param promotions Liste des promotions disponibles
   * @returns Le prix après remise et les détails de la promotion appliquée
   */
  calculateProductDiscount: (
    product: Product,
    promotions: Promotion[]
  ): { 
    discountedPrice: number; 
    originalPrice: number;
    appliedPromotion: Promotion | null;
    discountAmount: number;
    discountPercentage: number;
  } => {
    const originalPrice = product.price;
    
    // Filtrer les promotions actives et applicables à ce produit
    const applicablePromotions = promotions.filter(promo => 
      promo.is_active && 
      (
        (promo.applies_to === 'product' && promo.target_id === product.id) ||
        (promo.applies_to === 'category' && promo.target_id === product.category_id)
      )
    );

    if (applicablePromotions.length === 0) {
      return {
        discountedPrice: originalPrice,
        originalPrice,
        appliedPromotion: null,
        discountAmount: 0,
        discountPercentage: 0
      };
    }

    // Trouver la promotion qui offre la plus grande remise
    let bestPromotion: Promotion | null = null;
    let lowestPrice = originalPrice;
    let discountAmount = 0;
    let discountPercentage = 0;

    applicablePromotions.forEach(promo => {
      let currentDiscountedPrice = originalPrice;
      
      if (promo.discount_type === 'percentage') {
        const discount = originalPrice * (promo.discount_value / 100);
        currentDiscountedPrice = originalPrice - discount;
      } else if (promo.discount_type === 'fixed_amount') {
        currentDiscountedPrice = Math.max(0, originalPrice - promo.discount_value);
      }

      if (currentDiscountedPrice < lowestPrice) {
        lowestPrice = currentDiscountedPrice;
        bestPromotion = promo;
        discountAmount = originalPrice - currentDiscountedPrice;
        discountPercentage = (discountAmount / originalPrice) * 100;
      }
    });

    return {
      discountedPrice: lowestPrice,
      originalPrice,
      appliedPromotion: bestPromotion,
      discountAmount,
      discountPercentage
    };
  },

  /**
   * Calcule le total du panier après application des promotions
   * @param items Les articles du panier
   * @param promotions Liste des promotions disponibles
   * @returns Le total du panier après remises et les détails des promotions appliquées
   */
  calculateCartDiscount: (
    items: CartItem[],
    promotions: Promotion[]
  ): {
    subtotal: number;
    discountedTotal: number;
    appliedPromotions: { promotion: Promotion; discountAmount: number }[];
    totalDiscountAmount: number;
  } => {
    // Calculer le sous-total du panier
    const subtotal = items.reduce((total, item) => 
      total + (item.product.price * item.quantity), 0);
    
    // Appliquer les remises au niveau des produits
    const itemsWithDiscounts = items.map(item => {
      const discount = discountService.calculateProductDiscount(item.product, promotions);
      return {
        ...item,
        discountedPrice: discount.discountedPrice,
        appliedPromotion: discount.appliedPromotion,
        discountAmount: discount.discountAmount * item.quantity
      };
    });

    // Calculer le total après remises au niveau des produits
    let discountedTotal = itemsWithDiscounts.reduce((total, item) => 
      total + (item.discountedPrice * item.quantity), 0);
    
    // Collecter les promotions appliquées au niveau des produits
    const appliedProductPromotions = itemsWithDiscounts
      .filter(item => item.appliedPromotion !== null)
      .map(item => ({
        promotion: item.appliedPromotion!,
        discountAmount: item.discountAmount
      }));
    
    // Filtrer les promotions actives applicables au panier entier
    const cartPromotions = promotions.filter(promo => 
      promo.is_active && 
      promo.applies_to === 'cart' &&
      (promo.min_purchase_amount === null || promo.min_purchase_amount <= subtotal)
    );

    // Appliquer la meilleure promotion au niveau du panier
    let bestCartPromotion: Promotion | null = null;
    let bestCartDiscount = 0;

    cartPromotions.forEach(promo => {
      let currentDiscount = 0;
      
      if (promo.discount_type === 'percentage') {
        currentDiscount = discountedTotal * (promo.discount_value / 100);
      } else if (promo.discount_type === 'fixed_amount') {
        currentDiscount = Math.min(discountedTotal, promo.discount_value);
      }

      if (currentDiscount > bestCartDiscount) {
        bestCartDiscount = currentDiscount;
        bestCartPromotion = promo;
      }
    });

    // Appliquer la meilleure promotion au niveau du panier
    if (bestCartPromotion) {
      discountedTotal -= bestCartDiscount;
      appliedProductPromotions.push({
        promotion: bestCartPromotion,
        discountAmount: bestCartDiscount
      });
    }

    // Calculer le montant total des remises
    const totalDiscountAmount = subtotal - discountedTotal;

    return {
      subtotal,
      discountedTotal,
      appliedPromotions: appliedProductPromotions,
      totalDiscountAmount
    };
  },

  /**
   * Vérifie si un code de promotion est valide et applicable
   * @param code Le code de promotion
   * @param promotions Liste des promotions disponibles
   * @param cartTotal Total du panier (optionnel, pour vérifier le montant minimum d'achat)
   * @returns La promotion si valide, null sinon
   */
  validatePromotionCode: (
    code: string,
    promotions: Promotion[],
    cartTotal?: number
  ): Promotion | null => {
    const promotion = promotions.find(
      p => p.discount_code === code && p.is_active
    );

    if (!promotion) {
      return null;
    }

    // Vérifier la date de validité
    const now = new Date();
    const startDate = new Date(promotion.start_date);
    const endDate = promotion.end_date ? new Date(promotion.end_date) : null;

    if (now < startDate || (endDate && now > endDate)) {
      return null;
    }

    // Vérifier le montant minimum d'achat si applicable
    if (
      cartTotal !== undefined &&
      promotion.min_purchase_amount &&
      cartTotal < promotion.min_purchase_amount
    ) {
      return null;
    }

    return promotion;
  }
};

export default discountService;
