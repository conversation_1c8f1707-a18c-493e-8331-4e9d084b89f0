import axios, { AxiosError } from 'axios';
import { API_URL, CURRENCY } from '@/config';
import { getAuthToken, logout } from '@/utils/auth';

// Types
export interface Promotion {
  id: number;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  discount_code?: string;
  applies_to: 'product' | 'category' | 'cart';
  target_id?: number;
  target_name?: string;
  min_purchase_amount?: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
  is_cumulative?: boolean;
  usage_limit_per_customer?: number;
  total_usage_limit?: number;
  total_usage?: number;
  created_at: string;
  updated_at: string;
}

export interface Campaign {
  id: number;
  name: string;
  description?: string;
  start_date: string;
  end_date?: string;
  discount_percentage: number;
  discount_amount?: number;
  discount_code?: string;
  products: number[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  product_count: number;
}

export interface PromotionListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Promotion[];
}

export interface CampaignListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Campaign[];
}

// Helper function to handle API errors
const handleApiError = (error: any) => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;

    // Handle 401 Unauthorized errors (expired token)
    if (axiosError.response?.status === 401) {
      logout();
      window.location.href = '/login';
    }

    // Return error message from API if available
    let errorMessage = 'Une erreur est survenue';

    if (axiosError.response?.data && typeof axiosError.response.data === 'object') {
      const responseData = axiosError.response.data as Record<string, unknown>;
      if ('detail' in responseData && typeof responseData.detail === 'string') {
        errorMessage = responseData.detail;
      } else if ('message' in responseData && typeof responseData.message === 'string') {
        errorMessage = responseData.message;
      }
    } else if (axiosError.message) {
      errorMessage = axiosError.message;
    }

    console.error('API Error:', errorMessage);
    throw new Error(errorMessage);
  }

  console.error('Unexpected error:', error);
  throw error;
};

// Types pour la validation des codes promo
export interface PromoCodeValidationResponse {
  valid: boolean;
  message?: string;
  discount_amount: number;
  promotion_id: number;
  promotion?: Promotion;
}

// Promotion API service
const promotionApi = {
  // Promotions pour les administrateurs (nécessite authentification)
  getPromotions: async (params?: any): Promise<PromotionListResponse> => {
    try {
      const response = await axios.get(`${API_URL}/marketing/promotions/`, {
        params,
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching promotions:', error);
      return handleApiError(error);
    }
  },
  
  // Promotions actives pour les clients (utilise l'API publique)
  getActivePromotions: async (params?: any): Promise<PromotionListResponse> => {
    // Vérifier si l'utilisateur est authentifié
    const token = getAuthToken();
    
    if (token) {
      try {
        console.log('Utilisation de l\'API admin avec authentification');
        const response = await axios.get(`${API_URL}/marketing/promotions/`, {
          params: {
            ...params,
            is_active: true,
            page_size: params?.page_size || 5 // Limiter le nombre de promotions
          },
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        return response.data;
      } catch (error) {
        console.error('Erreur lors de la récupération des promotions actives:', error);
        return { count: 0, next: null, previous: null, results: [] };
      }
    } else {
      // Si l'utilisateur n'est pas authentifié, retour d'une liste vide
      console.log('Utilisateur non authentifié, retour d\'une liste vide');
      return { count: 0, next: null, previous: null, results: [] };
    }
  },

  // Récupérer les détails d'une promotion pour les clients (utilise l'API publique)
  getPublicPromotion: async (id: number): Promise<Promotion> => {
    try {
      // Utiliser le nouvel endpoint public qui ne nécessite pas d'authentification
      const response = await axios.get(`${API_URL}/marketing/promotions/${id}/public/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching public promotion ${id}:`, error);
      
      // Essayer avec l'API de promotions actives comme fallback
      try {
        console.log(`Essai avec l'API des promotions actives comme fallback`);
        const activeResponse = await axios.get(`${API_URL}/marketing/promotions/active/`);
        const promotion = activeResponse.data.find((p: Promotion) => p.id === Number(id));
        
        if (!promotion) {
          throw new Error(`Promotion with ID ${id} not found or not active`);
        }
        
        return promotion;
      } catch (fallbackError) {
        console.error(`Fallback failed:`, fallbackError);
        if (error instanceof Error && error.message.includes('not found')) {
          throw error;
        }
        return handleApiError(error);
      }
    }
  },

  // Valider un code promo pour les clients
  validatePromoCode: async (code: string, cartTotal: number): Promise<PromoCodeValidationResponse> => {
    try {
      // Endpoint public pour valider un code promo
      const response = await axios.post(`${API_URL}/marketing/promotions/validate-code/`, {
        code,
        cart_total: cartTotal
      });
      return response.data;
    } catch (error: any) {
      console.error('Error validating promo code:', error);
      
      // Gérer les erreurs spécifiques
      if (error.response) {
        const statusCode = error.response.status;
        const errorMessage = error.response.data?.detail || error.response.data?.message || '';
        
        if (statusCode === 404) {
          return {
            valid: false,
            message: 'Code promo invalide ou expiré',
            discount_amount: 0,
            promotion_id: 0
          };
        } else if (statusCode === 400) {
          return {
            valid: false,
            message: errorMessage || 'Ce code promo ne peut pas être appliqué à votre panier',
            discount_amount: 0,
            promotion_id: 0
          };
        }
      }
      
      // Erreur générique
      return {
        valid: false,
        message: 'Une erreur est survenue lors de la validation du code promo',
        discount_amount: 0,
        promotion_id: 0
      };
    }
  },

  getPromotion: async (id: number): Promise<Promotion> => {
    try {
      const response = await axios.get(`${API_URL}/marketing/promotions/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching promotion ${id}:`, error);
      return handleApiError(error);
    }
  },

  createPromotion: async (data: Partial<Promotion>): Promise<Promotion> => {
    try {
      const response = await axios.post(`${API_URL}/marketing/promotions/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating promotion:', error);
      return handleApiError(error);
    }
  },

  updatePromotion: async (id: number, data: Partial<Promotion>): Promise<Promotion> => {
    try {
      const response = await axios.patch(`${API_URL}/marketing/promotions/${id}/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating promotion ${id}:`, error);
      return handleApiError(error);
    }
  },

  deletePromotion: async (id: number): Promise<void> => {
    try {
      await axios.delete(`${API_URL}/marketing/promotions/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error deleting promotion ${id}:`, error);
      throw error;
    }
  },

  // Campaigns
  getCampaigns: async (params?: any): Promise<CampaignListResponse> => {
    try {
      const response = await axios.get(`${API_URL}/marketing/campaigns/`, {
        params,
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      throw error;
    }
  },

  getCampaign: async (id: number): Promise<Campaign> => {
    try {
      const response = await axios.get(`${API_URL}/marketing/campaigns/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching campaign ${id}:`, error);
      throw error;
    }
  },

  createCampaign: async (data: Partial<Campaign>): Promise<Campaign> => {
    try {
      const response = await axios.post(`${API_URL}/marketing/campaigns/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating campaign:', error);
      throw error;
    }
  },

  updateCampaign: async (id: number, data: Partial<Campaign>): Promise<Campaign> => {
    try {
      const response = await axios.patch(`${API_URL}/marketing/campaigns/${id}/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating campaign ${id}:`, error);
      throw error;
    }
  },

  deleteCampaign: async (id: number): Promise<void> => {
    try {
      await axios.delete(`${API_URL}/marketing/campaigns/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error deleting campaign ${id}:`, error);
      throw error;
    }
  },

  // Campaign products
  addProductToCampaign: async (campaignId: number, productId: number): Promise<void> => {
    try {
      await axios.post(`${API_URL}/marketing/campaigns/${campaignId}/add_product/`, {
        product_id: productId
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error adding product ${productId} to campaign ${campaignId}:`, error);
      throw error;
    }
  },

  removeProductFromCampaign: async (campaignId: number, productId: number): Promise<void> => {
    try {
      await axios.post(`${API_URL}/marketing/campaigns/${campaignId}/remove_product/`, {
        product_id: productId
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error removing product ${productId} from campaign ${campaignId}:`, error);
      throw error;
    }
  },

  getCampaignsByProduct: async (productId: number): Promise<Campaign[]> => {
    try {
      const response = await axios.get(`${API_URL}/marketing/campaigns/by_product/`, {
        params: { product_id: productId },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching campaigns for product ${productId}:`, error);
      throw error;
    }
  }
};

export default promotionApi;
