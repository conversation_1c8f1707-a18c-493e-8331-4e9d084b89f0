import React, { useState, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Ta<PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { 
  Download, 
  Upload, 
  FileText, 
  CheckCircle2, 
  AlertCircle, 
  RefreshCw, 
  Info, 
  HelpCircle 
} from 'lucide-react';
import { productApi } from '@/services/productApi';

interface ImportExportManagerProps {
  entityType: 'products' | 'categories' | 'attributes' | 'attribute-values' | 'product-variants' | 'inventory';
}

const ImportExportManager: React.FC<ImportExportManagerProps> = ({ entityType }) => {
  const [file, setFile] = useState<File | null>(null);
  const [importResult, setImportResult] = useState<any | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  
  // Export mutation
  const exportMutation = useMutation({
    mutationFn: () => productApi.exportData(entityType),
    onSuccess: (blob) => {
      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${entityType}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: 'Export successful',
        description: `${entityType} data has been exported successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Export failed',
        description: error.message || 'An error occurred during export.',
        variant: 'destructive',
      });
    },
  });
  
  // Import mutation
  const importMutation = useMutation({
    mutationFn: (data: FormData) => productApi.importData(entityType, data),
    onSuccess: (result) => {
      setImportResult(result);
      setIsImporting(false);
      setImportProgress(100);
      
      toast({
        title: 'Import successful',
        description: `${result.imported} ${entityType} have been imported successfully.`,
      });
    },
    onError: (error: any) => {
      setIsImporting(false);
      setImportProgress(0);
      
      toast({
        title: 'Import failed',
        description: error.message || 'An error occurred during import.',
        variant: 'destructive',
      });
    },
  });
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setImportResult(null);
    }
  };
  
  // Handle export
  const handleExport = () => {
    exportMutation.mutate();
  };
  
  // Handle import
  const handleImport = () => {
    if (!file) {
      toast({
        title: 'No file selected',
        description: 'Please select a CSV file to import.',
        variant: 'destructive',
      });
      return;
    }
    
    // Check file extension
    if (!file.name.endsWith('.csv')) {
      toast({
        title: 'Invalid file format',
        description: 'Please select a CSV file.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsImporting(true);
    setImportProgress(10);
    
    const formData = new FormData();
    formData.append('file', file);
    
    // Simulate progress
    const progressInterval = setInterval(() => {
      setImportProgress((prev) => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 500);
    
    importMutation.mutate(formData);
  };
  
  // Reset import form
  const resetImport = () => {
    setFile(null);
    setImportResult(null);
    setImportProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Get entity name for display
  const getEntityName = () => {
    const names: Record<string, string> = {
      'products': 'Products',
      'categories': 'Categories',
      'attributes': 'Product Attributes',
      'attribute-values': 'Attribute Values',
      'product-variants': 'Product Variants',
      'inventory': 'Inventory'
    };
    return names[entityType] || entityType;
  };
  
  // Get template description
  const getTemplateDescription = () => {
    const descriptions: Record<string, string> = {
      'products': 'name,slug,sku,description,short_description,price,sale_price,cost_price,categories,is_featured,is_digital,status',
      'categories': 'name,slug,description,parent,is_active',
      'attributes': 'name,description',
      'attribute-values': 'attribute,value',
      'product-variants': 'product,name,sku,price_adjustment,attributes,is_active',
      'inventory': 'product,variant,quantity,reserved_quantity,low_stock_threshold'
    };
    return descriptions[entityType] || '';
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Import/Export {getEntityName()}</CardTitle>
        <CardDescription>
          Import data from CSV files or export your data to CSV format.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="import">
          <TabsList className="mb-4">
            <TabsTrigger value="import">Import</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
          </TabsList>
          
          {/* Import Tab */}
          <TabsContent value="import" className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Import Instructions</AlertTitle>
              <AlertDescription>
                <p className="mb-2">
                  Upload a CSV file with the following columns:
                </p>
                <code className="bg-gray-100 p-1 rounded text-xs block mb-2 whitespace-normal break-all">
                  {getTemplateDescription()}
                </code>
                <p className="text-sm">
                  The first row should contain the column headers. Download a template from the Export tab if needed.
                </p>
              </AlertDescription>
            </Alert>
            
            <div className="space-y-4">
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="csv-file">CSV File</Label>
                <Input
                  id="csv-file"
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  ref={fileInputRef}
                  disabled={isImporting}
                />
                {file && (
                  <p className="text-sm text-muted-foreground">
                    Selected file: {file.name} ({(file.size / 1024).toFixed(2)} KB)
                  </p>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={handleImport} 
                  disabled={!file || isImporting}
                  className="gap-2"
                >
                  {isImporting ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      <span>Importing...</span>
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4" />
                      <span>Import</span>
                    </>
                  )}
                </Button>
                
                {file && (
                  <Button 
                    variant="outline" 
                    onClick={resetImport}
                    disabled={isImporting}
                  >
                    Cancel
                  </Button>
                )}
              </div>
              
              {isImporting && (
                <div className="space-y-2">
                  <Progress value={importProgress} className="w-full" />
                  <p className="text-sm text-center text-muted-foreground">
                    Importing data... {importProgress}%
                  </p>
                </div>
              )}
              
              {importResult && (
                <div className="space-y-2 bg-gray-50 p-4 rounded-md">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                    <h3 className="font-medium">Import Completed</h3>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Total rows:</div>
                    <div className="font-medium">{importResult.total_rows}</div>
                    
                    <div>Imported:</div>
                    <div className="font-medium">{importResult.imported}</div>
                    
                    <div>Added:</div>
                    <div className="font-medium">{importResult.added}</div>
                    
                    <div>Updated:</div>
                    <div className="font-medium">{importResult.updated}</div>
                    
                    <div>Skipped:</div>
                    <div className="font-medium">{importResult.skipped}</div>
                    
                    <div>Errors:</div>
                    <div className="font-medium">{importResult.errors}</div>
                  </div>
                  
                  {importResult.error_messages && importResult.error_messages.length > 0 && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium mb-1">Error Messages:</h4>
                      <ul className="text-xs text-red-600 space-y-1 ml-5 list-disc">
                        {importResult.error_messages.map((error: string, index: number) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </TabsContent>
          
          {/* Export Tab */}
          <TabsContent value="export" className="space-y-4">
            <Alert>
              <HelpCircle className="h-4 w-4" />
              <AlertTitle>Export Information</AlertTitle>
              <AlertDescription>
                <p className="mb-2">
                  Export your {getEntityName().toLowerCase()} data to a CSV file. You can use this file as a template for imports.
                </p>
                <p className="text-sm">
                  The export includes all {getEntityName().toLowerCase()} in your database.
                </p>
              </AlertDescription>
            </Alert>
            
            <div className="flex justify-center py-8">
              <Button 
                onClick={handleExport} 
                disabled={exportMutation.isPending}
                size="lg"
                className="gap-2"
              >
                {exportMutation.isPending ? (
                  <>
                    <RefreshCw className="h-5 w-5 animate-spin" />
                    <span>Exporting...</span>
                  </>
                ) : (
                  <>
                    <Download className="h-5 w-5" />
                    <span>Export {getEntityName()} to CSV</span>
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ImportExportManager;
