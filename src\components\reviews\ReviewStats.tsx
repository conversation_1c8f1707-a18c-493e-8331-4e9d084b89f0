import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Star, Shield } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import reviewApi from '@/services/reviewApi';

interface ReviewStatsProps {
  productId: number;
}

const ReviewStats: React.FC<ReviewStatsProps> = ({ productId }) => {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['reviewStats', productId],
    queryFn: () => reviewApi.getProductReviewStats(productId),
  });

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-5 w-5 ${
              star <= Math.round(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Évaluations clients</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-6 w-24" />
          </div>
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-2">
                <Skeleton className="h-4 w-8" />
                <Skeleton className="h-2 flex-1" />
                <Skeleton className="h-4 w-8" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats || stats.total_reviews === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Évaluations clients</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="flex justify-center mb-4">
              {renderStars(0)}
            </div>
            <p className="text-gray-500">Aucun avis pour ce produit</p>
            <p className="text-sm text-gray-400 mt-1">
              Soyez le premier à laisser un avis !
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { total_reviews, average_rating, rating_distribution, verified_purchases_count } = stats;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Évaluations clients</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Note moyenne */}
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-3xl font-bold">{average_rating.toFixed(1)}</div>
            <div className="flex justify-center mb-1">
              {renderStars(average_rating)}
            </div>
            <div className="text-sm text-gray-500">
              {total_reviews} avis
            </div>
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm text-gray-600">
                {verified_purchases_count} achats vérifiés
              </span>
            </div>
            <div className="text-sm text-gray-500">
              {((verified_purchases_count / total_reviews) * 100).toFixed(0)}% des avis proviennent d'achats vérifiés
            </div>
          </div>
        </div>

        {/* Distribution des notes */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Répartition des notes</h4>
          {[5, 4, 3, 2, 1].map((rating) => {
            const count = rating_distribution[rating.toString()] || 0;
            const percentage = total_reviews > 0 ? (count / total_reviews) * 100 : 0;
            
            return (
              <div key={rating} className="flex items-center space-x-2">
                <div className="flex items-center space-x-1 w-12">
                  <span className="text-sm">{rating}</span>
                  <Star className="h-3 w-3 text-yellow-400 fill-current" />
                </div>
                
                <div className="flex-1">
                  <Progress value={percentage} className="h-2" />
                </div>
                
                <div className="text-sm text-gray-500 w-8 text-right">
                  {count}
                </div>
              </div>
            );
          })}
        </div>

        {/* Informations supplémentaires */}
        <div className="pt-4 border-t text-xs text-gray-500 space-y-1">
          <p>• Les avis sont modérés avant publication</p>
          <p>• Seuls les clients ayant acheté le produit peuvent laisser un avis vérifié</p>
          <p>• Vous pouvez voter pour l'utilité des avis</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewStats;
