import axios from 'axios';
import { API_BASE_URL } from '@/config/api';

// Types pour la gestion des abonnements
export interface NewsletterSubscription {
  id: number;
  email: string;
  is_active: boolean;
  subscribed_at: string;
  unsubscribed_at?: string;
  preferences: {
    promotions: boolean;
    new_products: boolean;
    newsletters: boolean;
    order_updates: boolean;
  };
}

export interface SubscriptionPreferences {
  promotions: boolean;
  new_products: boolean;
  newsletters: boolean;
  order_updates: boolean;
}

export interface SubscribeRequest {
  email: string;
  preferences?: Partial<SubscriptionPreferences>;
}

export interface UnsubscribeRequest {
  email: string;
  token?: string;
}

// Helper pour obtenir le token d'authentification
const getAuthToken = () => {
  return localStorage.getItem('accessToken') || localStorage.getItem('authToken');
};

// Configuration axios
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/core`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// API pour la gestion des abonnements
const subscriptionApi = {
  // S'abonner à la newsletter
  subscribe: async (data: SubscribeRequest): Promise<{ message: string; subscription: NewsletterSubscription }> => {
    const response = await apiClient.post('/newsletter/subscribe/', data);
    return response.data;
  },

  // Se désabonner de la newsletter
  unsubscribe: async (data: UnsubscribeRequest): Promise<{ message: string }> => {
    const response = await apiClient.post('/newsletter/unsubscribe/', data);
    return response.data;
  },

  // Obtenir les détails de l'abonnement (pour les utilisateurs connectés)
  getSubscription: async (): Promise<NewsletterSubscription> => {
    const response = await apiClient.get('/newsletter/subscription/');
    return response.data;
  },

  // Mettre à jour les préférences d'abonnement
  updatePreferences: async (preferences: Partial<SubscriptionPreferences>): Promise<NewsletterSubscription> => {
    const response = await apiClient.patch('/newsletter/preferences/', preferences);
    return response.data;
  },

  // Réactiver un abonnement
  reactivate: async (email: string): Promise<{ message: string }> => {
    const response = await apiClient.post('/newsletter/reactivate/', { email });
    return response.data;
  },

  // Vérifier le statut d'un abonnement par email
  checkStatus: async (email: string): Promise<{ is_subscribed: boolean; preferences?: SubscriptionPreferences }> => {
    const response = await apiClient.get(`/newsletter/status/?email=${encodeURIComponent(email)}`);
    return response.data;
  },

  // Obtenir les statistiques d'abonnement (admin seulement)
  getStats: async (): Promise<{
    total_subscribers: number;
    active_subscribers: number;
    recent_subscriptions: number;
    unsubscribe_rate: number;
    preferences_stats: {
      promotions: number;
      new_products: number;
      newsletters: number;
      order_updates: number;
    };
  }> => {
    const response = await apiClient.get('/newsletter/stats/');
    return response.data;
  },

  // Envoyer une newsletter à tous les abonnés (admin seulement)
  sendNewsletter: async (data: {
    subject: string;
    content: string;
    type: 'promotion' | 'newsletter' | 'announcement';
    target_preferences?: string[];
  }): Promise<{ message: string; sent_count: number }> => {
    const response = await apiClient.post('/newsletter/send/', data);
    return response.data;
  },

  // Obtenir l'historique des newsletters envoyées (admin seulement)
  getNewsletterHistory: async (): Promise<Array<{
    id: number;
    subject: string;
    type: string;
    sent_at: string;
    sent_count: number;
    open_rate?: number;
    click_rate?: number;
  }>> => {
    const response = await apiClient.get('/newsletter/history/');
    return response.data;
  }
};

export default subscriptionApi;
