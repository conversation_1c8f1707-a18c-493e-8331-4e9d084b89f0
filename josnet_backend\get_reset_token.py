#!/usr/bin/env python
"""
Script pour récupérer un token de reset valide depuis la base de données
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from authentication.models import PasswordReset, User
from django.utils import timezone

def get_latest_reset_token():
    """Récupère le dernier token de reset généré"""
    try:
        # Chercher le dernier token de reset non expiré
        latest_reset = PasswordReset.objects.filter(
            expires_at__gt=timezone.now(),
            is_used=False
        ).order_by('-created_at').first()
        
        if latest_reset:
            print(f"✅ Token trouvé: {latest_reset.token}")
            print(f"📧 Utilisateur: {latest_reset.user.email}")
            print(f"⏰ Expire le: {latest_reset.expires_at}")
            print(f"🔗 URL complète: http://localhost:8080/reset-password/{latest_reset.token}")
            return latest_reset.token
        else:
            print("❌ Aucun token de reset valide trouvé")
            
            # Créer un nouveau token pour test
            user = User.objects.filter(email='<EMAIL>').first()
            if user:
                print("🔄 Création d'un nouveau token...")
                import uuid
                from datetime import timedelta
                
                token = str(uuid.uuid4())
                expires_at = timezone.now() + timedelta(hours=24)
                
                reset = PasswordReset.objects.create(
                    user=user,
                    token=token,
                    expires_at=expires_at
                )
                
                print(f"✅ Nouveau token créé: {token}")
                print(f"🔗 URL: http://localhost:8080/reset-password/{token}")
                return token
            else:
                print("❌ Utilisateur <EMAIL> non trouvé")
                return None
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_reset_token(token):
    """Teste un token de reset"""
    print(f"\n🧪 Test du token: {token}")
    
    try:
        reset = PasswordReset.objects.filter(token=token).first()
        if not reset:
            print("❌ Token non trouvé en base")
            return False
            
        if reset.is_used:
            print("❌ Token déjà utilisé")
            return False
            
        if reset.expires_at < timezone.now():
            print("❌ Token expiré")
            return False
            
        print("✅ Token valide")
        print(f"👤 Utilisateur: {reset.user.email}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Recherche de tokens de reset...\n")
    
    # Afficher tous les tokens récents
    recent_resets = PasswordReset.objects.order_by('-created_at')[:5]
    print("📋 Derniers tokens de reset:")
    for reset in recent_resets:
        status = "✅ Valide" if not reset.is_used and reset.expires_at > timezone.now() else "❌ Invalide"
        print(f"  {reset.token[:20]}... - {reset.user.email} - {status}")
    
    print("\n" + "="*50)
    
    # Récupérer le token le plus récent
    token = get_latest_reset_token()
    
    if token:
        print("\n" + "="*50)
        test_reset_token(token)
        
        print(f"\n💡 Pour tester manuellement:")
        print(f"curl -X POST http://localhost:8000/api/v1/auth/password/reset/confirm/ \\")
        print(f"  -H 'Content-Type: application/json' \\")
        print(f"  -d '{{\"token\":\"{token}\",\"new_password\":\"NewPassword123!\",\"confirm_password\":\"NewPassword123!\"}}'")
