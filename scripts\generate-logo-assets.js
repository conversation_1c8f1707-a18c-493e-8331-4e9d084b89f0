#!/usr/bin/env node

/**
 * Script pour générer les assets de logo en différents formats
 * Utilise sharp pour convertir les SVG en PNG/WebP
 */

const fs = require('fs');
const path = require('path');

// Configuration des tailles et formats
const logoConfigs = [
  // Favicon
  {
    input: 'public/assets/images/josnet-favicon.svg',
    outputs: [
      { file: 'public/favicon-16x16.png', width: 16, height: 16 },
      { file: 'public/favicon-32x32.png', width: 32, height: 32 },
      { file: 'public/favicon-48x48.png', width: 48, height: 48 },
      { file: 'public/apple-touch-icon.png', width: 180, height: 180 },
      { file: 'public/android-chrome-192x192.png', width: 192, height: 192 },
      { file: 'public/android-chrome-512x512.png', width: 512, height: 512 },
    ]
  },
  // Logo compact
  {
    input: 'public/assets/images/josnet-logo-compact.svg',
    outputs: [
      { file: 'public/assets/images/josnet-logo-compact-sm.png', width: 80, height: 27 },
      { file: 'public/assets/images/josnet-logo-compact-md.png', width: 120, height: 40 },
      { file: 'public/assets/images/josnet-logo-compact-lg.png', width: 180, height: 60 },
      { file: 'public/assets/images/josnet-logo-compact-xl.png', width: 240, height: 80 },
    ]
  },
  // Logo complet
  {
    input: 'public/assets/images/josnet-logo.svg',
    outputs: [
      { file: 'public/assets/images/josnet-logo-sm.png', width: 150, height: 45 },
      { file: 'public/assets/images/josnet-logo-md.png', width: 200, height: 60 },
      { file: 'public/assets/images/josnet-logo-lg.png', width: 300, height: 90 },
      { file: 'public/assets/images/josnet-logo-xl.png', width: 400, height: 120 },
    ]
  }
];

// Fonction pour créer les dossiers si nécessaire
function ensureDirectoryExists(filePath) {
  const dirname = path.dirname(filePath);
  if (!fs.existsSync(dirname)) {
    fs.mkdirSync(dirname, { recursive: true });
  }
}

// Fonction principale
async function generateAssets() {
  console.log('🎨 Génération des assets de logo...');
  
  try {
    // Vérifier si sharp est disponible
    const sharp = require('sharp');
    
    for (const config of logoConfigs) {
      console.log(`\n📁 Traitement de ${config.input}...`);
      
      if (!fs.existsSync(config.input)) {
        console.warn(`⚠️  Fichier source non trouvé: ${config.input}`);
        continue;
      }
      
      for (const output of config.outputs) {
        try {
          ensureDirectoryExists(output.file);
          
          await sharp(config.input)
            .resize(output.width, output.height)
            .png({ quality: 90, compressionLevel: 9 })
            .toFile(output.file);
            
          console.log(`✅ Généré: ${output.file} (${output.width}x${output.height})`);
        } catch (error) {
          console.error(`❌ Erreur pour ${output.file}:`, error.message);
        }
      }
    }
    
    // Générer le manifest.json mis à jour
    await generateManifest();
    
    console.log('\n🎉 Génération terminée avec succès !');
    
  } catch (error) {
    if (error.code === 'MODULE_NOT_FOUND') {
      console.log('\n📦 Sharp n\'est pas installé. Installation...');
      console.log('Exécutez: npm install sharp --save-dev');
      console.log('\nEn attendant, les fichiers SVG seront utilisés directement.');
    } else {
      console.error('❌ Erreur lors de la génération:', error);
    }
  }
}

// Générer un manifest.json mis à jour
async function generateManifest() {
  const manifest = {
    "name": "JOSNET NETWORK - Solutions IT & Télécom",
    "short_name": "JOSNET",
    "description": "Votre partenaire pour des solutions IT et télécom de haute qualité au Burundi",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#ffffff",
    "theme_color": "#2563eb",
    "orientation": "portrait-primary",
    "icons": [
      {
        "src": "/favicon-16x16.png",
        "sizes": "16x16",
        "type": "image/png"
      },
      {
        "src": "/favicon-32x32.png",
        "sizes": "32x32",
        "type": "image/png"
      },
      {
        "src": "/android-chrome-192x192.png",
        "sizes": "192x192",
        "type": "image/png"
      },
      {
        "src": "/android-chrome-512x512.png",
        "sizes": "512x512",
        "type": "image/png"
      },
      {
        "src": "/assets/images/josnet-favicon.svg",
        "sizes": "32x32",
        "type": "image/svg+xml",
        "purpose": "any maskable"
      }
    ],
    "categories": ["business", "technology", "shopping"],
    "lang": "fr",
    "scope": "/",
    "prefer_related_applications": false
  };
  
  fs.writeFileSync('public/manifest.json', JSON.stringify(manifest, null, 2));
  console.log('✅ Manifest.json mis à jour');
}

// Exécuter le script
if (require.main === module) {
  generateAssets();
}

module.exports = { generateAssets };
