
import React from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Helmet } from "react-helmet";

const About = () => {
  return (
    <>
      <Helmet>
        <title>À propos de JOSNET NETWORK | Notre histoire et notre mission</title>
        <meta name="description" content="Découvrez l'histoire de JOSNET NETWORK, notre mission, nos valeurs et notre équipe. Leader dans le secteur des technologies de l'information et des communications au Burundi." />
      </Helmet>
      
      <div className="min-h-screen flex flex-col">
        <Navbar />
        
        <main className="flex-1">
          {/* Hero Section */}
          <section className="bg-primary py-16 text-white">
            <div className="container mx-auto px-4">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Notre Histoire</h1>
              <p className="text-xl max-w-3xl">
                Leader dans le secteur des technologies de l'information et des communications, 
                JOSNET NETWORK reste à la pointe de l'innovation depuis 2015.
              </p>
            </div>
          </section>
          
          {/* Mission Section */}
          <section className="py-16">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
                <div>
                  <h2 className="text-3xl font-bold mb-6 text-primary">Notre Mission</h2>
                  <p className="text-gray-700 mb-4">
                    Notre mission est de fournir des solutions technologiques innovantes qui aident 
                    nos clients à atteindre leurs objectifs commerciaux. Nous croyons en la création 
                    de solutions personnalisées qui répondent aux besoins spécifiques de chaque client.
                  </p>
                  <p className="text-gray-700">
                    En tant que leader de l'industrie au Burundi, nous nous efforçons d'améliorer 
                    constamment nos services et de maintenir les plus hauts standards de qualité et de fiabilité.
                  </p>
                </div>
                
                <div className="bg-gray-100 p-8 rounded-lg">
                  <h3 className="text-2xl font-bold mb-4 text-primary">Nos Valeurs</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">1</span>
                      <span><strong className="text-primary">Innovation</strong> - Nous repoussons constamment les limites de la technologie</span>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">2</span>
                      <span><strong className="text-primary">Qualité</strong> - Nous nous engageons à fournir des produits et services de la plus haute qualité</span>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">3</span>
                      <span><strong className="text-primary">Intégrité</strong> - Nous agissons avec honnêteté et transparence dans toutes nos interactions</span>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">4</span>
                      <span><strong className="text-primary">Service client</strong> - Notre succès est mesuré par la satisfaction de nos clients</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>
          
          {/* Team Section */}
          <section className="py-16 bg-gray-50">
            <div className="container mx-auto px-4">
              <h2 className="text-3xl font-bold mb-12 text-center text-primary">Notre Équipe</h2>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
                {/* Team Member 1 */}
                <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                  <div className="h-64 bg-gray-200"></div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-1">Jean Nkurunziza</h3>
                    <p className="text-accent mb-4">Fondateur & PDG</p>
                    <p className="text-gray-600">
                      Visionnaire technologique avec plus de 15 ans d'expérience dans l'industrie IT.
                    </p>
                  </div>
                </div>
                
                {/* Team Member 2 */}
                <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                  <div className="h-64 bg-gray-200"></div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-1">Marie Hakizimana</h3>
                    <p className="text-accent mb-4">Directrice Technique</p>
                    <p className="text-gray-600">
                      Experte en architecture de systèmes et en solutions innovantes.
                    </p>
                  </div>
                </div>
                
                {/* Team Member 3 */}
                <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                  <div className="h-64 bg-gray-200"></div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-1">Paul Ndayishimiye</h3>
                    <p className="text-accent mb-4">Directeur Commercial</p>
                    <p className="text-gray-600">
                      Stratège commercial avec une profonde connaissance du marché africain.
                    </p>
                  </div>
                </div>
                
                {/* Team Member 4 */}
                <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                  <div className="h-64 bg-gray-200"></div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-1">Claire Mutoni</h3>
                    <p className="text-accent mb-4">Directrice Marketing</p>
                    <p className="text-gray-600">
                      Spécialiste en marketing digital et en stratégies de croissance.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* History Timeline */}
          <section className="py-16">
            <div className="container mx-auto px-4">
              <h2 className="text-3xl font-bold mb-12 text-center text-primary">Notre Parcours</h2>
              
              <div className="relative">
                {/* Timeline Line */}
                <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-primary"></div>
                
                {/* Timeline Items */}
                <div className="space-y-12">
                  {/* Item 1 */}
                  <div className="flex flex-col md:flex-row items-center">
                    <div className="md:w-1/2 md:pr-12 md:text-right">
                      <h3 className="text-xl font-bold mb-2 text-primary">2015</h3>
                      <p className="text-gray-700">
                        Fondation de JOSNET NETWORK à Bujumbura, Burundi, avec une vision d'apporter 
                        des solutions technologiques innovantes au marché local.
                      </p>
                    </div>
                    <div className="hidden md:block w-12 h-12 rounded-full bg-primary text-white flex-shrink-0 flex items-center justify-center border-4 border-white shadow-lg relative z-10">
                      <span className="text-lg font-bold">1</span>
                    </div>
                    <div className="md:w-1/2 md:pl-12 mt-4 md:mt-0">
                    </div>
                  </div>
                  
                  {/* Item 2 */}
                  <div className="flex flex-col md:flex-row items-center">
                    <div className="md:w-1/2 md:pr-12 md:text-right">
                    </div>
                    <div className="hidden md:block w-12 h-12 rounded-full bg-primary text-white flex-shrink-0 flex items-center justify-center border-4 border-white shadow-lg relative z-10">
                      <span className="text-lg font-bold">2</span>
                    </div>
                    <div className="md:w-1/2 md:pl-12 mt-4 md:mt-0">
                      <h3 className="text-xl font-bold mb-2 text-primary">2018</h3>
                      <p className="text-gray-700">
                        Expansion régionale avec l'ouverture de bureaux au Rwanda et en Tanzanie, 
                        marquant le début de notre présence internationale.
                      </p>
                    </div>
                  </div>
                  
                  {/* Item 3 */}
                  <div className="flex flex-col md:flex-row items-center">
                    <div className="md:w-1/2 md:pr-12 md:text-right">
                      <h3 className="text-xl font-bold mb-2 text-primary">2020</h3>
                      <p className="text-gray-700">
                        Lancement de notre plateforme e-commerce innovante, offrant une 
                        expérience d'achat en ligne inégalée dans la région.
                      </p>
                    </div>
                    <div className="hidden md:block w-12 h-12 rounded-full bg-primary text-white flex-shrink-0 flex items-center justify-center border-4 border-white shadow-lg relative z-10">
                      <span className="text-lg font-bold">3</span>
                    </div>
                    <div className="md:w-1/2 md:pl-12 mt-4 md:mt-0">
                    </div>
                  </div>
                  
                  {/* Item 4 */}
                  <div className="flex flex-col md:flex-row items-center">
                    <div className="md:w-1/2 md:pr-12 md:text-right">
                    </div>
                    <div className="hidden md:block w-12 h-12 rounded-full bg-primary text-white flex-shrink-0 flex items-center justify-center border-4 border-white shadow-lg relative z-10">
                      <span className="text-lg font-bold">4</span>
                    </div>
                    <div className="md:w-1/2 md:pl-12 mt-4 md:mt-0">
                      <h3 className="text-xl font-bold mb-2 text-primary">2025</h3>
                      <p className="text-gray-700">
                        Aujourd'hui, nous sommes fiers d'être reconnus comme un leader 
                        dans le domaine des technologies de l'information et des communications 
                        en Afrique de l'Est.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default About;
