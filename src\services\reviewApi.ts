import axios from 'axios';
import { API_BASE_URL } from '@/config/api';
import { getAuthToken } from '@/utils/auth';

// Types
export interface Review {
  id: number;
  product: number;
  user: number;
  user_email: string;
  user_name: string;
  user_avatar?: string;
  order?: number;
  rating: number;
  title: string;
  comment: string;
  pros?: string;
  cons?: string;
  is_approved: boolean;
  is_verified_purchase: boolean;
  helpful_count: number;
  not_helpful_count: number;
  helpfulness_ratio: number;
  user_has_voted: boolean;
  user_vote_is_helpful?: boolean;
  created_at: string;
  updated_at: string;
}

export interface ReviewStats {
  total_reviews: number;
  average_rating: number;
  rating_distribution: Record<string, number>;
  verified_purchases_count: number;
}

export interface ReviewListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Review[];
}

export interface CreateReviewData {
  product: number;
  rating: number;
  title: string;
  comment: string;
  pros?: string;
  cons?: string;
}

export interface UpdateReviewData {
  rating?: number;
  title?: string;
  comment?: string;
  pros?: string;
  cons?: string;
}

// Review API service
const reviewApi = {
  // Récupérer les avis d'un produit
  getProductReviews: async (productId: number, params?: any): Promise<ReviewListResponse> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/products/reviews/`, {
        params: {
          product: productId,
          ...params
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching product reviews:', error);
      throw error;
    }
  },

  // Récupérer les statistiques d'avis d'un produit
  getProductReviewStats: async (productId: number): Promise<ReviewStats> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/products/reviews/stats/`, {
        params: { product: productId }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching review stats:', error);
      throw error;
    }
  },

  // Vérifier si l'utilisateur peut écrire un avis
  canUserReview: async (productId: number): Promise<{
    can_review: boolean;
    reason: string;
    existing_review_id?: number;
  }> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/products/reviews/can-review/`, {
        params: { product: productId },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error checking review permission:', error);
      throw error;
    }
  },

  // Créer un nouvel avis
  createReview: async (data: CreateReviewData): Promise<Review> => {
    try {
      const response = await axios.post(`${API_BASE_URL}/products/reviews/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating review:', error);

      // Gérer l'erreur de contrainte d'unicité
      if (error.response?.status === 400 && error.response?.data?.detail) {
        const errorMessage = error.response.data.detail;
        if (errorMessage.includes('déjà écrit un avis')) {
          throw new Error('Vous avez déjà écrit un avis pour ce produit. Vous pouvez le modifier depuis votre profil.');
        }
      }

      throw error;
    }
  },

  // Mettre à jour un avis
  updateReview: async (reviewId: number, data: UpdateReviewData): Promise<Review> => {
    try {
      const response = await axios.patch(`${API_BASE_URL}/products/reviews/${reviewId}/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  },

  // Supprimer un avis
  deleteReview: async (reviewId: number): Promise<void> => {
    try {
      await axios.delete(`${API_BASE_URL}/products/reviews/${reviewId}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  },

  // Voter sur l'utilité d'un avis
  voteHelpful: async (reviewId: number, isHelpful: boolean): Promise<{ helpful_count: number; not_helpful_count: number }> => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/products/reviews/${reviewId}/vote_helpful/`,
        { is_helpful: isHelpful },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error voting on review:', error);
      throw error;
    }
  },

  // Récupérer les avis de l'utilisateur connecté
  getMyReviews: async (params?: any): Promise<ReviewListResponse> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/products/reviews/`, {
        params: {
          user: 'me', // Le backend devra gérer ce cas spécial
          ...params
        },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching my reviews:', error);
      throw error;
    }
  },

  // Vérifier si l'utilisateur peut laisser un avis pour un produit
  canReviewProduct: async (productId: number): Promise<{ can_review: boolean; reason?: string }> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/products/reviews/can_review/`, {
        params: { product: productId },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error checking review eligibility:', error);
      throw error;
    }
  },

  // Récupérer un avis spécifique
  getReview: async (reviewId: number): Promise<Review> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/products/reviews/${reviewId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching review:', error);
      throw error;
    }
  },

  // Signaler un avis
  reportReview: async (reviewId: number, reason: string): Promise<void> => {
    try {
      await axios.post(
        `${API_BASE_URL}/products/reviews/${reviewId}/report/`,
        { reason },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
    } catch (error) {
      console.error('Error reporting review:', error);
      throw error;
    }
  }
};

export default reviewApi;
