import { getAuthToken } from '@/utils/auth';
import axios from 'axios';
import { API_BASE_URL } from '@/config/api';

// Types
export interface UserProfile {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone_number: string | null;
  date_of_birth: string | null;
  profile_picture: string | null;
  loyalty_points: number;
  loyalty_tier: string;
  is_verified: boolean;
  addresses: UserAddress[];
  bio: string | null;
  department: string | null;
  position: string | null;
  start_date: string | null;
}

export interface UserAddress {
  id: number;
  address_type: 'shipping' | 'billing' | 'both';
  is_default: boolean;
  first_name: string;
  last_name: string;
  company: string | null;
  address_line1: string;
  address_line2: string | null;
  city: string;
  state: string | null;
  postal_code: string;
  country: string;
  phone: string | null;
  full_address: string;
  created_at?: string;
  updated_at?: string;
}

export interface AddressCreateUpdate {
  address_type: 'shipping' | 'billing' | 'both';
  is_default: boolean;
  first_name: string;
  last_name: string;
  company?: string | null;
  address_line1: string;
  address_line2?: string | null;
  city: string;
  state?: string | null;
  postal_code: string;
  country: string;
  phone?: string | null;
}

export interface LoyaltyTransaction {
  id: number;
  points: number;
  transaction_type: 'earn' | 'redeem' | 'expire' | 'adjust';
  description: string;
  order_number: string | null;
  created_at: string;
  expires_at: string | null;
  is_expired: boolean;
}

export interface LoyaltyInfo {
  id: number;
  email: string;
  loyalty_points: number;
  loyalty_tier: string;
}

export interface DashboardStats {
  orders_count: number;
  pending_orders_count: number;
  returns_count: number;
  loyalty_points: number;
  recent_order?: {
    id: string;
    order_number: string;
    date: string;
    status: string;
    status_display: string;
    total: string;
  };
  is_mock_data?: boolean;
}

// User API service
const userApi = {
  // Get user profile
  getProfile: async (): Promise<UserProfile> => {
    const response = await axios.get(`${API_BASE_URL}/auth/profile/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Update user profile
  updateProfilePicture: async (formData: FormData): Promise<{ profile_picture: string }> => {
    const response = await axios.post(`${API_BASE_URL}/auth/account/upload-picture/`, formData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  updateProfile: async (profileData: Partial<UserProfile>): Promise<UserProfile> => {
    try {
      console.log('🔄 Updating profile with data:', profileData);

      // Utiliser PATCH au lieu de PUT car le backend supporte PATCH
      const response = await axios.patch(`${API_BASE_URL}/auth/profile/`, profileData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Profile updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating profile:', error);

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 405) {
          throw new Error('Méthode non autorisée. Veuillez contacter le support technique.');
        }

        if (error.response?.status === 401) {
          throw new Error('Session expirée. Veuillez vous reconnecter.');
        }

        if (error.response?.status === 400) {
          const errorData = error.response.data;
          if (typeof errorData === 'object' && errorData !== null) {
            // Extraire les erreurs de validation
            const errorMessages: string[] = [];
            Object.keys(errorData).forEach(field => {
              const fieldErrors = errorData[field];
              if (Array.isArray(fieldErrors)) {
                fieldErrors.forEach(errorMsg => {
                  errorMessages.push(`${field}: ${errorMsg}`);
                });
              } else if (typeof fieldErrors === 'string') {
                errorMessages.push(`${field}: ${fieldErrors}`);
              }
            });

            if (errorMessages.length > 0) {
              throw new Error(errorMessages.join('\n'));
            }
          }

          throw new Error(error.response.data?.message || 'Données invalides');
        }

        throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du profil');
      }

      throw new Error('Erreur de connexion au serveur. Veuillez réessayer.');
    }
  },

  // Get user addresses
  getAddresses: async (): Promise<UserAddress[]> => {
    const response = await axios.get(`${API_BASE_URL}/auth/addresses/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });

    // Gérer la pagination - si les données sont paginées, retourner results
    const data = response.data;
    if (data && typeof data === 'object' && 'results' in data) {
      return data.results;
    }

    // Sinon retourner les données directement (si c'est déjà un tableau)
    return data;
  },

  // Get a specific address by ID
  getAddress: async (id: number): Promise<UserAddress> => {
    const response = await axios.get(`${API_BASE_URL}/auth/addresses/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Get a single address
  getAddress: async (id: number): Promise<UserAddress> => {
    const response = await axios.get(`${API_BASE_URL}/auth/addresses/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Create a new address
  createAddress: async (addressData: AddressCreateUpdate): Promise<UserAddress> => {
    const response = await axios.post(`${API_BASE_URL}/auth/addresses/`, addressData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Update an address
  updateAddress: async (id: number, addressData: Partial<AddressCreateUpdate>): Promise<UserAddress> => {
    const response = await axios.put(`${API_BASE_URL}/auth/addresses/${id}/`, addressData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Delete an address
  deleteAddress: async (id: number): Promise<void> => {
    await axios.delete(`${API_BASE_URL}/auth/addresses/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Set an address as default
  setDefaultAddress: async (id: number): Promise<void> => {
    await axios.post(`${API_BASE_URL}/auth/addresses/${id}/set_default/`, {}, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Get loyalty information
  getLoyaltyInfo: async (): Promise<LoyaltyInfo> => {
    const response = await axios.get(`${API_BASE_URL}/auth/loyalty/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Get loyalty transactions
  getLoyaltyTransactions: async (): Promise<LoyaltyTransaction[]> => {
    const response = await axios.get(`${API_BASE_URL}/auth/loyalty/transactions/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Get dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    // Appeler l'API réelle
    try {
      const response = await axios.get(`${API_BASE_URL}/auth/dashboard/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback for dashboard stats');

      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 500));

      // Retourner des données simulées avec indication
      return {
        orders_count: 5,
        pending_orders_count: 1,
        returns_count: 2,
        loyalty_points: 450,
        recent_order: {
          id: "123",
          order_number: "CMD-12345",
          date: "15/05/2025",
          status: "delivered",
          status_display: "Livrée",
          total: "549,780 BIF"
        },
        is_mock_data: true
      };
    }
  }
};

export default userApi;
