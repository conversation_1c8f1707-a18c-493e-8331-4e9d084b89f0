
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  FileText,
  Search,
  Plus,
  Edit,
  Trash2,
  Image,
  Eye,
  Home,
  FileQuestion,
  FileCode,
  Mail,
  Loader2,
  AlertCircle,
  RefreshCw
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import cmsApi, { Page, BlogPost, Media, Menu, MenuItem, SiteSetting } from "@/services/cmsApi";
import { CMSRefreshButton } from '@/components/ui/refresh-button';
import SiteSettingsForm from "@/components/admin/cms/SiteSettingsForm";

// Le CMS utilise maintenant les vraies données de l'API avec fallbacks intégrés

const CMS = () => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("pages");

  // Requêtes pour récupérer les données
  const {
    data: pages,
    isLoading: isLoadingPages,
    isError: isPagesError,
    refetch: refetchPages
  } = useQuery({
    queryKey: ['pages'],
    queryFn: cmsApi.getPages,
    enabled: activeTab === 'pages',
    staleTime: 30000, // 30 secondes
    refetchOnWindowFocus: true,
  });

  const {
    data: blogPosts,
    isLoading: isLoadingBlogPosts,
    isError: isBlogPostsError,
    refetch: refetchBlogPosts
  } = useQuery({
    queryKey: ['blogPosts'],
    queryFn: cmsApi.getBlogPosts,
    enabled: activeTab === 'blog',
    staleTime: 30000, // 30 secondes
    refetchOnWindowFocus: true,
  });

  const {
    data: mediaItems,
    isLoading: isLoadingMedia,
    isError: isMediaError,
    refetch: refetchMedia
  } = useQuery({
    queryKey: ['media'],
    queryFn: cmsApi.getMedia,
    enabled: activeTab === 'media',
  });

  const {
    data: menus,
    isLoading: isLoadingMenus,
    isError: isMenusError,
    refetch: refetchMenus
  } = useQuery({
    queryKey: ['menus'],
    queryFn: cmsApi.getMenus,
    enabled: activeTab === 'menus',
  });

  const {
    data: siteSettings,
    isLoading: isLoadingSettings,
    isError: isSettingsError,
    refetch: refetchSettings
  } = useQuery({
    queryKey: ['siteSettings'],
    queryFn: cmsApi.getCurrentSiteSettings,
    enabled: activeTab === 'settings',
  });

  // Mutations pour les opérations CRUD
  const deletePage = useMutation({
    mutationFn: (slug: string) => cmsApi.deletePage(slug),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pages'] });
      toast({
        title: "Page supprimée",
        description: "La page a été supprimée avec succès",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer la page",
        variant: "destructive",
      });
    }
  });

  const deleteBlogPost = useMutation({
    mutationFn: (slug: string) => cmsApi.deleteBlogPost(slug),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogPosts'] });
      toast({
        title: "Article supprimé",
        description: "L'article a été supprimé avec succès",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer l'article",
        variant: "destructive",
      });
    }
  });

  const deleteMedia = useMutation({
    mutationFn: (id: number) => cmsApi.deleteMedia(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      toast({
        title: "Média supprimé",
        description: "Le média a été supprimé avec succès",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le média",
        variant: "destructive",
      });
    }
  });

  // Fonctions de filtrage pour la recherche selon l'onglet actif
  const getFilteredItems = () => {
    switch (activeTab) {
      case "pages":
        const pagesData = pages?.data || pages || [];
        return pagesData.filter((page: any) =>
          page.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          page.slug.toLowerCase().includes(searchQuery.toLowerCase())
        );
      case "blog":
        const blogData = blogPosts?.data || blogPosts || [];
        return blogData.filter((post: any) =>
          post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.slug.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (post.categories && post.categories.some((cat: any) => cat.name.toLowerCase().includes(searchQuery.toLowerCase())))
        );
      case "media":
        const mediaData = mediaItems?.data || mediaItems || [];
        return mediaData.filter((media: any) =>
          media.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          media.file_type?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          media.name?.toLowerCase().includes(searchQuery.toLowerCase())
        );
      default:
        return [];
    }
  };

  const handleEdit = (id: string, type: string) => {
    // Rediriger vers le formulaire d'édition approprié
    if (type === 'page') {
      window.location.href = `/admin/cms/pages/edit/${id}`;
    } else if (type === 'blog') {
      window.location.href = `/admin/cms/blog/edit/${id}`;
    } else if (type === 'media') {
      window.location.href = `/admin/cms/media`;
    }
  };

  const handleDelete = (id: string, type: string) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer cet élément ?")) {
      if (type === 'page') {
        deletePage.mutate(id);
      } else if (type === 'blog') {
        deleteBlogPost.mutate(id);
      } else if (type === 'media') {
        deleteMedia.mutate(Number(id));
      }
    }
  };

  const handleAdd = () => {
    const itemType = activeTab === "pages"
      ? "page"
      : activeTab === "blog"
      ? "article"
      : "média";

    // Rediriger vers le formulaire de création approprié
    if (activeTab === "pages") {
      window.location.href = "/admin/cms/pages/new";
    } else if (activeTab === "blog") {
      window.location.href = "/admin/cms/blog/new";
    } else if (activeTab === "media") {
      window.location.href = "/admin/cms/media";
    }
  };

  const handleView = (slug: string) => {
    // Ouvrir dans un nouvel onglet
    window.open(`/${slug}`, '_blank');
  };

  const handleRefresh = () => {
    switch (activeTab) {
      case "pages":
        refetchPages();
        break;
      case "blog":
        refetchBlogPosts();
        break;
      case "media":
        refetchMedia();
        break;
      case "menus":
        refetchMenus();
        break;
      case "settings":
        refetchSettings();
        break;
    }
  };

  // Fonction pour obtenir l'icône de la page
  const getPageIcon = (template: string) => {
    switch (template) {
      case "Homepage":
        return <Home size={16} className="mr-2 text-blue-500" />;
      case "Legal":
        return <FileText size={16} className="mr-2 text-orange-500" />;
      case "Services":
        return <FileCode size={16} className="mr-2 text-green-500" />;
      default:
        return <FileQuestion size={16} className="mr-2 text-gray-500" />;
    }
  };

  // Rendu du contenu selon l'onglet actif
  const renderTabContent = () => {
    const filteredItems = getFilteredItems();

    switch (activeTab) {
      case "pages":
        if (isLoadingPages) {
          return (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Chargement des pages...</span>
            </div>
          );
        }

        if (isPagesError) {
          return (
            <div className="flex items-center justify-center py-8 text-amber-600">
              <AlertCircle className="h-6 w-6 mr-2" />
              <span>Utilisation des données de fallback (API non disponible)</span>
            </div>
          );
        }

        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Titre</TableHead>
                <TableHead>URL</TableHead>
                <TableHead>Template</TableHead>
                <TableHead>Dernière MàJ</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    Aucune page trouvée
                  </TableCell>
                </TableRow>
              ) : (
                filteredItems.map((page: any) => (
                  <TableRow key={page.id}>
                    <TableCell>
                      <div className="flex items-center">
                        {getPageIcon(page.template)}
                        <span>{page.title}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{page.slug}</code>
                    </TableCell>
                    <TableCell>{page.template || 'Standard'}</TableCell>
                    <TableCell>
                      {page.updated_at ? new Date(page.updated_at).toLocaleDateString('fr-FR') : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        page.status === "published" ? "bg-green-100 text-green-800" : "bg-amber-100 text-amber-800"
                      }`}>
                        {page.status === "published" ? "Publié" : "Brouillon"}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleView(page.slug)}
                        >
                          <Eye size={16} className="text-gray-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(page.id, 'page')}
                        >
                          <Edit size={16} className="text-blue-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(page.id, 'page')}
                        >
                          <Trash2 size={16} className="text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        );
      case "blog":
        if (isLoadingBlogPosts) {
          return (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Chargement des articles...</span>
            </div>
          );
        }

        if (isBlogPostsError) {
          return (
            <div className="flex items-center justify-center py-8 text-amber-600">
              <AlertCircle className="h-6 w-6 mr-2" />
              <span>Utilisation des données de fallback (API non disponible)</span>
            </div>
          );
        }

        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Titre</TableHead>
                <TableHead>URL</TableHead>
                <TableHead>Catégorie</TableHead>
                <TableHead>Mis en avant</TableHead>
                <TableHead>Dernière MàJ</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    Aucun article trouvé
                  </TableCell>
                </TableRow>
              ) : (
                filteredItems.map((post: any) => (
                  <TableRow key={post.id}>
                    <TableCell className="font-medium">{post.title}</TableCell>
                    <TableCell>
                      <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{post.slug}</code>
                    </TableCell>
                    <TableCell>
                      {post.categories && post.categories.length > 0
                        ? post.categories[0].name
                        : 'Sans catégorie'
                      }
                    </TableCell>
                    <TableCell>
                      {post.featured ? (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          ⭐ Oui
                        </span>
                      ) : (
                        <span className="text-gray-500">Non</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {post.updated_at ? new Date(post.updated_at).toLocaleDateString('fr-FR') : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        post.status === "published" ? "bg-green-100 text-green-800" : "bg-amber-100 text-amber-800"
                      }`}>
                        {post.status === "published" ? "Publié" : "Brouillon"}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleView(post.slug)}
                        >
                          <Eye size={16} className="text-gray-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(post.id, 'blog')}
                        >
                          <Edit size={16} className="text-blue-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(post.id, 'blog')}
                        >
                          <Trash2 size={16} className="text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        );
      case "media":
        return (
          <div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredItems.map((media: any) => (
                <Card key={media.id} className="overflow-hidden">
                  <div className="h-40 bg-gray-100 flex items-center justify-center">
                    {media.file_type?.startsWith("image/") || media.type?.startsWith("image/") ? (
                      <div className="bg-gray-200 w-full h-full flex items-center justify-center">
                        <Image size={32} className="text-gray-400" />
                      </div>
                    ) : (
                      <div className="bg-gray-200 w-full h-full flex items-center justify-center">
                        <FileText size={32} className="text-gray-400" />
                      </div>
                    )}
                  </div>
                  <CardContent className="p-3">
                    <div className="font-medium truncate">{media.title || media.name || 'Fichier sans nom'}</div>
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>
                        {(media.file_type || media.type || 'unknown/unknown').split('/')[1]?.toUpperCase() || 'UNKNOWN'}
                      </span>
                      <span>{media.file_size || media.size || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between mt-3">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(media.id, 'media')}
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(media.id, 'media')}
                      >
                        <Trash2 size={16} className="text-red-500" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );
      case "templates":
        return (
          <div className="text-center py-12 space-y-3">
            <FileCode size={48} className="mx-auto text-gray-400" />
            <h3 className="text-lg font-medium">Gestion des modèles</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              La gestion des modèles de page est disponible uniquement pour les administrateurs techniques. Contactez l'équipe développement pour créer ou modifier des modèles.
            </p>
          </div>
        );
      case "menus":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Menu principal</CardTitle>
                <CardDescription>Navigation principale du site</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {["Accueil", "Produits", "Services", "Blog", "Contact"].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                      <span>{item}</span>
                      <Button variant="ghost" size="icon">
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full mt-4">
                    <Plus size={16} className="mr-2" />
                    Ajouter un élément
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Menu pied de page</CardTitle>
                <CardDescription>Navigation du pied de page</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {["À propos", "CGV", "Politique de confidentialité", "FAQ", "Contact"].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                      <span>{item}</span>
                      <Button variant="ghost" size="icon">
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full mt-4">
                    <Plus size={16} className="mr-2" />
                    Ajouter un élément
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case "settings":
        return <SiteSettingsForm />;
      case "forms":
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom du formulaire</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Envois</TableHead>
                <TableHead>Dernière soumission</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                { name: "Contact", desc: "Formulaire de contact principal", submissions: 48, lastSubmission: "15/05/2025" },
                { name: "Demande de devis", desc: "Demande de devis professionnels", submissions: 23, lastSubmission: "14/05/2025" },
                { name: "Newsletter", desc: "Inscription à la newsletter", submissions: 156, lastSubmission: "15/05/2025" },
                { name: "Support technique", desc: "Demande d'assistance", submissions: 37, lastSubmission: "15/05/2025" }
              ].map((form, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{form.name}</TableCell>
                  <TableCell>{form.desc}</TableCell>
                  <TableCell>{form.submissions}</TableCell>
                  <TableCell>{form.lastSubmission}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                      >
                        <Mail size={16} className="mr-1" />
                        Voir les messages
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion du Contenu</h1>
          <p className="text-gray-500">Gérez les pages, articles et médias de votre site</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <TabsList>
              <TabsTrigger value="pages">Pages</TabsTrigger>
              <TabsTrigger value="blog">Blog</TabsTrigger>
              <TabsTrigger value="media">Média</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="menus">Menus</TabsTrigger>
              <TabsTrigger value="forms">Formulaires</TabsTrigger>
              <TabsTrigger value="settings">Paramètres</TabsTrigger>
            </TabsList>

            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              <div className="relative w-full sm:w-auto">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Rechercher..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <CMSRefreshButton
                  refetchFn={handleRefresh}
                  iconOnly={true}
                  size="icon"
                />
                {(activeTab === "pages" || activeTab === "blog" || activeTab === "media") && (
                  <Button onClick={handleAdd} className="flex items-center gap-1 w-full sm:w-auto">
                    <Plus size={16} />
                    Ajouter {activeTab === "pages" ? "une page" : activeTab === "blog" ? "un article" : "un média"}
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div className="border rounded-lg">
            <TabsContent value={activeTab} className="p-0">
              <div className={activeTab === "media" ? "p-4" : ""}>
                {renderTabContent()}
              </div>
            </TabsContent>
          </div>
        </Tabs>

        {(activeTab === "pages" || activeTab === "blog" || activeTab === "media") && (
          <div className="text-sm text-gray-500">
            Affichage de {getFilteredItems().length} éléments sur {
              activeTab === "pages" ? (pages?.data?.length || 0) :
              activeTab === "blog" ? (blogPosts?.data?.length || 0) :
              activeTab === "media" ? (mediaItems?.data?.length || 0) : 0
            }
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default CMS;
