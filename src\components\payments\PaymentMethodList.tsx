import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  CreditCard, 
  CheckCircle,
  XCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import paymentApi, { PaymentMethod } from '@/services/paymentApi';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const PaymentMethodList: React.FC = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [methodToDelete, setMethodToDelete] = useState<number | null>(null);
  
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      const data = await paymentApi.getPaymentMethods();
      // Vérifier si data est un tableau ou un objet avec une propriété results
      if (Array.isArray(data)) {
        setPaymentMethods(data);
      } else if (data && typeof data === 'object') {
        // Si c'est un objet, vérifier s'il a une propriété results qui est un tableau
        const dataObj = data as any; // Cast explicite pour éviter les erreurs TypeScript
        if (dataObj.results && Array.isArray(dataObj.results)) {
          setPaymentMethods(dataObj.results);
        } else {
          // Si ce n'est pas un tableau et n'a pas de propriété results, utiliser un tableau vide
          console.error('Format de données inattendu:', data);
          setPaymentMethods([]);
        }
      } else {
        // Fallback à un tableau vide si data n'est ni un tableau ni un objet
        console.error('Format de données inattendu:', data);
        setPaymentMethods([]);
      }
      setError(null);
    } catch (err: any) {
      console.error('Erreur lors de la récupération des méthodes de paiement:', err);
      setError(err.response?.data?.detail || 'Failed to fetch payment methods');
      toast({
        title: "Error",
        description: "Failed to fetch payment methods",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (id: number) => {
    navigate(`/admin/payment-methods/${id}/edit`);
  };

  const handleDelete = async () => {
    if (!methodToDelete) return;
    
    try {
      await paymentApi.deletePaymentMethod(methodToDelete);
      setPaymentMethods(paymentMethods.filter(method => method.id !== methodToDelete));
      toast({
        title: "Success",
        description: "Payment method deleted successfully",
      });
    } catch (err: any) {
      toast({
        title: "Error",
        description: err.response?.data?.detail || "Failed to delete payment method",
        variant: "destructive",
      });
    } finally {
      setDeleteDialogOpen(false);
      setMethodToDelete(null);
    }
  };

  const confirmDelete = (id: number) => {
    setMethodToDelete(id);
    setDeleteDialogOpen(true);
  };

  const filteredMethods = paymentMethods.filter(method => 
    method.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    method.provider.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (method.description && method.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'stripe':
        return <CreditCard className="h-5 w-5 text-blue-500" />;
      case 'paypal':
        return <CreditCard className="h-5 w-5 text-indigo-500" />;
      default:
        return <CreditCard className="h-5 w-5 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-4">
            <p className="text-red-500">{error}</p>
            <Button onClick={fetchPaymentMethods} className="mt-2">Retry</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Payment Methods</CardTitle>
        <Button onClick={() => navigate('/admin/payment-methods/create')} className="flex items-center gap-1">
          <Plus size={16} />
          Add Method
        </Button>
      </CardHeader>
      <CardContent>
        <div className="mb-4 relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search payment methods..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {filteredMethods.length === 0 ? (
          <div className="text-center p-4">
            <p className="text-gray-500">No payment methods found</p>
          </div>
        ) : (
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Provider</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Default</TableHead>
                  <TableHead>Fees</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMethods.map((method) => (
                  <TableRow key={method.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {getProviderIcon(method.provider)}
                        {method.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{method.provider}</Badge>
                    </TableCell>
                    <TableCell>
                      {method.is_active ? (
                        <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Active</Badge>
                      ) : (
                        <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {method.is_default ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-gray-300" />
                      )}
                    </TableCell>
                    <TableCell>
                      {typeof method.fee_fixed === 'number' && method.fee_fixed > 0 && `${method.fee_fixed.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}`}
                      {typeof method.fee_fixed === 'number' && method.fee_fixed > 0 && typeof method.fee_percentage === 'number' && method.fee_percentage > 0 && ' + '}
                      {typeof method.fee_percentage === 'number' && method.fee_percentage > 0 && `${method.fee_percentage}%`}
                      {(typeof method.fee_fixed !== 'number' || method.fee_fixed === 0) && (typeof method.fee_percentage !== 'number' || method.fee_percentage === 0) && 'Free'}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(method.id)}
                        >
                          <Edit size={16} className="text-blue-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => confirmDelete(method.id)}
                        >
                          <Trash2 size={16} className="text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the payment method.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

export default PaymentMethodList;
