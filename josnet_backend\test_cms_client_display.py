#!/usr/bin/env python
"""
Test complet du CMS et affichage côté client.
Vérifie que tout ce que l'admin publie s'affiche bien côté client.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from cms.models import Page, BlogPost, BlogCategory
from core.models import SiteSettings, Service, Testimonial, Banner, FAQ, ActivityLog

User = get_user_model()


def create_admin_user():
    """Créer un utilisateur admin pour les tests CMS."""
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Admin',
            'last_name': '<PERSON><PERSON>',
            'password': 'adminpass123',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'role': 'admin'
        }
    )
    
    if created:
        admin_user.set_password('adminpass123')
        admin_user.save()
    else:
        admin_user.role = 'admin'
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.save()
    
    return admin_user


def test_cms_pages_admin_to_client():
    """Test que les pages CMS créées par l'admin s'affichent côté client."""
    print("📄 TEST: PAGES CMS - ADMIN → CLIENT")
    print("=" * 60)
    
    admin_user = create_admin_user()
    
    # Client admin (avec authentification)
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    admin_client = APIClient()
    admin_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    # Client public (sans authentification)
    public_client = APIClient()
    
    # Nettoyer les anciennes pages de test
    Page.objects.filter(title__contains='Test CMS Client').delete()
    
    print(f"🔄 ÉTAPE 1: Admin crée une page...")
    
    # Admin crée une page
    page_data = {
        'title': 'Test CMS Client - À propos de JosNet',
        'slug': 'test-cms-client-a-propos',
        'content': '''
        <div class="about-section">
            <h1>À propos de JosNet Network</h1>
            <p>JosNet Network est votre partenaire technologique de confiance au Burundi depuis plus de 10 ans.</p>
            
            <h2>Notre Mission</h2>
            <p>Fournir des solutions IT et télécommunications innovantes pour accompagner la transformation digitale du Burundi.</p>
            
            <h2>Nos Valeurs</h2>
            <ul>
                <li>Excellence technique</li>
                <li>Service client de qualité</li>
                <li>Innovation continue</li>
                <li>Engagement communautaire</li>
            </ul>
            
            <h2>Notre Équipe</h2>
            <p>Une équipe de 25+ experts passionnés par la technologie.</p>
        </div>
        ''',
        'template': 'about',
        'status': 'published',
        'meta_description': 'Découvrez JosNet Network, votre partenaire IT au Burundi',
        'meta_keywords': 'JosNet, IT, Burundi, technologie, à propos'
    }
    
    response = admin_client.post('/api/v1/cms/pages/', page_data, format='json')
    
    if response.status_code == 201:
        page = response.json()
        print(f"   ✅ Page créée par admin: {page['title']}")
        print(f"   📝 Slug: {page['slug']}")
        print(f"   📊 Statut: {page['status']}")
        
        print(f"\n🔄 ÉTAPE 2: Client public accède à la page...")
        
        # Client public accède à la page
        response = public_client.get(f"/api/v1/cms/pages/{page['slug']}/")
        
        if response.status_code == 200:
            client_page = response.json()
            print(f"   ✅ Page accessible côté client")
            print(f"   📝 Titre: {client_page['title']}")
            print(f"   📄 Contenu: {len(client_page['content'])} caractères")
            print(f"   🎨 Template: {client_page['template']}")
            print(f"   🔍 SEO - Description: {client_page['meta_description'][:50]}...")
            
            # Vérifier que le contenu est bien présent
            if 'JosNet Network' in client_page['content'] and 'Notre Mission' in client_page['content']:
                print(f"   ✅ Contenu complet transmis au client")
                return True
            else:
                print(f"   ❌ Contenu incomplet côté client")
                return False
        else:
            print(f"   ❌ Page non accessible côté client: {response.status_code}")
            return False
    else:
        print(f"   ❌ Erreur création page: {response.status_code}")
        return False


def test_blog_admin_to_client():
    """Test que les articles de blog créés par l'admin s'affichent côté client."""
    print("\n📝 TEST: BLOG - ADMIN → CLIENT")
    print("=" * 60)
    
    admin_user = create_admin_user()
    
    # Client admin
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    admin_client = APIClient()
    admin_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    # Client public
    public_client = APIClient()
    
    # Nettoyer les anciennes données de test
    BlogPost.objects.filter(title__contains='Test CMS Client').delete()
    BlogCategory.objects.filter(name__contains='Test CMS Client').delete()
    
    print(f"🔄 ÉTAPE 1: Admin crée une catégorie de blog...")
    
    # Admin crée une catégorie
    category_data = {
        'name': 'Test CMS Client - Actualités Tech',
        'slug': 'test-cms-client-actualites-tech',
        'description': 'Les dernières actualités technologiques au Burundi'
    }
    
    response = admin_client.post('/api/v1/cms/blog/categories/', category_data, format='json')
    
    if response.status_code == 201:
        category = response.json()
        print(f"   ✅ Catégorie créée: {category['name']}")
        
        print(f"\n🔄 ÉTAPE 2: Admin crée un article de blog...")
        
        # Admin crée un article
        blog_data = {
            'title': 'Test CMS Client - La 5G arrive au Burundi',
            'slug': 'test-cms-client-5g-burundi',
            'content': '''
            <article class="blog-post">
                <h1>La 5G arrive enfin au Burundi !</h1>
                
                <p class="lead">Une révolution technologique s'annonce au Burundi avec l'arrivée imminente de la 5G.</p>
                
                <h2>Qu'est-ce que la 5G ?</h2>
                <p>La 5G est la cinquième génération de technologie mobile qui promet des vitesses de connexion jusqu'à 100 fois plus rapides que la 4G.</p>
                
                <h2>Impact sur le Burundi</h2>
                <ul>
                    <li>Amélioration des services de santé à distance</li>
                    <li>Développement de l'agriculture intelligente</li>
                    <li>Boost pour l'éducation numérique</li>
                    <li>Opportunités pour les startups tech</li>
                </ul>
                
                <h2>JosNet Network, pionnier de la 5G</h2>
                <p>JosNet Network travaille activement avec les opérateurs pour déployer cette technologie révolutionnaire.</p>
                
                <blockquote>
                    "La 5G va transformer la façon dont nous travaillons, apprenons et vivons au Burundi."
                    <cite>- Équipe JosNet Network</cite>
                </blockquote>
            </article>
            ''',
            'excerpt': 'Découvrez comment la 5G va révolutionner les télécommunications au Burundi et l\'impact sur notre société.',
            'status': 'published',
            'featured': True,
            'categories': [category['id']],
            'author_name': 'Admin CMS Test',
            'author_email': '<EMAIL>',
            'meta_description': 'La 5G arrive au Burundi - JosNet Network',
            'meta_keywords': '5G, Burundi, télécommunications, JosNet, technologie'
        }
        
        response = admin_client.post('/api/v1/cms/blog/posts/', blog_data, format='json')
        
        if response.status_code == 201:
            article = response.json()
            print(f"   ✅ Article créé: {article['title']}")
            print(f"   ⭐ Mis en avant: {article['featured']}")
            
            print(f"\n🔄 ÉTAPE 3: Client public accède aux articles...")
            
            # Client public accède à la liste des articles
            response = public_client.get('/api/v1/cms/blog/posts/')
            
            if response.status_code == 200:
                articles_list = response.json()
                articles = articles_list.get('results', [])
                
                print(f"   ✅ Liste des articles accessible: {len(articles)} articles")
                
                # Trouver notre article de test
                test_article = None
                for art in articles:
                    if 'Test CMS Client' in art['title']:
                        test_article = art
                        break
                
                if test_article:
                    print(f"   ✅ Article de test trouvé dans la liste")
                    print(f"   📝 Titre: {test_article['title']}")
                    print(f"   📄 Extrait: {test_article['excerpt'][:50]}...")
                    
                    # Accéder à l'article complet
                    response = public_client.get(f"/api/v1/cms/blog/posts/{test_article['slug']}/")
                    
                    if response.status_code == 200:
                        full_article = response.json()
                        print(f"   ✅ Article complet accessible")
                        print(f"   📄 Contenu: {len(full_article['content'])} caractères")
                        print(f"   👤 Auteur: {full_article['author_name']}")
                        
                        # Vérifier le contenu
                        if '5G' in full_article['content'] and 'JosNet Network' in full_article['content']:
                            print(f"   ✅ Contenu complet transmis au client")
                            return True
                        else:
                            print(f"   ❌ Contenu incomplet")
                            return False
                    else:
                        print(f"   ❌ Article complet non accessible: {response.status_code}")
                        return False
                else:
                    print(f"   ❌ Article de test non trouvé dans la liste")
                    return False
            else:
                print(f"   ❌ Liste des articles non accessible: {response.status_code}")
                return False
        else:
            print(f"   ❌ Erreur création article: {response.status_code}")
            return False
    else:
        print(f"   ❌ Erreur création catégorie: {response.status_code}")
        return False


def test_site_settings_admin_to_client():
    """Test que les paramètres du site sont accessibles côté client."""
    print("\n⚙️  TEST: PARAMÈTRES DU SITE - ADMIN → CLIENT")
    print("=" * 60)
    
    print(f"🔄 ÉTAPE 1: Configuration des paramètres du site...")
    
    # Créer ou mettre à jour les paramètres du site
    site_settings, created = SiteSettings.objects.get_or_create(
        defaults={
            'site_name': 'JosNet Network - Test CMS',
            'site_description': 'Votre partenaire technologique de confiance au Burundi - Version Test CMS',
            'phone_primary': '+257 22 123 456',
            'phone_secondary': '+257 79 123 456',
            'email_primary': '<EMAIL>',
            'email_secondary': '<EMAIL>',
            'address_line1': '123 Avenue de l\'Indépendance',
            'address_line2': 'Quartier Rohero',
            'city': 'Bujumbura',
            'country': 'Burundi',
            'business_hours': 'Lun-Ven: 8h00-18h00, Sam: 8h00-13h00, Dim: Fermé',
            'facebook_url': 'https://facebook.com/josnetnetwork',
            'twitter_url': 'https://twitter.com/josnetnetwork',
            'linkedin_url': 'https://linkedin.com/company/josnetnetwork',
            'instagram_url': 'https://instagram.com/josnetnetwork',
            'meta_description': 'JosNet Network - Solutions IT et télécommunications au Burundi',
            'meta_keywords': 'JosNet, IT, télécommunications, Burundi, technologie'
        }
    )
    
    if not created:
        # Mettre à jour les paramètres existants
        site_settings.site_name = 'JosNet Network - Test CMS'
        site_settings.site_description = 'Votre partenaire technologique de confiance au Burundi - Version Test CMS'
        site_settings.save()
    
    print(f"   ✅ Paramètres configurés: {site_settings.site_name}")
    
    print(f"\n🔄 ÉTAPE 2: Client public accède aux paramètres...")
    
    # Client public accède aux paramètres
    public_client = APIClient()
    response = public_client.get('/api/v1/core/site-settings/')
    
    if response.status_code == 200:
        settings_data = response.json()
        print(f"   ✅ Paramètres accessibles côté client")
        print(f"   🏢 Nom du site: {settings_data.get('site_name', 'N/A')}")
        print(f"   📧 Email principal: {settings_data.get('email_primary', 'N/A')}")
        print(f"   📞 Téléphone: {settings_data.get('phone_primary', 'N/A')}")
        print(f"   📍 Adresse: {settings_data.get('address_line1', 'N/A')}")
        print(f"   🕒 Horaires: {settings_data.get('business_hours', 'N/A')}")
        
        # Vérifier que les données sont complètes
        required_fields = ['site_name', 'email_primary', 'phone_primary', 'address_line1']
        missing_fields = [field for field in required_fields if not settings_data.get(field)]
        
        if not missing_fields:
            print(f"   ✅ Toutes les données essentielles sont présentes")
            return True
        else:
            print(f"   ❌ Champs manquants: {missing_fields}")
            return False
    else:
        print(f"   ❌ Paramètres non accessibles: {response.status_code}")
        return False


def test_journalisation():
    """Test du système de journalisation."""
    print("\n📝 TEST: SYSTÈME DE JOURNALISATION")
    print("=" * 60)
    
    print(f"🔄 Vérification des logs d'activité...")
    
    # Compter les logs récents
    recent_logs = ActivityLog.objects.all().order_by('-timestamp')[:10]
    
    print(f"   📊 Logs récents trouvés: {recent_logs.count()}")
    
    if recent_logs.exists():
        print(f"   ✅ Système de journalisation actif")
        
        # Afficher les 5 derniers logs
        print(f"\n   📋 Dernières activités:")
        for i, log in enumerate(recent_logs[:5], 1):
            timestamp = log.timestamp.strftime('%d/%m/%Y %H:%M:%S')
            action = log.get_action_type_display()
            user = log.user_email or 'Système'
            print(f"      {i}. {timestamp} - {action} - {user}")
        
        return True
    else:
        print(f"   ⚠️  Aucun log trouvé - Système de journalisation peut-être inactif")
        return False


def main():
    """Fonction principale."""
    print("🚀 TEST COMPLET CMS ET AFFICHAGE CÔTÉ CLIENT")
    print("=" * 80)
    print("Ce test vérifie que tout ce que l'admin publie dans son dashboard")
    print("s'affiche correctement côté client (frontend).")
    print("=" * 80)
    
    results = []
    
    try:
        # Test 1: Pages CMS
        print("🧪 PHASE 1: Test des pages CMS")
        pages_success = test_cms_pages_admin_to_client()
        results.append(("Pages CMS - Admin → Client", pages_success))
        
        # Test 2: Blog
        print("\n🧪 PHASE 2: Test du blog")
        blog_success = test_blog_admin_to_client()
        results.append(("Blog - Admin → Client", blog_success))
        
        # Test 3: Paramètres du site
        print("\n🧪 PHASE 3: Test des paramètres du site")
        settings_success = test_site_settings_admin_to_client()
        results.append(("Paramètres du site - Admin → Client", settings_success))
        
        # Test 4: Journalisation
        print("\n🧪 PHASE 4: Test de la journalisation")
        logging_success = test_journalisation()
        results.append(("Système de journalisation", logging_success))
        
        # Résumé final
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ DU TEST CMS ET AFFICHAGE CLIENT")
        print("=" * 80)
        
        passed = 0
        failed = 0
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print(f"\nTotal: {len(results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print(f"\n🎉 CMS ET AFFICHAGE CLIENT 100% OPÉRATIONNELS !")
            print(f"✅ Les pages CMS créées par l'admin s'affichent côté client")
            print(f"✅ Les articles de blog sont accessibles publiquement")
            print(f"✅ Les paramètres du site sont transmis au frontend")
            print(f"✅ La journalisation fonctionne")
            print(f"\n🌐 LE CONTENU ADMIN EST BIEN AFFICHÉ CÔTÉ CLIENT !")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué")
            print(f"🔧 Vérifiez les composants défaillants")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DES TESTS CMS: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
