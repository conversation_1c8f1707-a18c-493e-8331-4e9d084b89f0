import React, { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { 
  Search, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Tag, 
  Package, 
  AlertCircle, 
  Save, 
  Clock 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';
import { productApi, ProductListItem } from '@/services/productApi';
import { OptimizedImage } from '@/components/ui/optimized-image';
import { debounce } from 'lodash';

interface AdvancedSearchProps {
  onProductSelect?: (product: ProductListItem) => void;
  onSaveSearch?: (searchParams: SearchParams) => void;
  savedSearches?: SavedSearch[];
}

interface SearchParams {
  query: string;
  searchIn: string[];
  minPrice?: number;
  maxPrice?: number;
  categories?: number[];
  status?: string[];
  inStock?: boolean;
}

interface SavedSearch {
  id: string;
  name: string;
  params: SearchParams;
  createdAt: string;
}

interface HighlightedTextProps {
  text: string;
  highlight: string;
}

// Component to highlight search terms in text
const HighlightedText: React.FC<HighlightedTextProps> = ({ text, highlight }) => {
  if (!highlight.trim()) {
    return <span>{text}</span>;
  }
  
  const regex = new RegExp(`(${highlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);
  
  return (
    <span>
      {parts.map((part, i) => 
        regex.test(part) ? (
          <mark key={i} className="bg-yellow-200 px-0.5 rounded">{part}</mark>
        ) : (
          <span key={i}>{part}</span>
        )
      )}
    </span>
  );
};

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({ 
  onProductSelect, 
  onSaveSearch,
  savedSearches = []
}) => {
  // Search parameters
  const [searchParams, setSearchParams] = useState<SearchParams>({
    query: '',
    searchIn: ['name', 'description', 'sku'],
    minPrice: undefined,
    maxPrice: undefined,
    categories: undefined,
    status: undefined,
    inStock: undefined,
  });
  
  // UI state
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [isSavedSearchesOpen, setIsSavedSearchesOpen] = useState(false);
  const [newSearchName, setNewSearchName] = useState('');
  const [debouncedSearchParams, setDebouncedSearchParams] = useState(searchParams);
  
  // Debounce search params changes
  const debouncedSetSearchParams = useCallback(
    debounce((params: SearchParams) => {
      setDebouncedSearchParams(params);
    }, 500),
    []
  );
  
  // Update debounced search params when searchParams changes
  useEffect(() => {
    debouncedSetSearchParams(searchParams);
  }, [searchParams, debouncedSetSearchParams]);
  
  // Fetch categories for filtering
  const { 
    data: categories = [], 
    isLoading: isLoadingCategories 
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => productApi.getCategories(),
  });
  
  // Search products
  const { 
    data: searchResults, 
    isLoading: isSearching,
    isError: isSearchError
  } = useQuery({
    queryKey: ['productSearch', debouncedSearchParams],
    queryFn: () => productApi.searchProducts(debouncedSearchParams),
    enabled: !!debouncedSearchParams.query && debouncedSearchParams.query.length >= 2,
  });
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      if (name === 'searchIn') {
        // Handle searchIn checkboxes
        setSearchParams(prev => {
          const newSearchIn = checked 
            ? [...prev.searchIn, value]
            : prev.searchIn.filter(item => item !== value);
          
          return { ...prev, searchIn: newSearchIn };
        });
      } else if (name === 'status') {
        // Handle status checkboxes
        setSearchParams(prev => {
          const currentStatus = prev.status || [];
          const newStatus = checked
            ? [...currentStatus, value]
            : currentStatus.filter(item => item !== value);
          
          return { ...prev, status: newStatus.length ? newStatus : undefined };
        });
      } else if (name === 'categories') {
        // Handle category checkboxes
        setSearchParams(prev => {
          const currentCategories = prev.categories || [];
          const categoryId = parseInt(value);
          const newCategories = checked
            ? [...currentCategories, categoryId]
            : currentCategories.filter(id => id !== categoryId);
          
          return { ...prev, categories: newCategories.length ? newCategories : undefined };
        });
      } else if (name === 'inStock') {
        // Handle inStock checkbox
        setSearchParams(prev => ({
          ...prev,
          inStock: checked || undefined
        }));
      }
    } else {
      // Handle text/number inputs
      setSearchParams(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Clear search
  const clearSearch = () => {
    setSearchParams({
      query: '',
      searchIn: ['name', 'description', 'sku'],
      minPrice: undefined,
      maxPrice: undefined,
      categories: undefined,
      status: undefined,
      inStock: undefined,
    });
    setIsAdvancedOpen(false);
  };
  
  // Save current search
  const saveCurrentSearch = () => {
    if (!newSearchName.trim() || !onSaveSearch) return;
    
    onSaveSearch({
      query: searchParams.query,
      searchIn: searchParams.searchIn,
      minPrice: searchParams.minPrice,
      maxPrice: searchParams.maxPrice,
      categories: searchParams.categories,
      status: searchParams.status,
      inStock: searchParams.inStock,
    });
    
    setNewSearchName('');
  };
  
  // Load a saved search
  const loadSavedSearch = (search: SavedSearch) => {
    setSearchParams(search.params);
    setIsSavedSearchesOpen(false);
  };
  
  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (searchParams.minPrice !== undefined) count++;
    if (searchParams.maxPrice !== undefined) count++;
    if (searchParams.categories && searchParams.categories.length > 0) count++;
    if (searchParams.status && searchParams.status.length > 0) count++;
    if (searchParams.inStock !== undefined) count++;
    return count;
  };
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Recherche avancée</CardTitle>
        <CardDescription>
          Recherchez des produits avec des filtres avancés
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Main search input */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              name="query"
              placeholder="Rechercher des produits..."
              className="pl-8"
              value={searchParams.query}
              onChange={handleInputChange}
            />
            {searchParams.query && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 h-7 w-7"
                onClick={() => setSearchParams(prev => ({ ...prev, query: '' }))}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <Button
            variant="outline"
            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
            className="gap-1"
          >
            Filtres
            {getActiveFilterCount() > 0 && (
              <Badge className="ml-1 h-5 w-5 rounded-full p-0 flex items-center justify-center">
                {getActiveFilterCount()}
              </Badge>
            )}
            {isAdvancedOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          
          {savedSearches.length > 0 && (
            <Button
              variant="outline"
              onClick={() => setIsSavedSearchesOpen(!isSavedSearchesOpen)}
            >
              <Clock className="h-4 w-4 mr-2" />
              Récentes
            </Button>
          )}
        </div>
        
        {/* Search in options */}
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="search-name"
              name="searchIn"
              value="name"
              checked={searchParams.searchIn.includes('name')}
              onCheckedChange={e => handleInputChange({
                target: { name: 'searchIn', value: 'name', type: 'checkbox', checked: !!e }
              } as any)}
            />
            <Label htmlFor="search-name">Nom</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="search-description"
              name="searchIn"
              value="description"
              checked={searchParams.searchIn.includes('description')}
              onCheckedChange={e => handleInputChange({
                target: { name: 'searchIn', value: 'description', type: 'checkbox', checked: !!e }
              } as any)}
            />
            <Label htmlFor="search-description">Description</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="search-sku"
              name="searchIn"
              value="sku"
              checked={searchParams.searchIn.includes('sku')}
              onCheckedChange={e => handleInputChange({
                target: { name: 'searchIn', value: 'sku', type: 'checkbox', checked: !!e }
              } as any)}
            />
            <Label htmlFor="search-sku">SKU</Label>
          </div>
        </div>
        
        {/* Advanced filters */}
        <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <CollapsibleContent className="space-y-4 pt-2">
            {/* Price range */}
            <div className="space-y-2">
              <Label>Plage de prix</Label>
              <div className="flex gap-2 items-center">
                <Input
                  type="number"
                  name="minPrice"
                  placeholder="Min"
                  value={searchParams.minPrice || ''}
                  onChange={handleInputChange}
                  min={0}
                />
                <span>à</span>
                <Input
                  type="number"
                  name="maxPrice"
                  placeholder="Max"
                  value={searchParams.maxPrice || ''}
                  onChange={handleInputChange}
                  min={0}
                />
              </div>
            </div>
            
            {/* Status */}
            <div className="space-y-2">
              <Label>Statut</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-published"
                    name="status"
                    value="published"
                    checked={searchParams.status?.includes('published')}
                    onCheckedChange={e => handleInputChange({
                      target: { name: 'status', value: 'published', type: 'checkbox', checked: !!e }
                    } as any)}
                  />
                  <Label htmlFor="status-published">Publié</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-draft"
                    name="status"
                    value="draft"
                    checked={searchParams.status?.includes('draft')}
                    onCheckedChange={e => handleInputChange({
                      target: { name: 'status', value: 'draft', type: 'checkbox', checked: !!e }
                    } as any)}
                  />
                  <Label htmlFor="status-draft">Brouillon</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-archived"
                    name="status"
                    value="archived"
                    checked={searchParams.status?.includes('archived')}
                    onCheckedChange={e => handleInputChange({
                      target: { name: 'status', value: 'archived', type: 'checkbox', checked: !!e }
                    } as any)}
                  />
                  <Label htmlFor="status-archived">Archivé</Label>
                </div>
              </div>
            </div>
            
            {/* Stock */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="in-stock"
                  name="inStock"
                  checked={searchParams.inStock === true}
                  onCheckedChange={e => handleInputChange({
                    target: { name: 'inStock', type: 'checkbox', checked: !!e }
                  } as any)}
                />
                <Label htmlFor="in-stock">En stock uniquement</Label>
              </div>
            </div>
            
            {/* Categories */}
            <div className="space-y-2">
              <Label>Catégories</Label>
              {isLoadingCategories ? (
                <div className="space-y-2">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-6 w-40" />
                  <Skeleton className="h-6 w-36" />
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                  {categories.map(category => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category.id}`}
                        name="categories"
                        value={category.id.toString()}
                        checked={searchParams.categories?.includes(category.id)}
                        onCheckedChange={e => handleInputChange({
                          target: { name: 'categories', value: category.id.toString(), type: 'checkbox', checked: !!e }
                        } as any)}
                      />
                      <Label htmlFor={`category-${category.id}`}>{category.name}</Label>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Action buttons */}
            <div className="flex justify-between pt-2">
              <Button variant="outline" onClick={clearSearch}>
                Réinitialiser
              </Button>
              
              {onSaveSearch && (
                <div className="flex gap-2">
                  <Input
                    placeholder="Nom de la recherche"
                    value={newSearchName}
                    onChange={e => setNewSearchName(e.target.value)}
                    className="w-48"
                  />
                  <Button 
                    onClick={saveCurrentSearch}
                    disabled={!newSearchName.trim()}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Sauvegarder
                  </Button>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
        
        {/* Saved searches */}
        <Collapsible open={isSavedSearchesOpen} onOpenChange={setIsSavedSearchesOpen}>
          <CollapsibleContent className="space-y-2 pt-2">
            <h3 className="text-sm font-medium">Recherches sauvegardées</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {savedSearches.map(search => (
                <div 
                  key={search.id}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer"
                  onClick={() => loadSavedSearch(search)}
                >
                  <div>
                    <div className="font-medium">{search.name}</div>
                    <div className="text-xs text-gray-500">
                      {new Date(search.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <Badge variant="outline">
                    {search.params.query}
                  </Badge>
                </div>
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
        
        {/* Search results */}
        {searchParams.query.length >= 2 && (
          <div className="pt-4 border-t">
            <h3 className="text-sm font-medium mb-2">Résultats de recherche</h3>
            
            {isSearching ? (
              <div className="space-y-2">
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
              </div>
            ) : isSearchError ? (
              <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
                <AlertCircle className="h-5 w-5" />
                <span>Une erreur est survenue lors de la recherche.</span>
              </div>
            ) : searchResults?.results.length === 0 ? (
              <div className="text-center py-8 text-gray-500 border border-dashed rounded-md">
                Aucun résultat trouvé pour "{searchParams.query}".
              </div>
            ) : (
              <div className="space-y-2 max-h-[400px] overflow-y-auto">
                {searchResults?.results.map(product => (
                  <div 
                    key={product.id}
                    className="flex items-center gap-3 p-3 border rounded-md hover:bg-gray-50 cursor-pointer"
                    onClick={() => onProductSelect?.(product)}
                  >
                    <div className="w-12 h-12 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
                      {product.primary_image ? (
                        <OptimizedImage
                          src={product.primary_image.image}
                          alt={product.name}
                          width={48}
                          height={48}
                          objectFit="cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          <Package className="h-6 w-6" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">
                        <HighlightedText 
                          text={product.name} 
                          highlight={searchParams.query} 
                        />
                      </div>
                      
                      <div className="text-sm text-gray-500 flex items-center gap-2">
                        <span>
                          <HighlightedText 
                            text={product.sku} 
                            highlight={searchParams.query} 
                          />
                        </span>
                        <span>•</span>
                        <span>{formatPrice(product.current_price)}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {product.status}
                      </Badge>
                      
                      {product.inventory && (
                        <Badge className={product.inventory.is_in_stock ? 'bg-green-500' : 'bg-red-500'}>
                          {product.inventory.is_in_stock 
                            ? `${product.inventory.available_quantity} en stock` 
                            : 'Rupture'
                          }
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {searchResults && searchResults.count > searchResults.results.length && (
              <div className="text-center mt-2 text-sm text-gray-500">
                {searchResults.count - searchResults.results.length} résultats supplémentaires non affichés
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AdvancedSearch;
