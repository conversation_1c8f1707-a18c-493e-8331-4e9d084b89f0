import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AccountLayout from "@/components/account/AccountLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  MessageSquare,
  Send,
  PlusCircle,
  Loader2,
  RefreshCw,
  AlertCircle,
  PaperclipIcon,
  FileIcon,
  X,
  ArrowLeft,
  Menu
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format, parseISO } from "date-fns";
import { fr } from "date-fns/locale";
import messagingApi, { Conversation, Message as MessageType } from "@/services/messagingApi";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

// Fonction pour formater les dates
const formatDate = (dateString: string) => {
  try {
    return format(parseISO(dateString), "dd/MM/yyyy HH:mm", { locale: fr });
  } catch (error) {
    return dateString;
  }
};

// Fonction pour mapper les statuts API vers les statuts d'affichage
const mapStatusToDisplay = (status: string) => {
  switch (status) {
    case 'new':
      return 'En attente';
    case 'in_progress':
      return 'En cours';
    case 'resolved':
      return 'Répondu';
    case 'closed':
      return 'Fermé';
    default:
      return status;
  }
};

const Messages = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedConversationId, setSelectedConversationId] = useState<number | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [isNewConversationOpen, setIsNewConversationOpen] = useState(false);
  const [newConversationSubject, setNewConversationSubject] = useState("");
  const [newConversationMessage, setNewConversationMessage] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const [showConversationList, setShowConversationList] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024; // lg breakpoint
      setIsMobile(mobile);
      if (!mobile) {
        setShowConversationList(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Récupérer la liste des conversations
  const { data: conversationsData, isLoading: isLoadingConversations, error: conversationsError, refetch: refetchConversations } = useQuery({
    queryKey: ['conversations'],
    queryFn: () => messagingApi.getConversations(),
    staleTime: 1000 * 60 * 10, // 10 minutes (augmenté pour réduire les requêtes)
    retry: 0, // Ne pas réessayer en cas d'échec
    retryDelay: 1000, // Attendre 1 seconde avant de réessayer
    refetchOnWindowFocus: false, // Ne pas rafraîchir automatiquement lors du focus de la fenêtre
  });

  // Récupérer les détails d'une conversation sélectionnée
  const { data: selectedConversation, isLoading: isLoadingConversation } = useQuery({
    queryKey: ['conversation', selectedConversationId],
    queryFn: () => messagingApi.getConversation(selectedConversationId!),
    enabled: !!selectedConversationId,
    staleTime: 1000 * 60 * 5, // 5 minutes (augmenté pour réduire les requêtes)
    retry: 0, // Ne pas réessayer en cas d'échec
    refetchOnWindowFocus: false, // Ne pas rafraîchir automatiquement lors du focus de la fenêtre
  });

  // Mutation pour envoyer un message
  const sendMessageMutation = useMutation({
    mutationFn: (content: string) =>
      messagingApi.createMessage(selectedConversationId!, { content, uploaded_files: selectedFiles }),
    onSuccess: () => {
      // Invalider toutes les requêtes liées aux conversations et messages
      queryClient.invalidateQueries({ queryKey: ['conversation', selectedConversationId] });
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: [`messages_${selectedConversationId}`] });

      // Forcer un rafraîchissement après un court délai
      setTimeout(() => {
        refetchConversations();
      }, 500);

      setNewMessage("");
      setSelectedFiles([]);
      toast({
        title: "Message envoyé",
        description: "Votre message a été envoyé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'envoi du message",
        variant: "destructive",
      });
      console.error("Error sending message:", error);

      // Même en cas d'erreur, essayer de rafraîchir les données
      setTimeout(() => {
        refetchConversations();
      }, 500);
    }
  });

  // Mutation pour créer une nouvelle conversation
  const createConversationMutation = useMutation({
    mutationFn: () =>
      messagingApi.createConversation({
        subject: newConversationSubject,
        initial_message: newConversationMessage,
        uploaded_files: selectedFiles
      }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      setNewConversationSubject("");
      setNewConversationMessage("");
      setSelectedFiles([]);
      setIsNewConversationOpen(false);
      setSelectedConversationId(data.id);
      toast({
        title: "Conversation créée",
        description: "Votre message a été envoyé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création de la conversation",
        variant: "destructive",
      });
      console.error("Error creating conversation:", error);
    }
  });

  // Sélectionner la première conversation au chargement si aucune n'est sélectionnée
  useEffect(() => {
    if (conversationsData?.results?.length > 0 && !selectedConversationId) {
      setSelectedConversationId(conversationsData.results[0].id);
    }
  }, [conversationsData, selectedConversationId]);

  const handleSendMessage = () => {
    if (newMessage.trim() === "" || !selectedConversationId) return;
    sendMessageMutation.mutate(newMessage.trim());
  };

  const handleCreateConversation = () => {
    if (newConversationSubject.trim() === "" || newConversationMessage.trim() === "") return;
    createConversationMutation.mutate();
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setSelectedFiles(prev => [...prev, ...filesArray]);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const formatStatusBadge = (status: string) => {
    switch (status) {
      case "En attente":
        return "bg-yellow-100 text-yellow-800";
      case "En cours":
        return "bg-blue-100 text-blue-800";
      case "Répondu":
        return "bg-green-100 text-green-800";
      case "Fermé":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <AccountLayout title="Mes messages">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">Mes messages</h1>
            <p className="text-gray-500 text-sm sm:text-base">Consultez vos conversations avec notre service client</p>
          </div>
          <div className="flex gap-2">
            {isMobile && selectedConversationId && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedConversationId(null);
                  setShowConversationList(true);
                }}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchConversations()}
              disabled={isLoadingConversations}
            >
              {isLoadingConversations ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              <span className="hidden sm:inline">Actualiser</span>
            </Button>
            <Dialog open={isNewConversationOpen} onOpenChange={setIsNewConversationOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Nouveau message</span>
                  <span className="sm:hidden">Nouveau</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Nouveau message</DialogTitle>
                  <DialogDescription>
                    Envoyez un nouveau message à notre service client
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="subject">Sujet</Label>
                    <Input
                      id="subject"
                      placeholder="Sujet de votre message"
                      value={newConversationSubject}
                      onChange={(e) => setNewConversationSubject(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      placeholder="Votre message..."
                      rows={5}
                      value={newConversationMessage}
                      onChange={(e) => setNewConversationMessage(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="files">Pièces jointes (optionnel)</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="files"
                        type="file"
                        multiple
                        className="hidden"
                        ref={fileInputRef}
                        onChange={handleFileSelect}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <PaperclipIcon className="h-4 w-4 mr-2" />
                        Ajouter des fichiers
                      </Button>
                    </div>
                    {selectedFiles.length > 0 && (
                      <div className="mt-2 space-y-2">
                        {selectedFiles.map((file, index) => (
                          <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                            <div className="flex items-center gap-2 text-sm">
                              <FileIcon className="h-4 w-4" />
                              <span className="truncate max-w-[200px]">{file.name}</span>
                              <span className="text-gray-500 text-xs">
                                ({(file.size / 1024).toFixed(1)} KB)
                              </span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsNewConversationOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button
                    type="button"
                    onClick={handleCreateConversation}
                    disabled={newConversationSubject.trim() === "" || newConversationMessage.trim() === "" || createConversationMutation.isPending}
                  >
                    {createConversationMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Send className="h-4 w-4 mr-2" />
                    )}
                    Envoyer
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {conversationsError ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-800">Erreur de chargement</h3>
              <p className="text-red-700 text-sm">Impossible de charger vos messages. Veuillez réessayer.</p>
            </div>
          </div>
        ) : conversationsData?.results?.length === 0 ? (
          <div className="bg-white border rounded-lg p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Aucun message</h3>
            <p className="text-gray-500 mb-4">Vous n'avez pas encore de conversation avec notre service client.</p>
            <Button onClick={() => setIsNewConversationOpen(true)}>
              <PlusCircle className="h-4 w-4 mr-2" />
              Nouveau message
            </Button>
          </div>
        ) : (
          <div className="relative">
            {/* Mobile: Show either list or conversation */}
            {isMobile ? (
              <>
                {/* Liste des conversations - Mobile */}
                {showConversationList && (
                  <div className="bg-white border rounded-lg">
                    <div className="px-4 py-3 border-b flex justify-between items-center">
                      <span className="text-sm font-medium">
                        {conversationsData?.results?.length} conversation{conversationsData?.results?.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <div className="h-[calc(100vh-300px)] overflow-y-auto">
                  {isLoadingConversations ? (
                    // Skeleton loader pour les conversations
                    Array.from({ length: 3 }).map((_, index) => (
                      <div key={index} className="p-3 border-b">
                        <div className="flex justify-between items-start mb-1">
                          <Skeleton className="h-5 w-32" />
                          <Skeleton className="h-5 w-16 rounded-full" />
                        </div>
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    ))
                  ) : (
                    conversationsData?.results?.map((conversation) => (
                      <div
                        key={conversation.id}
                        className={`p-3 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                          selectedConversationId === conversation.id ? "bg-primary/5 border-l-4 border-l-primary" : ""
                        }`}
                        onClick={() => {
                          setSelectedConversationId(conversation.id);
                          if (isMobile) {
                            setShowConversationList(false);
                          }
                        }}
                      >
                        <div className="flex justify-between items-start mb-1">
                          <h3 className={`font-medium ${selectedConversationId === conversation.id ? "text-primary" : ""}`}>
                            {conversation.subject}
                          </h3>
                          <span className={`text-xs px-2 py-0.5 rounded-full ${formatStatusBadge(mapStatusToDisplay(conversation.status))}`}>
                            {mapStatusToDisplay(conversation.status)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1 line-clamp-1">
                          {conversation.last_message_preview?.content || "Aucun message"}
                        </p>
                        <div className="text-xs text-gray-500 flex items-center gap-1">
                          <span>{formatDate(conversation.last_message_at)}</span>
                          {conversation.unread && (
                            <span className="ml-auto bg-primary w-2 h-2 rounded-full" title="Non lu"></span>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                    </div>
                  </div>
                )}

                {/* Conversation active - Mobile */}
                {!showConversationList && selectedConversationId && (
                  <div className="bg-white border rounded-lg">
                    <div className="p-4 border-b flex items-center gap-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowConversationList(true)}
                      >
                        <ArrowLeft className="h-4 w-4" />
                      </Button>
                      <h2 className="font-medium truncate">
                        {selectedConversation?.subject || 'Conversation'}
                      </h2>
                    </div>
                    {/* Le contenu de la conversation sera affiché ici */}
                  </div>
                )}
              </>
            ) : (
              /* Desktop: Show both list and conversation */
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Liste des conversations - Desktop */}
                <div className="lg:col-span-1">
                  <div className="bg-white border rounded-lg">
                    <div className="px-2 py-2 border-b flex justify-between items-center">
                      <span className="text-sm font-medium px-2">
                        {conversationsData?.results?.length} conversation{conversationsData?.results?.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <div className="h-[calc(100vh-400px)] overflow-y-auto">
                      {isLoadingConversations ? (
                        // Skeleton loader pour les conversations
                        Array.from({ length: 3 }).map((_, index) => (
                          <div key={index} className="p-3 border-b">
                            <div className="flex justify-between items-start mb-1">
                              <Skeleton className="h-5 w-32" />
                              <Skeleton className="h-5 w-16 rounded-full" />
                            </div>
                            <Skeleton className="h-4 w-full mb-1" />
                            <Skeleton className="h-3 w-24" />
                          </div>
                        ))
                      ) : (
                        conversationsData?.results?.map((conversation) => (
                          <div
                            key={conversation.id}
                            className={`p-3 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                              selectedConversationId === conversation.id ? "bg-primary/5 border-l-4 border-l-primary" : ""
                            }`}
                            onClick={() => setSelectedConversationId(conversation.id)}
                          >
                            <div className="flex justify-between items-start mb-1">
                              <h3 className={`font-medium ${selectedConversationId === conversation.id ? "text-primary" : ""}`}>
                                {conversation.subject}
                              </h3>
                              <span className={`text-xs px-2 py-0.5 rounded-full ${formatStatusBadge(mapStatusToDisplay(conversation.status))}`}>
                                {mapStatusToDisplay(conversation.status)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-1 line-clamp-1">
                              {conversation.last_message_preview?.content || "Aucun message"}
                            </p>
                            <div className="text-xs text-gray-500 flex items-center gap-1">
                              <span>{formatDate(conversation.last_message_at)}</span>
                              {conversation.unread && (
                                <span className="ml-auto bg-primary w-2 h-2 rounded-full" title="Non lu"></span>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>

                {/* Conversation active - Desktop */}
                <div className="lg:col-span-2">
              {isLoadingConversation && selectedConversationId ? (
                // Skeleton loader pour la conversation
                <div className="bg-white border rounded-lg h-full flex flex-col">
                  <div className="p-4 border-b">
                    <Skeleton className="h-6 w-64 mb-2" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <div className="flex-grow p-4 overflow-y-auto bg-gray-50 h-[calc(100vh-500px)]">
                    {Array.from({ length: 3 }).map((_, index) => (
                      <div key={index} className={`mb-4 flex ${index % 2 === 0 ? "justify-start" : "justify-end"}`}>
                        <Skeleton className={`h-24 ${index % 2 === 0 ? "w-3/4" : "w-2/3"} rounded-lg`} />
                      </div>
                    ))}
                  </div>
                  <div className="p-4 border-t">
                    <div className="flex gap-2">
                      <Skeleton className="h-10 flex-grow" />
                      <Skeleton className="h-10 w-28" />
                    </div>
                  </div>
                </div>
              ) : selectedConversation ? (
                <div className="bg-white border rounded-lg h-full flex flex-col">
                  {/* Entête de la conversation */}
                  <div className="p-4 border-b">
                    <h2 className="font-medium">{selectedConversation.subject}</h2>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <span>Statut: {mapStatusToDisplay(selectedConversation.status)}</span>
                      <span>•</span>
                      <span>Créé le {formatDate(selectedConversation.created_at)}</span>
                    </div>
                  </div>

                  {/* Messages de la conversation */}
                  <div className="flex-grow p-4 overflow-y-auto bg-gray-50 h-[calc(100vh-500px)]">
                    {selectedConversation.messages?.map((msg) => (
                      <div
                        key={msg.id}
                        className={`mb-4 ${msg.is_from_admin ? "flex justify-start" : "flex justify-end"}`}
                      >
                        <div
                          className={`max-w-[80%] p-3 rounded-lg ${
                            msg.is_from_admin
                              ? "bg-white border"
                              : "bg-primary text-white"
                          }`}
                        >
                          <p className="text-sm whitespace-pre-wrap">{msg.content}</p>

                          {/* Affichage des pièces jointes */}
                          {msg.attachments && msg.attachments.length > 0 && (
                            <div className="mt-2 space-y-1">
                              {msg.attachments.map(attachment => (
                                <a
                                  key={attachment.id}
                                  href={attachment.file_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className={`flex items-center gap-1 text-xs ${
                                    msg.is_from_admin ? "text-primary hover:text-primary/80" : "text-white/90 hover:text-white"
                                  }`}
                                >
                                  <FileIcon size={12} />
                                  <span className="underline">{attachment.file_name}</span>
                                </a>
                              ))}
                            </div>
                          )}

                          <div className={`text-xs mt-1 text-right ${
                            msg.is_from_admin ? "text-gray-500" : "text-primary-foreground/80"
                          }`}>
                            {formatDate(msg.created_at)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Formulaire de réponse */}
                  <div className="p-4 border-t">
                    {selectedConversation.status !== 'closed' ? (
                      <>
                        <div className="flex flex-col gap-2">
                          <div className="flex items-center gap-2">
                            <Input
                              type="file"
                              multiple
                              className="hidden"
                              ref={fileInputRef}
                              onChange={handleFileSelect}
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => fileInputRef.current?.click()}
                            >
                              <PaperclipIcon className="h-4 w-4 mr-2" />
                              Joindre des fichiers
                            </Button>
                            {selectedFiles.length > 0 && (
                              <span className="text-xs text-gray-500">
                                {selectedFiles.length} fichier{selectedFiles.length > 1 ? 's' : ''} sélectionné{selectedFiles.length > 1 ? 's' : ''}
                              </span>
                            )}
                          </div>

                          {selectedFiles.length > 0 && (
                            <div className="flex flex-wrap gap-2 mb-2">
                              {selectedFiles.map((file, index) => (
                                <div key={index} className="flex items-center gap-1 bg-gray-100 rounded-full px-2 py-1 text-xs">
                                  <FileIcon className="h-3 w-3" />
                                  <span className="truncate max-w-[100px]">{file.name}</span>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeFile(index)}
                                    className="h-4 w-4 p-0 ml-1"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}

                          <div className="flex gap-2">
                            <Textarea
                              placeholder="Votre réponse..."
                              className="flex-grow min-h-[80px]"
                              value={newMessage}
                              onChange={(e) => setNewMessage(e.target.value)}
                              disabled={sendMessageMutation.isPending}
                            />
                            <Button
                              onClick={handleSendMessage}
                              className="self-end"
                              disabled={newMessage.trim() === "" || sendMessageMutation.isPending}
                            >
                              {sendMessageMutation.isPending ? (
                                <Loader2 size={16} className="mr-2 animate-spin" />
                              ) : (
                                <Send size={16} className="mr-2" />
                              )}
                              Envoyer
                            </Button>
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="bg-gray-50 p-4 rounded-lg text-center">
                        <p className="text-gray-500">Cette conversation est fermée. Vous ne pouvez plus y répondre.</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => setIsNewConversationOpen(true)}
                        >
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Créer une nouvelle conversation
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="bg-white border rounded-lg h-full flex items-center justify-center p-8">
                  <div className="text-center">
                    <MessageSquare size={48} className="text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium">Aucune conversation sélectionnée</h3>
                    <p className="text-gray-500 mt-1">Sélectionnez une conversation pour afficher les messages.</p>
                  </div>
                </div>
              )}
                </div>
              </div>
            )}

            {/* Contenu de conversation pour mobile */}
            {isMobile && !showConversationList && selectedConversationId && (
              <div className="mt-4">
                {isLoadingConversation ? (
                  // Skeleton loader pour la conversation mobile
                  <div className="bg-white border rounded-lg h-full flex flex-col">
                    <div className="p-4 border-b">
                      <Skeleton className="h-6 w-64 mb-2" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <div className="flex-grow p-4 overflow-y-auto bg-gray-50 h-[calc(100vh-400px)]">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <div key={index} className={`mb-4 flex ${index % 2 === 0 ? "justify-start" : "justify-end"}`}>
                          <Skeleton className={`h-24 ${index % 2 === 0 ? "w-3/4" : "w-2/3"} rounded-lg`} />
                        </div>
                      ))}
                    </div>
                    <div className="p-4 border-t">
                      <div className="flex gap-2">
                        <Skeleton className="h-10 flex-grow" />
                        <Skeleton className="h-10 w-28" />
                      </div>
                    </div>
                  </div>
                ) : selectedConversation ? (
                  <div className="bg-white border rounded-lg h-full flex flex-col">
                    {/* Messages de la conversation - Mobile */}
                    <div className="flex-grow p-4 overflow-y-auto bg-gray-50 h-[calc(100vh-350px)]">
                      {selectedConversation.messages?.map((msg) => (
                        <div
                          key={msg.id}
                          className={`mb-4 ${msg.is_from_admin ? "flex justify-start" : "flex justify-end"}`}
                        >
                          <div
                            className={`max-w-[85%] p-3 rounded-lg ${
                              msg.is_from_admin
                                ? "bg-white border"
                                : "bg-primary text-white"
                            }`}
                          >
                            <p className="text-sm whitespace-pre-wrap">{msg.content}</p>

                            {/* Affichage des pièces jointes */}
                            {msg.attachments && msg.attachments.length > 0 && (
                              <div className="mt-2 space-y-1">
                                {msg.attachments.map(attachment => (
                                  <a
                                    key={attachment.id}
                                    href={attachment.file_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className={`flex items-center gap-1 text-xs ${
                                      msg.is_from_admin ? "text-primary hover:text-primary/80" : "text-white/90 hover:text-white"
                                    }`}
                                  >
                                    <FileIcon size={12} />
                                    <span className="underline">{attachment.file_name}</span>
                                  </a>
                                ))}
                              </div>
                            )}

                            <div className={`text-xs mt-1 text-right ${
                              msg.is_from_admin ? "text-gray-500" : "text-primary-foreground/80"
                            }`}>
                              {formatDate(msg.created_at)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Formulaire de réponse - Mobile */}
                    <div className="p-4 border-t">
                      {selectedConversation.status !== 'closed' ? (
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Input
                              type="file"
                              multiple
                              className="hidden"
                              ref={fileInputRef}
                              onChange={handleFileSelect}
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => fileInputRef.current?.click()}
                            >
                              <PaperclipIcon className="h-4 w-4 mr-2" />
                              <span className="hidden sm:inline">Joindre des fichiers</span>
                              <span className="sm:hidden">Fichiers</span>
                            </Button>
                            {selectedFiles.length > 0 && (
                              <span className="text-xs text-gray-500">
                                {selectedFiles.length} fichier{selectedFiles.length > 1 ? 's' : ''}
                              </span>
                            )}
                          </div>

                          {selectedFiles.length > 0 && (
                            <div className="flex flex-wrap gap-2">
                              {selectedFiles.map((file, index) => (
                                <div key={index} className="flex items-center gap-1 bg-gray-100 rounded-full px-2 py-1 text-xs">
                                  <FileIcon className="h-3 w-3" />
                                  <span className="truncate max-w-[80px]">{file.name}</span>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeFile(index)}
                                    className="h-4 w-4 p-0 ml-1"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}

                          <div className="flex gap-2">
                            <Textarea
                              placeholder="Votre réponse..."
                              className="flex-grow min-h-[60px]"
                              value={newMessage}
                              onChange={(e) => setNewMessage(e.target.value)}
                              disabled={sendMessageMutation.isPending}
                            />
                            <Button
                              onClick={handleSendMessage}
                              className="self-end"
                              disabled={newMessage.trim() === "" || sendMessageMutation.isPending}
                            >
                              {sendMessageMutation.isPending ? (
                                <Loader2 size={16} className="animate-spin" />
                              ) : (
                                <Send size={16} />
                              )}
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-gray-50 p-4 rounded-lg text-center">
                          <p className="text-gray-500 text-sm">Cette conversation est fermée.</p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => setIsNewConversationOpen(true)}
                          >
                            <PlusCircle className="h-4 w-4 mr-2" />
                            Nouvelle conversation
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ) : null}
              </div>
            )}
          </div>
        )}
      </div>
    </AccountLayout>
  );
};

export default Messages;
