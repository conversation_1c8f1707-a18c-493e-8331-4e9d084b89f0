<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Système d'Envoi d'Emails - JosNet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .btn-test {
            background: #007bff;
            color: white;
        }
        .btn-config {
            background: #28a745;
            color: white;
        }
        .btn-reset {
            background: #dc3545;
            color: white;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .config-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .config-info h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .config-step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Test du Système d'Envoi d'Emails - JosNet</h1>
        
        <div class="config-info">
            <h4>📋 Configuration Email Requise</h4>
            <p>Pour tester l'envoi d'emails réels, vous devez configurer le fichier <code>josnet_backend/.env</code> :</p>
            
            <div class="config-step">
                <strong>1. Configuration Gmail (Recommandée pour les tests)</strong>
                <ul>
                    <li>Activez l'authentification à 2 facteurs sur votre compte Gmail</li>
                    <li>Générez un mot de passe d'application : <a href="https://myaccount.google.com/apppasswords" target="_blank">https://myaccount.google.com/apppasswords</a></li>
                    <li>Utilisez ce mot de passe dans EMAIL_HOST_PASSWORD</li>
                </ul>
            </div>
            
            <div class="config-step">
                <strong>2. Variables d'environnement dans .env</strong>
                <pre style="background: #f1f1f1; padding: 10px; border-radius: 3px; font-size: 11px;">
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre-mot-de-passe-app
DEFAULT_FROM_EMAIL=<EMAIL></pre>
            </div>
        </div>

        <div class="test-section">
            <h3>📧 Test 1: Email de Récupération de Mot de Passe</h3>
            <div class="form-group">
                <label for="resetEmail">Email destinataire:</label>
                <input type="email" id="resetEmail" placeholder="<EMAIL>" value="">
            </div>
            <button class="btn-test" onclick="testPasswordResetEmail()">
                🔐 Tester l'envoi de récupération de mot de passe
            </button>
            <span id="resetStatus" class="status pending">En attente</span>
        </div>

        <div class="test-section">
            <h3>📨 Test 2: Email de Contact</h3>
            <div class="form-group">
                <label for="contactName">Nom:</label>
                <input type="text" id="contactName" value="Test User" placeholder="Votre nom">
            </div>
            <div class="form-group">
                <label for="contactEmail">Email:</label>
                <input type="email" id="contactEmail" placeholder="<EMAIL>" value="">
            </div>
            <div class="form-group">
                <label for="contactSubject">Sujet:</label>
                <input type="text" id="contactSubject" value="Test d'envoi d'email" placeholder="Sujet du message">
            </div>
            <div class="form-group">
                <label for="contactMessage">Message:</label>
                <textarea id="contactMessage" placeholder="Votre message">Ceci est un test d'envoi d'email depuis le système JosNet. Si vous recevez ce message, la configuration email fonctionne correctement !</textarea>
            </div>
            <button class="btn-test" onclick="testContactEmail()">
                📨 Tester l'envoi de message de contact
            </button>
            <span id="contactStatus" class="status pending">En attente</span>
        </div>

        <div class="test-section">
            <h3>✅ Test 3: Email de Vérification d'Inscription</h3>
            <div class="form-group">
                <label for="verifyEmail">Email pour vérification:</label>
                <input type="email" id="verifyEmail" placeholder="<EMAIL>" value="">
            </div>
            <button class="btn-test" onclick="testVerificationEmail()">
                ✅ Tester l'email de vérification
            </button>
            <span id="verifyStatus" class="status pending">En attente</span>
        </div>

        <div class="test-section">
            <h3>⚙️ Configuration et Diagnostic</h3>
            <button class="btn-config" onclick="checkEmailConfig()">
                🔍 Vérifier la configuration email
            </button>
            <button class="btn-test" onclick="testSMTPConnection()">
                🌐 Tester la connexion SMTP
            </button>
            <button class="btn-reset" onclick="clearLog()">
                🗑️ Effacer le journal
            </button>
        </div>

        <div class="log" id="logContainer">
            <strong>📋 Journal des tests email:</strong><br>
            Prêt à tester le système d'envoi d'emails...<br>
            <span class="warning">⚠️ Assurez-vous d'avoir configuré le fichier .env avant de commencer les tests.</span><br>
        </div>

        <div style="margin-top: 20px;">
            <h3>📝 Instructions de test:</h3>
            <ol>
                <li><strong>Configuration:</strong> Configurez d'abord le fichier <code>josnet_backend/.env</code> avec vos informations email</li>
                <li><strong>Redémarrage:</strong> Redémarrez le serveur Django après modification du .env</li>
                <li><strong>Test de configuration:</strong> Cliquez sur "Vérifier la configuration email"</li>
                <li><strong>Tests d'envoi:</strong> Testez chaque type d'email avec votre vraie adresse email</li>
                <li><strong>Vérification:</strong> Vérifiez votre boîte de réception (et spam) pour les emails reçus</li>
            </ol>
            
            <div class="config-info">
                <h4>🔧 Dépannage</h4>
                <ul>
                    <li><strong>Erreur d'authentification:</strong> Vérifiez que vous utilisez un mot de passe d'application Gmail</li>
                    <li><strong>Emails non reçus:</strong> Vérifiez le dossier spam/courrier indésirable</li>
                    <li><strong>Erreur de connexion:</strong> Vérifiez votre connexion internet et les paramètres SMTP</li>
                    <li><strong>Mode console:</strong> Si EMAIL_BACKEND=console, les emails s'affichent dans la console Django</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
            logContainer.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span><br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        async function makeApiCall(url, method = 'GET', body = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(url, options);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    let errorMessage;
                    try {
                        const errorData = JSON.parse(errorText);
                        errorMessage = errorData.message || errorData.detail || response.statusText;
                    } catch {
                        errorMessage = errorText || response.statusText;
                    }
                    throw new Error(`HTTP ${response.status}: ${errorMessage}`);
                }
                
                return await response.json();
            } catch (error) {
                log(`❌ Erreur API: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testPasswordResetEmail() {
            const email = document.getElementById('resetEmail').value;
            if (!email) {
                log('⚠️ Veuillez entrer un email destinataire', 'warning');
                return;
            }

            log(`🔐 Test d'envoi d'email de récupération vers: ${email}`);
            document.getElementById('resetStatus').textContent = 'En cours...';
            document.getElementById('resetStatus').className = 'status pending';

            try {
                const result = await makeApiCall(`${API_BASE}/auth/password/reset/request/`, 'POST', {
                    email: email
                });

                log(`✅ Email de récupération envoyé avec succès!`, 'success');
                log(`📧 Message: ${result.message}`, 'info');
                
                if (result.reset_link) {
                    log(`🔗 Lien de test: ${result.reset_link}`, 'info');
                }

                document.getElementById('resetStatus').textContent = 'Envoyé';
                document.getElementById('resetStatus').className = 'status success';
                
            } catch (error) {
                log(`❌ Échec de l'envoi: ${error.message}`, 'error');
                document.getElementById('resetStatus').textContent = 'Échec';
                document.getElementById('resetStatus').className = 'status error';
            }
        }

        async function testContactEmail() {
            const name = document.getElementById('contactName').value;
            const email = document.getElementById('contactEmail').value;
            const subject = document.getElementById('contactSubject').value;
            const message = document.getElementById('contactMessage').value;

            if (!name || !email || !subject || !message) {
                log('⚠️ Veuillez remplir tous les champs du formulaire de contact', 'warning');
                return;
            }

            log(`📨 Test d'envoi d'email de contact vers: ${email}`);
            document.getElementById('contactStatus').textContent = 'En cours...';
            document.getElementById('contactStatus').className = 'status pending';

            try {
                const result = await makeApiCall(`${API_BASE}/core/contact/`, 'POST', {
                    name: name,
                    email: email,
                    subject: subject,
                    message: message
                });

                log(`✅ Message de contact envoyé avec succès!`, 'success');
                log(`📧 Réponse: ${result.message}`, 'info');

                document.getElementById('contactStatus').textContent = 'Envoyé';
                document.getElementById('contactStatus').className = 'status success';
                
            } catch (error) {
                log(`❌ Échec de l'envoi: ${error.message}`, 'error');
                document.getElementById('contactStatus').textContent = 'Échec';
                document.getElementById('contactStatus').className = 'status error';
            }
        }

        async function testVerificationEmail() {
            const email = document.getElementById('verifyEmail').value;
            if (!email) {
                log('⚠️ Veuillez entrer un email pour la vérification', 'warning');
                return;
            }

            log(`✅ Test d'envoi d'email de vérification vers: ${email}`);
            document.getElementById('verifyStatus').textContent = 'En cours...';
            document.getElementById('verifyStatus').className = 'status pending';

            try {
                // D'abord créer un compte temporaire pour déclencher l'email de vérification
                const result = await makeApiCall(`${API_BASE}/auth/register/`, 'POST', {
                    first_name: 'Test',
                    last_name: 'User',
                    email: email,
                    password: 'TestPassword123!',
                    confirm_password: 'TestPassword123!'
                });

                log(`✅ Email de vérification envoyé avec succès!`, 'success');
                log(`📧 Compte créé pour: ${result.user.email}`, 'info');

                document.getElementById('verifyStatus').textContent = 'Envoyé';
                document.getElementById('verifyStatus').className = 'status success';
                
            } catch (error) {
                if (error.message.includes('already exists')) {
                    log(`⚠️ Compte déjà existant. Tentative d'envoi de vérification...`, 'warning');
                    // Ici on pourrait implémenter un endpoint pour renvoyer la vérification
                    document.getElementById('verifyStatus').textContent = 'Compte existant';
                    document.getElementById('verifyStatus').className = 'status warning';
                } else {
                    log(`❌ Échec de l'envoi: ${error.message}`, 'error');
                    document.getElementById('verifyStatus').textContent = 'Échec';
                    document.getElementById('verifyStatus').className = 'status error';
                }
            }
        }

        async function checkEmailConfig() {
            log('🔍 Vérification de la configuration email...');
            
            try {
                // Test simple de l'API pour vérifier si le serveur répond
                const response = await fetch(`${API_BASE}/auth/password/reset/request/`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: '<EMAIL>' })
                });

                if (response.ok) {
                    log('✅ Serveur Django accessible et endpoint email fonctionnel', 'success');
                    log('📧 Configuration email semble correcte côté serveur', 'info');
                } else {
                    log('⚠️ Serveur accessible mais problème avec l\'endpoint email', 'warning');
                }
            } catch (error) {
                log('❌ Impossible de contacter le serveur Django', 'error');
                log('🔧 Vérifiez que le serveur Django est démarré sur http://localhost:8000', 'info');
            }
        }

        async function testSMTPConnection() {
            log('🌐 Test de connexion SMTP...');
            log('ℹ️ Ce test vérifie la connectivité générale. Pour un test complet, utilisez les autres boutons.', 'info');
            
            // Simuler un test de connexion
            setTimeout(() => {
                log('📡 Test de connectivité réseau...', 'info');
                setTimeout(() => {
                    log('✅ Connectivité réseau OK', 'success');
                    log('📧 Pour tester SMTP réellement, utilisez les boutons d\'envoi d\'email', 'info');
                }, 1000);
            }, 500);
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<strong>📋 Journal des tests email:</strong><br>Journal effacé. Prêt pour de nouveaux tests...<br>';
        }

        // Initialisation
        window.onload = function() {
            log('🚀 Page de test email chargée');
            log('💡 Configurez d\'abord le fichier .env puis testez les envois d\'emails');
            
            // Pré-remplir avec un email par défaut si disponible
            const defaultEmail = '<EMAIL>';
            document.getElementById('resetEmail').value = defaultEmail;
            document.getElementById('contactEmail').value = defaultEmail;
            document.getElementById('verifyEmail').value = defaultEmail;
        };
    </script>
</body>
</html>
