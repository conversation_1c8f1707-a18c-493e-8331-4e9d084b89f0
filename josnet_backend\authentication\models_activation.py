
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import uuid

User = get_user_model()

class AccountActivation(models.Model):
    """
    Modèle pour gérer l'activation de compte par email
    """
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE,
        related_name='activation'
    )
    activation_token = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        editable=False
    )
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_activated = models.BooleanField(default=False)
    activated_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'account_activation'
        verbose_name = 'Account Activation'
        verbose_name_plural = 'Account Activations'
    
    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Token expire dans 24 heures
            self.expires_at = timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        """Vérifier si le token d'activation a expiré"""
        return timezone.now() > self.expires_at
    
    def activate_account(self):
        """Activer le compte utilisateur"""
        if not self.is_expired and not self.is_activated:
            # Activer le compte
            self.is_activated = True
            self.activated_at = timezone.now()
            self.save()
            
            # Activer l'utilisateur
            self.user.is_active = True
            self.user.is_verified = True
            self.user.save()
            
            return True
        return False
    
    def __str__(self):
        status = "Activated" if self.is_activated else "Pending"
        return f"{self.user.email} - {status}"
