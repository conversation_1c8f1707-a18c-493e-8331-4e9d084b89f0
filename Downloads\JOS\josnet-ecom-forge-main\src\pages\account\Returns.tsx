
import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import AccountLayout from "@/components/account/AccountLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Package2,
  FileCheck,
  Clock,
  AlertCircle,
  Loader2,
  RefreshCw,
  Filter,
  MessageSquare,
  ExternalLink
} from "lucide-react";
import { accountApi } from "@/services/accountApi";
import { useToast } from "@/hooks/use-toast";
import { ReturnFilters, ReturnFiltersState } from "@/components/account/returns/ReturnFilters";
import { CreateReturnForm } from "@/components/account/returns/CreateReturnForm";
import { CreateClaimForm } from "@/components/account/claims/CreateClaimForm";
import { PaginationControls } from "@/components/common/PaginationControls";

const Returns = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("returns");
  const [isCreateReturnOpen, setIsCreateReturnOpen] = useState(false);
  const [isCreateClaimOpen, setIsCreateClaimOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [claimsCurrentPage, setClaimsCurrentPage] = useState(1);
  const [filters, setFilters] = useState<ReturnFiltersState>({
    orderId: '',
    status: '',
    dateFrom: undefined,
    dateTo: undefined,
  });

  // Fetch returns data
  const {
    data: returnsData = [],
    isLoading: isLoadingReturns,
    isError: isErrorReturns,
    refetch: refetchReturns
  } = useQuery({
    queryKey: ['returns', filters],
    queryFn: () => accountApi.getReturns(),
    retry: 1,
    retryDelay: 1000,
  });

  // Fetch claims data
  const {
    data: claimsData = [],
    isLoading: isLoadingClaims,
    isError: isErrorClaims,
    refetch: refetchClaims
  } = useQuery({
    queryKey: ['claims'],
    queryFn: () => accountApi.getClaims(),
    retry: 1,
    retryDelay: 1000,
  });

  // Pagination settings
  const itemsPerPage = 5;

  // Filter and paginate returns data
  const filteredReturns = useMemo(() => {
    return returnsData.filter(item => {
      // Filter by order ID
      if (filters.orderId && !item.orderId.toLowerCase().includes(filters.orderId.toLowerCase())) {
        return false;
      }

      // Filter by status
      if (filters.status && item.status !== filters.status) {
        return false;
      }

      // Filter by date range (simplified for demo)
      // In a real app, you would parse the dates and compare them properly

      return true;
    });
  }, [returnsData, filters]);

  // Calculate pagination
  const totalPages = Math.max(1, Math.ceil(filteredReturns.length / itemsPerPage));
  const paginatedReturns = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredReturns.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredReturns, currentPage, itemsPerPage]);

  // Reset pagination when filters change
  const handleFilterChange = (newFilters: ReturnFiltersState) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  // Reset filters
  const handleResetFilters = () => {
    setFilters({
      orderId: '',
      status: '',
      dateFrom: undefined,
      dateTo: undefined,
    });
    setCurrentPage(1);
  };

  const handleRefresh = () => {
    if (activeTab === "returns") {
      refetchReturns();
      toast({
        title: "Rafraîchissement",
        description: "Liste des retours mise à jour",
      });
    } else {
      refetchClaims();
      toast({
        title: "Rafraîchissement",
        description: "Liste des réclamations mise à jour",
      });
    }
  };

  const handleCreateReturnSuccess = () => {
    refetchReturns();
    toast({
      title: "Retour créé",
      description: "Votre demande de retour a été créée avec succès.",
    });
  };

  const handleCreateClaimSuccess = () => {
    refetchClaims();
    toast({
      title: "Réclamation créée",
      description: "Votre réclamation a été soumise avec succès.",
    });
  };

  return (
    <AccountLayout title="Retours et réclamations">
      <Tabs defaultValue="returns" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="returns">Retours</TabsTrigger>
          <TabsTrigger value="claims">Réclamations</TabsTrigger>
        </TabsList>

        <TabsContent value="returns" className="mt-6 space-y-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-medium">Vos demandes de retour</h2>
            <div className="flex flex-wrap gap-2">
              <ReturnFilters
                filters={filters}
                onFilterChange={handleFilterChange}
                onReset={handleResetFilters}
              />
              <Button variant="outline" onClick={handleRefresh} className="flex items-center gap-1">
                <RefreshCw size={16} />
                Actualiser
              </Button>
              <Button onClick={() => setIsCreateReturnOpen(true)}>Créer un retour</Button>
            </div>
          </div>

          {isLoadingReturns ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <Loader2 className="h-12 w-12 text-gray-400 mb-4 animate-spin" />
                <h3 className="text-lg font-medium mb-2">Chargement des retours...</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Veuillez patienter pendant que nous récupérons vos demandes de retour.
                </p>
              </CardContent>
            </Card>
          ) : isErrorReturns ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Une erreur est survenue lors du chargement de vos retours. Veuillez réessayer.
                </p>
                <Button variant="outline" onClick={() => refetchReturns()}>
                  Réessayer
                </Button>
              </CardContent>
            </Card>
          ) : filteredReturns.length > 0 ? (
            <div className="space-y-4">
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>N° Retour</TableHead>
                      <TableHead>N° Commande</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Articles</TableHead>
                      <TableHead>Montant</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedReturns.map((returnItem) => (
                      <TableRow key={returnItem.id}>
                        <TableCell className="font-medium">{returnItem.id}</TableCell>
                        <TableCell>{returnItem.orderId}</TableCell>
                        <TableCell>{returnItem.date}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 ${
                            returnItem.status === "Approuvé"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                            } rounded text-xs font-medium`}>
                            {returnItem.status}
                          </span>
                        </TableCell>
                        <TableCell>{returnItem.items}</TableCell>
                        <TableCell>{returnItem.refundAmount}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="outline" size="sm">Détails</Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {totalPages > 1 && (
                <div className="flex justify-center mt-6">
                  <PaginationControls
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              )}

              <div className="text-sm text-gray-500 text-center">
                Affichage de {paginatedReturns.length} retour(s) sur {filteredReturns.length}
              </div>
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <Package2 className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Aucun retour</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Vous n'avez pas encore effectué de demande de retour sur vos commandes.
                </p>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Comment effectuer un retour</CardTitle>
              <CardDescription>
                Procédure simple pour retourner un article
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <span className="text-primary font-semibold">1</span>
                </div>
                <div>
                  <h4 className="font-medium">Créer une demande de retour</h4>
                  <p className="text-gray-500 text-sm">
                    Cliquez sur le bouton "Créer un retour" et sélectionnez les produits que vous souhaitez retourner
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <span className="text-primary font-semibold">2</span>
                </div>
                <div>
                  <h4 className="font-medium">Imprimer l'étiquette de retour</h4>
                  <p className="text-gray-500 text-sm">
                    Une fois votre demande approuvée, téléchargez et imprimez l'étiquette de retour
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <span className="text-primary font-semibold">3</span>
                </div>
                <div>
                  <h4 className="font-medium">Expédier le colis</h4>
                  <p className="text-gray-500 text-sm">
                    Déposez le colis avec l'étiquette au point de collecte indiqué
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <p className="text-sm text-gray-500">
                Pour plus d'informations, consultez notre <a href="#" className="text-primary hover:underline">politique de retour</a>.
              </p>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="claims" className="mt-6 space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">Vos réclamations</h2>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleRefresh} className="flex items-center gap-1">
                <RefreshCw size={16} />
                Actualiser
              </Button>
              <Button onClick={() => setIsCreateClaimOpen(true)}>
                <MessageSquare className="mr-2 h-4 w-4" />
                Nouvelle réclamation
              </Button>
            </div>
          </div>

          {isLoadingClaims ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <Loader2 className="h-12 w-12 text-gray-400 mb-4 animate-spin" />
                <h3 className="text-lg font-medium mb-2">Chargement des réclamations...</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Veuillez patienter pendant que nous récupérons vos réclamations.
                </p>
              </CardContent>
            </Card>
          ) : isErrorClaims ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Une erreur est survenue lors du chargement de vos réclamations. Veuillez réessayer.
                </p>
                <Button variant="outline" onClick={() => refetchClaims()}>
                  Réessayer
                </Button>
              </CardContent>
            </Card>
          ) : claimsData.length > 0 ? (
            <div className="space-y-4">
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>N° Réclamation</TableHead>
                      <TableHead>N° Commande</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Sujet</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {claimsData.map((claim) => (
                      <TableRow key={claim.id}>
                        <TableCell className="font-medium">{claim.id}</TableCell>
                        <TableCell>{claim.orderId}</TableCell>
                        <TableCell>{claim.date}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 ${
                            claim.status === "Résolu"
                              ? "bg-green-100 text-green-800"
                              : claim.status === "En cours"
                                ? "bg-yellow-100 text-yellow-800"
                                : claim.status === "Nouveau"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-gray-100 text-gray-800"
                            } rounded text-xs font-medium`}>
                            {claim.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-[200px] truncate" title={claim.subject}>
                            {claim.subject}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/account/claims/${claim.id}`} className="flex items-center gap-1">
                              Détails
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </Link>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {claimsData.length > 5 && (
                <div className="flex justify-center mt-6">
                  <PaginationControls
                    currentPage={claimsCurrentPage}
                    totalPages={Math.ceil(claimsData.length / 5)}
                    onPageChange={setClaimsCurrentPage}
                  />
                </div>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Aucune réclamation</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Vous n'avez pas encore effectué de réclamation concernant vos commandes.
                </p>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Service client</CardTitle>
              <CardDescription>
                Nous sommes là pour vous aider
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                Si vous avez besoin d'aide concernant votre commande ou pour toute autre question,
                n'hésitez pas à contacter notre service client.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button variant="outline" className="flex-1">Contacter par email</Button>
                <Button className="flex-1">Contacter par téléphone</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Formulaire de création de retour */}
      <CreateReturnForm
        isOpen={isCreateReturnOpen}
        onOpenChange={setIsCreateReturnOpen}
        onSuccess={handleCreateReturnSuccess}
      />

      {/* Formulaire de création de réclamation */}
      <CreateClaimForm
        isOpen={isCreateClaimOpen}
        onOpenChange={setIsCreateClaimOpen}
        onSuccess={handleCreateClaimSuccess}
      />
    </AccountLayout>
  );
};

export default Returns;
