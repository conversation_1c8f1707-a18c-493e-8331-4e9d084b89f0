#!/usr/bin/env python3
"""
Script pour tester les améliorations des retours et réclamations
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from orders.models import Order
from orders.models_extension import ReturnRequest, Claim

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access')
    return None

def test_returns_api():
    """Tester l'API des retours avec les améliorations"""
    print("📦 TEST DE L'API DES RETOURS")
    print("=" * 50)
    
    token = get_auth_token()
    if not token:
        print("   ❌ Impossible de s'authentifier")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test de la liste des retours
        response = requests.get(f"{API_BASE_URL}/orders/returns/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            returns = data.get('results', [])
            print(f"   ✅ API liste accessible")
            print(f"   📊 Retours trouvés: {len(returns)}")
            
            if returns:
                first_return = returns[0]
                print(f"\n   📋 Premier retour:")
                print(f"      🆔 ID: {first_return.get('id')}")
                print(f"      📝 Numéro: {first_return.get('return_number')}")
                print(f"      👤 Utilisateur: {first_return.get('user', {})}")
                print(f"      📦 Commande: {first_return.get('order_number')}")
                print(f"      📊 Articles: {first_return.get('items_count', 0)}")
                print(f"      💰 Remboursement: {first_return.get('refund_amount', 0)}")
                
                # Test des détails d'un retour
                return_id = first_return.get('id')
                detail_response = requests.get(f"{API_BASE_URL}/orders/returns/{return_id}/", headers=headers)
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print(f"\n   📋 Détails du retour:")
                    print(f"      👤 Utilisateur complet: {detail_data.get('user', {})}")
                    print(f"      📦 Détails commande: {detail_data.get('order_details', {})}")
                    
                    items = detail_data.get('items', [])
                    print(f"      📊 Articles ({len(items)}):")
                    for i, item in enumerate(items[:2], 1):  # Afficher les 2 premiers
                        print(f"         {i}. {item.get('product_name')}")
                        print(f"            Quantité: {item.get('quantity')}")
                        print(f"            Prix: {item.get('price')}")
                        print(f"            Raison: {item.get('reason_display')}")
                        print(f"            Détails produit: {item.get('product_details', {})}")
                        print(f"            Détails commande: {item.get('order_item_details', {})}")
                    
                    return True
                else:
                    print(f"   ❌ Erreur détails: {detail_response.status_code}")
                    return False
            else:
                print(f"   ⚠️ Aucun retour trouvé")
                return True
        else:
            print(f"   ❌ Erreur API: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_claims_api():
    """Tester l'API des réclamations avec les améliorations"""
    print(f"\n🎫 TEST DE L'API DES RÉCLAMATIONS")
    print("=" * 50)
    
    token = get_auth_token()
    if not token:
        print("   ❌ Impossible de s'authentifier")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test de la liste des réclamations
        response = requests.get(f"{API_BASE_URL}/orders/claims/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            claims = data.get('results', [])
            print(f"   ✅ API liste accessible")
            print(f"   📊 Réclamations trouvées: {len(claims)}")
            
            if claims:
                first_claim = claims[0]
                print(f"\n   📋 Première réclamation:")
                print(f"      🆔 ID: {first_claim.get('id')}")
                print(f"      📝 Numéro: {first_claim.get('claim_number')}")
                print(f"      👤 Utilisateur: {first_claim.get('user', {})}")
                print(f"      📦 Commande: {first_claim.get('order_number')}")
                print(f"      📝 Sujet: {first_claim.get('subject_display')}")
                print(f"      📊 Statut: {first_claim.get('status_display')}")
                
                # Test des détails d'une réclamation
                claim_id = first_claim.get('id')
                detail_response = requests.get(f"{API_BASE_URL}/orders/claims/{claim_id}/", headers=headers)
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print(f"\n   📋 Détails de la réclamation:")
                    print(f"      👤 Utilisateur complet: {detail_data.get('user', {})}")
                    print(f"      📦 Détails commande: {detail_data.get('order_details', {})}")
                    
                    messages = detail_data.get('messages', [])
                    print(f"      💬 Messages ({len(messages)}):")
                    for i, message in enumerate(messages[:2], 1):  # Afficher les 2 premiers
                        print(f"         {i}. De: {message.get('sender_display')}")
                        print(f"            Message: {message.get('message', '')[:50]}...")
                        print(f"            Pièces jointes: {len(message.get('attachments', []))}")
                    
                    history = detail_data.get('status_history', [])
                    print(f"      📈 Historique ({len(history)}):")
                    for i, entry in enumerate(history[:2], 1):  # Afficher les 2 premiers
                        print(f"         {i}. {entry.get('old_status_display')} → {entry.get('new_status_display')}")
                        print(f"            Par: {entry.get('created_by_name')}")
                    
                    return True
                else:
                    print(f"   ❌ Erreur détails: {detail_response.status_code}")
                    return False
            else:
                print(f"   ⚠️ Aucune réclamation trouvée")
                return True
        else:
            print(f"   ❌ Erreur API: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def check_database_data():
    """Vérifier les données en base de données"""
    print(f"\n🗄️ VÉRIFICATION DES DONNÉES EN BASE")
    print("=" * 50)
    
    try:
        # Compter les retours
        returns_count = ReturnRequest.objects.count()
        print(f"   📦 Retours en base: {returns_count}")
        
        if returns_count > 0:
            latest_return = ReturnRequest.objects.select_related('user', 'order').first()
            print(f"      Dernier retour:")
            print(f"         👤 Utilisateur: {latest_return.user.email}")
            print(f"         📦 Commande: {latest_return.order.order_number if latest_return.order else 'N/A'}")
            print(f"         📊 Articles: {latest_return.items.count()}")
        
        # Compter les réclamations
        claims_count = Claim.objects.count()
        print(f"   🎫 Réclamations en base: {claims_count}")
        
        if claims_count > 0:
            latest_claim = Claim.objects.select_related('user', 'order').first()
            print(f"      Dernière réclamation:")
            print(f"         👤 Utilisateur: {latest_claim.user.email}")
            print(f"         📦 Commande: {latest_claim.order.order_number if latest_claim.order else 'N/A'}")
            print(f"         💬 Messages: {latest_claim.messages.count()}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🧪 TEST DES AMÉLIORATIONS RETOURS ET RÉCLAMATIONS")
    print("=" * 70)
    
    # 1. Vérifier les données en base
    db_ok = check_database_data()
    
    # 2. Tester l'API des retours
    returns_ok = test_returns_api()
    
    # 3. Tester l'API des réclamations
    claims_ok = test_claims_api()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Données en base: {'✅' if db_ok else '❌'}")
    print(f"   API Retours: {'✅' if returns_ok else '❌'}")
    print(f"   API Réclamations: {'✅' if claims_ok else '❌'}")
    
    total_tests = 3
    passed_tests = sum([db_ok, returns_ok, claims_ok])
    
    print(f"\n🎯 SCORE: {passed_tests}/{total_tests} tests réussis")
    
    if passed_tests == total_tests:
        print(f"\n🎉 TOUTES LES AMÉLIORATIONS FONCTIONNENT!")
        print(f"   ✅ Les informations utilisateur sont récupérées")
        print(f"   ✅ Les détails des articles sont complets")
        print(f"   ✅ Les APIs sont optimisées (select_related/prefetch_related)")
        
        print(f"\n🌐 PAGES À TESTER:")
        print(f"   • Admin Retours: http://localhost:8080/admin/returns")
        print(f"   • Admin Réclamations: http://localhost:8080/admin/claims")
        
        print(f"\n✨ AMÉLIORATIONS APPORTÉES:")
        print(f"   • Sérialisation complète des utilisateurs")
        print(f"   • Détails enrichis des articles retournés")
        print(f"   • Optimisation des requêtes base de données")
        print(f"   • Préchargement des relations (user, order, items, messages)")
        
    elif passed_tests >= 2:
        print(f"\n✅ AMÉLIORATIONS LARGEMENT RÉUSSIES!")
        print(f"   La plupart des fonctionnalités ont été améliorées.")
    else:
        print(f"\n⚠️ CERTAINS PROBLÈMES PERSISTENT")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
