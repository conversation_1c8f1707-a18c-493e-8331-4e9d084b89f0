{% extends "notifications/base_email.html" %}

{% block title %}Commande #{{ order.id }} - JosNet{% endblock %}

{% block content %}
    <h2 style="color: #e74c3c; margin-bottom: 20px;">📦 Mise à jour de votre commande</h2>
    
    <p style="font-size: 16px; margin-bottom: 20px;">{{ status_message }}</p>
    
    <div class="order-details">
        <h3 style="margin-top: 0; color: #333;">Détails de la commande</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 8px 0; font-weight: bold;">Numéro de commande:</td>
                <td style="padding: 8px 0;">#{{ order.id }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; font-weight: bold;">Montant total:</td>
                <td style="padding: 8px 0;">{{ order.total }} {{ order.currency }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; font-weight: bold;">Statut:</td>
                <td style="padding: 8px 0;">{{ order.get_status_display }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; font-weight: bold;">Date de commande:</td>
                <td style="padding: 8px 0;">{{ order.created_at|date:"d/m/Y à H:i" }}</td>
            </tr>
        </table>
    </div>
    
    {% if order_url %}
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ order_url }}" class="button">Suivre ma commande</a>
        </div>
    {% endif %}
    
    <p>Si vous avez des questions concernant votre commande, n'hésitez pas à nous contacter.</p>
{% endblock %}
