
import React from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Helmet } from "react-helmet";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Truck, RotateCcw } from "lucide-react";

const Returns = () => {
  return (
    <>
      <Helmet>
        <title>Politique de Retour | JOSNET NETWORK</title>
        <meta name="description" content="Consultez notre politique de retour et de remboursement. Garantie de satisfaction de 30 jours sur tous nos produits." />
      </Helmet>
      
      <div className="min-h-screen flex flex-col">
        <Navbar />
        
        <main className="flex-1">
          {/* Hero Section */}
          <section className="bg-primary py-16 text-white">
            <div className="container mx-auto px-4">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Politique de Retour</h1>
              <p className="text-xl max-w-3xl">
                Nous voulons que vous soyez entièrement satisfait de votre achat. Découvrez notre politique de retour simple et transparente.
              </p>
            </div>
          </section>
          
          {/* Return Process Steps */}
          <section className="py-16 bg-white">
            <div className="container mx-auto px-4">
              <h2 className="text-3xl font-bold mb-12 text-center text-primary">Notre Processus de Retour en 3 Étapes</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Step 1 */}
                <div className="text-center">
                  <div className="w-20 h-20 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                    <RotateCcw className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">1. Demande de Retour</h3>
                  <p className="text-gray-600">
                    Connectez-vous à votre compte et remplissez le formulaire de retour dans les 30 jours suivant la réception de votre commande.
                  </p>
                </div>
                
                {/* Step 2 */}
                <div className="text-center">
                  <div className="w-20 h-20 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                    <Truck className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">2. Envoi du Produit</h3>
                  <p className="text-gray-600">
                    Emballez le produit dans son emballage d'origine avec tous les accessoires et envoyez-le à notre centre de retour.
                  </p>
                </div>
                
                {/* Step 3 */}
                <div className="text-center">
                  <div className="w-20 h-20 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">3. Remboursement</h3>
                  <p className="text-gray-600">
                    Votre remboursement sera traité dans les 7 jours ouvrables suivant la réception et la vérification du produit retourné.
                  </p>
                </div>
              </div>
              
              <div className="mt-10 text-center">
                <Button size="lg" className="mt-6">
                  Demander un retour
                </Button>
              </div>
            </div>
          </section>
          
          {/* Return Policy Details */}
          <section className="py-16 bg-gray-50">
            <div className="container mx-auto px-4">
              <h2 className="text-3xl font-bold mb-8 text-center text-primary">Informations Détaillées</h2>
              
              <div className="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-md">
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <AccordionTrigger>Période de retour</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-gray-700 mb-4">
                        Vous disposez de 30 jours à compter de la date de réception de votre commande pour nous retourner un article.
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-gray-700">
                        <li>Les produits doivent être dans leur état d'origine, non ouverts et non utilisés</li>
                        <li>Tous les accessoires, manuels et étiquettes doivent être inclus</li>
                        <li>L'emballage d'origine doit être intact</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="item-2">
                    <AccordionTrigger>Conditions de remboursement</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-gray-700 mb-4">
                        Les remboursements sont traités selon le mode de paiement original:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-gray-700">
                        <li><strong>Carte de crédit/débit:</strong> 3-5 jours ouvrables</li>
                        <li><strong>Mobile Money:</strong> 1-2 jours ouvrables</li>
                        <li><strong>Virement bancaire:</strong> 5-7 jours ouvrables</li>
                      </ul>
                      <p className="mt-4 text-gray-700">
                        Les frais de livraison initiaux ne sont pas remboursables, sauf en cas d'erreur de notre part ou de produit défectueux.
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="item-3">
                    <AccordionTrigger>Produits non retournables</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-gray-700 mb-4">
                        Certains articles ne peuvent pas être retournés pour des raisons d'hygiène ou de protection des consommateurs:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-gray-700">
                        <li>Produits avec sceaux de sécurité brisés</li>
                        <li>Articles personnalisés ou sur mesure</li>
                        <li>Téléchargements de logiciels et licences numériques</li>
                        <li>Cartes cadeaux</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="item-4">
                    <AccordionTrigger>Produits défectueux</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-gray-700 mb-4">
                        Si vous recevez un produit défectueux:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2 text-gray-700">
                        <li>Contactez-nous dans les 48 heures suivant la réception</li>
                        <li>Fournissez des photos montrant le défaut</li>
                        <li>Nous vous offrirons un remplacement ou un remboursement complet, y compris les frais de livraison</li>
                      </ol>
                      <p className="mt-4 text-gray-700">
                        Les produits défectueux sont soumis à notre garantie standard de 12 mois.
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="item-5">
                    <AccordionTrigger>Frais de retour</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-gray-700">
                        Les frais de retour sont à la charge du client, sauf dans les cas suivants:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-gray-700 mt-3">
                        <li>Produit défectueux</li>
                        <li>Erreur d'expédition (produit incorrect envoyé)</li>
                        <li>Produit endommagé pendant le transport</li>
                      </ul>
                      <p className="mt-4 text-gray-700">
                        Dans ces cas, nous organiserons et couvrirons les frais de retour.
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            </div>
          </section>
          
          {/* Contact For Returns */}
          <section className="py-16">
            <div className="container mx-auto px-4 text-center">
              <h2 className="text-3xl font-bold mb-4">Des questions sur votre retour?</h2>
              <p className="text-gray-700 mb-8 max-w-2xl mx-auto">
                Notre équipe du service client est disponible pour vous aider avec vos questions 
                concernant les retours et remboursements.
              </p>
              <div className="flex flex-col md:flex-row justify-center items-center gap-4">
                <a href="/contact" className="bg-primary text-white px-6 py-3 rounded-md hover:bg-primary-dark transition-colors">
                  Nous contacter
                </a>
                <a href="/account/returns" className="bg-white border border-primary text-primary px-6 py-3 rounded-md hover:bg-gray-50 transition-colors">
                  Accéder à mon compte
                </a>
              </div>
            </div>
          </section>
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default Returns;
