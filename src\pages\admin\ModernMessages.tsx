import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import AdminLayout from '@/components/admin/AdminLayout';
import ConversationList from '@/components/messaging/ConversationList';
import ChatInterface from '@/components/messaging/ChatInterface';
import MessageNotifications from '@/components/messaging/MessageNotifications';
import NewConversationForm from '@/components/messaging/NewConversationForm';
import ConversationFormHelp from '@/components/messaging/ConversationFormHelp';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MessageSquare,
  Plus,
  Settings,
  BarChart3,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Filter,
  Search,
  MoreVertical,
  Archive,
  Star,
  Tag,
  Menu,
  X,
  ArrowLeft
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import messagingApi from '@/services/messagingApi';
import { motion } from 'framer-motion';

const ModernMessages = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedConversationId, setSelectedConversationId] = useState<number | null>(null);
  const [isNewConversationOpen, setIsNewConversationOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Fetch conversations
  const { data: conversationsData, isLoading: isLoadingConversations, refetch: refetchConversations } = useQuery({
    queryKey: ['conversations', activeTab],
    queryFn: () => messagingApi.getConversations({
      status: activeTab === 'all' ? undefined : activeTab
    }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch selected conversation details
  const { data: selectedConversation, isLoading: isLoadingConversation } = useQuery({
    queryKey: ['conversation', selectedConversationId],
    queryFn: () => messagingApi.getConversation(selectedConversationId!),
    enabled: !!selectedConversationId,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });

  // Fetch statistics
  const { data: statistics } = useQuery({
    queryKey: ['conversation-statistics'],
    queryFn: () => messagingApi.getConversationStatistics(),
    staleTime: 1000 * 60 * 15, // 15 minutes
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: ({ content, files }: { content: string; files?: File[] }) =>
      messagingApi.createMessage(selectedConversationId!, { content, uploaded_files: files }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversation', selectedConversationId] });
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      toast({
        title: "Message envoyé",
        description: "Votre message a été envoyé avec succès",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible d'envoyer le message",
        variant: "destructive",
      });
    }
  });

  // Update status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ status, silent }: { status: string; silent?: boolean }) =>
      messagingApi.updateConversationStatus(selectedConversationId!, status),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['conversation', selectedConversationId] });
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      if (!variables.silent) {
        toast({
          title: "Statut mis à jour",
          description: "Le statut de la conversation a été mis à jour.",
        });
      }
    },
    onError: () => {
        toast({
            title: "Erreur",
            description: "Impossible de mettre à jour le statut.",
            variant: "destructive",
        });
    },
  });

  // Automatically update status to 'in progress' when a 'new' conversation is opened
  useEffect(() => {
    if (selectedConversation && selectedConversation.status === 'new') {
      updateStatusMutation.mutate({ status: 'in_progress', silent: true });
    }
  }, [selectedConversation, updateStatusMutation]);

  // Update priority mutation
  const updatePriorityMutation = useMutation({
    mutationFn: (priority: string) =>
      messagingApi.updateConversationPriority(selectedConversationId!, priority),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversation', selectedConversationId] });
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      toast({
        title: "Priorité mise à jour",
        description: "La priorité de la conversation a été mise à jour",
      });
    },
  });

  // Auto-select first conversation
  useEffect(() => {
    if (conversationsData?.results?.length > 0 && !selectedConversationId) {
      setSelectedConversationId(conversationsData.results[0].id);
    }
  }, [conversationsData, selectedConversationId]);

  const handleSendMessage = (content: string, files?: File[]) => {
    if (selectedConversationId) {
      sendMessageMutation.mutate({ content, files });
    }
  };

  const handleUpdateStatus = (status: string) => {
    if (!selectedConversationId) return;
    updateStatusMutation.mutate({ status });
  };

  const handleUpdatePriority = (priority: string) => {
    updatePriorityMutation.mutate(priority);
  };

  const conversations = conversationsData?.results || [];
  const stats = statistics || {
    total_conversations: 0,
    new_conversations: 0,
    in_progress_conversations: 0,
    resolved_conversations: 0,
    average_response_time: 0,
    customer_satisfaction: 0
  };

  return (
    <AdminLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 md:p-6 border-b bg-white">
          <div className="flex items-center space-x-4">
            {/* Mobile menu button */}
            {isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden"
              >
                <Menu className="h-5 w-5" />
              </Button>
            )}

            <div>
              <h1 className="text-xl md:text-2xl font-bold flex items-center">
                <MessageSquare className="h-5 w-5 md:h-6 md:w-6 mr-2 text-blue-600" />
                <span className="hidden sm:inline">Messagerie Interactive</span>
                <span className="sm:hidden">Messages</span>
              </h1>
              <p className="text-gray-500 text-sm hidden md:block">Communication en temps réel avec vos clients</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Notifications */}
            <MessageNotifications />

            {/* Quick Stats - Hidden on mobile */}
            <div className="hidden lg:flex items-center space-x-4 px-4 py-2 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">{stats.new_conversations}</div>
                <div className="text-xs text-gray-500">Nouveaux</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-orange-600">{stats.in_progress_conversations}</div>
                <div className="text-xs text-gray-500">En cours</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">{stats.resolved_conversations}</div>
                <div className="text-xs text-gray-500">Résolus</div>
              </div>
            </div>

            {/* Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Voir les statistiques
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="h-4 w-4 mr-2" />
                  Paramètres
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Archive className="h-4 w-4 mr-2" />
                  Archives
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden relative">
          {/* Mobile Overlay */}
          {isMobile && isMobileMenuOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
              onClick={() => setIsMobileMenuOpen(false)}
            />
          )}

          {/* Sidebar with Tabs */}
          <div className={`
            ${isMobile ? 'fixed inset-y-0 left-0 z-50 w-80 transform transition-transform duration-300 ease-in-out' : 'w-80 relative'}
            ${isMobile && !isMobileMenuOpen ? '-translate-x-full' : 'translate-x-0'}
            border-r bg-white flex flex-col md:relative md:translate-x-0
          `}>
            {/* Mobile header */}
            {isMobile && (
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="font-semibold">Conversations</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            )}

            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 m-4">
                <TabsTrigger value="all" className="text-xs">
                  <span className="hidden md:inline">Tous</span>
                  <span className="md:hidden">All</span>
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {stats.total_conversations}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="new" className="text-xs">
                  <span className="hidden md:inline">Nouveaux</span>
                  <span className="md:hidden">New</span>
                  {stats.new_conversations > 0 && (
                    <Badge variant="destructive" className="ml-1 text-xs">
                      {stats.new_conversations}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="in_progress" className="text-xs">
                  <span className="hidden md:inline">En cours</span>
                  <span className="md:hidden">Progress</span>
                  <Badge variant="default" className="ml-1 text-xs">
                    {stats.in_progress_conversations}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="resolved" className="text-xs">
                  <span className="hidden md:inline">Résolus</span>
                  <span className="md:hidden">Done</span>
                  <Badge variant="outline" className="ml-1 text-xs">
                    {stats.resolved_conversations}
                  </Badge>
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="flex-1 m-0">
                <ConversationList
                  conversations={conversations}
                  selectedConversationId={selectedConversationId}
                  onSelectConversation={(id) => {
                    setSelectedConversationId(id);
                    if (isMobile) setIsMobileMenuOpen(false);
                  }}
                  onCreateConversation={() => setIsNewConversationOpen(true)}
                  isLoading={isLoadingConversations}
                  className="h-full"
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            {selectedConversation ? (
              <div className="h-full flex flex-col">
                {/* Mobile back button */}
                {isMobile && (
                  <div className="flex items-center p-4 border-b bg-white md:hidden">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedConversationId(null)}
                      className="mr-3"
                    >
                      <ArrowLeft className="h-5 w-5" />
                    </Button>
                    <h3 className="font-semibold truncate">
                      {selectedConversation.subject}
                    </h3>
                  </div>
                )}

                <ChatInterface
                  conversation={selectedConversation}
                  onSendMessage={handleSendMessage}
                  onUpdateStatus={handleUpdateStatus}
                  onUpdatePriority={handleUpdatePriority}
                  isLoading={sendMessageMutation.isPending}
                  className="flex-1"
                />
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center bg-gray-50 p-4">
                <div className="text-center max-w-md">
                  <MessageSquare className="h-12 w-12 md:h-16 md:w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {isMobile ? 'Aucune conversation' : 'Sélectionnez une conversation'}
                  </h3>
                  <p className="text-gray-500 mb-4 text-sm md:text-base">
                    {isMobile
                      ? 'Ouvrez le menu pour voir les conversations'
                      : 'Choisissez une conversation dans la liste pour commencer à discuter'
                    }
                  </p>
                  {isMobile ? (
                    <Button onClick={() => setIsMobileMenuOpen(true)}>
                      <Menu className="h-4 w-4 mr-2" />
                      Voir les conversations
                    </Button>
                  ) : (
                    <Button onClick={() => setIsNewConversationOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Nouvelle conversation
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Performance Metrics Bar */}
        <div className="border-t bg-white px-4 md:px-6 py-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between text-sm space-y-2 md:space-y-0">
            <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-6">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span className="text-gray-600">Temps de réponse:</span>
                <span className="font-medium">{stats.average_response_time}min</span>
              </div>
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span className="text-gray-600">Satisfaction:</span>
                <span className="font-medium">{stats.customer_satisfaction}%</span>
              </div>
            </div>
            <div className="text-gray-500 text-xs md:text-sm">
              <span className="hidden md:inline">Dernière mise à jour: </span>
              {new Date().toLocaleTimeString('fr-FR')}
            </div>
          </div>
        </div>
      </div>

      {/* New Conversation Dialog */}
      <Dialog open={isNewConversationOpen} onOpenChange={setIsNewConversationOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle>Nouvelle conversation</DialogTitle>
                <DialogDescription>
                  Créer une nouvelle conversation avec un client
                </DialogDescription>
              </div>
              <ConversationFormHelp />
            </div>
          </DialogHeader>
          <NewConversationForm
            onSuccess={(conversation) => {
              setIsNewConversationOpen(false);
              setSelectedConversationId(conversation.id);
              refetchConversations();
            }}
            onCancel={() => setIsNewConversationOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default ModernMessages;
