import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";
import cmsApi from "@/services/cmsApi";

interface SchedulePublishDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contentType: "page" | "blog";
  contentId: string;
  currentStatus: string;
}

const SchedulePublishDialog = ({
  open,
  onOpenChange,
  contentType,
  contentId,
  currentStatus,
}: SchedulePublishDialogProps) => {
  const queryClient = useQueryClient();
  const [publishDate, setPublishDate] = useState<Date | undefined>(
    new Date(Date.now() + 24 * 60 * 60 * 1000) // Demain par défaut
  );
  const [publishTime, setPublishTime] = useState("12:00");

  // Mutation pour planifier la publication
  const schedulePublication = useMutation({
    mutationFn: async () => {
      if (!publishDate) return;

      // Combiner la date et l'heure
      const [hours, minutes] = publishTime.split(":").map(Number);
      const scheduledDate = new Date(publishDate);
      scheduledDate.setHours(hours, minutes);

      // Appeler l'API appropriée selon le type de contenu
      if (contentType === "page") {
        return cmsApi.updatePage(contentId, {
          status: "scheduled",
          scheduled_publish_at: scheduledDate.toISOString(),
        });
      } else {
        return cmsApi.updateBlogPost(contentId, {
          status: "scheduled",
          scheduled_publish_at: scheduledDate.toISOString(),
        });
      }
    },
    onSuccess: () => {
      // Invalider les requêtes pour mettre à jour les données
      if (contentType === "page") {
        queryClient.invalidateQueries({ queryKey: ["pages"] });
        queryClient.invalidateQueries({ queryKey: ["page", contentId] });
      } else {
        queryClient.invalidateQueries({ queryKey: ["blogPosts"] });
        queryClient.invalidateQueries({ queryKey: ["blogPost", contentId] });
      }

      toast({
        title: "Publication planifiée",
        description: `La publication a été planifiée pour le ${format(
          publishDate!,
          "d MMMM yyyy",
          { locale: fr }
        )} à ${publishTime}`,
      });

      onOpenChange(false);
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de planifier la publication",
        variant: "destructive",
      });
    },
  });

  // Vérifier si la date et l'heure sont valides
  const isValidSchedule = () => {
    if (!publishDate) return false;

    const [hours, minutes] = publishTime.split(":").map(Number);
    const scheduledDate = new Date(publishDate);
    scheduledDate.setHours(hours, minutes);

    // La date doit être dans le futur
    return scheduledDate > new Date();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Planifier la publication</DialogTitle>
          <DialogDescription>
            Choisissez la date et l'heure de publication
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="col-span-4 space-y-2">
              <label className="text-sm font-medium">Date de publication</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !publishDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {publishDate ? (
                      format(publishDate, "PPP", { locale: fr })
                    ) : (
                      <span>Choisir une date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={publishDate}
                    onSelect={setPublishDate}
                    initialFocus
                    disabled={(date) => date < new Date()}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="col-span-4 space-y-2">
              <label className="text-sm font-medium">Heure de publication</label>
              <input
                type="time"
                value={publishTime}
                onChange={(e) => setPublishTime(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Annuler
          </Button>
          <Button
            onClick={() => schedulePublication.mutate()}
            disabled={!isValidSchedule() || schedulePublication.isPending}
          >
            {schedulePublication.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Planification...
              </>
            ) : (
              "Planifier"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SchedulePublishDialog;
