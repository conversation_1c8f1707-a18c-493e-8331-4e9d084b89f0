
import { useState, ReactNode } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  Package,
  FolderTree,
  ShoppingCart,
  BarChart,
  Users,
  PackageOpen,
  Tag,
  MessageSquare,
  FileText,
  ClipboardList,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  CreditCard,
  Receipt,
  ArrowDownUp
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();

  const menuItems = [
    {
      title: "Tableau de bord",
      icon: <LayoutDashboard size={20} />,
      href: "/admin/dashboard",
    },
    {
      title: "Produits",
      icon: <Package size={20} />,
      href: "/admin/products",
    },
    {
      title: "Catégories",
      icon: <FolderTree size={20} />,
      href: "/admin/categories",
    },
    {
      title: "Commandes",
      icon: <ShoppingCart size={20} />,
      href: "/admin/orders",
    },
    {
      title: "Paiements",
      icon: <CreditCard size={20} />,
      href: "/admin/payment-methods",
    },
    {
      title: "Transactions",
      icon: <ArrowDownUp size={20} />,
      href: "/admin/transactions",
    },
    {
      title: "Factures",
      icon: <Receipt size={20} />,
      href: "/admin/invoices",
    },
    {
      title: "Statistiques",
      icon: <BarChart size={20} />,
      href: "/admin/statistics",
    },
    {
      title: "Utilisateurs",
      icon: <Users size={20} />,
      href: "/admin/users",
    },
    {
      title: "Stocks",
      icon: <PackageOpen size={20} />,
      href: "/admin/inventory",
    },
    {
      title: "Promotions",
      icon: <Tag size={20} />,
      href: "/admin/promotions",
    },
    {
      title: "Messagerie",
      icon: <MessageSquare size={20} />,
      href: "/admin/messages",
    },
    {
      title: "Contenu (CMS)",
      icon: <FileText size={20} />,
      href: "/admin/cms",
    },
    {
      title: "Journalisation",
      icon: <ClipboardList size={20} />,
      href: "/admin/logs",
    },
  ];

  const handleLogout = () => {
    // In a real app, you would implement actual logout logic here
    toast({
      title: "Déconnexion réussie",
      description: "À bientôt!",
    });

    // Redirect to login page
    window.location.href = "/login";
  };

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="bg-white shadow-md"
        >
          {mobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>
      </div>

      {/* Sidebar - Mobile */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 bg-black/50 z-40 lg:hidden" onClick={() => setMobileMenuOpen(false)}>
          <div
            className="absolute top-0 left-0 w-64 h-full bg-white shadow-lg overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 flex items-center justify-between border-b">
              <Link to="/admin/dashboard" className="flex items-center">
                <span className="text-xl font-bold text-primary">JOSNET</span>
                <span className="text-xl font-bold text-accent ml-1">ADMIN</span>
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X size={20} />
              </Button>
            </div>
            <div className="p-4">
              <nav className="space-y-1">
                {menuItems.map((item) => (
                  <Link
                    key={item.href}
                    to={item.href}
                    className={`flex items-center px-2 py-2 rounded-md ${
                      location.pathname === item.href
                        ? "bg-primary/10 text-primary font-medium"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span>{item.title}</span>
                  </Link>
                ))}
              </nav>
              <div className="mt-6 pt-6 border-t">
                <Button
                  variant="ghost"
                  className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={handleLogout}
                >
                  <LogOut size={20} className="mr-3" />
                  <span>Déconnexion</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sidebar - Desktop */}
      <div
        className={`hidden lg:block bg-white border-r border-gray-200 transition-all duration-300 ${
          sidebarCollapsed ? "w-20" : "w-64"
        }`}
      >
        <div className="h-full flex flex-col">
          <div className={`p-4 flex ${sidebarCollapsed ? "justify-center" : "justify-between"} items-center border-b`}>
            {!sidebarCollapsed ? (
              <>
                <Link to="/admin/dashboard" className="flex items-center">
                  <span className="text-xl font-bold text-primary">JOSNET</span>
                  <span className="text-xl font-bold text-accent ml-1">ADMIN</span>
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarCollapsed(true)}
                  className="ml-2"
                >
                  <ChevronLeft size={20} />
                </Button>
              </>
            ) : (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarCollapsed(false)}
              >
                <ChevronRight size={20} />
              </Button>
            )}
          </div>
          <div className="flex-grow overflow-y-auto p-4">
            <nav className="space-y-1">
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  className={`flex items-center px-2 py-2 rounded-md ${
                    location.pathname === item.href
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-gray-700 hover:bg-gray-100"
                  } ${sidebarCollapsed ? "justify-center" : ""}`}
                  title={sidebarCollapsed ? item.title : undefined}
                >
                  <span className={sidebarCollapsed ? "" : "mr-3"}>{item.icon}</span>
                  {!sidebarCollapsed && <span>{item.title}</span>}
                </Link>
              ))}
            </nav>
          </div>
          <div className={`p-4 border-t ${sidebarCollapsed ? "flex justify-center" : ""}`}>
            <Button
              variant="ghost"
              className={`text-red-600 hover:text-red-700 hover:bg-red-50 ${
                sidebarCollapsed ? "w-10 h-10 p-0" : "w-full justify-start"
              }`}
              title={sidebarCollapsed ? "Déconnexion" : undefined}
              onClick={handleLogout}
            >
              <LogOut size={20} className={sidebarCollapsed ? "" : "mr-3"} />
              {!sidebarCollapsed && <span>Déconnexion</span>}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-end">
            <div className="relative inline-block text-left">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className="text-sm font-medium">Administrateur</p>
                    <p className="text-xs text-gray-500"><EMAIL></p>
                  </div>
                  <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium">
                    AD
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 overflow-auto bg-gray-50 p-4 sm:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
