import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthToken } from '@/utils/auth';

/**
 * Hook pour gérer automatiquement les erreurs d'authentification
 */
export const useAuthErrorHandler = () => {
  const { user, isAuthenticated, logout } = useAuth();

  useEffect(() => {
    // Fonction pour nettoyer l'authentification
    const clearAuth = () => {
      console.log('🧹 Nettoyage de l\'authentification...');
      
      // Clear localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('token');
      localStorage.removeItem('jwt');
      localStorage.removeItem('user');
      localStorage.removeItem('refresh_token');
      
      // Clear sessionStorage
      sessionStorage.clear();
      
      // Call logout from context if available
      if (logout) {
        logout();
      }
    };

    // Fonction pour vérifier la validité du token
    const checkTokenValidity = async () => {
      const token = getAuthToken();
      
      if (!token) {
        console.log('🔍 Aucun token trouvé');
        return false;
      }

      try {
        // Test simple de l'API pour vérifier le token
        const response = await fetch('http://localhost:8000/api/v1/auth/verify-token/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ token })
        });

        if (response.status === 401) {
          console.log('🔍 Token invalide détecté');
          return false;
        }

        return response.ok;
      } catch (error) {
        console.log('🔍 Erreur lors de la vérification du token:', error);
        return false;
      }
    };

    // Vérifier le token si l'utilisateur semble authentifié
    if (isAuthenticated && user) {
      checkTokenValidity().then(isValid => {
        if (!isValid) {
          console.log('🚪 Token invalide - nettoyage automatique');
          clearAuth();
          
          // Rediriger vers la page de connexion après un délai
          setTimeout(() => {
            window.location.href = '/login?reason=token_expired';
          }, 1000);
        }
      });
    }
  }, [isAuthenticated, user, logout]);

  // Fonction utilitaire pour forcer un logout
  const forceLogout = (reason = 'manual') => {
    console.log(`🚪 Force logout - raison: ${reason}`);
    
    // Clear all auth data
    localStorage.removeItem('authToken');
    localStorage.removeItem('token');
    localStorage.removeItem('jwt');
    localStorage.removeItem('user');
    localStorage.removeItem('refresh_token');
    sessionStorage.clear();
    
    // Call logout from context
    if (logout) {
      logout();
    }
    
    // Redirect to login
    window.location.href = `/login?reason=${reason}`;
  };

  return {
    forceLogout
  };
};
