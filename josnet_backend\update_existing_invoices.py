import os
import django

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from payments.models import Invoice, InvoiceItem

def add_items_to_existing_invoices():
    """
    Add invoice items to existing invoices that don't have any items.
    """
    # Get all invoices
    invoices = Invoice.objects.all()
    print(f"Found {invoices.count()} invoices")
    
    for invoice in invoices:
        # Check if the invoice already has items
        if invoice.items.count() == 0:
            print(f"Processing invoice #{invoice.invoice_number} (ID: {invoice.id})")
            
            # Get the order items
            order_items = invoice.order.items.all()
            print(f"  Found {order_items.count()} order items")
            
            # Create invoice items for each order item
            for order_item in order_items:
                InvoiceItem.objects.create(
                    invoice=invoice,
                    order_item=order_item,
                    product_name=order_item.product_name,
                    variant_name=order_item.variant_name,
                    sku=order_item.sku,
                    price=order_item.price,
                    quantity=order_item.quantity,
                    subtotal=order_item.subtotal,
                    discount_amount=order_item.discount_amount,
                    final_price=order_item.final_price
                )
                print(f"  Created invoice item for {order_item.product_name}")
            
            print(f"  Added {order_items.count()} items to invoice #{invoice.invoice_number}")
        else:
            print(f"Invoice #{invoice.invoice_number} (ID: {invoice.id}) already has {invoice.items.count()} items")

if __name__ == '__main__':
    add_items_to_existing_invoices()
    print("Done!")
