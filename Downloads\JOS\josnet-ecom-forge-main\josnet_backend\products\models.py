from django.db import models
from django.utils.text import slugify
from django.conf import settings
from django.core.validators import MinValueValidator
import uuid

class Category(models.Model):
    """
    Model for product categories.
    """
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=150, unique=True)
    description = models.TextField(blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='children')
    image = models.ImageField(upload_to='categories/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = 'Categories'
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def full_path(self):
        """Return the full category path (including parent categories)."""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name

class ProductAttribute(models.Model):
    """
    Model for product attributes (e.g., color, size, material).
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

class AttributeValue(models.Model):
    """
    Model for attribute values (e.g., red, blue, small, large).
    """
    attribute = models.ForeignKey(ProductAttribute, on_delete=models.CASCADE, related_name='values')
    value = models.CharField(max_length=100)

    def __str__(self):
        return f"{self.attribute.name}: {self.value}"

class Product(models.Model):
    """
    Model for products.
    """
    STATUS_CHOICES = (
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    )

    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True)
    sku = models.CharField(max_length=50, unique=True)
    description = models.TextField()
    short_description = models.TextField(blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    sale_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, validators=[MinValueValidator(0)])
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, validators=[MinValueValidator(0)])
    categories = models.ManyToManyField(Category, related_name='products')
    attributes = models.ManyToManyField(ProductAttribute, through='ProductAttributeValue', related_name='products')
    is_featured = models.BooleanField(default=False)
    is_digital = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_products')
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='updated_products')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        if not self.sku:
            self.sku = f"PRD-{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)

    @property
    def current_price(self):
        """Return the current price (sale price if available, otherwise regular price)."""
        return self.sale_price if self.sale_price else self.price

    @property
    def discount_percentage(self):
        """Return the discount percentage if a sale price is set."""
        if self.sale_price and self.price > 0:
            return round((1 - self.sale_price / self.price) * 100)
        return 0

    @property
    def is_on_sale(self):
        """Check if the product is on sale."""
        return bool(self.sale_price and self.sale_price < self.price)

    @property
    def profit_margin(self):
        """Calculate the profit margin percentage."""
        if self.cost_price and self.cost_price > 0:
            return round(((self.current_price - self.cost_price) / self.cost_price) * 100)
        return None

class ProductImage(models.Model):
    """
    Model for product images.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/')
    alt_text = models.CharField(max_length=255, blank=True, null=True)
    is_primary = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Image for {self.product.name}"

class ProductAttributeValue(models.Model):
    """
    Model for product-specific attribute values.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='attribute_values')
    attribute = models.ForeignKey(ProductAttribute, on_delete=models.CASCADE)
    value = models.ForeignKey(AttributeValue, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('product', 'attribute', 'value')

    def __str__(self):
        return f"{self.product.name} - {self.attribute.name}: {self.value.value}"

class ProductVariant(models.Model):
    """
    Model for product variants (e.g., different colors, sizes).
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    name = models.CharField(max_length=255)
    sku = models.CharField(max_length=50, unique=True)
    price_adjustment = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    attributes = models.ManyToManyField(AttributeValue, related_name='variants')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.product.name} - {self.name}"

    def save(self, *args, **kwargs):
        if not self.sku:
            self.sku = f"VAR-{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)

    @property
    def price(self):
        """Calculate the variant price based on the base product price and adjustment."""
        return self.product.current_price + self.price_adjustment

class Inventory(models.Model):
    """
    Model for inventory management.
    """
    product = models.OneToOneField(Product, on_delete=models.CASCADE, related_name='inventory', null=True, blank=True)
    variant = models.OneToOneField(ProductVariant, on_delete=models.CASCADE, related_name='inventory', null=True, blank=True)
    quantity = models.PositiveIntegerField(default=0)
    low_stock_threshold = models.PositiveIntegerField(default=5)
    reserved_quantity = models.PositiveIntegerField(default=0)
    last_checked = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = 'Inventories'

    def __str__(self):
        if self.product:
            return f"Inventory for {self.product.name}"
        return f"Inventory for {self.variant.product.name} - {self.variant.name}"

    @property
    def available_quantity(self):
        """Calculate the available quantity (total - reserved)."""
        return max(0, self.quantity - self.reserved_quantity)

    @property
    def is_in_stock(self):
        """Check if the product is in stock."""
        return self.available_quantity > 0

    @property
    def is_low_stock(self):
        """Check if the product is low in stock."""
        return 0 < self.available_quantity <= self.low_stock_threshold

    def adjust_stock(self, quantity, reason=None, reference=None, user=None):
        """
        Adjust stock quantity and create a stock movement record.

        Args:
            quantity: The quantity to adjust (positive for additions, negative for reductions)
            reason: The reason for the adjustment
            reference: Reference to an order or other document
            user: The user who made the adjustment
        """
        # Calculate new quantity (don't allow negative)
        new_quantity = max(0, self.quantity + quantity)

        # Create stock movement record
        movement = StockMovement.objects.create(
            inventory=self,
            quantity_before=self.quantity,
            quantity_after=new_quantity,
            quantity_changed=quantity,
            reason=reason,
            reference=reference,
            created_by=user
        )

        # Update inventory
        self.quantity = new_quantity
        self.save()

        return movement

    def reserve_stock(self, quantity, reference=None, user=None):
        """
        Reserve stock for an order or other purpose.

        Args:
            quantity: The quantity to reserve
            reference: Reference to an order or other document
            user: The user who made the reservation

        Returns:
            True if reservation was successful, False otherwise
        """
        if quantity <= 0:
            return False

        if quantity > self.available_quantity:
            return False

        # Create stock reservation record
        reservation = StockReservation.objects.create(
            inventory=self,
            quantity=quantity,
            reference=reference,
            created_by=user
        )

        # Update reserved quantity
        self.reserved_quantity += quantity
        self.save()

        return True

    def release_reservation(self, reservation_id=None, reference=None, quantity=None, user=None):
        """
        Release a stock reservation.

        Args:
            reservation_id: ID of the reservation to release
            reference: Reference to an order or other document
            quantity: Quantity to release (if not releasing the whole reservation)
            user: The user who released the reservation

        Returns:
            True if release was successful, False otherwise
        """
        if reservation_id:
            try:
                reservation = StockReservation.objects.get(id=reservation_id, inventory=self)
            except StockReservation.DoesNotExist:
                return False

            # Release the reservation
            release_quantity = reservation.quantity
            reservation.delete()

        elif reference:
            # Find reservations by reference
            reservations = StockReservation.objects.filter(inventory=self, reference=reference)
            if not reservations.exists():
                return False

            # Calculate total quantity to release
            release_quantity = sum(r.quantity for r in reservations)

            # Delete the reservations
            reservations.delete()

        elif quantity:
            # Just release a specific quantity
            if quantity > self.reserved_quantity:
                return False

            release_quantity = quantity

        else:
            return False

        # Update reserved quantity
        self.reserved_quantity = max(0, self.reserved_quantity - release_quantity)
        self.save()

        return True

class StockMovement(models.Model):
    """
    Model for tracking stock movements.
    """
    REASON_CHOICES = [
        ('purchase', 'Purchase'),
        ('sale', 'Sale'),
        ('return', 'Return'),
        ('adjustment', 'Adjustment'),
        ('loss', 'Loss/Damage'),
        ('transfer', 'Transfer'),
        ('other', 'Other'),
    ]

    inventory = models.ForeignKey(Inventory, on_delete=models.CASCADE, related_name='movements')
    quantity_before = models.IntegerField()
    quantity_after = models.IntegerField()
    quantity_changed = models.IntegerField()
    reason = models.CharField(max_length=20, choices=REASON_CHOICES, default='adjustment')
    reference = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Stock movement for {self.inventory} ({self.quantity_changed:+d})"

class StockReservation(models.Model):
    """
    Model for tracking stock reservations.
    """
    inventory = models.ForeignKey(Inventory, on_delete=models.CASCADE, related_name='reservations')
    quantity = models.PositiveIntegerField()
    reference = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Reservation of {self.quantity} units for {self.inventory}"

class ProductReview(models.Model):
    """
    Model for product reviews.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reviews')
    rating = models.PositiveSmallIntegerField()
    title = models.CharField(max_length=255)
    comment = models.TextField()
    is_approved = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Review for {self.product.name} by {self.user.email}"

class RelatedProduct(models.Model):
    """
    Model for related products, upsells, cross-sells, etc.
    """
    RELATION_TYPE_CHOICES = [
        ('related', 'Related'),
        ('upsell', 'Upsell'),
        ('cross_sell', 'Cross Sell'),
        ('accessory', 'Accessory'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='related_products')
    related_product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='related_to_products')
    relation_type = models.CharField(max_length=20, choices=RELATION_TYPE_CHOICES)
    position = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('product', 'related_product', 'relation_type')
        ordering = ['relation_type', 'position']

    def __str__(self):
        return f"{self.product.name} -> {self.related_product.name} ({self.get_relation_type_display()})"
