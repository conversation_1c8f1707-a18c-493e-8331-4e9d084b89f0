from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CategoryViewSet,
    ProductAttributeViewSet,
    AttributeValueViewSet,
    ProductViewSet,
    ProductImageViewSet,
    ProductVariantViewSet,
    InventoryViewSet,
    ProductReviewViewSet,
    RelatedProductViewSet,
    CategoryImportExportView,
    ProductAttributeImportExportView,
    AttributeValueImportExportView,
    ProductImportExportView,
    ProductVariantImportExportView,
    InventoryImportExportView
)

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'categories', CategoryViewSet)
router.register(r'attributes', ProductAttributeViewSet)
router.register(r'attribute-values', AttributeValueViewSet)
router.register(r'products', ProductViewSet)
router.register(r'product-images', ProductImageViewSet)
router.register(r'product-variants', ProductVariantViewSet)
router.register(r'inventory', InventoryViewSet)
router.register(r'reviews', ProductReviewViewSet)
router.register(r'related-products', RelatedProductViewSet)

urlpatterns = [
    path('', include(router.urls)),

    # Import/Export URLs
    path('import-export/categories/', CategoryImportExportView.as_view(), name='import-export-categories'),
    path('import-export/attributes/', ProductAttributeImportExportView.as_view(), name='import-export-attributes'),
    path('import-export/attribute-values/', AttributeValueImportExportView.as_view(), name='import-export-attribute-values'),
    path('import-export/products/', ProductImportExportView.as_view(), name='import-export-products'),
    path('import-export/product-variants/', ProductVariantImportExportView.as_view(), name='import-export-product-variants'),
    path('import-export/inventory/', InventoryImportExportView.as_view(), name='import-export-inventory'),
]
