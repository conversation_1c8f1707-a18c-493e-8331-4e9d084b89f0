#!/usr/bin/env python3
"""
Script pour vérifier l'état du système de traduction
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from translations.models import TranslationKey, Translation, TranslationCache
from django.conf import settings

def check_translation_status():
    """Vérifier l'état du système de traduction"""
    print("🌐 ÉTAT DU SYSTÈME DE TRADUCTION")
    print("=" * 50)
    
    # Statistiques générales
    total_keys = TranslationKey.objects.count()
    total_translations = Translation.objects.count()
    
    print(f"📊 STATISTIQUES GÉNÉRALES:")
    print(f"   • Clés de traduction: {total_keys}")
    print(f"   • Traductions totales: {total_translations}")
    
    # Statistiques par langue
    print(f"\n🌍 TRADUCTIONS PAR LANGUE:")
    languages = [
        ('fr', 'Français', '🇫🇷'),
        ('en', 'English', '🇬🇧'),
        ('sw', 'Kiswahili', '🇹🇿'),
        ('rn', 'Kirundi', '🇧🇮'),
    ]
    
    for code, name, flag in languages:
        count = Translation.objects.filter(language_code=code).count()
        approved = Translation.objects.filter(language_code=code, is_approved=True).count()
        percentage = (count / total_keys * 100) if total_keys > 0 else 0
        print(f"   {flag} {name} ({code}): {count}/{total_keys} ({percentage:.1f}%) - {approved} approuvées")
    
    # Clés les plus récentes
    print(f"\n🆕 CLÉS RÉCENTES:")
    recent_keys = TranslationKey.objects.order_by('-created_at')[:5]
    for key in recent_keys:
        print(f"   • {key.key} ({key.category or 'Sans catégorie'})")
    
    # Cache
    cache_count = TranslationCache.objects.count()
    print(f"\n💾 CACHE:")
    print(f"   • Entrées en cache: {cache_count}")
    
    # Configuration
    print(f"\n⚙️ CONFIGURATION:")
    print(f"   • Langue par défaut: {settings.LANGUAGE_CODE}")
    print(f"   • Langues supportées: {[lang[0] for lang in settings.LANGUAGES]}")
    print(f"   • Timeout cache: {getattr(settings, 'TRANSLATION_CACHE_TIMEOUT', 'Non défini')}")

def show_missing_translations():
    """Afficher les traductions manquantes"""
    print(f"\n❌ TRADUCTIONS MANQUANTES:")
    print("=" * 50)
    
    all_keys = TranslationKey.objects.filter(is_active=True)
    languages = ['fr', 'en', 'sw', 'rn']
    
    for lang_code in languages:
        missing_keys = []
        for key in all_keys:
            if not Translation.objects.filter(key=key, language_code=lang_code).exists():
                missing_keys.append(key.key)
        
        if missing_keys:
            lang_name = dict([('fr', 'Français'), ('en', 'English'), ('sw', 'Kiswahili'), ('rn', 'Kirundi')])[lang_code]
            print(f"\n🔴 {lang_name} ({lang_code}) - {len(missing_keys)} manquantes:")
            for key in missing_keys[:10]:  # Afficher seulement les 10 premières
                print(f"   • {key}")
            if len(missing_keys) > 10:
                print(f"   ... et {len(missing_keys) - 10} autres")

def suggest_next_steps():
    """Suggérer les prochaines étapes"""
    print(f"\n🚀 PROCHAINES ÉTAPES POUR TRADUIRE TOUTE LA PLATEFORME:")
    print("=" * 60)
    
    total_keys = TranslationKey.objects.count()
    
    if total_keys == 0:
        print("1. 📝 Importer les traductions initiales:")
        print("   python josnet_backend/manage.py import_translations")
        print("\n2. 🔍 Scanner le code pour détecter les clés manquantes")
        print("\n3. 🌐 Ajouter les traductions via l'interface admin")
    else:
        print("1. 🔍 Compléter les traductions manquantes via l'admin Django")
        print("   http://localhost:8000/admin/translations/")
        
        print("\n2. 🌐 Utiliser Rosetta pour une interface plus conviviale")
        print("   http://localhost:8000/rosetta/")
        
        print("\n3. 📱 Intégrer le sélecteur de langue dans le frontend")
        print("   Utiliser le composant LanguageSelector existant")
        
        print("\n4. 🔧 Remplacer les textes en dur par des appels t()")
        print("   Exemple: 'Accueil' → t('nav.home', 'Accueil')")
        
        print("\n5. 🧪 Tester avec le composant TranslationDemo")

if __name__ == "__main__":
    try:
        check_translation_status()
        show_missing_translations()
        suggest_next_steps()
        
        print(f"\n✅ Vérification terminée!")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
