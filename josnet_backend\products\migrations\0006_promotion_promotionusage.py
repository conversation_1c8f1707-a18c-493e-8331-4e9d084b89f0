# Generated by Django 4.2.23 on 2025-07-06 07:18

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0006_order_currency'),
        ('products', '0005_alter_productreview_options_productreview_cons_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Promotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('promotion_type', models.CharField(choices=[('percentage', 'Pourcentage'), ('fixed_amount', 'Montant fixe'), ('buy_x_get_y', 'Achetez X obtenez Y'), ('free_shipping', 'Livraison gratuite')], max_length=20)),
                ('status', models.CharField(choices=[('draft', 'Brouillon'), ('active', 'Active'), ('scheduled', 'Programmée'), ('expired', 'Expirée'), ('paused', 'En pause')], default='draft', max_length=20)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MinValueValidator(100)])),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('minimum_order_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('maximum_uses', models.PositiveIntegerField(blank=True, null=True)),
                ('maximum_uses_per_user', models.PositiveIntegerField(blank=True, null=True)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('apply_to_all', models.BooleanField(default=False)),
                ('send_email_notification', models.BooleanField(default=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total_uses', models.PositiveIntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('categories', models.ManyToManyField(blank=True, related_name='promotions', to='products.category')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_promotions', to=settings.AUTH_USER_MODEL)),
                ('products', models.ManyToManyField(blank=True, related_name='promotions', to='products.product')),
            ],
            options={
                'verbose_name': 'Promotion',
                'verbose_name_plural': 'Promotions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PromotionUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discount_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
                ('promotion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usages', to='products.promotion')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Utilisation de Promotion',
                'verbose_name_plural': 'Utilisations de Promotions',
                'ordering': ['-used_at'],
            },
        ),
    ]
