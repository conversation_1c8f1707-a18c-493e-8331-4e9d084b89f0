import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Chip,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import { Grid } from '@mui/material';
import { useTheme, alpha } from '@mui/material/styles';
import AdminLayout from '../../components/admin/AdminLayout';
import { formatCurrency } from '../../utils/formatters';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import DoneAllIcon from '@mui/icons-material/DoneAll';

// Fonction utilitaire pour formater les dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
};

// Types
interface ReturnItem {
  id: string;
  product_name: string;
  quantity: number;
  price: number;
  reason: string;
  description: string | null;
}

interface ReturnRequest {
  id: string;
  return_number: string;
  order: {
    id: string;
    order_number: string;
  };
  user: {
    id: string;
    email: string;
    full_name: string;
  };
  status: 'pending' | 'approved' | 'rejected' | 'processing' | 'completed';
  reason: string;
  additional_info: string | null;
  created_at: string;
  updated_at: string;
  items: ReturnItem[];
}

const statusColors = {
  pending: '#FFA000',      // Amber
  approved: '#4CAF50',     // Green
  rejected: '#F44336',     // Red
  processing: '#2196F3',   // Blue
  completed: '#9C27B0'     // Purple
};

const statusLabels = {
  pending: 'En attente',
  approved: 'Approuvé',
  rejected: 'Rejeté',
  processing: 'En traitement',
  completed: 'Terminé'
};

const reasonLabels = {
  defective: 'Produit défectueux',
  wrong_item: 'Mauvais article',
  not_as_described: 'Ne correspond pas à la description',
  damaged: 'Endommagé pendant le transport',
  no_longer_needed: 'Plus nécessaire',
  other: 'Autre raison'
};

const AdminReturns: React.FC = () => {
  const theme = useTheme();
  const [returns, setReturns] = useState<ReturnRequest[]>([]);
  const [filteredReturns, setFilteredReturns] = useState<ReturnRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedReturn, setSelectedReturn] = useState<ReturnRequest | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [updateStatusOpen, setUpdateStatusOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [adminNote, setAdminNote] = useState('');
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [userDataCache, setUserDataCache] = useState<{[key: string]: any}>({});

  // Fonction pour enrichir les données avec les informations utilisateur
  const enrichReturnsWithUserData = async (returnsArray: any[]) => {
    const enrichedReturns = [];

    for (const returnItem of returnsArray) {
      let enrichedReturn = { ...returnItem };

      // Si les données utilisateur sont manquantes ou incomplètes
      if (!returnItem.user || !returnItem.user.full_name) {
        try {
          // Vérifier le cache d'abord
          const userId = returnItem.user?.id;
          if (userId && userDataCache[userId]) {
            enrichedReturn.user = { ...returnItem.user, ...userDataCache[userId] };
          } else if (userId) {
            // Récupérer les données utilisateur depuis l'API
            const userResponse = await fetch(`http://localhost:8000/api/v1/auth/users/${userId}/`, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
                'Content-Type': 'application/json'
              }
            });

            if (userResponse.ok) {
              const userData = await userResponse.json();
              // Mettre en cache
              setUserDataCache(prev => ({ ...prev, [userId]: userData }));
              enrichedReturn.user = { ...returnItem.user, ...userData };
              console.log(`✅ Données utilisateur récupérées pour ${userData.email}`);
            }
          }
        } catch (err) {
          console.warn('Erreur lors de la récupération des données utilisateur:', err);
        }
      }

      enrichedReturns.push(enrichedReturn);
    }

    return enrichedReturns;
  };

  // Fetch returns data
  useEffect(() => {
    const fetchReturns = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:8000/api/v1/orders/returns/', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Erreur ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Returns API response:', data);

        // S'assurer que data est un tableau
        const returnsArray = Array.isArray(data) ? data : (data.results ? data.results : []);
        console.log('Returns array:', returnsArray);

        // Enrichir avec les données utilisateur si nécessaires
        const enrichedReturns = await enrichReturnsWithUserData(returnsArray);

        setReturns(enrichedReturns);
        setFilteredReturns(enrichedReturns);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
        console.error('Error fetching returns:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchReturns();
  }, []);

  // Filter returns based on search term and status filter
  useEffect(() => {
    // S'assurer que returns est un tableau avant de l'utiliser
    if (!Array.isArray(returns)) {
      console.warn('Returns is not an array:', returns);
      setFilteredReturns([]);
      return;
    }

    let result = returns;

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(item => item.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(item =>
        item.return_number.toLowerCase().includes(term) ||
        (item.order?.order_number?.toLowerCase().includes(term)) ||
        (item.user?.email?.toLowerCase().includes(term)) ||
        (item.user?.full_name?.toLowerCase().includes(term))
      );
    }

    setFilteredReturns(result);
  }, [returns, searchTerm, statusFilter]);

  const handleViewDetails = async (returnRequest: ReturnRequest) => {
    try {
      setDetailsLoading(true);
      setDetailsOpen(true);

      // Récupérer les détails complets du retour depuis l'API
      const response = await fetch(`http://localhost:8000/api/v1/orders/returns/${returnRequest.id}/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      const detailedReturn = await response.json();
      console.log('Detailed return data:', detailedReturn);

      setSelectedReturn(detailedReturn);
    } catch (err) {
      console.error('Error fetching return details:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors de la récupération des détails');
      // En cas d'erreur, utiliser les données de base disponibles
      setSelectedReturn(returnRequest);
    } finally {
      setDetailsLoading(false);
    }
  };

  const handleUpdateStatus = (returnRequest: ReturnRequest) => {
    setSelectedReturn(returnRequest);
    setNewStatus(returnRequest.status);
    setUpdateStatusOpen(true);
  };

  const submitStatusUpdate = async () => {
    if (!selectedReturn || !newStatus) return;

    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8000/api/v1/orders/returns/${selectedReturn.id}/update_status/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus,
          comment: adminNote
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      // Update local state
      const updatedReturn = await response.json();
      setReturns(prevReturns => 
        prevReturns.map(item => 
          item.id === updatedReturn.id ? updatedReturn : item
        )
      );

      setUpdateStatusOpen(false);
      setAdminNote('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      console.error('Error updating return status:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon />;
      case 'rejected':
        return <CancelIcon />;
      case 'processing':
        return <LocalShippingIcon />;
      case 'completed':
        return <DoneAllIcon />;
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: theme.palette.primary.main, mb: 2 }}>
          Gestion des Retours
        </Typography>
        <Typography variant="subtitle1" sx={{ mb: 3, color: theme.palette.text.secondary }}>
          Interface d'administration pour le traitement des demandes de retour clients
        </Typography>
        
        {/* Filters and Search */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
              <InputLabel id="status-filter-label">Statut</InputLabel>
              <Select
                labelId="status-filter-label"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Statut"
              >
                <MenuItem value="all">Tous les statuts</MenuItem>
                <MenuItem value="pending">En attente</MenuItem>
                <MenuItem value="approved">Approuvé</MenuItem>
                <MenuItem value="rejected">Rejeté</MenuItem>
                <MenuItem value="processing">En traitement</MenuItem>
                <MenuItem value="completed">Terminé</MenuItem>
              </Select>
            </FormControl>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <TextField
                size="small"
                variant="outlined"
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Box>
          </Box>
        </Box>

        {/* Error message */}
        {error && (
          <Box sx={{ mb: 2 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}

        {/* Returns Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ mb: 4, borderRadius: '8px', overflow: 'hidden', boxShadow: '0 3px 10px rgba(0,0,0,0.1)' }}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  <TableCell>Numéro de retour</TableCell>
                  <TableCell>Commande</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Date de création</TableCell>
                  <TableCell>Raison</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredReturns.length > 0 ? (
                  filteredReturns.map((returnRequest) => (
                    <TableRow key={returnRequest.id}>
                      <TableCell>{returnRequest.return_number}</TableCell>
                      <TableCell>{returnRequest.order.order_number}</TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {returnRequest.user?.full_name || returnRequest.user?.email || 'Utilisateur inconnu'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {returnRequest.user?.email || 'Email non disponible'}
                        </Typography>
                      </TableCell>
                      <TableCell>{formatDate(returnRequest.created_at)}</TableCell>
                      <TableCell>{reasonLabels[returnRequest.reason as keyof typeof reasonLabels] || returnRequest.reason}</TableCell>
                      <TableCell>
                        <Chip 
                          label={statusLabels[returnRequest.status] || returnRequest.status}
                          sx={{ 
                            backgroundColor: statusColors[returnRequest.status] || theme.palette.grey[500],
                            color: '#fff',
                            fontWeight: 'bold',
                            padding: '4px 0',
                            borderRadius: '16px',
                            '& .MuiChip-label': { px: 2 }
                          }}
                          icon={getStatusIcon(returnRequest.status)}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Voir les détails">
                            <IconButton 
                              size="small" 
                              onClick={() => handleViewDetails(returnRequest)}
                              sx={{ 
                                color: theme.palette.primary.main, 
                                '&:hover': { 
                                  backgroundColor: alpha(theme.palette.primary.main, 0.1) 
                                }
                              }}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Button 
                            size="small" 
                            variant="outlined" 
                            onClick={() => handleUpdateStatus(returnRequest)}
                          >
                            Mettre à jour
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      Aucun retour trouvé
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {/* Return Details Dialog */}
        <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth="md" fullWidth>
          {detailsLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>Chargement des détails...</Typography>
            </Box>
          ) : selectedReturn && (
            <>
              <DialogTitle>
                Détails du retour {selectedReturn.return_number}
                <Chip
                  label={statusLabels[selectedReturn.status] || selectedReturn.status}
                  size="small"
                  sx={{ 
                    backgroundColor: statusColors[selectedReturn.status] || theme.palette.grey[500],
                    color: '#fff',
                    fontWeight: 'bold',
                    padding: '4px 0',
                    borderRadius: '16px',
                    '& .MuiChip-label': { px: 2 }
                  }}
                />
              </DialogTitle>
              <DialogContent dividers>
                <Grid container spacing={3}>
                  <Grid xs={12} md={6}>
                    <Card variant="outlined" sx={{ mb: 2, width: '100%', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom>Informations générales</Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Numéro de commande:</Typography>
                            <Typography variant="body2">{selectedReturn.order.order_number}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Date de création:</Typography>
                            <Typography variant="body2">{formatDate(selectedReturn.created_at)}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Dernière mise à jour:</Typography>
                            <Typography variant="body2">{formatDate(selectedReturn.updated_at)}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Raison principale:</Typography>
                            <Typography variant="body2">{reasonLabels[selectedReturn.reason as keyof typeof reasonLabels] || selectedReturn.reason}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">ID du retour:</Typography>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>{selectedReturn.id}</Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>

                    <Card variant="outlined" sx={{ width: '100%' }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>Informations client</Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Nom:</Typography>
                            <Typography variant="body2">
                              {selectedReturn.user?.full_name || selectedReturn.user?.email || 'Utilisateur inconnu'}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Email:</Typography>
                            <Typography variant="body2">
                              {selectedReturn.user?.email || 'Email non disponible'}
                            </Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid xs={12} md={6}>
                    <Card variant="outlined" sx={{ mb: 2, width: '100%' }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>Informations supplémentaires</Typography>
                        <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                          {selectedReturn.additional_info || 'Aucune information supplémentaire fournie.'}
                        </Typography>
                      </CardContent>
                    </Card>

                    <Card variant="outlined" sx={{ width: '100%' }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>Métadonnées</Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Créé le:</Typography>
                            <Typography variant="body2">{formatDate(selectedReturn.created_at)}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Modifié le:</Typography>
                            <Typography variant="body2">{formatDate(selectedReturn.updated_at)}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Statut actuel:</Typography>
                            <Chip
                              label={statusLabels[selectedReturn.status] || selectedReturn.status}
                              size="small"
                              sx={{
                                backgroundColor: statusColors[selectedReturn.status] || theme.palette.grey[500],
                                color: '#fff'
                              }}
                            />
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid xs={12}>
                    <Card variant="outlined" sx={{ width: '100%' }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>Articles à retourner</Typography>
                        <TableContainer>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Produit</TableCell>
                                <TableCell align="right">Quantité</TableCell>
                                <TableCell align="right">Prix unitaire</TableCell>
                                <TableCell align="right">Total</TableCell>
                                <TableCell>Raison</TableCell>
                                <TableCell>Description</TableCell>
                                <TableCell>ID Article</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {selectedReturn.items && Array.isArray(selectedReturn.items) && selectedReturn.items.length > 0 ? (
                                selectedReturn.items.map((item) => (
                                  <TableRow key={item.id}>
                                    <TableCell>
                                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                        {item.product_name}
                                      </Typography>
                                    </TableCell>
                                    <TableCell align="right">
                                      <Typography variant="body2">{item.quantity}</Typography>
                                    </TableCell>
                                    <TableCell align="right">
                                      <Typography variant="body2">{formatCurrency(item.price)}</Typography>
                                    </TableCell>
                                    <TableCell align="right">
                                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                        {formatCurrency(item.price * item.quantity)}
                                      </Typography>
                                    </TableCell>
                                    <TableCell>
                                      <Chip
                                        label={reasonLabels[item.reason as keyof typeof reasonLabels] || item.reason}
                                        size="small"
                                        variant="outlined"
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <Typography variant="body2" sx={{ maxWidth: 200, wordBreak: 'break-word' }}>
                                        {item.description || '-'}
                                      </Typography>
                                    </TableCell>
                                    <TableCell>
                                      <Typography variant="caption" sx={{ fontFamily: 'monospace', color: 'text.secondary' }}>
                                        {item.id}
                                      </Typography>
                                    </TableCell>
                                  </TableRow>
                                ))
                              ) : (
                                <TableRow>
                                  <TableCell colSpan={7} align="center">
                                    Aucun article détaillé disponible pour ce retour
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </TableContainer>

                        {/* Total du retour */}
                        {selectedReturn.items && Array.isArray(selectedReturn.items) && selectedReturn.items.length > 0 && (
                          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                            <Typography variant="h6" align="right">
                              Total du retour: {formatCurrency(
                                selectedReturn.items.reduce((total, item) => total + (item.price * item.quantity), 0)
                              )}
                            </Typography>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDetailsOpen(false)}>Fermer</Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => {
                    setDetailsOpen(false);
                    handleUpdateStatus(selectedReturn);
                  }}
                >
                  Mettre à jour le statut
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>

        {/* Update Status Dialog */}
        <Dialog open={updateStatusOpen} onClose={() => setUpdateStatusOpen(false)}>
          {selectedReturn && (
            <>
              <DialogTitle>Mettre à jour le statut du retour {selectedReturn.return_number}</DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1, minWidth: 400 }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel id="new-status-label">Nouveau statut</InputLabel>
                    <Select
                      labelId="new-status-label"
                      value={newStatus}
                      onChange={(e) => setNewStatus(e.target.value)}
                      label="Nouveau statut"
                    >
                      <MenuItem value="pending">En attente</MenuItem>
                      <MenuItem value="approved">Approuvé</MenuItem>
                      <MenuItem value="rejected">Rejeté</MenuItem>
                      <MenuItem value="processing">En traitement</MenuItem>
                      <MenuItem value="completed">Terminé</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    label="Note administrative (optionnelle)"
                    multiline
                    rows={4}
                    fullWidth
                    value={adminNote}
                    onChange={(e) => setAdminNote(e.target.value)}
                  />
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setUpdateStatusOpen(false)}>Annuler</Button>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<VisibilityIcon />}
                  onClick={submitStatusUpdate}
                  disabled={loading}
                  sx={{ 
                    backgroundColor: theme.palette.primary.main,
                    '&:hover': { backgroundColor: theme.palette.primary.dark },
                    borderRadius: '8px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Enregistrer'}
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Box>
    </AdminLayout>
  );
};

export default AdminReturns;
