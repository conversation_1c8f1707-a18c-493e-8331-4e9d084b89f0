#!/usr/bin/env python3
"""
Script pour nettoyer et recréer des transactions de test avec méthodes de paiement
"""

import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from orders.models import Order
from payments.models import Transaction, PaymentMethod

def cleanup_and_recreate():
    """Nettoyer et recréer des transactions de test"""
    print("🧹 Nettoyage et recréation des transactions de test")
    print("=" * 60)
    
    try:
        # Récupérer l'utilisateur de test
        user = User.objects.get(email="<EMAIL>")
        print(f"👤 Utilisateur: {user.email}")
        
        # Supprimer les anciennes transactions
        old_transactions = Transaction.objects.filter(order__user=user)
        count = old_transactions.count()
        old_transactions.delete()
        print(f"🗑️ {count} anciennes transactions supprimées")
        
        # Récupérer ou créer la commande
        order = Order.objects.filter(user=user).first()
        if not order:
            order = Order.objects.create(
                user=user,
                status='completed',
                total_amount=50000,
                currency='BIF'
            )
            print(f"📦 Nouvelle commande créée (ID: {order.id})")
        else:
            print(f"📦 Commande existante utilisée (ID: {order.id})")
        
        # Créer ou récupérer des méthodes de paiement
        payment_methods = []
        payment_method_data = [
            {'name': 'Carte de crédit', 'provider': 'stripe', 'description': 'Paiement par carte bancaire'},
            {'name': 'Mobile Money', 'provider': 'mobile_money', 'description': 'Paiement mobile'},
            {'name': 'Virement bancaire', 'provider': 'bank_transfer', 'description': 'Virement bancaire'},
            {'name': 'PayPal', 'provider': 'paypal', 'description': 'Paiement PayPal'}
        ]
        
        for pm_data in payment_method_data:
            pm, created = PaymentMethod.objects.get_or_create(
                name=pm_data['name'],
                defaults={
                    'provider': pm_data['provider'], 
                    'is_active': True,
                    'description': pm_data['description']
                }
            )
            payment_methods.append(pm)
            if created:
                print(f"💳 Méthode de paiement créée: {pm.name}")
            else:
                print(f"💳 Méthode de paiement existante: {pm.name}")
        
        # Créer des transactions de test variées
        transactions_data = [
            {
                'transaction_id': 'TXN-CC-001',
                'amount': 25000,
                'status': 'completed',
                'currency': 'BIF',
                'payment_method': payment_methods[0],
                'error_message': None
            },
            {
                'transaction_id': 'TXN-MM-002',
                'amount': 15000,
                'status': 'pending',
                'currency': 'BIF',
                'payment_method': payment_methods[1],
                'error_message': None
            },
            {
                'transaction_id': 'TXN-BT-003',
                'amount': 10000,
                'status': 'failed',
                'currency': 'BIF',
                'payment_method': payment_methods[2],
                'error_message': 'Fonds insuffisants'
            },
            {
                'transaction_id': 'TXN-PP-004',
                'amount': 35000,
                'status': 'completed',
                'currency': 'BIF',
                'payment_method': payment_methods[3],
                'error_message': None
            },
            {
                'transaction_id': 'TXN-CC-005',
                'amount': 8000,
                'status': 'refunded',
                'currency': 'BIF',
                'payment_method': payment_methods[0],
                'error_message': None
            }
        ]
        
        print(f"\n💳 Création de {len(transactions_data)} nouvelles transactions...")
        
        for i, txn_data in enumerate(transactions_data, 1):
            transaction = Transaction.objects.create(
                order=order,
                **txn_data
            )
            print(f"   {i}. ✅ {transaction.transaction_id} - {transaction.amount} BIF - {transaction.get_status_display()} - {transaction.payment_method.name}")
        
        # Vérifier le résultat
        final_count = Transaction.objects.filter(order__user=user).count()
        print(f"\n📊 Résultat final:")
        print(f"   Total des transactions: {final_count}")
        print(f"   Utilisateur: {user.email}")
        print(f"   Commande: #{order.id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = cleanup_and_recreate()
    
    if success:
        print(f"\n🎉 NETTOYAGE ET RECRÉATION RÉUSSIS!")
        print(f"   Les nouvelles transactions sont prêtes pour les tests.")
        print(f"   Testez maintenant: http://localhost:8080/account/transactions")
    else:
        print(f"\n❌ ÉCHEC DU NETTOYAGE")
        print(f"   Vérifiez les erreurs ci-dessus.")
