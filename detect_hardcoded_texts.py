#!/usr/bin/env python3
"""
Script pour détecter les textes en dur qui doivent être traduits
"""
import os
import re
import json
from pathlib import Path

def scan_react_files():
    """Scanner les fichiers React/TypeScript pour les textes en dur"""
    print("🔍 SCAN DES FICHIERS REACT/TYPESCRIPT")
    print("=" * 50)
    
    src_path = Path("src")
    if not src_path.exists():
        print("❌ Dossier src/ non trouvé")
        return []
    
    hardcoded_texts = []
    
    # Patterns pour détecter les textes en dur
    patterns = [
        # Textes entre guillemets simples ou doubles (français détecté)
        r'["\']([A-ZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞŸ][^"\']{3,})["\']',
        # Textes dans les placeholders
        r'placeholder=["\']([^"\']+)["\']',
        # Textes dans les labels
        r'label=["\']([^"\']+)["\']',
        # Textes dans les title
        r'title=["\']([^"\']+)["\']',
    ]
    
    excluded_patterns = [
        r'^[a-z_]+\.[a-z_]+',  # Clés de traduction existantes
        r'^[A-Z_]+$',          # Constantes
        r'^\d+',               # Nombres
        r'^[a-z]+$',           # Mots simples anglais
        r'className|onClick|onChange|onSubmit',  # Props React
    ]
    
    for file_path in src_path.rglob("*.tsx"):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for pattern in patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    text = match.group(1)
                    
                    # Exclure certains patterns
                    if any(re.match(excl, text) for excl in excluded_patterns):
                        continue
                    
                    # Vérifier si c'est du français (présence d'accents ou mots français)
                    if (any(c in text for c in 'àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞŸ') or
                        any(word in text.lower() for word in ['accueil', 'produit', 'panier', 'commande', 'connexion', 'inscription', 'recherche'])):
                        
                        line_num = content[:match.start()].count('\n') + 1
                        hardcoded_texts.append({
                            'file': str(file_path),
                            'line': line_num,
                            'text': text,
                            'context': content.split('\n')[line_num-1].strip()
                        })
        
        except Exception as e:
            print(f"⚠️ Erreur lors du scan de {file_path}: {e}")
    
    return hardcoded_texts

def scan_django_files():
    """Scanner les fichiers Django pour les textes en dur"""
    print("\n🔍 SCAN DES FICHIERS DJANGO")
    print("=" * 50)
    
    backend_path = Path("josnet_backend")
    if not backend_path.exists():
        print("❌ Dossier josnet_backend/ non trouvé")
        return []
    
    hardcoded_texts = []
    
    # Patterns pour Django
    patterns = [
        # Messages dans les vues
        r'messages\.(success|error|warning|info)\([^,]+,\s*["\']([^"\']+)["\']',
        # Textes dans les serializers
        r'help_text=["\']([^"\']+)["\']',
        # Labels de modèles
        r'verbose_name=["\']([^"\']+)["\']',
        # Messages d'erreur
        r'ValidationError\(["\']([^"\']+)["\']',
    ]
    
    for file_path in backend_path.rglob("*.py"):
        if "migrations" in str(file_path) or "__pycache__" in str(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for pattern in patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    text = match.group(-1)  # Dernier groupe capturé
                    
                    # Vérifier si c'est du français
                    if (len(text) > 3 and 
                        (any(c in text for c in 'àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞŸ') or
                         any(word in text.lower() for word in ['succès', 'erreur', 'créé', 'modifié', 'supprimé']))):
                        
                        line_num = content[:match.start()].count('\n') + 1
                        hardcoded_texts.append({
                            'file': str(file_path),
                            'line': line_num,
                            'text': text,
                            'context': content.split('\n')[line_num-1].strip()
                        })
        
        except Exception as e:
            print(f"⚠️ Erreur lors du scan de {file_path}: {e}")
    
    return hardcoded_texts

def suggest_translation_keys(hardcoded_texts):
    """Suggérer des clés de traduction pour les textes trouvés"""
    print("\n💡 SUGGESTIONS DE CLÉS DE TRADUCTION")
    print("=" * 50)
    
    suggestions = []
    
    for item in hardcoded_texts:
        text = item['text']
        file_path = item['file']
        
        # Déterminer la catégorie basée sur le fichier
        if 'nav' in file_path.lower() or 'header' in file_path.lower():
            category = 'nav'
        elif 'product' in file_path.lower():
            category = 'product'
        elif 'cart' in file_path.lower() or 'panier' in file_path.lower():
            category = 'cart'
        elif 'order' in file_path.lower() or 'commande' in file_path.lower():
            category = 'order'
        elif 'auth' in file_path.lower() or 'login' in file_path.lower():
            category = 'auth'
        elif 'form' in file_path.lower():
            category = 'form'
        else:
            category = 'common'
        
        # Générer une clé basée sur le texte
        key_base = re.sub(r'[^a-zA-Z0-9\s]', '', text.lower())
        key_base = re.sub(r'\s+', '_', key_base)[:20]
        
        suggested_key = f"{category}.{key_base}"
        
        suggestions.append({
            'original_text': text,
            'suggested_key': suggested_key,
            'file': file_path,
            'line': item['line'],
            'category': category
        })
    
    return suggestions

def generate_translation_template(suggestions):
    """Générer un template de traductions à compléter"""
    print("\n📝 GÉNÉRATION DU TEMPLATE DE TRADUCTIONS")
    print("=" * 50)
    
    template = []
    
    for suggestion in suggestions:
        template.append({
            "key": suggestion['suggested_key'],
            "category": suggestion['category'],
            "description": f"Traduction pour: {suggestion['original_text']}",
            "source_file": suggestion['file'],
            "source_line": suggestion['line'],
            "translations": {
                "fr": suggestion['original_text'],
                "en": "TODO: Translate to English",
                "sw": "TODO: Translate to Kiswahili", 
                "rn": "TODO: Translate to Kirundi"
            }
        })
    
    # Sauvegarder le template
    with open('new_translations_template.json', 'w', encoding='utf-8') as f:
        json.dump(template, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Template sauvegardé dans: new_translations_template.json")
    return template

def main():
    """Fonction principale"""
    print("🌐 DÉTECTION DES TEXTES À TRADUIRE")
    print("=" * 60)
    
    # Scanner les fichiers React
    react_texts = scan_react_files()
    print(f"📱 Textes React trouvés: {len(react_texts)}")
    
    # Scanner les fichiers Django
    django_texts = scan_django_files()
    print(f"🐍 Textes Django trouvés: {len(django_texts)}")
    
    # Combiner tous les textes
    all_texts = react_texts + django_texts
    
    if not all_texts:
        print("\n✅ Aucun texte en dur détecté ou tous les textes sont déjà traduits!")
        return
    
    # Afficher les résultats
    print(f"\n📊 RÉSULTATS DÉTAILLÉS:")
    for i, item in enumerate(all_texts[:10], 1):  # Afficher les 10 premiers
        print(f"\n{i}. 📄 {item['file']}:{item['line']}")
        print(f"   💬 Texte: \"{item['text']}\"")
        print(f"   🔍 Contexte: {item['context'][:80]}...")
    
    if len(all_texts) > 10:
        print(f"\n... et {len(all_texts) - 10} autres textes trouvés")
    
    # Générer des suggestions
    suggestions = suggest_translation_keys(all_texts)
    template = generate_translation_template(suggestions)
    
    print(f"\n🎯 PROCHAINES ÉTAPES:")
    print(f"   1. Examinez le fichier: new_translations_template.json")
    print(f"   2. Complétez les traductions manquantes")
    print(f"   3. Importez avec: python import_translations_manual.py")
    print(f"   4. Remplacez les textes en dur par t('key', 'fallback')")
    
    print(f"\n📈 STATISTIQUES:")
    print(f"   • Total de textes à traduire: {len(all_texts)}")
    print(f"   • Fichiers React concernés: {len(set(item['file'] for item in react_texts))}")
    print(f"   • Fichiers Django concernés: {len(set(item['file'] for item in django_texts))}")

if __name__ == "__main__":
    main()
