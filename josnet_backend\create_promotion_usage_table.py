import os
import django
import sqlite3

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

# Connect to the SQLite database
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db.sqlite3')
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Check if the table already exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='marketing_promotionusage'")
if cursor.fetchone():
    print("Table marketing_promotionusage already exists.")
else:
    # Create the marketing_promotionusage table
    cursor.execute('''
    CREATE TABLE "marketing_promotionusage" (
        "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
        "discount_amount" decimal NOT NULL,
        "created_at" datetime NOT NULL,
        "order_id" integer NOT NULL REFERENCES "orders_order" ("id") DEFERRABLE INITIALLY DEFERRED,
        "promotion_id" integer NOT NULL REFERENCES "marketing_promotion" ("id") DEFERRABLE INITIALLY DEFERRED,
        "user_id" integer NULL REFERENCES "authentication_user" ("id") DEFERRABLE INITIALLY DEFERRED
    )
    ''')
    
    # Create indexes
    cursor.execute('CREATE INDEX "marketing_promotionusage_order_id_idx" ON "marketing_promotionusage" ("order_id")')
    cursor.execute('CREATE INDEX "marketing_promotionusage_promotion_id_idx" ON "marketing_promotionusage" ("promotion_id")')
    cursor.execute('CREATE INDEX "marketing_promotionusage_user_id_idx" ON "marketing_promotionusage" ("user_id")')
    
    # Create unique constraint
    cursor.execute('CREATE UNIQUE INDEX "marketing_promotionusage_promotion_id_order_id_uniq" ON "marketing_promotionusage" ("promotion_id", "order_id")')
    
    # Update django_migrations table to mark the migration as applied
    cursor.execute('''
    INSERT INTO django_migrations (app, name, applied)
    VALUES ('marketing', '0002_promotionusage', datetime('now'))
    ''')
    
    print("Table marketing_promotionusage created successfully.")

# Commit changes and close connection
conn.commit()
conn.close()
