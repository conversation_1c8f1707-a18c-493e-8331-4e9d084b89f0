from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.db.models import Count, Sum
from ..models import UserAddress, LoyaltyTransaction

User = get_user_model()

class AdminUserSerializer(serializers.ModelSerializer):
    """Serializer for admin user management."""
    
    full_name = serializers.CharField(read_only=True)
    order_count = serializers.IntegerField(read_only=True, default=0)
    total_spent = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2, default=0)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name', 'role',
            'phone_number', 'date_of_birth', 'profile_picture',
            'is_active', 'is_verified', 'loyalty_points', 'loyalty_tier',
            'created_at', 'updated_at', 'order_count', 'total_spent'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'loyalty_points', 'loyalty_tier']

class AdminUserCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating users by admin."""
    
    password = serializers.Char<PERSON>ield(write_only=True, required=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True, required=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'role',
            'phone_number', 'date_of_birth', 'profile_picture',
            'is_active', 'is_verified', 'password', 'confirm_password'
        ]
        read_only_fields = ['id']
    
    def validate(self, attrs):
        if attrs['password'] != attrs.pop('confirm_password'):
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs
    
    def create(self, validated_data):
        password = validated_data.pop('password')
        user = User.objects.create(**validated_data)
        user.set_password(password)
        user.save()
        return user

class AdminUserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating users by admin."""
    
    password = serializers.CharField(write_only=True, required=False, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True, required=False)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'role',
            'phone_number', 'date_of_birth', 'profile_picture',
            'is_active', 'is_verified', 'password', 'confirm_password'
        ]
        read_only_fields = ['id', 'email']  # Email cannot be changed
    
    def validate(self, attrs):
        if 'password' in attrs and 'confirm_password' not in attrs:
            raise serializers.ValidationError({"confirm_password": "This field is required when changing password."})
        
        if 'confirm_password' in attrs and 'password' not in attrs:
            raise serializers.ValidationError({"password": "This field is required when confirming password."})
        
        if 'password' in attrs and attrs['password'] != attrs.pop('confirm_password'):
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        
        return attrs
    
    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        if password:
            instance.set_password(password)
        
        instance.save()
        return instance

class AdminUserAddressSerializer(serializers.ModelSerializer):
    """Serializer for user addresses in admin panel."""
    
    class Meta:
        model = UserAddress
        fields = '__all__'
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']

class AdminLoyaltyTransactionSerializer(serializers.ModelSerializer):
    """Serializer for loyalty transactions in admin panel."""
    
    class Meta:
        model = LoyaltyTransaction
        fields = '__all__'
        read_only_fields = ['id', 'user', 'created_at']

class AdminUserStatsSerializer(serializers.Serializer):
    """Serializer for user statistics in admin panel."""
    
    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    inactive_users = serializers.IntegerField()
    verified_users = serializers.IntegerField()
    unverified_users = serializers.IntegerField()
    role_distribution = serializers.DictField(child=serializers.IntegerField())
    recent_users = AdminUserSerializer(many=True)
    top_customers = serializers.ListField(child=serializers.DictField())
