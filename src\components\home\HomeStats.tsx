import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import homeApi from "@/services/homeApi";

const HomeStats = () => {
  // Fetch home statistics
  const { 
    data: stats, 
    isLoading, 
    isError 
  } = useQuery({
    queryKey: ['homeStats'],
    queryFn: homeApi.getHomeStats,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  // Default stats in case of loading or error
  const defaultStats = {
    total_customers: 1000,
    total_projects: 500,
    years_experience: 10,
    total_products: 150
  };

  const displayStats = stats || defaultStats;

  return (
    <section className="py-20 bg-gradient-to-r from-primary-dark to-primary text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')] bg-cover bg-center opacity-10"></div>
      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">JOSNET en chiffres</h2>
          <div className="w-24 h-1 bg-accent mx-auto mb-6"></div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {/* Clients satisfaits */}
          <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
            {isLoading ? (
              <>
                <Skeleton className="h-12 w-20 mx-auto mb-3 bg-white/20" />
                <Skeleton className="h-6 w-24 mx-auto bg-white/20" />
              </>
            ) : (
              <>
                <div className="text-5xl font-bold mb-3 text-accent">
                  {displayStats.total_customers.toLocaleString()}+
                </div>
                <p className="text-xl text-white/90">Clients satisfaits</p>
              </>
            )}
          </div>

          {/* Projets réalisés */}
          <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
            {isLoading ? (
              <>
                <Skeleton className="h-12 w-20 mx-auto mb-3 bg-white/20" />
                <Skeleton className="h-6 w-24 mx-auto bg-white/20" />
              </>
            ) : (
              <>
                <div className="text-5xl font-bold mb-3 text-accent">
                  {displayStats.total_projects.toLocaleString()}+
                </div>
                <p className="text-xl text-white/90">Projets réalisés</p>
              </>
            )}
          </div>

          {/* Années d'expérience */}
          <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
            {isLoading ? (
              <>
                <Skeleton className="h-12 w-20 mx-auto mb-3 bg-white/20" />
                <Skeleton className="h-6 w-24 mx-auto bg-white/20" />
              </>
            ) : (
              <>
                <div className="text-5xl font-bold mb-3 text-accent">
                  {displayStats.years_experience}+
                </div>
                <p className="text-xl text-white/90">Années d'expérience</p>
              </>
            )}
          </div>

          {/* Produits disponibles */}
          <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
            {isLoading ? (
              <>
                <Skeleton className="h-12 w-20 mx-auto mb-3 bg-white/20" />
                <Skeleton className="h-6 w-24 mx-auto bg-white/20" />
              </>
            ) : (
              <>
                <div className="text-5xl font-bold mb-3 text-accent">
                  {displayStats.total_products.toLocaleString()}+
                </div>
                <p className="text-xl text-white/90">Produits disponibles</p>
              </>
            )}
          </div>
        </div>

        {/* Error indicator (subtle) */}
        {isError && !isLoading && (
          <div className="text-center mt-8">
            <p className="text-white/70 text-sm">
              * Données mises à jour automatiquement
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default HomeStats;
