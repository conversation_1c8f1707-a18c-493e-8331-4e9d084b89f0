#!/usr/bin/env python
"""
Test simple et robuste du CMS et affichage côté client.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from cms.models import BlogPost, BlogCategory
from core.models import SiteSettings, ActivityLog

User = get_user_model()


def test_blog_system():
    """Test simple du système de blog."""
    print("📝 TEST: SYSTÈME DE BLOG COMPLET")
    print("=" * 60)
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Admin',
            'last_name': 'BlogSimple',
            'password': 'adminpass123',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'role': 'admin'
        }
    )
    
    if created:
        admin_user.set_password('adminpass123')
        admin_user.save()
    else:
        admin_user.role = 'admin'
        admin_user.save()
    
    # Client admin
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    admin_client = APIClient()
    admin_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    # Client public
    public_client = APIClient()
    
    print(f"🔄 ÉTAPE 1: Admin crée une catégorie...")
    
    # Nettoyer les anciennes données
    BlogPost.objects.filter(title__contains='Blog Simple Test').delete()
    BlogCategory.objects.filter(name__contains='Blog Simple Test').delete()
    
    # Créer une catégorie
    category_data = {
        'name': 'Blog Simple Test - Tech News',
        'slug': 'blog-simple-test-tech-news',
        'description': 'Actualités technologiques pour test'
    }
    
    response = admin_client.post('/api/v1/cms/blog/categories/', category_data, format='json')
    
    if response.status_code == 201:
        category = response.json()
        print(f"   ✅ Catégorie créée: {category['name']}")
        
        print(f"\n🔄 ÉTAPE 2: Admin crée un article...")
        
        # Créer un article
        blog_data = {
            'title': 'Blog Simple Test - Intelligence Artificielle au Burundi',
            'slug': 'blog-simple-test-ia-burundi',
            'content': '''
            <h1>L'Intelligence Artificielle arrive au Burundi</h1>
            <p>Une révolution technologique se profile à l'horizon burundais avec l'émergence de l'Intelligence Artificielle.</p>
            
            <h2>Applications concrètes</h2>
            <ul>
                <li>Agriculture intelligente</li>
                <li>Santé numérique</li>
                <li>Éducation personnalisée</li>
                <li>Services financiers automatisés</li>
            </ul>
            
            <p>JosNet Network accompagne cette transformation digitale.</p>
            ''',
            'excerpt': 'Découvrez comment l\'IA transforme le paysage technologique burundais.',
            'status': 'published',
            'featured': True,
            'categories': [category['id']],
            'author_name': 'Admin Blog Simple',
            'meta_description': 'IA au Burundi - JosNet Network',
            'meta_keywords': 'IA, intelligence artificielle, Burundi, JosNet'
        }
        
        response = admin_client.post('/api/v1/cms/blog/posts/', blog_data, format='json')
        
        if response.status_code == 201:
            article = response.json()
            print(f"   ✅ Article créé: {article['title']}")
            print(f"   📝 Slug: {article['slug']}")
            print(f"   ⭐ Mis en avant: {article['featured']}")
            
            print(f"\n🔄 ÉTAPE 3: Client public accède aux articles...")
            
            # Test d'accès public
            response = public_client.get('/api/v1/cms/blog/posts/')
            
            if response.status_code == 200:
                articles_data = response.json()
                articles = articles_data.get('results', [])
                
                print(f"   ✅ Liste accessible: {len(articles)} articles")
                
                # Trouver notre article
                test_article = None
                for art in articles:
                    if 'Blog Simple Test' in art['title']:
                        test_article = art
                        break
                
                if test_article:
                    print(f"   ✅ Article trouvé: {test_article['title']}")
                    print(f"   📄 Extrait: {test_article['excerpt'][:50]}...")
                    
                    # Accès à l'article complet
                    response = public_client.get(f"/api/v1/cms/blog/posts/{test_article['slug']}/")
                    
                    if response.status_code == 200:
                        full_article = response.json()
                        print(f"   ✅ Article complet accessible")
                        print(f"   📄 Contenu: {len(full_article['content'])} caractères")
                        
                        # Vérifier le contenu
                        if 'Intelligence Artificielle' in full_article['content']:
                            print(f"   ✅ Contenu complet transmis au client")
                            return True
                        else:
                            print(f"   ❌ Contenu incomplet")
                            return False
                    else:
                        print(f"   ❌ Article complet non accessible")
                        return False
                else:
                    print(f"   ❌ Article non trouvé dans la liste")
                    return False
            else:
                print(f"   ❌ Liste non accessible: {response.status_code}")
                return False
        else:
            print(f"   ❌ Erreur création article: {response.status_code}")
            print(f"   Détails: {response.content.decode()}")
            return False
    else:
        print(f"   ❌ Erreur création catégorie: {response.status_code}")
        print(f"   Détails: {response.content.decode()}")
        return False


def test_site_settings():
    """Test des paramètres du site."""
    print("\n⚙️  TEST: PARAMÈTRES DU SITE")
    print("=" * 60)
    
    print(f"🔄 Configuration des paramètres...")
    
    # Configurer les paramètres
    site_settings, created = SiteSettings.objects.get_or_create(
        defaults={
            'site_name': 'JosNet Network - Test Simple',
            'site_description': 'Solutions IT et télécommunications au Burundi',
            'phone_primary': '+257 22 123 456',
            'email_primary': '<EMAIL>',
            'address_line1': 'Avenue de l\'Indépendance',
            'city': 'Bujumbura',
            'country': 'Burundi',
            'business_hours': 'Lun-Ven: 8h-18h',
            'facebook_url': 'https://facebook.com/josnetnetwork',
            'twitter_url': 'https://twitter.com/josnetnetwork'
        }
    )
    
    if not created:
        site_settings.site_name = 'JosNet Network - Test Simple'
        site_settings.save()
    
    print(f"   ✅ Paramètres configurés: {site_settings.site_name}")
    
    print(f"\n🔄 Test d'accès public aux paramètres...")
    
    # Test d'accès public
    public_client = APIClient()
    response = public_client.get('/api/v1/core/site-settings/')
    
    if response.status_code == 200:
        settings_data = response.json()
        print(f"   ✅ Paramètres accessibles côté client")
        print(f"   🏢 Nom: {settings_data.get('site_name', 'N/A')}")
        print(f"   📧 Email: {settings_data.get('email_primary', 'N/A')}")
        print(f"   📞 Téléphone: {settings_data.get('phone_primary', 'N/A')}")
        print(f"   📍 Ville: {settings_data.get('city', 'N/A')}")
        
        # Vérifier les données essentielles
        if settings_data.get('site_name') and settings_data.get('email_primary'):
            print(f"   ✅ Données essentielles présentes")
            return True
        else:
            print(f"   ❌ Données essentielles manquantes")
            return False
    else:
        print(f"   ❌ Paramètres non accessibles: {response.status_code}")
        return False


def test_activity_logs():
    """Test du système de journalisation."""
    print("\n📝 TEST: JOURNALISATION")
    print("=" * 60)
    
    print(f"🔄 Vérification des logs d'activité...")
    
    # Compter les logs
    total_logs = ActivityLog.objects.count()
    recent_logs = ActivityLog.objects.order_by('-timestamp')[:5]
    
    print(f"   📊 Total des logs: {total_logs}")
    print(f"   📊 Logs récents: {recent_logs.count()}")
    
    if recent_logs.exists():
        print(f"   ✅ Système de journalisation actif")
        
        print(f"\n   📋 Activités récentes:")
        for i, log in enumerate(recent_logs, 1):
            timestamp = log.timestamp.strftime('%d/%m %H:%M')
            action = log.get_action_type_display()
            user = log.user_email or 'Système'
            print(f"      {i}. {timestamp} - {action} - {user}")
        
        return True
    else:
        print(f"   ⚠️  Aucun log trouvé")
        return False


def test_public_api_endpoints():
    """Test des endpoints publics pour le frontend."""
    print("\n🌐 TEST: ENDPOINTS PUBLICS POUR FRONTEND")
    print("=" * 60)
    
    public_client = APIClient()
    
    # Endpoints à tester
    endpoints = [
        ('/api/v1/cms/blog/posts/', 'Articles de blog'),
        ('/api/v1/cms/blog/categories/', 'Catégories de blog'),
        ('/api/v1/core/site-settings/', 'Paramètres du site'),
        ('/api/v1/products/categories/', 'Catégories de produits'),
        ('/api/v1/products/products/', 'Produits'),
    ]
    
    results = []
    
    for endpoint, description in endpoints:
        print(f"🔄 Test: {description}")
        response = public_client.get(endpoint)
        
        if response.status_code == 200:
            print(f"   ✅ {description}: Accessible")
            results.append(True)
        else:
            print(f"   ❌ {description}: Erreur {response.status_code}")
            results.append(False)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 Résumé endpoints: {success_count}/{total_count} accessibles")
    
    return success_count == total_count


def main():
    """Fonction principale."""
    print("🚀 TEST SIMPLE CMS ET AFFICHAGE CLIENT")
    print("=" * 80)
    print("Test que le contenu créé par l'admin s'affiche côté client")
    print("=" * 80)
    
    results = []
    
    try:
        # Test 1: Blog
        blog_success = test_blog_system()
        results.append(("Système de blog", blog_success))
        
        # Test 2: Paramètres du site
        settings_success = test_site_settings()
        results.append(("Paramètres du site", settings_success))
        
        # Test 3: Journalisation
        logs_success = test_activity_logs()
        results.append(("Journalisation", logs_success))
        
        # Test 4: Endpoints publics
        api_success = test_public_api_endpoints()
        results.append(("Endpoints publics", api_success))
        
        # Résumé
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ CMS ET AFFICHAGE CLIENT")
        print("=" * 80)
        
        passed = sum(1 for _, success in results if success)
        failed = len(results) - passed
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\nTotal: {len(results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print(f"\n🎉 CMS ET AFFICHAGE CLIENT 100% OPÉRATIONNELS !")
            print(f"✅ Le contenu admin s'affiche bien côté client")
            print(f"✅ Les articles de blog sont accessibles publiquement")
            print(f"✅ Les paramètres du site sont transmis au frontend")
            print(f"✅ La journalisation fonctionne")
            print(f"✅ Tous les endpoints publics sont accessibles")
            print(f"\n🌐 LE SYSTÈME CMS EST PRÊT POUR LE FRONTEND !")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
