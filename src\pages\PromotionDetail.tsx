import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Tag, ArrowLeft, Clock, Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import promotionApi from '@/services/promotionApi';
import { format, parseISO, isAfter, isBefore } from 'date-fns';
import { fr } from 'date-fns/locale';
import { formatCurrency } from '@/utils/formatters';

const PromotionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [copied, setCopied] = useState(false);

  // Récupérer les détails de la promotion (utilise l'API publique)
  const {
    data: promotion,
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: ['promotion', id],
    queryFn: () => promotionApi.getPublicPromotion(Number(id)),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Vérifier si la promotion est active
  const isActive = promotion && 
    (!promotion.end_date || isAfter(parseISO(promotion.end_date), new Date())) &&
    (!promotion.start_date || isBefore(parseISO(promotion.start_date), new Date()));

  // Copier le code promo dans le presse-papier
  const copyPromoCode = () => {
    if (promotion?.discount_code) {
      navigator.clipboard.writeText(promotion.discount_code);
      setCopied(true);
      toast({
        title: "Code promo copié !",
        description: "Le code a été copié dans votre presse-papier.",
      });
      
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Rediriger vers la page des produits avec le code promo
  const shopNow = () => {
    navigate('/products', { state: { promoCode: promotion?.discount_code } });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="mb-6">
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <Link to="/" className="hover:underline">Accueil</Link>
              <span>/</span>
              <Link to="/promotions" className="hover:underline">Promotions</Link>
              <span>/</span>
              <span>Détails</span>
            </div>
          </div>
          <Skeleton className="h-64 w-full rounded-lg mb-6" />
          <div className="space-y-4">
            <Skeleton className="h-10 w-3/4" />
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-24 w-full" />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (isError || !promotion) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8 text-center">
          <div className="max-w-lg mx-auto">
            <h1 className="text-2xl font-bold mb-4">Promotion introuvable</h1>
            <p className="text-gray-600 mb-6">
              Désolé, la promotion que vous recherchez n'existe pas ou n'est plus disponible.
            </p>
            <Link to="/promotions">
              <Button>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Retour aux promotions
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
            <Link to="/" className="hover:underline">Accueil</Link>
            <span>/</span>
            <Link to="/promotions" className="hover:underline">Promotions</Link>
            <span>/</span>
            <span>{promotion.name}</span>
          </div>
          
          <Link to="/promotions" className="inline-flex items-center text-primary hover:underline mb-4">
            <ArrowLeft className="mr-1 h-4 w-4" />
            Retour aux promotions
          </Link>
        </div>
        
        {/* Promotion Card */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-primary/20 max-w-4xl mx-auto">
          {/* Status Banner */}
          <div className={`py-2 px-4 text-center text-white font-medium ${isActive ? 'bg-green-600' : 'bg-red-600'}`}>
            {isActive ? 'Promotion active' : 'Promotion expirée'}
          </div>
          
          <div className="p-6 md:p-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row justify-between items-start gap-4 mb-6">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">{promotion.name}</h1>
                
                <div className="flex items-center text-gray-500 mb-4">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-sm">
                    {promotion.start_date && (
                      <>Valable du {format(parseISO(promotion.start_date), 'dd MMMM yyyy', { locale: fr })}</>
                    )}
                    {promotion.end_date && (
                      <> au {format(parseISO(promotion.end_date), 'dd MMMM yyyy', { locale: fr })}</>
                    )}
                    {!promotion.start_date && !promotion.end_date && (
                      <>Valable sans limite de temps</>
                    )}
                  </span>
                </div>
              </div>
              
              <div className="bg-primary/10 text-primary px-4 py-2 rounded-full text-lg font-medium">
                {promotion.discount_type === 'percentage' 
                  ? `${promotion.discount_value}% de réduction` 
                  : `${formatCurrency(promotion.discount_value)} de réduction`}
              </div>
            </div>
            
            {/* Description */}
            <div className="prose max-w-none mb-8">
              <p className="text-gray-700 whitespace-pre-line">{promotion.description}</p>
            </div>
            
            {/* Promo Code */}
            {promotion.discount_code && (
              <div className="mb-8">
                <p className="text-gray-700 font-medium mb-2">Utilisez ce code promo lors de votre commande :</p>
                <div className="flex items-center">
                  <div className="bg-gray-100 py-3 px-6 rounded-l-lg font-mono text-lg font-medium flex-grow text-center">
                    {promotion.discount_code}
                  </div>
                  <Button 
                    onClick={copyPromoCode} 
                    variant="default" 
                    className="rounded-l-none py-3 h-auto"
                  >
                    {copied ? <Check className="h-5 w-5" /> : <Copy className="h-5 w-5" />}
                    <span className="ml-2">{copied ? 'Copié' : 'Copier'}</span>
                  </Button>
                </div>
              </div>
            )}
            
            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button onClick={shopNow} size="lg" className="flex-1">
                Profiter de cette offre
              </Button>
              <Link to="/products" className="flex-1">
                <Button variant="outline" size="lg" className="w-full">
                  Voir tous les produits
                </Button>
              </Link>
            </div>
            
            {/* Conditions supplémentaires si nécessaire */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-medium mb-2">Conditions d'utilisation</h3>
              <p className="text-sm text-gray-600 whitespace-pre-line">
                Cette promotion est soumise aux conditions générales de vente du site. 
                Veuillez consulter notre page des conditions générales pour plus d'informations.
              </p>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default PromotionDetail;
