import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box, Typography, Paper, Grid, Button, TextField, MenuItem,
  Divider, IconButton, CircularProgress, Alert, Checkbox, FormControlLabel
} from '@/components/mui-compatibility';
import { Plus as AddIcon, Trash as DeleteIcon } from 'lucide-react';
import orderApi, { Order, OrderItem, ShippingMethod } from '../../services/orderApi';
import { productApi } from '../../services/productApi';

interface ProductOption {
  id: number;
  name: string;
  price: number;
  sku: string;
}

const OrderForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = Boolean(id);

  const [order, setOrder] = useState<Order>({
    email: '',
    billing_first_name: '',
    billing_last_name: '',
    billing_address_line1: '',
    billing_city: '',
    billing_postal_code: '',
    billing_country: '',
    shipping_first_name: '',
    shipping_last_name: '',
    shipping_address_line1: '',
    shipping_city: '',
    shipping_postal_code: '',
    shipping_country: '',
    payment_method: 'credit_card',
    subtotal: 0,
    shipping_cost: 0,
    tax_amount: 0,
    discount_amount: 0,
    total: 0,
    items: []
  });

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<ProductOption[]>([]);
  const [shippingMethods, setShippingMethods] = useState<ShippingMethod[]>([]);
  const [sameAsBilling, setSameAsBilling] = useState<boolean>(true);

  // Fetch order data if in edit mode
  useEffect(() => {
    if (isEditMode && id) {
      setLoading(true);
      orderApi.getOrder(parseInt(id))
        .then(data => {
          setOrder(data);
          // Check if shipping address is same as billing
          const isSame =
            data.shipping_first_name === data.billing_first_name &&
            data.shipping_last_name === data.billing_last_name &&
            data.shipping_address_line1 === data.billing_address_line1 &&
            data.shipping_address_line2 === data.billing_address_line2 &&
            data.shipping_city === data.billing_city &&
            data.shipping_state === data.billing_state &&
            data.shipping_postal_code === data.billing_postal_code &&
            data.shipping_country === data.billing_country;
          setSameAsBilling(isSame);
        })
        .catch(err => setError(err.message || 'Failed to fetch order'))
        .finally(() => setLoading(false));
    }
  }, [id, isEditMode]);

  // Fetch products and shipping methods
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productsData, shippingData] = await Promise.all([
          productApi.getProducts({ page_size: 100 }),
          orderApi.getShippingMethods()
        ]);

        const productOptions = productsData.results.map((product: any) => ({
          id: product.id,
          name: product.name,
          price: product.current_price,
          sku: product.sku
        }));

        setProducts(productOptions);
        setShippingMethods(shippingData);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch data');
      }
    };

    fetchData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setOrder(prev => ({ ...prev, [name]: value }));

    // If same as billing is checked, update shipping address
    if (sameAsBilling && name.startsWith('billing_')) {
      const shippingField = name.replace('billing_', 'shipping_');
      setOrder(prev => ({ ...prev, [shippingField]: value }));
    }
  };

  const handleSameAsBillingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    setSameAsBilling(checked);

    if (checked) {
      // Copy billing address to shipping address
      setOrder(prev => ({
        ...prev,
        shipping_first_name: prev.billing_first_name,
        shipping_last_name: prev.billing_last_name,
        shipping_company: prev.billing_company,
        shipping_address_line1: prev.billing_address_line1,
        shipping_address_line2: prev.billing_address_line2,
        shipping_city: prev.billing_city,
        shipping_state: prev.billing_state,
        shipping_postal_code: prev.billing_postal_code,
        shipping_country: prev.billing_country
      }));
    }
  };

  const handleAddItem = () => {
    if (products.length === 0) return;

    const product = products[0];
    const newItem: OrderItem = {
      product: product.id,
      product_name: product.name,
      sku: product.sku,
      price: product.price,
      quantity: 1,
      discount_amount: 0
    };

    setOrder(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));

    updateOrderTotals([...(order.items || []), newItem]);
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...(order.items || [])];
    updatedItems.splice(index, 1);

    setOrder(prev => ({
      ...prev,
      items: updatedItems
    }));

    updateOrderTotals(updatedItems);
  };

  const handleItemChange = (index: number, field: keyof OrderItem, value: any) => {
    const updatedItems = [...(order.items || [])];

    if (field === 'product') {
      const selectedProduct = products.find(p => p.id === parseInt(value));
      if (selectedProduct) {
        updatedItems[index] = {
          ...updatedItems[index],
          product: selectedProduct.id,
          product_name: selectedProduct.name,
          sku: selectedProduct.sku,
          price: selectedProduct.price
        };
      }
    } else {
      updatedItems[index] = {
        ...updatedItems[index],
        [field]: field === 'quantity' || field === 'price' || field === 'discount_amount'
          ? parseFloat(value)
          : value
      };
    }

    setOrder(prev => ({
      ...prev,
      items: updatedItems
    }));

    updateOrderTotals(updatedItems);
  };

  const updateOrderTotals = (items: OrderItem[]) => {
    // Calculate subtotal
    const subtotal = items.reduce((sum, item) => {
      return sum + (item.price * item.quantity);
    }, 0);

    // Calculate discount total
    const discountTotal = items.reduce((sum, item) => {
      return sum + (item.discount_amount || 0);
    }, 0) + (order.discount_amount || 0);

    // Calculate total
    const total = subtotal + (order.shipping_cost || 0) + (order.tax_amount || 0) - discountTotal;

    setOrder(prev => ({
      ...prev,
      subtotal,
      discount_amount: discountTotal,
      total: Math.max(0, total)
    }));
  };

  const handleShippingMethodChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const methodId = parseInt(e.target.value);
    const selectedMethod = shippingMethods.find(method => method.id === methodId);

    if (selectedMethod) {
      setOrder(prev => ({
        ...prev,
        shipping_cost: selectedMethod.price
      }));

      updateOrderTotals(order.items || []);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (isEditMode && id) {
        // Update existing order
        const { items, ...orderData } = order;
        await orderApi.updateOrder(parseInt(id), orderData);
      } else {
        // Create new order
        await orderApi.createOrder(order);
      }

      navigate('/admin/orders');
    } catch (err: any) {
      setError(err.message || 'Failed to save order');
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditMode) {
    return <CircularProgress />;
  }

  return (
    <form onSubmit={handleSubmit}>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4">
          {isEditMode ? 'Edit Order' : 'Create Order'}
        </Typography>
        <Box>
          <Button
            variant="outlined"
            className="mr-2"
            onClick={() => navigate('/admin/orders')}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Save Order'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} className="mb-6">{error}</Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Customer Information</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="email"
                  label="Email"
                  value={order.email}
                  onChange={handleChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="phone"
                  label="Phone"
                  value={order.phone || ''}
                  onChange={handleChange}
                  fullWidth
                />
              </Grid>
            </Grid>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Billing Address</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="billing_first_name"
                  label="First Name"
                  value={order.billing_first_name}
                  onChange={handleChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="billing_last_name"
                  label="Last Name"
                  value={order.billing_last_name}
                  onChange={handleChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="billing_company"
                  label="Company (Optional)"
                  value={order.billing_company || ''}
                  onChange={handleChange}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="billing_address_line1"
                  label="Address Line 1"
                  value={order.billing_address_line1}
                  onChange={handleChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="billing_address_line2"
                  label="Address Line 2 (Optional)"
                  value={order.billing_address_line2 || ''}
                  onChange={handleChange}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="billing_city"
                  label="City"
                  value={order.billing_city}
                  onChange={handleChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="billing_state"
                  label="State/Province (Optional)"
                  value={order.billing_state || ''}
                  onChange={handleChange}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="billing_postal_code"
                  label="Postal Code"
                  value={order.billing_postal_code}
                  onChange={handleChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="billing_country"
                  label="Country"
                  value={order.billing_country}
                  onChange={handleChange}
                  fullWidth
                  required
                />
              </Grid>
            </Grid>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Shipping Address</Typography>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={sameAsBilling}
                    onChange={handleSameAsBillingChange}
                  />
                }
                label="Same as billing address"
              />
            </Box>

            {!sameAsBilling && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="shipping_first_name"
                    label="First Name"
                    value={order.shipping_first_name}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="shipping_last_name"
                    label="Last Name"
                    value={order.shipping_last_name}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    name="shipping_company"
                    label="Company (Optional)"
                    value={order.shipping_company || ''}
                    onChange={handleChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    name="shipping_address_line1"
                    label="Address Line 1"
                    value={order.shipping_address_line1}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    name="shipping_address_line2"
                    label="Address Line 2 (Optional)"
                    value={order.shipping_address_line2 || ''}
                    onChange={handleChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="shipping_city"
                    label="City"
                    value={order.shipping_city}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="shipping_state"
                    label="State/Province (Optional)"
                    value={order.shipping_state || ''}
                    onChange={handleChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="shipping_postal_code"
                    label="Postal Code"
                    value={order.shipping_postal_code}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="shipping_country"
                    label="Country"
                    value={order.shipping_country}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>
              </Grid>
            )}
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Order Items</Typography>
              <Button
                startIcon={<AddIcon />}
                variant="outlined"
                onClick={handleAddItem}
              >
                Add Item
              </Button>
            </Box>

            {(order.items || []).length === 0 ? (
              <Typography color="textSecondary">No items added yet</Typography>
            ) : (
              order.items?.map((item, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={4}>
                      <TextField
                        select
                        label="Product"
                        value={item.product}
                        onChange={(e) => handleItemChange(index, 'product', e.target.value)}
                        fullWidth
                        required
                      >
                        {products.map((product) => (
                          <MenuItem key={product.id} value={product.id}>
                            {product.name}
                          </MenuItem>
                        ))}
                      </TextField>
                    </Grid>
                    <Grid item xs={6} sm={2}>
                      <TextField
                        type="number"
                        label="Price"
                        value={item.price}
                        onChange={(e) => handleItemChange(index, 'price', e.target.value)}
                        fullWidth
                        required
                        InputProps={{
                          startAdornment: <Typography className="mr-1">$</Typography>,
                        }}
                      />
                    </Grid>
                    <Grid item xs={6} sm={2}>
                      <TextField
                        type="number"
                        label="Quantity"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                        fullWidth
                        required
                        inputProps={{ min: 1 }}
                      />
                    </Grid>
                    <Grid item xs={6} sm={2}>
                      <TextField
                        type="number"
                        label="Discount"
                        value={item.discount_amount || 0}
                        onChange={(e) => handleItemChange(index, 'discount_amount', e.target.value)}
                        fullWidth
                        InputProps={{
                          startAdornment: <Typography className="mr-1">$</Typography>,
                        }}
                      />
                    </Grid>
                    <Grid item xs={6} sm={1}>
                      <Typography variant="body1" align="right">
                        ${((item.price * item.quantity) - (item.discount_amount || 0)).toFixed(2)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={1}>
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </Box>
              ))
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Order Details</Typography>

            <TextField
              select
              name="payment_method"
              label="Payment Method"
              value={order.payment_method}
              onChange={handleChange}
              fullWidth
              margin="normal"
              required
            >
              <MenuItem value="credit_card">Credit Card</MenuItem>
              <MenuItem value="paypal">PayPal</MenuItem>
              <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
              <MenuItem value="cash_on_delivery">Cash on Delivery</MenuItem>
              <MenuItem value="other">Other</MenuItem>
            </TextField>

            <TextField
              name="payment_reference"
              label="Payment Reference"
              value={order.payment_reference || ''}
              onChange={handleChange}
              fullWidth
              margin="normal"
            />

            {isEditMode && (
              <TextField
                select
                name="status"
                label="Order Status"
                value={order.status || 'pending'}
                onChange={handleChange}
                fullWidth
                margin="normal"
              >
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="processing">Processing</MenuItem>
                <MenuItem value="shipped">Shipped</MenuItem>
                <MenuItem value="delivered">Delivered</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
                <MenuItem value="refunded">Refunded</MenuItem>
                <MenuItem value="partially_refunded">Partially Refunded</MenuItem>
                <MenuItem value="on_hold">On Hold</MenuItem>
              </TextField>
            )}

            <TextField
              name="customer_notes"
              label="Customer Notes"
              value={order.customer_notes || ''}
              onChange={handleChange}
              fullWidth
              margin="normal"
              multiline
              rows={3}
            />
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Shipping</Typography>

            {shippingMethods.length > 0 ? (
              <TextField
                select
                label="Shipping Method"
                value={shippingMethods.find(m => m.price === order.shipping_cost)?.id || ''}
                onChange={handleShippingMethodChange}
                fullWidth
                margin="normal"
              >
                {shippingMethods.map((method) => (
                  <MenuItem key={method.id} value={method.id}>
                    {method.name} - ${parseFloat(method.price.toString()).toFixed(2)}
                  </MenuItem>
                ))}
              </TextField>
            ) : (
              <Typography color="textSecondary">No shipping methods available</Typography>
            )}

            <TextField
              type="number"
              name="shipping_cost"
              label="Shipping Cost"
              value={order.shipping_cost}
              onChange={handleChange}
              fullWidth
              margin="normal"
              InputProps={{
                startAdornment: <Typography className="mr-1">$</Typography>,
              }}
            />

            {isEditMode && (
              <>
                <TextField
                  name="tracking_number"
                  label="Tracking Number"
                  value={order.tracking_number || ''}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                />

                <TextField
                  name="shipping_carrier"
                  label="Shipping Carrier"
                  value={order.shipping_carrier || ''}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                />
              </>
            )}
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Order Summary</Typography>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>Subtotal:</Typography>
              <Typography>${parseFloat(order.subtotal.toString()).toFixed(2)}</Typography>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>Shipping:</Typography>
              <Typography>${parseFloat(order.shipping_cost.toString()).toFixed(2)}</Typography>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>Tax:</Typography>
              <TextField
                type="number"
                name="tax_amount"
                value={order.tax_amount}
                onChange={handleChange}
                size="small"
                sx={{ width: 100 }}
                InputProps={{
                  startAdornment: <Typography className="mr-1">$</Typography>,
                }}
              />
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>Discount:</Typography>
              <Typography>${parseFloat(order.discount_amount.toString()).toFixed(2)}</Typography>
            </Box>

            <Divider className="my-4" />

            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="h6">Total:</Typography>
              <Typography variant="h6">${parseFloat(order.total.toString()).toFixed(2)}</Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </form>
  );
};

export default OrderForm;
