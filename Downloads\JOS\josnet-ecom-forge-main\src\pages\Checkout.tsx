import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CreditCard, Landmark, Mail, Truck, ShieldCheck, ArrowLeft } from "lucide-react";
import { useCart } from "@/contexts/CartContext";
import { toast } from "@/hooks/use-toast";

const Checkout = () => {
  const { cartItems, cartTotal, clearCart } = useCart();
  const [paymentMethod, setPaymentMethod] = useState("card");
  const [step, setStep] = useState(1);
  const navigate = useNavigate();
  
  // Calculate totals
  const subtotal = cartTotal;
  const shippingCost = subtotal > 100 ? 0 : 15;  // Renamed from shipping to shippingCost
  const total = subtotal + shippingCost;

  // Form states
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    province: "",
    postcode: "",
    notes: "",
    cardName: "",
    cardNumber: "",
    expiry: "",
    cvc: ""
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  // Form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 1) {
      // Move to payment step
      setStep(2);
      window.scrollTo(0, 0);
    } else {
      // Process order
      const orderId = "CMD-" + Math.floor(100000 + Math.random() * 900000);
      const orderData = {
        id: orderId,
        date: new Date(),
        items: cartItems,
        shipping: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address: formData.address,
          city: formData.city,
          province: formData.province,
          postcode: formData.postcode
        },
        paymentMethod,
        subtotal,
        shippingCost,  // Renamed from shipping to shippingCost
        total
      };
      
      // In a real app, this would send the order to a backend
      localStorage.setItem("lastOrder", JSON.stringify(orderData));
      
      // Show success message
      toast({
        title: "Commande confirmée!",
        description: `Votre commande #${orderId} a été enregistrée avec succès.`,
      });
      
      // Clear the cart after successful checkout
      clearCart();
      
      // Redirect to confirmation page
      navigate(`/order-confirmation/${orderId}`);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Checkout Header */}
        <div className="mb-6">
          <Link to={step === 1 ? "/cart" : "#"} 
                onClick={(e) => {
                  if (step === 2) {
                    e.preventDefault();
                    setStep(1);
                    window.scrollTo(0, 0);
                  }
                }} 
                className="text-gray-600 hover:text-primary flex items-center">
            <ArrowLeft size={16} className="mr-2" />
            {step === 1 ? "Retour au panier" : "Retour aux informations de livraison"}
          </Link>
          <h1 className="text-3xl font-bold mt-4">Finaliser votre commande</h1>
        </div>
        
        {/* Checkout Progress */}
        <div className="mb-8">
          <div className="flex items-center">
            <div className={`rounded-full h-8 w-8 flex items-center justify-center ${
              step >= 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              1
            </div>
            <div className={`h-1 flex-grow mx-2 ${
              step >= 2 ? 'bg-primary' : 'bg-gray-200'
            }`}></div>
            <div className={`rounded-full h-8 w-8 flex items-center justify-center ${
              step >= 2 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              2
            </div>
            <div className={`h-1 flex-grow mx-2 bg-gray-200`}></div>
            <div className="rounded-full h-8 w-8 flex items-center justify-center bg-gray-200 text-gray-600">
              3
            </div>
          </div>
          <div className="flex justify-between mt-2 text-sm">
            <span className={step >= 1 ? 'text-primary font-medium' : 'text-gray-600'}>Livraison</span>
            <span className={step >= 2 ? 'text-primary font-medium' : 'text-gray-600'}>Paiement</span>
            <span className="text-gray-600">Confirmation</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            {step === 1 ? (
              /* Step 1: Shipping Information */
              <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-medium mb-6">Informations de livraison</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium mb-1">
                      Prénom*
                    </label>
                    <Input
                      id="firstName"
                      placeholder="Jean"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium mb-1">
                      Nom*
                    </label>
                    <Input
                      id="lastName"
                      placeholder="Dupont"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                
                <div className="mb-4">
                  <label htmlFor="email" className="block text-sm font-medium mb-1">
                    Email*
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="phone" className="block text-sm font-medium mb-1">
                    Téléphone*
                  </label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+257 XX XX XX XX"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="address" className="block text-sm font-medium mb-1">
                    Adresse*
                  </label>
                  <Input
                    id="address"
                    placeholder="123 Rue Example"
                    value={formData.address}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium mb-1">
                      Ville*
                    </label>
                    <Input
                      id="city"
                      placeholder="Bujumbura"
                      value={formData.city}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="province" className="block text-sm font-medium mb-1">
                      Province*
                    </label>
                    <Input
                      id="province"
                      placeholder="Province"
                      value={formData.province}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="postcode" className="block text-sm font-medium mb-1">
                      Code Postal
                    </label>
                    <Input
                      id="postcode"
                      placeholder="Code postal"
                      value={formData.postcode}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="mb-6">
                  <label htmlFor="notes" className="block text-sm font-medium mb-1">
                    Instructions de livraison (facultatif)
                  </label>
                  <textarea
                    id="notes"
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="Instructions spéciales pour la livraison..."
                    rows={3}
                    value={formData.notes}
                    onChange={handleInputChange}
                  ></textarea>
                </div>
                
                <Button type="submit" className="w-full">
                  Continuer vers le paiement
                </Button>
              </form>
            ) : (
              /* Step 2: Payment Information */
              <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-medium mb-6">Mode de paiement</h2>
                
                <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="mb-6">
                  <div className="flex items-center space-x-2 border border-gray-200 rounded-lg p-4 mb-3">
                    <RadioGroupItem value="card" id="card" />
                    <Label htmlFor="card" className="flex items-center">
                      <CreditCard size={18} className="mr-2" />
                      Carte bancaire
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 border border-gray-200 rounded-lg p-4 mb-3">
                    <RadioGroupItem value="bank" id="bank" />
                    <Label htmlFor="bank" className="flex items-center">
                      <Landmark size={18} className="mr-2" />
                      Virement bancaire
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 border border-gray-200 rounded-lg p-4 mb-3">
                    <RadioGroupItem value="mobile" id="mobile" />
                    <Label htmlFor="mobile" className="flex items-center">
                      <Mail size={18} className="mr-2" />
                      Mobile Money
                    </Label>
                  </div>
                </RadioGroup>
                
                {paymentMethod === "card" && (
                  <div className="space-y-4 mb-6 p-4 bg-gray-50 rounded-lg">
                    <div>
                      <label htmlFor="cardName" className="block text-sm font-medium mb-1">
                        Nom sur la carte*
                      </label>
                      <Input
                        id="cardName"
                        placeholder="Jean Dupont"
                        value={formData.cardName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="cardNumber" className="block text-sm font-medium mb-1">
                        Numéro de carte*
                      </label>
                      <Input
                        id="cardNumber"
                        placeholder="1234 5678 9012 3456"
                        value={formData.cardNumber}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="expiry" className="block text-sm font-medium mb-1">
                          Date d'expiration*
                        </label>
                        <Input
                          id="expiry"
                          placeholder="MM / YY"
                          value={formData.expiry}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div>
                        <label htmlFor="cvc" className="block text-sm font-medium mb-1">
                          CVC*
                        </label>
                        <Input
                          id="cvc"
                          placeholder="123"
                          value={formData.cvc}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                    </div>
                  </div>
                )}
                
                {paymentMethod === "bank" && (
                  <div className="space-y-4 mb-6 p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-700">
                      Veuillez effectuer un virement bancaire vers le compte suivant:
                    </p>
                    <div className="bg-white border border-gray-200 rounded p-3">
                      <p className="font-medium">JOSNET NETWORK</p>
                      <p>Banque: Exemple Banque</p>
                      <p>IBAN: BU123456789</p>
                      <p>BIC: EXAMPLEXXX</p>
                      <p className="mt-2 text-sm text-gray-600">
                        Veuillez inclure votre numéro de commande comme référence.
                      </p>
                    </div>
                  </div>
                )}
                
                {paymentMethod === "mobile" && (
                  <div className="space-y-4 mb-6 p-4 bg-gray-50 rounded-lg">
                    <div>
                      <label htmlFor="mobileNumber" className="block text-sm font-medium mb-1">
                        Numéro de téléphone*
                      </label>
                      <Input
                        id="mobileNumber"
                        placeholder="+257 XX XX XX XX"
                        required
                      />
                    </div>
                    <p className="text-sm text-gray-600">
                      Vous recevrez un code de vérification sur ce numéro pour compléter le paiement.
                    </p>
                  </div>
                )}
                
                <div className="flex items-center mb-6">
                  <input
                    type="checkbox"
                    id="terms"
                    className="h-4 w-4 mr-2"
                    required
                  />
                  <label htmlFor="terms" className="text-sm">
                    J'accepte les <Link to="/terms" className="text-primary hover:underline">Conditions Générales de Vente</Link> et la <Link to="/privacy" className="text-primary hover:underline">Politique de Confidentialité</Link>.
                  </label>
                </div>
                
                <Button type="submit" className="w-full">
                  Confirmer la commande
                </Button>
              </form>
            )}
          </div>
          
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h2 className="font-medium text-lg mb-4 pb-2 border-b border-gray-200">
                Récapitulatif de la commande
              </h2>
              
              {/* Product list */}
              <ul className="max-h-80 overflow-y-auto mb-4 pb-4 border-b border-gray-200">
                {cartItems.map((item) => (
                  <li key={item.product.id} className="flex py-2">
                    <div className="w-16 h-16 flex-shrink-0 mr-4">
                      <img 
                        src={item.product.image} 
                        alt={item.product.name} 
                        className="w-full h-full object-cover rounded"
                      />
                    </div>
                    <div className="flex-grow">
                      <h3 className="font-medium line-clamp-1">{item.product.name}</h3>
                      <p className="text-gray-500 text-sm">Qté: {item.quantity}</p>
                      <p className="font-bold">
                        {(item.product.price * item.quantity).toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}
                      </p>
                    </div>
                  </li>
                ))}
              </ul>
              
              {/* Price breakdown */}
              <div className="space-y-2 pb-4 border-b border-gray-200">
                <div className="flex justify-between">
                  <span className="text-gray-600">Sous-total</span>
                  <span>{subtotal.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Frais de livraison</span>
                  <span>{shippingCost === 0 ? "Gratuit" : shippingCost.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}</span>
                </div>
              </div>
              
              {/* Total */}
              <div className="py-4">
                <div className="flex justify-between font-bold text-lg">
                  <span>Total</span>
                  <span>{total.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}</span>
                </div>
              </div>
              
              {/* Additional info */}
              <div className="mt-4 pt-4 border-t border-gray-200 space-y-3">
                <div className="flex">
                  <Truck size={18} className="text-primary mr-2 flex-shrink-0" />
                  <p className="text-sm">
                    Livraison estimée: <span className="font-medium">3-5 jours ouvrables</span>
                  </p>
                </div>
                <div className="flex">
                  <ShieldCheck size={18} className="text-primary mr-2 flex-shrink-0" />
                  <p className="text-sm">
                    Paiement sécurisé et données protégées
                  </p>
                </div>
              </div>
            </div>
            
            {/* Need help box */}
            <div className="mt-4 bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h3 className="font-medium mb-2">Besoin d'aide?</h3>
              <p className="text-sm text-gray-600 mb-3">
                Notre équipe de support est disponible pour vous aider.
              </p>
              <div className="flex justify-between">
                <Button variant="outline" asChild size="sm">
                  <Link to="/contact">Contacter le support</Link>
                </Button>
                <Button variant="outline" asChild size="sm">
                  <Link to="/faq">FAQ</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Checkout;
