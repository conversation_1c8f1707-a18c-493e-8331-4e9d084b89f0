import { useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Input } from "@/components/ui/input";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { HelpCircle, Search, ShoppingCart, Package, CreditCard, RefreshCw, Truck, Headphones, ShieldCheck } from "lucide-react";
import { formatCurrency } from '@/utils/currency';

// FAQ categories and questions
const faqCategories = [
  {
    id: "general",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    icon: HelpCircle,
    questions: [
      {
        id: "what-is-josnet",
        question: "Qu'est-ce que JOSNET NETWORK ?",
        answer: "JOSNET NETWORK est une entreprise spécialisée dans la fourniture de solutions IT et de télécommunications au Burundi. Nous proposons une gamme complète de produits technologiques de haute qualité pour les particuliers et les entreprises."
      },
      {
        id: "shipping-areas",
        question: "Où livrez-vous ?",
        answer: "Nous livrons partout au Burundi. Pour les commandes internationales, veuillez nous contacter directement pour discuter des options de livraison disponibles."
      },
      {
        id: "customer-support",
        question: "Comment contacter le service client ?",
        answer: "Vous pouvez contacter notre service client par téléphone au +257 XX XX XX XX, par email à <EMAIL>, ou en utilisant le formulaire de contact sur notre site. Nous sommes disponibles du lundi au vendredi de 8h00 à 18h00, et le samedi de 9h00 à 13h00."
      }
    ]
  },
  {
    id: "orders",
    name: "Commandes",
    icon: ShoppingCart,
    questions: [
      {
        id: "order-process",
        question: "Comment passer une commande ?",
        answer: "Passer une commande est simple ! Parcourez notre catalogue, ajoutez les produits désirés à votre panier, puis suivez les étapes du processus de paiement. Vous recevrez une confirmation par email une fois votre commande validée."
      },
      {
        id: "order-modification",
        question: "Puis-je modifier ma commande après l'avoir passée ?",
        answer: "Vous pouvez modifier votre commande dans l'heure qui suit sa validation. Pour cela, contactez notre service client dès que possible. Une fois que la commande est en cours de traitement, les modifications ne sont plus possibles."
      },
      {
        id: "order-cancel",
        question: "Comment annuler ma commande ?",
        answer: "Pour annuler votre commande, contactez notre service client immédiatement. Les commandes peuvent être annulées gratuitement avant qu'elles ne soient expédiées. Des frais peuvent s'appliquer si la commande a déjà été préparée pour l'expédition."
      }
    ]
  },
  {
    id: "shipping",
    name: "Livraison",
    icon: Truck,
    questions: [
      {
        id: "shipping-time",
        question: "Combien de temps faut-il pour recevoir ma commande ?",
        answer: "Le délai de livraison standard est de 3 à 5 jours ouvrables pour les commandes à Bujumbura, et de 5 à 7 jours ouvrables pour le reste du pays. Certains produits peuvent avoir des délais différents, qui seront indiqués lors de l'achat."
      },
      {
        id: "shipping-cost",
        question: "Combien coûte la livraison ?",
                answer: <>Les frais de livraison dépendent de la taille et du poids de votre commande, ainsi que de votre emplacement. La livraison est gratuite pour toutes les commandes supérieures à {formatCurrency(100000)}. Les frais exacts sont calculés lors du processus de paiement.</>
      },
      {
        id: "order-tracking",
        question: "Comment suivre ma commande ?",
        answer: "Une fois votre commande expédiée, vous recevrez un email contenant un numéro de suivi. Vous pouvez utiliser ce numéro sur notre page 'Suivi de commande' ou contacter notre service client pour obtenir des informations actualisées sur la livraison."
      }
    ]
  },
  {
    id: "payment",
    name: "Paiement",
    icon: CreditCard,
    questions: [
      {
        id: "payment-methods",
        question: "Quels modes de paiement acceptez-vous ?",
        answer: "Nous acceptons plusieurs modes de paiement : cartes de crédit/débit (Visa, MasterCard), virements bancaires et Mobile Money. Tous les paiements sont traités de manière sécurisée."
      },
      {
        id: "payment-security",
        question: "Mes informations de paiement sont-elles sécurisées ?",
        answer: "Absolument ! Nous utilisons des protocoles de sécurité de pointe pour protéger vos informations personnelles et financières. Toutes les transactions sont cryptées et nous ne stockons pas les détails complets de votre carte."
      },
      {
        id: "invoice",
        question: "Comment obtenir une facture pour ma commande ?",
        answer: "Une facture électronique est automatiquement envoyée à l'adresse email que vous avez fournie lors de la commande. Si vous avez besoin d'une facture papier ou d'un format spécifique, veuillez contacter notre service client."
      }
    ]
  },
  {
    id: "returns",
    name: "Retours",
    icon: RefreshCw,
    questions: [
      {
        id: "return-policy",
        question: "Quelle est votre politique de retour ?",
        answer: "Vous pouvez retourner un produit dans les 30 jours suivant la réception si vous n'êtes pas satisfait. Le produit doit être dans son état d'origine, non utilisé et dans son emballage d'origine. Certains produits, comme les logiciels activés, ne peuvent pas être retournés."
      },
      {
        id: "return-process",
        question: "Comment retourner un produit ?",
        answer: "Pour retourner un produit, contactez d'abord notre service client pour obtenir un numéro d'autorisation de retour (RMA). Emballez soigneusement le produit avec tous ses accessoires et incluez le RMA. Les frais de retour sont à la charge du client sauf en cas de produit défectueux."
      },
      {
        id: "refund-time",
        question: "Combien de temps faut-il pour recevoir un remboursement ?",
        answer: "Une fois que nous avons reçu et inspecté le produit retourné, le remboursement est traité dans un délai de 5 à 7 jours ouvrables. Le temps nécessaire pour que l'argent apparaisse sur votre compte dépend de votre institution financière."
      }
    ]
  },
  {
    id: "warranty",
    name: "Garantie",
    icon: ShieldCheck,
    questions: [
      {
        id: "warranty-coverage",
        question: "Quelle garantie offrez-vous sur vos produits ?",
        answer: "Tous nos produits sont couverts par une garantie d'au moins 12 mois contre les défauts de fabrication. Certains produits peuvent avoir des garanties plus longues, comme indiqué dans leur description."
      },
      {
        id: "warranty-claim",
        question: "Comment faire une réclamation sous garantie ?",
        answer: "Pour faire une réclamation sous garantie, contactez notre service client avec votre preuve d'achat et une description du problème. Notre équipe vous guidera à travers les étapes nécessaires pour la réparation ou le remplacement du produit."
      },
      {
        id: "extend-warranty",
        question: "Puis-je prolonger la garantie de mon produit ?",
        answer: "Oui, pour certains produits, nous proposons des extensions de garantie qui peuvent être achetées au moment de l'achat ou dans les 30 jours suivant. Contactez notre équipe commerciale pour plus d'informations."
      }
    ]
  },
  {
    id: "technical",
    name: "Support technique",
    icon: Headphones,
    questions: [
      {
        id: "tech-support",
        question: "Comment obtenir de l'aide technique pour un produit ?",
        answer: "Notre équipe de support technique est disponible par téléphone, email ou chat en direct pendant les heures d'ouverture. Vous pouvez également consulter nos guides d'utilisation et tutoriels en ligne pour des solutions rapides à des problèmes courants."
      },
      {
        id: "product-installation",
        question: "Proposez-vous des services d'installation ?",
        answer: "Oui, nous offrons des services d'installation professionnelle pour la plupart de nos produits, en particulier pour les solutions réseau et les systèmes plus complexes. Ces services sont disponibles à un coût supplémentaire qui varie selon le produit et la complexité de l'installation."
      },
      {
        id: "training",
        question: "Offrez-vous des formations pour vos produits ?",
        answer: "Nous proposons des sessions de formation pour certains produits et solutions, particulièrement pour les clients professionnels. Ces formations peuvent être individuelles ou en groupe, et peuvent être adaptées à vos besoins spécifiques."
      }
    ]
  }
];

const FAQ = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");
  const [expandedQuestions, setExpandedQuestions] = useState<string[]>([]);

  // Filter questions based on search term and active category
  const filteredQuestions = faqCategories.flatMap(category => {
    // Skip categories that don't match the active filter (unless "all" is selected)
    if (activeCategory !== "all" && category.id !== activeCategory) {
      return [];
    }
    
    // Filter questions based on search term
    return category.questions.filter(question => {
      const searchContent = `${question.question} ${question.answer}`.toLowerCase();
      return !searchTerm || searchContent.includes(searchTerm.toLowerCase());
    }).map(question => ({
      ...question,
      category: category.name
    }));
  });

  // Toggle question expansion
  const toggleQuestion = (questionId: string) => {
    setExpandedQuestions(prev => 
      prev.includes(questionId) 
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero section */}
        <section className="bg-primary-dark text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Foire Aux Questions</h1>
            <p className="text-xl max-w-2xl mx-auto mb-8">
              Trouvez rapidement des réponses aux questions les plus fréquemment posées.
            </p>
            
            {/* Search bar */}
            <div className="max-w-xl mx-auto relative">
              <Input
                type="text"
                placeholder="Rechercher une question..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 py-6 bg-white/90 border-0 text-gray-800"
              />
              <Search className="absolute left-3 top-3 text-gray-500" />
            </div>
          </div>
        </section>
        
        {/* FAQ Content */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Category sidebar */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 sticky top-24">
                  <h2 className="p-4 border-b border-gray-200 font-medium">Catégories</h2>
                  <ul>
                    <li>
                      <button
                        onClick={() => setActiveCategory("all")}
                        className={`w-full text-left px-4 py-3 flex items-center ${
                          activeCategory === "all" 
                            ? "bg-primary-dark text-white" 
                            : "hover:bg-gray-50"
                        }`}
                      >
                        <HelpCircle size={18} className="mr-2" />
                        Toutes les questions
                      </button>
                    </li>
                    
                    {faqCategories.map((category) => (
                      <li key={category.id}>
                        <button
                          onClick={() => setActiveCategory(category.id)}
                          className={`w-full text-left px-4 py-3 flex items-center ${
                            activeCategory === category.id 
                              ? "bg-primary-dark text-white" 
                              : "hover:bg-gray-50"
                          }`}
                        >
                          <category.icon size={18} className="mr-2" />
                          {category.name}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              {/* Questions and answers */}
              <div className="lg:col-span-3">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="p-6">
                    <h2 className="text-2xl font-bold mb-6">
                      {activeCategory === "all" 
                        ? "Toutes les questions fréquentes" 
                        : faqCategories.find(cat => cat.id === activeCategory)?.name || ""
                      }
                    </h2>
                    
                    {searchTerm && (
                      <p className="mb-6 text-gray-600">
                        {filteredQuestions.length} résultat(s) pour "{searchTerm}"
                      </p>
                    )}
                    
                    {filteredQuestions.length > 0 ? (
                      <Accordion type="multiple" value={expandedQuestions} className="space-y-4">
                        {filteredQuestions.map((question) => (
                          <AccordionItem
                            key={question.id}
                            value={question.id}
                            className="border border-gray-200 rounded-md overflow-hidden"
                          >
                            <AccordionTrigger
                              onClick={() => toggleQuestion(question.id)}
                              className="px-4 py-3 hover:bg-gray-50"
                            >
                              <div className="text-left">
                                <h3 className="font-medium">{question.question}</h3>
                                {activeCategory === "all" && (
                                  <span className="text-xs text-gray-500 mt-1 block">
                                    {question.category}
                                  </span>
                                )}
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 py-3 bg-gray-50 text-gray-700">
                              <p>{question.answer}</p>
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    ) : (
                      <div className="text-center py-8">
                        <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-medium mb-2">Aucun résultat trouvé</h3>
                        <p className="text-gray-600">
                          Essayez de modifier votre recherche ou parcourez les catégories pour trouver ce que vous cherchez.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Contact box */}
                <div className="mt-8 bg-gray-50 rounded-lg border border-gray-200 p-6 text-center">
                  <h3 className="text-lg font-medium mb-2">Vous n'avez pas trouvé de réponse à votre question ?</h3>
                  <p className="text-gray-600 mb-4">
                    Notre équipe de support client est là pour vous aider.
                  </p>
                  <a
                    href="/contact"
                    className="inline-block bg-primary text-white px-6 py-2 rounded hover:bg-primary-dark transition-colors"
                  >
                    Contactez-nous
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FAQ;
