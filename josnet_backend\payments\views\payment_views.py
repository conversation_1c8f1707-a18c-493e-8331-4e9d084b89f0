from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.http import HttpResponse
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from orders.models import Order
from authentication.permissions import IsStaffOrAdmin, IsOwnerOrStaff

from payments.models import PaymentMethod, Transaction, Invoice, CustomerPaymentMethod
from payments.serializers import (
    PaymentMethodSerializer, PaymentMethodDetailSerializer,
    TransactionSerializer, TransactionDetailSerializer,
    InvoiceSerializer, InvoiceDetailSerializer,
    CustomerPaymentMethodSerializer, CustomerPaymentMethodCreateSerializer
)

from payments.services import stripe_service, paypal_service, invoice_service


class PaymentMethodViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing payment methods.
    """
    queryset = PaymentMethod.objects.all()
    serializer_class = PaymentMethodSerializer
    permission_classes = [IsStaffOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['provider', 'is_active', 'is_default']
    search_fields = ['name', 'description']
    ordering_fields = ['position', 'name', 'created_at']
    ordering = ['position', 'name']

    def get_serializer_class(self):
        if self.action in ['retrieve', 'create', 'update', 'partial_update']:
            return PaymentMethodDetailSerializer
        return PaymentMethodSerializer

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def active(self, request):
        """Get all active payment methods."""
        payment_methods = PaymentMethod.objects.filter(is_active=True)
        serializer = self.get_serializer(payment_methods, many=True)
        return Response(serializer.data)


class TransactionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing payment transactions.
    """
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    permission_classes = [IsOwnerOrStaff]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['order', 'payment_method', 'status', 'currency']
    search_fields = ['transaction_id', 'error_message']
    ordering_fields = ['created_at', 'completed_at', 'amount']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action in ['retrieve']:
            return TransactionDetailSerializer
        return TransactionSerializer

    def get_queryset(self):
        """Filter transactions based on user permissions."""
        user = self.request.user
        if user.is_staff or user.is_superuser:
            return Transaction.objects.all()
        return Transaction.objects.filter(order__user=user)

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def my_transactions(self, request):
        """Get all transactions for the current user."""
        transactions = Transaction.objects.filter(order__user=request.user)
        page = self.paginate_queryset(transactions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(transactions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsStaffOrAdmin])
    def refund(self, request, pk=None):
        """Refund a transaction."""
        transaction = self.get_object()

        # Check if transaction can be refunded
        if transaction.status not in ['completed']:
            return Response(
                {"detail": "Only completed transactions can be refunded."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get refund amount from request or use full amount
        amount = request.data.get('amount', transaction.amount)
        reason = request.data.get('reason', 'Refund requested')

        # Process refund based on payment method
        if transaction.payment_method.provider == 'stripe':
            result = stripe_service.process_refund(transaction, amount, reason)
        elif transaction.payment_method.provider == 'paypal':
            result = paypal_service.process_refund(transaction, amount, reason)
        else:
            # Manual refund for other payment methods
            result = {
                'success': True,
                'transaction_id': f"refund_{transaction.transaction_id}",
                'message': 'Manual refund processed'
            }

        if result['success']:
            # Create refund transaction
            refund_transaction = Transaction.objects.create(
                order=transaction.order,
                payment_method=transaction.payment_method,
                transaction_id=result.get('transaction_id'),
                amount=-amount,  # Negative amount for refunds
                currency=transaction.currency,
                status='completed',
                completed_at=timezone.now(),
                provider_response=result
            )

            # Update original transaction status
            if amount == transaction.amount:
                transaction.status = 'refunded'
            else:
                transaction.status = 'partially_refunded'
            transaction.save()

            # Update order payment status
            order = transaction.order
            order.payment_status = transaction.status
            order.save()

            serializer = TransactionSerializer(refund_transaction)
            return Response(serializer.data)
        else:
            return Response(
                {"detail": result.get('message', 'Refund failed')},
                status=status.HTTP_400_BAD_REQUEST
            )
