/**
 * Composant dynamique pour afficher les statistiques depuis l'API
 */

import React, { useEffect, useState } from 'react';
import { Users, CheckCircle, Award, Clock, Loader2 } from "lucide-react";
import { useHomePageStats } from '@/hooks/useHomePageData';
import ErrorDisplay from '@/components/ui/ErrorDisplay';

// Animation des compteurs
const useCountUp = (end: number, duration: number = 2000) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [end, duration]);

  return count;
};

interface StatCardProps {
  icon: React.ComponentType<any>;
  value: number;
  label: string;
  suffix?: string;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ icon: Icon, value, label, suffix = '', color }) => {
  const animatedValue = useCountUp(value);

  return (
    <div className="flex flex-col items-center">
      <div className={`w-16 h-16 bg-${color}/20 rounded-full flex items-center justify-center mb-4`}>
        <Icon className={`h-8 w-8 text-${color}`} />
      </div>
      <div className="text-3xl font-bold text-white mb-2">
        {animatedValue.toLocaleString()}{suffix}
      </div>
      <div className="text-gray-400">{label}</div>
    </div>
  );
};

const DynamicStats: React.FC = () => {
  const { stats, loading, error, refetch } = useHomePageStats();

  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            JOSNET en chiffres
          </h2>

          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-300">Chargement des statistiques...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error || !stats) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            JOSNET en chiffres
          </h2>

          <ErrorDisplay
            title="Impossible de charger les statistiques"
            message={error || "Les données statistiques ne sont pas disponibles"}
            onRetry={refetch}
            showServerHint={true}
          />
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="container mx-auto px-4 text-center relative">
        <h2 className="text-4xl md:text-5xl font-bold mb-6">
          JOSNET en chiffres
        </h2>
        <p className="text-xl max-w-3xl mx-auto mb-12 text-gray-300 leading-relaxed">
          Des résultats concrets qui témoignent de notre engagement et de notre expertise au service de nos clients.
        </p>

        {/* Statistiques principales */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          <StatCard
            icon={Users}
            value={stats.clients_count}
            label="Clients satisfaits"
            suffix="+"
            color="primary"
          />
          <StatCard
            icon={CheckCircle}
            value={stats.projects_count}
            label="Projets réalisés"
            suffix="+"
            color="primary"
          />
          <StatCard
            icon={Award}
            value={stats.experience_years}
            label="Années d'expérience"
            suffix="+"
            color="primary"
          />
          <StatCard
            icon={Clock}
            value={stats.team_members}
            label="Experts à votre service"
            color="primary"
          />
        </div>

        {/* Statistiques secondaires */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-2">
              {stats.products_sold.toLocaleString()}+
            </div>
            <div className="text-gray-400">Produits vendus</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-2">
              {stats.cities_served}
            </div>
            <div className="text-gray-400">Villes desservies</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-2">
              {stats.satisfaction_rate}%
            </div>
            <div className="text-gray-400">Taux de satisfaction</div>
          </div>
        </div>

        {/* Support 24/7 */}
        <div className="mt-12 p-6 bg-white/10 rounded-lg backdrop-blur-sm">
          <div className="text-3xl font-bold text-primary mb-2">
            {stats.support_hours}
          </div>
          <div className="text-gray-300">Support technique disponible</div>
        </div>

        {/* Indicateur de données en temps réel */}
        <div className="mt-8 flex items-center justify-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-gray-400">
            Données en temps réel depuis la base de données
          </span>
        </div>

        {/* Dernière mise à jour */}
        {stats.updated_at && (
          <div className="mt-2 text-xs text-gray-500">
            Mise à jour: {new Date(stats.updated_at).toLocaleString('fr-FR')}
          </div>
        )}
      </div>
    </section>
  );
};

export default DynamicStats;
