import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Truck, 
  Package, 
  MapPin, 
  Calendar,
  ExternalLink,
  Save,
  RefreshCw
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import orderApi from '@/services/orderApi';

interface TrackingManagementProps {
  order: any;
  onUpdate?: () => void;
}

const TrackingManagement = ({ order, onUpdate }: TrackingManagementProps) => {
  const [trackingNumber, setTrackingNumber] = useState(order?.tracking_number || '');
  const [shippingCarrier, setShippingCarrier] = useState(order?.shipping_carrier || '');
  const [estimatedDeliveryDate, setEstimatedDeliveryDate] = useState(
    order?.estimated_delivery_date ? order.estimated_delivery_date.split('T')[0] : ''
  );
  const [isUpdating, setIsUpdating] = useState(false);

  const queryClient = useQueryClient();

  // Mutation pour mettre à jour les informations de suivi
  const updateTrackingMutation = useMutation({
    mutationFn: async (data: any) => {
      return orderApi.updateOrderTracking(order.id, data);
    },
    onSuccess: () => {
      toast({
        title: "Suivi mis à jour",
        description: "Les informations de suivi ont été mises à jour avec succès.",
      });
      queryClient.invalidateQueries({ queryKey: ['admin-order', order.id.toString()] });
      onUpdate?.();
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.message || "Impossible de mettre à jour les informations de suivi.",
        variant: "destructive",
      });
    },
  });

  const handleUpdateTracking = async () => {
    setIsUpdating(true);
    try {
      await updateTrackingMutation.mutateAsync({
        tracking_number: trackingNumber.trim() || null,
        shipping_carrier: shippingCarrier.trim() || null,
        estimated_delivery_date: estimatedDeliveryDate || null,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTrackOrder = () => {
    if (trackingNumber) {
      // URLs de suivi pour différents transporteurs
      const trackingUrls: { [key: string]: string } = {
        'DHL': `https://www.dhl.com/tracking?id=${trackingNumber}`,
        'FedEx': `https://www.fedex.com/fedextrack/?tracknumbers=${trackingNumber}`,
        'UPS': `https://www.ups.com/track?tracknum=${trackingNumber}`,
        'TNT': `https://www.tnt.com/express/tracking.do?searchType=con&cons=${trackingNumber}`,
        'La Poste': `https://www.laposte.fr/outils/suivre-vos-envois?code=${trackingNumber}`,
        'Chronopost': `https://www.chronopost.fr/tracking-colis?listeNumerosLT=${trackingNumber}`,
      };

      const url = trackingUrls[shippingCarrier] || `https://www.google.com/search?q=track+package+${trackingNumber}`;
      window.open(url, '_blank');
      
      toast({
        title: "Suivi ouvert",
        description: "Le suivi de la commande a été ouvert dans un nouvel onglet.",
      });
    }
  };

  const carriers = [
    'DHL',
    'FedEx', 
    'UPS',
    'TNT',
    'La Poste',
    'Chronopost',
    'Autre'
  ];

  const getStatusBadge = () => {
    if (!trackingNumber) {
      return <Badge variant="outline" className="bg-gray-100">Pas de suivi</Badge>;
    }
    
    switch (order?.status) {
      case 'shipped':
        return <Badge className="bg-blue-100 text-blue-800">Expédié</Badge>;
      case 'delivered':
        return <Badge className="bg-green-100 text-green-800">Livré</Badge>;
      case 'processing':
        return <Badge className="bg-yellow-100 text-yellow-800">En préparation</Badge>;
      default:
        return <Badge className="bg-orange-100 text-orange-800">En cours</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Truck className="h-5 w-5 text-blue-600" />
          <span>Gestion du Suivi</span>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Informations actuelles */}
        {order?.tracking_number && (
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-3 flex items-center">
              <Package className="h-4 w-4 mr-2" />
              Informations de Suivi Actuelles
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-blue-700 font-medium">Numéro:</span>
                <div className="font-mono bg-white px-2 py-1 rounded border">
                  {order.tracking_number}
                </div>
              </div>
              {order.shipping_carrier && (
                <div>
                  <span className="text-blue-700 font-medium">Transporteur:</span>
                  <div className="bg-white px-2 py-1 rounded border">
                    {order.shipping_carrier}
                  </div>
                </div>
              )}
              {order.estimated_delivery_date && (
                <div>
                  <span className="text-blue-700 font-medium">Livraison estimée:</span>
                  <div className="bg-white px-2 py-1 rounded border">
                    {new Date(order.estimated_delivery_date).toLocaleDateString('fr-FR')}
                  </div>
                </div>
              )}
            </div>
            
            {trackingNumber && (
              <div className="mt-3">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleTrackOrder}
                  className="text-blue-600 border-blue-600 hover:bg-blue-50"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Suivre le colis
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Formulaire de mise à jour */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Mettre à jour les informations de suivi</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="tracking-number">Numéro de suivi</Label>
              <Input
                id="tracking-number"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                placeholder="Ex: 1Z999AA1234567890"
                className="font-mono"
              />
            </div>
            
            <div>
              <Label htmlFor="shipping-carrier">Transporteur</Label>
              <select
                id="shipping-carrier"
                value={shippingCarrier}
                onChange={(e) => setShippingCarrier(e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Sélectionner un transporteur</option>
                {carriers.map((carrier) => (
                  <option key={carrier} value={carrier}>
                    {carrier}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="estimated-delivery">Date de livraison estimée</Label>
            <Input
              id="estimated-delivery"
              type="date"
              value={estimatedDeliveryDate}
              onChange={(e) => setEstimatedDeliveryDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-gray-600">
            <MapPin className="h-4 w-4 inline mr-1" />
            Adresse de livraison: {order?.formatted_shipping_address}
          </div>
          
          <Button
            onClick={handleUpdateTracking}
            disabled={isUpdating || updateTrackingMutation.isPending}
            className="min-w-[120px]"
          >
            {isUpdating || updateTrackingMutation.isPending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Mise à jour...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Sauvegarder
              </>
            )}
          </Button>
        </div>

        {/* Historique des statuts */}
        {order?.status_history && order.status_history.length > 0 && (
          <div className="pt-4 border-t">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Historique des statuts
            </h4>
            <div className="space-y-2">
              {order.status_history.slice(0, 3).map((history: any, index: number) => (
                <div key={index} className="flex items-center justify-between text-sm p-2 bg-gray-50 rounded">
                  <div>
                    <span className="font-medium">{history.status_display || history.status}</span>
                    {history.notes && (
                      <span className="text-gray-600 ml-2">- {history.notes}</span>
                    )}
                  </div>
                  <span className="text-gray-500">
                    {new Date(history.created_at).toLocaleDateString('fr-FR')}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TrackingManagement;
