
import { useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Calendar, User, ArrowRight, Tag } from "lucide-react";
import { Link } from "react-router-dom";
import { Pagination, PaginationContent, PaginationItem, PaginationLink } from "@/components/ui/pagination";

// Mock blog posts data
const blogPosts = [
  {
    id: 1,
    title: "Les tendances technologiques à surveiller en 2025",
    excerpt: "Découvrez les innovations qui vont façonner l'avenir de la technologie et transformer nos vies quotidiennes.",
    image: "https://images.unsplash.com/photo-1607799279861-4dd421887fb3?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8dGVjaCUyMHRyZW5kc3xlbnwwfHwwfHx8MA%3D%3D",
    date: "12 mai 2025",
    author: "Jean Dupont",
    category: "Technologie",
    tags: ["IA", "IoT", "5G", "Tendances"]
  },
  {
    id: 2,
    title: "Comment renforcer la sécurité de votre réseau domestique",
    excerpt: "Protégez vos appareils et vos données personnelles avec ces conseils pratiques pour sécuriser votre réseau Wi-Fi.",
    image: "https://images.unsplash.com/photo-1614064641938-3bbee52942c7?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8bmV0d29yayUyMHNlY3VyaXR5fGVufDB8fDB8fHww",
    date: "5 mai 2025",
    author: "Marie Lambert",
    category: "Cybersécurité",
    tags: ["Sécurité", "Wi-Fi", "Réseau", "Protection"]
  },
  {
    id: 3,
    title: "Les meilleurs outils bureautiques pour le télétravail",
    excerpt: "Maximisez votre productivité à distance avec cette sélection d'outils de collaboration et de gestion de projet.",
    image: "https://images.unsplash.com/photo-1598257006458-087169a1f08d?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8cmVtb3RlJTIwd29ya3xlbnwwfHwwfHx8MA%3D%3D",
    date: "28 avril 2025",
    author: "Thomas Martin",
    category: "Productivité",
    tags: ["Télétravail", "Collaboration", "Outils", "Productivité"]
  },
  {
    id: 4,
    title: "L'importance des solutions de sauvegarde pour votre entreprise",
    excerpt: "Protégez les données critiques de votre entreprise contre les pertes accidentelles et les cyberattaques.",
    image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8ZGF0YSUyMGJhY2t1cHxlbnwwfHwwfHx8MA%3D%3D",
    date: "20 avril 2025",
    author: "Sophie Dubois",
    category: "Entreprise",
    tags: ["Sauvegarde", "Données", "Entreprise", "Sécurité"]
  },
  {
    id: 5,
    title: "Le futur des réseaux 5G au Burundi",
    excerpt: "Analyse des opportunités et défis du déploiement de la 5G pour l'économie numérique du Burundi.",
    image: "https://images.unsplash.com/photo-1621346003993-d95cc9a356cd?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8NWd8ZW58MHx8MHx8fDA%3D",
    date: "15 avril 2025",
    author: "Jean Dupont",
    category: "Télécommunications",
    tags: ["5G", "Burundi", "Télécoms", "Innovation"]
  },
  {
    id: 6,
    title: "Comment choisir le bon ordinateur portable pour vos besoins",
    excerpt: "Guide pratique pour vous aider à faire le meilleur choix en fonction de votre utilisation et de votre budget.",
    image: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8bGFwdG9wfGVufDB8fDB8fHww",
    date: "8 avril 2025",
    author: "Marie Lambert",
    category: "Conseils d'achat",
    tags: ["Ordinateur", "Achat", "Conseils", "Technologie"]
  }
];

// Available categories
const categories = [
  "Tous", 
  "Technologie", 
  "Cybersécurité", 
  "Productivité", 
  "Entreprise", 
  "Télécommunications",
  "Conseils d'achat"
];

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState("Tous");
  
  // Filter posts based on search term and active category
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = 
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = 
      activeCategory === "Tous" || post.category === activeCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero section */}
        <section className="bg-primary-dark text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Blog & Actualités</h1>
            <p className="text-xl max-w-2xl mx-auto mb-8">
              Découvrez nos derniers articles, conseils et actualités sur le monde de la technologie.
            </p>
            
            {/* Search bar */}
            <div className="max-w-xl mx-auto relative">
              <Input
                type="text"
                placeholder="Rechercher un article..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 py-6 bg-white/90 border-0 text-gray-800"
              />
              <Search className="absolute left-3 top-3 text-gray-500" />
            </div>
          </div>
        </section>
        
        {/* Blog Content */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            {/* Category filters */}
            <div className="mb-10 overflow-x-auto">
              <div className="flex space-x-2 pb-2">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={activeCategory === category ? "default" : "outline"}
                    className={`whitespace-nowrap ${
                      activeCategory === category 
                        ? "bg-primary" 
                        : ""
                    }`}
                    onClick={() => setActiveCategory(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Featured post */}
            {filteredPosts.length > 0 && (
              <div className="mb-16">
                <Link to={`/blog/${filteredPosts[0].id}`} className="group block">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 bg-white rounded-lg shadow-sm overflow-hidden">
                    <div className="h-64 lg:h-auto overflow-hidden">
                      <img 
                        src={filteredPosts[0].image} 
                        alt={filteredPosts[0].title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" 
                      />
                    </div>
                    <div className="p-6 lg:p-8 flex flex-col justify-center">
                      <div className="flex items-center text-sm text-gray-500 mb-3">
                        <span className="flex items-center mr-4">
                          <Calendar size={14} className="mr-1" />
                          {filteredPosts[0].date}
                        </span>
                        <span className="flex items-center">
                          <User size={14} className="mr-1" />
                          {filteredPosts[0].author}
                        </span>
                      </div>
                      <h2 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">
                        {filteredPosts[0].title}
                      </h2>
                      <p className="text-gray-600 mb-6">
                        {filteredPosts[0].excerpt}
                      </p>
                      <div className="flex items-center justify-between mt-auto">
                        <div className="flex flex-wrap gap-2">
                          {filteredPosts[0].tags.slice(0, 3).map(tag => (
                            <span key={tag} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                        <Button 
                          variant="link" 
                          className="text-primary group-hover:text-primary-dark flex items-center p-0"
                        >
                          Lire la suite <ArrowRight size={16} className="ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            )}
            
            {/* Blog posts grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.length > 0 ? (
                filteredPosts.slice(1).map(post => (
                  <Card key={post.id} className="overflow-hidden group">
                    <Link to={`/blog/${post.id}`} className="block">
                      <div className="h-48 overflow-hidden">
                        <img 
                          src={post.image} 
                          alt={post.title} 
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                          <span className="flex items-center">
                            <Calendar size={12} className="mr-1" />
                            {post.date}
                          </span>
                          <span className="bg-gray-100 text-primary-dark px-2 py-1 rounded">
                            {post.category}
                          </span>
                        </div>
                        <h3 className="font-bold text-lg mb-2 group-hover:text-primary transition-colors">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">
                          {post.excerpt}
                        </p>
                        <div className="flex items-center justify-between mt-4">
                          <span className="text-xs text-gray-500 flex items-center">
                            <User size={12} className="mr-1" />
                            {post.author}
                          </span>
                          <Button 
                            variant="link" 
                            className="text-primary group-hover:text-primary-dark flex items-center p-0 text-sm"
                          >
                            Lire <ArrowRight size={14} className="ml-1" />
                          </Button>
                        </div>
                      </CardContent>
                    </Link>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <h3 className="text-xl font-medium mb-2">Aucun article trouvé</h3>
                  <p className="text-gray-600">
                    Essayez de modifier votre recherche ou de sélectionner une autre catégorie.
                  </p>
                </div>
              )}
            </div>
            
            {/* Pagination */}
            {filteredPosts.length > 0 && (
              <div className="mt-12">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationLink href="#" aria-label="Previous page">
                        «
                      </PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#" isActive>
                        1
                      </PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#">2</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#">3</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#" aria-label="Next page">
                        »
                      </PaginationLink>
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </div>
        </section>
        
        {/* Newsletter Section */}
        <section className="bg-gray-100 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-xl mx-auto text-center">
              <h2 className="text-2xl md:text-3xl font-bold mb-4">Restez informé</h2>
              <p className="text-gray-600 mb-6">
                Abonnez-vous à notre newsletter pour recevoir les derniers articles et actualités technologiques.
              </p>
              <form className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="email"
                  placeholder="Votre adresse email"
                  className="flex-grow"
                  required
                />
                <Button type="submit">S'abonner</Button>
              </form>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Blog;
