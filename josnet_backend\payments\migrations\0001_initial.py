# Generated by Django 4.2.10 on 2025-05-17 17:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('provider', models.CharField(choices=[('stripe', 'Stripe'), ('paypal', 'PayPal'), ('bank_transfer', 'Virement bancaire'), ('cash_on_delivery', 'Paiement à la livraison'), ('other', 'Autre')], max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.<PERSON><PERSON>anField(default=False)),
                ('config', models.JSONField(blank=True, help_text='Configuration JSON for the payment provider', null=True)),
                ('icon', models.CharField(blank=True, max_length=50, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('position', models.PositiveSmallIntegerField(default=0)),
                ('fee_fixed', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('fee_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['position', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(blank=True, help_text='ID de transaction fourni par la passerelle de paiement', max_length=255, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='EUR', max_length=3)),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('processing', 'En traitement'), ('completed', 'Complété'), ('failed', 'Échoué'), ('refunded', 'Remboursé'), ('partially_refunded', 'Partiellement remboursé'), ('cancelled', 'Annulé')], default='pending', max_length=20)),
                ('provider_response', models.JSONField(blank=True, help_text='Réponse complète de la passerelle de paiement', null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='orders.order')),
                ('payment_method', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transactions', to='payments.paymentmethod')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('status', models.CharField(choices=[('draft', 'Brouillon'), ('issued', 'Émise'), ('paid', 'Payée'), ('cancelled', 'Annulée')], default='draft', max_length=20)),
                ('issue_date', models.DateField(auto_now_add=True)),
                ('due_date', models.DateField(blank=True, null=True)),
                ('paid_date', models.DateField(blank=True, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('shipping_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='invoices/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='orders.order')),
            ],
            options={
                'ordering': ['-issue_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='CustomerPaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(help_text='Token fourni par la passerelle de paiement', max_length=255)),
                ('is_default', models.BooleanField(default=False)),
                ('card_type', models.CharField(blank=True, max_length=50, null=True)),
                ('last_four', models.CharField(blank=True, max_length=4, null=True)),
                ('expiry_month', models.CharField(blank=True, max_length=2, null=True)),
                ('expiry_year', models.CharField(blank=True, max_length=4, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('payment_method', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_methods', to='payments.paymentmethod')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_methods', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-is_default', '-created_at'],
            },
        ),
    ]
