# Generated by Django 4.2.10 on 2025-05-17 16:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0003_relatedproduct'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(editable=False, max_length=50, unique=True)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('billing_first_name', models.Char<PERSON>ield(max_length=100)),
                ('billing_last_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('billing_company', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('billing_address_line1', models.Char<PERSON>ield(max_length=255)),
                ('billing_address_line2', models.CharField(blank=True, max_length=255, null=True)),
                ('billing_city', models.CharField(max_length=100)),
                ('billing_state', models.CharField(blank=True, max_length=100, null=True)),
                ('billing_postal_code', models.CharField(max_length=20)),
                ('billing_country', models.CharField(max_length=100)),
                ('shipping_first_name', models.CharField(max_length=100)),
                ('shipping_last_name', models.CharField(max_length=100)),
                ('shipping_company', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_address_line1', models.CharField(max_length=255)),
                ('shipping_address_line2', models.CharField(blank=True, max_length=255, null=True)),
                ('shipping_city', models.CharField(max_length=100)),
                ('shipping_state', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_postal_code', models.CharField(max_length=20)),
                ('shipping_country', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('processing', 'En traitement'), ('shipped', 'Expédié'), ('delivered', 'Livré'), ('cancelled', 'Annulé'), ('refunded', 'Remboursé'), ('partially_refunded', 'Partiellement remboursé'), ('on_hold', 'En attente de paiement')], default='pending', max_length=20)),
                ('payment_status', models.CharField(choices=[('pending', 'En attente'), ('paid', 'Payé'), ('failed', 'Échoué'), ('refunded', 'Remboursé'), ('partially_refunded', 'Partiellement remboursé')], default='pending', max_length=20)),
                ('payment_method', models.CharField(choices=[('credit_card', 'Carte de crédit'), ('paypal', 'PayPal'), ('bank_transfer', 'Virement bancaire'), ('cash_on_delivery', 'Paiement à la livraison'), ('other', 'Autre')], default='credit_card', max_length=20)),
                ('payment_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10)),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('tracking_number', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_carrier', models.CharField(blank=True, max_length=100, null=True)),
                ('estimated_delivery_date', models.DateField(blank=True, null=True)),
                ('customer_notes', models.TextField(blank=True, null=True)),
                ('admin_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
                ('shipped_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(max_length=255)),
                ('variant_name', models.CharField(blank=True, max_length=255, null=True)),
                ('sku', models.CharField(max_length=100)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('final_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.product')),
                ('variant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.productvariant')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='ShippingMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('estimated_delivery_days', models.PositiveIntegerField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='OrderStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('processing', 'En traitement'), ('shipped', 'Expédié'), ('delivered', 'Livré'), ('cancelled', 'Annulé'), ('refunded', 'Remboursé'), ('partially_refunded', 'Partiellement remboursé'), ('on_hold', 'En attente de paiement')], max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='orders.order')),
            ],
            options={
                'verbose_name_plural': 'Order status histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderRefund',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('refund_number', models.CharField(editable=False, max_length=50, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('approved', 'Approuvé'), ('rejected', 'Rejeté'), ('completed', 'Terminé')], default='pending', max_length=20)),
                ('refund_type', models.CharField(choices=[('full', 'Remboursement complet'), ('partial', 'Remboursement partiel'), ('item', "Remboursement d'article")], max_length=20)),
                ('reason', models.TextField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_refunds', to=settings.AUTH_USER_MODEL)),
                ('items', models.ManyToManyField(blank=True, related_name='refunds', to='orders.orderitem')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refunds', to='orders.order')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_refunds', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
