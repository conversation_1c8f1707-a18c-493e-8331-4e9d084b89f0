from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import OrderViewSet, OrderItemViewSet, OrderRefundViewSet, ShippingMethodViewSet

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'orders', OrderViewSet)
router.register(r'order-items', OrderItemViewSet)
router.register(r'refunds', OrderRefundViewSet)
router.register(r'shipping-methods', ShippingMethodViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
