#!/usr/bin/env python
"""
Script pour créer manuellement les tables CMS manquantes.
"""

import os
import sys
import django
from django.db import connection

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

def create_cms_tables():
    """Créer manuellement les tables CMS."""
    
    cursor = connection.cursor()
    
    # Vérifier quelles tables existent
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'cms_%';")
    existing_tables = [row[0] for row in cursor.fetchall()]
    print(f"Tables CMS existantes: {existing_tables}")
    
    # SQL pour créer la table cms_page si elle n'existe pas
    if 'cms_page' not in existing_tables:
        print("Création de la table cms_page...")
        cursor.execute("""
            CREATE TABLE "cms_page" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "title" varchar(200) NOT NULL,
                "slug" varchar(200) NOT NULL UNIQUE,
                "content" text NOT NULL,
                "template" varchar(50) NOT NULL,
                "status" varchar(20) NOT NULL,
                "meta_description" varchar(255) NOT NULL,
                "meta_keywords" varchar(255) NOT NULL,
                "author_id" bigint NULL REFERENCES "authentication_user" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_page_slug_b8c28e8e" ON "cms_page" ("slug");
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_page_author_id_b3b6b8e8" ON "cms_page" ("author_id");
        """)
        print("✅ Table cms_page créée")
    
    # SQL pour créer la table cms_blogcategory si elle n'existe pas
    if 'cms_blogcategory' not in existing_tables:
        print("Création de la table cms_blogcategory...")
        cursor.execute("""
            CREATE TABLE "cms_blogcategory" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "name" varchar(100) NOT NULL,
                "slug" varchar(100) NOT NULL UNIQUE,
                "description" text NOT NULL
            );
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_blogcategory_slug_c8d28f9f" ON "cms_blogcategory" ("slug");
        """)
        print("✅ Table cms_blogcategory créée")
    
    # SQL pour créer la table cms_blogpost si elle n'existe pas
    if 'cms_blogpost' not in existing_tables:
        print("Création de la table cms_blogpost...")
        cursor.execute("""
            CREATE TABLE "cms_blogpost" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "title" varchar(200) NOT NULL,
                "slug" varchar(200) NOT NULL UNIQUE,
                "content" text NOT NULL,
                "excerpt" text NOT NULL,
                "status" varchar(20) NOT NULL,
                "featured_image" varchar(100) NULL,
                "meta_description" varchar(255) NOT NULL,
                "meta_keywords" varchar(255) NOT NULL,
                "author_id" bigint NULL REFERENCES "authentication_user" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_blogpost_slug_d9e39a0b" ON "cms_blogpost" ("slug");
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_blogpost_author_id_e4f5b6c7" ON "cms_blogpost" ("author_id");
        """)
        print("✅ Table cms_blogpost créée")
    
    # Table de liaison pour les catégories de blog
    if 'cms_blogpost_categories' not in existing_tables:
        print("Création de la table cms_blogpost_categories...")
        cursor.execute("""
            CREATE TABLE "cms_blogpost_categories" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "blogpost_id" bigint NOT NULL REFERENCES "cms_blogpost" ("id") DEFERRABLE INITIALLY DEFERRED,
                "blogcategory_id" bigint NOT NULL REFERENCES "cms_blogcategory" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        
        cursor.execute("""
            CREATE UNIQUE INDEX "cms_blogpost_categories_blogpost_id_blogcategory_id_a1b2c3d4" 
            ON "cms_blogpost_categories" ("blogpost_id", "blogcategory_id");
        """)
        print("✅ Table cms_blogpost_categories créée")
    
    # SQL pour créer la table cms_menu si elle n'existe pas
    if 'cms_menu' not in existing_tables:
        print("Création de la table cms_menu...")
        cursor.execute("""
            CREATE TABLE "cms_menu" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "name" varchar(100) NOT NULL,
                "location" varchar(20) NOT NULL
            );
        """)
        print("✅ Table cms_menu créée")
    
    # SQL pour créer la table cms_menuitem si elle n'existe pas
    if 'cms_menuitem' not in existing_tables:
        print("Création de la table cms_menuitem...")
        cursor.execute("""
            CREATE TABLE "cms_menuitem" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "title" varchar(100) NOT NULL,
                "url" varchar(255) NOT NULL,
                "order" integer unsigned NOT NULL CHECK ("order" >= 0),
                "menu_id" bigint NOT NULL REFERENCES "cms_menu" ("id") DEFERRABLE INITIALLY DEFERRED,
                "page_id" bigint NULL REFERENCES "cms_page" ("id") DEFERRABLE INITIALLY DEFERRED,
                "parent_id" bigint NULL REFERENCES "cms_menuitem" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_menuitem_menu_id_f6g7h8i9" ON "cms_menuitem" ("menu_id");
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_menuitem_page_id_j0k1l2m3" ON "cms_menuitem" ("page_id");
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_menuitem_parent_id_n4o5p6q7" ON "cms_menuitem" ("parent_id");
        """)
        print("✅ Table cms_menuitem créée")
    
    # SQL pour créer la table cms_media si elle n'existe pas
    if 'cms_media' not in existing_tables:
        print("Création de la table cms_media...")
        cursor.execute("""
            CREATE TABLE "cms_media" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "title" varchar(200) NOT NULL,
                "file" varchar(100) NOT NULL,
                "file_type" varchar(20) NOT NULL,
                "description" text NOT NULL,
                "alt_text" varchar(255) NOT NULL,
                "uploader_id" bigint NULL REFERENCES "authentication_user" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_media_uploader_id_r8s9t0u1" ON "cms_media" ("uploader_id");
        """)
        print("✅ Table cms_media créée")
    
    # SQL pour créer la table cms_comment si elle n'existe pas
    if 'cms_comment' not in existing_tables:
        print("Création de la table cms_comment...")
        cursor.execute("""
            CREATE TABLE "cms_comment" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "author_name" varchar(100) NOT NULL,
                "author_email" varchar(254) NOT NULL,
                "content" text NOT NULL,
                "status" varchar(20) NOT NULL,
                "post_id" bigint NOT NULL REFERENCES "cms_blogpost" ("id") DEFERRABLE INITIALLY DEFERRED,
                "user_id" bigint NULL REFERENCES "authentication_user" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_comment_post_id_v2w3x4y5" ON "cms_comment" ("post_id");
        """)
        
        cursor.execute("""
            CREATE INDEX "cms_comment_user_id_z6a7b8c9" ON "cms_comment" ("user_id");
        """)
        print("✅ Table cms_comment créée")
    
    print("\n🎉 Toutes les tables CMS ont été créées avec succès !")
    
    # Vérifier les tables créées
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'cms_%';")
    final_tables = [row[0] for row in cursor.fetchall()]
    print(f"Tables CMS finales: {final_tables}")

if __name__ == '__main__':
    create_cms_tables()
