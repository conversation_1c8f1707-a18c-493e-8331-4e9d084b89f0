import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Eye, 
  Monitor, 
  Smartphone, 
  Tablet, 
  ShoppingCart, 
  Heart, 
  Share2, 
  Star 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';
import { formatImageUrl, handleImageError, getImageUrl } from '@/utils/imageUtils';
import { ProductDetail } from '@/services/productApi';

interface ProductPreviewProps {
  product: Partial<ProductDetail>;
  variant?: 'desktop' | 'tablet' | 'mobile';
}

const ProductPreview: React.FC<ProductPreviewProps> = ({ 
  product, 
  variant = 'desktop' 
}) => {
  const [selectedVariant, setSelectedVariant] = useState<number | null>(null);
  const [activeDevice, setActiveDevice] = useState<'desktop' | 'tablet' | 'mobile'>(variant);
  const [quantity, setQuantity] = useState(1);

  // Calculate current price
  const currentPrice = product.sale_price && product.sale_price < product.price!
    ? product.sale_price
    : product.price;

  // Calculate discount percentage
  const discountPercentage = product.sale_price && product.price
    ? Math.round(((product.price - product.sale_price) / product.price) * 100)
    : 0;

  // Get device width
  const getDeviceWidth = () => {
    switch (activeDevice) {
      case 'mobile':
        return 'w-full max-w-[375px]';
      case 'tablet':
        return 'w-full max-w-[768px]';
      default:
        return 'w-full max-w-[1024px]';
    }
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="bg-gray-50 border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Prévisualisation du produit</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant={activeDevice === 'desktop' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setActiveDevice('desktop')}
              title="Vue bureau"
            >
              <Monitor className="h-4 w-4" />
            </Button>
            <Button
              variant={activeDevice === 'tablet' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setActiveDevice('tablet')}
              title="Vue tablette"
            >
              <Tablet className="h-4 w-4" />
            </Button>
            <Button
              variant={activeDevice === 'mobile' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setActiveDevice('mobile')}
              title="Vue mobile"
            >
              <Smartphone className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <CardDescription>
          Aperçu de l'affichage du produit sur le site
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <div className={`preview-container mx-auto border-x ${getDeviceWidth()}`}>
          <div className="p-4">
            {/* Breadcrumb */}
            <div className="text-sm text-gray-500 mb-4">
              Accueil &gt; {product.categories?.[0]?.name || 'Catégorie'} &gt; {product.name || 'Nom du produit'}
            </div>

            <div className={`grid ${activeDevice === 'mobile' ? 'grid-cols-1' : 'grid-cols-2'} gap-6`}>
              {/* Product Images */}
              <div className="product-images">
                <div className="main-image bg-gray-100 rounded-md overflow-hidden aspect-square flex items-center justify-center">
                  {product.primary_image && typeof product.primary_image !== 'undefined' ? (
                    <img 
                      src={getImageUrl(product.primary_image)} 
                      alt={product.primary_image.alt_text || product.name || 'Image produit'} 
                      className="w-full h-full object-contain"
                      onError={handleImageError}
                    />
                  ) : (
                    <div className="text-gray-400 flex flex-col items-center justify-center h-full">
                      <Eye className="h-12 w-12 mb-2" />
                      <span>Aucune image</span>
                    </div>
                  )}
                </div>
                
                {/* Thumbnails */}
                {product.images && product.images.length > 1 && (
                  <div className="thumbnails grid grid-cols-5 gap-2 mt-2">
                    {product.images.slice(0, 5).map((image, index) => (
                      <div 
                        key={image.id} 
                        className={`aspect-square rounded-md overflow-hidden border-2 ${
                          index === 0 ? 'border-primary' : 'border-transparent'
                        }`}
                      >
                        <img 
                          src={getImageUrl(image)} 
                          alt={image.alt_text || `${product.name || 'Produit'} - Image ${index + 1}`} 
                          className="w-full h-full object-cover"
                          onError={handleImageError}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Product Info */}
              <div className="product-info">
                <h1 className="text-2xl font-bold mb-2">{product.name || 'Nom du produit'}</h1>
                
                {/* Rating */}
                <div className="flex items-center gap-1 mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star 
                      key={star} 
                      className={`h-4 w-4 ${star <= 4 ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
                    />
                  ))}
                  <span className="text-sm text-gray-500 ml-1">(24 avis)</span>
                </div>
                
                {/* Price */}
                <div className="mb-4">
                  {product.sale_price ? (
                    <div className="flex items-center gap-2">
                      <span className="text-2xl font-bold">{formatPrice(currentPrice || 0)}</span>
                      <span className="text-lg text-gray-500 line-through">{formatPrice(product.price || 0)}</span>
                      <Badge className="bg-red-500">-{discountPercentage}%</Badge>
                    </div>
                  ) : (
                    <span className="text-2xl font-bold">{formatPrice(product.price || 0)}</span>
                  )}
                </div>
                
                {/* Short Description */}
                <p className="text-gray-600 mb-6">{product.short_description || 'Description courte du produit...'}</p>
                
                {/* Variants */}
                {product.variants && product.variants.length > 0 && (
                  <div className="variants mb-6">
                    <h3 className="text-sm font-medium mb-2">Options</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.variants.map((variant) => (
                        <Button
                          key={variant.id}
                          variant={selectedVariant === variant.id ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSelectedVariant(variant.id)}
                        >
                          {variant.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Quantity */}
                <div className="quantity mb-6">
                  <h3 className="text-sm font-medium mb-2">Quantité</h3>
                  <div className="flex items-center">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    >
                      -
                    </Button>
                    <span className="w-12 text-center">{quantity}</span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(quantity + 1)}
                    >
                      +
                    </Button>
                  </div>
                </div>
                
                {/* Actions */}
                <div className="actions flex flex-wrap gap-2">
                  <Button className="flex-1 gap-2">
                    <ShoppingCart className="h-4 w-4" />
                    <span>Ajouter au panier</span>
                  </Button>
                  <Button variant="outline" size="icon">
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Product Details Tabs */}
            <div className="mt-8">
              <Tabs defaultValue="description">
                <TabsList className="w-full">
                  <TabsTrigger value="description" className="flex-1">Description</TabsTrigger>
                  <TabsTrigger value="specifications" className="flex-1">Caractéristiques</TabsTrigger>
                  <TabsTrigger value="reviews" className="flex-1">Avis</TabsTrigger>
                </TabsList>
                <TabsContent value="description" className="mt-4">
                  <div 
                    className="prose max-w-none" 
                    dangerouslySetInnerHTML={{ __html: product.description || '<p>Description détaillée du produit...</p>' }} 
                  />
                </TabsContent>
                <TabsContent value="specifications" className="mt-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-gray-50 p-2 rounded">
                      <span className="font-medium">SKU:</span> {product.sku || 'SKU12345'}
                    </div>
                    <div className="bg-gray-50 p-2 rounded">
                      <span className="font-medium">Catégories:</span> {product.categories?.map(c => c.name).join(', ') || 'Non catégorisé'}
                    </div>
                    <div className="bg-gray-50 p-2 rounded">
                      <span className="font-medium">En stock:</span> {product.inventory?.is_in_stock ? 'Oui' : 'Non'}
                    </div>
                    <div className="bg-gray-50 p-2 rounded">
                      <span className="font-medium">Type:</span> {product.is_digital ? 'Produit numérique' : 'Produit physique'}
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="reviews" className="mt-4">
                  <div className="text-center py-8 text-gray-500">
                    {product.reviews && product.reviews.length > 0 ? (
                      <div>Affichage des avis clients...</div>
                    ) : (
                      <div>Aucun avis pour ce produit</div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductPreview;
