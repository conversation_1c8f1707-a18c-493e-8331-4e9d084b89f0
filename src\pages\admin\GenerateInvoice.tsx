import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import paymentApi from '@/services/paymentApi';
import { Loader2 } from 'lucide-react';

const GenerateInvoice: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const [customOrderId, setCustomOrderId] = useState(orderId || '');
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleGenerateInvoice = async () => {
    if (!customOrderId) {
      toast({
        title: "Erreur",
        description: "Veuillez spécifier un ID de commande valide",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const invoice = await paymentApi.generateInvoiceForOrder(Number(customOrderId));
      toast({
        title: "Succès",
        description: `Facture ${invoice.invoice_number} générée avec succès`,
        variant: "default",
      });
      
      // Rediriger vers la page de détail de la facture
      navigate(`/admin/invoices/${invoice.id}`);
    } catch (error: any) {
      console.error('Erreur lors de la génération de la facture:', error);
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Impossible de générer la facture",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Générer une facture</CardTitle>
          <CardDescription>
            Créez une nouvelle facture pour une commande existante
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="orderId">ID de la commande</Label>
              <Input
                id="orderId"
                placeholder="Entrez l'ID de la commande"
                value={customOrderId}
                onChange={(e) => setCustomOrderId(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => navigate('/admin/invoices')}>
            Annuler
          </Button>
          <Button onClick={handleGenerateInvoice} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Génération...
              </>
            ) : (
              'Générer la facture'
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default GenerateInvoice;
