import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Slider
} from "@/components/ui/slider";
import {
  Filter,
  X,
  ChevronDown,
  ChevronUp,
  Search,
  RefreshCw,
  Check
} from 'lucide-react';
import { productApi, ProductFilterOption } from '@/services/productApi';
import { formatCurrency } from '@/utils/currency';

// Types
interface ProductAttribute {
  id: number;
  name: string;
  description?: string;
  values: AttributeValue[];
}

interface AttributeValue {
  id: number;
  attribute: number;
  value: string;
  value_name: string;
  products_count?: number;
}

interface PriceRange {
  min: number;
  max: number;
}

interface FilterState {
  attributes: Record<number, number[]>; // attribute_id -> [value_ids]
  price?: {
    min?: number;
    max?: number;
  };
  categories?: number[];
  inStock?: boolean;
  onSale?: boolean;
}

interface DynamicFiltersProps {
  onFilterChange: (filters: FilterState) => void;
  initialFilters?: FilterState;
  showPriceFilter?: boolean;
  showStockFilter?: boolean;
  showSaleFilter?: boolean;
  showCategoryFilter?: boolean;
  maxHeight?: string;
}

const DynamicFilters: React.FC<DynamicFiltersProps> = ({
  onFilterChange,
  initialFilters,
  showPriceFilter = true,
  showStockFilter = true,
  showSaleFilter = true,
  showCategoryFilter = true,
  maxHeight = '600px',
}) => {
  // State
  const [filters, setFilters] = useState<FilterState>(initialFilters || {
    attributes: {},
    price: undefined,
    categories: undefined,
    inStock: undefined,
    onSale: undefined,
  });

  const [priceRange, setPriceRange] = useState<PriceRange>({ min: 0, max: 1000 });
  const [currentPriceRange, setCurrentPriceRange] = useState<[number, number]>([0, 1000]);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedAttributes, setExpandedAttributes] = useState<string[]>([]);
  const [activeFilterCount, setActiveFilterCount] = useState(0);

  // Fetch attributes
  const {
    data: attributes = [],
    isLoading: isLoadingAttributes
  } = useQuery({
    queryKey: ['productAttributes'],
    queryFn: () => productApi.getAttributes(),
  });

  // Fetch categories
  const {
    data: categories = [],
    isLoading: isLoadingCategories
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => productApi.getCategories(),
    enabled: showCategoryFilter,
  });

  // Fetch price range
  const {
    data: priceBoundaries,
    isLoading: isLoadingPriceRange
  } = useQuery({
    queryKey: ['productPriceRange'],
    queryFn: () => productApi.getProductPriceRange(),
    enabled: showPriceFilter,
  });

  // Update price range when data is loaded
  useEffect(() => {
    if (priceBoundaries) {
      setPriceRange({ min: priceBoundaries.min_price, max: priceBoundaries.max_price });
      setCurrentPriceRange([
        filters.price?.min !== undefined ? filters.price.min : priceBoundaries.min_price,
        filters.price?.max !== undefined ? filters.price.max : priceBoundaries.max_price
      ]);
    }
  }, [priceBoundaries, filters.price]);

  // Initialize expanded attributes
  useEffect(() => {
    if (attributes.length > 0) {
      // Expand first 3 attributes by default
      setExpandedAttributes(attributes.slice(0, 3).map(attr => attr.id.toString()));
    }
  }, [attributes]);

  // Update active filter count
  useEffect(() => {
    let count = 0;

    // Count attribute filters
    Object.values(filters.attributes).forEach(values => {
      if (values.length > 0) count += values.length;
    });

    // Count other filters
    if (filters.price?.min !== undefined || filters.price?.max !== undefined) count++;
    if (filters.categories && filters.categories.length > 0) count += filters.categories.length;
    if (filters.inStock !== undefined) count++;
    if (filters.onSale !== undefined) count++;

    setActiveFilterCount(count);
  }, [filters]);

  // Notify parent component when filters change
  useEffect(() => {
    onFilterChange(filters);
  }, [filters, onFilterChange]);

  // Handle attribute filter change
  const handleAttributeFilterChange = (attributeId: number, valueId: number, checked: boolean) => {
    setFilters(prev => {
      const currentValues = prev.attributes[attributeId] || [];
      const newValues = checked
        ? [...currentValues, valueId]
        : currentValues.filter(id => id !== valueId);

      return {
        ...prev,
        attributes: {
          ...prev.attributes,
          [attributeId]: newValues
        }
      };
    });
  };

  // Handle category filter change
  const handleCategoryFilterChange = (categoryId: number, checked: boolean) => {
    setFilters(prev => {
      const currentCategories = prev.categories || [];
      const newCategories = checked
        ? [...currentCategories, categoryId]
        : currentCategories.filter(id => id !== categoryId);

      return {
        ...prev,
        categories: newCategories.length > 0 ? newCategories : undefined
      };
    });
  };

  // Handle price range change
  const handlePriceRangeChange = (values: [number, number]) => {
    setCurrentPriceRange(values);
  };

  // Apply price range after slider interaction ends
  const handlePriceRangeChangeEnd = (values: [number, number]) => {
    const [min, max] = values;
    setFilters(prev => ({
      ...prev,
      price: {
        min: min !== priceRange.min ? min : undefined,
        max: max !== priceRange.max ? max : undefined
      }
    }));
  };

  // Handle stock filter change
  const handleStockFilterChange = (checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      inStock: checked || undefined
    }));
  };

  // Handle sale filter change
  const handleSaleFilterChange = (checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      onSale: checked || undefined
    }));
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      attributes: {},
      price: undefined,
      categories: undefined,
      inStock: undefined,
      onSale: undefined,
    });

    if (priceBoundaries) {
      setCurrentPriceRange([priceBoundaries.min_price, priceBoundaries.max_price]);
    }
  };

  // Filter attributes by search term
  const filteredAttributes = searchTerm
    ? attributes.filter(attr =>
        attr.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        attr.values.some(v => v.value_name.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : attributes;

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Filtres</CardTitle>
            <CardDescription>
              Affinez votre recherche de produits
            </CardDescription>
          </div>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="h-8 gap-1"
            >
              <RefreshCw className="h-3.5 w-3.5" />
              <span>Réinitialiser</span>
              <Badge className="ml-1 h-5 w-5 rounded-full p-0 flex items-center justify-center">
                {activeFilterCount}
              </Badge>
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search attributes */}
        {attributes.length > 5 && (
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Rechercher des filtres..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 h-7 w-7"
                onClick={() => setSearchTerm('')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}

        {/* Filters container */}
        <div
          className="space-y-4 overflow-y-auto pr-1"
          style={{ maxHeight }}
        >
          {/* Price range filter */}
          {showPriceFilter && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Prix</h3>
                {(filters.price?.min !== undefined || filters.price?.max !== undefined) && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => {
                      setFilters(prev => ({ ...prev, price: undefined }));
                      if (priceBoundaries) {
                        setCurrentPriceRange([priceBoundaries.min_price, priceBoundaries.max_price]);
                      }
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>

              {isLoadingPriceRange ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <>
                  <Slider
                    min={priceRange.min}
                    max={priceRange.max}
                    step={1}
                    value={currentPriceRange}
                    onValueChange={handlePriceRangeChange}
                    onValueCommit={handlePriceRangeChangeEnd}
                  />
                  <div className="flex items-center justify-between text-sm">
                    <span>{formatCurrency(currentPriceRange[0])}</span>
                    <span>{formatCurrency(currentPriceRange[1])}</span>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Stock and sale filters */}
          <div className="space-y-2">
            {showStockFilter && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="in-stock"
                  checked={filters.inStock === true}
                  onCheckedChange={(checked) => handleStockFilterChange(!!checked)}
                />
                <Label htmlFor="in-stock">En stock uniquement</Label>
              </div>
            )}

            {showSaleFilter && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="on-sale"
                  checked={filters.onSale === true}
                  onCheckedChange={(checked) => handleSaleFilterChange(!!checked)}
                />
                <Label htmlFor="on-sale">En promotion uniquement</Label>
              </div>
            )}
          </div>

          {/* Category filter */}
          {showCategoryFilter && (
            <Accordion
              type="multiple"
              value={['categories']}
              className="w-full"
            >
              <AccordionItem value="categories" className="border-b-0">
                <AccordionTrigger className="py-2">
                  <span className="text-sm font-medium">Catégories</span>
                </AccordionTrigger>
                <AccordionContent>
                  {isLoadingCategories ? (
                    <div className="space-y-2">
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-6 w-full" />
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {categories.map(category => (
                        <div key={category.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category.id}`}
                            checked={(filters.categories || []).includes(category.id)}
                            onCheckedChange={(checked) =>
                              handleCategoryFilterChange(category.id, !!checked)
                            }
                          />
                          <Label
                            htmlFor={`category-${category.id}`}
                            className="text-sm"
                          >
                            {category.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}

          {/* Attribute filters */}
          {isLoadingAttributes ? (
            <div className="space-y-4">
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : filteredAttributes.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              Aucun attribut trouvé pour "{searchTerm}".
            </div>
          ) : (
            <Accordion
              type="multiple"
              value={expandedAttributes}
              onValueChange={setExpandedAttributes}
              className="w-full"
            >
              {filteredAttributes.map(attribute => (
                <AccordionItem
                  key={attribute.id}
                  value={attribute.id.toString()}
                  className="border-b-0"
                >
                  <AccordionTrigger className="py-2">
                    <div className="flex items-center">
                      <span className="text-sm font-medium">{attribute.name}</span>
                      {(filters.attributes[attribute.id] || []).length > 0 && (
                        <Badge className="ml-2">
                          {(filters.attributes[attribute.id] || []).length}
                        </Badge>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-1">
                      {attribute.values.map(value => (
                        <div key={value.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`attr-${attribute.id}-${value.id}`}
                            checked={(filters.attributes[attribute.id] || []).includes(value.id)}
                            onCheckedChange={(checked) =>
                              handleAttributeFilterChange(attribute.id, value.id, !!checked)
                            }
                          />
                          <Label
                            htmlFor={`attr-${attribute.id}-${value.id}`}
                            className="text-sm flex-1"
                          >
                            {value.value_name}
                          </Label>
                          {value.products_count !== undefined && (
                            <span className="text-xs text-gray-500">
                              ({value.products_count})
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </div>
      </CardContent>

      {activeFilterCount > 0 && (
        <CardFooter className="border-t pt-4 flex justify-between">
          <div className="text-sm text-gray-500">
            {activeFilterCount} filtre{activeFilterCount > 1 ? 's' : ''} actif{activeFilterCount > 1 ? 's' : ''}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={resetFilters}
          >
            Réinitialiser
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default DynamicFilters;
