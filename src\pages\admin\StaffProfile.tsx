import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import {
  User,
  Shield,
  Settings,
  Clock,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Award,
  BarChart3,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Loader2,
  Camera,
  Edit,
  Save,
  X
} from 'lucide-react';
import userApi from '@/services/userApi';
import { motion } from 'framer-motion';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';

const StaffProfile = () => {
  const { user, updateUser } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const pageTitle = user?.is_superuser || user?.role === 'admin' ? "Mon profil Superadmin" : "Mon profil Staff";
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    date_of_birth: '',
    bio: '',
    department: '',
    position: '',
    start_date: ''
  });

  // Fetch user profile
  const { data: profile, isLoading, isError } = useQuery({
    queryKey: ['staffProfile'],
    queryFn: userApi.getProfile
  });

  // Mock staff statistics - replace with actual API
  const staffStats = {
    conversations_handled: 156,
    average_response_time: 15.5,
    customer_satisfaction: 4.7,
    tickets_resolved: 142,
    current_workload: 8,
    this_month_performance: {
      conversations: 45,
      satisfaction: 4.8,
      response_time: 12.3
    },
    achievements: [
      { id: 1, title: 'Expert Support', description: '100+ tickets résolus', date: '2024-01-15T00:00:00Z', icon: '🏆' },
      { id: 2, title: 'Réponse Rapide', description: 'Temps de réponse < 10min', date: '2024-01-10T00:00:00Z', icon: '⚡' },
      { id: 3, title: 'Client Satisfait', description: 'Note moyenne > 4.5', date: '2024-01-05T00:00:00Z', icon: '⭐' }
    ],
    recent_activity: [
      { id: 1, action: 'Ticket résolu', details: 'Problème de commande #12345', time: new Date().toISOString() },
      { id: 2, action: 'Message envoyé', details: 'Réponse à Marie Dubois', time: new Date(Date.now() - 3600000).toISOString() },
      { id: 3, action: 'Statut mis à jour', details: 'Ticket #12344 en cours', time: new Date(Date.now() - 7200000).toISOString() }
    ]
  };

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: userApi.updateProfile,
    onSuccess: (updatedProfile) => {
      queryClient.invalidateQueries({ queryKey: ['staffProfile'] });
      updateUser({
        first_name: updatedProfile.first_name,
        last_name: updatedProfile.last_name,
        phone_number: updatedProfile.phone_number,
        date_of_birth: updatedProfile.date_of_birth
      });
      setIsEditing(false);
      toast({
        title: "Profil mis à jour",
        description: "Vos informations ont été mises à jour avec succès.",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour votre profil.",
        variant: "destructive",
      });
    }
  });

  const updateProfilePictureMutation = useMutation({
    mutationFn: userApi.updateProfilePicture,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['staffProfile'] });
      updateUser({ profile_picture: data.profile_picture });
      setSelectedFile(null);
      setPreview(null);
      toast({
        title: "Photo de profil mise à jour",
        description: "Votre nouvelle photo a été enregistrée.",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour votre photo de profil.",
        variant: "destructive",
      });
    }
  });

  // Update form data when profile is loaded
  useEffect(() => {
    if (profile) {
      setFormData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        email: profile.email || '',
        phone_number: profile.phone_number || '',
        date_of_birth: profile.date_of_birth || '',
        bio: profile.bio || '',
        department: profile.department || 'Support Client',
        position: profile.position || 'Agent Support',
        start_date: profile.start_date || new Date().toISOString().split('T')[0]
      });
    }
  }, [profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate(formData);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setPreview(URL.createObjectURL(file));
    }
  };

  const handleUpload = () => {
    if (selectedFile) {
      const formData = new FormData();
      formData.append('profile_picture', selectedFile);
      updateProfilePictureMutation.mutate(formData);
    }
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${minutes.toFixed(1)}min`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}min`;
  };

  const formatDate = (dateString: string) => {
    try {
      if (!dateString) return 'Date non disponible';
      const date = parseISO(dateString);
      if (isNaN(date.getTime())) return 'Date invalide';
      return format(date, 'dd MMMM yyyy à HH:mm', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date invalide';
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">{pageTitle}</h1>
            <p className="text-gray-500">Gérez vos informations personnelles et consultez vos performances</p>
          </div>
          <Badge className="bg-blue-100 text-blue-800 px-3 py-1">
            <Shield className="h-4 w-4 mr-1" />
            {user?.is_superuser || user?.role === 'admin' ? 'Superadmin' : 'Staff'}
          </Badge>
        </div>

        {/* Profile Header Card */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-6">
              <div className="relative">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={preview || profile?.profile_picture || ''} alt={profile?.full_name} />
                  <AvatarFallback>{profile?.first_name?.[0]}{profile?.last_name?.[0]}</AvatarFallback>
                </Avatar>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept="image/*"
                />
                <div 
                  className="absolute bottom-0 right-0 bg-primary text-white rounded-full p-1 cursor-pointer hover:bg-primary/90"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Camera size={16} />
                </div>
              </div>
              
              <div className="flex-1">
                <h2 className="text-2xl font-bold">{profile?.full_name}</h2>
                <p className="text-gray-600 mb-2">{formData.position} - {formData.department}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-1" />
                    {profile?.email}
                  </div>
                  {profile?.phone_number && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      {profile.phone_number}
                    </div>
                  )}
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Depuis {formData.start_date ? format(parseISO(formData.start_date), 'MMMM yyyy', { locale: fr }) : 'Date non définie'}
                  </div>
                </div>
              </div>

              <div className="text-right">
                  {/* Photo upload buttons */}
                  {preview && (
                    <div className="flex gap-2 justify-end mb-2">
                      <Button onClick={handleUpload} disabled={updateProfilePictureMutation.isPending}>
                        {updateProfilePictureMutation.isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save size={16} className="mr-2" />}
                        Enregistrer la photo
                      </Button>
                      <Button variant="outline" onClick={() => { setPreview(null); setSelectedFile(null); }}>Annuler</Button>
                    </div>
                  )}

                  {/* Form edit toggle button */}
                  {!isEditing && !preview && (
                    <Button onClick={() => setIsEditing(true)}>
                      <Edit size={16} className="mr-2" />
                      Modifier
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Conversations</p>
                    <p className="text-2xl font-bold">{staffStats.conversations_handled}</p>
                  </div>
                  <MessageSquare className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Temps de réponse</p>
                    <p className="text-2xl font-bold">{formatTime(staffStats.average_response_time)}</p>
                  </div>
                  <Clock className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Satisfaction</p>
                    <p className="text-2xl font-bold">{staffStats.customer_satisfaction}/5</p>
                  </div>
                  <Award className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Tickets résolus</p>
                    <p className="text-2xl font-bold">{staffStats.tickets_resolved}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">Profil</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="achievements">Récompenses</TabsTrigger>
            <TabsTrigger value="activity">Activité</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Informations personnelles</CardTitle>
                <CardDescription>
                  Modifiez vos informations personnelles et professionnelles
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  {isEditing && (
                    <div className="flex justify-end gap-2 pt-4 border-t mt-4">
                      <Button variant="outline" type="button" onClick={() => {
                        setIsEditing(false);
                        // Reset form data to original profile data if changes are cancelled
                        if (profile) {
                          setFormData({
                            first_name: profile.first_name || '',
                            last_name: profile.last_name || '',
                            email: profile.email || '',
                            phone_number: profile.phone_number || '',
                            date_of_birth: profile.date_of_birth || '',
                            bio: profile.bio || '',
                            department: profile.department || 'Support Client',
                            position: profile.position || 'Agent Support',
                            start_date: profile.start_date || new Date().toISOString().split('T')[0]
                          });
                        }
                      }}>
                        <X className="mr-2 h-4 w-4" />
                        Annuler
                      </Button>
                      <Button type="submit" disabled={updateProfileMutation.isPending}>
                        {updateProfileMutation.isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save size={16} className="mr-2" />}
                        Enregistrer
                      </Button>
                    </div>
                  )}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Prénom</label>
                      <Input
                        name="first_name"
                        value={formData.first_name}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Nom</label>
                      <Input
                        name="last_name"
                        value={formData.last_name}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <Input
                      name="email"
                      value={formData.email}
                      disabled
                    />
                    <p className="text-xs text-gray-500">L'email ne peut pas être modifié</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Téléphone</label>
                      <Input
                        name="phone_number"
                        value={formData.phone_number}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Date de naissance</label>
                      <Input
                        name="date_of_birth"
                        type="date"
                        value={formData.date_of_birth}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Département</label>
                      <Input
                        name="department"
                        value={formData.department}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Poste</label>
                      <Input
                        name="position"
                        value={formData.position}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Bio professionnelle</label>
                    <Textarea
                      name="bio"
                      value={formData.bio}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      placeholder="Décrivez votre expérience et vos compétences..."
                      rows={3}
                    />
                  </div>

                  {isEditing && (
                    <Button type="submit" disabled={updateProfileMutation.isPending}>
                      {updateProfileMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Enregistrement...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Enregistrer les modifications
                        </>
                      )}
                    </Button>
                  )}
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance ce mois</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Conversations traitées</span>
                      <Badge variant="outline">{staffStats.this_month_performance.conversations}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Satisfaction moyenne</span>
                      <Badge className="bg-green-100 text-green-800">
                        {staffStats.this_month_performance.satisfaction}/5
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Temps de réponse moyen</span>
                      <Badge className="bg-blue-100 text-blue-800">
                        {formatTime(staffStats.this_month_performance.response_time)}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Charge de travail actuelle</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {staffStats.current_workload}
                    </div>
                    <p className="text-gray-600">conversations en cours</p>
                    <div className="mt-4">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(staffStats.current_workload / 15) * 100}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Capacité: 15 conversations max</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="achievements" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Récompenses et Badges</CardTitle>
                <CardDescription>Vos accomplissements et reconnaissances</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {staffStats.achievements.map((achievement) => (
                    <motion.div
                      key={achievement.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="border rounded-lg p-4 text-center"
                    >
                      <div className="text-4xl mb-2">{achievement.icon}</div>
                      <h3 className="font-semibold">{achievement.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                      <p className="text-xs text-gray-500">
                        {achievement.date ? format(parseISO(achievement.date), 'dd MMM yyyy', { locale: fr }) : 'Date non définie'}
                      </p>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Activité récente</CardTitle>
                <CardDescription>Vos dernières actions sur la plateforme</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {staffStats.recent_activity.map((activity) => (
                    <div key={activity.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <div className="flex-1">
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-gray-600">{activity.details}</p>
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(activity.time)}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default StaffProfile;
