from django.db import models
from django.utils.text import slugify
from django.conf import settings
from django.utils.translation import gettext_lazy as _

class TimeStampedModel(models.Model):
    """
    An abstract base class model that provides self-updating
    created and modified fields.
    """
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class Page(TimeStampedModel):
    """
    Model for static pages like Home, About, etc.
    """
    TEMPLATE_CHOICES = (
        ('standard', _('Standard')),
        ('homepage', _('Homepage')),
        ('services', _('Services')),
        ('legal', _('Legal')),
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('published', _('Published')),
        ('archived', _('Archived')),
    )

    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    content = models.TextField()
    template = models.CharField(max_length=50, choices=TEMPLATE_CHOICES, default='standard')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    meta_description = models.CharField(max_length=255, blank=True)
    meta_keywords = models.CharField(max_length=255, blank=True)
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='pages'
    )

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-updated_at']
        verbose_name = _('Page')
        verbose_name_plural = _('Pages')

class BlogCategory(TimeStampedModel):
    """
    Categories for blog posts
    """
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = _('Blog Category')
        verbose_name_plural = _('Blog Categories')

class BlogPost(TimeStampedModel):
    """
    Model for blog posts
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('published', _('Published')),
        ('archived', _('Archived')),
    )

    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    content = models.TextField()
    excerpt = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    featured = models.BooleanField(default=False, help_text=_('Mark as featured post'))
    featured_image = models.ImageField(upload_to='blog/images/', blank=True, null=True)
    categories = models.ManyToManyField(BlogCategory, related_name='posts')
    meta_description = models.CharField(max_length=255, blank=True)
    meta_keywords = models.CharField(max_length=255, blank=True)
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='blog_posts'
    )

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Blog Post')
        verbose_name_plural = _('Blog Posts')

class Comment(TimeStampedModel):
    """
    Model for comments on blog posts
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
    )

    post = models.ForeignKey(BlogPost, on_delete=models.CASCADE, related_name='comments')
    author_name = models.CharField(max_length=100)
    author_email = models.EmailField()
    content = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='blog_comments'
    )

    def __str__(self):
        return f"Comment by {self.author_name} on {self.post.title}"

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Comment')
        verbose_name_plural = _('Comments')

class Media(TimeStampedModel):
    """
    Model for media files (images, documents, etc.)
    """
    TYPE_CHOICES = (
        ('image', _('Image')),
        ('document', _('Document')),
        ('video', _('Video')),
        ('audio', _('Audio')),
        ('other', _('Other')),
    )

    title = models.CharField(max_length=200)
    file = models.FileField(upload_to='cms/media/')
    file_type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    description = models.TextField(blank=True)
    alt_text = models.CharField(max_length=255, blank=True)
    uploader = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='uploaded_media'
    )

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = _('Media')
        verbose_name_plural = _('Media')

    @property
    def file_url(self):
        return self.file.url if self.file else None

    @property
    def file_size(self):
        return self.file.size if self.file else 0

    @property
    def file_name(self):
        return self.file.name.split('/')[-1] if self.file else None

class Menu(TimeStampedModel):
    """
    Model for navigation menus
    """
    LOCATION_CHOICES = (
        ('main', _('Main Navigation')),
        ('footer', _('Footer')),
        ('sidebar', _('Sidebar')),
    )

    name = models.CharField(max_length=100)
    location = models.CharField(max_length=20, choices=LOCATION_CHOICES)

    def __str__(self):
        return f"{self.name} ({self.get_location_display()})"

    class Meta:
        verbose_name = _('Menu')
        verbose_name_plural = _('Menus')

class MenuItem(TimeStampedModel):
    """
    Model for menu items
    """
    menu = models.ForeignKey(Menu, on_delete=models.CASCADE, related_name='items')
    title = models.CharField(max_length=100)
    url = models.CharField(max_length=255, blank=True)
    page = models.ForeignKey(
        Page,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='menu_items'
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children'
    )
    order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['order']
        verbose_name = _('Menu Item')
        verbose_name_plural = _('Menu Items')

class SiteSetting(TimeStampedModel):
    """
    Model for site settings
    """
    site_title = models.CharField(max_length=200)
    site_description = models.TextField(blank=True)
    contact_email = models.EmailField(blank=True)
    contact_phone = models.CharField(max_length=20, blank=True)
    contact_address = models.TextField(blank=True)
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    logo = models.ImageField(upload_to='cms/site/', blank=True, null=True)
    favicon = models.ImageField(upload_to='cms/site/', blank=True, null=True)

    def __str__(self):
        return self.site_title

    class Meta:
        verbose_name = _('Site Setting')
        verbose_name_plural = _('Site Settings')
