import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  AlertCircle,
  Clock,
  FileCheck,
  FileX,
  Loader2,
  Package,
  RefreshCw,
  Calendar,
  FileText,
  ExternalLink
} from 'lucide-react';
import { accountApi, Claim, ClaimStatusUpdate } from '@/services/accountApi';
import { ClaimMessages } from './ClaimMessages';

interface ClaimDetailsProps {
  claimId: string;
  onRefresh: () => void;
}

export function ClaimDetails({ claimId, onRefresh }: ClaimDetailsProps) {
  // Récupérer les détails de la réclamation
  const {
    data: claim,
    isLoading: isLoadingClaim,
    isError: isErrorClaim,
    refetch: refetchClaim
  } = useQuery({
    queryKey: ['claim', claimId],
    queryFn: () => accountApi.getClaimDetails(claimId),
    enabled: !!claimId
  });
  
  // Récupérer l'historique des statuts
  const {
    data: statusHistory = [],
    isLoading: isLoadingHistory,
    isError: isErrorHistory
  } = useQuery({
    queryKey: ['claimStatusHistory', claimId],
    queryFn: () => accountApi.getClaimStatusHistory(claimId),
    enabled: !!claimId
  });
  
  // Obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Nouveau':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            {status}
          </Badge>
        );
      case 'En cours':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-50 border-yellow-200">
            <Clock className="mr-1 h-3 w-3" />
            {status}
          </Badge>
        );
      case 'Résolu':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">
            <FileCheck className="mr-1 h-3 w-3" />
            {status}
          </Badge>
        );
      case 'Fermé':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-50 border-gray-200">
            <FileX className="mr-1 h-3 w-3" />
            {status}
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };
  
  // État de chargement
  if (isLoadingClaim) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
          <p className="text-gray-500">Chargement des détails de la réclamation...</p>
        </CardContent>
      </Card>
    );
  }
  
  // État d'erreur
  if (isErrorClaim || !claim) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <AlertCircle className="h-8 w-8 text-red-500 mb-4" />
          <p className="text-gray-700 font-medium mb-2">Impossible de charger les détails</p>
          <p className="text-gray-500 mb-4">Une erreur est survenue lors du chargement des détails de la réclamation.</p>
          <Button variant="outline" onClick={() => {
            refetchClaim();
            onRefresh();
          }} className="flex items-center gap-1">
            <RefreshCw className="h-4 w-4" />
            Réessayer
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Informations principales */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">Réclamation #{claim.id}</CardTitle>
              <CardDescription>
                Créée le {claim.date} • Dernière mise à jour: {claim.lastUpdate}
              </CardDescription>
            </div>
            <div>
              {getStatusBadge(claim.status)}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-1">{claim.subject}</h3>
            <p className="text-gray-700">{claim.description}</p>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Package className="h-4 w-4" />
            <span>Commande: <strong>{claim.orderId}</strong></span>
          </div>
          
          {claim.attachments && claim.attachments.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Pièces jointes</h4>
              <div className="flex flex-wrap gap-2">
                {claim.attachments.map((attachment, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 text-xs"
                    asChild
                  >
                    <a href="#" onClick={(e) => e.preventDefault()}>
                      <FileText className="h-3.5 w-3.5" />
                      {attachment}
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Historique des statuts */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">Historique des statuts</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingHistory ? (
            <div className="flex justify-center py-4">
              <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
            </div>
          ) : isErrorHistory ? (
            <div className="text-center py-4">
              <p className="text-gray-500">Impossible de charger l'historique des statuts.</p>
            </div>
          ) : statusHistory.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-gray-500">Aucun historique de statut disponible.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {statusHistory.map((update: ClaimStatusUpdate, index: number) => (
                <div key={update.id} className="relative pl-6">
                  {index !== statusHistory.length - 1 && (
                    <div className="absolute left-2.5 top-3 w-px h-full bg-gray-200" />
                  )}
                  <div className="absolute left-0 top-1 w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-primary" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {update.oldStatus ? `${update.oldStatus} → ${update.newStatus}` : update.newStatus}
                      </span>
                      <span className="text-xs text-gray-500">
                        <Calendar className="inline h-3 w-3 mr-0.5" />
                        {update.date}
                      </span>
                    </div>
                    {update.comment && (
                      <p className="text-sm text-gray-600 mt-1">{update.comment}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Messages */}
      <ClaimMessages claimId={claimId} />
    </div>
  );
}
