from rest_framework import status, generics, permissions, viewsets, filters
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import action
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Sum
from datetime import timed<PERSON>ta
from django.conf import settings
import uuid

from .serializers import (
    UserSerializer,
    UserProfileSerializer,
    UserProfileExtendedSerializer,
    LoginSerializer,
    PasswordChangeSerializer,
    PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer,
    EmailVerificationSerializer,
    UserAddressSerializer,
    UserAddressListSerializer,
    LoyaltyTransactionSerializer,
    UserLoyaltySerializer
)
from .models import UserVerification, PasswordReset, UserAddress, LoyaltyTransaction

User = get_user_model()

class RegisterView(generics.CreateAPIView):
    """
    API endpoint for user registration.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = UserSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            # Generate tokens
            refresh = RefreshToken.for_user(user)

            # Return response with tokens
            return Response({
                'user': UserSerializer(user).data,
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'message': 'User registered successfully. Please verify your email.'
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LoginView(APIView):
    """
    API endpoint for user login.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = LoginSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']
        refresh = RefreshToken.for_user(user)

        return Response({
            'user': UserSerializer(user).data,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }, status=status.HTTP_200_OK)

class LogoutView(APIView):
    """
    API endpoint for user logout.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        try:
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            return Response({'message': 'Logout successful'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class UserProfileView(generics.RetrieveUpdateAPIView):
    """
    API endpoint for retrieving and updating user profile.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserProfileExtendedSerializer

    def get_object(self):
        return self.request.user

class PasswordChangeView(APIView):
    """
    API endpoint for changing password.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = PasswordChangeSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user = request.user

            # Check old password
            if not user.check_password(serializer.validated_data['old_password']):
                return Response({'old_password': ['Wrong password.']}, status=status.HTTP_400_BAD_REQUEST)

            # Set new password
            user.set_password(serializer.validated_data['new_password'])
            user.save()

            return Response({'message': 'Password changed successfully.'}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetRequestView(APIView):
    """
    API endpoint for requesting password reset.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = PasswordResetRequestSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']

            try:
                user = User.objects.get(email=email)

                # Generate reset token
                token = str(uuid.uuid4())
                expires_at = timezone.now() + timedelta(hours=24)

                # Save reset token
                PasswordReset.objects.create(
                    user=user,
                    token=token,
                    expires_at=expires_at
                )

                # In a real application, send an email with the reset link
                # For now, just return the token in the response (for testing)
                reset_link = f"{settings.FRONTEND_URL}/reset-password/{token}"

                return Response({
                    'message': 'Password reset email sent.',
                    'token': token,  # Remove this in production
                    'reset_link': reset_link  # Remove this in production
                }, status=status.HTTP_200_OK)

            except User.DoesNotExist:
                # Don't reveal that the user doesn't exist
                pass

            # Always return success to prevent email enumeration
            return Response({'message': 'If your email is registered, you will receive a password reset link.'},
                           status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetConfirmView(APIView):
    """
    API endpoint for confirming password reset.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = PasswordResetConfirmSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            token = serializer.validated_data['token']
            new_password = serializer.validated_data['new_password']

            try:
                reset = PasswordReset.objects.get(token=token, is_used=False)

                if reset.is_expired:
                    return Response({'token': ['Reset token has expired.']}, status=status.HTTP_400_BAD_REQUEST)

                # Reset password
                user = reset.user
                user.set_password(new_password)
                user.save()

                # Mark token as used
                reset.is_used = True
                reset.save()

                return Response({'message': 'Password reset successful.'}, status=status.HTTP_200_OK)

            except PasswordReset.DoesNotExist:
                return Response({'token': ['Invalid reset token.']}, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class EmailVerificationView(APIView):
    """
    API endpoint for email verification.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = EmailVerificationSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            token = serializer.validated_data['token']

            try:
                verification = UserVerification.objects.get(token=token)

                if verification.is_expired:
                    return Response({'token': ['Verification token has expired.']}, status=status.HTTP_400_BAD_REQUEST)

                # Mark user as verified
                user = verification.user
                user.is_verified = True
                user.save()

                # Delete verification token
                verification.delete()

                return Response({'message': 'Email verified successfully.'}, status=status.HTTP_200_OK)

            except UserVerification.DoesNotExist:
                return Response({'token': ['Invalid verification token.']}, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ResendVerificationView(APIView):
    """
    API endpoint for resending verification email.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user

        if user.is_verified:
            return Response({'message': 'Email already verified.'}, status=status.HTTP_400_BAD_REQUEST)

        # Delete existing verification if any
        UserVerification.objects.filter(user=user).delete()

        # Create new verification token
        token = str(uuid.uuid4())
        expires_at = timezone.now() + timedelta(days=1)
        UserVerification.objects.create(user=user, token=token, expires_at=expires_at)

        # In a real application, send an email with the verification link
        # For now, just return the token in the response (for testing)
        verification_link = f"{settings.FRONTEND_URL}/verify-email/{token}"

        return Response({
            'message': 'Verification email sent.',
            'token': token,  # Remove this in production
            'verification_link': verification_link  # Remove this in production
        }, status=status.HTTP_200_OK)

class UserAddressViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user addresses.
    """
    serializer_class = UserAddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Return addresses for the current user only."""
        return UserAddress.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Save the user when creating a new address."""
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set an address as the default for its type."""
        address = self.get_object()
        address.is_default = True
        address.save()
        return Response({'status': 'Address set as default'})

class LoyaltyTransactionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing loyalty transactions.
    """
    serializer_class = LoyaltyTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Return transactions for the current user only."""
        return LoyaltyTransaction.objects.filter(user=self.request.user)

class UserLoyaltyView(generics.RetrieveAPIView):
    """
    API endpoint for retrieving user loyalty information.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserLoyaltySerializer

    def get_object(self):
        return self.request.user
