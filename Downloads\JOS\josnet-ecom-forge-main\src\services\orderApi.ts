import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '@/utils/auth';

// Types
export interface OrderItem {
  id?: number;
  product: number;
  variant?: number | null;
  product_name: string;
  variant_name?: string | null;
  sku: string;
  price: number;
  quantity: number;
  subtotal?: number;
  discount_amount: number;
  final_price?: number;
  product_details?: any;
  variant_details?: any;
}

export interface OrderStatusHistory {
  id?: number;
  order: number;
  status: string;
  status_display?: string;
  notes?: string | null;
  created_by?: number | null;
  created_by_name?: string | null;
  created_at?: string;
}

export interface OrderRefund {
  id?: number;
  order: number;
  refund_number?: string;
  amount: number;
  status: string;
  status_display?: string;
  refund_type: string;
  refund_type_display?: string;
  reason: string;
  notes?: string | null;
  items?: number[];
  items_details?: OrderItem[];
  processed_by?: number | null;
  processed_by_name?: string | null;
  processed_at?: string | null;
  transaction_id?: string | null;
  created_by?: number | null;
  created_by_name?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface ShippingMethod {
  id?: number;
  name: string;
  description?: string | null;
  price: number;
  estimated_delivery_days: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Order {
  id?: number;
  order_number?: string;
  user?: number | null;
  email: string;
  phone?: string | null;

  // Billing information
  billing_first_name: string;
  billing_last_name: string;
  billing_company?: string | null;
  billing_address_line1: string;
  billing_address_line2?: string | null;
  billing_city: string;
  billing_state?: string | null;
  billing_postal_code: string;
  billing_country: string;
  billing_address_formatted?: string;

  // Shipping information
  shipping_first_name: string;
  shipping_last_name: string;
  shipping_company?: string | null;
  shipping_address_line1: string;
  shipping_address_line2?: string | null;
  shipping_city: string;
  shipping_state?: string | null;
  shipping_postal_code: string;
  shipping_country: string;
  shipping_address_formatted?: string;

  // Order details
  status?: string;
  status_display?: string;
  payment_status?: string;
  payment_status_display?: string;
  payment_method: string;
  payment_method_display?: string;
  payment_reference?: string | null;

  // Pricing
  subtotal: number;
  shipping_cost: number;
  tax_amount: number;
  discount_amount: number;
  total: number;

  // Tracking
  tracking_number?: string | null;
  shipping_carrier?: string | null;
  estimated_delivery_date?: string | null;

  // Notes
  customer_notes?: string | null;
  admin_notes?: string | null;

  // Related data
  items?: OrderItem[];
  status_history?: OrderStatusHistory[];
  refunds?: OrderRefund[];

  // Timestamps
  created_at?: string;
  updated_at?: string;
  paid_at?: string | null;
  shipped_at?: string | null;
  delivered_at?: string | null;
  cancelled_at?: string | null;

  // Additional fields for list view
  customer_name?: string;
  item_count?: number;
}

export interface OrderDashboardData {
  status_counts: Record<string, number>;
  total_revenue: number;
  recent_orders: Order[];
  payment_method_counts: Record<string, number>;
  total_orders: number;
}

// Order API service
const orderApi = {
  // Orders
  getOrders: async (params?: any) => {
    const response = await axios.get(`${API_URL}/orders/orders/`, {
      params,
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  getOrder: async (id: number) => {
    const response = await axios.get(`${API_URL}/orders/orders/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  createOrder: async (order: Order) => {
    const response = await axios.post(`${API_URL}/orders/orders/`, order, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  updateOrder: async (id: number, order: Partial<Order>) => {
    const response = await axios.patch(`${API_URL}/orders/orders/${id}/`, order, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  deleteOrder: async (id: number) => {
    const response = await axios.delete(`${API_URL}/orders/orders/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Order items
  getOrderItems: async (orderId: number) => {
    const response = await axios.get(`${API_URL}/orders/orders/${orderId}/items/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Order status history
  getOrderStatusHistory: async (orderId: number) => {
    const response = await axios.get(`${API_URL}/orders/orders/${orderId}/status_history/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  addOrderStatus: async (orderId: number, status: string, notes?: string) => {
    const response = await axios.post(`${API_URL}/orders/orders/${orderId}/add_status/`,
      { status, notes },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data;
  },

  // Order refunds
  getOrderRefunds: async (orderId: number) => {
    const response = await axios.get(`${API_URL}/orders/orders/${orderId}/refunds/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  createRefund: async (refund: OrderRefund) => {
    const response = await axios.post(`${API_URL}/orders/refunds/`, refund, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  processRefund: async (refundId: number, status: string, transaction_id?: string, notes?: string) => {
    const response = await axios.post(
      `${API_URL}/orders/refunds/${refundId}/process/`,
      { status, transaction_id, notes },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data;
  },

  // Shipping methods
  getShippingMethods: async (active: boolean = true) => {
    const url = active
      ? `${API_URL}/orders/shipping-methods/active/`
      : `${API_URL}/orders/shipping-methods/`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // User orders
  getMyOrders: async () => {
    const response = await axios.get(`${API_URL}/orders/orders/my_orders/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Cancel order
  cancelOrder: async (orderId: number, reason: string) => {
    const response = await axios.post(
      `${API_URL}/orders/orders/${orderId}/cancel/`,
      { reason },
      {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      }
    );
    return response.data;
  },

  // Dashboard data
  getDashboardData: async () => {
    const response = await axios.get(`${API_URL}/orders/orders/dashboard/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data as OrderDashboardData;
  }
};

export default orderApi;
