import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import ReviewList from '@/components/reviews/ReviewList';
import ReviewStats from '@/components/reviews/ReviewStats';
import ReviewForm from '@/components/reviews/ReviewForm';
import { useAuth } from '@/contexts/AuthContext';
import { productApi } from '@/services/productApi';

const ProductReviews: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Récupérer les informations du produit
  const { data: product, isLoading } = useQuery({
    queryKey: ['product', productId],
    queryFn: () => productApi.getProduct(Number(productId)),
    enabled: !!productId,
  });

  const handleCreateReview = () => {
    if (!user) {
      navigate('/login');
      return;
    }
    setShowCreateForm(true);
  };

  const handleFormSuccess = () => {
    setShowCreateForm(false);
  };

  if (!productId) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>ID de produit manquant</p>
          <Button onClick={() => navigate('/products')} className="mt-4">
            Voir tous les produits
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Chargement du produit...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Produit non trouvé</p>
          <Button onClick={() => navigate('/products')} className="mt-4">
            Voir tous les produits
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* En-tête */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => navigate(`/product/${productId}`)}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour au produit
        </Button>
        <h1 className="text-3xl font-bold mb-2">
          Avis pour {product.name}
        </h1>
        <p className="text-gray-600">
          Découvrez ce que pensent nos clients de ce produit
        </p>
      </div>

      {/* Contenu principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Statistiques des avis */}
        <div className="lg:col-span-1">
          <ReviewStats productId={Number(productId)} />
        </div>

        {/* Liste des avis */}
        <div className="lg:col-span-2">
          <ReviewList
            productId={Number(productId)}
            showCreateButton={true}
            onCreateReview={handleCreateReview}
          />
        </div>
      </div>

      {/* Dialog pour créer un avis */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Écrire un avis</DialogTitle>
          </DialogHeader>
          <ReviewForm
            productId={Number(productId)}
            productName={product.name}
            onSuccess={handleFormSuccess}
            onCancel={() => setShowCreateForm(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductReviews;
