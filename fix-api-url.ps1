# Script PowerShell pour corriger les API_URL
$files = @(
    "src/services/paymentApi.ts",
    "src/services/messagingApi.ts",
    "src/hooks/useBlog.ts"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Correction de $file..."
        $content = Get-Content $file -Raw
        $content = $content -replace '\$\{API_URL\}', '${API_BASE_URL}'
        $content = $content -replace 'API_URL', 'API_BASE_URL'
        Set-Content $file $content
        Write-Host "✅ $file corrigé"
    } else {
        Write-Host "❌ $file non trouvé"
    }
}

Write-Host "Correction terminée !"
