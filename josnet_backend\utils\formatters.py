"""
Utility functions for formatting data in the backend.
"""
from decimal import Decimal


def format_currency_bif(amount):
    """
    Format a currency amount to Burundian Franc (BIF).
    
    Args:
        amount: The amount to format (can be int, float, Decimal)
        
    Returns:
        str: Formatted amount string with BIF symbol
    """
    if amount is None:
        return "FBu 0"
    
    # Convert to Decimal for precision
    if not isinstance(amount, Decimal):
        amount = Decimal(str(amount))
    
    # Convert from EUR to BIF (1 EUR ≈ 2200 BIF)
    #exchange_rate = 2200
    amount_bif = amount
    
    # Round to nearest integer (BIF doesn't use decimal places)
    amount_rounded = int(round(amount_bif))
    
    # Format with thousands separator
    formatted = "{:,}".format(amount_rounded).replace(",", " ")
    
    return f"FBu {formatted}"
