# Generated by Django 4.2.23 on 2025-07-03 08:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0006_order_currency'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('products', '0004_productsuggestion_productsuggestionattachment_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='productreview',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddField(
            model_name='productreview',
            name='cons',
            field=models.TextField(blank=True, help_text='What could be improved?', null=True),
        ),
        migrations.AddField(
            model_name='productreview',
            name='helpful_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='productreview',
            name='is_verified_purchase',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productreview',
            name='moderation_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='productreview',
            name='not_helpful_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='productreview',
            name='order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviews', to='orders.order'),
        ),
        migrations.AddField(
            model_name='productreview',
            name='pros',
            field=models.TextField(blank=True, help_text='What did you like?', null=True),
        ),
        migrations.AlterField(
            model_name='productreview',
            name='rating',
            field=models.PositiveSmallIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Rating from 1 to 5 stars'),
        ),
        migrations.AlterUniqueTogether(
            name='productreview',
            unique_together={('product', 'user')},
        ),
        migrations.CreateModel(
            name='ReviewHelpfulness',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_helpful', models.BooleanField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='helpfulness_votes', to='products.productreview')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('review', 'user')},
            },
        ),
    ]
