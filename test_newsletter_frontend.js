/**
 * Script de test pour l'API frontend du système de newsletter
 * Ce script teste les fonctionnalités d'abonnement depuis le frontend
 */

const API_BASE_URL = 'http://localhost:8000/api/v1/core';

class NewsletterAPITester {
    constructor() {
        this.testResults = [];
        this.authToken = null;
    }

    async log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async makeRequest(endpoint, options = {}) {
        const url = `${API_BASE_URL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` })
            }
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            return {
                ok: response.ok,
                status: response.status,
                data: data
            };
        } catch (error) {
            return {
                ok: false,
                status: 0,
                error: error.message
            };
        }
    }

    async testAnonymousSubscription() {
        await this.log('Test d\'abonnement anonyme...');
        
        try {
            const testEmail = `test-${Date.now()}@example.com`;
            
            const response = await this.makeRequest('/newsletter/subscribe/', {
                method: 'POST',
                body: JSON.stringify({
                    email: testEmail,
                    preferences: {
                        promotions: true,
                        new_products: false,
                        newsletters: true,
                        order_updates: true
                    }
                })
            });

            if (response.ok) {
                await this.log(`Abonnement anonyme réussi pour ${testEmail}`, 'success');
                this.testResults.push({ test: 'Abonnement anonyme', success: true, details: testEmail });
                return testEmail;
            } else {
                await this.log(`Échec abonnement anonyme: ${JSON.stringify(response.data)}`, 'error');
                this.testResults.push({ test: 'Abonnement anonyme', success: false, details: response.data });
                return null;
            }
        } catch (error) {
            await this.log(`Erreur abonnement anonyme: ${error.message}`, 'error');
            this.testResults.push({ test: 'Abonnement anonyme', success: false, details: error.message });
            return null;
        }
    }

    async testDuplicateSubscription(email) {
        await this.log('Test d\'abonnement en double...');
        
        try {
            const response = await this.makeRequest('/newsletter/subscribe/', {
                method: 'POST',
                body: JSON.stringify({
                    email: email
                })
            });

            if (!response.ok && response.status === 400) {
                await this.log('Prévention d\'abonnement en double fonctionne', 'success');
                this.testResults.push({ test: 'Prévention doublons', success: true, details: 'Erreur 400 attendue' });
            } else {
                await this.log('Prévention d\'abonnement en double échouée', 'error');
                this.testResults.push({ test: 'Prévention doublons', success: false, details: 'Devrait retourner erreur 400' });
            }
        } catch (error) {
            await this.log(`Erreur test doublons: ${error.message}`, 'error');
            this.testResults.push({ test: 'Prévention doublons', success: false, details: error.message });
        }
    }

    async testUnsubscribe(email) {
        await this.log('Test de désabonnement...');
        
        try {
            const response = await this.makeRequest('/newsletter/unsubscribe/', {
                method: 'POST',
                body: JSON.stringify({
                    email: email
                })
            });

            if (response.ok) {
                await this.log(`Désabonnement réussi pour ${email}`, 'success');
                this.testResults.push({ test: 'Désabonnement', success: true, details: email });
            } else {
                await this.log(`Échec désabonnement: ${JSON.stringify(response.data)}`, 'error');
                this.testResults.push({ test: 'Désabonnement', success: false, details: response.data });
            }
        } catch (error) {
            await this.log(`Erreur désabonnement: ${error.message}`, 'error');
            this.testResults.push({ test: 'Désabonnement', success: false, details: error.message });
        }
    }

    async testSubscriptionStatus(email) {
        await this.log('Test de vérification du statut...');
        
        try {
            const response = await this.makeRequest(`/newsletter/status/?email=${encodeURIComponent(email)}`);

            if (response.ok) {
                const isSubscribed = response.data.is_subscribed;
                await this.log(`Statut récupéré: ${isSubscribed ? 'abonné' : 'non abonné'}`, 'success');
                this.testResults.push({ test: 'Vérification statut', success: true, details: `Abonné: ${isSubscribed}` });
                return isSubscribed;
            } else {
                await this.log(`Échec vérification statut: ${JSON.stringify(response.data)}`, 'error');
                this.testResults.push({ test: 'Vérification statut', success: false, details: response.data });
                return null;
            }
        } catch (error) {
            await this.log(`Erreur vérification statut: ${error.message}`, 'error');
            this.testResults.push({ test: 'Vérification statut', success: false, details: error.message });
            return null;
        }
    }

    async testReactivation(email) {
        await this.log('Test de réactivation...');
        
        try {
            const response = await this.makeRequest('/newsletter/reactivate/', {
                method: 'POST',
                body: JSON.stringify({
                    email: email
                })
            });

            if (response.ok) {
                await this.log(`Réactivation réussie pour ${email}`, 'success');
                this.testResults.push({ test: 'Réactivation', success: true, details: email });
            } else {
                await this.log(`Échec réactivation: ${JSON.stringify(response.data)}`, 'error');
                this.testResults.push({ test: 'Réactivation', success: false, details: response.data });
            }
        } catch (error) {
            await this.log(`Erreur réactivation: ${error.message}`, 'error');
            this.testResults.push({ test: 'Réactivation', success: false, details: error.message });
        }
    }

    async testAuthenticatedUser() {
        await this.log('Test avec utilisateur authentifié...');
        
        try {
            // Simuler une connexion (vous devrez adapter selon votre système d'auth)
            const loginResponse = await this.makeRequest('/auth/login/', {
                method: 'POST',
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'testpass123'
                })
            });

            if (loginResponse.ok && loginResponse.data.access) {
                this.authToken = loginResponse.data.access;
                await this.log('Connexion réussie', 'success');

                // Tester la récupération de l'abonnement
                const subResponse = await this.makeRequest('/newsletter-subscriptions/subscription/');
                
                if (subResponse.ok) {
                    await this.log('Récupération abonnement utilisateur réussie', 'success');
                    this.testResults.push({ test: 'Abonnement utilisateur auth', success: true, details: 'Récupération OK' });
                    
                    // Tester la mise à jour des préférences
                    const updateResponse = await this.makeRequest('/newsletter-subscriptions/preferences/', {
                        method: 'PATCH',
                        body: JSON.stringify({
                            promotions: false,
                            new_products: true,
                            newsletters: true,
                            order_updates: false
                        })
                    });

                    if (updateResponse.ok) {
                        await this.log('Mise à jour préférences réussie', 'success');
                        this.testResults.push({ test: 'Mise à jour préférences', success: true, details: 'Préférences modifiées' });
                    } else {
                        await this.log(`Échec mise à jour préférences: ${JSON.stringify(updateResponse.data)}`, 'error');
                        this.testResults.push({ test: 'Mise à jour préférences', success: false, details: updateResponse.data });
                    }
                } else {
                    await this.log(`Échec récupération abonnement: ${JSON.stringify(subResponse.data)}`, 'error');
                    this.testResults.push({ test: 'Abonnement utilisateur auth', success: false, details: subResponse.data });
                }
            } else {
                await this.log('Échec de connexion - test utilisateur auth ignoré', 'error');
                this.testResults.push({ test: 'Connexion utilisateur', success: false, details: 'Impossible de se connecter' });
            }
        } catch (error) {
            await this.log(`Erreur test utilisateur auth: ${error.message}`, 'error');
            this.testResults.push({ test: 'Test utilisateur auth', success: false, details: error.message });
        }
    }

    async runAllTests() {
        await this.log('🚀 Démarrage des tests API Newsletter Frontend\n');

        // Test 1: Abonnement anonyme
        const testEmail = await this.testAnonymousSubscription();
        
        if (testEmail) {
            // Test 2: Prévention des doublons
            await this.testDuplicateSubscription(testEmail);
            
            // Test 3: Vérification du statut (abonné)
            await this.testSubscriptionStatus(testEmail);
            
            // Test 4: Désabonnement
            await this.testUnsubscribe(testEmail);
            
            // Test 5: Vérification du statut (désabonné)
            await this.testSubscriptionStatus(testEmail);
            
            // Test 6: Réactivation
            await this.testReactivation(testEmail);
            
            // Test 7: Vérification du statut (réactivé)
            await this.testSubscriptionStatus(testEmail);
        }

        // Test 8: Utilisateur authentifié
        await this.testAuthenticatedUser();

        // Afficher le résumé
        this.printSummary();
    }

    printSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 RÉSUMÉ DES TESTS API FRONTEND');
        console.log('='.repeat(60));

        let passed = 0;
        let failed = 0;

        this.testResults.forEach(result => {
            const status = result.success ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.test}: ${result.details}`);
            
            if (result.success) {
                passed++;
            } else {
                failed++;
            }
        });

        console.log('\n' + '-'.repeat(60));
        console.log(`Total: ${this.testResults.length} tests`);
        console.log(`Réussis: ${passed}`);
        console.log(`Échoués: ${failed}`);

        if (failed === 0) {
            console.log('\n🎉 Tous les tests API sont passés avec succès !');
        } else {
            console.log(`\n⚠️  ${failed} test(s) API ont échoué. Vérifiez la configuration.`);
        }

        console.log('='.repeat(60));
    }
}

// Exécution des tests
async function main() {
    const tester = new NewsletterAPITester();
    await tester.runAllTests();
}

// Vérifier si on est dans Node.js ou dans le navigateur
if (typeof window === 'undefined') {
    // Node.js
    const fetch = require('node-fetch');
    global.fetch = fetch;
    main().catch(console.error);
} else {
    // Navigateur
    main().catch(console.error);
}
