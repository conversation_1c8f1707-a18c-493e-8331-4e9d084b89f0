# Generated by Django 4.2.7 on 2025-05-25 09:48

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('subtitle', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='banners/')),
                ('button_text', models.CharField(blank=True, max_length=50)),
                ('button_url', models.URLField(blank=True)),
                ('position', models.CharField(choices=[('hero', 'Hero principal'), ('secondary', 'Secondaire'), ('sidebar', '<PERSON><PERSON> latérale')], default='secondary', max_length=20)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Bannière',
                'verbose_name_plural': 'Bannières',
                'ordering': ['order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BlogCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
                ('color', models.CharField(default='blue', help_text='Couleur du thème', max_length=20)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Catégorie de blog',
                'verbose_name_plural': 'Catégories de blog',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('company', models.CharField(blank=True, max_length=100)),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('service_requested', models.CharField(blank=True, choices=[('reseaux', 'Réseaux & Connectivité'), ('materiel', 'Matériel Informatique'), ('mobile', 'Solutions Mobiles'), ('infrastructure', 'Infrastructure IT'), ('maintenance', 'Maintenance'), ('formation', 'Formation'), ('autre', 'Autre')], max_length=50)),
                ('status', models.CharField(choices=[('new', 'Nouveau'), ('in_progress', 'En cours'), ('replied', 'Répondu'), ('closed', 'Fermé')], default='new', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('replied_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Message de contact',
                'verbose_name_plural': 'Messages de contact',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FAQ',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.CharField(max_length=200)),
                ('answer', models.TextField()),
                ('category', models.CharField(choices=[('general', 'Général'), ('products', 'Produits'), ('orders', 'Commandes'), ('shipping', 'Livraison'), ('support', 'Support'), ('technical', 'Technique')], default='general', max_length=50)),
                ('featured', models.BooleanField(default=False, help_text='Afficher en priorité')),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'FAQ',
                'verbose_name_plural': 'FAQ',
                'ordering': ['order', 'question'],
            },
        ),
        migrations.CreateModel(
            name='HomePageStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clients_count', models.PositiveIntegerField(default=1000, help_text='Nombre de clients satisfaits')),
                ('projects_count', models.PositiveIntegerField(default=5000, help_text='Nombre de projets réalisés')),
                ('experience_years', models.PositiveIntegerField(default=10, help_text="Années d'expérience")),
                ('team_members', models.PositiveIntegerField(default=25, help_text="Membres de l'équipe")),
                ('products_sold', models.PositiveIntegerField(default=10000, help_text='Produits vendus')),
                ('cities_served', models.PositiveIntegerField(default=18, help_text='Villes desservies')),
                ('support_hours', models.CharField(default='24/7', help_text='Heures de support', max_length=10)),
                ('satisfaction_rate', models.PositiveIntegerField(default=98, help_text='Taux de satisfaction (%)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': "Statistiques de la page d'accueil",
                'verbose_name_plural': "Statistiques de la page d'accueil",
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsletterSubscriber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('name', models.CharField(blank=True, max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('confirmed', models.BooleanField(default=False)),
                ('confirmation_token', models.CharField(blank=True, max_length=100)),
                ('frequency', models.CharField(choices=[('weekly', 'Hebdomadaire'), ('monthly', 'Mensuel'), ('quarterly', 'Trimestriel')], default='monthly', max_length=20)),
                ('subscribed_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('unsubscribed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Abonné newsletter',
                'verbose_name_plural': 'Abonnés newsletter',
                'ordering': ['-subscribed_at'],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('short_description', models.CharField(help_text="Description courte pour la page d'accueil", max_length=200)),
                ('icon', models.CharField(help_text="Nom de l'icône Lucide React", max_length=50)),
                ('color', models.CharField(default='blue', help_text='Couleur du thème (blue, green, purple, etc.)', max_length=20)),
                ('slug', models.SlugField(unique=True)),
                ('featured', models.BooleanField(default=False, help_text="Afficher sur la page d'accueil")),
                ('order', models.PositiveIntegerField(default=0, help_text="Ordre d'affichage")),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='JOSNET NETWORK', max_length=100)),
                ('site_description', models.TextField(default='Votre partenaire IT & Télécom de confiance au Burundi')),
                ('site_logo', models.ImageField(blank=True, null=True, upload_to='site/')),
                ('phone_primary', models.CharField(default='+257 12 345 678', max_length=20)),
                ('phone_secondary', models.CharField(blank=True, max_length=20)),
                ('email_primary', models.EmailField(default='<EMAIL>', max_length=254)),
                ('email_secondary', models.EmailField(blank=True, max_length=254)),
                ('address_line1', models.CharField(default='123 Avenue Principale', max_length=200)),
                ('address_line2', models.CharField(blank=True, max_length=200)),
                ('city', models.CharField(default='Bujumbura', max_length=100)),
                ('country', models.CharField(default='Burundi', max_length=100)),
                ('business_hours', models.TextField(default='Lun-Ven: 8h00-18h00, Sam: 8h00-13h00')),
                ('facebook_url', models.URLField(blank=True)),
                ('twitter_url', models.URLField(blank=True)),
                ('linkedin_url', models.URLField(blank=True)),
                ('instagram_url', models.URLField(blank=True)),
                ('meta_description', models.TextField(blank=True, max_length=160)),
                ('meta_keywords', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Configuration du site',
                'verbose_name_plural': 'Configurations du site',
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.CharField(max_length=100)),
                ('client_title', models.CharField(blank=True, help_text='Poste ou titre du client', max_length=100)),
                ('client_company', models.CharField(blank=True, max_length=100)),
                ('client_photo', models.ImageField(blank=True, null=True, upload_to='testimonials/')),
                ('content', models.TextField(help_text='Contenu du témoignage')),
                ('rating', models.PositiveIntegerField(default=5, help_text='Note sur 5', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('featured', models.BooleanField(default=False, help_text="Afficher sur la page d'accueil")),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Témoignage',
                'verbose_name_plural': 'Témoignages',
                'ordering': ['order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BlogPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('excerpt', models.TextField(help_text="Résumé de l'article", max_length=300)),
                ('content', models.TextField()),
                ('featured_image', models.ImageField(blank=True, null=True, upload_to='blog/')),
                ('featured_image_alt', models.CharField(blank=True, max_length=200)),
                ('tags', models.CharField(blank=True, help_text='Tags séparés par des virgules', max_length=500)),
                ('author_name', models.CharField(max_length=100)),
                ('author_email', models.EmailField(blank=True, max_length=254)),
                ('author_bio', models.TextField(blank=True)),
                ('author_avatar', models.ImageField(blank=True, null=True, upload_to='authors/')),
                ('status', models.CharField(choices=[('draft', 'Brouillon'), ('published', 'Publié'), ('archived', 'Archivé')], default='draft', max_length=20)),
                ('featured', models.BooleanField(default=False, help_text='Article mis en avant')),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('meta_description', models.CharField(blank=True, max_length=160)),
                ('meta_keywords', models.CharField(blank=True, max_length=500)),
                ('views_count', models.PositiveIntegerField(default=0)),
                ('likes_count', models.PositiveIntegerField(default=0)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='posts', to='core.blogcategory')),
            ],
            options={
                'verbose_name': 'Article de blog',
                'verbose_name_plural': 'Articles de blog',
                'ordering': ['-published_at', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BlogComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('author_name', models.CharField(max_length=100)),
                ('author_email', models.EmailField(max_length=254)),
                ('content', models.TextField()),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('approved', 'Approuvé'), ('rejected', 'Rejeté')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='core.blogpost')),
            ],
            options={
                'verbose_name': 'Commentaire',
                'verbose_name_plural': 'Commentaires',
                'ordering': ['-created_at'],
            },
        ),
    ]
