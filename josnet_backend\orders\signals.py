"""
Signaux Django pour la gestion automatique du stock lors des commandes.
"""

from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.db import transaction
from django.utils import timezone
import logging

from .models import Order, OrderItem, OrderStatusHistory
from products.models import Inventory
from notifications.email_service import EmailNotificationService

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Order)
def handle_order_status_change(sender, instance, created, **kwargs):
    """
    Gérer les changements de statut de commande et ajuster le stock en conséquence.
    """
    if created:
        # Nouvelle commande créée - réserver le stock
        reserve_stock_for_order(instance)
        # Envoyer un email de confirmation
        send_order_creation_email(instance)
    else:
        # Commande mise à jour - vérifier les changements de statut
        handle_order_status_update(instance)
        # Envoyer un email de mise à jour si le statut a changé
        send_order_status_email(instance)

def reserve_stock_for_order(order):
    """
    Réserver le stock pour une nouvelle commande.
    """
    try:
        with transaction.atomic():
            for item in order.items.all():
                # Déterminer quel inventaire utiliser (produit ou variante)
                inventory = None
                if item.variant:
                    try:
                        inventory = item.variant.inventory
                    except Inventory.DoesNotExist:
                        logger.warning(f"Pas d'inventaire trouvé pour la variante {item.variant.id}")
                elif item.product:
                    try:
                        inventory = item.product.inventory
                    except Inventory.DoesNotExist:
                        logger.warning(f"Pas d'inventaire trouvé pour le produit {item.product.id}")
                
                if inventory:
                    # Réserver le stock
                    success = inventory.reserve_stock(
                        quantity=item.quantity,
                        reference=f"ORDER-{order.order_number}",
                        user=order.user
                    )
                    
                    if not success:
                        logger.error(f"Impossible de réserver {item.quantity} unités pour {item.product_name} (Commande {order.order_number})")
                        # Optionnel: Marquer la commande comme problématique
                        order.admin_notes = f"ATTENTION: Stock insuffisant pour {item.product_name}"
                        order.save()
                    else:
                        logger.info(f"Stock réservé: {item.quantity} unités de {item.product_name} pour commande {order.order_number}")
                else:
                    logger.warning(f"Aucun inventaire trouvé pour l'article {item.product_name} dans la commande {order.order_number}")
                    
    except Exception as e:
        logger.error(f"Erreur lors de la réservation de stock pour la commande {order.order_number}: {e}")

def handle_order_status_update(order):
    """
    Gérer les mises à jour de statut de commande.
    """
    try:
        # Récupérer le statut précédent depuis l'historique
        previous_status_history = order.status_history.exclude(status=order.status).first()
        previous_status = previous_status_history.status if previous_status_history else None
        
        if previous_status != order.status:
            logger.info(f"Changement de statut pour commande {order.order_number}: {previous_status} -> {order.status}")
            
            # Gérer les différents changements de statut
            if order.status == 'cancelled':
                release_reserved_stock(order)
            elif order.status == 'shipped':
                confirm_stock_reduction(order)
            elif order.status == 'delivered':
                # Le stock a déjà été réduit lors de l'expédition
                pass
            elif order.status == 'refunded' or order.status == 'partially_refunded':
                handle_refund_stock_return(order)
                
    except Exception as e:
        logger.error(f"Erreur lors de la gestion du changement de statut pour la commande {order.order_number}: {e}")

def release_reserved_stock(order):
    """
    Libérer le stock réservé pour une commande annulée.
    """
    try:
        with transaction.atomic():
            for item in order.items.all():
                inventory = get_inventory_for_item(item)
                if inventory:
                    success = inventory.release_reservation(
                        reference=f"ORDER-{order.order_number}",
                        user=order.user
                    )
                    
                    if success:
                        logger.info(f"Stock libéré: {item.quantity} unités de {item.product_name} pour commande annulée {order.order_number}")
                    else:
                        logger.warning(f"Impossible de libérer le stock pour {item.product_name} (Commande {order.order_number})")
                        
    except Exception as e:
        logger.error(f"Erreur lors de la libération du stock pour la commande {order.order_number}: {e}")

def confirm_stock_reduction(order):
    """
    Confirmer la réduction de stock lors de l'expédition.
    """
    try:
        with transaction.atomic():
            for item in order.items.all():
                inventory = get_inventory_for_item(item)
                if inventory:
                    # Libérer la réservation et réduire le stock réel
                    inventory.release_reservation(
                        reference=f"ORDER-{order.order_number}",
                        user=order.user
                    )
                    
                    # Réduire le stock physique
                    movement = inventory.adjust_stock(
                        quantity=-item.quantity,
                        reason='sale',
                        reference=f"ORDER-{order.order_number}",
                        user=order.user
                    )
                    
                    logger.info(f"Stock réduit: -{item.quantity} unités de {item.product_name} pour commande expédiée {order.order_number}")
                    
    except Exception as e:
        logger.error(f"Erreur lors de la réduction du stock pour la commande {order.order_number}: {e}")

def handle_refund_stock_return(order):
    """
    Gérer le retour de stock lors d'un remboursement.
    """
    try:
        with transaction.atomic():
            # Récupérer les articles remboursés
            refunds = order.refunds.filter(status='completed')
            
            for refund in refunds:
                if refund.refund_type == 'full':
                    # Remboursement complet - remettre tout le stock
                    for item in order.items.all():
                        inventory = get_inventory_for_item(item)
                        if inventory:
                            movement = inventory.adjust_stock(
                                quantity=item.quantity,
                                reason='return',
                                reference=f"REFUND-{refund.refund_number}",
                                user=refund.processed_by
                            )
                            logger.info(f"Stock retourné: +{item.quantity} unités de {item.product_name} pour remboursement {refund.refund_number}")
                            
                elif refund.refund_type in ['partial', 'item']:
                    # Remboursement partiel - remettre seulement les articles concernés
                    for item in refund.items.all():
                        inventory = get_inventory_for_item(item)
                        if inventory:
                            movement = inventory.adjust_stock(
                                quantity=item.quantity,
                                reason='return',
                                reference=f"REFUND-{refund.refund_number}",
                                user=refund.processed_by
                            )
                            logger.info(f"Stock retourné (partiel): +{item.quantity} unités de {item.product_name} pour remboursement {refund.refund_number}")
                            
    except Exception as e:
        logger.error(f"Erreur lors du retour de stock pour la commande {order.order_number}: {e}")

def get_inventory_for_item(order_item):
    """
    Obtenir l'inventaire approprié pour un article de commande.
    """
    try:
        if order_item.variant:
            return order_item.variant.inventory
        elif order_item.product:
            return order_item.product.inventory
    except Inventory.DoesNotExist:
        pass
    return None

@receiver(post_save, sender=OrderItem)
def handle_order_item_change(sender, instance, created, **kwargs):
    """
    Gérer les changements d'articles de commande.
    """
    if created and instance.order.status not in ['cancelled', 'refunded']:
        # Nouvel article ajouté à une commande existante
        inventory = get_inventory_for_item(instance)
        if inventory:
            success = inventory.reserve_stock(
                quantity=instance.quantity,
                reference=f"ORDER-{instance.order.order_number}",
                user=instance.order.user
            )
            
            if success:
                logger.info(f"Stock réservé pour nouvel article: {instance.quantity} unités de {instance.product_name}")
            else:
                logger.warning(f"Stock insuffisant pour nouvel article: {instance.product_name}")

@receiver(pre_delete, sender=OrderItem)
def handle_order_item_deletion(sender, instance, **kwargs):
    """
    Gérer la suppression d'articles de commande.
    """
    if instance.order.status not in ['cancelled', 'refunded', 'shipped', 'delivered']:
        # Libérer le stock réservé pour cet article
        inventory = get_inventory_for_item(instance)
        if inventory:
            # Calculer la quantité à libérer
            reserved_quantity = instance.quantity
            
            success = inventory.release_reservation(
                reference=f"ORDER-{instance.order.order_number}",
                quantity=reserved_quantity,
                user=instance.order.user
            )
            
            if success:
                logger.info(f"Stock libéré pour article supprimé: {reserved_quantity} unités de {instance.product_name}")
            else:
                logger.warning(f"Impossible de libérer le stock pour article supprimé: {instance.product_name}")

# Fonction utilitaire pour vérifier la disponibilité du stock
def check_stock_availability(order_items):
    """
    Vérifier la disponibilité du stock pour une liste d'articles.
    
    Args:
        order_items: Liste d'articles de commande ou dictionnaires avec 'product'/'variant' et 'quantity'
        
    Returns:
        dict: {
            'available': bool,
            'issues': [{'item': str, 'requested': int, 'available': int}]
        }
    """
    issues = []
    
    for item in order_items:
        if hasattr(item, 'product'):
            # C'est un OrderItem
            inventory = get_inventory_for_item(item)
            requested_qty = item.quantity
            item_name = item.product_name
        else:
            # C'est un dictionnaire
            product = item.get('product')
            variant = item.get('variant')
            requested_qty = item.get('quantity', 0)
            
            if variant:
                try:
                    inventory = variant.inventory
                    item_name = f"{product.name} - {variant.name}" if product else variant.name
                except Inventory.DoesNotExist:
                    inventory = None
                    item_name = f"{product.name} - {variant.name}" if product else variant.name
            elif product:
                try:
                    inventory = product.inventory
                    item_name = product.name
                except Inventory.DoesNotExist:
                    inventory = None
                    item_name = product.name
            else:
                continue
        
        if inventory:
            available_qty = inventory.available_quantity
            if requested_qty > available_qty:
                issues.append({
                    'item': item_name,
                    'requested': requested_qty,
                    'available': available_qty
                })
        else:
            issues.append({
                'item': item_name,
                'requested': requested_qty,
                'available': 0
            })
    
    return {
        'available': len(issues) == 0,
        'issues': issues
    }

def send_order_creation_email(order):
    """
    Envoyer un email de confirmation lors de la création d'une commande.
    """
    try:
        status_message = f"Votre commande #{order.id} a été créée avec succès et est en cours de traitement."

        EmailNotificationService.send_order_email(
            user=order.user,
            order=order,
            status_message=status_message
        )

        logger.info(f"Email de création de commande envoyé pour la commande #{order.id}")

    except Exception as e:
        logger.error(f"Erreur lors de l'envoi d'email pour la nouvelle commande #{order.id}: {e}")

def send_order_status_email(order):
    """
    Envoyer un email lors du changement de statut d'une commande.
    """
    try:
        # Récupérer le statut précédent depuis l'historique
        previous_status_history = order.status_history.exclude(status=order.status).first()
        previous_status = previous_status_history.status if previous_status_history else None

        if previous_status and previous_status != order.status:
            # Le statut a changé
            status_messages = {
                'pending': "Votre commande est en attente de confirmation.",
                'confirmed': "Votre commande a été confirmée et va être préparée.",
                'processing': "Votre commande est en cours de préparation.",
                'shipped': "Votre commande a été expédiée ! Vous devriez la recevoir bientôt.",
                'delivered': "Votre commande a été livrée avec succès. Merci pour votre achat !",
                'cancelled': "Votre commande a été annulée. Si vous avez des questions, contactez notre service client.",
                'refunded': "Votre commande a été remboursée. Le montant sera crédité sur votre compte sous 3-5 jours ouvrables."
            }

            status_message = status_messages.get(
                order.status,
                f"Le statut de votre commande a été mis à jour : {order.get_status_display()}"
            )

            EmailNotificationService.send_order_email(
                user=order.user,
                order=order,
                status_message=status_message
            )

            logger.info(f"Email de changement de statut envoyé pour la commande #{order.id} ({previous_status} -> {order.status})")

    except Exception as e:
        logger.error(f"Erreur lors de l'envoi d'email pour le changement de statut de la commande #{order.id}: {e}")
