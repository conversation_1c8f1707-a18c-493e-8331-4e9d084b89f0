#!/usr/bin/env python
"""
Test avec correction des signaux et envoi réel d'emails à <EMAIL>
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from django.conf import settings
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category, Promotion

User = get_user_model()

# Adresse email réelle à tester
REAL_EMAIL = "<EMAIL>"


def setup_real_email_subscription():
    """Configurer l'abonnement pour l'adresse email réelle."""
    print("🔧 Configuration de l'abonnement email réel")
    print("=" * 50)
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Admin',
            'last_name': 'FixedTest',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
    
    # Créer un utilisateur avec l'adresse email réelle
    real_user, created = User.objects.get_or_create(
        email=REAL_EMAIL,
        defaults={
            'password': 'testpass123',
            'first_name': 'Alain',
            'last_name': 'Asifiwe'
        }
    )
    if created:
        real_user.set_password('testpass123')
        real_user.save()
    
    # Créer ou mettre à jour l'abonnement newsletter
    subscription, created = NewsletterSubscription.objects.get_or_create(
        email=REAL_EMAIL,
        defaults={
            'user': real_user,
            'preferences_promotions': True,
            'preferences_new_products': True,
            'preferences_newsletters': True,
            'preferences_order_updates': True
        }
    )
    
    if not created:
        subscription.user = real_user
        subscription.preferences_promotions = True
        subscription.preferences_new_products = True
        subscription.preferences_newsletters = True
        subscription.preferences_order_updates = True
        subscription.is_active = True
        subscription.save()
    
    print(f"✅ Abonnement configuré pour: {subscription.email}")
    print(f"   🎉 Promotions: {'✅' if subscription.preferences_promotions else '❌'}")
    print(f"   ✨ Nouveaux produits: {'✅' if subscription.preferences_new_products else '❌'}")
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Fixed Test Category',
        defaults={'slug': 'fixed-test-category'}
    )
    
    return admin_user, real_user, subscription, category


def test_product_signal_fix():
    """Test spécifique pour vérifier que les signaux produits fonctionnent."""
    print("\n🔧 TEST: Correction des signaux produits")
    print("=" * 50)
    
    admin_user, real_user, subscription, category = setup_real_email_subscription()
    
    # Nettoyer les anciens produits
    Product.objects.filter(name__contains='Fixed Signal Test').delete()
    NewsletterCampaign.objects.filter(title__contains='Fixed Signal Test').delete()
    
    print(f"\n🔄 Test 1: Produit créé en brouillon puis publié")
    
    # Créer un produit en brouillon
    product = Product.objects.create(
        name='Fixed Signal Test - MacBook Pro M4',
        slug='fixed-signal-test-macbook-pro-m4',
        sku='FST001',
        description='Test de correction des signaux pour nouveaux produits.',
        short_description='MacBook Pro M4 - Test signaux corrigés',
        price=Decimal('2999.99'),
        status='draft',  # Créé en brouillon
        created_by=admin_user
    )
    product.categories.add(category)
    
    print(f"   ✅ Produit créé en brouillon: {product.name}")
    print(f"   📊 Statut initial: {product.status}")
    
    # Vérifier qu'aucune campagne n'existe
    campaigns_before = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Fixed Signal Test'
    ).count()
    print(f"   📨 Campagnes avant publication: {campaigns_before}")
    
    # Publier le produit (cela devrait déclencher le signal)
    print(f"\n🚀 Publication du produit...")
    product.status = 'published'
    product.save()
    
    print(f"   ✅ Produit publié: {product.status}")
    
    # Vérifier les campagnes après publication
    campaigns_after = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Fixed Signal Test'
    )
    print(f"   📨 Campagnes après publication: {campaigns_after.count()}")
    
    if campaigns_after.exists():
        campaign = campaigns_after.first()
        print(f"\n📧 CAMPAGNE CRÉÉE:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        
        # Vérifier que l'email a été envoyé à notre adresse réelle
        product_subscribers = NewsletterSubscription.objects.filter(
            is_active=True,
            preferences_new_products=True
        ).count()
        
        if campaign.total_recipients >= 1:  # Au moins notre adresse
            print(f"   ✅ Email envoyé à {REAL_EMAIL} et {campaign.total_recipients-1} autres abonnés")
            return True
        else:
            print(f"   ❌ Aucun destinataire trouvé")
            return False
    else:
        print(f"   ❌ Aucune campagne créée - Les signaux ne fonctionnent pas")
        return False


def test_with_console_backend():
    """Test avec backend console pour voir les emails."""
    print("\n📧 TEST: Envoi avec backend console")
    print("=" * 50)
    
    # Forcer le backend console pour voir les emails
    with override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend'):
        success = test_product_signal_fix()
        return success


def show_email_configuration_instructions():
    """Afficher les instructions pour configurer l'envoi réel d'emails."""
    print("\n📧 CONFIGURATION POUR VRAIS EMAILS")
    print("=" * 60)
    print(f"Pour envoyer de vrais emails à {REAL_EMAIL}, suivez ces étapes:")
    print()
    print("1. 🔐 CONFIGURATION GMAIL:")
    print("   - Allez sur https://myaccount.google.com/security")
    print("   - Activez l'authentification à 2 facteurs")
    print("   - Générez un 'Mot de passe d'application' pour 'JosNet'")
    print()
    print("2. ⚙️  MODIFICATION DU FICHIER settings.py:")
    print("   Ajoutez ces lignes à la fin du fichier:")
    print()
    print("   # Configuration email réel")
    print("   EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'")
    print("   EMAIL_HOST = 'smtp.gmail.com'")
    print("   EMAIL_PORT = 587")
    print("   EMAIL_USE_TLS = True")
    print(f"   EMAIL_HOST_USER = '{REAL_EMAIL}'")
    print("   EMAIL_HOST_PASSWORD = 'VOTRE-MOT-DE-PASSE-APP'  # Mot de passe généré")
    print("   DEFAULT_FROM_EMAIL = 'JosNet <<EMAIL>>'")
    print()
    print("3. 🔄 REDÉMARRAGE:")
    print("   - Redémarrez le serveur Django")
    print("   - Relancez ce test")
    print()
    print("4. ✅ VÉRIFICATION:")
    print(f"   - Vérifiez votre boîte email {REAL_EMAIL}")
    print("   - Vous devriez recevoir les emails de test")
    print()


def main():
    """Fonction principale."""
    print("🚀 TEST CORRIGÉ AVEC ADRESSE EMAIL RÉELLE")
    print("=" * 80)
    print(f"📧 Adresse email de test: {REAL_EMAIL}")
    print("=" * 80)
    
    try:
        # Test avec backend console pour voir si les signaux fonctionnent
        print("🧪 PHASE 1: Test des signaux corrigés")
        signal_success = test_with_console_backend()
        
        # Afficher les instructions pour l'envoi réel
        show_email_configuration_instructions()
        
        # Résumé
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ DU TEST CORRIGÉ")
        print("=" * 80)
        
        if signal_success:
            print("✅ SIGNAUX CORRIGÉS: Les emails de nouveaux produits fonctionnent maintenant!")
            print("✅ SYSTÈME OPÉRATIONNEL: Prêt pour l'envoi de vrais emails")
            print(f"✅ DESTINATAIRE CONFIGURÉ: {REAL_EMAIL} recevra les emails")
            print()
            print("🎯 PROCHAINES ÉTAPES:")
            print("   1. Configurez le SMTP Gmail selon les instructions ci-dessus")
            print("   2. Redémarrez le serveur Django")
            print("   3. Publiez une vraie promotion ou un nouveau produit")
            print(f"   4. Vérifiez votre boîte email {REAL_EMAIL}")
        else:
            print("❌ PROBLÈME PERSISTANT: Les signaux ne fonctionnent toujours pas")
            print("⚠️  Vérifiez que les signaux sont bien importés dans apps.py")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
