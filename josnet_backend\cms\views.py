from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import (
    Page, BlogCategory, BlogPost, Comment,
    Media, Menu, MenuItem, SiteSetting
)
from .serializers import (
    PageSerializer, BlogCategorySerializer,
    BlogPostListSerializer, BlogPostDetailSerializer,
    CommentSerializer, MediaSerializer,
    MenuSerializer, MenuItemSerializer, SiteSettingSerializer
)
from .permissions import IsAdminOrReadOnly

class PageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for pages
    """
    queryset = Page.objects.all()
    serializer_class = PageSerializer
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'template']
    search_fields = ['title', 'content']
    ordering_fields = ['title', 'created_at', 'updated_at']
    lookup_field = 'slug'

    def perform_create(self, serializer):
        serializer.save(author=self.request.user)

class BlogCategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for blog categories
    """
    queryset = BlogCategory.objects.all()
    serializer_class = BlogCategorySerializer
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    lookup_field = 'slug'

class BlogPostViewSet(viewsets.ModelViewSet):
    """
    API endpoint for blog posts
    """
    queryset = BlogPost.objects.all()
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'categories']
    search_fields = ['title', 'content', 'excerpt']
    ordering_fields = ['title', 'created_at', 'updated_at']
    ordering = ['-created_at']  # Par défaut, trier par date de création décroissante
    lookup_field = 'slug'

    def get_queryset(self):
        queryset = super().get_queryset()

        # Pour les utilisateurs non-admin, ne montrer que les articles publiés
        if not (self.request.user and self.request.user.is_staff):
            queryset = queryset.filter(status='published')

        # Filtrer par featured si demandé
        featured = self.request.query_params.get('featured', None)
        if featured is not None:
            if featured.lower() in ['true', '1']:
                queryset = queryset.filter(featured=True)
            elif featured.lower() in ['false', '0']:
                queryset = queryset.filter(featured=False)

        return queryset

    def get_serializer_class(self):
        if self.action == 'list':
            return BlogPostListSerializer
        return BlogPostDetailSerializer

    def perform_create(self, serializer):
        serializer.save(author=self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def add_comment(self, request, slug=None):
        post = self.get_object()
        serializer = CommentSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(post=post, user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CommentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for comments
    """
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['post', 'status']
    ordering_fields = ['created_at']

    def get_permissions(self):
        if self.action in ['update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class MediaViewSet(viewsets.ModelViewSet):
    """
    API endpoint for media files
    """
    queryset = Media.objects.all()
    serializer_class = MediaSerializer
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['file_type']
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'created_at']

    def perform_create(self, serializer):
        serializer.save(uploader=self.request.user)

class MenuViewSet(viewsets.ModelViewSet):
    """
    API endpoint for menus
    """
    queryset = Menu.objects.all()
    serializer_class = MenuSerializer
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name']
    ordering_fields = ['name']

    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        menu = self.get_object()
        items = MenuItem.objects.filter(menu=menu)
        serializer = MenuItemSerializer(items, many=True)
        return Response(serializer.data)

class MenuItemViewSet(viewsets.ModelViewSet):
    """
    API endpoint for menu items
    """
    queryset = MenuItem.objects.all()
    serializer_class = MenuItemSerializer
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['menu', 'parent']
    ordering_fields = ['order']

class SiteSettingViewSet(viewsets.ModelViewSet):
    """
    API endpoint for site settings
    """
    queryset = SiteSetting.objects.all()
    serializer_class = SiteSettingSerializer
    permission_classes = [IsAdminOrReadOnly]

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current site settings"""
        settings = SiteSetting.objects.first()
        if not settings:
            return Response({"detail": "No site settings found"}, status=status.HTTP_404_NOT_FOUND)
        serializer = self.get_serializer(settings)
        return Response(serializer.data)
