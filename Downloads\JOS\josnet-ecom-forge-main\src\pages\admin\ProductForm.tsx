import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Helmet } from 'react-helmet';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import ProductImageManager from '@/components/admin/products/ProductImageManager';
import ProductVariantManager from '@/components/admin/products/ProductVariantManager';
import InventoryManager from '@/components/admin/products/InventoryManager';
import ImportExportManager from '@/components/admin/products/ImportExportManager';
import RichTextEditor from '@/components/admin/products/RichTextEditor';
import ProductPreview from '@/components/admin/products/ProductPreview';
import PromotionManager from '@/components/admin/products/PromotionManager';
import RelatedProductsManager from '@/components/admin/products/RelatedProductsManager';
import ProductSalesStats from '@/components/admin/products/ProductSalesStats';
import CustomerProductInsights from '@/components/admin/products/CustomerProductInsights';
import ProductCampaignManager from '@/components/admin/products/ProductCampaignManager';
import ProductDashboard from '@/components/admin/products/ProductDashboard';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { productApi, ProductDetail, ProductCreateUpdate } from '@/services/productApi';
import { ArrowLeft, Save, Trash2 } from 'lucide-react';

// Form validation schema
const productSchema = z.object({
  name: z.string().min(3, 'Le nom doit contenir au moins 3 caractères'),
  slug: z.string().optional(),
  sku: z.string().optional(),
  description: z.string().min(10, 'La description doit contenir au moins 10 caractères'),
  short_description: z.string().optional(),
  price: z.coerce.number().min(0, 'Le prix doit être positif'),
  sale_price: z.coerce.number().min(0, 'Le prix de vente doit être positif').optional().nullable(),
  cost_price: z.coerce.number().min(0, 'Le prix de revient doit être positif').optional().nullable(),
  categories: z.array(z.number()).min(1, 'Sélectionnez au moins une catégorie'),
  is_featured: z.boolean().default(false),
  is_digital: z.boolean().default(false),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
});

type ProductFormValues = z.infer<typeof productSchema>;

const ProductForm: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const isEditMode = !!slug && slug !== 'new';

  // Fetch product data if in edit mode
  const {
    data: product,
    isLoading: isLoadingProduct,
    isError: isProductError
  } = useQuery({
    queryKey: ['product', slug],
    queryFn: () => productApi.getProduct(slug!),
    enabled: isEditMode,
  });

  // Fetch categories
  const {
    data: categories = [],
    isLoading: isLoadingCategories
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => productApi.getCategories(),
  });

  // Form setup
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      slug: '',
      sku: '',
      description: '',
      short_description: '',
      price: 0,
      sale_price: null,
      cost_price: null,
      categories: [],
      is_featured: false,
      is_digital: false,
      status: 'draft',
    },
  });

  // Update form values when product data is loaded
  useEffect(() => {
    if (product && isEditMode) {
      form.reset({
        name: product.name,
        slug: product.slug,
        sku: product.sku,
        description: product.description,
        short_description: product.short_description || '',
        price: product.price,
        sale_price: product.sale_price || null,
        cost_price: null, // Backend doesn't return cost_price for security
        categories: product.categories.map(c => c.id),
        is_featured: product.is_featured,
        is_digital: product.is_digital,
        status: product.status,
      });
    }
  }, [product, isEditMode, form]);

  // Create product mutation
  const createProductMutation = useMutation({
    mutationFn: (data: ProductCreateUpdate) => productApi.createProduct(data),
    onSuccess: (data) => {
      toast({
        title: 'Produit créé',
        description: 'Le produit a été créé avec succès.',
      });
      queryClient.invalidateQueries({ queryKey: ['products'] });
      navigate(`/admin/products/edit/${data.slug}`);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la création du produit.',
        variant: 'destructive',
      });
    },
  });

  // Update product mutation
  const updateProductMutation = useMutation({
    mutationFn: ({ slug, data }: { slug: string; data: ProductCreateUpdate }) =>
      productApi.updateProduct(slug, data),
    onSuccess: () => {
      toast({
        title: 'Produit mis à jour',
        description: 'Le produit a été mis à jour avec succès.',
      });
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product', slug] });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la mise à jour du produit.',
        variant: 'destructive',
      });
    },
  });

  // Form submission handler
  const onSubmit = (values: ProductFormValues) => {
    // Clean up values
    const productData: ProductCreateUpdate = {
      ...values,
      sale_price: values.sale_price || undefined,
      cost_price: values.cost_price || undefined,
      slug: values.slug || undefined,
      sku: values.sku || undefined,
      short_description: values.short_description || undefined,
    };

    if (isEditMode) {
      updateProductMutation.mutate({ slug: slug!, data: productData });
    } else {
      createProductMutation.mutate(productData);
    }
  };

  // Loading state
  if (isEditMode && isLoadingProduct) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-6">
          <Skeleton className="h-10 w-1/3 mb-4" />
          <Skeleton className="h-6 w-1/4 mb-8" />
          <div className="grid gap-6">
            <Skeleton className="h-96 w-full" />
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Error state
  if (isEditMode && isProductError) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            Une erreur est survenue lors du chargement du produit. Veuillez réessayer.
          </div>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => navigate('/admin/products')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour à la liste des produits
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Helmet>
        <title>{isEditMode ? 'Modifier le produit' : 'Nouveau produit'} | Admin JosNet</title>
      </Helmet>

      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {isEditMode ? 'Modifier le produit' : 'Nouveau produit'}
            </h1>
            <p className="text-muted-foreground">
              {isEditMode
                ? 'Modifiez les informations du produit'
                : 'Créez un nouveau produit dans votre catalogue'}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => navigate('/admin/products')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Annuler
            </Button>
            <Button
              type="submit"
              form="product-form"
              disabled={createProductMutation.isPending || updateProductMutation.isPending}
            >
              <Save className="mr-2 h-4 w-4" />
              Enregistrer
            </Button>
          </div>
        </div>

        <Form {...form}>
          <form id="product-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Tabs defaultValue="general">
              <TabsList className="mb-4 flex flex-wrap">
                <TabsTrigger value="general">Général</TabsTrigger>
                <TabsTrigger value="pricing">Prix</TabsTrigger>
                <TabsTrigger value="organization">Organisation</TabsTrigger>
                {isEditMode && (
                  <>
                    <TabsTrigger value="images">Images</TabsTrigger>
                    <TabsTrigger value="variants">Variantes</TabsTrigger>
                    <TabsTrigger value="inventory">Stock</TabsTrigger>
                    <TabsTrigger value="related">Produits liés</TabsTrigger>
                    <TabsTrigger value="promotions">Promotions</TabsTrigger>
                    <TabsTrigger value="preview">Prévisualisation</TabsTrigger>
                    <TabsTrigger value="analytics">Analytiques</TabsTrigger>
                    <TabsTrigger value="marketing">Marketing</TabsTrigger>
                    <TabsTrigger value="import-export">Import/Export</TabsTrigger>
                  </>
                )}
              </TabsList>

              {/* General Tab */}
              <TabsContent value="general">
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid gap-6">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Nom du produit</FormLabel>
                            <FormControl>
                              <Input placeholder="Nom du produit" {...field} />
                            </FormControl>
                            <FormDescription>
                              Le nom du produit tel qu'il apparaîtra sur le site.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="slug"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Slug</FormLabel>
                              <FormControl>
                                <Input placeholder="slug-du-produit" {...field} />
                              </FormControl>
                              <FormDescription>
                                L'identifiant URL du produit. Laissez vide pour générer automatiquement.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="sku"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>SKU</FormLabel>
                              <FormControl>
                                <Input placeholder="PRD-12345" {...field} />
                              </FormControl>
                              <FormDescription>
                                Code unique du produit. Laissez vide pour générer automatiquement.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <RichTextEditor
                                value={field.value || ''}
                                onChange={field.onChange}
                                placeholder="Description détaillée du produit"
                                minHeight="200px"
                              />
                            </FormControl>
                            <FormDescription>
                              Description complète du produit.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="short_description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description courte</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Brève description du produit"
                                className="min-h-20"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Résumé court du produit pour les listes et aperçus.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Pricing Tab */}
              <TabsContent value="pricing">
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid gap-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name="price"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Prix normal</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  placeholder="0.00"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Prix de vente standard du produit.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="sale_price"
                          render={({ field: { value, onChange, ...field } }) => (
                            <FormItem>
                              <FormLabel>Prix promotionnel</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  placeholder="0.00"
                                  value={value === null ? '' : value}
                                  onChange={e => onChange(e.target.value === '' ? null : Number(e.target.value))}
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Prix en promotion (laissez vide si pas de promotion).
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="cost_price"
                          render={({ field: { value, onChange, ...field } }) => (
                            <FormItem>
                              <FormLabel>Prix de revient</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  placeholder="0.00"
                                  value={value === null ? '' : value}
                                  onChange={e => onChange(e.target.value === '' ? null : Number(e.target.value))}
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Coût d'achat ou de production (interne uniquement).
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Organization Tab */}
              <TabsContent value="organization">
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid gap-6">
                      <FormField
                        control={form.control}
                        name="categories"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Catégories</FormLabel>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-2">
                              {isLoadingCategories ? (
                                <Skeleton className="h-8 w-full" />
                              ) : (
                                categories.map((category) => (
                                  <div key={category.id} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`category-${category.id}`}
                                      checked={field.value.includes(category.id)}
                                      onCheckedChange={(checked) => {
                                        if (checked) {
                                          field.onChange([...field.value, category.id]);
                                        } else {
                                          field.onChange(field.value.filter(id => id !== category.id));
                                        }
                                      }}
                                    />
                                    <label
                                      htmlFor={`category-${category.id}`}
                                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                    >
                                      {category.name}
                                    </label>
                                  </div>
                                ))
                              )}
                            </div>
                            <FormDescription>
                              Sélectionnez les catégories auxquelles ce produit appartient.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name="status"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Statut</FormLabel>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Sélectionnez un statut" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="draft">Brouillon</SelectItem>
                                  <SelectItem value="published">Publié</SelectItem>
                                  <SelectItem value="archived">Archivé</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Statut de publication du produit.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="is_featured"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Produit en vedette</FormLabel>
                                <FormDescription>
                                  Mettre ce produit en avant sur la page d'accueil et les sections spéciales.
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="is_digital"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Produit numérique</FormLabel>
                                <FormDescription>
                                  Ce produit est numérique et ne nécessite pas d'expédition.
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Images Tab */}
              {isEditMode && (
                <TabsContent value="images">
                  <ProductImageManager productId={product?.id || 0} />
                </TabsContent>
              )}

              {/* Variants Tab */}
              {isEditMode && (
                <TabsContent value="variants">
                  <ProductVariantManager
                    productId={product?.id || 0}
                    productPrice={product?.price || 0}
                  />
                </TabsContent>
              )}

              {/* Inventory Tab */}
              {isEditMode && (
                <TabsContent value="inventory">
                  <InventoryManager productId={product?.id} />
                </TabsContent>
              )}

              {/* Related Products Tab */}
              {isEditMode && (
                <TabsContent value="related">
                  <RelatedProductsManager productId={product?.id || 0} />
                </TabsContent>
              )}

              {/* Promotions Tab */}
              {isEditMode && (
                <TabsContent value="promotions">
                  <PromotionManager productId={product?.id} />
                </TabsContent>
              )}

              {/* Preview Tab */}
              {isEditMode && (
                <TabsContent value="preview">
                  <ProductPreview product={product} />
                </TabsContent>
              )}

              {/* Analytics Tab */}
              {isEditMode && (
                <TabsContent value="analytics">
                  <div className="space-y-6">
                    <ProductDashboard productId={product?.id || 0} />
                    <ProductSalesStats productId={product?.id || 0} />
                    <CustomerProductInsights productId={product?.id || 0} />
                  </div>
                </TabsContent>
              )}

              {/* Marketing Tab */}
              {isEditMode && (
                <TabsContent value="marketing">
                  <ProductCampaignManager productId={product?.id || 0} />
                </TabsContent>
              )}

              {/* Import/Export Tab */}
              {isEditMode && (
                <TabsContent value="import-export">
                  <div className="space-y-6">
                    <ImportExportManager entityType="products" />
                    <ImportExportManager entityType="product-variants" />
                    <ImportExportManager entityType="inventory" />
                  </div>
                </TabsContent>
              )}
            </Tabs>
          </form>
        </Form>
      </div>
    </AdminLayout>
  );
};

export default ProductForm;
