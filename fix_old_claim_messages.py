#!/usr/bin/env python3
"""
Script pour corriger les anciens messages avec type d'expéditeur vide
"""

import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from orders.models_extension import Claim, ClaimMessage

def fix_empty_sender_messages():
    """Corriger les messages avec sender vide"""
    print("🔧 CORRECTION DES ANCIENS MESSAGES")
    print("=" * 50)
    
    try:
        # Récupérer tous les messages avec sender vide
        empty_messages = ClaimMessage.objects.filter(sender__in=['', None])
        
        print(f"📊 Messages avec sender vide: {empty_messages.count()}")
        
        if empty_messages.exists():
            admin_user = User.objects.get(email="<EMAIL>")
            client_user = User.objects.get(email="<EMAIL>")
            
            fixed_count = 0
            
            for message in empty_messages:
                print(f"\n📝 Message ID: {message.id}")
                print(f"   📅 Date: {message.created_at}")
                print(f"   💬 Contenu: {message.message[:50]}...")
                print(f"   👤 Réclamation de: {message.claim.user.email}")
                
                # Déterminer le type d'expéditeur basé sur le contenu et le contexte
                if "admin" in message.message.lower() or "support" in message.message.lower() or "équipe" in message.message.lower():
                    # Probablement un message admin/support
                    message.sender = 'support'
                    print(f"   🔧 Corrigé vers: 'support'")
                elif message.claim.user.email == client_user.email:
                    # Si c'est la réclamation du client, probablement un message client
                    if "test" in message.message.lower() and "admin" in message.message.lower():
                        # Message de test admin
                        message.sender = 'support'
                        print(f"   🔧 Corrigé vers: 'support'")
                    else:
                        # Probablement un message client
                        message.sender = 'customer'
                        print(f"   🔧 Corrigé vers: 'customer'")
                else:
                    # Par défaut, considérer comme support
                    message.sender = 'support'
                    print(f"   🔧 Corrigé vers: 'support' (défaut)")
                
                message.save()
                fixed_count += 1
            
            print(f"\n✅ {fixed_count} messages corrigés")
            return True
        else:
            print(f"✅ Aucun message à corriger")
            return True
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def verify_message_types():
    """Vérifier les types de messages après correction"""
    print(f"\n🔍 VÉRIFICATION APRÈS CORRECTION")
    print("=" * 50)
    
    try:
        all_messages = ClaimMessage.objects.all().order_by('created_at')
        
        print(f"📊 Total messages: {all_messages.count()}")
        
        # Compter par type
        sender_counts = {}
        for message in all_messages:
            sender = message.sender or 'VIDE'
            if sender not in sender_counts:
                sender_counts[sender] = 0
            sender_counts[sender] += 1
        
        print(f"\n📈 RÉPARTITION PAR TYPE:")
        for sender, count in sender_counts.items():
            icon = '✅' if sender != 'VIDE' else '❌'
            print(f"   {icon} '{sender}': {count} messages")
        
        # Afficher tous les messages
        print(f"\n💬 TOUS LES MESSAGES:")
        for i, message in enumerate(all_messages, 1):
            sender = message.sender or 'VIDE'
            sender_display = message.get_sender_display() if message.sender else 'Inconnu'
            content = message.message[:40]
            date = message.created_at.strftime('%Y-%m-%d %H:%M')
            
            print(f"   {i}. [{date}] '{sender}' ({sender_display}): {content}...")
        
        # Vérifier si tout est correct
        has_empty = any(not msg.sender for msg in all_messages)
        has_support = any(msg.sender == 'support' for msg in all_messages)
        has_customer = any(msg.sender == 'customer' for msg in all_messages)
        
        print(f"\n🎯 RÉSULTAT:")
        print(f"   Messages vides: {'❌' if has_empty else '✅'}")
        print(f"   Messages support: {'✅' if has_support else '❌'}")
        print(f"   Messages customer: {'✅' if has_customer else '❌'}")
        
        return not has_empty and has_support and has_customer
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_interaction_after_fix():
    """Tester l'interaction après correction"""
    print(f"\n🔄 TEST D'INTERACTION APRÈS CORRECTION")
    print("=" * 50)
    
    try:
        claim = Claim.objects.first()
        if claim:
            print(f"📋 Réclamation: {claim.claim_number}")
            print(f"👤 Client: {claim.user.email}")
            
            messages = claim.messages.all().order_by('created_at')
            print(f"💬 Messages: {messages.count()}")
            
            # Analyser la conversation
            conversation = []
            for message in messages:
                sender_type = message.sender
                sender_display = message.get_sender_display()
                conversation.append({
                    'type': sender_type,
                    'display': sender_display,
                    'content': message.message[:30]
                })
            
            print(f"\n📝 FLUX DE CONVERSATION:")
            for i, msg in enumerate(conversation, 1):
                arrow = "→" if msg['type'] == 'customer' else "←"
                print(f"   {i}. {arrow} {msg['display']}: {msg['content']}...")
            
            # Vérifier l'alternance
            has_bidirectional = False
            customer_count = sum(1 for msg in conversation if msg['type'] == 'customer')
            support_count = sum(1 for msg in conversation if msg['type'] == 'support')
            
            if customer_count > 0 and support_count > 0:
                has_bidirectional = True
                print(f"\n✅ INTERACTION BIDIRECTIONNELLE CONFIRMÉE!")
                print(f"   👤 Messages client: {customer_count}")
                print(f"   👨‍💼 Messages support: {support_count}")
            else:
                print(f"\n❌ Interaction unidirectionnelle seulement")
            
            return has_bidirectional
        else:
            print(f"❌ Aucune réclamation trouvée")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    print("🛠️ CORRECTION DES MESSAGES DE RÉCLAMATIONS")
    print("=" * 70)
    
    # 1. Corriger les messages vides
    fix_ok = fix_empty_sender_messages()
    
    # 2. Vérifier après correction
    verify_ok = verify_message_types()
    
    # 3. Tester l'interaction
    interaction_ok = test_interaction_after_fix()
    
    print(f"\n📊 RÉSUMÉ FINAL:")
    print(f"   Correction messages: {'✅' if fix_ok else '❌'}")
    print(f"   Vérification types: {'✅' if verify_ok else '❌'}")
    print(f"   Interaction bidirectionnelle: {'✅' if interaction_ok else '❌'}")
    
    if fix_ok and verify_ok and interaction_ok:
        print(f"\n🎉 SYSTÈME PARFAITEMENT CORRIGÉ!")
        print(f"   ✅ Tous les messages ont un type correct")
        print(f"   ✅ Interaction client ↔ admin fonctionnelle")
        print(f"   ✅ Système de réclamations opérationnel")
        
        print(f"\n💬 FONCTIONNEMENT CONFIRMÉ:")
        print(f"   • Client crée réclamation et peut envoyer des messages")
        print(f"   • Admin/Staff peut répondre aux réclamations")
        print(f"   • Conversation bidirectionnelle complète")
        print(f"   • Types d'expéditeurs correctement identifiés")
        print(f"   • Historique complet conservé")
        
        print(f"\n🌐 LE SYSTÈME FONCTIONNE COMME PRÉVU!")
        print(f"   Testez sur: http://localhost:8080/admin/claims")
        
    else:
        print(f"\n⚠️ CORRECTIONS PARTIELLES")
        print(f"   Certains problèmes peuvent persister")

if __name__ == "__main__":
    main()
