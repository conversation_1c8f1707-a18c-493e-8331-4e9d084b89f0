"""
Serializers pour les données dynamiques du site.
"""

from rest_framework import serializers
from .models import (
    SiteSettings, HomePageStats, Service, Testimonial,
    NewsletterSubscriber, ContactMessage, FAQ, Banner,
    BlogCategory, BlogPost, BlogComment, NewsletterSubscription, NewsletterCampaign
)


class SiteSettingsSerializer(serializers.ModelSerializer):
    """Serializer pour les paramètres du site."""

    class Meta:
        model = SiteSettings
        fields = [
            'id', 'site_name', 'site_description', 'site_logo',
            'phone_primary', 'phone_secondary', 'email_primary', 'email_secondary',
            'address_line1', 'address_line2', 'city', 'country',
            'business_hours', 'facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url',
            'meta_description', 'meta_keywords'
        ]


class HomePageStatsSerializer(serializers.ModelSerializer):
    """Serializer pour les statistiques de la page d'accueil."""

    class Meta:
        model = HomePageStats
        fields = [
            'id', 'clients_count', 'projects_count', 'experience_years', 'team_members',
            'products_sold', 'cities_served', 'support_hours', 'satisfaction_rate',
            'updated_at'
        ]


class ServiceSerializer(serializers.ModelSerializer):
    """Serializer pour les services."""

    class Meta:
        model = Service
        fields = [
            'id', 'name', 'description', 'short_description', 'icon', 'color',
            'slug', 'featured', 'order'
        ]


class TestimonialSerializer(serializers.ModelSerializer):
    """Serializer pour les témoignages."""

    client_photo_url = serializers.SerializerMethodField()

    class Meta:
        model = Testimonial
        fields = [
            'id', 'client_name', 'client_title', 'client_company', 'client_photo_url',
            'content', 'rating', 'featured', 'order'
        ]

    def get_client_photo_url(self, obj):
        """Retourner l'URL de la photo du client."""
        if obj.client_photo:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.client_photo.url)
            return obj.client_photo.url
        return None


class NewsletterSubscriberSerializer(serializers.ModelSerializer):
    """Serializer pour les abonnés newsletter."""

    class Meta:
        model = NewsletterSubscriber
        fields = ['email', 'name', 'frequency']

    def validate_email(self, value):
        """Valider l'email."""
        if not value:
            raise serializers.ValidationError("L'email est requis.")
        return value.lower()


class ContactMessageSerializer(serializers.ModelSerializer):
    """Serializer pour les messages de contact."""

    class Meta:
        model = ContactMessage
        fields = [
            'name', 'email', 'phone', 'company', 'subject', 'message', 'service_requested'
        ]

    def validate_name(self, value):
        """Valider le nom."""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Le nom doit contenir au moins 2 caractères.")
        return value.strip()

    def validate_email(self, value):
        """Valider l'email."""
        if not value:
            raise serializers.ValidationError("L'email est requis.")
        return value.lower()

    def validate_subject(self, value):
        """Valider le sujet."""
        if not value or len(value.strip()) < 5:
            raise serializers.ValidationError("Le sujet doit contenir au moins 5 caractères.")
        return value.strip()

    def validate_message(self, value):
        """Valider le message."""
        if not value or len(value.strip()) < 10:
            raise serializers.ValidationError("Le message doit contenir au moins 10 caractères.")
        return value.strip()


class FAQSerializer(serializers.ModelSerializer):
    """Serializer pour les FAQ."""

    class Meta:
        model = FAQ
        fields = [
            'id', 'question', 'answer', 'category', 'featured', 'order'
        ]


class BannerSerializer(serializers.ModelSerializer):
    """Serializer pour les bannières."""

    image_url = serializers.SerializerMethodField()
    is_visible = serializers.SerializerMethodField()

    class Meta:
        model = Banner
        fields = [
            'id', 'title', 'subtitle', 'description', 'image_url',
            'button_text', 'button_url', 'position', 'is_visible',
            'start_date', 'end_date', 'order'
        ]

    def get_image_url(self, obj):
        """Retourner l'URL de l'image."""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None

    def get_is_visible(self, obj):
        """Vérifier si la bannière est visible."""
        return obj.is_visible()


class HomePageDataSerializer(serializers.Serializer):
    """Serializer pour les données consolidées de la page d'accueil."""

    site_settings = SiteSettingsSerializer(read_only=True)
    stats = HomePageStatsSerializer(read_only=True)
    featured_services = ServiceSerializer(many=True, read_only=True)
    featured_testimonials = TestimonialSerializer(many=True, read_only=True)
    hero_banners = BannerSerializer(many=True, read_only=True)
    last_updated = serializers.DateTimeField(read_only=True)


# Serializers pour les données mock (développement)
class MockStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques mock."""

    clients_count = serializers.IntegerField()
    projects_count = serializers.IntegerField()
    experience_years = serializers.IntegerField()
    team_members = serializers.IntegerField()
    products_sold = serializers.IntegerField()
    cities_served = serializers.IntegerField()
    support_hours = serializers.CharField()
    satisfaction_rate = serializers.IntegerField()
    recent_orders = serializers.IntegerField(required=False)
    total_products = serializers.IntegerField(required=False)
    last_updated = serializers.DateTimeField(required=False)


class MockServiceSerializer(serializers.Serializer):
    """Serializer pour les services mock."""

    id = serializers.IntegerField()
    name = serializers.CharField()
    description = serializers.CharField()
    short_description = serializers.CharField()
    icon = serializers.CharField()
    color = serializers.CharField()
    slug = serializers.CharField()
    featured = serializers.BooleanField()
    order = serializers.IntegerField()


class MockTestimonialSerializer(serializers.Serializer):
    """Serializer pour les témoignages mock."""

    id = serializers.IntegerField()
    client_name = serializers.CharField()
    client_title = serializers.CharField()
    client_company = serializers.CharField()
    client_photo_url = serializers.URLField(required=False, allow_null=True)
    content = serializers.CharField()
    rating = serializers.IntegerField()
    featured = serializers.BooleanField()
    order = serializers.IntegerField()


# Serializers pour le blog
class BlogCategorySerializer(serializers.ModelSerializer):
    """Serializer pour les catégories de blog."""

    posts_count = serializers.SerializerMethodField()

    class Meta:
        model = BlogCategory
        fields = [
            'id', 'name', 'slug', 'description', 'color', 'order', 'posts_count'
        ]

    def get_posts_count(self, obj):
        """Retourner le nombre d'articles publiés dans cette catégorie."""
        return obj.posts.filter(status='published').count()


class BlogPostListSerializer(serializers.ModelSerializer):
    """Serializer pour la liste des articles de blog."""

    category_name = serializers.CharField(source='category.name', read_only=True)
    category_slug = serializers.CharField(source='category.slug', read_only=True)
    category_color = serializers.CharField(source='category.color', read_only=True)
    featured_image_url = serializers.SerializerMethodField()
    author_avatar_url = serializers.SerializerMethodField()
    tags_list = serializers.SerializerMethodField()
    comments_count = serializers.SerializerMethodField()
    reading_time = serializers.SerializerMethodField()

    class Meta:
        model = BlogPost
        fields = [
            'id', 'title', 'slug', 'excerpt', 'featured_image_url', 'featured_image_alt',
            'category_name', 'category_slug', 'category_color', 'tags_list',
            'author_name', 'author_avatar_url', 'featured', 'published_at',
            'views_count', 'likes_count', 'comments_count', 'reading_time'
        ]

    def get_featured_image_url(self, obj):
        """Retourner l'URL de l'image mise en avant."""
        if obj.featured_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.featured_image.url)
            return obj.featured_image.url
        return None

    def get_author_avatar_url(self, obj):
        """Retourner l'URL de l'avatar de l'auteur."""
        if obj.author_avatar:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.author_avatar.url)
            return obj.author_avatar.url
        return None

    def get_tags_list(self, obj):
        """Retourner la liste des tags."""
        return obj.get_tags_list()

    def get_comments_count(self, obj):
        """Retourner le nombre de commentaires approuvés."""
        return obj.comments.filter(status='approved').count()

    def get_reading_time(self, obj):
        """Calculer le temps de lecture estimé (en minutes)."""
        words_per_minute = 200
        word_count = len(obj.content.split())
        reading_time = max(1, round(word_count / words_per_minute))
        return reading_time


class BlogPostDetailSerializer(BlogPostListSerializer):
    """Serializer pour le détail d'un article de blog."""

    class Meta(BlogPostListSerializer.Meta):
        fields = BlogPostListSerializer.Meta.fields + [
            'content', 'author_email', 'author_bio', 'meta_description', 'meta_keywords'
        ]


class BlogCommentSerializer(serializers.ModelSerializer):
    """Serializer pour les commentaires de blog."""

    class Meta:
        model = BlogComment
        fields = [
            'id', 'author_name', 'author_email', 'content', 'created_at', 'status'
        ]
        read_only_fields = ['id', 'created_at', 'status']

    def validate_author_name(self, value):
        """Valider le nom de l'auteur."""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Le nom doit contenir au moins 2 caractères.")
        return value.strip()

    def validate_content(self, value):
        """Valider le contenu du commentaire."""
        if not value or len(value.strip()) < 10:
            raise serializers.ValidationError("Le commentaire doit contenir au moins 10 caractères.")
        return value.strip()


class BlogSearchSerializer(serializers.Serializer):
    """Serializer pour la recherche dans le blog."""

    query = serializers.CharField(max_length=200, required=False, allow_blank=True)
    category = serializers.CharField(max_length=100, required=False, allow_blank=True)
    tags = serializers.CharField(max_length=500, required=False, allow_blank=True)
    author = serializers.CharField(max_length=100, required=False, allow_blank=True)
    date_from = serializers.DateField(required=False)
    date_to = serializers.DateField(required=False)
    featured_only = serializers.BooleanField(default=False)
    page = serializers.IntegerField(min_value=1, default=1)
    page_size = serializers.IntegerField(min_value=1, max_value=50, default=12)


class NewsletterSubscriptionSerializer(serializers.ModelSerializer):
    """Serializer pour les abonnements newsletter."""

    preferences = serializers.SerializerMethodField()

    class Meta:
        model = NewsletterSubscription
        fields = [
            'id', 'email', 'is_active', 'subscribed_at', 'unsubscribed_at',
            'preferences', 'source'
        ]
        read_only_fields = ['id', 'subscribed_at', 'unsubscribed_at']

    def get_preferences(self, obj):
        """Retourne les préférences sous forme de dictionnaire."""
        return obj.preferences_dict


class NewsletterSubscribeSerializer(serializers.Serializer):
    """Serializer pour s'abonner à la newsletter."""

    email = serializers.EmailField()
    preferences = serializers.DictField(required=False, default=dict)
    source = serializers.CharField(max_length=50, default='website')

    def validate_email(self, value):
        """Valide que l'email n'est pas déjà abonné."""
        if NewsletterSubscription.objects.filter(email=value, is_active=True).exists():
            raise serializers.ValidationError("Cet email est déjà abonné à la newsletter.")
        return value


class NewsletterUnsubscribeSerializer(serializers.Serializer):
    """Serializer pour se désabonner de la newsletter."""

    email = serializers.EmailField()
    token = serializers.UUIDField(required=False)

    def validate(self, data):
        """Valide que l'email est abonné."""
        try:
            subscription = NewsletterSubscription.objects.get(
                email=data['email'],
                is_active=True
            )
            data['subscription'] = subscription
        except NewsletterSubscription.DoesNotExist:
            raise serializers.ValidationError("Cet email n'est pas abonné à la newsletter.")

        return data


class NewsletterPreferencesSerializer(serializers.Serializer):
    """Serializer pour mettre à jour les préférences."""

    promotions = serializers.BooleanField(required=False)
    new_products = serializers.BooleanField(required=False)
    newsletters = serializers.BooleanField(required=False)
    order_updates = serializers.BooleanField(required=False)


class NewsletterCampaignSerializer(serializers.ModelSerializer):
    """Serializer pour les campagnes newsletter."""

    open_rate = serializers.ReadOnlyField()
    click_rate = serializers.ReadOnlyField()
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = NewsletterCampaign
        fields = [
            'id', 'title', 'subject', 'content', 'campaign_type', 'status',
            'target_preferences', 'target_all', 'scheduled_at', 'sent_at',
            'total_recipients', 'sent_count', 'failed_count', 'open_count', 'click_count',
            'open_rate', 'click_rate', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'sent_at', 'total_recipients', 'sent_count', 'failed_count',
            'open_count', 'click_count', 'created_at', 'updated_at'
        ]


class NewsletterSendSerializer(serializers.Serializer):
    """Serializer pour envoyer une newsletter."""

    subject = serializers.CharField(max_length=200)
    content = serializers.CharField()
    campaign_type = serializers.ChoiceField(choices=NewsletterCampaign.CAMPAIGN_TYPES)
    target_preferences = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list
    )
    target_all = serializers.BooleanField(default=False)

    def validate(self, data):
        """Valide que soit target_all soit target_preferences est spécifié."""
        if not data.get('target_all') and not data.get('target_preferences'):
            raise serializers.ValidationError(
                "Vous devez spécifier soit 'target_all' soit 'target_preferences'."
            )
        return data