from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from django.db.models import Count, Sum, Q
from django.utils.crypto import get_random_string
from django_filters.rest_framework import DjangoFilterBackend
from ..models import UserAddress, LoyaltyTransaction
from ..serializers.admin_serializers import (
    AdminUserSerializer,
    AdminUserCreateSerializer,
    AdminUserUpdateSerializer,
    AdminUserAddressSerializer,
    AdminLoyaltyTransactionSerializer,
    AdminUserStatsSerializer
)
from ..permissions import IsAdminUser

User = get_user_model()

class AdminUserViewSet(viewsets.ModelViewSet):
    """ViewSet for managing users by admin."""
    
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['role', 'is_active', 'is_verified']
    search_fields = ['email', 'first_name', 'last_name', 'phone_number']
    ordering_fields = ['id', 'email', 'first_name', 'last_name', 'created_at', 'updated_at', 'loyalty_points']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Get the queryset with order count and total spent."""
        return User.objects.all().annotate(
            order_count=Count('orders'),
            total_spent=Sum('orders__total', default=0)
        )
    
    def get_serializer_class(self):
        """Return appropriate serializer class based on action."""
        if self.action == 'create':
            return AdminUserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return AdminUserUpdateSerializer
        return AdminUserSerializer
    
    @action(detail=True, methods=['get'])
    def addresses(self, request, pk=None):
        """Get all addresses for a specific user."""
        user = self.get_object()
        addresses = UserAddress.objects.filter(user=user)
        serializer = AdminUserAddressSerializer(addresses, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def loyalty_transactions(self, request, pk=None):
        """Get all loyalty transactions for a specific user."""
        user = self.get_object()
        transactions = LoyaltyTransaction.objects.filter(user=user)
        serializer = AdminLoyaltyTransactionSerializer(transactions, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_loyalty_points(self, request, pk=None):
        """Add loyalty points to a user."""
        user = self.get_object()
        points = request.data.get('points', 0)
        description = request.data.get('description', 'Manual adjustment by admin')
        
        if not points or points <= 0:
            return Response(
                {'error': 'Points must be a positive number'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create transaction
        transaction = LoyaltyTransaction.objects.create(
            user=user,
            points=points,
            transaction_type='adjust',
            description=description
        )
        
        # Update user's loyalty points
        user.loyalty_points += points
        
        # Update loyalty tier based on total points
        if user.loyalty_points >= 1000:
            user.loyalty_tier = 'gold'
        elif user.loyalty_points >= 500:
            user.loyalty_tier = 'silver'
        else:
            user.loyalty_tier = 'bronze'
        
        user.save()
        
        serializer = AdminLoyaltyTransactionSerializer(transaction)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Mark a user's email as verified."""
        user = self.get_object()
        user.is_verified = True
        user.save()
        serializer = AdminUserSerializer(user)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def set_active(self, request, pk=None):
        """Set a user's active status."""
        user = self.get_object()
        is_active = request.data.get('is_active', True)
        user.is_active = is_active
        user.save()
        serializer = AdminUserSerializer(user)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reset_password(self, request, pk=None):
        """Reset a user's password and return the new password."""
        user = self.get_object()
        
        # Generate a random password
        new_password = get_random_string(10)
        
        # Set the new password
        user.set_password(new_password)
        user.save()
        
        return Response({'password': new_password})
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get user statistics for admin dashboard."""
        total_users = User.objects.count()
        active_users = User.objects.filter(is_active=True).count()
        inactive_users = User.objects.filter(is_active=False).count()
        verified_users = User.objects.filter(is_verified=True).count()
        unverified_users = User.objects.filter(is_verified=False).count()
        
        # Role distribution
        role_distribution = {
            'customer': User.objects.filter(role='customer').count(),
            'staff': User.objects.filter(role='staff').count(),
            'admin': User.objects.filter(role='admin').count(),
        }
        
        # Recent users
        recent_users = User.objects.order_by('-created_at')[:5]
        recent_users_serializer = AdminUserSerializer(recent_users, many=True)
        
        # Top customers by order count and total spent
        top_customers_data = User.objects.filter(role='customer').annotate(
            order_count=Count('orders'),
            total_spent=Sum('orders__total', default=0)
        ).order_by('-total_spent')[:5]
        
        top_customers = []
        for user in top_customers_data:
            top_customers.append({
                'user': AdminUserSerializer(user).data,
                'order_count': user.order_count,
                'total_spent': user.total_spent
            })
        
        data = {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': inactive_users,
            'verified_users': verified_users,
            'unverified_users': unverified_users,
            'role_distribution': role_distribution,
            'recent_users': recent_users_serializer.data,
            'top_customers': top_customers
        }
        
        serializer = AdminUserStatsSerializer(data)
        return Response(serializer.data)
