
import { ReactNode } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AccountSidebar from "./AccountSidebar";

interface AccountLayoutProps {
  children: ReactNode;
  title: string;
}

const AccountLayout = ({ children, title }: AccountLayoutProps) => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <div className="container mx-auto flex-1 py-8 px-4">
        <h1 className="text-3xl font-bold text-primary mb-6">{title}</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-[250px_1fr] gap-8">
          <div className="hidden md:block sticky top-24 self-start">
            <AccountSidebar />
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="md:hidden mb-6">
              <AccountSidebar />
            </div>
            
            {children}
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default AccountLayout;
