#!/usr/bin/env python3
"""
Test direct de l'API de reset password
"""
import requests
import json

def test_reset_api():
    """Test direct de l'API"""
    
    # Configuration
    API_BASE = "http://localhost:8000/api/v1"
    EMAIL = "<EMAIL>"
    NEW_PASSWORD = "NewPassword123!"
    
    print("🧪 Test direct de l'API de reset password\n")
    
    # Étape 1: Demander un reset
    print("📧 Étape 1: Demande de reset...")
    try:
        response = requests.post(
            f"{API_BASE}/auth/password/reset/request/",
            json={"email": EMAIL},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status: {response.status_code}")
        print(f"Réponse: {response.text}")
        
        if response.status_code == 200:
            print("✅ Demande de reset réussie")
        else:
            print("❌ Échec de la demande de reset")
            return
            
    except Exception as e:
        print(f"❌ Erreur lors de la demande: {e}")
        return
    
    # Étape 2: Récupérer le token depuis la base
    print("\n🔑 Étape 2: Récupération du token...")
    import os
    import django
    
    # Configuration Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
    django.setup()
    
    from authentication.models import PasswordReset
    from django.utils import timezone
    
    try:
        latest_reset = PasswordReset.objects.filter(
            user__email=EMAIL,
            expires_at__gt=timezone.now(),
            is_used=False
        ).order_by('-created_at').first()
        
        if not latest_reset:
            print("❌ Aucun token trouvé")
            return
            
        token = latest_reset.token
        print(f"✅ Token récupéré: {token}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la récupération du token: {e}")
        return
    
    # Étape 3: Tester le reset
    print(f"\n🔄 Étape 3: Test du reset avec token...")
    
    payload = {
        "token": token,
        "new_password": NEW_PASSWORD,
        "confirm_password": NEW_PASSWORD
    }
    
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            f"{API_BASE}/auth/password/reset/confirm/",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\nStatus: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Réponse: {response.text}")
        
        if response.status_code == 200:
            print("✅ Reset réussi!")
            
            # Test de connexion avec le nouveau mot de passe
            print(f"\n🔐 Étape 4: Test de connexion...")
            login_response = requests.post(
                f"{API_BASE}/auth/login/",
                json={"email": EMAIL, "password": NEW_PASSWORD},
                headers={"Content-Type": "application/json"}
            )
            
            print(f"Login Status: {login_response.status_code}")
            print(f"Login Réponse: {login_response.text}")
            
            if login_response.status_code == 200:
                print("✅ Connexion réussie avec le nouveau mot de passe!")
            else:
                print("❌ Échec de la connexion")
                
        else:
            print("❌ Échec du reset")
            try:
                error_data = response.json()
                print(f"Erreur détaillée: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Erreur brute: {response.text}")
                
    except Exception as e:
        print(f"❌ Erreur lors du reset: {e}")

if __name__ == "__main__":
    test_reset_api()
