#!/usr/bin/env python3
"""
Script pour tester les détails d'une transaction spécifique
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def test_transaction_detail():
    """Teste la récupération des détails d'une transaction"""
    print("🔍 Test des détails de transaction")
    print("=" * 40)
    
    # Authentification
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    token = response.json().get('access')
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # D'abord récupérer la liste pour avoir un ID valide
    print("1. Récupération de la liste des transactions...")
    response = requests.get(f"{API_BASE_URL}/payments/transactions/my_transactions/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        transactions = data.get('results', [])
        
        if transactions:
            first_transaction = transactions[0]
            transaction_id = first_transaction['id']
            print(f"   ✅ Transaction trouvée: ID {transaction_id}")
            
            # Tester les détails
            print(f"\n2. Test des détails de la transaction {transaction_id}...")
            detail_response = requests.get(f"{API_BASE_URL}/payments/transactions/{transaction_id}/", headers=headers)
            
            if detail_response.status_code == 200:
                detail = detail_response.json()
                print(f"   ✅ Détails récupérés avec succès!")
                print(f"   📊 Informations détaillées:")
                print(f"      ID: {detail.get('id')}")
                print(f"      Transaction ID: {detail.get('transaction_id')}")
                print(f"      Montant: {detail.get('amount')} {detail.get('currency')}")
                print(f"      Statut: {detail.get('status_display', detail.get('status'))}")
                print(f"      Méthode: {detail.get('payment_method_name', 'N/A')}")
                print(f"      Commande: {detail.get('order')}")
                print(f"      Créé le: {detail.get('created_at')}")
                print(f"      Erreur: {detail.get('error_message', 'Aucune')}")
                
                # Vérifier si les détails de commande sont inclus
                if 'order_details' in detail:
                    print(f"      Détails commande: ✅ Inclus")
                else:
                    print(f"      Détails commande: ❌ Non inclus")
                
                return True
            else:
                print(f"   ❌ Erreur lors de la récupération des détails: {detail_response.status_code}")
                print(f"   Réponse: {detail_response.text}")
                return False
        else:
            print("   ❌ Aucune transaction trouvée")
            return False
    else:
        print(f"   ❌ Erreur lors de la récupération de la liste: {response.status_code}")
        return False

if __name__ == "__main__":
    success = test_transaction_detail()
    
    if success:
        print(f"\n🎉 TEST RÉUSSI!")
        print(f"   Les détails de transaction fonctionnent correctement.")
    else:
        print(f"\n❌ TEST ÉCHOUÉ")
        print(f"   Vérifiez les erreurs ci-dessus.")
