import { ProductImage } from './productApi';
import { API_BASE_URL } from './apiConfig';

// Interface pour le type de réponse paginable de l'API
export interface PaginatedResponse<T> {
  results: T[];
  count: number;
  next: string | null;
  previous: string | null;
}

// Helper function to get auth header
const getAuthHeader = () => {
  // Vérifier plusieurs clés possibles pour le token
  const token = localStorage.getItem('accessToken') || 
               localStorage.getItem('authToken') || 
               localStorage.getItem('token') || 
               sessionStorage.getItem('accessToken') || 
               sessionStorage.getItem('authToken');
  
  console.log('Auth token found:', token ? 'yes' : 'no');
  
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Helper function to generate mock product images for development
export const getMockProductImages = (productId: number): ProductImage[] => {
  console.log('Generating mock product images for product ID:', productId);
  
  const mockImages: ProductImage[] = [
    {
      id: 1,
      product: productId,
      image: '/assets/images/products/product-1.jpg',
      alt_text: 'Image principale du produit',
      is_primary: true,
      created_at: new Date().toISOString()
    },
    {
      id: 2,
      product: productId,
      image: '/assets/images/products/product-2.jpg',
      alt_text: 'Vue latérale du produit',
      is_primary: false,
      created_at: new Date().toISOString()
    },
    {
      id: 3,
      product: productId,
      image: '/assets/images/products/product-3.jpg',
      alt_text: 'Vue arrière du produit',
      is_primary: false,
      created_at: new Date().toISOString()
    }
  ];

  return mockImages;
};

// API service for product images
export const productImageApi = {
  // Get product images
  getProductImages: async (productId: number): Promise<ProductImage[] | PaginatedResponse<ProductImage>> => {
    try {
      console.log('Fetching product images for product ID:', productId);
      
      const response = await fetch(`${API_BASE_URL}/api/v1/products/${productId}/images/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Product images API error:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        
        // En cas d'erreur, retourner des images fictives
        console.warn('Affichage de données simulées - Le backend n\'est pas disponible ou n\'est pas configuré pour cet endpoint');
        return getMockProductImages(productId);
      }

      const data = await response.json();
      console.log('Received product images data:', data);
      return data;
    } catch (error) {
      console.error('Error fetching product images:', error);
      console.warn('Affichage de données simulées - Le backend n\'est pas disponible ou n\'est pas configuré pour cet endpoint');
      return getMockProductImages(productId);
    }
  },

  // Upload a single product image
  uploadProductImage: async (productId: number, image: File, altText?: string): Promise<ProductImage> => {
    try {
      const formData = new FormData();
      formData.append('image', image);
      if (altText) {
        formData.append('alt_text', altText);
      }

      console.log('Uploading image for product ID:', productId);
      console.log('Image details:', image.name, image.type, image.size);

      const response = await fetch(`${API_BASE_URL}/api/v1/products/${productId}/images/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
          // Ne pas ajouter Content-Type ici, FormData le définit automatiquement avec le boundary
        },
        body: formData,
      });

      if (!response.ok) {
        console.error('Image upload failed:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error('Une erreur est survenue lors du téléchargement de l\'image');
      }

      const data = await response.json();
      console.log('Image upload successful:', data);
      return data;
    } catch (error) {
      console.error('Error uploading product image:', error);
      throw error;
    }
  },

  // Upload multiple product images
  uploadMultipleProductImages: async (productId: number, images: File[], altText?: string): Promise<ProductImage[]> => {
    try {
      const results: ProductImage[] = [];
      
      for (const image of images) {
        const result = await productImageApi.uploadProductImage(productId, image, altText);
        results.push(result);
      }
      
      return results;
    } catch (error) {
      console.error('Error uploading multiple product images:', error);
      throw error;
    }
  },

  // Set primary product image
  setPrimaryProductImage: async (imageId: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/product-images/${imageId}/set-primary/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Set primary image failed:', response.status, response.statusText);
        throw new Error('Une erreur est survenue lors de la définition de l\'image principale');
      }
    } catch (error) {
      console.error('Error setting primary product image:', error);
      throw error;
    }
  },

  // Delete product image
  deleteProductImage: async (imageId: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/product-images/${imageId}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Delete image failed:', response.status, response.statusText);
        throw new Error('Une erreur est survenue lors de la suppression de l\'image');
      }
    } catch (error) {
      console.error('Error deleting product image:', error);
      throw error;
    }
  },

  // Reorder product images
  reorderProductImages: async (productId: number, imageIds: number[]): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/products/${productId}/reorder-images/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify({ image_ids: imageIds }),
      });

      if (!response.ok) {
        console.error('Reorder images failed:', response.status, response.statusText);
        throw new Error('Une erreur est survenue lors de la réorganisation des images');
      }
    } catch (error) {
      console.error('Error reordering product images:', error);
      throw error;
    }
  },
};
