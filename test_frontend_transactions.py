#!/usr/bin/env python3
"""
Script pour tester que le frontend peut récupérer les transactions correctement
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def test_frontend_transactions():
    """Teste que l'API retourne les données dans le format attendu par le frontend"""
    print("🧪 Test de l'API des transactions pour le frontend")
    print("=" * 60)
    
    # 1. Authentification
    print("🔐 Authentification...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access')
            print(f"✅ Authentification réussie")
        else:
            print(f"❌ Échec de l'authentification: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. Test de l'endpoint utilisé par le frontend (user_only=true)
    print(f"\n📋 Test de récupération des transactions utilisateur...")
    
    try:
        # Simuler l'appel fait par le frontend
        response = requests.get(f"{API_BASE_URL}/payments/transactions/my_transactions/", headers=headers)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Vérifier le format des données
            print(f"📊 Format des données reçues:")
            print(f"   Type: {type(data)}")
            
            if isinstance(data, dict) and 'results' in data:
                transactions = data['results']
                print(f"   ✅ Données paginées détectées")
                print(f"   📦 Nombre de transactions: {len(transactions)}")
                print(f"   📈 Total: {data.get('count', 'N/A')}")
                
                # Vérifier la structure d'une transaction
                if transactions:
                    first_transaction = transactions[0]
                    print(f"\n📍 Structure de la première transaction:")
                    required_fields = ['id', 'transaction_id', 'amount', 'currency', 'status', 
                                     'order', 'created_at', 'payment_method_name']
                    
                    for field in required_fields:
                        if field in first_transaction:
                            value = first_transaction[field]
                            print(f"   ✅ {field}: {value}")
                        else:
                            print(f"   ⚠️ {field}: MANQUANT")
                    
                    # Vérifier les champs optionnels
                    optional_fields = ['status_display', 'completed_at', 'error_message']
                    print(f"\n📍 Champs optionnels:")
                    for field in optional_fields:
                        if field in first_transaction:
                            value = first_transaction[field]
                            print(f"   ✅ {field}: {value}")
                        else:
                            print(f"   ➖ {field}: Non présent")
                
                return True
                
            elif isinstance(data, list):
                print(f"   ✅ Données directes (tableau)")
                print(f"   📦 Nombre de transactions: {len(data)}")
                return True
            else:
                print(f"   ❌ Format inattendu: {type(data)}")
                print(f"   📄 Contenu: {data}")
                return False
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_transaction_detail(token, transaction_id):
    """Teste la récupération des détails d'une transaction"""
    print(f"\n🔍 Test de récupération des détails de la transaction {transaction_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/payments/transactions/{transaction_id}/", headers=headers)
        
        if response.status_code == 200:
            transaction = response.json()
            print(f"✅ Détails récupérés")
            print(f"   ID: {transaction.get('id')}")
            print(f"   Transaction ID: {transaction.get('transaction_id')}")
            print(f"   Montant: {transaction.get('amount')} {transaction.get('currency')}")
            print(f"   Statut: {transaction.get('status')}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_pagination():
    """Teste la pagination des transactions"""
    print(f"\n📄 Test de pagination...")
    
    # Authentification
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    token = response.json().get('access')
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test avec pagination
        params = {
            'page': 1,
            'page_size': 2
        }
        
        response = requests.get(f"{API_BASE_URL}/payments/transactions/my_transactions/", 
                              headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Pagination testée")
            print(f"   Page 1, taille 2: {len(data.get('results', []))} résultats")
            print(f"   Total: {data.get('count', 'N/A')}")
            print(f"   Page suivante: {'Oui' if data.get('next') else 'Non'}")
            return True
        else:
            print(f"❌ Erreur de pagination: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TEST COMPLET DU FRONTEND DES TRANSACTIONS")
    print("=" * 70)
    
    # Test 1: Récupération des transactions
    success1 = test_frontend_transactions()
    
    # Test 2: Pagination
    success2 = test_pagination()
    
    # Test 3: Détails d'une transaction (si on en a)
    if success1:
        # Récupérer le token pour le test des détails
        login_data = {
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD
        }
        response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        token = response.json().get('access')
        
        success3 = test_transaction_detail(token, 1)  # Tester avec l'ID 1
    else:
        success3 = False
    
    print(f"\n📊 RÉSUMÉ DES TESTS:")
    print(f"   Récupération des transactions: {'✅' if success1 else '❌'}")
    print(f"   Pagination: {'✅' if success2 else '❌'}")
    print(f"   Détails de transaction: {'✅' if success3 else '❌'}")
    
    if success1 and success2:
        print(f"\n🎉 TESTS PRINCIPAUX RÉUSSIS!")
        print(f"   Le frontend devrait maintenant pouvoir afficher les transactions correctement.")
        print(f"   Allez sur: http://localhost:8080/account/transactions")
        print(f"\n💡 Fonctionnalités disponibles:")
        print(f"   - Liste des transactions avec pagination")
        print(f"   - Filtrage par statut")
        print(f"   - Recherche par ID de transaction")
        print(f"   - Tri par date, montant, etc.")
        print(f"   - Détails de chaque transaction")
    else:
        print(f"\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"   Vérifiez les erreurs ci-dessus.")
