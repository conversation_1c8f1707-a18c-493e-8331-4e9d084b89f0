import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { InfoIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PromotionUsageStatsProps {
  totalUsage: number;
  totalUsageLimit: number;
  customerUsage?: number;
  customerUsageLimit?: number;
}

const PromotionUsageStats: React.FC<PromotionUsageStatsProps> = ({
  totalUsage = 0,
  totalUsageLimit = 0,
  customerUsage = 0,
  customerUsageLimit = 0,
}) => {
  // Calcul des pourcentages d'utilisation
  const totalUsagePercent = totalUsageLimit > 0 
    ? Math.min(Math.round((totalUsage / totalUsageLimit) * 100), 100)
    : 0;
  
  const customerUsagePercent = customerUsageLimit > 0 
    ? Math.min(Math.round((customerUsage / customerUsageLimit) * 100), 100)
    : 0;

  // Détermination des couleurs en fonction du pourcentage
  const getTotalProgressColor = () => {
    if (totalUsagePercent >= 90) return 'bg-red-500';
    if (totalUsagePercent >= 70) return 'bg-amber-500';
    return 'bg-green-500';
  };

  const getCustomerProgressColor = () => {
    if (customerUsagePercent >= 90) return 'bg-red-500';
    if (customerUsagePercent >= 70) return 'bg-amber-500';
    return 'bg-green-500';
  };

  // Composant Progress personnalisé avec couleur d'indicateur
  const CustomProgress = ({ value, color }: { value: number, color: string }) => (
    <Progress 
      value={value} 
      className="h-2"
    >
      <div 
        className={cn("h-full", color)} 
        style={{ width: `${value}%` }}
      />
    </Progress>
  );

  return (
    <Card className="mt-4">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Statistiques d'utilisation</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Suivi de l'utilisation de cette promotion</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <CardDescription>
          Nombre d'utilisations et limites
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {totalUsageLimit > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Utilisation totale</span>
              <span className="font-medium">{totalUsage} / {totalUsageLimit}</span>
            </div>
            <CustomProgress 
              value={totalUsagePercent} 
              color={getTotalProgressColor()} 
            />
          </div>
        )}

        {customerUsageLimit > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Utilisation par client (exemple)</span>
              <span className="font-medium">{customerUsage} / {customerUsageLimit}</span>
            </div>
            <CustomProgress 
              value={customerUsagePercent} 
              color={getCustomerProgressColor()} 
            />
          </div>
        )}

        {totalUsageLimit === 0 && customerUsageLimit === 0 && (
          <p className="text-sm text-muted-foreground">
            Aucune limite d'utilisation n'est définie pour cette promotion.
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default PromotionUsageStats;
