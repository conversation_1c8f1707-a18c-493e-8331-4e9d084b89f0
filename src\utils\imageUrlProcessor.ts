import { API_BASE_URL } from '@/services/apiConfig';

/**
 * Utilitaire pour traiter les URLs d'images de manière cohérente
 */

/**
 * Traite une URL d'image pour la rendre absolue et accessible
 */
export const processImageUrl = (url: string | null | undefined): string | null => {
  if (!url || url === '/placeholder.svg') {
    return null;
  }

  // Si l'URL est déjà absolue (commence par http), la retourner telle quelle
  if (url.startsWith('http')) {
    return url;
  }

  // Si l'URL commence par /media/, ajouter le base URL
  if (url.startsWith('/media/')) {
    return `${API_BASE_URL}${url}`;
  }

  // Si l'URL ne commence pas par /, l'ajouter
  if (!url.startsWith('/')) {
    url = `/${url}`;
  }

  // Construire l'URL complète
  return `${API_BASE_URL}/media${url}`;
};

/**
 * Extrait la meilleure URL d'image d'un objet produit
 */
export const extractBestImageUrl = (product: any): string | null => {
  // 1. Priorité absolue : primary_image
  if (product.primary_image?.image) {
    return processImageUrl(product.primary_image.image);
  }

  // 2. Deuxième priorité : première image de la liste
  if (product.images && Array.isArray(product.images) && product.images.length > 0) {
    return processImageUrl(product.images[0].image);
  }

  // 3. Troisième priorité : propriété image directe
  if (product.image && typeof product.image === 'string') {
    return processImageUrl(product.image);
  }

  // 4. Aucune image trouvée
  return null;
};

/**
 * Crée un objet primary_image compatible à partir d'une URL
 */
export const createPrimaryImageObject = (imageUrl: string | null, altText: string = '') => {
  if (!imageUrl) {
    return null;
  }

  return {
    id: 1,
    image: imageUrl,
    alt_text: altText
  };
};

/**
 * Transforme un produit de l'API home en ProductListItem
 */
export const transformFeaturedProductToListItem = (product: any): any => {
  const imageUrl = extractBestImageUrl(product);
  
  return {
    id: product.id,
    name: product.name,
    slug: product.slug || `product-${product.id}`,
    sku: product.sku || `SKU-${product.id}`,
    short_description: product.description || product.short_description,
    price: parseFloat(product.price) || 0,
    sale_price: product.sale_price ? parseFloat(product.sale_price) : null,
    current_price: parseFloat(product.current_price || product.price) || 0,
    discount_percentage: product.discount_percentage || 0,
    is_on_sale: product.is_on_sale || false,
    categories: product.categories || (product.category ? [{ 
      id: 1, 
      name: product.category, 
      slug: product.category.toLowerCase().replace(/\s+/g, '-') 
    }] : []),
    primary_image: createPrimaryImageObject(imageUrl, product.name),
    is_featured: product.is_featured || false,
    in_stock: product.in_stock !== undefined ? product.in_stock : true,
    status: 'published' as const,
    created_at: product.created_at || new Date().toISOString()
  };
};

/**
 * Teste si une URL d'image est accessible
 */
export const testImageUrl = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }

    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;

    // Timeout après 5 secondes
    setTimeout(() => resolve(false), 5000);
  });
};

/**
 * Trouve la meilleure URL d'image en testant plusieurs stratégies
 */
export const findBestWorkingImageUrl = async (product: any): Promise<string | null> => {
  const strategies = [
    () => extractBestImageUrl(product),
    () => product.primary_image?.image ? processImageUrl(product.primary_image.image) : null,
    () => product.images?.[0]?.image ? processImageUrl(product.images[0].image) : null,
    () => product.image ? processImageUrl(product.image) : null,
  ];

  for (const strategy of strategies) {
    const url = strategy();
    if (url) {
      const isWorking = await testImageUrl(url);
      if (isWorking) {
        return url;
      }
    }
  }

  return null;
};

/**
 * Log des informations de débogage pour une image
 */
export const logImageDebug = (product: any, context: string = '') => {
  if (process.env.NODE_ENV !== 'development') return;

  console.group(`🖼️ Image Debug - ${product.name} ${context}`);
  console.log('Product data:', product);
  console.log('Primary image:', product.primary_image);
  console.log('Images array:', product.images);
  console.log('Direct image:', product.image);
  console.log('Best URL:', extractBestImageUrl(product));
  console.groupEnd();
};

export default {
  processImageUrl,
  extractBestImageUrl,
  createPrimaryImageObject,
  transformFeaturedProductToListItem,
  testImageUrl,
  findBestWorkingImageUrl,
  logImageDebug
};
