/**
 * Composant dynamique pour afficher les services depuis l'API
 */

import React from 'react';
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowRight, Wifi, Monitor, Smartphone, Server, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import homeApi from "@/services/homeApi";

// Map des icônes disponibles
const iconMap: Record<string, React.ComponentType<any>> = {
  Wifi,
  Monitor,
  Smartphone,
  Server,
};

// Map des couleurs
const colorMap: Record<string, string> = {
  blue: 'blue',
  green: 'green',
  purple: 'purple',
  orange: 'orange',
  red: 'red',
  yellow: 'yellow',
  indigo: 'indigo',
  pink: 'pink',
};

const DynamicServices: React.FC = () => {
  // Récupérer les données dynamiques
  const {
    data: homeData,
    isLoading,
    isError
  } = useQuery({
    queryKey: ['dynamicHomePageData'],
    queryFn: homeApi.getDynamicHomePageData,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const services = homeData?.services || [];
  const content = homeData?.content?.find(c => c.section === 'services');

  // Services par défaut en cas d'erreur
  const defaultServices = [
    {
      id: 1,
      title: 'Réseaux & Connectivité',
      description: 'Installation et maintenance de réseaux informatiques, Wi-Fi, fibre optique et solutions de connectivité avancées.',
      icon: 'Wifi',
      color: 'blue',
      link: '/products'
    },
    {
      id: 2,
      title: 'Matériel Informatique',
      description: 'Vente d\'ordinateurs, serveurs, équipements réseau et accessoires informatiques de qualité professionnelle.',
      icon: 'Monitor',
      color: 'green',
      link: '/products'
    },
    {
      id: 3,
      title: 'Solutions Mobiles',
      description: 'Smartphones, tablettes, accessoires mobiles et solutions de mobilité pour entreprises et particuliers.',
      icon: 'Smartphone',
      color: 'purple',
      link: '/products'
    },
    {
      id: 4,
      title: 'Infrastructure IT',
      description: 'Conception, installation et maintenance d\'infrastructures IT complètes pour entreprises de toutes tailles.',
      icon: 'Server',
      color: 'orange',
      link: '/products'
    }
  ];

  const displayServices = services.length > 0 ? services : defaultServices;

  const getIconComponent = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || Server;
    return IconComponent;
  };

  const getColorClasses = (color: string) => {
    const colorKey = colorMap[color as keyof typeof colorMap] || 'blue';
    return {
      bg: `bg-${colorKey}-100`,
      bgHover: `group-hover:bg-${colorKey}-200`,
      text: `text-${colorKey}-600`,
    };
  };

  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          {isLoading ? (
            <>
              <Skeleton className="h-10 w-80 mx-auto mb-4" />
              <Skeleton className="h-4 w-96 mx-auto mb-6" />
              <Skeleton className="h-4 w-full max-w-3xl mx-auto" />
            </>
          ) : (
            <>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
                {content?.title || 'Nos Domaines d\'Expertise'}
              </h2>
              <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
              <p className="text-gray-600 max-w-3xl mx-auto text-lg">
                {content?.description || 'JOSNET NETWORK vous accompagne dans tous vos projets technologiques avec des solutions sur mesure et un service d\'excellence.'}
              </p>
            </>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {isLoading ? (
            // Skeleton pour les services
            Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                <Skeleton className="w-14 h-14 rounded-lg mb-4" />
                <Skeleton className="h-6 w-32 mb-3" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ))
          ) : (
            displayServices.map((service) => {
              const IconComponent = getIconComponent(service.icon);
              const colorClasses = getColorClasses(service.color);

              return (
                <div
                  key={service.id}
                  className="group bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-gray-100"
                >
                  <div className={`w-14 h-14 ${colorClasses.bg} ${colorClasses.bgHover} rounded-lg flex items-center justify-center mb-4 transition-colors`}>
                    <IconComponent className={`h-7 w-7 ${colorClasses.text}`} />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-800">{service.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{service.description}</p>
                </div>
              );
            })
          )}
        </div>

        <div className="text-center mt-12">
          <Link to="/products">
            <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-lg text-lg font-medium">
              Découvrir nos produits
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>

        {/* Indicateur d'erreur discret */}
        {isError && !isLoading && (
          <div className="text-center mt-8">
            <p className="text-gray-500 text-sm">
              * Contenu mis à jour automatiquement
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default DynamicServices;
