import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  MessageSquare,
  BarChart3,
  Users,
  Settings,
  User,
  Shield,
  Archive,
  FileText,
  Gift,
  CreditCard,
  RefreshCw,
  AlertTriangle,
  HelpCircle,
  LogOut,
  Bell,
  Search,
  Warehouse
} from 'lucide-react';
import { cn } from '@/lib/utils';

const StaffSidebar = () => {
  const location = useLocation();
  const { user, logout } = useAuth();

  // Navigation items for staff users
  const staffNavItems = [
    {
      title: "Tableau de bord",
      href: "/admin/dashboard",
      icon: LayoutDashboard,
      description: "Vue d'ensemble des activités"
    },
    {
      title: "Gestion des produits",
      href: "/admin/products",
      icon: Package,
      description: "Catalogue et inventaire",
      children: [
        { title: "Liste des produits", href: "/admin/products" },
        { title: "Ajouter un produit", href: "/admin/products/new" },
        { title: "Catégories", href: "/admin/categories" },
        { title: "Inventaire", href: "/admin/inventory" }
      ]
    },
    {
      title: "Gestion des commandes",
      href: "/admin/orders",
      icon: ShoppingCart,
      description: "Suivi et traitement",
      children: [
        { title: "Toutes les commandes", href: "/admin/orders" },
        { title: "Tableau de bord", href: "/admin/order-dashboard" },
        { title: "Créer une commande", href: "/admin/orders/create" }
      ]
    },
    {
      title: "Messagerie",
      href: "/admin/messages-modern",
      icon: MessageSquare,
      description: "Support client",
      badge: "Nouveau",
      children: [
        { title: "Messages modernes", href: "/admin/messages-modern" },
        { title: "Messages classiques", href: "/admin/messages" },
        { title: "Templates", href: "/admin/response-templates" }
      ]
    },
    {
      title: "Promotions",
      href: "/admin/promotions",
      icon: Gift,
      description: "Offres et réductions"
    },
    {
      title: "Transactions",
      href: "/admin/transactions",
      icon: CreditCard,
      description: "Paiements et factures",
      children: [
        { title: "Transactions", href: "/admin/transactions" },
        { title: "Factures", href: "/admin/invoices" }
      ]
    },
    {
      title: "Retours & SAV",
      href: "/admin/returns",
      icon: RefreshCw,
      description: "Service après-vente",
      children: [
        { title: "Retours", href: "/admin/returns" },
        { title: "Réclamations", href: "/admin/claims" }
      ]
    },
    {
      title: "Statistiques",
      href: "/admin/statistics",
      icon: BarChart3,
      description: "Rapports et analytics"
    },
    {
      title: "CMS",
      href: "/admin/cms",
      icon: FileText,
      description: "Gestion de contenu",
      children: [
        { title: "Pages", href: "/admin/cms" },
        { title: "Médias", href: "/admin/cms/media" }
      ]
    }
  ];

  // Restricted items (admin only) - shown as disabled for staff
  const restrictedItems = [
    {
      title: "Gestion des utilisateurs",
      href: "/admin/users",
      icon: Users,
      description: "Accès administrateur requis",
      restricted: true
    },
    {
      title: "Paramètres système",
      href: "/admin/message-settings",
      icon: Settings,
      description: "Configuration système",
      restricted: true
    },
    {
      title: "Logs système",
      href: "/admin/logs",
      icon: AlertTriangle,
      description: "Journaux d'activité",
      restricted: true
    },
    {
      title: "Méthodes de paiement",
      href: "/admin/payment-methods",
      icon: CreditCard,
      description: "Configuration paiements",
      restricted: true
    }
  ];

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const handleRestrictedClick = (e: React.MouseEvent, item: any) => {
    e.preventDefault();
    // Redirect to unauthorized page
    window.location.href = '/unauthorized';
  };

  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={user?.profile_picture} />
            <AvatarFallback>
              {user?.first_name?.[0]}{user?.last_name?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.full_name}
            </p>
            <div className="flex items-center space-x-2">
              <Badge className="bg-blue-100 text-blue-800 text-xs">
                <Shield className="h-3 w-3 mr-1" />
                Staff
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-1 px-3">
          {/* Staff Navigation Items */}
          <div className="space-y-1">
            {staffNavItems.map((item) => (
              <div key={item.href}>
                <Link
                  to={item.href}
                  className={cn(
                    "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    isActive(item.href)
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  )}
                >
                  <item.icon
                    className={cn(
                      "mr-3 h-5 w-5 flex-shrink-0",
                      isActive(item.href) ? "text-blue-500" : "text-gray-400"
                    )}
                  />
                  <span className="flex-1">{item.title}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </Link>
                
                {/* Sub-navigation */}
                {item.children && isActive(item.href) && (
                  <div className="ml-8 mt-1 space-y-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.href}
                        to={child.href}
                        className={cn(
                          "block px-3 py-1 text-xs rounded-md transition-colors",
                          location.pathname === child.href
                            ? "bg-blue-50 text-blue-600"
                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                        )}
                      >
                        {child.title}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Separator */}
          <div className="border-t border-gray-200 my-4" />

          {/* Restricted Items */}
          <div className="space-y-1">
            <div className="px-3 py-2">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Accès Restreint
              </h3>
            </div>
            {restrictedItems.map((item) => (
              <button
                key={item.href}
                onClick={(e) => handleRestrictedClick(e, item)}
                className="w-full group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-400 hover:bg-red-50 hover:text-red-600 transition-colors cursor-not-allowed"
                title={item.description}
              >
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                <span className="flex-1 text-left">{item.title}</span>
                <AlertTriangle className="h-4 w-4 ml-2" />
              </button>
            ))}
          </div>
        </nav>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4 space-y-2">
        {/* Profile Link */}
        <Link
          to="/admin/staff-profile"
          className={cn(
            "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
            location.pathname === "/admin/staff-profile"
              ? "bg-blue-100 text-blue-700"
              : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
          )}
        >
          <User className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400" />
          Mon Profil
        </Link>

        {/* Help */}
        <Link
          to="/help"
          className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
        >
          <HelpCircle className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400" />
          Aide
        </Link>

        {/* Logout */}
        <button
          onClick={logout}
          className="w-full group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors"
        >
          <LogOut className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-red-500" />
          Déconnexion
        </button>
      </div>
    </div>
  );
};

export default StaffSidebar;
