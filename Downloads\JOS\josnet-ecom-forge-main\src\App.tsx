
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import Products from "./pages/Products";
import ProductDetail from "./pages/ProductDetail";
import Cart from "./pages/Cart";
import Checkout from "./pages/Checkout";
import OrderConfirmation from "./pages/OrderConfirmation";
import OrderTracking from "./pages/OrderTracking";
import Reviews from "./pages/Reviews";
import Contact from "./pages/Contact";
import FAQ from "./pages/FAQ";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import Blog from "./pages/Blog";
import BlogPost from "./pages/BlogPost";
import NotFound from "./pages/NotFound";
import Login from "./pages/Login";
import Register from "./pages/Register";
import About from "./pages/about/About";
import Shipping from "./pages/shipping/Shipping";
import Returns from "./pages/returns/Returns";

// Client account pages
import Dashboard from "./pages/account/Dashboard";
import OrderHistory from "./pages/account/OrderHistory";
import Profile from "./pages/account/Profile";
import AccountReturns from "./pages/account/Returns";
import ClaimDetail from "./pages/account/ClaimDetail";
import Invoices from "./pages/account/Invoices";
import Suggestions from "./pages/account/Suggestions";

// Admin pages
import AdminDashboard from "./pages/admin/Dashboard";
import AdminProducts from "./pages/admin/AdminProducts";
import ProductForm from "./pages/admin/ProductForm";
import AdminCategories from "./pages/admin/Categories";
import AdminOrders from "./pages/admin/Orders";
import OrderDetailPage from "./pages/admin/OrderDetail";
import OrderCreatePage from "./pages/admin/OrderCreate";
import OrderEditPage from "./pages/admin/OrderEdit";
import OrderDashboardPage from "./pages/admin/OrderDashboard";
import AdminStatistics from "./pages/admin/Statistics";
import AdminUsers from "./pages/admin/Users";
import UserDetail from "./pages/admin/UserDetail";
import AdminInventory from "./pages/admin/Inventory";
import InventoryManagement from "./pages/admin/products/InventoryManagement";
import AdminPromotions from "./pages/admin/Promotions";
import AdminMessages from "./pages/admin/Messages";
import AdminCMS from "./pages/admin/CMS";
import AdminLogs from "./pages/admin/Logs";
import PaymentMethods from "./pages/admin/PaymentMethods";
import PaymentMethodFormPage from "./pages/admin/PaymentMethodForm";
import Transactions from "./pages/admin/Transactions";
import TransactionDetailPage from "./pages/admin/TransactionDetail";
import AdminInvoices from "./pages/admin/Invoices";
import InvoiceDetailPage from "./pages/admin/InvoiceDetail";

// Import ProtectedRoute
import ProtectedRoute from "@/components/auth/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/products" element={<Products />} />
            <Route path="/product/:id" element={<ProductDetail />} />
            <Route path="/cart" element={<Cart />} />
            <Route path="/reviews" element={<Reviews />} />
            <Route path="/reviews/:productId" element={<Reviews />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/faq" element={<FAQ />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/blog/:id" element={<BlogPost />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/about" element={<About />} />
            <Route path="/shipping" element={<Shipping />} />
            <Route path="/returns" element={<Returns />} />

            {/* Protected routes that require authentication */}
            <Route path="/checkout" element={
              <ProtectedRoute>
                <Checkout />
              </ProtectedRoute>
            } />
            <Route path="/order-confirmation/:orderId" element={
              <ProtectedRoute>
                <OrderConfirmation />
              </ProtectedRoute>
            } />
            <Route path="/order-tracking" element={
              <ProtectedRoute>
                <OrderTracking />
              </ProtectedRoute>
            } />

            {/* Client account routes - require customer role */}
            <Route path="/account" element={
              <ProtectedRoute requiredRole="customer">
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/account/orders" element={
              <ProtectedRoute requiredRole="customer">
                <OrderHistory />
              </ProtectedRoute>
            } />
            <Route path="/account/profile" element={
              <ProtectedRoute requiredRole="customer">
                <Profile />
              </ProtectedRoute>
            } />
            <Route path="/account/returns" element={
              <ProtectedRoute requiredRole="customer">
                <AccountReturns />
              </ProtectedRoute>
            } />
            <Route path="/account/claims/:id" element={
              <ProtectedRoute requiredRole="customer">
                <ClaimDetail />
              </ProtectedRoute>
            } />
            <Route path="/account/invoices" element={
              <ProtectedRoute requiredRole="customer">
                <Invoices />
              </ProtectedRoute>
            } />
            <Route path="/account/suggestions" element={
              <ProtectedRoute requiredRole="customer">
                <Suggestions />
              </ProtectedRoute>
            } />

            {/* Admin routes - require admin role */}
            <Route path="/admin/dashboard" element={
              <ProtectedRoute requiredRole="admin">
                <AdminDashboard />
              </ProtectedRoute>
            } />
            <Route path="/admin/products" element={
              <ProtectedRoute requiredRole="admin">
                <AdminProducts />
              </ProtectedRoute>
            } />
            <Route path="/admin/products/new" element={
              <ProtectedRoute requiredRole="admin">
                <ProductForm />
              </ProtectedRoute>
            } />
            <Route path="/admin/products/edit/:slug" element={
              <ProtectedRoute requiredRole="admin">
                <ProductForm />
              </ProtectedRoute>
            } />
            <Route path="/admin/categories" element={
              <ProtectedRoute requiredRole="admin">
                <AdminCategories />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders" element={
              <ProtectedRoute requiredRole="admin">
                <AdminOrders />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/:id" element={
              <ProtectedRoute requiredRole="admin">
                <OrderDetailPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/create" element={
              <ProtectedRoute requiredRole="admin">
                <OrderCreatePage />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/:id/edit" element={
              <ProtectedRoute requiredRole="admin">
                <OrderEditPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/order-dashboard" element={
              <ProtectedRoute requiredRole="admin">
                <OrderDashboardPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/statistics" element={
              <ProtectedRoute requiredRole="admin">
                <AdminStatistics />
              </ProtectedRoute>
            } />
            <Route path="/admin/users" element={
              <ProtectedRoute requiredRole="admin">
                <AdminUsers />
              </ProtectedRoute>
            } />
            <Route path="/admin/users/:id" element={
              <ProtectedRoute requiredRole="admin">
                <UserDetail />
              </ProtectedRoute>
            } />
            <Route path="/admin/inventory" element={
              <ProtectedRoute requiredRole="admin">
                <AdminInventory />
              </ProtectedRoute>
            } />
            <Route path="/admin/products/inventory" element={
              <ProtectedRoute requiredRole="admin">
                <InventoryManagement />
              </ProtectedRoute>
            } />
            <Route path="/admin/promotions" element={
              <ProtectedRoute requiredRole="admin">
                <AdminPromotions />
              </ProtectedRoute>
            } />
            <Route path="/admin/messages" element={
              <ProtectedRoute requiredRole="admin">
                <AdminMessages />
              </ProtectedRoute>
            } />
            <Route path="/admin/cms" element={
              <ProtectedRoute requiredRole="admin">
                <AdminCMS />
              </ProtectedRoute>
            } />
            <Route path="/admin/logs" element={
              <ProtectedRoute requiredRole="admin">
                <AdminLogs />
              </ProtectedRoute>
            } />

            {/* Payment routes */}
            <Route path="/admin/payment-methods" element={
              <ProtectedRoute requiredRole="admin">
                <PaymentMethods />
              </ProtectedRoute>
            } />
            <Route path="/admin/payment-methods/create" element={
              <ProtectedRoute requiredRole="admin">
                <PaymentMethodFormPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/payment-methods/:id/edit" element={
              <ProtectedRoute requiredRole="admin">
                <PaymentMethodFormPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/transactions" element={
              <ProtectedRoute requiredRole="admin">
                <Transactions />
              </ProtectedRoute>
            } />
            <Route path="/admin/transactions/:id" element={
              <ProtectedRoute requiredRole="admin">
                <TransactionDetailPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/invoices" element={
              <ProtectedRoute requiredRole="admin">
                <AdminInvoices />
              </ProtectedRoute>
            } />
            <Route path="/admin/invoices/:id" element={
              <ProtectedRoute requiredRole="admin">
                <InvoiceDetailPage />
              </ProtectedRoute>
            } />

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
