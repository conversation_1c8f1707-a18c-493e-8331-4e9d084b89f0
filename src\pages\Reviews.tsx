
import { useState } from "react";
import { useParams } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Star, Filter } from "lucide-react";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { products } from "@/utils/data";

// Mock reviews data
const mockReviews = [
  {
    id: 1,
    productId: 1,
    name: "<PERSON>",
    rating: 5,
    date: "15/05/2025",
    title: "Excellent produit !",
    comment: "Ce routeur a considérablement amélioré ma connexion internet. Installation facile et performances au rendez-vous.",
    helpful: 12,
    verified: true
  },
  {
    id: 2,
    productId: 1,
    name: "<PERSON>",
    rating: 4,
    date: "28/04/2025",
    title: "Très bon rapport qualité-prix",
    comment: "Bon produit pour le prix. La couverture WiFi est excellente même dans les zones éloignées de ma maison. L'installation était un peu compliquée mais le service client m'a bien aidé.",
    helpful: 8,
    verified: true
  },
  {
    id: 3,
    productId: 2,
    name: "Thomas <PERSON>",
    rating: 5,
    date: "10/05/2025",
    title: "Puissant et élégant",
    comment: "Cet ordinateur portable est exactement ce dont j'avais besoin. Rapide, léger et avec une excellente autonomie de batterie.",
    helpful: 15,
    verified: true
  },
  {
    id: 4,
    productId: 3,
    name: "Sophie Dubois",
    rating: 3,
    date: "02/05/2025",
    title: "Bon téléphone mais batterie moyenne",
    comment: "Le téléphone est bien dans l'ensemble, rapide et avec un bon appareil photo, mais l'autonomie de la batterie pourrait être meilleure.",
    helpful: 6,
    verified: false
  }
];

const Reviews = () => {
  const { productId } = useParams<{ productId: string }>();
  const [activeFilter, setActiveFilter] = useState("all");
  
  // Determine if we're showing reviews for a specific product
  const isProductSpecific = !!productId;
  
  // Find product if we have a productId
  const product = isProductSpecific 
    ? products.find(p => p.id === Number(productId))
    : null;
  
  // Filter reviews based on productId if provided
  const filteredReviews = isProductSpecific
    ? mockReviews.filter(review => review.productId === Number(productId))
    : mockReviews;
  
  // Calculate average rating
  const averageRating = filteredReviews.reduce((acc, review) => acc + review.rating, 0) / filteredReviews.length;
  
  // Count ratings
  const ratingCounts = filteredReviews.reduce((acc, review) => {
    acc[review.rating] = (acc[review.rating] || 0) + 1;
    return acc;
  }, {} as {[key: number]: number});
  
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2">
          {isProductSpecific ? `Avis sur ${product?.name}` : 'Tous les avis clients'}
        </h1>
        <p className="text-gray-600 mb-8">
          {isProductSpecific 
            ? `Découvrez ce que nos clients pensent du ${product?.name}`
            : 'Découvrez ce que nos clients pensent de nos produits'
          }
        </p>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Summary and filters */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <h2 className="text-xl font-medium mb-4">Résumé des avis</h2>
              
              {/* Average rating */}
              <div className="flex items-center mb-6">
                <div className="mr-4">
                  <span className="text-4xl font-bold">{averageRating.toFixed(1)}</span>
                  <span className="text-gray-500">/5</span>
                </div>
                <div>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star 
                        key={star} 
                        size={16} 
                        className={star <= Math.round(averageRating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
                      />
                    ))}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">Basé sur {filteredReviews.length} avis</p>
                </div>
              </div>
              
              {/* Rating breakdown */}
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center">
                    <div className="w-16 flex items-center">
                      <span className="mr-1">{rating}</span>
                      <Star size={14} className="text-yellow-400 fill-yellow-400" />
                    </div>
                    <div className="flex-grow mx-2">
                      <div className="h-2 rounded-full bg-gray-200 overflow-hidden">
                        <div 
                          className="h-full bg-yellow-400" 
                          style={{ width: `${((ratingCounts[rating] || 0) / filteredReviews.length) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="w-8 text-right text-sm text-gray-600">
                      {ratingCounts[rating] || 0}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Filter size={16} className="mr-2" />
                <h2 className="font-medium">Filtres</h2>
              </div>
              
              <div className="space-y-2">
                <button
                  onClick={() => setActiveFilter("all")}
                  className={`w-full text-left px-3 py-2 rounded ${
                    activeFilter === "all" ? "bg-primary-dark text-white" : "hover:bg-gray-100"
                  }`}
                >
                  Tous les avis
                </button>
                <button
                  onClick={() => setActiveFilter("verified")}
                  className={`w-full text-left px-3 py-2 rounded ${
                    activeFilter === "verified" ? "bg-primary-dark text-white" : "hover:bg-gray-100"
                  }`}
                >
                  Achats vérifiés
                </button>
                <button
                  onClick={() => setActiveFilter("positive")}
                  className={`w-full text-left px-3 py-2 rounded ${
                    activeFilter === "positive" ? "bg-primary-dark text-white" : "hover:bg-gray-100"
                  }`}
                >
                  Avis positifs (4-5★)
                </button>
                <button
                  onClick={() => setActiveFilter("critical")}
                  className={`w-full text-left px-3 py-2 rounded ${
                    activeFilter === "critical" ? "bg-primary-dark text-white" : "hover:bg-gray-100"
                  }`}
                >
                  Avis critiques (1-3★)
                </button>
              </div>
            </div>
            
            {/* Write a review button (only for product-specific page) */}
            {isProductSpecific && (
              <div className="mt-6">
                <Button className="w-full">
                  Écrire un avis
                </Button>
              </div>
            )}
          </div>
          
          {/* Reviews list */}
          <div className="lg:col-span-2">
            {/* Search bar */}
            <div className="relative mb-6">
              <Input
                placeholder="Rechercher dans les avis..."
                className="pl-10"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 absolute top-3 left-3 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            
            {/* Tabs */}
            <Tabs defaultValue="recent" className="mb-6">
              <TabsList>
                <TabsTrigger value="recent">Les plus récents</TabsTrigger>
                <TabsTrigger value="helpful">Les plus utiles</TabsTrigger>
                <TabsTrigger value="highest">Meilleures notes</TabsTrigger>
                <TabsTrigger value="lowest">Notes les plus basses</TabsTrigger>
              </TabsList>
            </Tabs>
            
            {/* Reviews */}
            {filteredReviews.length > 0 ? (
              <div className="space-y-6">
                {filteredReviews.map((review) => (
                  <div key={review.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center mb-1">
                          <div className="flex mr-2">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star 
                                key={star} 
                                size={16} 
                                className={star <= review.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
                              />
                            ))}
                          </div>
                          <h3 className="font-medium">{review.title}</h3>
                        </div>
                        <p className="text-sm text-gray-500 mb-3">
                          Par {review.name} le {review.date}
                          {review.verified && (
                            <span className="ml-2 text-green-600">✓ Achat vérifié</span>
                          )}
                        </p>
                      </div>
                      
                      {!isProductSpecific && (
                        <a href={`/product/${review.productId}`} className="text-primary hover:underline text-sm">
                          Voir le produit
                        </a>
                      )}
                    </div>
                    
                    <p className="text-gray-700 mb-4">{review.comment}</p>
                    
                    <div className="flex items-center justify-between">
                      <button className="text-sm flex items-center text-gray-600 hover:text-gray-900">
                        <span className="mr-1">Utile ({review.helpful})</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                          />
                        </svg>
                      </button>
                      <button className="text-sm text-primary hover:underline">
                        Signaler
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <h3 className="text-lg font-medium mb-2">Aucun avis pour le moment</h3>
                <p className="text-gray-600 mb-4">
                  Soyez le premier à partager votre expérience avec ce produit.
                </p>
                <Button>Écrire un avis</Button>
              </div>
            )}
            
            {/* Pagination */}
            {filteredReviews.length > 0 && (
              <div className="flex justify-center mt-8">
                <nav className="inline-flex">
                  <a
                    href="#"
                    className="px-3 py-2 border border-gray-300 rounded-l-md bg-white text-gray-700 hover:bg-gray-50"
                  >
                    Précédent
                  </a>
                  <a
                    href="#"
                    className="px-3 py-2 border-t border-b border-gray-300 bg-primary-dark text-white"
                  >
                    1
                  </a>
                  <a
                    href="#"
                    className="px-3 py-2 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                  >
                    2
                  </a>
                  <a
                    href="#"
                    className="px-3 py-2 border border-gray-300 rounded-r-md bg-white text-gray-700 hover:bg-gray-50"
                  >
                    Suivant
                  </a>
                </nav>
              </div>
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Reviews;
