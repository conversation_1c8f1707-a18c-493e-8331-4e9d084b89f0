#!/usr/bin/env python
"""
Test complet du système CMS et affichage côté client.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from cms.models import Page, BlogPost, BlogCategory, SiteSetting
from core.models import SiteSettings, Service, Testimonial

User = get_user_model()


def create_admin_user():
    """Créer un utilisateur admin pour les tests CMS."""
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Admin',
            'last_name': 'CMS',
            'password': 'adminpass123',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'role': 'admin'
        }
    )
    
    if created:
        admin_user.set_password('adminpass123')
        admin_user.save()
    else:
        admin_user.role = 'admin'
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.save()
    
    return admin_user


def test_cms_pages_creation():
    """Test de création de pages CMS."""
    print("📄 TEST: CRÉATION DE PAGES CMS")
    print("=" * 60)
    
    admin_user = create_admin_user()
    
    # Créer un token JWT
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    
    # Configurer le client API
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    # Nettoyer les anciennes pages de test
    Page.objects.filter(title__contains='Test CMS').delete()
    
    # Données de test pour une page
    page_data = {
        'title': 'Test CMS - Page d\'accueil',
        'slug': 'test-cms-page-accueil',
        'content': '''
        <h1>Bienvenue chez JosNet Network</h1>
        <p>Votre partenaire technologique de confiance au Burundi.</p>
        <h2>Nos Services</h2>
        <ul>
            <li>Vente d'équipements informatiques</li>
            <li>Solutions de télécommunications</li>
            <li>Support technique 24/7</li>
        </ul>
        ''',
        'template': 'homepage',
        'status': 'published',
        'meta_description': 'Page d\'accueil de JosNet Network - Solutions IT au Burundi',
        'meta_keywords': 'JosNet, IT, Burundi, technologie, informatique'
    }
    
    print(f"🔄 Création d'une page CMS...")
    response = client.post('/api/v1/cms/pages/', page_data, format='json')
    
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 201:
        data = response.json()
        print(f"   ✅ Page créée: {data['title']}")
        print(f"   📝 Slug: {data['slug']}")
        print(f"   📊 Statut: {data['status']}")
        print(f"   🎨 Template: {data['template']}")
        
        # Test de récupération de la page
        print(f"\n🔄 Récupération de la page...")
        response = client.get(f"/api/v1/cms/pages/{data['slug']}/")
        
        if response.status_code == 200:
            page_data = response.json()
            print(f"   ✅ Page récupérée: {page_data['title']}")
            return True
        else:
            print(f"   ❌ Erreur récupération: {response.status_code}")
            return False
    else:
        print(f"   ❌ Erreur création: {response.content.decode()}")
        return False


def test_cms_blog_posts():
    """Test de création d'articles de blog."""
    print("\n📝 TEST: CRÉATION D'ARTICLES DE BLOG")
    print("=" * 60)
    
    admin_user = create_admin_user()
    
    # Créer un token JWT
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    
    # Configurer le client API
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    # Créer une catégorie de blog
    category_data = {
        'name': 'Actualités Tech',
        'slug': 'actualites-tech',
        'description': 'Les dernières actualités technologiques'
    }
    
    print(f"🔄 Création d'une catégorie de blog...")
    response = client.post('/api/v1/cms/blog/categories/', category_data, format='json')
    
    if response.status_code == 201:
        category = response.json()
        print(f"   ✅ Catégorie créée: {category['name']}")
        
        # Créer un article de blog
        blog_data = {
            'title': 'Test CMS - Les nouvelles technologies en 2024',
            'slug': 'test-cms-nouvelles-technologies-2024',
            'content': '''
            <h1>Les nouvelles technologies qui révolutionnent 2024</h1>
            <p>Cette année marque un tournant dans l'évolution technologique...</p>
            <h2>Intelligence Artificielle</h2>
            <p>L'IA continue de transformer notre quotidien...</p>
            <h2>5G et Connectivité</h2>
            <p>La 5G ouvre de nouvelles possibilités...</p>
            ''',
            'excerpt': 'Découvrez les technologies qui façonnent l\'avenir en 2024.',
            'status': 'published',
            'featured': True,
            'categories': [category['id']],
            'meta_description': 'Les nouvelles technologies de 2024 - JosNet Network',
            'meta_keywords': 'technologie, 2024, IA, 5G, innovation'
        }
        
        print(f"🔄 Création d'un article de blog...")
        response = client.post('/api/v1/cms/blog/posts/', blog_data, format='json')
        
        if response.status_code == 201:
            article = response.json()
            print(f"   ✅ Article créé: {article['title']}")
            print(f"   📝 Slug: {article['slug']}")
            print(f"   ⭐ Mis en avant: {article['featured']}")
            return True
        else:
            print(f"   ❌ Erreur création article: {response.content.decode()}")
            return False
    else:
        print(f"   ❌ Erreur création catégorie: {response.content.decode()}")
        return False


def test_cms_public_access():
    """Test d'accès public aux données CMS."""
    print("\n🌐 TEST: ACCÈS PUBLIC AUX DONNÉES CMS")
    print("=" * 60)
    
    # Client sans authentification (accès public)
    client = APIClient()
    
    print(f"🔄 Test d'accès public aux pages...")
    response = client.get('/api/v1/cms/pages/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        pages_count = len(data.get('results', []))
        print(f"   ✅ Pages publiques accessibles: {pages_count}")
        
        # Afficher les pages publiées
        for page in data.get('results', []):
            if page['status'] == 'published':
                print(f"      📄 {page['title']} ({page['slug']})")
    else:
        print(f"   ❌ Erreur accès pages: {response.status_code}")
        return False
    
    print(f"\n🔄 Test d'accès public aux articles de blog...")
    response = client.get('/api/v1/cms/blog/posts/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        posts_count = len(data.get('results', []))
        print(f"   ✅ Articles publics accessibles: {posts_count}")
        
        # Afficher les articles publiés
        for post in data.get('results', []):
            if post['status'] == 'published':
                print(f"      📝 {post['title']} ({post['slug']})")
                if post['featured']:
                    print(f"         ⭐ Article mis en avant")
    else:
        print(f"   ❌ Erreur accès articles: {response.status_code}")
        return False
    
    return True


def test_site_settings():
    """Test des paramètres du site."""
    print("\n⚙️  TEST: PARAMÈTRES DU SITE")
    print("=" * 60)
    
    # Créer ou mettre à jour les paramètres du site
    site_settings, created = SiteSettings.objects.get_or_create(
        defaults={
            'site_title': 'JosNet Network',
            'site_description': 'Votre partenaire technologique de confiance au Burundi',
            'contact_email': '<EMAIL>',
            'contact_phone': '+257 22 123 456',
            'address': 'Bujumbura, Burundi',
            'facebook_url': 'https://facebook.com/josnetnetwork',
            'twitter_url': 'https://twitter.com/josnetnetwork',
            'linkedin_url': 'https://linkedin.com/company/josnetnetwork',
            'instagram_url': 'https://instagram.com/josnetnetwork'
        }
    )
    
    if created:
        print(f"   ✅ Paramètres du site créés")
    else:
        print(f"   ✅ Paramètres du site existants")
    
    print(f"      📝 Titre: {site_settings.site_title}")
    print(f"      📧 Email: {site_settings.contact_email}")
    print(f"      📞 Téléphone: {site_settings.contact_phone}")
    
    # Test d'accès public aux paramètres
    client = APIClient()
    response = client.get('/api/v1/core/site-settings/')
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Paramètres accessibles publiquement")
        return True
    else:
        print(f"   ❌ Erreur accès paramètres: {response.status_code}")
        return False


def main():
    """Fonction principale."""
    print("🚀 TEST COMPLET DU SYSTÈME CMS")
    print("=" * 80)
    print("Ce test vérifie :")
    print("1. 📄 Création et gestion des pages CMS")
    print("2. 📝 Création et gestion des articles de blog")
    print("3. 🌐 Accès public aux données CMS")
    print("4. ⚙️  Configuration des paramètres du site")
    print("=" * 80)
    
    results = []
    
    try:
        # Test 1: Pages CMS
        print("🧪 PHASE 1: Test des pages CMS")
        pages_success = test_cms_pages_creation()
        results.append(("Création et gestion des pages CMS", pages_success))
        
        # Test 2: Articles de blog
        print("\n🧪 PHASE 2: Test des articles de blog")
        blog_success = test_cms_blog_posts()
        results.append(("Création et gestion des articles de blog", blog_success))
        
        # Test 3: Accès public
        print("\n🧪 PHASE 3: Test d'accès public")
        public_success = test_cms_public_access()
        results.append(("Accès public aux données CMS", public_success))
        
        # Test 4: Paramètres du site
        print("\n🧪 PHASE 4: Test des paramètres du site")
        settings_success = test_site_settings()
        results.append(("Configuration des paramètres du site", settings_success))
        
        # Résumé final
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ DU SYSTÈME CMS")
        print("=" * 80)
        
        passed = 0
        failed = 0
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print(f"\nTotal: {len(results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print(f"\n🎉 SYSTÈME CMS 100% OPÉRATIONNEL !")
            print(f"✅ Les pages CMS sont créées et accessibles")
            print(f"✅ Les articles de blog fonctionnent")
            print(f"✅ L'accès public fonctionne")
            print(f"✅ Les paramètres du site sont configurés")
            print(f"🌐 Le contenu est prêt pour affichage côté client")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DES TESTS CMS: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
