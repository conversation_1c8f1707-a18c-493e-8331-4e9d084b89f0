import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import AccountLayout from '@/components/account/AccountLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, ChevronLeft, AlertCircle, Package, Truck, FileText, ArrowLeft, ArrowRight } from 'lucide-react';
import { accountApi, ReturnItem } from '@/services/accountApi';
import { formatCurrency } from '@/utils/formatters';

const ReturnDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Fetch return details
  const {
    data: returnDetails,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['return', id],
    queryFn: () => accountApi.getReturnDetails(id),
    enabled: !!id
  });

  // Format status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Approuvu00e9':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">{status}</Badge>;
      case 'En cours':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">{status}</Badge>;
      case 'Reu00e7u':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">{status}</Badge>;
      case 'Refusu00e9':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">{status}</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">{status}</Badge>;
    }
  };

  // Get timeline step based on status
  const getTimelineStep = (status: string) => {
    switch (status) {
      case 'Approuvu00e9':
        return 1;
      case 'En cours':
        return 2;
      case 'Reu00e7u':
        return 3;
      case 'Remboursu00e9':
        return 4;
      default:
        return 1;
    }
  };

  if (isLoading) {
    return (
      <AccountLayout title="Du00e9tails du retour">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 text-primary animate-spin" />
          <span className="ml-2">Chargement des du00e9tails...</span>
        </div>
      </AccountLayout>
    );
  }

  if (isError || !returnDetails) {
    return (
      <AccountLayout title="Du00e9tails du retour">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10 text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
            <p className="text-gray-500 max-w-sm mb-4">
              Une erreur est survenue lors du chargement des du00e9tails du retour. Veuillez ru00e9essayer.
            </p>
            <div className="flex gap-3">
              <Button variant="outline" onClick={() => navigate('/account/returns')}>
                <ChevronLeft className="mr-2 h-4 w-4" />
                Retour
              </Button>
              <Button onClick={() => refetch()}>
                Ru00e9essayer
              </Button>
            </div>
          </CardContent>
        </Card>
      </AccountLayout>
    );
  }

  const timelineStep = getTimelineStep(returnDetails?.status || '');

  return (
    <AccountLayout title="Du00e9tails du retour">
      <div className="mb-6">
        <Button 
          variant="outline" 
          className="mb-4" 
          onClick={() => navigate('/account/returns')}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Retour u00e0 mes retours
        </Button>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Return details */}
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">Retour #{returnDetails?.id || 'N/A'}</CardTitle>
                    <CardDescription>Commande #{returnDetails?.orderId || 'N/A'} • {returnDetails?.date || 'N/A'}</CardDescription>
                  </div>
                  {returnDetails?.status && getStatusBadge(returnDetails.status)}
                </div>
              </CardHeader>
              <CardContent>
                {/* Timeline */}
                <div className="mb-8">
                  <h3 className="text-sm font-medium text-gray-500 mb-4">Statut du retour</h3>
                  <div className="relative">
                    <div className="absolute left-0 top-4 w-full h-0.5 bg-gray-200"></div>
                    <div className="relative flex justify-between">
                      <div className="flex flex-col items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${timelineStep >= 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-500'}`}>
                          <Package className="h-4 w-4" />
                        </div>
                        <span className="text-xs mt-2">Approuvu00e9</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${timelineStep >= 2 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-500'}`}>
                          <Truck className="h-4 w-4" />
                        </div>
                        <span className="text-xs mt-2">En transit</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${timelineStep >= 3 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-500'}`}>
                          <Package className="h-4 w-4" />
                        </div>
                        <span className="text-xs mt-2">Reu00e7u</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${timelineStep >= 4 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-500'}`}>
                          <ArrowRight className="h-4 w-4" />
                        </div>
                        <span className="text-xs mt-2">Remboursu00e9</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Items */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-4">Articles retournu00e9s</h3>
                  <div className="space-y-4">
                    {returnDetails?.returnItems && returnDetails.returnItems.length > 0 ? (
                      returnDetails.returnItems.map((item) => (
                        <div key={item.id} className="flex border rounded-md p-4">
                          <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden mr-4 flex-shrink-0">
                            <img
                              src={item.image}
                              alt={item.productName}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-grow">
                            <h4 className="font-medium">{item.productName}</h4>
                            <div className="flex flex-wrap gap-x-4 text-sm text-gray-500 mt-1">
                              <span>Quantité: {item.quantity}</span>
                              <span>Prix: {item.price}</span>
                            </div>
                            <div className="mt-2">
                              <span className="text-sm font-medium">Raison: </span>
                              <span className="text-sm">{item.reason}</span>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <p>Aucun article trouvé pour ce retour</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Refund information */}
                <div className="mt-6 border-t pt-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Informations de remboursement</h3>
                  <div className="flex justify-between items-center">
                    <span>Montant du remboursement:</span>
                    <span className="font-medium">{returnDetails?.refundAmount || '0 €'}</span>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span>Mu00e9thode de remboursement:</span>
                    <span>Carte bancaire (xxxx-xxxx-xxxx-4567)</span>
                  </div>
                  {returnDetails?.status === 'Remboursé' && (
                    <div className="flex justify-between items-center mt-1">
                      <span>Date de remboursement:</span>
                      <span>15/05/2025</span>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <div className="w-full flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      const orderId = returnDetails?.orderId || returnDetails?.order;
                      if (orderId) {
                        navigate(`/account/orders/${orderId}`);
                      } else {
                        console.error('Aucun ID de commande trouvé:', returnDetails);
                      }
                    }}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Voir la commande
                  </Button>
                  
                  {returnDetails?.status === 'Approuvé' && (
                    <Button 
                      variant="outline"
                      onClick={() => window.print()}
                    >
                      Imprimer l'u00e9tiquette
                    </Button>
                  )}
                </div>
              </CardFooter>
            </Card>
          </div>
          
          {/* Sidebar */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Informations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium">Statut</h4>
                  <div className="mt-1">{returnDetails?.status && getStatusBadge(returnDetails.status)}</div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium">Commande associu00e9e</h4>
                  <Button 
                    variant="link" 
                    className="p-0 h-auto text-sm mt-1"
                    onClick={() => {
                      const orderId = returnDetails?.orderId || returnDetails?.order;
                      if (orderId) {
                        navigate(`/account/orders/${orderId}`);
                      } else {
                        console.error('Aucun ID de commande trouvé:', returnDetails);
                      }
                    }}
                  >
                    Voir la commande {returnDetails?.orderId}
                    <FileText className="h-3 w-3 ml-1" />
                  </Button>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium">Instructions</h4>
                  <ol className="text-sm text-gray-500 mt-1 space-y-2 list-decimal list-inside">
                    <li>Imprimez l'u00e9tiquette de retour</li>
                    <li>Emballez soigneusement les articles</li>
                    <li>Collez l'u00e9tiquette sur le colis</li>
                    <li>Du00e9posez le colis au point de collecte</li>
                  </ol>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium">Besoin d'aide ?</h4>
                  <p className="text-sm text-gray-500 mt-1">
                    Si vous avez des questions concernant votre retour, n'hu00e9sitez pas u00e0 nous contacter.
                  </p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2 w-full"
                    onClick={() => navigate('/contact')}
                  >
                    Contacter le support
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AccountLayout>
  );
};

export default ReturnDetail;
