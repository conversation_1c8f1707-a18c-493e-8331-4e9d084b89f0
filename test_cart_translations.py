#!/usr/bin/env python3
"""
Script pour tester les traductions du panier
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from translations.models import TranslationKey, Translation

def test_cart_translations():
    """Tester les traductions du panier"""
    print("🧪 TEST DES TRADUCTIONS DU PANIER")
    print("=" * 50)
    
    # Clés de traduction du panier à tester
    cart_keys = [
        "cart.product_added",
        "cart.product_not_found", 
        "cart.product_unavailable",
        "cart.out_of_stock",
        "cart.added_to_cart",
        "cart.quantity_updated",
        "cart.added_to_your_cart",
        "cart.invalid_promo_code",
        "cart.promo_validation_error"
    ]
    
    languages = ['fr', 'en', 'sw', 'rn']
    
    print("🔍 VÉRIFICATION DES TRADUCTIONS:")
    
    all_good = True
    
    for key in cart_keys:
        try:
            translation_key = TranslationKey.objects.get(key=key)
            print(f"\n✅ Clé: {key}")
            
            for lang in languages:
                try:
                    translation = Translation.objects.get(
                        key=translation_key, 
                        language_code=lang
                    )
                    flag = {'fr': '🇫🇷', 'en': '🇬🇧', 'sw': '🇹🇿', 'rn': '🇧🇮'}[lang]
                    print(f"   {flag} {lang}: \"{translation.value}\"")
                except Translation.DoesNotExist:
                    print(f"   ❌ {lang}: MANQUANT")
                    all_good = False
                    
        except TranslationKey.DoesNotExist:
            print(f"❌ Clé manquante: {key}")
            all_good = False
    
    return all_good

def show_usage_examples():
    """Afficher des exemples d'utilisation"""
    print(f"\n💡 EXEMPLES D'UTILISATION DANS CARTCONTEXT.TSX")
    print("=" * 50)
    
    examples = [
        {
            "situation": "Produit ajouté avec quantité mise à jour",
            "code": 'title: t("cart.product_added", "Produit ajouté")',
            "description": "Message affiché quand un produit déjà dans le panier est ajouté à nouveau"
        },
        {
            "situation": "Produit non trouvé",
            "code": 'title: t("common.error", "Erreur"), description: t("cart.product_not_found", "Produit non trouvé")',
            "description": "Message d'erreur quand le produit n'existe pas"
        },
        {
            "situation": "Produit indisponible",
            "code": 'title: t("cart.product_unavailable", "Produit indisponible")',
            "description": "Message quand le produit est en rupture de stock"
        },
        {
            "situation": "Produit ajouté au panier",
            "code": 'title: t("cart.added_to_cart", "Produit ajouté au panier")',
            "description": "Message de confirmation d'ajout au panier"
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['situation']}:")
        print(f"   Code: {example['code']}")
        print(f"   Usage: {example['description']}")

def test_api_endpoint():
    """Tester l'endpoint API des traductions"""
    print(f"\n🌐 TEST DE L'API DE TRADUCTIONS")
    print("=" * 50)
    
    try:
        import requests
        
        # Tester l'endpoint des traductions
        response = requests.get('http://localhost:8000/api/translations/', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API accessible")
            print(f"📊 Traductions disponibles: {len(data.get('translations', {}))}")
            
            # Vérifier quelques traductions du panier
            translations = data.get('translations', {})
            cart_translations = {k: v for k, v in translations.items() if k.startswith('cart.')}
            
            print(f"🛒 Traductions du panier: {len(cart_translations)}")
            
            for key, value in list(cart_translations.items())[:3]:
                print(f"   • {key}: \"{value}\"")
                
        else:
            print(f"❌ API non accessible (status: {response.status_code})")
            
    except requests.exceptions.ConnectionError:
        print(f"⚠️ Serveur Django non démarré")
        print(f"   Démarrez avec: cd josnet_backend && python manage.py runserver")
    except Exception as e:
        print(f"❌ Erreur API: {e}")

def show_next_steps():
    """Afficher les prochaines étapes"""
    print(f"\n🎯 PROCHAINES ÉTAPES")
    print("=" * 50)
    
    print(f"1. 🚀 DÉMARRER LE SERVEUR:")
    print(f"   cd josnet_backend")
    print(f"   python manage.py runserver")
    
    print(f"\n2. 🌐 DÉMARRER LE FRONTEND:")
    print(f"   npm run dev")
    print(f"   # ou")
    print(f"   yarn dev")
    
    print(f"\n3. 🧪 TESTER LES TRADUCTIONS:")
    print(f"   • Ouvrez l'application dans le navigateur")
    print(f"   • Changez de langue avec le sélecteur")
    print(f"   • Ajoutez des produits au panier")
    print(f"   • Vérifiez que les messages sont traduits")
    
    print(f"\n4. 🔧 CONTINUER LA TRADUCTION:")
    print(f"   • Prochains composants: Navigation, ProductCard, Login")
    print(f"   • Utilisez le même pattern: t('key', 'fallback')")
    print(f"   • Ajoutez les nouvelles clés au système")
    
    print(f"\n📋 COMPOSANTS PRIORITAIRES À TRADUIRE ENSUITE:")
    print(f"   • src/components/navigation/")
    print(f"   • src/components/cart/Cart.tsx")
    print(f"   • src/components/auth/")
    print(f"   • src/pages/products/")

def main():
    """Fonction principale"""
    print("🧪 TEST COMPLET DES TRADUCTIONS DU PANIER")
    print("=" * 60)
    
    # Tester les traductions
    all_good = test_cart_translations()
    
    if all_good:
        print(f"\n🎉 TOUTES LES TRADUCTIONS DU PANIER SONT DISPONIBLES!")
    else:
        print(f"\n⚠️ Certaines traductions sont manquantes")
    
    # Afficher des exemples d'utilisation
    show_usage_examples()
    
    # Tester l'API
    test_api_endpoint()
    
    # Afficher les prochaines étapes
    show_next_steps()
    
    print(f"\n✅ CARTCONTEXT.TSX TRADUIT AVEC SUCCÈS!")
    print(f"\n📊 PROGRESSION:")
    print(f"   ✅ CartContext.tsx - Terminé")
    print(f"   🔄 Navigation - À faire")
    print(f"   🔄 Cart.tsx - À faire") 
    print(f"   🔄 Auth components - À faire")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
