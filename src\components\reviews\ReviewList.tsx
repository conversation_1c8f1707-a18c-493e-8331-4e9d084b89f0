import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Star, ThumbsUp, ThumbsDown, Shield, Calendar, MoreVertical } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import reviewApi, { Review } from '@/services/reviewApi';
import { useAuth } from '@/contexts/AuthContext';

interface ReviewListProps {
  productId: number;
  showCreateButton?: boolean;
  onCreateReview?: () => void;
}

const ReviewList: React.FC<ReviewListProps> = ({ 
  productId, 
  showCreateButton = true, 
  onCreateReview 
}) => {
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState('created_at');
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Récupérer les avis
  const { data: reviewsData, isLoading } = useQuery({
    queryKey: ['productReviews', productId, page, sortBy],
    queryFn: () => reviewApi.getProductReviews(productId, {
      page,
      ordering: sortBy === 'helpful' ? '-helpful_count' : `-${sortBy}`,
      page_size: 10
    }),
  });

  // Mutation pour voter sur l'utilité
  const voteHelpfulMutation = useMutation({
    mutationFn: ({ reviewId, isHelpful }: { reviewId: number; isHelpful: boolean }) =>
      reviewApi.voteHelpful(reviewId, isHelpful),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productReviews', productId] });
      toast({
        title: 'Vote enregistré',
        description: 'Merci pour votre feedback !',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.response?.data?.error || 'Impossible d\'enregistrer votre vote.',
        variant: 'destructive',
      });
    },
  });

  const handleVote = (reviewId: number, isHelpful: boolean) => {
    if (!user) {
      toast({
        title: 'Connexion requise',
        description: 'Vous devez être connecté pour voter.',
        variant: 'destructive',
      });
      return;
    }
    voteHelpfulMutation.mutate({ reviewId, isHelpful });
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[100px]" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const reviews = reviewsData?.results || [];

  return (
    <div className="space-y-6">
      {/* En-tête avec bouton de création et tri */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold">
            Avis clients ({reviewsData?.count || 0})
          </h3>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="created_at">Plus récents</option>
            <option value="rating">Mieux notés</option>
            <option value="helpful">Plus utiles</option>
          </select>
        </div>

        {showCreateButton && user && (
          <Button onClick={onCreateReview}>
            Écrire un avis
          </Button>
        )}
      </div>

      {/* Liste des avis */}
      {reviews.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">Aucun avis pour ce produit.</p>
            {showCreateButton && user && (
              <Button onClick={onCreateReview} className="mt-4">
                Soyez le premier à laisser un avis
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {reviews.map((review: Review) => (
            <Card key={review.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={review.user_avatar} />
                      <AvatarFallback>
                        {getInitials(review.user_name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{review.user_name}</span>
                        {review.is_verified_purchase && (
                          <Badge variant="secondary" className="text-xs">
                            <Shield className="h-3 w-3 mr-1" />
                            Achat vérifié
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 mt-1">
                        {renderStars(review.rating)}
                        <span className="text-sm text-gray-500">
                          <Calendar className="h-3 w-3 inline mr-1" />
                          {formatDistanceToNow(new Date(review.created_at), {
                            addSuffix: true,
                            locale: fr,
                          })}
                        </span>
                      </div>
                    </div>
                  </div>

                  {user && user.id !== review.user && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          Signaler cet avis
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <h4 className="font-medium mb-2">{review.title}</h4>
                <p className="text-gray-700 mb-4">{review.comment}</p>

                {review.pros && (
                  <div className="mb-3">
                    <span className="text-sm font-medium text-green-600">Points positifs :</span>
                    <p className="text-sm text-gray-600 mt-1">{review.pros}</p>
                  </div>
                )}

                {review.cons && (
                  <div className="mb-4">
                    <span className="text-sm font-medium text-red-600">Points négatifs :</span>
                    <p className="text-sm text-gray-600 mt-1">{review.cons}</p>
                  </div>
                )}

                {/* Boutons d'utilité */}
                <div className="flex items-center space-x-4 pt-3 border-t">
                  <span className="text-sm text-gray-500">Cet avis vous a-t-il été utile ?</span>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={review.user_vote_is_helpful === true ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleVote(review.id, true)}
                      disabled={voteHelpfulMutation.isPending || review.user_has_voted}
                    >
                      <ThumbsUp className="h-3 w-3 mr-1" />
                      {review.helpful_count}
                    </Button>
                    
                    <Button
                      variant={review.user_vote_is_helpful === false ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleVote(review.id, false)}
                      disabled={voteHelpfulMutation.isPending || review.user_has_voted}
                    >
                      <ThumbsDown className="h-3 w-3 mr-1" />
                      {review.not_helpful_count}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {reviewsData && reviewsData.count > 10 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setPage(page - 1)}
            disabled={!reviewsData.previous}
          >
            Précédent
          </Button>
          <Button
            variant="outline"
            onClick={() => setPage(page + 1)}
            disabled={!reviewsData.next}
          >
            Suivant
          </Button>
        </div>
      )}
    </div>
  );
};

export default ReviewList;
