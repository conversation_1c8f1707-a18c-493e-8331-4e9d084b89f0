
import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { formatCurrency } from '@/utils/currency';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Star, ShoppingCart, Truck, ShieldCheck, ArrowLeft, Loader2 } from "lucide-react";
import { useCart } from "@/contexts/CartContext";
import { toast } from "@/hooks/use-toast";
import { productApi } from "@/services/productApi";
import { formatImageUrl, handleImageError, getImageUrl } from "@/utils/imageUtils";
import { API_BASE_URL } from "@/services/apiConfig";

import ProductImage from "@/components/products/ProductImage";
import ProductReviews from "@/components/products/ProductReviews";
import reviewApi from "@/services/reviewApi";

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [quantity, setQuantity] = useState(1);
  const { addToCart } = useCart();

  // Récupérer les détails du produit depuis l'API
  const {
    data: product,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['product', id],
    queryFn: () => productApi.getProduct(id || ''),
    enabled: !!id,
    retry: 1,
    staleTime: 60000, // 1 minute
  });

  // Récupérer les statistiques d'avis
  const {
    data: reviewStats,
    isLoading: reviewStatsLoading
  } = useQuery({
    queryKey: ['reviewStats', id],
    queryFn: () => reviewApi.getProductReviewStats(Number(id)),
    enabled: !!id,
    retry: 1,
  });

  // Log des détails du produit pour le débogage
  useEffect(() => {
    if (product) {
      console.log('Détails complets du produit:', product);
      console.log('Images du produit:', product.images);
      console.log('Image principale:', product.primary_image);
      
      // Tester différentes URLs d'images possibles
      if (product.id === 39) {
        const testUrls = [
          `${API_BASE_URL}/media/products/${product.id}/primary.jpg`,
          `${API_BASE_URL}/media/products/${product.id}/1.jpg`,
          `${API_BASE_URL}/media/products/${product.id}.jpg`,
          `${API_BASE_URL}/media/products/images/${product.id}.jpg`,
          `${API_BASE_URL}/media/images/products/${product.id}.jpg`
        ];
        
        console.log('Test d\'URLs d\'images pour le produit 39:');
        testUrls.forEach((url, index) => {
          const img = new Image();
          img.onload = () => console.log(`URL #${index} fonctionne:`, url);
          img.onerror = () => console.log(`URL #${index} ne fonctionne pas:`, url);
          img.src = url;
        });
      }
    }
  }, [product]);
  
  // Afficher un état de chargement
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Skeleton className="h-[500px] w-full rounded-lg" />
            <div>
              <Skeleton className="h-8 w-32 mb-4" />
              <Skeleton className="h-12 w-3/4 mb-4" />
              <Skeleton className="h-6 w-1/4 mb-6" />
              <Skeleton className="h-10 w-full mb-4" />
              <Skeleton className="h-24 w-full mb-6" />
              <Skeleton className="h-12 w-1/2" />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Si le produit n'existe pas ou s'il y a une erreur, afficher un message
  if (isError || !product) {
    console.error('Erreur de chargement du produit:', error);
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-12 flex flex-col items-center justify-center">
          <h2 className="text-2xl font-bold mb-4">Produit non trouvé</h2>
          <p className="text-gray-600 mb-6">Le produit que vous recherchez n'existe pas ou a été retiré.</p>
          <Button asChild>
            <Link to="/products">Retourner au catalogue</Link>
          </Button>
        </main>
        <Footer />
      </div>
    );
  }

  // Handle quantity change
  const increaseQuantity = () => setQuantity(prev => prev + 1);
  const decreaseQuantity = () => setQuantity(prev => (prev > 1 ? prev - 1 : 1));

  // Handle add to cart
  const handleAddToCart = () => {
    if (product) {
      addToCart(product.id, quantity);
      toast({
        title: "Produit ajouté au panier",
        description: `${quantity} x ${product.name} ajouté à votre panier`,
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link to="/products" className="text-gray-600 hover:text-primary flex items-center">
            <ArrowLeft size={16} className="mr-2" />
            Retour aux produits
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Image */}
          <div className="bg-white rounded-lg overflow-hidden">
            <ProductImage 
              product={product} 
              className="w-full h-auto object-cover"
              style={{ maxHeight: '500px' }}
            />
          </div>

          {/* Product Info */}
          <div>
            <div className="flex items-center">
              {product.categories && product.categories.length > 0 && (
                <span className="bg-accent text-white px-3 py-1 rounded text-sm font-medium">
                  {product.categories[0].name}
                </span>
              )}
              {product.is_featured && (
                <span className="ml-2 bg-primary-dark text-white px-3 py-1 rounded text-sm font-medium">
                  Populaire
                </span>
              )}
            </div>

            <h1 className="text-3xl font-bold mt-4">{product.name}</h1>

            <div className="flex items-center mt-2 mb-4">
              {/* Star rating - données réelles */}
              <div className="flex mr-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    size={16}
                    className={
                      star <= Math.round(reviewStats?.average_rating || 0)
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-gray-300"
                    }
                  />
                ))}
              </div>
              {reviewStatsLoading ? (
                <span className="text-gray-600">Chargement...</span>
              ) : (
                <span className="text-gray-600">
                  ({reviewStats?.total_reviews || 0} avis)
                  {reviewStats?.average_rating && (
                    <span className="ml-2 text-sm">
                      {reviewStats.average_rating.toFixed(1)}/5
                    </span>
                  )}
                </span>
              )}
            </div>

            <p className="text-2xl font-bold text-primary-dark mb-4">
              {formatCurrency(Number(product.price))}
            </p>

            <div className="text-gray-700 mb-6" dangerouslySetInnerHTML={{ __html: product.description }} />

            {/* Stock info */}
            <div className="mb-6">
              <span className={`font-medium ${product.inventory?.is_in_stock || product.in_stock ? 'text-green-500' : 'text-red-500'}`}>
                {product.inventory?.is_in_stock || product.in_stock ? 'En stock' : 'Indisponible'}
              </span>
              {product.inventory && (
                <span className="ml-2 text-sm text-gray-600">
                  ({product.inventory.available_quantity} disponibles)
                </span>
              )}
            </div>

            {/* Quantity selector */}
            {(product.inventory?.is_in_stock || product.in_stock) && (
              <div className="flex items-center mb-6">
                <span className="mr-4">Quantité:</span>
                <div className="flex items-center border border-gray-300 rounded">
                  <button
                    onClick={decreaseQuantity}
                    className="px-3 py-1 border-r border-gray-300"
                    disabled={quantity <= 1}
                  >
                    -
                  </button>
                  <span className="px-4 py-1">{quantity}</span>
                  <button
                    onClick={increaseQuantity}
                    className="px-3 py-1 border-l border-gray-300"
                  >
                    +
                  </button>
                </div>
              </div>
            )}

            {/* Add to cart button */}
            <Button
              onClick={handleAddToCart}
              disabled={!(product.inventory?.is_in_stock || product.in_stock)}
              className="w-full py-6 text-lg font-medium flex items-center justify-center"
            >
              <ShoppingCart className="mr-2" />
              Ajouter au panier
            </Button>

            {/* Delivery options */}
            <div className="mt-6 space-y-4">
              <div className="flex items-start">
                <Truck className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium">Livraison gratuite</h4>
                  <p className="text-sm text-gray-600">Pour les commandes supérieures à {formatCurrency(50000)}</p>
                </div>
              </div>
              <div className="flex items-start">
                <ShieldCheck className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium">Garantie 1 an</h4>
                  <p className="text-sm text-gray-600">Retour et échange faciles</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product details tabs */}
        <div className="mt-12">
          <Tabs defaultValue="description">
            <TabsList className="w-full border-b grid grid-cols-3 mb-8">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="specs">Caractéristiques</TabsTrigger>
              <TabsTrigger value="reviews">Avis clients</TabsTrigger>
            </TabsList>

            <TabsContent value="description" className="px-1">
              <h3 className="text-xl font-bold mb-4">Description du produit</h3>
              <p className="text-gray-700 mb-4">{product.description}</p>
              <p className="text-gray-700">
                Le {product.name} est conçu pour répondre à vos besoins technologiques avec une qualité exceptionnelle.
                Fabriqué avec des matériaux durables et testé rigoureusement, ce produit vous offrira
                des performances fiables pendant des années.
              </p>
            </TabsContent>

            <TabsContent value="specs" className="px-1">
              <h3 className="text-xl font-bold mb-4">Caractéristiques techniques</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-medium mb-2">Spécifications générales</h4>
                  <table className="w-full">
                    <tbody>
                      <tr className="border-b border-gray-200">
                        <td className="py-2 text-gray-600">Marque</td>
                        <td className="py-2 font-medium">JOSNET NETWORK</td>
                      </tr>
                      <tr className="border-b border-gray-200">
                        <td className="py-2 text-gray-600">Modèle</td>
                        <td className="py-2 font-medium">{product.name}</td>
                      </tr>
                      <tr className="border-b border-gray-200">
                        <td className="py-2 text-gray-600">Catégorie</td>
                        <td className="py-2 font-medium">
                          {product.categories && product.categories.length > 0 
                            ? product.categories.map(cat => cat.name).join(', ')
                            : 'Non catégorisé'}
                        </td>
                      </tr>
                      <tr>
                        <td className="py-2 text-gray-600">Garantie</td>
                        <td className="py-2 font-medium">1 an</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-medium mb-2">Caractéristiques techniques</h4>
                  <p className="text-gray-600">
                    Ces informations varient selon le produit spécifique. Veuillez contacter
                    notre service client pour des détails supplémentaires.
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="reviews" className="px-1">
              <ProductReviews
                productId={product.id}
                productName={product.name}
              />
            </TabsContent>
          </Tabs>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ProductDetail;
