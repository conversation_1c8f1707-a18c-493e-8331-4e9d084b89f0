"""
Configuration pour l'envoi de vrais emails.
Ce fichier contient les paramètres SMTP pour envoyer de vrais emails.
"""

# Configuration SMTP pour Gmail (exemple)
# ATTENTION: Pour utiliser Gmail, vous devez:
# 1. Activer l'authentification à 2 facteurs
# 2. <PERSON><PERSON><PERSON><PERSON> un "mot de passe d'application" spécifique
# 3. Utiliser ce mot de passe d'application (pas votre mot de passe Gmail normal)

EMAIL_SETTINGS_GMAIL = {
    'EMAIL_BACKEND': 'django.core.mail.backends.smtp.EmailBackend',
    'EMAIL_HOST': 'smtp.gmail.com',
    'EMAIL_PORT': 587,
    'EMAIL_USE_TLS': True,
    'EMAIL_HOST_USER': '<EMAIL>',  # Remplacez par votre email
    'EMAIL_HOST_PASSWORD': 'pipz zycd drsn avpa',  # Mot de passe d'application Gmail
    'DEFAULT_FROM_EMAIL': 'JosNet <<EMAIL>>',
    'SERVER_EMAIL': '<EMAIL>',
}

# Configuration SMTP pour Outlook/Hotmail
EMAIL_SETTINGS_OUTLOOK = {
    'EMAIL_BACKEND': 'django.core.mail.backends.smtp.EmailBackend',
    'EMAIL_HOST': 'smtp-mail.outlook.com',
    'EMAIL_PORT': 587,
    'EMAIL_USE_TLS': True,
    'EMAIL_HOST_USER': '<EMAIL>',  # Remplacez par votre email
    'EMAIL_HOST_PASSWORD': 'pipz zycd drsn avpa',  # Mot de passe d'application Gmail
    'DEFAULT_FROM_EMAIL': 'JosNet <<EMAIL>>',
    'SERVER_EMAIL': '<EMAIL>',
}

# Configuration SMTP pour SendGrid (service professionnel)
EMAIL_SETTINGS_SENDGRID = {
    'EMAIL_BACKEND': 'django.core.mail.backends.smtp.EmailBackend',
    'EMAIL_HOST': 'smtp.sendgrid.net',
    'EMAIL_PORT': 587,
    'EMAIL_USE_TLS': True,
    'EMAIL_HOST_USER': 'apikey',
    'EMAIL_HOST_PASSWORD': 'votre-api-key-sendgrid',
    'DEFAULT_FROM_EMAIL': 'JosNet <<EMAIL>>',
    'SERVER_EMAIL': '<EMAIL>',
}

# Configuration pour les tests (emails affichés dans la console)
EMAIL_SETTINGS_CONSOLE = {
    'EMAIL_BACKEND': 'django.core.mail.backends.console.EmailBackend',
    'DEFAULT_FROM_EMAIL': 'JosNet <<EMAIL>>',
    'SERVER_EMAIL': '<EMAIL>',
}

# Configuration pour les tests (emails sauvegardés en mémoire)
EMAIL_SETTINGS_LOCMEM = {
    'EMAIL_BACKEND': 'django.core.mail.backends.locmem.EmailBackend',
    'DEFAULT_FROM_EMAIL': 'JosNet <<EMAIL>>',
    'SERVER_EMAIL': '<EMAIL>',
}

def apply_email_settings(settings_dict, django_settings):
    """
    Applique les paramètres email à la configuration Django.
    
    Usage:
        from email_settings import apply_email_settings, EMAIL_SETTINGS_GMAIL
        apply_email_settings(EMAIL_SETTINGS_GMAIL, settings)
    """
    for key, value in settings_dict.items():
        setattr(django_settings, key, value)


# Instructions pour configurer Gmail:
"""
CONFIGURATION GMAIL - ÉTAPES DÉTAILLÉES:

1. Connectez-vous à votre compte Gmail (<EMAIL>)

2. Allez dans "Gérer votre compte Google" > "Sécurité"

3. Activez l'authentification à 2 facteurs si ce n'est pas déjà fait

4. Dans "Sécurité", cherchez "Mots de passe des applications"

5. Générez un nouveau mot de passe d'application:
   - Sélectionnez "Autre (nom personnalisé)"
   - Tapez "JosNet Newsletter"
   - Cliquez sur "Générer"

6. Copiez le mot de passe généré (16 caractères)

7. Modifiez le fichier settings.py et ajoutez:

   EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
   EMAIL_HOST = 'smtp.gmail.com'
   EMAIL_PORT = 587
   EMAIL_USE_TLS = True
   EMAIL_HOST_USER = '<EMAIL>'
   EMAIL_HOST_PASSWORD = 'le-mot-de-passe-app-généré'
   DEFAULT_FROM_EMAIL = 'JosNet <<EMAIL>>'

8. Redémarrez le serveur Django

9. Testez l'envoi d'emails

SÉCURITÉ:
- Ne partagez jamais votre mot de passe d'application
- Vous pouvez révoquer ce mot de passe à tout moment
- Utilisez des variables d'environnement pour la production
"""
