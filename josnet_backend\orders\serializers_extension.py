from rest_framework import serializers
from django.urls import reverse
from django.conf import settings
from django.contrib.auth import get_user_model
from .models_extension import ReturnRequest, ReturnItem, Claim, ClaimMessage, ClaimAttachment, ClaimStatusHistory
from .models import OrderItem
from .serializers import OrderListSerializer

User = get_user_model()

class UserListSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour les données utilisateur dans les listes.
    Inclut uniquement les champs nécessaires pour l'affichage admin.
    """
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'full_name', 'first_name', 'last_name']
        read_only_fields = ['id', 'email', 'full_name', 'first_name', 'last_name']

    def get_full_name(self, obj):
        """
        Retourne le nom complet de l'utilisateur.
        Fallback vers email si pas de nom.
        """
        if obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        elif obj.first_name:
            return obj.first_name
        elif obj.last_name:
            return obj.last_name
        else:
            return obj.email or "Utilisateur inconnu"

class ReturnItemSerializer(serializers.ModelSerializer):
    reason_display = serializers.CharField(source='get_reason_display', read_only=True)
    image = serializers.SerializerMethodField()
    product_details = serializers.SerializerMethodField()
    order_item_details = serializers.SerializerMethodField()

    class Meta:
        model = ReturnItem
        fields = ['id', 'order_item', 'order_item_details', 'product_name', 'product_details', 'quantity', 'price',
                 'reason', 'reason_display', 'description', 'image', 'created_at']
        read_only_fields = ['created_at']

    def get_image(self, obj):
        request = self.context.get('request')
        if obj.order_item and obj.order_item.product:
            # Essayer d'abord les images du produit
            product_images = obj.order_item.product.images.filter(is_primary=True).first()
            if product_images and product_images.image:
                return request.build_absolute_uri(product_images.image.url)
        return None

    def get_product_details(self, obj):
        """Retourner les détails du produit"""
        if obj.order_item and obj.order_item.product:
            product = obj.order_item.product

            # Récupérer les catégories (relation Many-to-Many)
            categories = product.categories.all()
            category_names = [cat.name for cat in categories] if categories.exists() else []
            primary_category = category_names[0] if category_names else None

            return {
                'id': product.id,
                'name': product.name,
                'sku': getattr(product, 'sku', ''),
                'category': primary_category,  # ✅ CORRECTION: Première catégorie comme principale
                'categories': category_names,  # ✅ AJOUT: Toutes les catégories
            }
        return None

    def get_order_item_details(self, obj):
        """Retourner les détails de l'article commandé"""
        if obj.order_item:
            return {
                'id': obj.order_item.id,
                'quantity_ordered': obj.order_item.quantity,
                'price_ordered': str(obj.order_item.price),
                'total_ordered': str(obj.order_item.quantity * obj.order_item.price),
            }
        return None

class ReturnRequestListSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reason_display = serializers.CharField(source='get_reason_display', read_only=True)
    items_count = serializers.SerializerMethodField()
    refund_amount = serializers.SerializerMethodField()
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    date = serializers.DateTimeField(source='created_at', format='%d/%m/%Y', read_only=True)
    user = UserListSerializer(read_only=True)  # ✅ AJOUT CRUCIAL

    class Meta:
        model = ReturnRequest
        fields = ['id', 'return_number', 'order', 'order_number', 'status', 'status_display', 'reason', 'reason_display',
                 'items_count', 'refund_amount', 'date', 'created_at', 'user']  # ✅ AJOUT 'user'
        read_only_fields = ['id', 'return_number', 'created_at']
    
    def get_items_count(self, obj):
        return obj.items.count()
    
    def get_refund_amount(self, obj):
        total = sum(item.price * item.quantity for item in obj.items.all())
        return f"{total:.2f} u20ac"

class ReturnRequestDetailSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reason_display = serializers.CharField(source='get_reason_display', read_only=True)
    items = ReturnItemSerializer(many=True, read_only=True)
    order_details = OrderListSerializer(source='order', read_only=True)
    user = UserListSerializer(read_only=True)  # ✅ AJOUT: Sérialiser les détails utilisateur
    date = serializers.DateTimeField(source='created_at', format='%d/%m/%Y', read_only=True)

    class Meta:
        model = ReturnRequest
        fields = ['id', 'return_number', 'order', 'order_details', 'user', 'status', 'status_display', 'reason',
                 'reason_display', 'additional_info', 'items', 'date', 'created_at', 'updated_at']
        read_only_fields = ['id', 'return_number', 'created_at', 'updated_at']

class ReturnRequestCreateSerializer(serializers.ModelSerializer):
    items = serializers.ListField(child=serializers.DictField(), write_only=True)
    
    class Meta:
        model = ReturnRequest
        fields = ['order', 'reason', 'additional_info', 'items']
    
    def create(self, validated_data):
        items_data = validated_data.pop('items')
        user = self.context['request'].user
        validated_data['user'] = user
        
        return_request = ReturnRequest.objects.create(**validated_data)
        
        order = validated_data['order']
        for item_data in items_data:
            order_item_id = item_data.get('order_item_id')
            quantity = item_data.get('quantity')

            if not order_item_id or not quantity:
                raise serializers.ValidationError("Chaque article doit avoir un 'order_item_id' et une 'quantity'.")

            try:
                order_item = order.items.get(id=order_item_id)
            except OrderItem.DoesNotExist:
                raise serializers.ValidationError(f"L'article avec l'ID {order_item_id} n'a pas été trouvé dans cette commande.")

            if quantity > order_item.quantity:
                raise serializers.ValidationError(f"La quantité de retour ({quantity}) pour l'article {order_item.product_name} dépasse la quantité commandée ({order_item.quantity}).")

            ReturnItem.objects.create(
                return_request=return_request,
                order_item=order_item,
                product_name=order_item.product_name,
                quantity=quantity,
                price=order_item.price,
                reason=item_data.get('reason', return_request.reason),
                description=item_data.get('description', '')
            )
        
        return return_request

class ClaimAttachmentSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = ClaimAttachment
        fields = ['id', 'file', 'file_name', 'file_size', 'content_type', 'file_url', 'created_at']
        read_only_fields = ['file_size', 'content_type', 'created_at']
    
    def get_file_url(self, obj):
        request = self.context.get('request')
        if obj.file:
            return request.build_absolute_uri(obj.file.url)
        return None

class ClaimMessageSerializer(serializers.ModelSerializer):
    sender_display = serializers.CharField(source='get_sender_display', read_only=True)
    attachments = ClaimAttachmentSerializer(many=True, read_only=True)
    uploaded_files = serializers.ListField(child=serializers.FileField(), write_only=True, required=False)
    date = serializers.DateTimeField(source='created_at', format='%d/%m/%Y', read_only=True)
    
    class Meta:
        model = ClaimMessage
        fields = ['id', 'claim', 'sender', 'sender_display', 'message', 'is_read', 'attachments', 
                 'uploaded_files', 'date', 'created_at']
        read_only_fields = ['is_read', 'created_at']
    
    def create(self, validated_data):
        uploaded_files = validated_data.pop('uploaded_files', [])
        message = ClaimMessage.objects.create(**validated_data)
        
        for file in uploaded_files:
            ClaimAttachment.objects.create(
                message=message,
                file=file,
                file_name=file.name,
                file_size=file.size,
                content_type=file.content_type
            )
        
        return message

class ClaimStatusHistorySerializer(serializers.ModelSerializer):
    old_status_display = serializers.CharField(source='get_old_status_display', read_only=True)
    new_status_display = serializers.CharField(source='get_new_status_display', read_only=True)
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = ClaimStatusHistory
        fields = ['id', 'claim', 'old_status', 'old_status_display', 'new_status', 'new_status_display', 
                 'comment', 'created_by', 'created_by_name', 'created_at']
        read_only_fields = ['created_at']
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return "Systu00e8me"

class ClaimListSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    subject_display = serializers.CharField(source='get_subject_display', read_only=True)
    order_number = serializers.CharField(source='order.order_number', read_only=True, allow_null=True)
    date = serializers.DateTimeField(source='created_at', format='%d/%m/%Y', read_only=True)
    last_update_formatted = serializers.DateTimeField(source='last_update', format='%d/%m/%Y', read_only=True)
    user = UserListSerializer(read_only=True)  # ✅ AJOUT CRUCIAL

    class Meta:
        model = Claim
        fields = ['id', 'claim_number', 'user', 'order', 'order_number', 'subject', 'subject_display',
                 'status', 'status_display', 'date', 'last_update_formatted', 'created_at', 'last_update']
        read_only_fields = ['id', 'claim_number', 'created_at', 'last_update']

from authentication.serializers import UserSerializer

class ClaimDetailSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    subject_display = serializers.CharField(source='get_subject_display', read_only=True)
    messages = ClaimMessageSerializer(many=True, read_only=True)
    status_history = ClaimStatusHistorySerializer(many=True, read_only=True)
    order_details = OrderListSerializer(source='order', read_only=True)
    date = serializers.DateTimeField(source='created_at', format='%d/%m/%Y', read_only=True)
    last_update_formatted = serializers.DateTimeField(source='last_update', format='%d/%m/%Y', read_only=True)
    
    class Meta:
        model = Claim
        fields = ['id', 'claim_number', 'user', 'order', 'order_details', 'subject', 'subject_display', 
                 'description', 'status', 'status_display', 'messages', 'status_history', 'date', 
                 'last_update_formatted', 'created_at', 'last_update']
        read_only_fields = ['id', 'claim_number', 'created_at', 'last_update']

class ClaimCreateSerializer(serializers.ModelSerializer):
    uploaded_files = serializers.ListField(child=serializers.FileField(), write_only=True, required=False)
    order_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = Claim
        fields = ['order', 'order_id', 'subject', 'description', 'uploaded_files']
        read_only_fields = ['order']

    def create(self, validated_data):
        uploaded_files = validated_data.pop('uploaded_files', [])
        order_id = validated_data.pop('order_id', None)
        user = self.context['request'].user
        validated_data['user'] = user

        if order_id:
            try:
                order = Order.objects.get(id=order_id, user=user)
                validated_data['order'] = order
            except Order.DoesNotExist:
                raise serializers.ValidationError({'order_id': f'Commande invalide ou non trouvée.'})

        claim = Claim.objects.create(**validated_data)

        # Create initial message from customer
        message = ClaimMessage.objects.create(
            claim=claim,
            sender='customer',
            message=validated_data['description']
        )

        # Add attachments if any
        for file in uploaded_files:
            ClaimAttachment.objects.create(
                message=message,
                file=file,
                file_name=file.name,
                file_size=file.size,
                content_type=file.content_type
            )

        # Create initial status history
        ClaimStatusHistory.objects.create(
            claim=claim,
            old_status='',
            new_status='new',
            comment='Réclamation créée',
            created_by=user
        )

        return claim


class ClaimAttachmentSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour les pièces jointes des messages.
    """
    file_url = serializers.SerializerMethodField()
    file_size_formatted = serializers.SerializerMethodField()

    class Meta:
        model = ClaimAttachment
        fields = ['id', 'file', 'file_url', 'file_name', 'file_size', 'file_size_formatted', 'uploaded_at']
        read_only_fields = ['id', 'file_size', 'uploaded_at']

    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None

    def get_file_size_formatted(self, obj):
        if obj.file_size:
            # Convert bytes to human readable format
            size = obj.file_size
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        return "0 B"


class ClaimMessageSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour les messages de réclamation.
    """
    attachments = ClaimAttachmentSerializer(many=True, read_only=True)
    sender_display = serializers.CharField(source='get_sender_display', read_only=True)
    sender_name = serializers.SerializerMethodField()

    class Meta:
        model = ClaimMessage
        fields = [
            'id', 'claim', 'sender', 'sender_display', 'sender_name',
            'message', 'created_at', 'is_read', 'attachments'
        ]
        read_only_fields = ['id', 'created_at', 'sender']

    def get_sender_name(self, obj):
        """
        Retourne le nom de l'expéditeur selon le type.
        """
        if obj.sender == 'customer':
            if obj.claim and obj.claim.user:
                user = obj.claim.user
                if user.first_name and user.last_name:
                    return f"{user.first_name} {user.last_name}"
                elif user.first_name:
                    return user.first_name
                else:
                    return user.email
            return "Client"
        elif obj.sender == 'support':
            return "Support Client"
        elif obj.sender == 'admin':
            return "Administrateur"
        else:
            return "Système"

    def create(self, validated_data):
        # Le sender sera défini dans la vue
        return super().create(validated_data)
