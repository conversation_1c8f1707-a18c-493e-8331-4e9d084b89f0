import React, { useEffect, useState, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Tag, Bell } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import promotionApi, { Promotion } from '@/services/promotionApi';
import notificationApi, { Notification } from '@/services/notificationApi';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Composant qui vérifie périodiquement les nouvelles notifications et affiche une notification toast
 * lorsqu'une nouvelle promotion est disponible
 */
const PromotionToast: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const lastNotificationIdsRef = useRef<number[]>([]);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  const { isAuthenticated } = useAuth();

  // Utiliser l'API de notifications pour récupérer les notifications non lues
  const { data: notificationsData } = useQuery({
    queryKey: ['notifications', 'unread'],
    queryFn: notificationApi.getUnreadNotifications,
    refetchInterval: isAuthenticated ? 1 * 60 * 1000 : false, // Rafraîchir seulement si authentifié
    staleTime: 30 * 1000, // Considérer comme périmé après 30 secondes
    enabled: isAuthenticated && !!localStorage.getItem('accessToken'), // Double vérification
  });
  
  // Extraire les notifications du résultat de l'API et filtrer pour ne garder que celles de type promotion
  const promotionNotifications = notificationsData?.results
    ? notificationsData.results.filter(notification => notification.type === 'promotion')
    : [];

  useEffect(() => {
    // Si c'est le premier chargement, enregistrer simplement les IDs des notifications
    if (isFirstLoad && promotionNotifications.length) {
      lastNotificationIdsRef.current = promotionNotifications.map((notification) => notification.id);
      setIsFirstLoad(false);
      return;
    }

    // Si ce n'est pas le premier chargement et qu'il y a des données
    if (!isFirstLoad && promotionNotifications.length) {
      // Trouver les nouvelles notifications (celles qui n'étaient pas dans lastNotificationIdsRef.current)
      const newNotifications = promotionNotifications.filter(
        (notification) => !lastNotificationIdsRef.current.includes(notification.id)
      );

      // Afficher une notification pour chaque nouvelle notification (max 2 pour éviter le spam)
      newNotifications.slice(0, 2).forEach((notification) => {
        // Marquer la notification comme lue
        notificationApi.markAsRead(notification.id);

        toast({
          title: notification.title || "Nouvelle promotion disponible !",
          description: (
            <div className="mt-2">
              <p className="text-sm mt-1">{notification.message}</p>
              {notification.link && (
                <button
                  className="bg-primary text-white px-3 py-1 rounded text-xs mt-2 hover:bg-primary/90"
                  onClick={() => navigate(notification.link || '/promotions')}
                >
                  Voir les détails
                </button>
              )}
            </div>
          ),
          duration: 10000, // 10 secondes
        });
      });

      // Mettre à jour la liste des IDs de notifications
      lastNotificationIdsRef.current = promotionNotifications.map((notification) => notification.id);
    }
  }, [promotionNotifications, isFirstLoad, toast, navigate]);

  // Ce composant ne rend rien visuellement, il gère uniquement les notifications
  return null;
};

export default PromotionToast;
