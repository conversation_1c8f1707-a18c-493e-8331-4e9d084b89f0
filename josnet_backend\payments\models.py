from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from orders.models import Order

class PaymentMethod(models.Model):
    """
    Model for storing payment methods configuration.
    """
    PROVIDER_CHOICES = [
        ('stripe', 'Stripe'),
        ('paypal', 'PayPal'),
        ('bank_transfer', 'Virement bancaire'),
        ('cash_on_delivery', 'Paiement à la livraison'),
        ('other', 'Autre'),
    ]

    name = models.CharField(max_length=100)
    provider = models.CharField(max_length=50, choices=PROVIDER_CHOICES)
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    config = models.JSONField(blank=True, null=True, help_text=_("Configuration JSON for the payment provider"))

    # Display settings
    icon = models.CharField(max_length=50, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    position = models.PositiveSmallIntegerField(default=0)

    # Fees
    fee_fixed = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    fee_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['position', 'name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # If this is set as default, unset default on all other methods
        if self.is_default:
            PaymentMethod.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    def calculate_fee(self, amount):
        """Calculate the fee for a given amount."""
        return self.fee_fixed + (amount * self.fee_percentage / 100)


class Transaction(models.Model):
    """
    Model for tracking payment transactions.
    """
    STATUS_CHOICES = [
        ('pending', _('En attente')),
        ('processing', _('En traitement')),
        ('completed', _('Complété')),
        ('failed', _('Échoué')),
        ('refunded', _('Remboursé')),
        ('partially_refunded', _('Partiellement remboursé')),
        ('cancelled', _('Annulé')),
    ]

    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='transactions')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.SET_NULL, null=True, related_name='transactions')

    # Transaction details
    transaction_id = models.CharField(max_length=255, blank=True, null=True, help_text=_("ID de transaction fourni par la passerelle de paiement"))
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    currency = models.CharField(max_length=3, default='BIF')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Additional data
    provider_response = models.JSONField(blank=True, null=True, help_text=_("Réponse complète de la passerelle de paiement"))
    error_message = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Transaction {self.id} - {self.get_status_display()} - {self.amount} {self.currency}"


class Invoice(models.Model):
    """
    Model for storing invoices.
    """
    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('issued', _('Émise')),
        ('paid', _('Payée')),
        ('cancelled', _('Annulée')),
    ]

    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='invoices')
    invoice_number = models.CharField(max_length=50, unique=True)

    # Invoice details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    issue_date = models.DateField(auto_now_add=True)
    due_date = models.DateField(blank=True, null=True)
    paid_date = models.DateField(blank=True, null=True)

    # Amounts
    subtotal = models.DecimalField(max_digits=15, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    shipping_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total = models.DecimalField(max_digits=15, decimal_places=2)

    # PDF file
    pdf_file = models.FileField(upload_to='invoices/', blank=True, null=True)

    # Notes
    notes = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-issue_date', '-id']

    def __str__(self):
        return f"Facture {self.invoice_number} - {self.get_status_display()}"

    def save(self, *args, **kwargs):
        # Generate invoice number if not provided
        if not self.invoice_number:
            last_invoice = Invoice.objects.order_by('-id').first()
            if last_invoice:
                last_number = int(last_invoice.invoice_number.split('-')[1])
                self.invoice_number = f"FAC-{last_number + 1:05d}"
            else:
                self.invoice_number = "FAC-00001"

        # Calculate total
        self.total = self.subtotal + self.tax_amount + self.shipping_amount - self.discount_amount

        super().save(*args, **kwargs)


class InvoiceItem(models.Model):
    """
    Model for storing invoice line items.
    """
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    order_item = models.ForeignKey('orders.OrderItem', on_delete=models.SET_NULL, null=True, blank=True)
    product_name = models.CharField(max_length=255)
    variant_name = models.CharField(max_length=255, blank=True, null=True)
    sku = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=15, decimal_places=2)
    quantity = models.PositiveIntegerField(default=1)
    subtotal = models.DecimalField(max_digits=15, decimal_places=2)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    final_price = models.DecimalField(max_digits=15, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['id']

    def __str__(self):
        return f"{self.product_name} ({self.quantity}) - FBu {int(round(self.final_price))}"


class CustomerPaymentMethod(models.Model):
    """
    Model for storing customer payment methods (saved cards, etc.)
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payment_methods')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE, related_name='customer_methods')

    # Payment method details
    token = models.CharField(max_length=255, help_text=_("Token fourni par la passerelle de paiement"))
    is_default = models.BooleanField(default=False)

    # Card details (if applicable)
    card_type = models.CharField(max_length=50, blank=True, null=True)
    last_four = models.CharField(max_length=4, blank=True, null=True)
    expiry_month = models.CharField(max_length=2, blank=True, null=True)
    expiry_year = models.CharField(max_length=4, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_default', '-created_at']

    def __str__(self):
        if self.card_type and self.last_four:
            return f"{self.card_type} **** **** **** {self.last_four}"
        return f"Méthode de paiement {self.id}"

    def save(self, *args, **kwargs):
        # If this is set as default, unset default on all other methods for this user
        if self.is_default:
            CustomerPaymentMethod.objects.filter(user=self.user, is_default=True).update(is_default=False)
        super().save(*args, **kwargs)
