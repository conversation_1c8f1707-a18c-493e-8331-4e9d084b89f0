#!/usr/bin/env python3
"""
Script pour approuver l'avis de l'admin
"""

import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from products.models import Product, ProductReview

def approve_admin_review():
    """Approuver l'avis de l'admin"""
    print("✅ APPROBATION DE L'AVIS DE L'ADMIN")
    print("=" * 50)
    
    try:
        # Récupérer l'avis de l'admin
        admin_user = User.objects.get(email="<EMAIL>")
        product = Product.objects.get(id=1)
        
        admin_review = ProductReview.objects.filter(
            product=product,
            user=admin_user
        ).first()
        
        if admin_review:
            print(f"📋 Avis trouvé:")
            print(f"   🆔 ID: {admin_review.id}")
            print(f"   ⭐ Note: {admin_review.rating}")
            print(f"   📝 Titre: {admin_review.title}")
            print(f"   ✅ Approuvé: {admin_review.is_approved}")
            
            if not admin_review.is_approved:
                # Approuver l'avis
                admin_review.is_approved = True
                admin_review.save()
                
                print(f"\n🎉 AVIS APPROUVÉ AVEC SUCCÈS!")
                print(f"   ✅ L'avis est maintenant visible publiquement")
                print(f"   📊 Le compteur passera de 6 à 7 avis")
                print(f"   🌐 Rechargez la page: http://localhost:8080/product/1")
                
                return True
            else:
                print(f"\n💡 L'avis est déjà approuvé")
                return True
        else:
            print(f"❌ Aucun avis trouvé pour l'admin")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    approve_admin_review()
