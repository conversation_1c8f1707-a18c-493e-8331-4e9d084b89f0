(function($) {
    $(document).ready(function() {
        // Function to toggle visibility of product and category fields
        function toggleTargetFields() {
            var appliesTo = $('#id_applies_to').val();
            
            // Hide both fields initially
            $('#id_product').closest('.form-row').hide();
            $('#id_category').closest('.form-row').hide();
            
            // Show the appropriate field based on selection
            if (appliesTo === 'product') {
                $('#id_product').closest('.form-row').show();
            } else if (appliesTo === 'category') {
                $('#id_category').closest('.form-row').show();
            }
            
            // Show/hide min_purchase_amount field
            if (appliesTo === 'cart') {
                $('#id_min_purchase_amount').closest('.form-row').show();
            } else {
                $('#id_min_purchase_amount').closest('.form-row').hide();
            }
        }
        
        // Initial toggle on page load
        toggleTargetFields();
        
        // Toggle on change of applies_to field
        $('#id_applies_to').change(function() {
            toggleTargetFields();
        });
        
        // Format the discount value field based on discount type
        function updateDiscountValueField() {
            var discountType = $('#id_discount_type').val();
            var discountValueField = $('#id_discount_value');
            
            if (discountType === 'percentage') {
                discountValueField.attr('step', '0.01');
                discountValueField.attr('min', '0');
                discountValueField.attr('max', '100');
                $('label[for="id_discount_value"]').text('Discount Value (%)');
            } else {
                discountValueField.attr('step', '0.01');
                discountValueField.attr('min', '0');
                discountValueField.removeAttr('max');
                $('label[for="id_discount_value"]').text('Discount Value (BIF)');
            }
        }
        
        // Initial update on page load
        updateDiscountValueField();
        
        // Update on change of discount type
        $('#id_discount_type').change(function() {
            updateDiscountValueField();
        });
    });
})(django.jQuery);
