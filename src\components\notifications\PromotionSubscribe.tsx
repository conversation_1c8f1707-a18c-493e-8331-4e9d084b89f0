import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import notificationApi from '@/services/notificationApi';

interface PromotionSubscribeProps {
  className?: string;
}

const PromotionSubscribe: React.FC<PromotionSubscribeProps> = ({ className }) => {
  const [email, setEmail] = useState('');
  const { toast } = useToast();

  const { mutate, isPending, isSuccess, isError, error } = useMutation({
    mutationFn: (email: string) => notificationApi.subscribeToPromotions(email),
    onSuccess: () => {
      toast({
        title: 'Inscription réussie !',
        description: 'Vous recevrez désormais nos offres et promotions par email.',
        variant: 'default',
      });
      setEmail('');
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur lors de l\'inscription',
        description: error?.response?.data?.message || 'Veuillez réessayer ultérieurement.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email.trim()) {
      mutate(email);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Restez informé de nos promotions
        </CardTitle>
        <CardDescription>
          Inscrivez-vous pour recevoir en avant-première nos offres exclusives et codes promo
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isSuccess ? (
          <Alert className="bg-green-50 border-green-200">
            <AlertTitle>Merci pour votre inscription !</AlertTitle>
            <AlertDescription>
              Vous recevrez désormais nos meilleures offres directement dans votre boîte mail.
            </AlertDescription>
          </Alert>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                type="email"
                placeholder="Votre adresse email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="flex-1"
                disabled={isPending}
              />
              <Button type="submit" disabled={isPending || !email.trim()}>
                {isPending ? 'Inscription...' : 'S\'inscrire'}
              </Button>
            </div>
            
            {isError && (
              <p className="text-sm text-red-500">
                {error?.response?.data?.message || 'Une erreur est survenue. Veuillez réessayer.'}
              </p>
            )}
            
            <p className="text-xs text-gray-500">
              En vous inscrivant, vous acceptez de recevoir des emails concernant nos promotions.
              Vous pourrez vous désinscrire à tout moment.
            </p>
          </form>
        )}
      </CardContent>
    </Card>
  );
};

export default PromotionSubscribe;
