from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Conversation, Message, Attachment, Category, Tag, ResponseTemplate
from django.db.models import Q

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user information in messages."""
    full_name = serializers.CharField(source='get_full_name', read_only=True)

    class Meta:
        model = User
        fields = ['id', 'email', 'full_name', 'profile_picture']
        read_only_fields = ['id', 'email', 'full_name', 'profile_picture']


class CategorySerializer(serializers.ModelSerializer):
    """Serializer for conversation categories."""

    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'color', 'icon', 'is_active']
        read_only_fields = ['id']


class TagSerializer(serializers.ModelSerializer):
    """Serializer for conversation tags."""

    class Meta:
        model = Tag
        fields = ['id', 'name', 'color', 'is_active']
        read_only_fields = ['id']


class AttachmentSerializer(serializers.ModelSerializer):
    """Serializer for message attachments."""
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = Attachment
        fields = ['id', 'file', 'file_url', 'file_name', 'file_size', 'file_type', 'created_at']
        read_only_fields = ['id', 'file_url', 'created_at']

    def get_file_url(self, obj):
        """Get the URL for the file."""
        request = self.context.get('request')
        if obj.file and request:
            return request.build_absolute_uri(obj.file.url)
        return None


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for messages."""
    sender = UserSerializer(read_only=True)
    attachments = AttachmentSerializer(many=True, read_only=True)
    uploaded_files = serializers.ListField(
        child=serializers.FileField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = Message
        fields = ['id', 'conversation', 'sender', 'content', 'created_at',
                  'is_from_admin', 'attachments', 'uploaded_files']
        read_only_fields = ['id', 'sender', 'created_at', 'is_from_admin', 'attachments']

    def create(self, validated_data):
        """Create a new message with attachments."""
        uploaded_files = validated_data.pop('uploaded_files', [])

        # Set sender and is_from_admin
        user = self.context['request'].user
        validated_data['sender'] = user
        validated_data['is_from_admin'] = user.is_staff or user.is_superuser

        # Create message
        message = Message.objects.create(**validated_data)

        # Create attachments
        for file in uploaded_files:
            Attachment.objects.create(
                message=message,
                file=file,
                file_name=file.name,
                file_size=file.size,
                file_type=file.content_type
            )

        return message


class ConversationListSerializer(serializers.ModelSerializer):
    """Serializer for listing conversations."""
    customer = UserSerializer(read_only=True)
    last_message_preview = serializers.SerializerMethodField()
    unread = serializers.SerializerMethodField()
    message_count = serializers.IntegerField(read_only=True)
    category = CategorySerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    assigned_to = UserSerializer(read_only=True)
    order_number = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = ['id', 'subject', 'customer', 'status', 'priority', 'category', 'tags',
                  'created_at', 'last_message_at', 'last_message_preview', 'unread',
                  'message_count', 'assigned_to', 'order_number']
        read_only_fields = ['id', 'created_at', 'last_message_at', 'message_count']

    def get_last_message_preview(self, obj):
        """Get a preview of the last message."""
        last_message = obj.last_message
        if last_message:
            return {
                'content': last_message.content[:100] + ('...' if len(last_message.content) > 100 else ''),
                'created_at': last_message.created_at,
                'is_from_admin': last_message.is_from_admin
            }
        return None

    def get_unread(self, obj):
        """Check if the conversation has unread messages for the current user."""
        user = self.context['request'].user
        if user.is_staff or user.is_superuser:
            return not obj.is_read_by_admin
        return not obj.is_read_by_customer

    def get_order_number(self, obj):
        """Get the order number if the conversation is linked to an order."""
        if obj.order:
            return getattr(obj.order, 'order_number', str(obj.order.id))
        return None


class ConversationDetailSerializer(serializers.ModelSerializer):
    """Serializer for conversation details."""
    customer = UserSerializer(read_only=True)
    messages = MessageSerializer(many=True, read_only=True)
    category = CategorySerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    assigned_to = UserSerializer(read_only=True)
    order_details = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = ['id', 'subject', 'customer', 'status', 'priority', 'category', 'tags',
                  'created_at', 'updated_at', 'last_message_at', 'messages',
                  'assigned_to', 'order_details', 'is_read_by_admin', 'is_read_by_customer']
        read_only_fields = ['id', 'created_at', 'updated_at', 'last_message_at', 'messages']

    def get_order_details(self, obj):
        """Get basic details about the linked order."""
        try:
            if obj.order:
                return {
                    'id': obj.order.id,
                    'order_number': getattr(obj.order, 'order_number', str(obj.order.id)),
                    'created_at': obj.order.created_at,
                    'status': obj.order.status if hasattr(obj.order, 'status') else None,
                    'total': str(obj.order.total) if hasattr(obj.order, 'total') else None
                }
        except Exception as e:
            # Log the error but don't crash
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error getting order details: {e}")
            return None
        return None


class ResponseTemplateSerializer(serializers.ModelSerializer):
    """Serializer for response templates."""
    category = CategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(),
        source='category',
        write_only=True,
        required=False,
        allow_null=True
    )
    created_by = UserSerializer(read_only=True)

    class Meta:
        model = ResponseTemplate
        fields = ['id', 'name', 'content', 'category', 'category_id', 'is_active', 'created_at', 'updated_at', 'created_by']
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']


class TemplateGroupSerializer(serializers.Serializer):
    """Serializer for response templates grouped by category."""
    category = CategorySerializer(read_only=True, required=False, allow_null=True)
    templates = ResponseTemplateSerializer(many=True, read_only=True)


class ConversationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating a new conversation."""
    initial_message = serializers.CharField(write_only=True)
    uploaded_files = serializers.ListField(
        child=serializers.FileField(),
        write_only=True,
        required=False
    )
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.filter(is_active=True),
        write_only=True,
        required=False,
        allow_null=True
    )
    tag_ids = serializers.PrimaryKeyRelatedField(
        queryset=Tag.objects.filter(is_active=True),
        write_only=True,
        required=False,
        many=True
    )
    order_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = Conversation
        fields = ['subject', 'initial_message', 'uploaded_files', 'category_id', 'tag_ids', 'order_id', 'priority']

    def create(self, validated_data):
        """Create a new conversation with an initial message."""
        initial_message = validated_data.pop('initial_message')
        uploaded_files = validated_data.pop('uploaded_files', [])
        category = validated_data.pop('category_id', None)
        tag_ids = validated_data.pop('tag_ids', [])
        order_id = validated_data.pop('order_id', None)

        # Set customer to current user
        user = self.context['request'].user
        validated_data['customer'] = user

        # Set category if provided
        if category:
            validated_data['category'] = category

        # Set order if provided
        if order_id:
            from orders.models import Order
            try:
                order = Order.objects.get(id=order_id, customer=user)
                validated_data['order'] = order
            except Order.DoesNotExist:
                pass

        # Create conversation
        conversation = Conversation.objects.create(**validated_data)

        # Add tags if provided
        if tag_ids:
            conversation.tags.add(*tag_ids)

        # Create initial message
        message = Message.objects.create(
            conversation=conversation,
            sender=user,
            content=initial_message,
            is_from_admin=False
        )

        # Create attachments
        for file in uploaded_files:
            Attachment.objects.create(
                message=message,
                file=file,
                file_name=file.name,
                file_size=file.size,
                file_type=file.content_type
            )

        return conversation
