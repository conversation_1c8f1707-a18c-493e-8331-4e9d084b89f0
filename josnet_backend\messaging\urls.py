from django.urls import path
from .views import (
    ConversationViewSet,
    MessageViewSet,
    CategoryViewSet,
    TagViewSet,
    ResponseTemplateViewSet
)

# Import simplified views for alternative endpoints
from .views_simplified import (
    ConversationViewSet as ConversationViewSetSimplified,
    MessageViewSet as MessageViewSetSimplified
)

# Define URL patterns
urlpatterns = [
    # Simplified API endpoints (more robust error handling)
    path('v2/conversations/',
         ConversationViewSetSimplified.as_view({'get': 'list', 'post': 'create'}),
         name='conversation-list-v2'),
    path('v2/conversations/<int:pk>/',
         ConversationViewSetSimplified.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}),
         name='conversation-detail-v2'),
    path('v2/conversations/<int:pk>/update_status/',
         ConversationViewSetSimplified.as_view({'post': 'update_status'}),
         name='conversation-update-status-v2'),
    path('v2/conversations/<int:pk>/update_priority/',
         ConversationViewSetSimplified.as_view({'post': 'update_priority'}),
         name='conversation-update-priority-v2'),
    path('v2/conversations/<int:pk>/assign/',
         ConversationViewSetSimplified.as_view({'post': 'assign'}),
         name='conversation-assign-v2'),
    path('v2/conversations/<int:pk>/categorize/',
         ConversationViewSetSimplified.as_view({'post': 'categorize'}),
         name='conversation-categorize-v2'),
    path('v2/conversations/statistics/',
         ConversationViewSetSimplified.as_view({'get': 'statistics'}),
         name='conversation-statistics-v2'),
    path('v2/conversations/unread_count/',
         ConversationViewSetSimplified.as_view({'get': 'unread_count'}),
         name='conversation-unread-count-v2'),
    path('v2/conversations/<int:conversation_pk>/messages/',
         MessageViewSetSimplified.as_view({'get': 'list', 'post': 'create'}),
         name='conversation-messages-list-v2'),

    # Original API endpoints
    # Conversations
    path('conversations/',
         ConversationViewSet.as_view({'get': 'list', 'post': 'create'}),
         name='conversation-list'),
    path('conversations/<int:pk>/',
         ConversationViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}),
         name='conversation-detail'),
    path('conversations/<int:pk>/update_status/',
         ConversationViewSet.as_view({'post': 'update_status'}),
         name='conversation-update-status'),
    path('conversations/unread_count/',
         ConversationViewSet.as_view({'get': 'unread_count'}),
         name='conversation-unread-count'),
    path('conversations/statistics/',
         ConversationViewSet.as_view({'get': 'statistics'}),
         name='conversation-statistics'),

    # Messages
    path('conversations/<int:conversation_pk>/messages/',
         MessageViewSet.as_view({'get': 'list', 'post': 'create'}),
         name='conversation-messages-list'),
    path('conversations/<int:conversation_pk>/messages/<int:pk>/',
         MessageViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}),
         name='conversation-messages-detail'),

    # Categories
    path('categories/',
         CategoryViewSet.as_view({'get': 'list', 'post': 'create'}),
         name='category-list'),
    path('categories/<int:pk>/',
         CategoryViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}),
         name='category-detail'),
    path('categories/active/',
         CategoryViewSet.as_view({'get': 'active'}),
         name='category-active'),

    # Tags
    path('tags/',
         TagViewSet.as_view({'get': 'list', 'post': 'create'}),
         name='tag-list'),
    path('tags/<int:pk>/',
         TagViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}),
         name='tag-detail'),
    path('tags/active/',
         TagViewSet.as_view({'get': 'active'}),
         name='tag-active'),

    # Response Templates
    path('templates/',
         ResponseTemplateViewSet.as_view({'get': 'list', 'post': 'create'}),
         name='template-list'),
    path('templates/by_category/',
         ResponseTemplateViewSet.as_view({'get': 'by_category'}),
         name='template-by-category'),
    path('templates/<int:pk>/',
         ResponseTemplateViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}),
         name='template-detail'),
]
