
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import AccountLayout from "@/components/account/AccountLayout";
import { Package, ShoppingBag, Truck, LayoutDashboard, Loader2, AlertCircle, RefreshCw, Award } from "lucide-react";
import { Link } from "react-router-dom";
import userApi from "@/services/userApi";

const Dashboard = () => {
  // Récupérer les statistiques du tableau de bord
  const { data: dashboardStats, isLoading, isError, refetch } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: () => userApi.getDashboardStats(),
  });

  // Définir les statistiques à afficher
  const stats = [
    {
      title: "Commandes",
      value: isLoading ? "-" : dashboardStats?.orders_count.toString() || "0",
      icon: ShoppingBag,
      link: "/account/orders"
    },
    {
      title: "En attente",
      value: isLoading ? "-" : dashboardStats?.pending_orders_count.toString() || "0",
      icon: Truck,
      link: "/account/orders"
    },
    {
      title: "Retours",
      value: isLoading ? "-" : dashboardStats?.returns_count.toString() || "0",
      icon: Package,
      link: "/account/returns"
    },
    {
      title: "Points fidélité",
      value: isLoading ? "-" : dashboardStats?.loyalty_points.toString() || "0",
      icon: Award,
      link: "/account/profile"
    }
  ];

  // Commande récente
  const recentOrder = dashboardStats?.recent_order ? {
    id: dashboardStats.recent_order.order_number,
    date: dashboardStats.recent_order.date,
    status: dashboardStats.recent_order.status_display,
    total: dashboardStats.recent_order.total
  } : null;

  return (
    <AccountLayout title="Tableau de bord">
      {isError ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">Impossible de charger les données</h3>
          <p className="text-gray-500 max-w-md mb-4">
            Une erreur est survenue lors du chargement des données du tableau de bord.
          </p>
          <Button onClick={() => refetch()} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Réessayer
          </Button>
        </div>
      ) : (
        <div className="grid gap-6">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                {stats.map((stat, index) => (
                  <Card key={index} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">{stat.title}</CardTitle>
                        <stat.icon className="h-5 w-5 text-primary" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold">{stat.value}</div>
                      <Link to={stat.link}>
                        <Button variant="link" className="p-0 h-auto mt-2">Voir détails</Button>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="grid grid-cols-1 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Dernière commande</CardTitle>
                    <CardDescription>Un aperçu de votre commande la plus récente</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {recentOrder ? (
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="font-medium">Numéro</span>
                          <span>{recentOrder.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">Date</span>
                          <span>{recentOrder.date}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">Statut</span>
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                            {recentOrder.status}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">Total</span>
                          <span className="font-bold">{recentOrder.total}</span>
                        </div>
                        <div className="pt-4">
                          <Link to="/account/orders">
                            <Button className="w-full">Voir toutes mes commandes</Button>
                          </Link>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center p-6">
                        <LayoutDashboard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium">Pas de commande récente</h3>
                        <p className="text-gray-500 mb-4">Vous n'avez pas encore passé de commande</p>
                        <Link to="/products">
                          <Button>Découvrir nos produits</Button>
                        </Link>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Suggestions personnalisées</CardTitle>
                    <CardDescription>Basées sur vos achats précédents</CardDescription>
                  </CardHeader>
                  <CardContent className="text-center py-6">
                    <Link to="/account/suggestions">
                      <Button>Voir mes suggestions</Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </div>
      )}
    </AccountLayout>
  );
};

export default Dashboard;
