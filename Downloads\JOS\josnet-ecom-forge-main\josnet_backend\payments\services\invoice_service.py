import os
import logging
from io import BytesIO
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.template.loader import get_template
from ..models import Invoice

# Import ReportLab components
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import cm, mm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT

# Set up logger
logger = logging.getLogger(__name__)

def generate_invoice_pdf(invoice):
    """
    Generate a PDF invoice using ReportLab.

    Args:
        invoice: The invoice to generate a PDF for

    Returns:
        BytesIO: The PDF file
    """
    try:
        # Create a buffer to store the PDF
        buffer = BytesIO()

        # Create the PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm,
            title=f"Invoice {invoice.invoice_number}"
        )

        # Get styles
        styles = getSampleStyleSheet()

        # Create custom styles
        styles.add(ParagraphStyle(
            name='InvoiceTitle',
            parent=styles['Title'],
            fontSize=16,
            alignment=TA_CENTER,
            spaceAfter=0.5*cm
        ))

        styles.add(ParagraphStyle(
            name='InvoiceSubtitle',
            parent=styles['Normal'],
            fontSize=12,
            alignment=TA_CENTER,
            spaceAfter=0.5*cm
        ))

        styles.add(ParagraphStyle(
            name='InvoiceHeading',
            parent=styles['Heading2'],
            fontSize=12,
            spaceAfter=0.3*cm
        ))

        styles.add(ParagraphStyle(
            name='InvoiceInfo',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=0.1*cm
        ))

        styles.add(ParagraphStyle(
            name='InvoiceTotal',
            parent=styles['Normal'],
            fontSize=12,
            alignment=TA_RIGHT,
            fontName='Helvetica-Bold'
        ))

        # Create elements list to build the PDF
        elements = []

        # Add company logo if available
        # logo_path = os.path.join(settings.STATIC_ROOT, 'images', 'logo.png')
        # if os.path.exists(logo_path):
        #     elements.append(Image(logo_path, width=5*cm, height=2*cm))
        #     elements.append(Spacer(1, 0.5*cm))

        # Add invoice header
        elements.append(Paragraph(f"FACTURE", styles['InvoiceTitle']))
        elements.append(Paragraph(f"N° {invoice.invoice_number}", styles['InvoiceSubtitle']))
        elements.append(Spacer(1, 0.5*cm))

        # Create a table for invoice details and customer info
        invoice_date = invoice.issue_date.strftime('%d/%m/%Y')
        due_date = invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else 'N/A'

        # Get order and customer details
        order = invoice.order
        customer_name = f"{order.billing_first_name} {order.billing_last_name}"
        customer_email = order.email
        customer_address = f"{order.billing_address_line1}"
        if hasattr(order, 'billing_address_line2') and order.billing_address_line2:
            customer_address += f", {order.billing_address_line2}"
        customer_address += f", {order.billing_city}, {order.billing_postal_code}, {order.billing_country}"

        # Create a table for invoice details and customer info
        data = [
            ['Date d\'émission:', invoice_date, 'Client:', customer_name],
            ['Date d\'échéance:', due_date, 'Email:', customer_email],
            ['Statut:', invoice.get_status_display(), 'Adresse:', customer_address],
            ['N° Commande:', f"#{order.id}", '', ''],
        ]

        # Create the table
        info_table = Table(data, colWidths=[3*cm, 4*cm, 2*cm, 8*cm])
        info_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), 'Helvetica', 10),
            ('FONT', (0, 0), (0, -1), 'Helvetica-Bold', 10),
            ('FONT', (2, 0), (2, -1), 'Helvetica-Bold', 10),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ]))

        elements.append(info_table)
        elements.append(Spacer(1, 1*cm))

        # Add order items heading
        elements.append(Paragraph("Détails de la commande", styles['InvoiceHeading']))

        # Create a table for order items
        items_data = [['Produit', 'Quantité', 'Prix unitaire', 'Total']]

        # Add order items
        for item in order.items.all():
            items_data.append([
                item.product_name,
                str(item.quantity),
                f"{item.price:.2f} €",
                f"{item.subtotal:.2f} €"
            ])

        # Create the items table
        items_table = Table(items_data, colWidths=[8*cm, 2*cm, 3*cm, 3*cm])
        items_table.setStyle(TableStyle([
            # Header row
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONT', (0, 0), (-1, 0), 'Helvetica-Bold', 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
            ('TOPPADDING', (0, 0), (-1, 0), 6),

            # Data rows
            ('FONT', (0, 1), (-1, -1), 'Helvetica', 10),
            ('ALIGN', (1, 1), (-1, -1), 'CENTER'),
            ('ALIGN', (2, 1), (3, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 4),
            ('TOPPADDING', (0, 1), (-1, -1), 4),
        ]))

        elements.append(items_table)
        elements.append(Spacer(1, 0.5*cm))

        # Add totals
        totals_data = [
            ['', '', 'Sous-total:', f"{invoice.subtotal:.2f} €"],
            ['', '', 'TVA:', f"{invoice.tax_amount:.2f} €"],
            ['', '', 'Frais de livraison:', f"{invoice.shipping_amount:.2f} €"],
        ]

        if invoice.discount_amount > 0:
            totals_data.append(['', '', 'Remise:', f"-{invoice.discount_amount:.2f} €"])

        totals_data.append(['', '', 'Total:', f"{invoice.total:.2f} €"])

        # Create the totals table
        totals_table = Table(totals_data, colWidths=[8*cm, 2*cm, 3*cm, 3*cm])
        totals_table.setStyle(TableStyle([
            ('FONT', (0, 0), (-1, -1), 'Helvetica', 10),
            ('FONT', (2, 0), (2, -1), 'Helvetica-Bold', 10),
            ('FONT', (2, -1), (3, -1), 'Helvetica-Bold', 12),
            ('ALIGN', (2, 0), (3, -1), 'RIGHT'),
            ('LINEABOVE', (2, -1), (3, -1), 1, colors.black),
            ('TOPPADDING', (2, -1), (3, -1), 6),
        ]))

        elements.append(totals_table)
        elements.append(Spacer(1, 1*cm))

        # Add payment information
        elements.append(Paragraph("Informations de paiement", styles['InvoiceHeading']))

        payment_info = "Veuillez effectuer le paiement avant la date d'échéance. Merci de votre confiance."
        elements.append(Paragraph(payment_info, styles['InvoiceInfo']))

        # Add notes if available
        if invoice.notes:
            elements.append(Spacer(1, 0.5*cm))
            elements.append(Paragraph("Notes", styles['InvoiceHeading']))
            elements.append(Paragraph(invoice.notes, styles['InvoiceInfo']))

        # Add footer
        elements.append(Spacer(1, 1*cm))
        footer_text = "JosNet E-Commerce - SIRET: 123 456 789 00012 - TVA: FR12 345 678 901"
        elements.append(Paragraph(footer_text, styles['InvoiceInfo']))

        # Build the PDF
        doc.build(elements)
        buffer.seek(0)
        return buffer

    except Exception as e:
        logger.exception(f"Error generating invoice PDF: {str(e)}")
        # Fallback to a simple PDF if there's an error
        buffer = BytesIO()
        buffer.write(f"Invoice #{invoice.invoice_number} - Error generating PDF: {str(e)}".encode('utf-8'))
        buffer.seek(0)
        return buffer

def create_invoice_for_order(order):
    """
    Create an invoice for an order.

    Args:
        order: The order to create an invoice for

    Returns:
        Invoice: The created invoice
    """
    # Create the invoice
    invoice = Invoice.objects.create(
        order=order,
        subtotal=order.subtotal,
        tax_amount=order.tax_amount,
        shipping_amount=order.shipping_cost,
        discount_amount=order.discount_amount,
        total=order.total,
        status='issued',
        due_date=timezone.now() + timezone.timedelta(days=30)
    )

    # Generate the PDF
    pdf_file = generate_invoice_pdf(invoice)

    # Save the PDF to the invoice
    invoice.pdf_file.save(f"{invoice.invoice_number}.pdf", pdf_file)

    return invoice

def mark_invoice_as_paid(invoice, paid_date=None):
    """
    Mark an invoice as paid.

    Args:
        invoice: The invoice to mark as paid
        paid_date: The date the invoice was paid (defaults to now)

    Returns:
        Invoice: The updated invoice
    """
    invoice.status = 'paid'
    invoice.paid_date = paid_date or timezone.now().date()
    invoice.save()
    return invoice

def cancel_invoice(invoice):
    """
    Cancel an invoice.

    Args:
        invoice: The invoice to cancel

    Returns:
        Invoice: The updated invoice
    """
    invoice.status = 'cancelled'
    invoice.save()
    return invoice
