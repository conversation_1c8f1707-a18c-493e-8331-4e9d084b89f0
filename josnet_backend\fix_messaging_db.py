#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to check and fix messaging database issues.
Run this script to diagnose and repair common issues with the messaging app.
"""
import os
import sys
import django
import logging

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.db import connection
from django.db.models import Q
from messaging.models import Conversation, Message, Category, Tag, Attachment, ResponseTemplate

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('messaging_fix.log')
    ]
)
logger = logging.getLogger('messaging_fix')

def check_database_integrity():
    """Check database integrity for messaging models."""
    logger.info("Checking database integrity for messaging models...")
    
    # Check Conversation model
    try:
        conversation_count = Conversation.objects.count()
        logger.info(f"Found {conversation_count} conversations")
    except Exception as e:
        logger.error(f"Error checking Conversation model: {e}")
    
    # Check Message model
    try:
        message_count = Message.objects.count()
        logger.info(f"Found {message_count} messages")
    except Exception as e:
        logger.error(f"Error checking Message model: {e}")
    
    # Check Category model
    try:
        category_count = Category.objects.count()
        logger.info(f"Found {category_count} categories")
    except Exception as e:
        logger.error(f"Error checking Category model: {e}")
    
    # Check Tag model
    try:
        tag_count = Tag.objects.count()
        logger.info(f"Found {tag_count} tags")
    except Exception as e:
        logger.error(f"Error checking Tag model: {e}")
    
    # Check Attachment model
    try:
        attachment_count = Attachment.objects.count()
        logger.info(f"Found {attachment_count} attachments")
    except Exception as e:
        logger.error(f"Error checking Attachment model: {e}")
    
    # Check ResponseTemplate model
    try:
        template_count = ResponseTemplate.objects.count()
        logger.info(f"Found {template_count} response templates")
    except Exception as e:
        logger.error(f"Error checking ResponseTemplate model: {e}")

def check_orphaned_messages():
    """Check for orphaned messages (messages without a valid conversation)."""
    logger.info("Checking for orphaned messages...")
    
    try:
        orphaned_messages = Message.objects.filter(
            ~Q(conversation__in=Conversation.objects.all())
        )
        orphaned_count = orphaned_messages.count()
        
        if orphaned_count > 0:
            logger.warning(f"Found {orphaned_count} orphaned messages")
            # Optionally delete orphaned messages
            # orphaned_messages.delete()
            # logger.info(f"Deleted {orphaned_count} orphaned messages")
        else:
            logger.info("No orphaned messages found")
    except Exception as e:
        logger.error(f"Error checking orphaned messages: {e}")

def check_conversation_integrity():
    """Check conversation integrity (e.g., missing customer references)."""
    logger.info("Checking conversation integrity...")
    
    try:
        invalid_conversations = Conversation.objects.filter(customer__isnull=True)
        invalid_count = invalid_conversations.count()
        
        if invalid_count > 0:
            logger.warning(f"Found {invalid_count} conversations with missing customer references")
            # These conversations are problematic and might need manual fixing
            for conv in invalid_conversations:
                logger.warning(f"Conversation ID {conv.id} has no customer reference")
        else:
            logger.info("All conversations have valid customer references")
    except Exception as e:
        logger.error(f"Error checking conversation integrity: {e}")

def check_message_integrity():
    """Check message integrity (e.g., missing sender references)."""
    logger.info("Checking message integrity...")
    
    try:
        invalid_messages = Message.objects.filter(sender__isnull=True)
        invalid_count = invalid_messages.count()
        
        if invalid_count > 0:
            logger.warning(f"Found {invalid_count} messages with missing sender references")
            # These messages are problematic and might need manual fixing
            for msg in invalid_messages:
                logger.warning(f"Message ID {msg.id} has no sender reference")
        else:
            logger.info("All messages have valid sender references")
    except Exception as e:
        logger.error(f"Error checking message integrity: {e}")

def fix_database_issues():
    """Fix common database issues."""
    logger.info("Fixing database issues...")
    
    # Fix conversations with incorrect last_message_at
    try:
        fixed_count = 0
        for conv in Conversation.objects.all():
            last_message = conv.messages.order_by('-created_at').first()
            if last_message and conv.last_message_at != last_message.created_at:
                conv.last_message_at = last_message.created_at
                conv.save(update_fields=['last_message_at'])
                fixed_count += 1
        
        logger.info(f"Fixed last_message_at for {fixed_count} conversations")
    except Exception as e:
        logger.error(f"Error fixing last_message_at: {e}")
    
    # Add more fixes as needed

def main():
    """Main function to run all checks and fixes."""
    logger.info("Starting messaging database check and fix...")
    
    # Run checks
    check_database_integrity()
    check_orphaned_messages()
    check_conversation_integrity()
    check_message_integrity()
    
    # Run fixes
    fix_database_issues()
    
    logger.info("Completed messaging database check and fix")

if __name__ == "__main__":
    main()
