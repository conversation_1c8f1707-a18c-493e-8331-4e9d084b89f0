from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from orders.models import Order
from authentication.permissions import IsStaffOrAdmin, IsOwnerOrStaff

from payments.models import Invoice
from payments.serializers import InvoiceSerializer, InvoiceDetailSerializer
from payments.services import invoice_service


class InvoiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing invoices.
    """
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    permission_classes = [IsOwnerOrStaff]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['order', 'status', 'issue_date', 'paid_date']
    search_fields = ['invoice_number', 'notes']
    ordering_fields = ['issue_date', 'due_date', 'paid_date', 'total']
    ordering = ['-issue_date']

    def get_serializer_class(self):
        if self.action in ['retrieve']:
            return InvoiceDetailSerializer
        return InvoiceSerializer

    def get_queryset(self):
        """Filter invoices based on user permissions."""
        user = self.request.user
        if user.is_staff or user.is_superuser:
            return Invoice.objects.all()
        return Invoice.objects.filter(order__user=user)

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def my_invoices(self, request):
        """Get all invoices for the current user."""
        invoices = Invoice.objects.filter(order__user=request.user)
        page = self.paginate_queryset(invoices)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(invoices, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download invoice PDF."""
        invoice = self.get_object()

        # Check if PDF exists
        if invoice.pdf_file:
            # Return existing PDF
            try:
                invoice.pdf_file.open()
                pdf_content = invoice.pdf_file.read()
                invoice.pdf_file.close()
                response = HttpResponse(pdf_content, content_type='application/pdf')
                response['Content-Disposition'] = f'attachment; filename="{invoice.invoice_number}.pdf"'
                return response
            except Exception as e:
                # If there's an error reading the existing PDF, regenerate it
                print(f"Error reading existing PDF for invoice {invoice.invoice_number}: {e}")
                # Continue to regeneration below

        # Generate PDF if it doesn't exist
        pdf_buffer = invoice_service.generate_invoice_pdf(invoice)

        # Get PDF content for response
        pdf_content = pdf_buffer.getvalue()

        # Save PDF to invoice
        from django.core.files.base import ContentFile
        invoice.pdf_file.save(f"{invoice.invoice_number}.pdf", ContentFile(pdf_content))

        # Return PDF
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{invoice.invoice_number}.pdf"'
        return response

    @action(detail=False, methods=['post'], permission_classes=[IsStaffOrAdmin])
    def generate_for_order(self, request):
        """Generate invoice for an order."""
        order_id = request.data.get('order_id')
        if not order_id:
            return Response(
                {"detail": "Order ID is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        order = get_object_or_404(Order, id=order_id)

        # Check if invoice already exists
        existing_invoice = Invoice.objects.filter(order=order).first()
        if existing_invoice:
            serializer = self.get_serializer(existing_invoice)
            return Response(serializer.data)

        # Create invoice
        invoice = invoice_service.create_invoice_for_order(order)

        serializer = self.get_serializer(invoice)
        return Response(serializer.data)
