import React from 'react';
import { AlertCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export interface ErrorStateProps {
  title?: string;
  message?: string;
  type?: 'network' | 'server' | 'auth' | 'not-found' | 'generic';
  onRetry?: () => void;
  retryLabel?: string;
  showRetry?: boolean;
  className?: string;
}

const ErrorState: React.FC<ErrorStateProps> = ({
  title,
  message,
  type = 'generic',
  onRetry,
  retryLabel = 'Réessayer',
  showRetry = true,
  className = ''
}) => {
  // Configuration par défaut selon le type d'erreur
  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          icon: <WifiOff className="h-12 w-12 text-red-400" />,
          defaultTitle: 'Problème de connexion',
          defaultMessage: 'Impossible de contacter le serveur. Vérifiez votre connexion internet.',
          color: 'red'
        };
      case 'server':
        return {
          icon: <AlertCircle className="h-12 w-12 text-orange-400" />,
          defaultTitle: 'Erreur du serveur',
          defaultMessage: 'Le serveur rencontre des difficultés. Veuillez réessayer dans quelques instants.',
          color: 'orange'
        };
      case 'auth':
        return {
          icon: <AlertCircle className="h-12 w-12 text-yellow-400" />,
          defaultTitle: 'Authentification requise',
          defaultMessage: 'Vous devez être connecté pour accéder à cette ressource.',
          color: 'yellow'
        };
      case 'not-found':
        return {
          icon: <AlertCircle className="h-12 w-12 text-gray-400" />,
          defaultTitle: 'Ressource non trouvée',
          defaultMessage: 'La ressource demandée n\'existe pas ou n\'est plus disponible.',
          color: 'gray'
        };
      default:
        return {
          icon: <AlertCircle className="h-12 w-12 text-red-400" />,
          defaultTitle: 'Une erreur s\'est produite',
          defaultMessage: 'Une erreur inattendue s\'est produite. Veuillez réessayer.',
          color: 'red'
        };
    }
  };

  const config = getErrorConfig();
  const displayTitle = title || config.defaultTitle;
  const displayMessage = message || config.defaultMessage;

  return (
    <Card className={`${className}`}>
      <CardContent className="py-8 text-center">
        <div className="flex flex-col items-center space-y-4">
          {config.icon}
          
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">
              {displayTitle}
            </h3>
            <p className="text-gray-600 max-w-md">
              {displayMessage}
            </p>
          </div>

          {showRetry && onRetry && (
            <Button 
              variant="outline" 
              onClick={onRetry}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              {retryLabel}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Composant spécialisé pour les erreurs de réseau
 */
export const NetworkErrorState: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorState {...props} type="network" />
);

/**
 * Composant spécialisé pour les erreurs de serveur
 */
export const ServerErrorState: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorState {...props} type="server" />
);

/**
 * Composant spécialisé pour les erreurs d'authentification
 */
export const AuthErrorState: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorState {...props} type="auth" />
);

/**
 * Composant spécialisé pour les ressources non trouvées
 */
export const NotFoundErrorState: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorState {...props} type="not-found" />
);

/**
 * Composant d'état de chargement avec fallback d'erreur
 */
export interface LoadingStateProps {
  isLoading: boolean;
  isError: boolean;
  error?: any;
  onRetry?: () => void;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  isLoading,
  isError,
  error,
  onRetry,
  children,
  loadingComponent,
  errorComponent
}) => {
  if (isLoading) {
    return (
      <>
        {loadingComponent || (
          <Card className="p-8 text-center">
            <CardContent className="flex flex-col items-center justify-center py-6">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
              <h3 className="text-lg font-medium mb-2">Chargement...</h3>
              <p className="text-gray-500 max-w-sm">
                Veuillez patienter pendant que nous récupérons les données.
              </p>
            </CardContent>
          </Card>
        )}
      </>
    );
  }

  if (isError) {
    return (
      <>
        {errorComponent || (
          <ErrorState
            type={error?.status === 0 ? 'network' : error?.status >= 500 ? 'server' : 'generic'}
            message={error?.message}
            onRetry={onRetry}
          />
        )}
      </>
    );
  }

  return <>{children}</>;
};

export default ErrorState;
