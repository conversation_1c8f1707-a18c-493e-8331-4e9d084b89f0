from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models_user_suggestions import (
    ProductSuggestion, 
    ProductSuggestionVote, 
    ProductSuggestionComment, 
    ProductSuggestionAttachment
)

User = get_user_model()


class ProductSuggestionUserSerializer(serializers.ModelSerializer):
    """Serializer pour les informations utilisateur dans les suggestions."""
    
    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name']
        read_only_fields = ['id', 'email', 'first_name', 'last_name']


class ProductSuggestionVoteSerializer(serializers.ModelSerializer):
    """Serializer pour les votes sur les suggestions."""
    
    user = ProductSuggestionUserSerializer(read_only=True)
    
    class Meta:
        model = ProductSuggestionVote
        fields = ['id', 'user', 'created_at']
        read_only_fields = ['id', 'created_at']


class ProductSuggestionCommentSerializer(serializers.ModelSerializer):
    """Serializer pour les commentaires sur les suggestions."""
    
    user = ProductSuggestionUserSerializer(read_only=True)
    
    class Meta:
        model = ProductSuggestionComment
        fields = [
            'id', 'user', 'content', 'is_admin_comment', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']


class ProductSuggestionAttachmentSerializer(serializers.ModelSerializer):
    """Serializer pour les pièces jointes des suggestions."""
    
    uploaded_by = ProductSuggestionUserSerializer(read_only=True)
    file_size_mb = serializers.ReadOnlyField()
    
    class Meta:
        model = ProductSuggestionAttachment
        fields = [
            'id', 'file', 'filename', 'file_size', 'file_size_mb',
            'content_type', 'uploaded_by', 'created_at'
        ]
        read_only_fields = ['id', 'uploaded_by', 'created_at']


class ProductSuggestionListSerializer(serializers.ModelSerializer):
    """Serializer pour la liste des suggestions (vue simplifiée)."""
    
    user = ProductSuggestionUserSerializer(read_only=True)
    reviewed_by = ProductSuggestionUserSerializer(read_only=True)
    votes_count = serializers.ReadOnlyField()
    days_since_creation = serializers.ReadOnlyField()
    response_time_days = serializers.ReadOnlyField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    
    class Meta:
        model = ProductSuggestion
        fields = [
            'id', 'name', 'category', 'status', 'status_display',
            'priority', 'priority_display', 'user', 'reviewed_by',
            'votes_count', 'days_since_creation', 'response_time_days',
            'created_at', 'updated_at', 'response_date'
        ]
        read_only_fields = [
            'id', 'user', 'reviewed_by', 'votes_count', 
            'days_since_creation', 'response_time_days',
            'created_at', 'updated_at', 'response_date'
        ]


class ProductSuggestionDetailSerializer(serializers.ModelSerializer):
    """Serializer pour les détails complets d'une suggestion."""
    
    user = ProductSuggestionUserSerializer(read_only=True)
    reviewed_by = ProductSuggestionUserSerializer(read_only=True)
    votes = ProductSuggestionVoteSerializer(many=True, read_only=True)
    comments = ProductSuggestionCommentSerializer(many=True, read_only=True)
    attachments = ProductSuggestionAttachmentSerializer(many=True, read_only=True)
    
    votes_count = serializers.ReadOnlyField()
    days_since_creation = serializers.ReadOnlyField()
    response_time_days = serializers.ReadOnlyField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    
    # Champs calculés
    user_has_voted = serializers.SerializerMethodField()
    comments_count = serializers.SerializerMethodField()
    attachments_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductSuggestion
        fields = [
            'id', 'name', 'category', 'description', 'estimated_price',
            'reason', 'status', 'status_display', 'priority', 'priority_display',
            'admin_response', 'response_date', 'implementation_notes',
            'estimated_implementation_time', 'user', 'reviewed_by',
            'votes_count', 'days_since_creation', 'response_time_days',
            'created_at', 'updated_at', 'votes', 'comments', 'attachments',
            'user_has_voted', 'comments_count', 'attachments_count'
        ]
        read_only_fields = [
            'id', 'user', 'reviewed_by', 'votes_count', 
            'days_since_creation', 'response_time_days',
            'created_at', 'updated_at', 'response_date',
            'votes', 'comments', 'attachments'
        ]
    
    def get_user_has_voted(self, obj):
        """Vérifie si l'utilisateur actuel a voté pour cette suggestion."""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.votes.filter(user=request.user).exists()
        return False
    
    def get_comments_count(self, obj):
        """Retourne le nombre de commentaires."""
        return obj.comments.count()
    
    def get_attachments_count(self, obj):
        """Retourne le nombre de pièces jointes."""
        return obj.attachments.count()


class ProductSuggestionCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de nouvelles suggestions."""
    
    class Meta:
        model = ProductSuggestion
        fields = [
            'name', 'category', 'description', 'estimated_price', 'reason'
        ]
    
    def validate_name(self, value):
        """Valide le nom du produit suggéré."""
        if len(value.strip()) < 3:
            raise serializers.ValidationError(
                "Le nom du produit doit contenir au moins 3 caractères."
            )
        return value.strip()
    
    def validate_description(self, value):
        """Valide la description."""
        if len(value.strip()) < 10:
            raise serializers.ValidationError(
                "La description doit contenir au moins 10 caractères."
            )
        return value.strip()
    
    def validate_reason(self, value):
        """Valide la raison de la suggestion."""
        if len(value.strip()) < 10:
            raise serializers.ValidationError(
                "Veuillez expliquer en détail pourquoi vous souhaitez ce produit (minimum 10 caractères)."
            )
        return value.strip()
    
    def validate_estimated_price(self, value):
        """Valide le prix estimé."""
        if value is not None and value < 0:
            raise serializers.ValidationError(
                "Le prix estimé ne peut pas être négatif."
            )
        return value
    
    def create(self, validated_data):
        """Crée une nouvelle suggestion."""
        # L'utilisateur est automatiquement assigné depuis la vue
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class ProductSuggestionUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour des suggestions (utilisateur)."""
    
    class Meta:
        model = ProductSuggestion
        fields = [
            'name', 'category', 'description', 'estimated_price', 'reason'
        ]
    
    def validate(self, attrs):
        """Valide que la suggestion peut être modifiée."""
        instance = self.instance
        if instance and instance.status != 'pending':
            raise serializers.ValidationError(
                "Vous ne pouvez modifier que les suggestions en attente."
            )
        return attrs


class ProductSuggestionAdminUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour des suggestions (admin)."""
    
    class Meta:
        model = ProductSuggestion
        fields = [
            'status', 'priority', 'admin_response', 'implementation_notes',
            'estimated_implementation_time'
        ]
    
    def validate(self, attrs):
        """Valide les données admin."""
        if 'status' in attrs and attrs['status'] in ['approved', 'rejected']:
            if not attrs.get('admin_response'):
                raise serializers.ValidationError(
                    "Une réponse administrateur est requise pour approuver ou rejeter une suggestion."
                )
        return attrs
    
    def update(self, instance, validated_data):
        """Met à jour la suggestion avec les données admin."""
        # Assigner l'admin qui fait la modification
        if 'status' in validated_data:
            validated_data['reviewed_by'] = self.context['request'].user
        
        return super().update(instance, validated_data)


class ProductSuggestionStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques des suggestions."""
    
    total_suggestions = serializers.IntegerField()
    pending_suggestions = serializers.IntegerField()
    approved_suggestions = serializers.IntegerField()
    rejected_suggestions = serializers.IntegerField()
    implemented_suggestions = serializers.IntegerField()
    
    suggestions_this_month = serializers.IntegerField()
    suggestions_this_week = serializers.IntegerField()
    
    top_categories = serializers.ListField(
        child=serializers.DictField()
    )
    
    avg_response_time_days = serializers.FloatField()
    most_voted_suggestions = serializers.ListField(
        child=ProductSuggestionListSerializer()
    )
