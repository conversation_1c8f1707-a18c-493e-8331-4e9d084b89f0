import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatPrice } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { productApi, ProductListItem } from '@/services/productApi';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, ShoppingCart } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface PromotionPreviewProps {
  discountType: 'percentage' | 'fixed_amount';
  discountValue: number;
  minPurchaseAmount: number;
}

interface CartItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
}

const PromotionPreview: React.FC<PromotionPreviewProps> = ({
  discountType,
  discountValue,
  minPurchaseAmount,
}) => {
  // Récupérer les produits populaires de la base de données
  const { data: productsData, isLoading, error } = useQuery({
    queryKey: ['popular-products-for-preview'],
    queryFn: () => productApi.getProducts({
      page: 1,
      page_size: 3,
      is_featured: true,
      in_stock: true
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Convertir les produits en articles de panier
  const [cart, setCart] = useState<CartItem[]>([]);

  useEffect(() => {
    if (productsData?.results) {
      // Créer un panier avec les produits récupérés
      const newCart = productsData.results.map((product: ProductListItem) => ({
        id: product.id,
        name: product.name,
        price: product.current_price,
        quantity: Math.floor(Math.random() * 2) + 1, // Quantité aléatoire entre 1 et 3
      }));
      setCart(newCart);
    }
  }, [productsData]);

  // Calculs
  const subtotal = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const isEligible = subtotal >= minPurchaseAmount;
  
  // Calcul de la remise
  const calculateDiscount = () => {
    if (!isEligible) return 0;
    
    if (discountType === 'percentage') {
      return (subtotal * discountValue) / 100;
    } else {
      return discountValue;
    }
  };
  
  const discount = calculateDiscount();
  const total = subtotal - discount;

  // Afficher un état de chargement pendant que les données sont récupérées
  if (isLoading) {
    return (
      <Card className="mt-6 bg-gray-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Prévisualisation de la promotion</CardTitle>
          <CardDescription>
            Chargement des produits pour la simulation...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Afficher un message d'erreur si la récupération des produits a échoué
  if (error) {
    return (
      <Card className="mt-6 bg-gray-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Prévisualisation de la promotion</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>
              Impossible de charger les produits pour la prévisualisation.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Afficher un message si aucun produit n'a été trouvé
  if (cart.length === 0) {
    return (
      <Card className="mt-6 bg-gray-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Prévisualisation de la promotion</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <ShoppingCart className="h-12 w-12 text-gray-400 mb-2" />
            <h3 className="text-lg font-medium">Aucun produit disponible</h3>
            <p className="text-sm text-gray-500 mt-1">
              Impossible de générer une prévisualisation sans produits.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-6 bg-gray-50">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Prévisualisation de la promotion</CardTitle>
        <CardDescription>
          Simulation de l'impact sur un panier réel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Produit</TableHead>
              <TableHead className="text-right">Prix</TableHead>
              <TableHead className="text-right">Qté</TableHead>
              <TableHead className="text-right">Total</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {cart.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.name}</TableCell>
                <TableCell className="text-right">{formatPrice(item.price)}</TableCell>
                <TableCell className="text-right">{item.quantity}</TableCell>
                <TableCell className="text-right">{formatPrice(item.price * item.quantity)}</TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell colSpan={3} className="text-right font-medium">Sous-total</TableCell>
              <TableCell className="text-right font-medium">{formatPrice(subtotal)}</TableCell>
            </TableRow>
            {!isEligible && minPurchaseAmount > 0 && (
              <TableRow className="text-amber-600">
                <TableCell colSpan={4} className="text-center">
                  Le panier n'atteint pas le montant minimum de {formatPrice(minPurchaseAmount)} requis pour cette promotion
                </TableCell>
              </TableRow>
            )}
            {isEligible && (
              <>
                <TableRow className="text-green-600">
                  <TableCell colSpan={3} className="text-right font-medium">
                    Remise {discountType === 'percentage' ? `(${discountValue}%)` : ''}
                  </TableCell>
                  <TableCell className="text-right font-medium">-{formatPrice(discount)}</TableCell>
                </TableRow>
                <TableRow className="border-t-2">
                  <TableCell colSpan={3} className="text-right font-bold">Total</TableCell>
                  <TableCell className="text-right font-bold">{formatPrice(total)}</TableCell>
                </TableRow>
              </>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default PromotionPreview;
