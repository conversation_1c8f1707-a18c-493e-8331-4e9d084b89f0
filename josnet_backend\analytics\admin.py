from django.contrib import admin
from .models import (
    ProductView, CartAction, WishlistAction, SalesData, SystemLog,
    HomePageContent, HomePageStats, HomePageService, HomePageTestimonial, HomePageFeature
)

@admin.register(ProductView)
class ProductViewAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'ip_address', 'device_type', 'created_at')
    list_filter = ('device_type', 'created_at')
    search_fields = ('product__name', 'user__email', 'ip_address')
    date_hierarchy = 'created_at'

@admin.register(CartAction)
class CartActionAdmin(admin.ModelAdmin):
    list_display = ('product', 'variant', 'user', 'action', 'quantity', 'created_at')
    list_filter = ('action', 'created_at')
    search_fields = ('product__name', 'user__email')
    date_hierarchy = 'created_at'

@admin.register(WishlistAction)
class WishlistActionAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'action', 'created_at')
    list_filter = ('action', 'created_at')
    search_fields = ('product__name', 'user__email')
    date_hierarchy = 'created_at'

@admin.register(SalesData)
class SalesDataAdmin(admin.ModelAdmin):
    list_display = ('product', 'variant', 'order_id', 'customer_id', 'quantity', 'unit_price', 'total_price', 'date')
    list_filter = ('date',)
    search_fields = ('product__name', 'order_id', 'customer_id')
    date_hierarchy = 'date'

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('log_id', 'timestamp', 'type', 'module', 'message_preview', 'user_display', 'ip_address')
    list_filter = ('type', 'module', 'timestamp')
    search_fields = ('log_id', 'message', 'user__email', 'user_email', 'ip_address')
    readonly_fields = ('id', 'log_id', 'timestamp')
    ordering = ('-timestamp',)

    def message_preview(self, obj):
        """Afficher un aperçu du message."""
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_preview.short_description = 'Message'

    def user_display(self, obj):
        """Afficher l'utilisateur."""
        if obj.user:
            return obj.user.email
        elif obj.user_email:
            return obj.user_email
        return "Système"
    user_display.short_description = 'Utilisateur'

    def has_add_permission(self, request):
        """Empêcher l'ajout manuel de logs."""
        return False

    def has_change_permission(self, request, obj=None):
        """Empêcher la modification des logs."""
        return False


@admin.register(HomePageContent)
class HomePageContentAdmin(admin.ModelAdmin):
    list_display = ('section', 'title', 'is_active', 'order', 'updated_at')
    list_filter = ('section', 'is_active', 'created_at')
    search_fields = ('title', 'subtitle', 'description')
    list_editable = ('is_active', 'order')
    ordering = ('order', 'section')


@admin.register(HomePageStats)
class HomePageStatsAdmin(admin.ModelAdmin):
    list_display = ('display_name', 'value', 'suffix', 'auto_update', 'is_active', 'order')
    list_filter = ('auto_update', 'is_active', 'created_at')
    search_fields = ('name', 'display_name', 'description')
    list_editable = ('value', 'is_active', 'order')
    ordering = ('order', 'name')


@admin.register(HomePageService)
class HomePageServiceAdmin(admin.ModelAdmin):
    list_display = ('title', 'icon', 'color', 'is_active', 'order')
    list_filter = ('color', 'is_active', 'created_at')
    search_fields = ('name', 'title', 'description')
    list_editable = ('is_active', 'order')
    ordering = ('order', 'name')


@admin.register(HomePageTestimonial)
class HomePageTestimonialAdmin(admin.ModelAdmin):
    list_display = ('client_name', 'client_company', 'rating', 'is_featured', 'is_active', 'order')
    list_filter = ('rating', 'is_featured', 'is_active', 'created_at')
    search_fields = ('client_name', 'client_company', 'testimonial')
    list_editable = ('is_featured', 'is_active', 'order')
    ordering = ('order', '-created_at')


@admin.register(HomePageFeature)
class HomePageFeatureAdmin(admin.ModelAdmin):
    list_display = ('title', 'icon', 'color', 'is_active', 'order')
    list_filter = ('color', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_editable = ('is_active', 'order')
    ordering = ('order', 'title')
