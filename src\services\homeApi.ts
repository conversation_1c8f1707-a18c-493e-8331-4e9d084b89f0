import { API_BASE_URL, getA<PERSON><PERSON>eader, handleApiError, ApiError } from './api';

// Types pour la page d'accueil
export interface FeaturedProduct {
  id: number;
  name: string;
  price: number;
  image: string;
  category: string;
  description: string;
  is_featured: boolean;
  in_stock: boolean;
  rating?: number;
  reviews_count?: number;
}

export interface HomeStats {
  total_customers: number;
  total_projects: number;
  years_experience: number;
  total_products: number;
}

export interface Testimonial {
  id: number;
  name: string;
  role: string;
  company?: string;
  content: string;
  rating: number;
  image_url?: string;
  is_featured: boolean;
}

export interface HomePageData {
  featured_products: FeaturedProduct[];
  stats: HomeStats;
  testimonials: Testimonial[];
  categories: string[];
}

const homeApi = {
  // Get all home page data
  getHomePageData: async (): Promise<HomePageData> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/home-data/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching home page data:', error);

      // Fallback to mock data in case of error
      console.warn('Using mock data as fallback for home page');

      return {
        featured_products: [
          {
            id: 1,
            name: "Routeur Wi-Fi Pro X500",
            price: 89.99,
            category: "Réseaux",
            image: "https://images.unsplash.com/photo-1648412918243-dccd4f247a96?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8cm91dGVyfGVufDB8fDB8fHww",
            description: "Routeur haute performance avec Wi-Fi 6, idéal pour le streaming 4K et les jeux en ligne.",
            is_featured: true,
            in_stock: true,
            rating: 4.5,
            reviews_count: 12
          },
          {
            id: 2,
            name: "Ordinateur Portable TechPro X15",
            price: 899.99,
            category: "Ordinateurs",
            image: "https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bGFwdG9wfGVufDB8fDB8fHww",
            description: "Ordinateur portable puissant avec processeur Intel Core i7, 16 Go de RAM et un SSD de 512 Go.",
            is_featured: true,
            in_stock: true,
            rating: 4.8,
            reviews_count: 25
          },
          {
            id: 3,
            name: "Smartphone NextGen S22",
            price: 699.99,
            category: "Téléphones",
            image: "https://images.unsplash.com/photo-1598327105666-5b89351aff97?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8c21hcnRwaG9uZXxlbnwwfHwwfHx8MA%3D%3D",
            description: "Smartphone dernier cri avec écran AMOLED 6.5\", caméra 108MP et batterie longue durée.",
            is_featured: true,
            in_stock: true,
            rating: 4.6,
            reviews_count: 18
          },
          {
            id: 4,
            name: "Casque Audio Premium",
            price: 149.99,
            category: "Audio",
            image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8aGVhZHBob25lc3xlbnwwfHwwfHx8MA%3D%3D",
            description: "Casque sans fil avec réduction de bruit active, autonomie de 30 heures et son de qualité studio.",
            is_featured: true,
            in_stock: true,
            rating: 4.7,
            reviews_count: 31
          }
        ],
        stats: {
          total_customers: 1000,
          total_projects: 500,
          years_experience: 10,
          total_products: 150
        },
        testimonials: [
          {
            id: 1,
            name: "Marie Uwimana",
            role: "Directrice IT",
            company: "TechCorp Burundi",
            content: "JOSNET NETWORK nous a fourni des solutions exceptionnelles pour notre infrastructure réseau. Leur expertise et leur service client sont remarquables.",
            rating: 5,
            image_url: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
            is_featured: true
          },
          {
            id: 2,
            name: "Jean-Baptiste Niyonzima",
            role: "Entrepreneur",
            company: "StartupBDI",
            content: "Grâce à JOSNET, nous avons pu moderniser complètement notre système informatique. Les résultats sont impressionnants !",
            rating: 5,
            image_url: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
            is_featured: true
          },
          {
            id: 3,
            name: "Aline Ndayisenga",
            role: "Responsable Technique",
            company: "EduTech Solutions",
            content: "L'équipe de JOSNET est très professionnelle. Ils ont su comprendre nos besoins et proposer des solutions adaptées.",
            rating: 4,
            image_url: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
            is_featured: true
          }
        ],
        categories: ["Tous", "Réseaux", "Ordinateurs", "Téléphones", "Audio", "Périphériques"]
      };
    }
  },

  // Get featured products only
  getFeaturedProducts: async (): Promise<FeaturedProduct[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/featured/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();

      // Handle both array and paginated response
      const products = Array.isArray(data) ? data : (data.results || []);

      // Transform data to match frontend expectations
      return products.map((product: any) => ({
        id: product.id,
        name: product.name,
        price: parseFloat(product.price),
        // Utiliser primary_image d'abord, puis la première image, puis placeholder
        image: product.primary_image?.image ||
               (product.images && product.images.length > 0 ? product.images[0].image : null) ||
               "/placeholder.svg",
        category: product.categories && product.categories.length > 0
          ? product.categories[0].name
          : "Général",
        description: product.description || product.short_description || "",
        is_featured: product.is_featured || false,
        in_stock: product.inventory ? product.inventory.quantity > 0 : true,
        rating: product.average_rating || 4.0,
        reviews_count: product.reviews_count || 0
      }));
    } catch (error) {
      console.error('Error fetching featured products:', error);

      // Fallback to home page data
      const homeData = await homeApi.getHomePageData();
      return homeData.featured_products;
    }
  },

  // Get home page statistics
  getHomeStats: async (): Promise<HomeStats> => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/home-stats/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching home stats:', error);

      // Return default stats
      return {
        total_customers: 1000,
        total_projects: 500,
        years_experience: 10,
        total_products: 150
      };
    }
  },

  // Get testimonials
  getTestimonials: async (): Promise<Testimonial[]> => {
    try {
      // For now, return mock testimonials since the endpoint doesn't exist yet
      // TODO: Implement testimonials endpoint in the backend
      console.log('Using mock testimonials data (endpoint not implemented yet)');

      return [
        {
          id: 1,
          name: "Marie Uwimana",
          role: "Directrice IT",
          company: "TechCorp Burundi",
          content: "JOSNET NETWORK nous a fourni des solutions exceptionnelles pour notre infrastructure réseau. Leur expertise et leur service client sont remarquables.",
          rating: 5,
          image_url: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
          is_featured: true
        },
        {
          id: 2,
          name: "Jean-Baptiste Niyonzima",
          role: "Entrepreneur",
          company: "StartupBDI",
          content: "Grâce à JOSNET, nous avons pu moderniser complètement notre système informatique. Les résultats sont impressionnants !",
          rating: 5,
          image_url: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
          is_featured: true
        },
        {
          id: 3,
          name: "Aline Ndayisenga",
          role: "Responsable Technique",
          company: "EduTech Solutions",
          content: "L'équipe de JOSNET est très professionnelle. Ils ont su comprendre nos besoins et proposer des solutions adaptées.",
          rating: 4,
          image_url: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
          is_featured: true
        }
      ];
    } catch (error) {
      console.error('Error fetching testimonials:', error);

      // Return empty array as fallback
      return [];
    }
  },

  // Get product categories
  getCategories: async (): Promise<string[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/categories/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();
      const categories = data.results || data;

      // Add "Tous" at the beginning and extract category names
      const categoryNames = ["Tous", ...categories.map((cat: any) => cat.name)];
      return categoryNames;
    } catch (error) {
      console.error('Error fetching categories:', error);

      // Return default categories
      return ["Tous", "Réseaux", "Ordinateurs", "Téléphones", "Audio", "Périphériques"];
    }
  },

  // Get a single product by ID
  getProductById: async (productId: number): Promise<FeaturedProduct | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/products/${productId}/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null; // Product not found
        }
        throw await handleApiError(response);
      }

      const productData = await response.json();

      // Transform to match frontend expectations
      return {
        id: productData.id,
        name: productData.name,
        price: parseFloat(productData.price),
        // Utiliser primary_image d'abord, puis la première image, puis placeholder
        image: productData.primary_image?.image ||
               (productData.images && productData.images.length > 0 ? productData.images[0].image : null) ||
               "/placeholder.svg",
        category: productData.categories && productData.categories.length > 0
          ? productData.categories[0].name
          : "Général",
        description: productData.description || productData.short_description || "",
        is_featured: productData.is_featured || false,
        in_stock: productData.inventory ? productData.inventory.quantity > 0 : true,
        rating: productData.average_rating || 4.0,
        reviews_count: productData.reviews_count || 0
      };
    } catch (error) {
      console.error(`Error fetching product ${productId}:`, error);
      return null;
    }
  },

  // Nouvelles méthodes pour les données dynamiques de la page d'accueil

  // Récupérer toutes les données dynamiques de la page d'accueil
  getDynamicHomePageData: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/home-page-data/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching dynamic home page data:', error);
      return {
        content: [],
        stats: [],
        services: [],
        testimonials: [],
        features: []
      };
    }
  },

  // Récupérer seulement les statistiques dynamiques
  getDynamicHomeStats: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/home-page-stats/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching dynamic home stats:', error);
      return [];
    }
  }
};

export default homeApi;
