#!/usr/bin/env python
"""
Script pour créer des notifications de test.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from notifications.models import Notification
from core.models import NewsletterSubscription

User = get_user_model()


def create_test_notifications():
    """Créer des notifications de test pour les utilisateurs."""
    print("📧 CRÉATION DE NOTIFICATIONS DE TEST")
    print("=" * 60)
    
    # Récupérer les utilisateurs avec vraies adresses email
    real_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    notifications_created = 0
    
    for email in real_emails:
        user = User.objects.filter(email=email).first()
        
        if user:
            print(f"🔄 Création de notifications pour: {email}")
            
            # Nettoyer les anciennes notifications de test
            Notification.objects.filter(
                user=user,
                title__contains='Test Notification'
            ).delete()
            
            # Notification de promotion
            promo_notification = Notification.objects.create(
                user=user,
                title='🎉 Test Notification - Nouvelle promotion disponible !',
                message='Profitez de 50% de réduction sur tous nos produits informatiques. Offre limitée dans le temps !',
                type='promotion',
                is_read=False,
                link='/promotions'
            )
            notifications_created += 1
            print(f"   ✅ Notification promotion créée: {promo_notification.id}")
            
            # Notification de commande
            order_notification = Notification.objects.create(
                user=user,
                title='📦 Test Notification - Votre commande est en cours',
                message='Votre commande #12345 a été confirmée et sera expédiée sous 24h. Merci pour votre confiance !',
                type='order',
                is_read=False,
                link='/account/orders'
            )
            notifications_created += 1
            print(f"   ✅ Notification commande créée: {order_notification.id}")
            
            # Notification système
            system_notification = Notification.objects.create(
                user=user,
                title='🔧 Test Notification - Maintenance programmée',
                message='Une maintenance du système est programmée dimanche de 2h à 4h du matin. Les services pourraient être temporairement indisponibles.',
                type='system',
                is_read=True,  # Déjà lue
                link='/maintenance'
            )
            notifications_created += 1
            print(f"   ✅ Notification système créée: {system_notification.id}")
            
            # Notification info
            info_notification = Notification.objects.create(
                user=user,
                title='ℹ️ Test Notification - Nouveautés JosNet Network',
                message='Découvrez nos nouveaux services : Cloud Computing, Cybersécurité et Solutions IoT. Contactez-nous pour plus d\'informations.',
                type='info',
                is_read=False,
                link='/services'
            )
            notifications_created += 1
            print(f"   ✅ Notification info créée: {info_notification.id}")
            
        else:
            print(f"   ⚠️  Utilisateur non trouvé: {email}")
    
    # Créer une notification globale (pour tous les utilisateurs)
    global_notification = Notification.objects.create(
        user=None,  # Notification globale
        title='📢 Test Notification - Annonce importante',
        message='JosNet Network vous souhaite une excellente année 2024 ! Merci pour votre fidélité.',
        type='info',
        is_read=False,
        link='/about'
    )
    notifications_created += 1
    print(f"🌐 Notification globale créée: {global_notification.id}")
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   ✅ Total notifications créées: {notifications_created}")
    print(f"   👥 Utilisateurs concernés: {len([email for email in real_emails if User.objects.filter(email=email).exists()])}")
    print(f"   🌐 Notifications globales: 1")
    
    return notifications_created


def verify_notifications():
    """Vérifier les notifications créées."""
    print(f"\n🔍 VÉRIFICATION DES NOTIFICATIONS")
    print("=" * 60)
    
    # Compter toutes les notifications
    total_notifications = Notification.objects.count()
    test_notifications = Notification.objects.filter(title__contains='Test Notification').count()
    unread_notifications = Notification.objects.filter(is_read=False).count()
    
    print(f"📊 Statistiques:")
    print(f"   📧 Total notifications: {total_notifications}")
    print(f"   🧪 Notifications de test: {test_notifications}")
    print(f"   📬 Non lues: {unread_notifications}")
    
    # Afficher les notifications par utilisateur
    real_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    print(f"\n👥 Notifications par utilisateur:")
    for email in real_emails:
        user = User.objects.filter(email=email).first()
        if user:
            user_notifications = Notification.objects.filter(user=user).count()
            user_unread = Notification.objects.filter(user=user, is_read=False).count()
            print(f"   📧 {email}: {user_notifications} total, {user_unread} non lues")
        else:
            print(f"   ⚠️  {email}: Utilisateur non trouvé")
    
    # Notifications globales
    global_notifications = Notification.objects.filter(user=None).count()
    print(f"   🌐 Notifications globales: {global_notifications}")


def main():
    """Fonction principale."""
    print("🚀 SCRIPT DE CRÉATION DE NOTIFICATIONS DE TEST")
    print("=" * 80)
    print("Ce script crée des notifications de test pour tester le système")
    print("=" * 80)
    
    try:
        # Créer les notifications
        notifications_count = create_test_notifications()
        
        # Vérifier les notifications
        verify_notifications()
        
        print(f"\n" + "=" * 80)
        print("🎉 NOTIFICATIONS DE TEST CRÉÉES AVEC SUCCÈS !")
        print("=" * 80)
        print(f"✅ {notifications_count} notifications créées")
        print(f"🧪 Vous pouvez maintenant tester :")
        print(f"   • Page de debug: http://localhost:5173/debug/auth")
        print(f"   • Notifications: http://localhost:5173/account/notifications")
        print(f"   • Cloche de notification dans la navbar")
        print(f"\n🔧 Pour tester l'API directement :")
        print(f"   • GET /api/v1/notifications/notifications/")
        print(f"   • GET /api/v1/notifications/notifications/unread/")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
