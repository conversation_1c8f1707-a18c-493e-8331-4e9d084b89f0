from django.contrib import admin
from .models_extension import ReturnRequest, ReturnI<PERSON>, <PERSON>laim, ClaimMessage, ClaimAttachment, ClaimStatusHistory

class ReturnItemInline(admin.TabularInline):
    model = ReturnItem
    extra = 0
    readonly_fields = ['created_at']

@admin.register(ReturnRequest)
class ReturnRequestAdmin(admin.ModelAdmin):
    list_display = ['return_number', 'order', 'user', 'status', 'reason', 'created_at']
    list_filter = ['status', 'reason', 'created_at']
    search_fields = ['return_number', 'order__order_number', 'user__email', 'user__first_name', 'user__last_name']
    readonly_fields = ['return_number', 'created_at', 'updated_at']
    inlines = [ReturnItemInline]
    date_hierarchy = 'created_at'

class ClaimMessageInline(admin.TabularInline):
    model = ClaimMessage
    extra = 0
    readonly_fields = ['created_at']

class ClaimStatusHistoryInline(admin.TabularInline):
    model = ClaimStatusHistory
    extra = 0
    readonly_fields = ['created_at']

@admin.register(Claim)
class ClaimAdmin(admin.ModelAdmin):
    list_display = ['claim_number', 'user', 'subject', 'status', 'created_at', 'last_update']
    list_filter = ['status', 'subject', 'created_at']
    search_fields = ['claim_number', 'user__email', 'user__first_name', 'user__last_name', 'description']
    readonly_fields = ['claim_number', 'created_at', 'updated_at', 'last_update']
    inlines = [ClaimMessageInline, ClaimStatusHistoryInline]
    date_hierarchy = 'created_at'

class ClaimAttachmentInline(admin.TabularInline):
    model = ClaimAttachment
    extra = 0
    readonly_fields = ['created_at']

@admin.register(ClaimMessage)
class ClaimMessageAdmin(admin.ModelAdmin):
    list_display = ['claim', 'sender', 'is_read', 'created_at']
    list_filter = ['sender', 'is_read', 'created_at']
    search_fields = ['claim__claim_number', 'message']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [ClaimAttachmentInline]
    date_hierarchy = 'created_at'
