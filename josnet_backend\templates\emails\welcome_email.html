<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bienvenue chez JosNet Network !</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .welcome-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .welcome-message {
            font-size: 16px;
            color: #555;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .features {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .features h3 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-size: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        
        .cta-section {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: transform 0.2s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            text-align: center;
        }
        
        .stat {
            flex: 1;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            margin: 10px 0;
            color: #666;
            font-size: 14px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .unsubscribe {
            margin-top: 20px;
            font-size: 12px;
            color: #999;
        }
        
        .unsubscribe a {
            color: #999;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                box-shadow: none;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .stats {
                flex-direction: column;
                gap: 20px;
            }
            
            .welcome-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚀 JosNet Network</div>
            <p>Votre marketplace tech de confiance</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <h1 class="welcome-title">🎉 Bienvenue {{ user_name }} !</h1>
            
            <p class="welcome-message">
                Félicitations ! Votre compte JosNet Network a été créé avec succès. 
                Nous sommes ravis de vous accueillir dans notre communauté de passionnés de technologie.
            </p>
            
            <!-- Stats -->
            <div class="stats">
                <div class="stat">
                    <span class="stat-number">10,000+</span>
                    <div class="stat-label">Produits tech</div>
                </div>
                <div class="stat">
                    <span class="stat-number">50,000+</span>
                    <div class="stat-label">Clients satisfaits</div>
                </div>
                <div class="stat">
                    <span class="stat-number">24/7</span>
                    <div class="stat-label">Support client</div>
                </div>
            </div>
            
            <!-- Features -->
            <div class="features">
                <h3>🎁 Ce qui vous attend chez JosNet :</h3>
                <ul class="feature-list">
                    <li>
                        <span class="feature-icon">📱</span>
                        <span>Les derniers smartphones, laptops et gadgets tech</span>
                    </li>
                    <li>
                        <span class="feature-icon">💰</span>
                        <span>Prix compétitifs et promotions exclusives</span>
                    </li>
                    <li>
                        <span class="feature-icon">🚚</span>
                        <span>Livraison rapide et sécurisée</span>
                    </li>
                    <li>
                        <span class="feature-icon">🔒</span>
                        <span>Paiements 100% sécurisés</span>
                    </li>
                    <li>
                        <span class="feature-icon">⭐</span>
                        <span>Programme de fidélité avec points bonus</span>
                    </li>
                    <li>
                        <span class="feature-icon">📧</span>
                        <span>Notifications des nouvelles offres et produits</span>
                    </li>
                </ul>
            </div>
            
            <!-- CTA -->
            <div class="cta-section">
                <p style="margin-bottom: 20px; color: #555;">
                    Prêt à découvrir nos produits exceptionnels ?
                </p>
                <a href="{{ site_url }}" class="cta-button">
                    🛍️ Commencer mes achats
                </a>
            </div>
            
            <!-- Account Info -->
            <div style="background-color: #e3f2fd; border-radius: 10px; padding: 20px; margin: 30px 0;">
                <h4 style="margin-top: 0; color: #1976d2;">📋 Informations de votre compte :</h4>
                <p style="margin: 5px 0;"><strong>Email :</strong> {{ user_email }}</p>
                <p style="margin: 5px 0;"><strong>Nom :</strong> {{ user_name }}</p>
                <p style="margin: 5px 0;"><strong>Date d'inscription :</strong> {{ signup_date }}</p>
                <p style="margin: 5px 0;"><strong>Points de fidélité :</strong> {{ loyalty_points }} points 🎁</p>
            </div>
            
            <!-- Next Steps -->
            <div style="border-left: 4px solid #667eea; padding-left: 20px; margin: 30px 0;">
                <h4 style="color: #333; margin-top: 0;">🎯 Prochaines étapes :</h4>
                <ol style="color: #555; padding-left: 20px;">
                    <li>Explorez notre catalogue de produits tech</li>
                    <li>Configurez vos préférences de notification</li>
                    <li>Ajoutez vos produits favoris à votre wishlist</li>
                    <li>Profitez de nos offres de bienvenue exclusives</li>
                </ol>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>Besoin d'aide ?</strong></p>
            <p>Notre équipe support est là pour vous aider 24/7</p>
            
            <div class="social-links">
                <a href="{{ site_url }}/contact">📞 Contact</a>
                <a href="{{ site_url }}/help">❓ Aide</a>
                <a href="{{ site_url }}/account">👤 Mon compte</a>
            </div>
            
            <p style="margin-top: 30px;">
                <strong>Cordialement,</strong><br>
                L'équipe JosNet Network 🚀
            </p>
            
            <div class="unsubscribe">
                <p>
                    Vous recevez cet email car vous venez de créer un compte sur JosNet Network.<br>
                    <a href="{{ site_url }}/account/notifications">Gérer mes préférences email</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
