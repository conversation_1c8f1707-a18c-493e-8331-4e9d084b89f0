import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Bell, Tag, ShoppingBag, Info } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Card } from '@/components/ui/card';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import notificationApi, { Notification } from '@/services/notificationApi';
import { useAuth } from '@/contexts/AuthContext';

const NotificationBell: React.FC = () => {
  const [open, setOpen] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  // Récupérer les notifications non lues
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['unreadNotifications'],
    queryFn: () => notificationApi.getUnreadNotifications(),
    enabled: isAuthenticated && !!user, // Double vérification
    refetchInterval: isAuthenticated ? 60000 : false, // Rafraîchir seulement si authentifié
    staleTime: 30000, // Considérer comme périmé après 30 secondes
  });

  // Marquer une notification comme lue et naviguer vers le lien si présent
  const handleNotificationClick = async (notification: Notification) => {
    await notificationApi.markAsRead(notification.id);
    refetch();
    
    if (notification.link) {
      navigate(notification.link);
    }
    
    setOpen(false);
  };

  // Marquer toutes les notifications comme lues
  const handleMarkAllAsRead = async () => {
    await notificationApi.markAllAsRead();
    refetch();
  };

  // Obtenir l'icône en fonction du type de notification
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'promotion':
        return <Tag className="h-4 w-4 text-green-500" />;
      case 'order':
        return <ShoppingBag className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: fr });
    } catch (error) {
      return 'Date inconnue';
    }
  };

  // Si l'utilisateur n'est pas authentifié, ne pas afficher la cloche
  if (!isAuthenticated) {
    return null;
  }

  const unreadCount = data?.count || 0;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-medium">Notifications</h3>
          {unreadCount > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleMarkAllAsRead}
              className="text-xs"
            >
              Tout marquer comme lu
            </Button>
          )}
        </div>
        
        <ScrollArea className="h-[300px]">
          {isLoading ? (
            <div className="p-4 text-center text-sm text-gray-500">
              Chargement...
            </div>
          ) : data?.results.length === 0 ? (
            <div className="p-4 text-center text-sm text-gray-500">
              Aucune notification non lue
            </div>
          ) : (
            <div className="divide-y">
              {data?.results.map((notification) => (
                <Card 
                  key={notification.id} 
                  className="p-3 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex gap-3">
                    <div className="mt-0.5">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium text-sm">{notification.title}</h4>
                        <span className="text-xs text-gray-500">
                          {formatDate(notification.created_at)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
        
        <div className="p-3 border-t text-center">
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-xs w-full"
            onClick={() => {
              navigate('/account/notifications');
              setOpen(false);
            }}
          >
            Voir toutes les notifications
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default NotificationBell;
