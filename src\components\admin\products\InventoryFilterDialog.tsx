import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { productApi } from '@/services/productApi';

interface InventoryFilterDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  filters: {
    status: string;
    category: string;
    lowStock: boolean;
  };
  onApplyFilters: (filters: any) => void;
}

const InventoryFilterDialog: React.FC<InventoryFilterDialogProps> = ({
  isOpen,
  onOpenChange,
  filters,
  onApplyFilters
}) => {
  const [localFilters, setLocalFilters] = useState(filters);

  // Reset local filters when the dialog opens
  useEffect(() => {
    if (isOpen) {
      setLocalFilters(filters);
    }
  }, [isOpen, filters]);

  // Fetch categories for the filter
  const { data: categoriesData, isLoading: isLoadingCategories } = useQuery({
    queryKey: ['categories'],
    queryFn: () => productApi.getCategories(),
    retry: 1,
    retryDelay: 1000,
  });

  // Ensure categories is always an array
  const categories = Array.isArray(categoriesData) ? categoriesData : [];

  const handleApplyFilters = () => {
    onApplyFilters(localFilters);
    onOpenChange(false);
  };

  const handleResetFilters = () => {
    const resetFilters = {
      status: 'all',
      category: 'all',
      lowStock: false
    };
    setLocalFilters(resetFilters);
    onApplyFilters(resetFilters);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Filtrer l'inventaire</DialogTitle>
          <DialogDescription>
            Appliquez des filtres pour affiner la liste des produits en stock.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Statut
            </Label>
            <Select
              value={localFilters.status}
              onValueChange={(value) => setLocalFilters({...localFilters, status: value})}
            >
              <SelectTrigger id="status" className="col-span-3">
                <SelectValue placeholder="Sélectionner un statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les produits</SelectItem>
                <SelectItem value="in_stock">En stock</SelectItem>
                <SelectItem value="low_stock">Stock bas</SelectItem>
                <SelectItem value="out_of_stock">Rupture de stock</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category" className="text-right">
              Catégorie
            </Label>
            <Select
              value={localFilters.category}
              onValueChange={(value) => setLocalFilters({...localFilters, category: value})}
            >
              <SelectTrigger id="category" className="col-span-3">
                <SelectValue placeholder="Sélectionner une catégorie" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les catégories</SelectItem>
                {isLoadingCategories ? (
                  <SelectItem value="loading" disabled>Chargement des catégories...</SelectItem>
                ) : categories.length === 0 ? (
                  <SelectItem value="empty" disabled>Aucune catégorie disponible</SelectItem>
                ) : (
                  categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="col-span-4 flex items-center space-x-2">
              <Checkbox
                id="lowStock"
                checked={localFilters.lowStock}
                onCheckedChange={(checked) =>
                  setLocalFilters({...localFilters, lowStock: checked === true})
                }
              />
              <Label htmlFor="lowStock">
                Afficher uniquement les produits en stock bas
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleResetFilters}>
            Réinitialiser
          </Button>
          <Button onClick={handleApplyFilters}>
            Appliquer les filtres
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryFilterDialog;
