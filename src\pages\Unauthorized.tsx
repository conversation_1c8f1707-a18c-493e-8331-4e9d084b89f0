import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ShieldX, 
  ArrowLeft, 
  Home, 
  AlertTriangle, 
  Lock,
  User,
  Settings,
  Eye
} from 'lucide-react';

const Unauthorized: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    if (user?.role === 'admin' || user?.role === 'staff') {
      navigate('/admin/dashboard');
    } else if (user?.role === 'customer') {
      navigate('/account');
    } else {
      navigate('/');
    }
  };

  const getRestrictedFeatures = () => {
    if (user?.role === 'staff') {
      return [
        {
          icon: <User className="h-5 w-5" />,
          title: "Gestion des Utilisateurs",
          description: "Création, modification et suppression des comptes utilisateurs",
          reason: "Sécurité et confidentialité des données personnelles"
        },
        {
          icon: <Settings className="h-5 w-5" />,
          title: "Paramètres des Messages",
          description: "Configuration des systèmes de messagerie et notifications",
          reason: "Configuration système critique"
        },
        {
          icon: <Eye className="h-5 w-5" />,
          title: "Logs Système",
          description: "Consultation des journaux d'activité du système",
          reason: "Informations sensibles de sécurité"
        },
        {
          icon: <Lock className="h-5 w-5" />,
          title: "Méthodes de Paiement",
          description: "Configuration des passerelles de paiement",
          reason: "Sécurité financière et conformité"
        }
      ];
    }
    return [];
  };

  const getAccessibleFeatures = () => {
    if (user?.role === 'staff') {
      return [
        "Tableau de bord administrateur",
        "Gestion des produits et catégories",
        "Gestion des commandes et statuts",
        "Gestion de l'inventaire et stocks",
        "Gestion des promotions",
        "Messages et support client",
        "CMS et gestion de contenu",
        "Transactions et factures",
        "Retours et réclamations",
        "Statistiques et rapports"
      ];
    }
    return [];
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full space-y-6">
        {/* Header Card */}
        <Card className="border-red-200 bg-red-50">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <ShieldX className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl text-red-800 flex items-center justify-center gap-2">
              <AlertTriangle className="h-6 w-6" />
              Accès Non Autorisé
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-red-700 text-lg mb-4">
              Désolé, vous n'avez pas les permissions nécessaires pour accéder à cette page.
            </p>
            <div className="bg-white border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-center gap-2 text-sm text-gray-600 mb-2">
                <User className="h-4 w-4" />
                Utilisateur connecté
              </div>
              <div className="text-lg font-semibold text-gray-800">
                {user?.first_name} {user?.last_name}
              </div>
              <div className="text-sm text-gray-600">
                {user?.email}
              </div>
              <div className="mt-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  user?.role === 'staff' 
                    ? 'bg-blue-100 text-blue-800' 
                    : user?.role === 'admin'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  Rôle: {user?.role?.toUpperCase()}
                </span>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button 
                onClick={handleGoBack}
                variant="outline"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Retour
              </Button>
              <Button 
                onClick={handleGoHome}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
              >
                <Home className="h-4 w-4" />
                Tableau de bord
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Staff Restrictions */}
        {user?.role === 'staff' && (
          <div className="grid md:grid-cols-2 gap-6">
            {/* Restricted Features */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-red-600 flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  Fonctionnalités Restreintes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  En tant qu'utilisateur <strong>Staff</strong>, vous n'avez pas accès aux fonctionnalités suivantes :
                </p>
                <div className="space-y-3">
                  {getRestrictedFeatures().map((feature, index) => (
                    <div key={index} className="border border-red-100 rounded-lg p-3 bg-red-50">
                      <div className="flex items-start gap-3">
                        <div className="text-red-600 mt-0.5">
                          {feature.icon}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-red-800 text-sm">
                            {feature.title}
                          </h4>
                          <p className="text-xs text-red-600 mt-1">
                            {feature.description}
                          </p>
                          <p className="text-xs text-red-500 mt-1 italic">
                            Raison: {feature.reason}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Accessible Features */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-green-600 flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Vos Permissions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Vous avez accès à toutes ces fonctionnalités importantes :
                </p>
                <div className="space-y-2">
                  {getAccessibleFeatures().map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-xs text-green-700">
                    💡 <strong>Astuce:</strong> Ces permissions vous permettent de gérer efficacement 
                    les opérations quotidiennes de la plateforme e-commerce.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Help Section */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">
                Besoin d'un accès supplémentaire ?
              </h3>
              <p className="text-blue-700 text-sm mb-4">
                Si vous pensez avoir besoin d'accéder à cette fonctionnalité pour votre travail, 
                contactez votre administrateur système.
              </p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center text-xs text-blue-600">
                <span>📧 <EMAIL></span>
                <span className="hidden sm:inline">•</span>
                <span>📞 +257 12 345 678</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Unauthorized;
