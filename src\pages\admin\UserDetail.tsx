import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  ShoppingBag,
  Award,
  Edit,
  Trash2,
  ArrowLeft,
  CheckCircle,
  XCircle,
  RefreshCw,
  AlertCircle,
  Plus,
  Loader2,
  Key,
  Copy,
} from "lucide-react";
import adminUserApi, { AdminUser } from "@/services/adminUserApi";
import UserForm from "@/components/admin/users/UserForm";
import AddressManager from "@/components/account/AddressManager";
import LoyaltyDashboard from "@/components/account/LoyaltyDashboard";

const UserDetail = () => {
  const { id } = useParams<{ id: string }>();
  const userId = parseInt(id || "0");
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("profile");
  const [isUserFormOpen, setIsUserFormOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch user details
  const {
    data: user,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["adminUser", userId],
    queryFn: () => adminUserApi.getUser(userId),
    enabled: userId > 0,
  });

  // Fetch user orders
  const {
    data: orders,
    isLoading: isLoadingOrders,
  } = useQuery({
    queryKey: ["userOrders", userId],
    queryFn: () => adminUserApi.getUserOrders(userId),
    enabled: userId > 0,
  });

  // Verify user mutation
  const verifyUserMutation = useMutation({
    mutationFn: adminUserApi.verifyUser,
    onSuccess: () => {
      toast({
        title: "Email vérifié",
        description: "L'email de l'utilisateur a été marqué comme vérifié.",
      });
      refetch();
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de vérifier l'email de l'utilisateur.",
        variant: "destructive",
      });
    },
  });

  // Set user active status mutation
  const setUserActiveStatusMutation = useMutation({
    mutationFn: ({ userId, isActive }: { userId: number; isActive: boolean }) =>
      adminUserApi.setUserActiveStatus(userId, isActive),
    onSuccess: (data, variables) => {
      const { isActive } = variables;
      toast({
        title: isActive ? "Utilisateur activé" : "Utilisateur désactivé",
        description: isActive
          ? "L'utilisateur a été activé avec succès."
          : "L'utilisateur a été désactivé avec succès.",
      });
      refetch();
    },
    onError: (error, variables) => {
      const { isActive } = variables;
      toast({
        title: "Erreur",
        description: `Impossible de ${isActive ? 'activer' : 'désactiver'} l'utilisateur.`,
        variant: "destructive",
      });
    },
  });

  // Reset password mutation
  const resetPasswordMutation = useMutation({
    mutationFn: adminUserApi.resetUserPassword,
    onSuccess: (data) => {
      setNewPassword(data.password);
      setIsResetPasswordDialogOpen(true);
      toast({
        title: "Mot de passe réinitialisé",
        description: "Le mot de passe de l'utilisateur a été réinitialisé.",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de réinitialiser le mot de passe.",
        variant: "destructive",
      });
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: adminUserApi.deleteUser,
    onSuccess: () => {
      toast({
        title: "Utilisateur supprimé",
        description: "L'utilisateur a été supprimé avec succès.",
      });
      navigate("/admin/users");
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer l'utilisateur.",
        variant: "destructive",
      });
      setIsDeleting(false);
    },
  });

  // Handle user form submit
  const handleUserFormSubmit = () => {
    setIsUserFormOpen(false);
    refetch();
  };

  // Handle delete user
  const handleDeleteUser = () => {
    setIsDeleting(true);
    deleteUserMutation.mutate(userId);
  };

  // Handle copy password
  const handleCopyPassword = () => {
    navigator.clipboard.writeText(newPassword);
    toast({
      title: "Copié",
      description: "Le mot de passe a été copié dans le presse-papiers.",
    });
  };

  // Format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Non spécifié";
    return format(new Date(dateString), "dd MMMM yyyy", { locale: fr });
  };

  // Loading state
  if (isLoading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigate("/admin/users")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Skeleton className="h-8 w-64" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Skeleton className="h-64 rounded-lg" />
            <Skeleton className="h-64 rounded-lg md:col-span-2" />
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Error state
  if (isError || !user) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-[60vh] text-center">
          <AlertCircle className="h-16 w-16 text-destructive mb-4" />
          <h2 className="text-2xl font-bold mb-2">Utilisateur non trouvé</h2>
          <p className="text-muted-foreground mb-6">
            L'utilisateur que vous recherchez n'existe pas ou a été supprimé.
          </p>
          <Button onClick={() => navigate("/admin/users")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour à la liste des utilisateurs
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigate("/admin/users")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">
              {user.full_name || user.email}
            </h1>
            <Badge
              className={
                user.role === "admin"
                  ? "bg-purple-100 text-purple-800 hover:bg-purple-100"
                  : user.role === "staff"
                  ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                  : "bg-green-100 text-green-800 hover:bg-green-100"
              }
            >
              {user.role === "admin"
                ? "Administrateur"
                : user.role === "staff"
                ? "Personnel"
                : "Client"}
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsUserFormOpen(true)}
              className="flex items-center gap-1"
            >
              <Edit className="h-4 w-4" />
              Modifier
            </Button>
            <Button
              variant="destructive"
              className="flex items-center gap-1"
              onClick={() => setIsDeleting(true)}
            >
              <Trash2 className="h-4 w-4" />
              Supprimer
            </Button>
          </div>
        </div>

        {/* User info and tabs */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* User info card */}
          <Card>
            <CardHeader>
              <CardTitle>Informations utilisateur</CardTitle>
              <CardDescription>
                Détails du compte utilisateur
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-2">
                <Mail className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <Phone className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">Téléphone</p>
                  <p className="text-sm text-muted-foreground">
                    {user.phone_number || "Non spécifié"}
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">Date de naissance</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(user.date_of_birth)}
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">Statut du compte</p>
                  <div className="flex flex-col gap-1 mt-1">
                    <div className="flex items-center gap-1">
                      {user.is_active ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="text-sm">
                        {user.is_active ? "Actif" : "Inactif"}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      {user.is_verified ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="text-sm">
                        {user.is_verified ? "Email vérifié" : "Email non vérifié"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">Inscrit le</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(user.created_at)}
                  </p>
                </div>
              </div>

              <Separator />

              <div className="flex items-start gap-2">
                <Award className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <p className="font-medium">Points de fidélité</p>
                  <div className="flex items-center gap-1">
                    <span className="text-lg font-bold">{user.loyalty_points}</span>
                    <span className="text-sm text-muted-foreground">
                      (Niveau {user.loyalty_tier})
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <ShoppingBag className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Commandes</p>
                  <p className="text-lg font-bold">{user.order_count || 0}</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => resetPasswordMutation.mutate(userId)}
                disabled={resetPasswordMutation.isPending}
              >
                {resetPasswordMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Réinitialisation...
                  </>
                ) : (
                  <>
                    <Key className="h-4 w-4 mr-2" />
                    Réinitialiser le mot de passe
                  </>
                )}
              </Button>
              {!user.is_verified && (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => verifyUserMutation.mutate(userId)}
                  disabled={verifyUserMutation.isPending}
                >
                  {verifyUserMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Vérification...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Marquer comme vérifié
                    </>
                  )}
                </Button>
              )}
              <Button
                variant={user.is_active ? "destructive" : "default"}
                className="w-full"
                onClick={() =>
                  setUserActiveStatusMutation.mutate({
                    userId,
                    isActive: !user.is_active,
                  })
                }
                disabled={setUserActiveStatusMutation.isPending}
              >
                {setUserActiveStatusMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Mise à jour...
                  </>
                ) : user.is_active ? (
                  <>
                    <XCircle className="h-4 w-4 mr-2" />
                    Désactiver le compte
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Activer le compte
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>

          {/* Tabs */}
          <div className="md:col-span-2">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="profile">Profil</TabsTrigger>
                <TabsTrigger value="addresses">Adresses</TabsTrigger>
                <TabsTrigger value="orders">Commandes</TabsTrigger>
              </TabsList>
              <TabsContent value="profile" className="mt-6">
                <LoyaltyDashboard />
              </TabsContent>
              <TabsContent value="addresses" className="mt-6">
                <AddressManager />
              </TabsContent>
              <TabsContent value="orders" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Historique des commandes</CardTitle>
                    <CardDescription>
                      Toutes les commandes passées par cet utilisateur
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingOrders ? (
                      <div className="space-y-4">
                        {Array.from({ length: 3 }).map((_, index) => (
                          <Skeleton
                            key={index}
                            className="h-16 w-full rounded-md"
                          />
                        ))}
                      </div>
                    ) : orders?.results?.length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>N° Commande</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Total</TableHead>
                            <TableHead>Statut</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {orders.results.map((order: any) => (
                            <TableRow key={order.id}>
                              <TableCell className="font-medium">
                                {order.order_number}
                              </TableCell>
                              <TableCell>
                                {formatDate(order.created_at)}
                              </TableCell>
                              <TableCell>
                                {new Intl.NumberFormat("fr-FR", {
                                  style: "currency",
                                  currency: "BIF",
                                  maximumFractionDigits: 0
                                }).format(order.total)}
                              </TableCell>
                              <TableCell>
                                <Badge
                                  className={
                                    order.status === "delivered"
                                      ? "bg-green-100 text-green-800 hover:bg-green-100"
                                      : order.status === "processing"
                                      ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                                      : order.status === "cancelled"
                                      ? "bg-red-100 text-red-800 hover:bg-red-100"
                                      : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                                  }
                                >
                                  {order.status_display || order.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <Link to={`/admin/orders/${order.id}`}>
                                  <Button variant="outline" size="sm">
                                    Détails
                                  </Button>
                                </Link>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <ShoppingBag className="h-12 w-12 mx-auto mb-4 opacity-20" />
                        <p>Cet utilisateur n'a pas encore passé de commande.</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* User Form Dialog */}
        <Dialog open={isUserFormOpen} onOpenChange={setIsUserFormOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Modifier l'utilisateur</DialogTitle>
              <DialogDescription>
                Modifiez les informations de l'utilisateur ci-dessous.
              </DialogDescription>
            </DialogHeader>
            <UserForm
              user={user}
              onSubmit={handleUserFormSubmit}
              onCancel={() => setIsUserFormOpen(false)}
            />
          </DialogContent>
        </Dialog>

        {/* Reset Password Dialog */}
        <Dialog
          open={isResetPasswordDialogOpen}
          onOpenChange={setIsResetPasswordDialogOpen}
        >
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Mot de passe réinitialisé</DialogTitle>
              <DialogDescription>
                Le mot de passe de l'utilisateur a été réinitialisé. Veuillez
                copier le nouveau mot de passe ci-dessous.
              </DialogDescription>
            </DialogHeader>
            <div className="flex items-center gap-2 mt-4">
              <Input
                value={newPassword}
                readOnly
                className="font-mono"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={handleCopyPassword}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <DialogFooter className="mt-4">
              <Button
                onClick={() => setIsResetPasswordDialogOpen(false)}
              >
                Fermer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleting} onOpenChange={(open) => !open && setIsDeleting(false)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                Êtes-vous sûr de vouloir supprimer cet utilisateur ?
              </AlertDialogTitle>
              <AlertDialogDescription>
                Cette action est irréversible. Toutes les données associées à
                cet utilisateur seront supprimées.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={deleteUserMutation.isPending} onClick={() => setIsDeleting(false)}>
                Annuler
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteUser}
                disabled={deleteUserMutation.isPending}
                className="bg-red-600 hover:bg-red-700"
              >
                {deleteUserMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Suppression...
                  </>
                ) : (
                  "Supprimer"
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AdminLayout>
  );
};

export default UserDetail;
