#!/usr/bin/env python3
"""
Script pour tester l'API des transactions utilisateur
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from orders.models import Order
from payments.models import Transaction, PaymentMethod

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    print("🔐 Authentification...")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access')
            print(f"✅ Authentification réussie")
            return token
        else:
            print(f"❌ Échec de l'authentification: {response.status_code}")
            print(f"Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return None

def test_transactions_api(token):
    """Tester l'API des transactions"""
    print("\n💳 Test de l'API des transactions...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test 1: Récupérer toutes les transactions
    print("\n1. Test de récupération de toutes les transactions...")
    try:
        response = requests.get(f"{API_BASE_URL}/payments/transactions/", headers=headers)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Données reçues")
            print(f"   Type: {type(data)}")
            
            if isinstance(data, dict) and 'results' in data:
                transactions = data['results']
                print(f"   📦 Données paginées - {len(transactions)} transaction(s)")
                print(f"   📈 Total: {data.get('count', 'N/A')}")
            elif isinstance(data, list):
                transactions = data
                print(f"   📦 Données directes - {len(transactions)} transaction(s)")
            else:
                print(f"   ⚠️ Format inattendu: {type(data)}")
                transactions = []
            
            # Afficher les détails des transactions
            for i, txn in enumerate(transactions[:3], 1):  # Afficher seulement les 3 premières
                print(f"\n   💳 Transaction {i}:")
                print(f"      ID: {txn.get('id')}")
                print(f"      Transaction ID: {txn.get('transaction_id', 'N/A')}")
                print(f"      Montant: {txn.get('amount')} {txn.get('currency', 'BIF')}")
                print(f"      Statut: {txn.get('status')}")
                print(f"      Commande: {txn.get('order')}")
                print(f"      Date: {txn.get('created_at')}")
                
            return transactions
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return []
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return []

def test_my_transactions_api(token):
    """Tester l'API des transactions utilisateur spécifiques"""
    print("\n👤 Test de l'API des transactions utilisateur...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/payments/transactions/my_transactions/", headers=headers)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Données reçues")
            
            if isinstance(data, dict) and 'results' in data:
                transactions = data['results']
                print(f"   📦 Données paginées - {len(transactions)} transaction(s)")
            elif isinstance(data, list):
                transactions = data
                print(f"   📦 Données directes - {len(transactions)} transaction(s)")
            else:
                transactions = []
            
            return transactions
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return []
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return []

def create_test_transactions():
    """Créer des transactions de test dans la base de données"""
    print("\n🔧 Création de transactions de test...")
    
    try:
        # Récupérer l'utilisateur de test
        user = User.objects.get(email=TEST_EMAIL)
        print(f"   👤 Utilisateur: {user.email}")
        
        # Vérifier s'il y a déjà des commandes
        existing_orders = Order.objects.filter(user=user).count()
        print(f"   📦 Commandes existantes: {existing_orders}")
        
        if existing_orders == 0:
            print("   ⚠️ Aucune commande trouvée. Création d'une commande de test...")
            
            # Créer une commande de test
            order = Order.objects.create(
                user=user,
                status='completed',
                total_amount=50000,  # 500 BIF
                currency='BIF'
            )
            print(f"   ✅ Commande créée (ID: {order.id})")
        else:
            order = Order.objects.filter(user=user).first()
            print(f"   ✅ Utilisation de la commande existante (ID: {order.id})")
        
        # Vérifier s'il y a déjà des transactions pour cet utilisateur
        existing_transactions = Transaction.objects.filter(order__user=user).count()
        print(f"   💳 Transactions existantes: {existing_transactions}")
        
        if existing_transactions == 0:
            print("   ⚠️ Aucune transaction trouvée. Création de transactions de test...")

            # Créer ou récupérer des méthodes de paiement
            payment_methods = []
            payment_method_data = [
                {'name': 'Carte de crédit', 'provider': 'stripe'},
                {'name': 'Mobile Money', 'provider': 'mobile_money'},
                {'name': 'Virement bancaire', 'provider': 'bank_transfer'}
            ]

            for pm_data in payment_method_data:
                pm, created = PaymentMethod.objects.get_or_create(
                    name=pm_data['name'],
                    defaults={'provider': pm_data['provider'], 'is_active': True}
                )
                payment_methods.append(pm)
                if created:
                    print(f"   ✅ Méthode de paiement créée: {pm.name}")

            # Créer des transactions de test avec méthodes de paiement
            transactions_data = [
                {
                    'transaction_id': 'TXN-TEST-001',
                    'amount': 25000,
                    'status': 'completed',
                    'currency': 'BIF',
                    'payment_method': payment_methods[0]
                },
                {
                    'transaction_id': 'TXN-TEST-002',
                    'amount': 15000,
                    'status': 'pending',
                    'currency': 'BIF',
                    'payment_method': payment_methods[1]
                },
                {
                    'transaction_id': 'TXN-TEST-003',
                    'amount': 10000,
                    'status': 'failed',
                    'currency': 'BIF',
                    'payment_method': payment_methods[2]
                }
            ]

            for txn_data in transactions_data:
                transaction = Transaction.objects.create(
                    order=order,
                    **txn_data
                )
                print(f"   ✅ Transaction créée: {transaction.transaction_id} - {transaction.amount} {transaction.currency} - {transaction.payment_method.name}")
        else:
            print("   ✅ Transactions existantes trouvées")
        
        # Compter le total final
        final_count = Transaction.objects.filter(order__user=user).count()
        print(f"   📊 Total des transactions pour {user.email}: {final_count}")
        
        return final_count > 0
        
    except Exception as e:
        print(f"   ❌ Erreur lors de la création: {e}")
        return False

def main():
    print("🧪 TEST DE L'API DES TRANSACTIONS")
    print("=" * 50)
    
    # 1. Créer des transactions de test si nécessaire
    test_data_created = create_test_transactions()
    
    # 2. Authentification
    token = get_auth_token()
    if not token:
        print("❌ Impossible de continuer sans authentification")
        return
    
    # 3. Tester l'API des transactions générales
    all_transactions = test_transactions_api(token)
    
    # 4. Tester l'API des transactions utilisateur
    my_transactions = test_my_transactions_api(token)
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Données de test créées: {'✅' if test_data_created else '❌'}")
    print(f"   API transactions générales: {'✅' if all_transactions else '❌'}")
    print(f"   API mes transactions: {'✅' if my_transactions else '❌'}")
    print(f"   Transactions trouvées: {len(my_transactions) if my_transactions else 0}")
    
    if all_transactions and my_transactions:
        print(f"\n🎉 TESTS RÉUSSIS!")
        print(f"   L'API des transactions fonctionne correctement.")
        print(f"   Allez sur: http://localhost:8080/account/transactions")
    else:
        print(f"\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
