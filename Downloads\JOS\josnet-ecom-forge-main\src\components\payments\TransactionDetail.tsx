import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  RefreshCw, 
  FileText, 
  RotateCcw,
  CreditCard,
  ShoppingCart,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import paymentApi, { TransactionDetail as ITransactionDetail } from '@/services/paymentApi';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate, formatCurrency } from '@/utils/formatters';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Refund form schema
const refundFormSchema = z.object({
  amount: z.number().positive({ message: "Amount must be positive" }),
  reason: z.string().min(3, { message: "Reason must be at least 3 characters" }),
});

type RefundFormValues = z.infer<typeof refundFormSchema>;

const TransactionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [transaction, setTransaction] = useState<ITransactionDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refundDialogOpen, setRefundDialogOpen] = useState(false);
  
  const navigate = useNavigate();
  const { toast } = useToast();

  const form = useForm<RefundFormValues>({
    resolver: zodResolver(refundFormSchema),
    defaultValues: {
      amount: 0,
      reason: "",
    },
  });

  useEffect(() => {
    if (id) {
      fetchTransaction();
    }
  }, [id]);

  const fetchTransaction = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const data = await paymentApi.getTransaction(Number(id));
      setTransaction(data);
      
      // Set default refund amount
      form.setValue('amount', data.amount);
      
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch transaction');
      toast({
        title: "Error",
        description: "Failed to fetch transaction",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefund = async (values: RefundFormValues) => {
    if (!id) return;
    
    try {
      await paymentApi.refundTransaction(Number(id), {
        amount: values.amount,
        reason: values.reason,
      });
      
      toast({
        title: "Success",
        description: "Refund processed successfully",
      });
      
      setRefundDialogOpen(false);
      fetchTransaction();
    } catch (err: any) {
      toast({
        title: "Error",
        description: err.response?.data?.detail || "Failed to process refund",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'pending':
        return <AlertCircle className="h-6 w-6 text-yellow-500" />;
      case 'processing':
        return <RefreshCw className="h-6 w-6 text-blue-500" />;
      case 'failed':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'refunded':
        return <RotateCcw className="h-6 w-6 text-purple-500" />;
      case 'partially_refunded':
        return <RotateCcw className="h-6 w-6 text-indigo-500" />;
      case 'cancelled':
        return <XCircle className="h-6 w-6 text-gray-500" />;
      default:
        return <AlertCircle className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'processing':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Processing</Badge>;
      case 'failed':
        return <Badge variant="destructive" className="bg-red-100 text-red-800">Failed</Badge>;
      case 'refunded':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800">Refunded</Badge>;
      case 'partially_refunded':
        return <Badge variant="outline" className="bg-indigo-100 text-indigo-800">Partially Refunded</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !transaction) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-4">
            <p className="text-red-500">{error || 'Transaction not found'}</p>
            <Button onClick={fetchTransaction} className="mt-2">Retry</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Transaction #{transaction.id}</CardTitle>
          <CardDescription>
            {transaction.transaction_id}
          </CardDescription>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate('/admin/transactions')} className="flex items-center gap-1">
            <ArrowLeft size={16} />
            Back to List
          </Button>
          <Button variant="outline" onClick={fetchTransaction} className="flex items-center gap-1">
            <RefreshCw size={16} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Transaction Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(transaction.status)}
                    <span className="font-medium">Status</span>
                  </div>
                  <div>
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">Payment Method</span>
                  </div>
                  <div>
                    {transaction.payment_method_name}
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">Created</span>
                  </div>
                  <div>
                    {formatDate(transaction.created_at)}
                  </div>
                </div>
                
                {transaction.completed_at && (
                  <>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-gray-500" />
                        <span className="font-medium">Completed</span>
                      </div>
                      <div>
                        {formatDate(transaction.completed_at)}
                      </div>
                    </div>
                  </>
                )}
                
                <Separator />
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">Amount</span>
                  </div>
                  <div className="font-bold">
                    {formatCurrency(transaction.amount)} {transaction.currency}
                  </div>
                </div>
                
                {transaction.error_message && (
                  <>
                    <Separator />
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <AlertCircle className="h-5 w-5 text-red-500" />
                        <span className="font-medium">Error</span>
                      </div>
                      <div className="text-red-500 text-sm mt-1 p-2 bg-red-50 rounded">
                        {transaction.error_message}
                      </div>
                    </div>
                  </>
                )}
              </div>
              
              {transaction.status === 'completed' && (
                <div className="mt-6">
                  <Dialog open={refundDialogOpen} onOpenChange={setRefundDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="w-full flex items-center gap-1">
                        <RotateCcw size={16} />
                        Process Refund
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Process Refund</DialogTitle>
                        <DialogDescription>
                          Enter the amount to refund and a reason for the refund.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <Form {...form}>
                        <form onSubmit={form.handleSubmit(handleRefund)} className="space-y-4">
                          <FormField
                            control={form.control}
                            name="amount"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Amount</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="number" 
                                    step="0.01" 
                                    min="0.01" 
                                    max={transaction.amount} 
                                    {...field}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Maximum amount: {formatCurrency(transaction.amount)}
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="reason"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Reason</FormLabel>
                                <FormControl>
                                  <Textarea 
                                    placeholder="Customer requested a refund" 
                                    {...field} 
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setRefundDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button type="submit">Process Refund</Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Order Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">Order ID</span>
                  </div>
                  <div>
                    <Button 
                      variant="link" 
                      onClick={() => navigate(`/admin/orders/${transaction.order}`)}
                      className="p-0 h-auto"
                    >
                      #{transaction.order}
                    </Button>
                  </div>
                </div>
                
                {transaction.order_details && (
                  <>
                    <Separator />
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Customer</span>
                      </div>
                      <div>
                        {transaction.order_details.user_email || 'Guest'}
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Order Status</span>
                      </div>
                      <div>
                        <Badge variant="outline">{transaction.order_details.status}</Badge>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Order Total</span>
                      </div>
                      <div className="font-bold">
                        {formatCurrency(transaction.order_details.total)}
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Order Date</span>
                      </div>
                      <div>
                        {formatDate(transaction.order_details.created_at)}
                      </div>
                    </div>
                  </>
                )}
              </div>
              
              <div className="mt-6">
                <Button 
                  variant="outline" 
                  className="w-full flex items-center gap-1"
                  onClick={() => navigate(`/admin/orders/${transaction.order}`)}
                >
                  <ShoppingCart size={16} />
                  View Order Details
                </Button>
              </div>
              
              <div className="mt-2">
                <Button 
                  variant="outline" 
                  className="w-full flex items-center gap-1"
                  onClick={() => navigate(`/admin/invoices/generate/${transaction.order}`)}
                >
                  <FileText size={16} />
                  Generate Invoice
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionDetail;
