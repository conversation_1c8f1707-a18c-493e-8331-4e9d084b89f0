"""
URLs pour les APIs du core.
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'site-settings', views.SiteSettingsViewSet)
router.register(r'homepage-stats', views.HomePageStatsViewSet)
router.register(r'services', views.ServiceViewSet)
router.register(r'testimonials', views.TestimonialViewSet)
router.register(r'newsletter', views.NewsletterSubscriberViewSet)
router.register(r'contact', views.ContactMessageViewSet)
router.register(r'faq', views.FAQViewSet)
router.register(r'banners', views.BannerViewSet)
router.register(r'newsletter-subscriptions', views.NewsletterSubscriptionViewSet, basename='newsletter-subscription')

urlpatterns = [
    # APIs ViewSets
    path('', include(router.urls)),
    
    # API consolidée pour la page d'accueil
    path('homepage-data/', views.homepage_data, name='homepage-data'),

    # APIs Newsletter
    path('newsletter/subscribe/', views.newsletter_subscribe, name='newsletter-subscribe'),
    path('newsletter/unsubscribe/', views.newsletter_unsubscribe, name='newsletter-unsubscribe'),
    path('newsletter/reactivate/', views.newsletter_reactivate, name='newsletter-reactivate'),
    path('newsletter/status/', views.newsletter_status, name='newsletter-status'),
    path('newsletter/stats/', views.newsletter_stats, name='newsletter-stats'),
    path('newsletter/send/', views.send_newsletter, name='newsletter-send'),
]
