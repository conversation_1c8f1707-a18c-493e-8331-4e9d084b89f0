from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Count, Q
from orders.models import Order
from authentication.models import User
from products.models import Product
from .models import HomePageContent, HomePageStats, HomePageService, HomePageTestimonial, HomePageFeature
from .serializers import HomePageDataSerializer, HomePageStatsSerializer, HomePageServiceSerializer, HomePageTestimonialSerializer, HomePageFeatureSerializer, HomePageContentSerializer
import logging

logger = logging.getLogger(__name__)

class HomeStatsView(APIView):
    """
    API endpoint for home page statistics.
    """
    permission_classes = []  # Public endpoint

    def get(self, request):
        """
        Get real statistics from the database for the home page.
        """
        try:
            # Count total customers (all registered users)
            total_customers = User.objects.filter(
                is_active=True
            ).count()

            # Count customers who have made orders
            customers_with_orders = User.objects.filter(
                orders__isnull=False
            ).distinct().count()

            # Count all projects (all orders regardless of status)
            total_projects = Order.objects.count()

            # Count completed projects (delivered orders)
            completed_projects = Order.objects.filter(
                status='delivered'
            ).count()

            # Years of experience (hardcoded but can be calculated from company founding date)
            years_experience = 10

            # Total products in database
            total_products = Product.objects.filter(
                status='published'
            ).count()

            # Total products including drafts
            all_products = Product.objects.count()

            # Return real data from database
            stats = {
                'total_customers': total_customers,
                'total_projects': total_projects,
                'years_experience': years_experience,
                'total_products': total_products,
                # Additional detailed stats for debugging/admin
                'detailed_stats': {
                    'customers_with_orders': customers_with_orders,
                    'completed_projects': completed_projects,
                    'all_products_including_drafts': all_products,
                    'published_products': total_products
                }
            }

            logger.info(f"Real home stats retrieved: {stats}")

            return Response(stats, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error retrieving home stats: {str(e)}")

            # Return minimal fallback stats in case of error
            return Response({
                'total_customers': 0,
                'total_projects': 0,
                'years_experience': 10,
                'total_products': 0,
                'error': 'Unable to fetch real statistics'
            }, status=status.HTTP_200_OK)


class HomePageDataView(APIView):
    """
    API endpoint for complete home page data including content, stats, services, etc.
    """
    permission_classes = []  # Public endpoint

    def get(self, request):
        """
        Get all dynamic content for the home page.
        """
        try:
            # Get content sections
            content = HomePageContent.objects.filter(is_active=True).order_by('order', 'section')

            # Get statistics with auto-update
            stats = HomePageStats.objects.filter(is_active=True).order_by('order', 'name')

            # Update auto-calculated stats
            for stat in stats:
                if stat.auto_update:
                    if stat.name == 'total_customers':
                        stat.value = User.objects.filter(is_active=True).count()
                    elif stat.name == 'total_projects':
                        stat.value = Order.objects.count()
                    elif stat.name == 'completed_projects':
                        stat.value = Order.objects.filter(status='delivered').count()
                    elif stat.name == 'total_products':
                        stat.value = Product.objects.filter(status='published').count()
                    stat.save()

            # Get services
            services = HomePageService.objects.filter(is_active=True).order_by('order', 'name')

            # Get testimonials
            testimonials = HomePageTestimonial.objects.filter(is_active=True).order_by('order', '-created_at')

            # Get features
            features = HomePageFeature.objects.filter(is_active=True).order_by('order', 'title')

            # Serialize data
            data = {
                'content': HomePageContentSerializer(content, many=True).data,
                'stats': HomePageStatsSerializer(stats, many=True).data,
                'services': HomePageServiceSerializer(services, many=True).data,
                'testimonials': HomePageTestimonialSerializer(testimonials, many=True).data,
                'features': HomePageFeatureSerializer(features, many=True).data,
            }

            logger.info("Home page data retrieved successfully")

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error retrieving home page data: {str(e)}")

            # Return empty data structure in case of error
            return Response({
                'content': [],
                'stats': [],
                'services': [],
                'testimonials': [],
                'features': [],
                'error': 'Unable to fetch home page data'
            }, status=status.HTTP_200_OK)


class HomePageStatsOnlyView(APIView):
    """
    API endpoint for just the statistics (for quick updates).
    """
    permission_classes = []  # Public endpoint

    def get(self, request):
        """
        Get only the statistics for the home page.
        """
        try:
            # Get statistics with auto-update
            stats = HomePageStats.objects.filter(is_active=True).order_by('order', 'name')

            # Update auto-calculated stats
            for stat in stats:
                if stat.auto_update:
                    if stat.name == 'total_customers':
                        stat.value = User.objects.filter(is_active=True).count()
                    elif stat.name == 'total_projects':
                        stat.value = Order.objects.count()
                    elif stat.name == 'completed_projects':
                        stat.value = Order.objects.filter(status='delivered').count()
                    elif stat.name == 'total_products':
                        stat.value = Product.objects.filter(status='published').count()
                    stat.save()

            # Serialize data
            data = HomePageStatsSerializer(stats, many=True).data

            logger.info("Home page stats retrieved successfully")

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error retrieving home page stats: {str(e)}")

            # Return empty array in case of error
            return Response([], status=status.HTTP_200_OK)
