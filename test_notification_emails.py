#!/usr/bin/env python3
"""
Script pour tester le système de notifications avec envoi d'emails
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from notifications.models import NotificationPreference
from notifications.email_service import EmailNotificationService

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    print("🔐 Authentification...")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access')
            print(f"✅ Authentification réussie")
            return token
        else:
            print(f"❌ Échec de l'authentification: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return None

def test_notification_preferences_api(token):
    """Tester l'API des préférences de notification"""
    print("\n⚙️ Test de l'API des préférences de notification...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test 1: Récupérer les préférences
    print("1. Récupération des préférences...")
    try:
        response = requests.get(f"{API_BASE_URL}/notifications/preferences/", headers=headers)
        
        if response.status_code == 200:
            preferences = response.json()
            print(f"   ✅ Préférences récupérées")
            print(f"   📧 Email promotions: {preferences.get('email_promotions')}")
            print(f"   📧 Email commandes: {preferences.get('email_orders')}")
            print(f"   📧 Email système: {preferences.get('email_system')}")
            
            # Test 2: Modifier les préférences
            print("\n2. Modification des préférences...")
            pref_id = preferences.get('id')
            
            update_data = {
                "email_promotions": True,
                "email_orders": True,
                "email_system": True
            }
            
            update_response = requests.patch(
                f"{API_BASE_URL}/notifications/preferences/{pref_id}/", 
                json=update_data, 
                headers=headers
            )
            
            if update_response.status_code == 200:
                print(f"   ✅ Préférences mises à jour")
                return True
            else:
                print(f"   ❌ Erreur de mise à jour: {update_response.status_code}")
                print(f"   Réponse: {update_response.text}")
                return False
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_email_service():
    """Tester le service d'email directement"""
    print("\n📧 Test du service d'email...")
    
    try:
        # Récupérer l'utilisateur de test
        user = User.objects.get(email=TEST_EMAIL)
        print(f"   👤 Utilisateur: {user.email}")
        
        # Test 1: Email de promotion
        print("\n1. Test d'email de promotion...")
        success1 = EmailNotificationService.send_promotion_email(
            user=user,
            promotion_title="Offre spéciale test",
            promotion_description="Ceci est un test d'email de promotion avec 50% de réduction sur tous les produits !",
            promotion_link="http://localhost:8080/promotions/test"
        )
        print(f"   {'✅' if success1 else '❌'} Email de promotion: {'Envoyé' if success1 else 'Échec'}")
        
        # Test 2: Email système
        print("\n2. Test d'email système...")
        success2 = EmailNotificationService.send_system_email(
            user=user,
            title="Test de notification système",
            message_content="Ceci est un test de notification système. Votre compte a été mis à jour avec succès."
        )
        print(f"   {'✅' if success2 else '❌'} Email système: {'Envoyé' if success2 else 'Échec'}")
        
        return success1 and success2
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_notification_emails_via_api(token):
    """Tester l'envoi d'emails de notification via l'API de test"""
    print("\n🔔 Test d'envoi d'emails via l'API...")

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Test 1: Email de promotion
    print("1. Test d'email de promotion via API...")
    promotion_data = {
        "title": "🎉 Promotion exclusive API !",
        "message": "Profitez de 25% de réduction sur tous nos produits. Offre limitée via API !",
        "type": "promotion",
        "link": "http://localhost:8080/promotions/api-test"
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/notifications/notifications/test_email/",
            json=promotion_data,
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ {result.get('message', 'Email envoyé')}")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

    # Test 2: Email système
    print("\n2. Test d'email système via API...")
    system_data = {
        "title": "Test de notification système",
        "message": "Ceci est un test de notification système envoyé via l'API. Votre compte fonctionne parfaitement !",
        "type": "system"
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/notifications/notifications/test_email/",
            json=system_data,
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ {result.get('message', 'Email envoyé')}")
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🧪 TEST DU SYSTÈME DE NOTIFICATIONS AVEC EMAILS")
    print("=" * 60)
    
    # 1. Authentification
    token = get_auth_token()
    if not token:
        print("❌ Impossible de continuer sans authentification")
        return
    
    # 2. Test de l'API des préférences
    success1 = test_notification_preferences_api(token)
    
    # 3. Test du service d'email direct
    success2 = test_email_service()
    
    # 4. Test d'envoi d'emails via API
    success3 = test_notification_emails_via_api(token)
    
    print(f"\n📊 RÉSUMÉ DES TESTS:")
    print(f"   API préférences: {'✅' if success1 else '❌'}")
    print(f"   Service email: {'✅' if success2 else '❌'}")
    print(f"   Emails via API: {'✅' if success3 else '❌'}")
    
    if success1 and success2 and success3:
        print(f"\n🎉 TOUS LES TESTS RÉUSSIS!")
        print(f"   Le système de notifications avec emails fonctionne correctement.")
        print(f"   Vérifiez votre boîte email: {TEST_EMAIL}")
        print(f"   Testez les préférences sur: http://localhost:8080/account/notifications")
    else:
        print(f"\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
