import os
from rest_framework import serializers
from django.db.models import Sum, Count, Q, F, Case, When, Value, IntegerField
from .models import (
    Category,
    ProductAttribute,
    AttributeValue,
    Product,
    ProductImage,
    ProductAttributeValue,
    ProductVariant,
    Inventory,
    StockMovement,
    StockReservation,
    ProductReview,
    ReviewHelpfulness,
    RelatedProduct
)
from .utils import process_product_image, optimize_image

class CategorySerializer(serializers.ModelSerializer):
    """Serializer for the Category model."""

    parent_name = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = [
            'id', 'name', 'slug', 'description', 'parent', 'parent_name',
            'image', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_parent_name(self, obj):
        return obj.parent.name if obj.parent else None

class CategoryTreeSerializer(serializers.ModelSerializer):
    """Serializer for nested category tree."""

    children = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = ['id', 'name', 'slug', 'children']

    def get_children(self, obj):
        children = obj.children.filter(is_active=True)
        return CategoryTreeSerializer(children, many=True).data if children else []

class ProductAttributeSerializer(serializers.ModelSerializer):
    """Serializer for the ProductAttribute model."""

    class Meta:
        model = ProductAttribute
        fields = ['id', 'name', 'description']

class AttributeValueSerializer(serializers.ModelSerializer):
    """Serializer for the AttributeValue model."""

    attribute_name = serializers.SerializerMethodField()

    class Meta:
        model = AttributeValue
        fields = ['id', 'attribute', 'attribute_name', 'value']

    def get_attribute_name(self, obj):
        return obj.attribute.name

class ProductImageSerializer(serializers.ModelSerializer):
    """Serializer for the ProductImage model."""

    image_url = serializers.SerializerMethodField()

    class Meta:
        model = ProductImage
        fields = ['id', 'product', 'image', 'image_url', 'alt_text', 'is_primary', 'created_at']
        read_only_fields = ['created_at', 'image_url']

    def get_image_url(self, obj):
        if obj.image:
            return obj.image.url
        return None

    def validate(self, data):
        """Validate image size and format."""
        image = data.get('image')
        if image:
            # Check file size (max 5MB)
            if image.size > 5 * 1024 * 1024:
                raise serializers.ValidationError({"image": "Image size should not exceed 5MB."})

            # Check file extension
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            ext = os.path.splitext(image.name)[1].lower()
            if ext not in valid_extensions:
                raise serializers.ValidationError({"image": f"Unsupported file extension. Use {', '.join(valid_extensions)}"})

        return data

class ProductImageBulkUploadSerializer(serializers.Serializer):
    """Serializer for bulk uploading product images."""

    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all())
    images = serializers.ListField(
        child=serializers.ImageField(max_length=1000, allow_empty_file=False),
        min_length=1,
        max_length=10  # Limit to 10 images per upload
    )
    alt_text = serializers.CharField(required=False, allow_blank=True)

    def validate(self, data):
        """Validate images size and format."""
        images = data.get('images', [])

        for image in images:
            # Check file size (max 5MB)
            if image.size > 5 * 1024 * 1024:
                raise serializers.ValidationError({"images": f"Image {image.name} size should not exceed 5MB."})

            # Check file extension
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            ext = os.path.splitext(image.name)[1].lower()
            if ext not in valid_extensions:
                raise serializers.ValidationError({"images": f"Unsupported file extension for {image.name}. Use {', '.join(valid_extensions)}"})

        return data

class ProductAttributeValueSerializer(serializers.ModelSerializer):
    """Serializer for the ProductAttributeValue model."""

    attribute_name = serializers.SerializerMethodField()
    value_name = serializers.SerializerMethodField()

    class Meta:
        model = ProductAttributeValue
        fields = ['id', 'product', 'attribute', 'attribute_name', 'value', 'value_name']

    def get_attribute_name(self, obj):
        return obj.attribute.name

    def get_value_name(self, obj):
        return obj.value.value

class StockMovementSerializer(serializers.ModelSerializer):
    """Serializer for the StockMovement model."""

    created_by_name = serializers.SerializerMethodField()

    class Meta:
        model = StockMovement
        fields = [
            'id', 'inventory', 'quantity_before', 'quantity_after', 'quantity_changed',
            'reason', 'reference', 'notes', 'created_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_at', 'created_by', 'created_by_name']

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None

class StockReservationSerializer(serializers.ModelSerializer):
    """Serializer for the StockReservation model."""

    created_by_name = serializers.SerializerMethodField()

    class Meta:
        model = StockReservation
        fields = [
            'id', 'inventory', 'quantity', 'reference', 'notes',
            'created_at', 'expires_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_at', 'created_by', 'created_by_name']

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None

class InventorySerializer(serializers.ModelSerializer):
    """Serializer for the Inventory model."""

    available_quantity = serializers.ReadOnlyField()
    is_in_stock = serializers.ReadOnlyField()
    is_low_stock = serializers.ReadOnlyField()
    movements = StockMovementSerializer(many=True, read_only=True)
    reservations = StockReservationSerializer(many=True, read_only=True)

    class Meta:
        model = Inventory
        fields = [
            'id', 'product', 'variant', 'quantity', 'reserved_quantity',
            'available_quantity', 'low_stock_threshold', 'is_in_stock',
            'is_low_stock', 'last_checked', 'movements', 'reservations'
        ]
        read_only_fields = ['last_checked', 'movements', 'reservations']

class InventoryAdjustmentSerializer(serializers.Serializer):
    """Serializer for adjusting inventory."""

    quantity = serializers.IntegerField()
    reason = serializers.ChoiceField(choices=StockMovement.REASON_CHOICES)
    reference = serializers.CharField(required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)

class StockReservationCreateSerializer(serializers.Serializer):
    """Serializer for creating a stock reservation."""

    quantity = serializers.IntegerField(min_value=1)
    reference = serializers.CharField(required=False, allow_blank=True)
    notes = serializers.CharField(required=False, allow_blank=True)
    expires_at = serializers.DateTimeField(required=False)

class StockReservationReleaseSerializer(serializers.Serializer):
    """Serializer for releasing a stock reservation."""

    reservation_id = serializers.IntegerField(required=False)
    reference = serializers.CharField(required=False, allow_blank=True)
    quantity = serializers.IntegerField(required=False, min_value=1)
    notes = serializers.CharField(required=False, allow_blank=True)

class ProductVariantSerializer(serializers.ModelSerializer):
    """Serializer for the ProductVariant model."""

    attributes = AttributeValueSerializer(many=True, read_only=True)
    inventory = InventorySerializer(read_only=True)
    price = serializers.ReadOnlyField()

    class Meta:
        model = ProductVariant
        fields = [
            'id', 'product', 'name', 'sku', 'price_adjustment', 'price',
            'attributes', 'inventory', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def create(self, validated_data):
        """Create a variant and its inventory."""
        variant = super().create(validated_data)

        # Create inventory for the variant
        Inventory.objects.create(variant=variant)

        return variant

class ProductVariantCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating a single product variant."""

    attribute_values = serializers.PrimaryKeyRelatedField(
        queryset=AttributeValue.objects.all(),
        many=True,
        write_only=True
    )

    class Meta:
        model = ProductVariant
        fields = [
            'product', 'name', 'sku', 'price_adjustment',
            'attribute_values', 'is_active'
        ]

    def validate(self, data):
        """Validate that all attribute values belong to different attributes."""
        attribute_values = data.get('attribute_values', [])

        # Check if we have attribute values
        if not attribute_values:
            return data

        # Check that all attribute values belong to different attributes
        attributes = set()
        for value in attribute_values:
            if value.attribute.id in attributes:
                raise serializers.ValidationError(
                    {"attribute_values": f"Multiple values for attribute '{value.attribute.name}' are not allowed."}
                )
            attributes.add(value.attribute.id)

        return data

    def create(self, validated_data):
        """Create a variant with attribute values and inventory."""
        attribute_values = validated_data.pop('attribute_values', [])

        # Generate name if not provided
        if not validated_data.get('name'):
            # Create name from attribute values
            name_parts = [av.value for av in attribute_values]
            validated_data['name'] = ' / '.join(name_parts)

        # Create the variant
        variant = super().create(validated_data)

        # Add attribute values
        variant.attributes.set(attribute_values)

        # Create inventory
        Inventory.objects.create(variant=variant)

        return variant

class VariantGeneratorSerializer(serializers.Serializer):
    """Serializer for generating product variants from attribute combinations."""

    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all())
    attribute_values = serializers.ListField(
        child=serializers.ListField(
            child=serializers.PrimaryKeyRelatedField(queryset=AttributeValue.objects.all())
        ),
        min_length=1
    )
    price_adjustment = serializers.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    is_active = serializers.BooleanField(default=True)

    def validate(self, data):
        """Validate that each inner list contains values for a single attribute."""
        attribute_values = data.get('attribute_values', [])

        for i, values in enumerate(attribute_values):
            if not values:
                raise serializers.ValidationError(
                    {"attribute_values": f"Attribute group {i+1} cannot be empty."}
                )

            # Check that all values in this group belong to the same attribute
            first_attribute = values[0].attribute
            for value in values[1:]:
                if value.attribute != first_attribute:
                    raise serializers.ValidationError(
                        {"attribute_values": f"All values in group {i+1} must belong to the same attribute."}
                    )

        return data

class ProductReviewSerializer(serializers.ModelSerializer):
    """Serializer for the ProductReview model."""

    user_email = serializers.SerializerMethodField()
    user_name = serializers.SerializerMethodField()
    user_avatar = serializers.SerializerMethodField()
    helpfulness_ratio = serializers.ReadOnlyField()
    user_has_voted = serializers.SerializerMethodField()
    user_vote_is_helpful = serializers.SerializerMethodField()

    class Meta:
        model = ProductReview
        fields = [
            'id', 'product', 'user', 'user_email', 'user_name', 'user_avatar',
            'order', 'rating', 'title', 'comment', 'pros', 'cons',
            'is_approved', 'is_verified_purchase', 'helpful_count', 'not_helpful_count',
            'helpfulness_ratio', 'user_has_voted', 'user_vote_is_helpful',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'helpful_count', 'not_helpful_count', 'is_verified_purchase', 'user']

    def get_user_email(self, obj):
        # Masquer l'email pour la confidentialité
        email = obj.user.email
        if '@' in email:
            local, domain = email.split('@', 1)
            masked_local = local[:2] + '*' * (len(local) - 2)
            return f"{masked_local}@{domain}"
        return email

    def get_user_name(self, obj):
        first_name = obj.user.first_name or ''
        last_name = obj.user.last_name or ''

        if first_name and last_name:
            return f"{first_name} {last_name[0]}."
        elif first_name:
            return first_name
        else:
            return "Utilisateur anonyme"

    def get_user_avatar(self, obj):
        # Retourner l'URL de l'avatar ou une image par défaut
        if hasattr(obj.user, 'avatar') and obj.user.avatar:
            return obj.user.avatar.url
        return None

    def get_user_has_voted(self, obj):
        """Check if current user has voted on this review"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            from .models import ReviewHelpfulness
            return ReviewHelpfulness.objects.filter(
                review=obj,
                user=request.user
            ).exists()
        return False

    def get_user_vote_is_helpful(self, obj):
        """Get current user's vote on this review"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            from .models import ReviewHelpfulness
            vote = ReviewHelpfulness.objects.filter(
                review=obj,
                user=request.user
            ).first()
            return vote.is_helpful if vote else None
        return None


class ReviewHelpfulnessSerializer(serializers.ModelSerializer):
    """Serializer for review helpfulness votes."""

    class Meta:
        model = ReviewHelpfulness
        fields = ['id', 'review', 'user', 'is_helpful', 'created_at']
        read_only_fields = ['created_at']


class ProductReviewStatsSerializer(serializers.Serializer):
    """Serializer for product review statistics."""

    total_reviews = serializers.IntegerField()
    average_rating = serializers.FloatField()
    rating_distribution = serializers.DictField()
    verified_purchases_count = serializers.IntegerField()


class ProductListSerializer(serializers.ModelSerializer):
    """Serializer for listing products."""

    categories = CategorySerializer(many=True, read_only=True)
    primary_image = serializers.SerializerMethodField()
    current_price = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()
    is_on_sale = serializers.ReadOnlyField()
    in_stock = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'sku', 'short_description', 'price',
            'sale_price', 'current_price', 'discount_percentage', 'is_on_sale',
            'categories', 'primary_image', 'is_featured', 'in_stock', 'status',
            'created_at'
        ]

    def get_primary_image(self, obj):
        primary_image = obj.images.filter(is_primary=True).first()
        if not primary_image:
            primary_image = obj.images.first()

        if primary_image:
            return {
                'id': primary_image.id,
                'image': primary_image.image.url if primary_image.image else None,
                'alt_text': primary_image.alt_text
            }
        return None

    def get_in_stock(self, obj):
        try:
            return obj.inventory.is_in_stock
        except:
            return False

class ProductDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed product view."""

    categories = CategorySerializer(many=True, read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    attribute_values = ProductAttributeValueSerializer(many=True, read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)
    inventory = InventorySerializer(read_only=True)
    reviews = ProductReviewSerializer(many=True, read_only=True)
    current_price = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()
    is_on_sale = serializers.ReadOnlyField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'sku', 'description', 'short_description',
            'price', 'sale_price', 'current_price', 'discount_percentage',
            'is_on_sale', 'categories', 'images', 'attribute_values',
            'variants', 'inventory', 'reviews', 'is_featured', 'is_digital',
            'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class ProductCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating products."""

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'sku', 'description', 'short_description',
            'price', 'sale_price', 'cost_price', 'categories', 'is_featured',
            'is_digital', 'status'
        ]

    def create(self, validated_data):
        # Get the current user from the context
        user = self.context['request'].user
        validated_data['created_by'] = user
        validated_data['updated_by'] = user

        # Create the product
        product = super().create(validated_data)

        # Create inventory for the product
        Inventory.objects.create(product=product)

        return product

    def update(self, instance, validated_data):
        # Update the updated_by field
        validated_data['updated_by'] = self.context['request'].user

        # Update the product
        return super().update(instance, validated_data)

class RelatedProductSerializer(serializers.ModelSerializer):
    """Serializer for the RelatedProduct model."""

    related_product_details = ProductListSerializer(source='related_product', read_only=True)

    class Meta:
        model = RelatedProduct
        fields = [
            'id', 'product', 'related_product', 'related_product_details',
            'relation_type', 'position', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class InventoryStatsSerializer(serializers.Serializer):
    """Serializer for inventory statistics."""

    total_products = serializers.IntegerField()
    total_variants = serializers.IntegerField()
    total_quantity = serializers.IntegerField()
    total_reserved = serializers.IntegerField()
    total_available = serializers.IntegerField()
    in_stock = serializers.IntegerField()
    low_stock = serializers.IntegerField()
    out_of_stock = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=12, decimal_places=2)
    reserved_value = serializers.DecimalField(max_digits=12, decimal_places=2)


class BulkInventoryAdjustmentSerializer(serializers.Serializer):
    """Serializer for bulk adjusting inventory."""

    class AdjustmentItemSerializer(serializers.Serializer):
        inventory_id = serializers.IntegerField()
        quantity = serializers.IntegerField()
        reason = serializers.ChoiceField(choices=StockMovement.REASON_CHOICES, default='adjustment')
        reference = serializers.CharField(required=False, allow_blank=True)
        notes = serializers.CharField(required=False, allow_blank=True)

    adjustments = serializers.ListField(
        child=AdjustmentItemSerializer(),
        min_length=1
    )


class InventoryListSerializer(serializers.ModelSerializer):
    """Serializer for listing inventory items."""

    product_name = serializers.SerializerMethodField()
    variant_name = serializers.SerializerMethodField()
    sku = serializers.SerializerMethodField()
    price = serializers.SerializerMethodField()
    stock_status = serializers.SerializerMethodField()
    stock_level_percentage = serializers.SerializerMethodField()

    class Meta:
        model = Inventory
        fields = [
            'id', 'product', 'variant', 'product_name', 'variant_name', 'sku',
            'quantity', 'reserved_quantity', 'available_quantity', 'low_stock_threshold',
            'is_in_stock', 'is_low_stock', 'stock_status', 'stock_level_percentage',
            'price', 'last_checked'
        ]

    def get_product_name(self, obj):
        if obj.product:
            return obj.product.name
        elif obj.variant:
            return obj.variant.product.name
        return None

    def get_variant_name(self, obj):
        if obj.variant:
            return obj.variant.name
        return None

    def get_sku(self, obj):
        if obj.product:
            return obj.product.sku
        elif obj.variant:
            return obj.variant.sku
        return None

    def get_price(self, obj):
        if obj.product:
            return obj.product.current_price
        elif obj.variant:
            return obj.variant.price
        return None

    def get_stock_status(self, obj):
        if not obj.is_in_stock:
            return "out_of_stock"
        if obj.is_low_stock:
            return "low_stock"
        return "in_stock"

    def get_stock_level_percentage(self, obj):
        if obj.low_stock_threshold == 0:
            return 100
        return min(100, int((obj.available_quantity / obj.low_stock_threshold) * 100))


class BulkInventoryAdjustmentSerializer(serializers.Serializer):
    """Serializer for bulk adjusting inventory."""

    adjustments = serializers.ListField(
        child=serializers.DictField(
            child=serializers.Field(),
            allow_empty=False
        ),
        min_length=1
    )

    def validate_adjustments(self, value):
        """Validate each adjustment in the list."""
        validated_adjustments = []

        for i, adjustment in enumerate(value):
            # Check required fields
            if 'inventory_id' not in adjustment:
                raise serializers.ValidationError(f"Adjustment {i+1} is missing 'inventory_id'")

            if 'quantity' not in adjustment:
                raise serializers.ValidationError(f"Adjustment {i+1} is missing 'quantity'")

            # Validate inventory_id
            try:
                inventory_id = int(adjustment['inventory_id'])
                Inventory.objects.get(id=inventory_id)
            except (ValueError, Inventory.DoesNotExist):
                raise serializers.ValidationError(f"Adjustment {i+1} has invalid 'inventory_id'")

            # Validate quantity
            try:
                quantity = int(adjustment['quantity'])
            except ValueError:
                raise serializers.ValidationError(f"Adjustment {i+1} has invalid 'quantity'")

            # Validate reason if provided
            reason = adjustment.get('reason', 'adjustment')
            if reason not in dict(StockMovement.REASON_CHOICES):
                raise serializers.ValidationError(f"Adjustment {i+1} has invalid 'reason'")

            # Add validated adjustment
            validated_adjustments.append({
                'inventory_id': inventory_id,
                'quantity': quantity,
                'reason': reason,
                'reference': adjustment.get('reference'),
                'notes': adjustment.get('notes')
            })

        return validated_adjustments
