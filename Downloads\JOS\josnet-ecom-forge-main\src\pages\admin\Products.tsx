
import AdminLayout from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Package, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Filter,
  ChevronDown
} from "lucide-react";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

// Données fictives pour les produits
const mockProducts = [
  { 
    id: "PRD-001", 
    name: "Routeur Wi-Fi 6", 
    sku: "RTR-WF6-001", 
    category: "Réseaux", 
    price: 89.99, 
    stock: 24, 
    status: "En stock"
  },
  { 
    id: "PRD-002", 
    name: "Câble HDMI 2.1 2m", 
    sku: "CBL-HD21-2M", 
    category: "Câbles", 
    price: 14.99, 
    stock: 45, 
    status: "En stock"
  },
  { 
    id: "PRD-003", 
    name: "Disque SSD 1TB", 
    sku: "SSD-1TB-001", 
    category: "Stockage", 
    price: 119.99, 
    stock: 12, 
    status: "En stock"
  },
  { 
    id: "PRD-004", 
    name: "Adaptateur USB-C", 
    sku: "ADP-USBC-001", 
    category: "Adaptateurs", 
    price: 29.99, 
    stock: 8, 
    status: "Stock faible"
  },
  { 
    id: "PRD-005", 
    name: "Carte graphique 8GB", 
    sku: "GPU-8GB-001", 
    category: "Composants", 
    price: 349.99, 
    stock: 0, 
    status: "Rupture"
  },
];

const Products = () => {
  const [searchQuery, setSearchQuery] = useState("");

  // Fonction de filtrage pour la recherche
  const filteredProducts = mockProducts.filter(product => 
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleEditProduct = (id: string) => {
    toast({
      title: "Modification demandée",
      description: `Édition du produit ${id} initiée`,
    });
    // Dans une app réelle, cette fonction ouvrirait un formulaire d'édition
  };

  const handleDeleteProduct = (id: string) => {
    toast({
      title: "Suppression demandée",
      description: `Suppression du produit ${id} initiée`,
      variant: "destructive",
    });
    // Dans une app réelle, cette fonction afficherait une confirmation
  };

  const handleAddProduct = () => {
    toast({
      title: "Nouveau produit",
      description: "Formulaire de création de produit ouvert",
    });
    // Dans une app réelle, cette fonction ouvrirait un formulaire de création
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Produits</h1>
          <p className="text-gray-500">Gérez votre catalogue de produits JOSNET</p>
        </div>

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher un produit..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-1">
              <Filter size={16} />
              Filtrer
              <ChevronDown size={14} />
            </Button>
            <Button onClick={handleAddProduct} className="flex items-center gap-1">
              <Plus size={16} />
              Nouveau Produit
            </Button>
          </div>
        </div>

        {/* Tableau des produits */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Produit</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>Catégorie</TableHead>
                <TableHead className="text-right">Prix</TableHead>
                <TableHead className="text-right">Stock</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id}>
                  <TableCell className="font-medium">{product.id}</TableCell>
                  <TableCell>{product.name}</TableCell>
                  <TableCell>{product.sku}</TableCell>
                  <TableCell>{product.category}</TableCell>
                  <TableCell className="text-right">{product.price.toFixed(2)} €</TableCell>
                  <TableCell className="text-right">{product.stock}</TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                      product.status === "En stock"
                        ? "bg-green-100 text-green-800"
                        : product.status === "Stock faible"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                    }`}>
                      {product.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleEditProduct(product.id)}
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleDeleteProduct(product.id)}
                      >
                        <Trash2 size={16} className="text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Indication du nombre de produits */}
        <div className="text-sm text-gray-500">
          Affichage de {filteredProducts.length} produits sur {mockProducts.length}
        </div>
      </div>
    </AdminLayout>
  );
};

export default Products;
