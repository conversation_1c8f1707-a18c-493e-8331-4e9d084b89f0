import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import AccountLayout from "@/components/account/AccountLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Package,
  Truck,
  CheckCircle,
  Clock,
  MapPin,
  CreditCard,
  FileText,
  Download,
  Loader2,
  ExternalLink,
  X,
  RefreshCw,
  MessageCircle,
  ShoppingCart
} from "lucide-react";
import orderApi from "@/services/orderApi";
import invoiceApi from "@/services/invoiceApi";
import { useAuth } from "@/contexts/AuthContext";
import { useCart } from "@/contexts/CartContext";
import { toast } from "@/hooks/use-toast";

const OrderDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { user, isAuthenticated } = useAuth();
  const { addToCart } = useCart();
  const navigate = useNavigate();
  const [isActionLoading, setIsActionLoading] = useState(false);

  // Helper function to safely format prices
  const formatPrice = (price: any): string => {
    if (price === null || price === undefined) return '0';
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return isNaN(numPrice) ? '0' : Math.round(numPrice).toString();
  };

  // Action functions
  const handleDownloadInvoice = async () => {
    if (!order?.id) return;

    setIsActionLoading(true);
    try {
      toast({
        title: "Recherche de la facture...",
        description: "Vérification de la disponibilité de la facture.",
      });

      // D'abord, chercher si une facture existe déjà pour cette commande
      let invoice = await invoiceApi.getInvoiceForOrder(order.id.toString());

      if (!invoice) {
        // Essayer de générer une facture si elle n'existe pas
        try {
          toast({
            title: "Génération de la facture...",
            description: "Création de votre facture en cours...",
          });

          invoice = await invoiceApi.generateInvoiceForOrder(order.id.toString());

          if (!invoice) {
            throw new Error("Impossible de générer la facture");
          }
        } catch (generateError: any) {
          console.error('Erreur lors de la génération de la facture:', generateError);
          toast({
            title: "Facture non disponible",
            description: "La facture n'est pas encore disponible et ne peut pas être générée automatiquement. Veuillez contacter le support.",
            variant: "destructive"
          });
          return;
        }
      }

      toast({
        title: "Téléchargement en cours...",
        description: "Génération du fichier PDF...",
      });

      // Télécharger la facture existante
      const pdfUrl = await invoiceApi.downloadInvoice(invoice.id);

      // Créer un lien de téléchargement
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `facture-${order.order_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Nettoyer l'URL blob
      window.URL.revokeObjectURL(pdfUrl);

      toast({
        title: "Facture téléchargée",
        description: `La facture pour la commande #${order.order_number} a été téléchargée avec succès.`,
      });

    } catch (error: any) {
      console.error('Erreur lors du téléchargement de la facture:', error);
      toast({
        title: "Erreur de téléchargement",
        description: error.message || "Impossible de télécharger la facture. Veuillez réessayer ou contacter le support.",
        variant: "destructive",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleTrackOrder = () => {
    if (order?.tracking_number) {
      // URLs de suivi pour différents transporteurs
      const trackingUrls: { [key: string]: string } = {
        'DHL': `https://www.dhl.com/tracking?id=${order.tracking_number}`,
        'FedEx': `https://www.fedex.com/fedextrack/?tracknumbers=${order.tracking_number}`,
        'UPS': `https://www.ups.com/track?tracknum=${order.tracking_number}`,
        'TNT': `https://www.tnt.com/express/tracking.do?searchType=con&cons=${order.tracking_number}`,
        'La Poste': `https://www.laposte.fr/outils/suivre-vos-envois?code=${order.tracking_number}`,
        'Chronopost': `https://www.chronopost.fr/tracking-colis?listeNumerosLT=${order.tracking_number}`,
      };

      const url = order.shipping_carrier && trackingUrls[order.shipping_carrier]
        ? trackingUrls[order.shipping_carrier]
        : `https://www.google.com/search?q=track+package+${order.tracking_number}`;

      window.open(url, '_blank');

      toast({
        title: "Suivi de commande",
        description: `Ouverture du suivi ${order.shipping_carrier ? `via ${order.shipping_carrier}` : 'de votre colis'} dans un nouvel onglet.`,
      });
    } else {
      toast({
        title: "Suivi non disponible",
        description: "Le numéro de suivi n'est pas encore disponible pour cette commande.",
        variant: "destructive",
      });
    }
  };

  const handleCancelOrder = async () => {
    if (!order) return;

    const confirmCancel = window.confirm(
      "Êtes-vous sûr de vouloir annuler cette commande ? Cette action ne peut pas être annulée."
    );

    if (!confirmCancel) return;

    setIsActionLoading(true);
    try {
      // Appel à l'API réelle d'annulation
      console.log(`Tentative d'annulation de la commande ${order.id}...`);
      await orderApi.cancelOrder(parseInt(id!), "Annulation demandée par le client");
      
      console.log(`Commande ${order.id} annulée avec succès`);
      toast({
        title: "Commande annulée",
        description: "Votre commande a été annulée avec succès.",
      });

      // Actualiser les données de la commande
      window.location.reload();
    } catch (error) {
      console.error("Erreur lors de l'annulation de la commande:", error);
      toast({
        title: "Erreur",
        description: error?.response?.data?.message || "Impossible d'annuler la commande. Veuillez contacter le support.",
        variant: "destructive",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleRequestRefund = () => {
    navigate('/account/returns', {
      state: { orderId: order?.id, orderNumber: order?.order_number }
    });
  };

  const handleContactSupport = () => {
    navigate('/account/messages', {
      state: {
        subject: `Support pour commande #${order?.order_number}`,
        orderId: order?.id
      }
    });
  };

  const handleReorder = async () => {
    if (!order?.items) return;

    setIsActionLoading(true);
    try {
      // Add all items from the order to cart
      for (const item of order.items) {
        // Assurez-vous que item.product est un ID numérique
        const productId = typeof item.product === 'object' ? item.product.id : item.product;
        
        if (!productId) {
          console.error('ID de produit invalide pour:', item);
          continue; // Passer à l'article suivant si l'ID est invalide
        }
        
        // Ajouter l'article au panier en utilisant son ID
        addToCart(productId, item.quantity);
      }

      toast({
        title: "Articles ajoutés au panier",
        description: `${order.items.length} article(s) de votre commande précédente ont été ajoutés au panier.`,
      });

      // Navigate to cart
      navigate('/cart');
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible d'ajouter les articles au panier.",
        variant: "destructive",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  // Helper function to check if order can be cancelled
  const canCancelOrder = (status: string) => {
    return ['pending', 'confirmed'].includes(status);
  };

  // Helper function to check if refund can be requested
  const canRequestRefund = (status: string) => {
    return ['delivered'].includes(status);
  };

  // Fetch order details
  const {
    data: order,
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: ['order', id],
    queryFn: () => orderApi.getOrder(parseInt(id!)),
    enabled: isAuthenticated && !!id,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 404 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 3;
    }
  });

  // Status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-purple-100 text-purple-800';
      case 'shipped': return 'bg-indigo-100 text-indigo-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Payment status color mapping
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'refunded': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Status icon mapping
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'confirmed': return <CheckCircle className="h-4 w-4" />;
      case 'processing': return <Package className="h-4 w-4" />;
      case 'shipped': return <Truck className="h-4 w-4" />;
      case 'delivered': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <FileText className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  if (!isAuthenticated) {
    return (
      <AccountLayout title="Détail de la commande">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-600">Vous devez être connecté pour voir cette commande.</p>
            <Link to="/login">
              <Button className="mt-4">Se connecter</Button>
            </Link>
          </CardContent>
        </Card>
      </AccountLayout>
    );
  }

  if (isLoading) {
    return (
      <AccountLayout title="Détail de la commande">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AccountLayout>
    );
  }

  if (isError || !order) {
    return (
      <AccountLayout title="Détail de la commande">
        <Card>
          <CardContent className="py-8 text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">Commande non trouvée</h3>
            <p className="text-gray-600 mb-4">
              Cette commande n'existe pas ou vous n'avez pas l'autorisation de la voir.
            </p>
            {process.env.NODE_ENV === 'development' && (
              <div className="text-left bg-gray-50 p-3 rounded mb-4">
                <p className="text-xs text-gray-500 mb-1">Informations de débogage :</p>
                <p className="text-xs">ID recherché : {id}</p>
                <p className="text-xs">Erreur : {error?.message || 'Inconnue'}</p>
              </div>
            )}
            <Link to="/account/orders">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour aux commandes
              </Button>
            </Link>
          </CardContent>
        </Card>
      </AccountLayout>
    );
  }

  return (
    <AccountLayout title={`Commande #${order.order_number}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <Link to="/account/orders">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour aux commandes
              </Button>
            </Link>

            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(order.status || 'pending')}>
                {getStatusIcon(order.status || 'pending')}
                <span className="ml-1">{order.status_display || order.status}</span>
              </Badge>
              <Badge className={getPaymentStatusColor(order.payment_status || 'pending')}>
                <CreditCard className="h-4 w-4 mr-1" />
                {order.payment_status_display || order.payment_status}
              </Badge>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            {/* Download Invoice */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadInvoice}
              disabled={isActionLoading}
            >
              {isActionLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Téléchargement...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Télécharger facture
                </>
              )}
            </Button>

            {/* Track Order */}
            {order.tracking_number && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleTrackOrder}
                disabled={isActionLoading}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Suivre la commande
              </Button>
            )}

            {/* Cancel Order */}
            {canCancelOrder(order.status || '') && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancelOrder}
                disabled={isActionLoading}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-2" />
                Annuler commande
              </Button>
            )}

            {/* Request Refund */}
            {canRequestRefund(order.status || '') && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRequestRefund}
                disabled={isActionLoading}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Demander remboursement
              </Button>
            )}

            {/* Reorder */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleReorder}
              disabled={isActionLoading}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Recommander
            </Button>

            {/* Contact Support */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleContactSupport}
              disabled={isActionLoading}
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              Contacter support
            </Button>
          </div>
        </div>

        {/* Order Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Informations de la commande
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Numéro de commande</p>
                <p className="font-semibold">{order.order_number}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Date de commande</p>
                <p className="font-semibold">
                  {order.created_at ? format(new Date(order.created_at), 'dd MMMM yyyy à HH:mm', { locale: fr }) : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Méthode de paiement</p>
                <p className="font-semibold">{order.payment_method_display || order.payment_method}</p>
              </div>
              {order.tracking_number && (
                <div>
                  <p className="text-sm text-gray-600">Numéro de suivi</p>
                  <p className="font-semibold">{order.tracking_number}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Order Items */}
        <Card>
          <CardHeader>
            <CardTitle>Articles commandés</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {order.items?.map((item, index) => (
                <div key={index} className="flex items-center justify-between py-4 border-b last:border-b-0">
                  <div className="flex-1">
                    <h4 className="font-semibold">{item.product_name}</h4>
                    <p className="text-sm text-gray-600">SKU: {item.sku}</p>
                    <p className="text-sm text-gray-600">Quantité: {item.quantity}</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="font-semibold">{formatPrice(item.final_price || item.subtotal)} BIF</p>
                      <p className="text-sm text-gray-600">{formatPrice(item.price)} BIF × {item.quantity}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Assurez-vous que item.product est un ID numérique
                        const productId = typeof item.product === 'object' ? item.product.id : item.product;
                        
                        if (!productId) {
                          console.error('ID de produit invalide pour:', item);
                          toast({
                            title: "Erreur",
                            description: "Impossible d'ajouter cet article au panier.",
                            variant: "destructive"
                          });
                          return;
                        }
                        
                        // Ajouter l'article au panier en utilisant son ID
                        addToCart(productId, 1);
                        toast({
                          title: "Article ajouté",
                          description: `${item.product_name} a été ajouté au panier.`,
                        });
                      }}
                      disabled={isActionLoading}
                    >
                      <ShoppingCart className="h-4 w-4 mr-1" />
                      Ajouter au panier
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Shipping Address */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Adresse de livraison
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <p className="font-semibold">{order.shipping_first_name} {order.shipping_last_name}</p>
              {order.shipping_company && <p>{order.shipping_company}</p>}
              <p>{order.shipping_address_line1}</p>
              {order.shipping_address_line2 && <p>{order.shipping_address_line2}</p>}
              <p>{order.shipping_postal_code} {order.shipping_city}</p>
              <p>{order.shipping_country}</p>
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Résumé de la commande</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Sous-total</span>
                <span>{formatPrice(order.subtotal)} BIF</span>
              </div>
              <div className="flex justify-between">
                <span>Frais de livraison</span>
                <span>{formatPrice(order.shipping_cost)} BIF</span>
              </div>
              {parseFloat(formatPrice(order.tax_amount)) > 0 && (
                <div className="flex justify-between">
                  <span>Taxes</span>
                  <span>{formatPrice(order.tax_amount)} BIF</span>
                </div>
              )}
              {parseFloat(formatPrice(order.discount_amount)) > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Remise {order.promo_code ? `(Code: ${order.promo_code})` : ''}</span>
                  <span>-{formatPrice(order.discount_amount)} BIF</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>Total</span>
                <span>{formatPrice(order.total)} BIF</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Notes */}
        {order.customer_notes && (
          <Card>
            <CardHeader>
              <CardTitle>Notes du client</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">{order.customer_notes}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </AccountLayout>
  );
};

export default OrderDetail;
