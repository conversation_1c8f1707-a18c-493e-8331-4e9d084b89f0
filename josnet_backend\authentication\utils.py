
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.contrib.auth.tokens import default_token_generator
import logging

logger = logging.getLogger(__name__)

def send_verification_email(user):
    """
    Envoie un email de vérification professionnel à l'utilisateur
    """
    try:
        # Générer le token de vérification
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        token = default_token_generator.make_token(user)
        
        # G<PERSON><PERSON> le cas où uid est déjà une string (Python 3)
        if isinstance(uid, bytes):
            uid_str = uid.decode()
        else:
            uid_str = uid
            
        verification_token = f"{uid_str}-{token}"
        
        # Construire l'URL de vérification
        verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
        
        # Préparer le contenu de l'email
        subject = "🔐 Vérifiez votre compte JOSNET NETWORK"
        
        # Message HTML
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Vérification de compte JOSNET</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb;">JOSNET NETWORK</h1>
                    <p style="color: #666;">Votre partenaire technologique de confiance</p>
                </div>
                
                <div style="background: #f8fafc; padding: 30px; border-radius: 10px; margin-bottom: 30px;">
                    <h2 style="color: #1e40af; margin-bottom: 20px;">Bienvenue {user.first_name or 'cher utilisateur'} !</h2>
                    
                    <p>Merci de vous être inscrit sur <strong>JOSNET NETWORK</strong>. Pour des raisons de sécurité, nous devons vérifier votre adresse email.</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{verification_url}" 
                           style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                            ✅ Vérifier mon email
                        </a>
                    </div>
                    
                    <p style="color: #666; font-size: 14px;">
                        Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :<br>
                        <a href="{verification_url}" style="color: #2563eb; word-break: break-all;">{verification_url}</a>
                    </p>
                </div>
                
                <div style="background: #fef3c7; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <p style="margin: 0; color: #92400e;">
                        <strong>⚠️ Important :</strong> Ce lien expire dans 24 heures pour votre sécurité.
                    </p>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 12px; border-top: 1px solid #e5e7eb; padding-top: 20px;">
                    <p>Cet email a été envoyé par JOSNET NETWORK</p>
                    <p>Si vous n'avez pas créé de compte, ignorez cet email.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Message texte (fallback)
        text_message = f"""
        Bienvenue sur JOSNET NETWORK !
        
        Bonjour {user.first_name or 'cher utilisateur'},
        
        Merci de vous être inscrit sur notre plateforme. Pour des raisons de sécurité, 
        veuillez vérifier votre adresse email en cliquant sur le lien ci-dessous :
        
        {verification_url}
        
        Ce lien expire dans 24 heures.
        
        Si vous n'avez pas créé de compte, ignorez cet email.
        
        Cordialement,
        L'équipe JOSNET NETWORK
        """
        
        # Envoyer l'email
        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_message, "text/html")
        
        result = msg.send()
        
        if result:
            logger.info(f"Email de vérification envoyé avec succès à {user.email}")
            print(f"✅ Email de vérification envoyé à {user.email}")
            return True
        else:
            logger.error(f"Échec de l'envoi de l'email à {user.email}")
            print(f"❌ Échec de l'envoi de l'email à {user.email}")
            return False
            
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de vérification à {user.email}: {str(e)}")
        print(f"❌ Erreur lors de l'envoi de l'email: {e}")
        return False

def send_welcome_email(user):
    """
    Envoie un email de bienvenue après vérification
    """
    try:
        subject = "🎉 Bienvenue sur JOSNET NETWORK !"
        
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb;">JOSNET NETWORK</h1>
                </div>
                
                <div style="background: #f0f9ff; padding: 30px; border-radius: 10px;">
                    <h2 style="color: #1e40af;">Félicitations {user.first_name} !</h2>
                    
                    <p>Votre compte a été vérifié avec succès. Vous pouvez maintenant profiter de tous nos services :</p>
                    
                    <ul style="color: #374151;">
                        <li>🛒 Parcourir notre catalogue de produits</li>
                        <li>💼 Accéder à votre espace personnel</li>
                        <li>📞 Contacter notre support technique</li>
                        <li>🎯 Recevoir des offres personnalisées</li>
                    </ul>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{settings.FRONTEND_URL}/account" 
                           style="background: #10b981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                            🚀 Accéder à mon compte
                        </a>
                    </div>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 12px; margin-top: 30px;">
                    <p>Merci de faire confiance à JOSNET NETWORK</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_message = f"""
        Félicitations {user.first_name} !
        
        Votre compte JOSNET NETWORK a été vérifié avec succès.
        
        Vous pouvez maintenant accéder à tous nos services sur {settings.FRONTEND_URL}
        
        Merci de faire confiance à JOSNET NETWORK !
        """
        
        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_message, "text/html")
        
        return msg.send()

    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de bienvenue: {str(e)}")
        return False

def send_password_reset_email(user):
    """
    Envoie un email de réinitialisation de mot de passe
    """
    try:
        # Générer le token de réinitialisation
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        token = default_token_generator.make_token(user)

        # Gérer le cas où uid est déjà une string (Python 3)
        if isinstance(uid, bytes):
            uid_str = uid.decode()
        else:
            uid_str = uid

        reset_token = f"{uid_str}-{token}"

        # Construire l'URL de réinitialisation
        reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"

        # Préparer le contenu de l'email
        subject = "🔐 Réinitialisation de votre mot de passe JOSNET"

        # Message HTML
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb;">JOSNET NETWORK</h1>
                </div>

                <div style="background: #fef3c7; padding: 30px; border-radius: 10px; margin-bottom: 30px;">
                    <h2 style="color: #92400e;">Réinitialisation de mot de passe</h2>

                    <p>Bonjour {user.first_name or 'cher utilisateur'},</p>

                    <p>Vous avez demandé la réinitialisation de votre mot de passe pour votre compte JOSNET NETWORK.</p>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{reset_url}"
                           style="background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                            🔑 Réinitialiser mon mot de passe
                        </a>
                    </div>

                    <p style="color: #666; font-size: 14px;">
                        Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :<br>
                        <a href="{reset_url}" style="color: #dc2626; word-break: break-all;">{reset_url}</a>
                    </p>
                </div>

                <div style="background: #fee2e2; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <p style="margin: 0; color: #dc2626;">
                        <strong>⚠️ Important :</strong> Ce lien expire dans 24 heures. Si vous n'avez pas demandé cette réinitialisation, ignorez cet email.
                    </p>
                </div>

                <div style="text-align: center; color: #666; font-size: 12px; border-top: 1px solid #e5e7eb; padding-top: 20px;">
                    <p>Cet email a été envoyé par JOSNET NETWORK</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Message texte (fallback)
        text_message = f"""
        Réinitialisation de mot de passe - JOSNET NETWORK

        Bonjour {user.first_name or 'cher utilisateur'},

        Vous avez demandé la réinitialisation de votre mot de passe.

        Cliquez sur le lien ci-dessous pour définir un nouveau mot de passe :
        {reset_url}

        Ce lien expire dans 24 heures.

        Si vous n'avez pas demandé cette réinitialisation, ignorez cet email.

        Cordialement,
        L'équipe JOSNET NETWORK
        """

        # Envoyer l'email
        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_message, "text/html")

        result = msg.send()

        if result:
            logger.info(f"Email de réinitialisation envoyé avec succès à {user.email}")
            print(f"✅ Email de réinitialisation envoyé à {user.email}")
            return True
        else:
            logger.error(f"Échec de l'envoi de l'email de réinitialisation à {user.email}")
            print(f"❌ Échec de l'envoi de l'email de réinitialisation à {user.email}")
            return False

    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de réinitialisation à {user.email}: {str(e)}")
        print(f"❌ Erreur lors de l'envoi de l'email de réinitialisation: {e}")
        return False

from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.template.loader import render_to_string
from .models_activation import AccountActivation
import logging

logger = logging.getLogger(__name__)

def send_activation_email(user):
    """
    Envoyer un email d'activation de compte
    """
    try:
        # Créer ou récupérer l'activation
        activation, created = AccountActivation.objects.get_or_create(
            user=user,
            defaults={'is_activated': False}
        )
        
        # Si déjà activé, ne pas renvoyer
        if activation.is_activated:
            return False, "Compte déjà activé"
        
        # Construire l'URL d'activation
        activation_url = f"{settings.FRONTEND_URL}/activate-account?token={activation.activation_token}"
        
        # Préparer le contenu de l'email
        subject = "🔐 Activez votre compte JOSNET NETWORK"
        
        # Message HTML
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Activation de compte JOSNET</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb;">JOSNET NETWORK</h1>
                    <p style="color: #666;">Votre partenaire technologique de confiance</p>
                </div>
                
                <div style="background: #f0f9ff; padding: 30px; border-radius: 10px; margin-bottom: 30px;">
                    <h2 style="color: #1e40af; margin-bottom: 20px;">Bienvenue {user.first_name or 'cher utilisateur'} !</h2>
                    
                    <p>Merci de vous être inscrit sur <strong>JOSNET NETWORK</strong>. Pour finaliser votre inscription et activer votre compte, cliquez sur le bouton ci-dessous :</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{activation_url}" 
                           style="background: #10b981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px;">
                            🚀 Activer mon compte
                        </a>
                    </div>
                    
                    <p style="color: #666; font-size: 14px;">
                        Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :<br>
                        <a href="{activation_url}" style="color: #10b981; word-break: break-all;">{activation_url}</a>
                    </p>
                </div>
                
                <div style="background: #fef3c7; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <p style="margin: 0; color: #92400e;">
                        <strong>⚠️ Important :</strong> Ce lien expire dans 24 heures. Après activation, vous pourrez vous connecter immédiatement.
                    </p>
                </div>
                
                <div style="background: #f3f4f6; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <h3 style="color: #374151; margin-top: 0;">Après activation, vous pourrez :</h3>
                    <ul style="color: #374151;">
                        <li>🛒 Parcourir notre catalogue de produits</li>
                        <li>💼 Accéder à votre espace personnel</li>
                        <li>📞 Contacter notre support technique</li>
                        <li>🎯 Recevoir des offres personnalisées</li>
                    </ul>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 12px; border-top: 1px solid #e5e7eb; padding-top: 20px;">
                    <p>Cet email a été envoyé par JOSNET NETWORK</p>
                    <p>Si vous n'avez pas créé de compte, ignorez cet email.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Message texte (fallback)
        text_message = f"""
        Bienvenue sur JOSNET NETWORK !
        
        Bonjour {user.first_name or 'cher utilisateur'},
        
        Merci de vous être inscrit sur notre plateforme. Pour activer votre compte, 
        cliquez sur le lien ci-dessous :
        
        {activation_url}
        
        Ce lien expire dans 24 heures.
        
        Après activation, vous pourrez vous connecter et accéder à tous nos services.
        
        Si vous n'avez pas créé de compte, ignorez cet email.
        
        Cordialement,
        L'équipe JOSNET NETWORK
        """
        
        # Envoyer l'email
        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_message, "text/html")
        
        result = msg.send()
        
        if result:
            logger.info(f"Email d'activation envoyé avec succès à {user.email}")
            return True, "Email d'activation envoyé"
        else:
            logger.error(f"Échec de l'envoi de l'email d'activation à {user.email}")
            return False, "Erreur lors de l'envoi de l'email"
            
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email d'activation à {user.email}: {str(e)}")
        return False, f"Erreur: {str(e)}"

def send_welcome_email_after_activation(user):
    """
    Envoyer un email de bienvenue après activation
    """
    try:
        subject = "🎉 Votre compte JOSNET NETWORK est activé !"
        
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb;">JOSNET NETWORK</h1>
                </div>
                
                <div style="background: #f0fdf4; padding: 30px; border-radius: 10px; border: 2px solid #10b981;">
                    <h2 style="color: #059669; text-align: center;">🎉 Compte activé avec succès !</h2>
                    
                    <p>Félicitations <strong>{user.first_name}</strong> !</p>
                    
                    <p>Votre compte JOSNET NETWORK est maintenant <strong>activé</strong> et prêt à être utilisé.</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{settings.FRONTEND_URL}/login" 
                           style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                            🚀 Se connecter maintenant
                        </a>
                    </div>
                    
                    <div style="background: #eff6ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
                        <h3 style="color: #1e40af; margin-top: 0;">Vos informations de connexion :</h3>
                        <p style="margin: 5px 0;"><strong>Email :</strong> {user.email}</p>
                        <p style="margin: 5px 0;"><strong>Mot de passe :</strong> Celui que vous avez choisi lors de l'inscription</p>
                    </div>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 12px; margin-top: 30px;">
                    <p>Merci de faire confiance à JOSNET NETWORK</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_message = f"""
        Félicitations {user.first_name} !
        
        Votre compte JOSNET NETWORK est maintenant activé et prêt à être utilisé.
        
        Vous pouvez vous connecter sur {settings.FRONTEND_URL}/login
        
        Email : {user.email}
        Mot de passe : Celui que vous avez choisi lors de l'inscription
        
        Merci de faire confiance à JOSNET NETWORK !
        """
        
        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_message, "text/html")
        
        return msg.send()
        
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de bienvenue: {str(e)}")
        return False

def send_activation_email(user):
    """
    Envoyer un email d'activation de compte
    """
    try:
        from .models_activation import AccountActivation

        # Créer ou récupérer l'activation
        activation, created = AccountActivation.objects.get_or_create(
            user=user,
            defaults={'is_activated': False}
        )

        # Si déjà activé, ne pas renvoyer
        if activation.is_activated:
            return False, "Compte déjà activé"

        # Construire l'URL d'activation
        activation_url = f"{settings.FRONTEND_URL}/activate-account?token={activation.activation_token}"

        # Préparer le contenu de l'email
        subject = "🔐 Activez votre compte JOSNET NETWORK"

        # Message HTML
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Activation de compte JOSNET</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb;">JOSNET NETWORK</h1>
                    <p style="color: #666;">Votre partenaire technologique de confiance</p>
                </div>

                <div style="background: #f0f9ff; padding: 30px; border-radius: 10px; margin-bottom: 30px;">
                    <h2 style="color: #1e40af; margin-bottom: 20px;">Bienvenue {user.first_name or 'cher utilisateur'} !</h2>

                    <p>Merci de vous être inscrit sur <strong>JOSNET NETWORK</strong>. Pour finaliser votre inscription et activer votre compte, cliquez sur le bouton ci-dessous :</p>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{activation_url}"
                           style="background: #10b981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px;">
                            🚀 Activer mon compte
                        </a>
                    </div>

                    <p style="color: #666; font-size: 14px;">
                        Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :<br>
                        <a href="{activation_url}" style="color: #10b981; word-break: break-all;">{activation_url}</a>
                    </p>
                </div>

                <div style="background: #fef3c7; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <p style="margin: 0; color: #92400e;">
                        <strong>⚠️ Important :</strong> Ce lien expire dans 24 heures. Après activation, vous pourrez vous connecter immédiatement.
                    </p>
                </div>

                <div style="background: #f3f4f6; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <h3 style="color: #374151; margin-top: 0;">Après activation, vous pourrez :</h3>
                    <ul style="color: #374151;">
                        <li>🛒 Parcourir notre catalogue de produits</li>
                        <li>💼 Accéder à votre espace personnel</li>
                        <li>📞 Contacter notre support technique</li>
                        <li>🎯 Recevoir des offres personnalisées</li>
                    </ul>
                </div>

                <div style="text-align: center; color: #666; font-size: 12px; border-top: 1px solid #e5e7eb; padding-top: 20px;">
                    <p>Cet email a été envoyé par JOSNET NETWORK</p>
                    <p>Si vous n'avez pas créé de compte, ignorez cet email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Message texte (fallback)
        text_message = f"""
        Bienvenue sur JOSNET NETWORK !

        Bonjour {user.first_name or 'cher utilisateur'},

        Merci de vous être inscrit sur notre plateforme. Pour activer votre compte,
        cliquez sur le lien ci-dessous :

        {activation_url}

        Ce lien expire dans 24 heures.

        Après activation, vous pourrez vous connecter et accéder à tous nos services.

        Si vous n'avez pas créé de compte, ignorez cet email.

        Cordialement,
        L'équipe JOSNET NETWORK
        """

        # Envoyer l'email
        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_message, "text/html")

        result = msg.send()

        if result:
            logger.info(f"Email d'activation envoyé avec succès à {user.email}")
            return True, "Email d'activation envoyé"
        else:
            logger.error(f"Échec de l'envoi de l'email d'activation à {user.email}")
            return False, "Erreur lors de l'envoi de l'email"

    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email d'activation à {user.email}: {str(e)}")
        return False, f"Erreur: {str(e)}"

def send_welcome_email_after_activation(user):
    """
    Envoyer un email de bienvenue après activation
    """
    try:
        subject = "🎉 Votre compte JOSNET NETWORK est activé !"

        html_message = f"""
        <!DOCTYPE html>
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb;">JOSNET NETWORK</h1>
                </div>

                <div style="background: #f0fdf4; padding: 30px; border-radius: 10px; border: 2px solid #10b981;">
                    <h2 style="color: #059669; text-align: center;">🎉 Compte activé avec succès !</h2>

                    <p>Félicitations <strong>{user.first_name}</strong> !</p>

                    <p>Votre compte JOSNET NETWORK est maintenant <strong>activé</strong> et prêt à être utilisé.</p>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{settings.FRONTEND_URL}/login"
                           style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                            🚀 Se connecter maintenant
                        </a>
                    </div>

                    <div style="background: #eff6ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
                        <h3 style="color: #1e40af; margin-top: 0;">Vos informations de connexion :</h3>
                        <p style="margin: 5px 0;"><strong>Email :</strong> {user.email}</p>
                        <p style="margin: 5px 0;"><strong>Mot de passe :</strong> Celui que vous avez choisi lors de l'inscription</p>
                    </div>
                </div>

                <div style="text-align: center; color: #666; font-size: 12px; margin-top: 30px;">
                    <p>Merci de faire confiance à JOSNET NETWORK</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_message = f"""
        Félicitations {user.first_name} !

        Votre compte JOSNET NETWORK est maintenant activé et prêt à être utilisé.

        Vous pouvez vous connecter sur {settings.FRONTEND_URL}/login

        Email : {user.email}
        Mot de passe : Celui que vous avez choisi lors de l'inscription

        Merci de faire confiance à JOSNET NETWORK !
        """

        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_message, "text/html")

        return msg.send()

    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de bienvenue: {str(e)}")
        return False
