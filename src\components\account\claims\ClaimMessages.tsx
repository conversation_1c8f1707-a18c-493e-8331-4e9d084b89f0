import React, { useState, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  Loader2,
  Send,
  Upload,
  X,
  FileText,
  Image as ImageIcon
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { accountApi, ClaimMessage } from '@/services/accountApi';

interface ClaimMessagesProps {
  claimId: string;
}

export function ClaimMessages({ claimId }: ClaimMessagesProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // État local
  const [message, setMessage] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fileError, setFileError] = useState<string | null>(null);
  
  // Récupérer les messages
  const {
    data: messages = [],
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['claimMessages', claimId],
    queryFn: () => accountApi.getClaimMessages(claimId),
    enabled: !!claimId
  });
  
  // Mutation pour envoyer un message
  const sendMessageMutation = useMutation({
    mutationFn: ({ message, files }: { message: string, files?: File[] }) => 
      accountApi.sendClaimMessage(claimId, message, files),
    onSuccess: () => {
      setMessage('');
      setFiles([]);
      setIsSubmitting(false);
      queryClient.invalidateQueries({ queryKey: ['claimMessages', claimId] });
      toast({
        title: 'Message envoyé',
        description: 'Votre message a été envoyé avec succès.',
      });
    },
    onError: (error: any) => {
      setIsSubmitting(false);
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de l\'envoi du message.',
        variant: 'destructive',
      });
    }
  });
  
  // Gérer l'envoi du message
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() && files.length === 0) {
      toast({
        title: 'Message vide',
        description: 'Veuillez saisir un message ou ajouter des pièces jointes.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsSubmitting(true);
    sendMessageMutation.mutate({ message, files: files.length > 0 ? files : undefined });
  };
  
  // Gérer l'upload de fichiers
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles) return;
    
    // Vérifier la taille des fichiers (max 5MB par fichier)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const invalidFiles = Array.from(selectedFiles).filter(file => file.size > maxSize);
    
    if (invalidFiles.length > 0) {
      setFileError(`Certains fichiers dépassent la taille maximale de 5MB: ${invalidFiles.map(f => f.name).join(', ')}`);
      return;
    }
    
    // Vérifier le nombre total de fichiers (max 3 par message)
    if (files.length + selectedFiles.length > 3) {
      setFileError('Vous ne pouvez pas ajouter plus de 3 fichiers par message.');
      return;
    }
    
    setFileError(null);
    setFiles(prev => [...prev, ...Array.from(selectedFiles)]);
  };
  
  // Supprimer un fichier
  const handleRemoveFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    setFileError(null);
  };
  
  // Formater la date
  const formatDate = (dateString: string) => {
    // Simplification pour l'exemple
    return dateString;
  };
  
  // Obtenir l'icône pour un type de fichier
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return <ImageIcon className="h-4 w-4 text-blue-500" />;
    }
    
    if (['pdf', 'doc', 'docx', 'txt'].includes(extension || '')) {
      return <FileText className="h-4 w-4 text-red-500" />;
    }
    
    return <FileText className="h-4 w-4 text-gray-500" />;
  };
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">Messages</CardTitle>
        <CardDescription>
          Communiquez avec notre service client concernant votre réclamation
        </CardDescription>
      </CardHeader>
      
      <CardContent className="p-0">
        {/* Messages */}
        <div className="px-6 py-2">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : isError ? (
            <div className="flex flex-col items-center justify-center py-8">
              <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
              <p className="text-gray-700">Impossible de charger les messages</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => refetch()} 
                className="mt-2"
              >
                Réessayer
              </Button>
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>Aucun message pour le moment.</p>
              <p className="text-sm mt-1">Envoyez un message pour démarrer la conversation.</p>
            </div>
          ) : (
            <div className="space-y-4 max-h-[400px] overflow-y-auto py-4">
              {messages.map((msg: ClaimMessage) => (
                <div 
                  key={msg.id} 
                  className={`flex gap-3 ${msg.sender === 'customer' ? 'justify-end' : ''}`}
                >
                  {msg.sender === 'support' && (
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-primary text-primary-foreground">SC</AvatarFallback>
                    </Avatar>
                  )}
                  
                  <div 
                    className={`max-w-[80%] rounded-lg p-3 ${
                      msg.sender === 'customer' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-gray-100'
                    }`}
                  >
                    <div className="flex justify-between items-start gap-2 mb-1">
                      <span className="font-medium text-sm">
                        {msg.sender === 'customer' ? 'Vous' : 'Service Client'}
                      </span>
                      <span className={`text-xs ${msg.sender === 'customer' ? 'text-primary-foreground/80' : 'text-gray-500'}`}>
                        {formatDate(msg.date)}
                      </span>
                    </div>
                    
                    <p className={`text-sm ${msg.sender === 'customer' ? 'text-primary-foreground' : 'text-gray-700'}`}>
                      {msg.message}
                    </p>
                    
                    {msg.attachments && msg.attachments.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-2">
                        {msg.attachments.map((attachment, index) => (
                          <div 
                            key={index} 
                            className={`text-xs px-2 py-1 rounded flex items-center gap-1 ${
                              msg.sender === 'customer' 
                                ? 'bg-primary-foreground/10 text-primary-foreground' 
                                : 'bg-white text-gray-700 border border-gray-200'
                            }`}
                          >
                            {getFileIcon(attachment)}
                            <span className="max-w-[120px] truncate">{attachment}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  {msg.sender === 'customer' && (
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>Vous</AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Formulaire de message */}
        <div className="border-t p-4">
          <form onSubmit={handleSendMessage} className="space-y-3">
            <Textarea
              placeholder="Écrivez votre message ici..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="min-h-[100px]"
              disabled={isSubmitting}
            />
            
            {fileError && (
              <p className="text-sm text-red-500">{fileError}</p>
            )}
            
            {files.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center gap-1 bg-gray-100 rounded px-2 py-1 text-sm">
                    {getFileIcon(file.name)}
                    <span className="max-w-[150px] truncate">{file.name}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5"
                      onClick={() => handleRemoveFile(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
            
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isSubmitting}
                className="flex items-center gap-1"
              >
                <Upload className="h-4 w-4" />
                Pièce jointe
              </Button>
              
              <Input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={handleFileChange}
                accept="image/jpeg,image/png,image/gif,application/pdf"
                disabled={isSubmitting}
              />
              
              <Button
                type="submit"
                disabled={isSubmitting || (!message.trim() && files.length === 0)}
                className="flex items-center gap-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Envoi...
                  </>
                ) : (
                  <>
                    Envoyer
                    <Send className="h-4 w-4 ml-1" />
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </CardContent>
    </Card>
  );
}
