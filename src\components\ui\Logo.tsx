import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { LogoInline } from './LogoVariants';

interface LogoProps {
  variant?: 'full' | 'compact' | 'icon' | 'text';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  linkTo?: string;
  showTagline?: boolean;
}

const Logo: React.FC<LogoProps> = ({
  variant = 'compact',
  size = 'md',
  className,
  linkTo = '/',
  showTagline = false
}) => {
  const [useInline, setUseInline] = useState(false);

  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12'
  };

  const LogoContent = () => {
    // Utiliser les SVG intégrés comme fallback ou par défaut
    if (useInline || variant === 'text') {
      return (
        <LogoInline
          variant={variant}
          size={size}
          className={className}
          showTagline={showTagline}
        />
      );
    }

    // E<PERSON>yer d'abord les fichiers SVG externes
    switch (variant) {
      case 'full':
        return (
          <div className={cn("flex items-center", className)}>
            <img
              src="/assets/images/josnet-logo.svg"
              alt="JosNet Network"
              className={cn(sizeClasses[size], "w-auto")}
              onError={() => setUseInline(true)}
            />
          </div>
        );

      case 'compact':
        return (
          <div className={cn("flex items-center", className)}>
            <img
              src="/assets/images/josnet-logo-compact.svg"
              alt="JosNet"
              className={cn(sizeClasses[size], "w-auto")}
              onError={() => setUseInline(true)}
            />
          </div>
        );

      case 'icon':
        return (
          <div className={cn("flex items-center", className)}>
            <img
              src="/assets/images/josnet-favicon.svg"
              alt="JosNet"
              className={cn(sizeClasses[size], "w-auto")}
              onError={() => setUseInline(true)}
            />
          </div>
        );

      default:
        return (
          <LogoInline
            variant={variant}
            size={size}
            className={className}
            showTagline={showTagline}
          />
        );
    }
  };

  if (linkTo) {
    return (
      <Link to={linkTo} className="flex items-center hover:opacity-80 transition-opacity">
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
};

export default Logo;
