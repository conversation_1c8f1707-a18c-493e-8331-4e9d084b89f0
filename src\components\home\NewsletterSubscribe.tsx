import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Mail, Check, X, Loader2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import subscriptionApi, { SubscriptionPreferences } from '@/services/subscriptionApi';
import { useAuth } from '@/contexts/AuthContext';

const NewsletterSubscribe: React.FC = () => {
  const [email, setEmail] = useState('');
  const [preferences, setPreferences] = useState<SubscriptionPreferences>({
    promotions: true,
    new_products: true,
    newsletters: true,
    order_updates: false,
  });
  const [isSubscribed, setIsSubscribed] = useState(false);
  
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();

  // Mutation pour s'abonner
  const subscribeMutation = useMutation({
    mutationFn: () => subscriptionApi.subscribe({ 
      email: email || user?.email || '',
      preferences 
    }),
    onSuccess: () => {
      setIsSubscribed(true);
      toast({
        title: '🎉 Abonnement réussi !',
        description: 'Vous recevrez nos newsletters selon vos préférences.',
      });
      
      // Réinitialiser le formulaire pour les utilisateurs anonymes
      if (!isAuthenticated) {
        setEmail('');
        setPreferences({
          promotions: true,
          new_products: true,
          newsletters: true,
          order_updates: false,
        });
      }
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Impossible de s\'abonner.';
      toast({
        title: 'Erreur d\'abonnement',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  // Gérer le changement de préférence
  const handlePreferenceChange = (key: keyof SubscriptionPreferences, checked: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: checked,
    }));
  };

  // Gérer la soumission du formulaire
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const emailToUse = email || user?.email;
    if (!emailToUse) {
      toast({
        title: 'Email requis',
        description: 'Veuillez saisir votre adresse email.',
        variant: 'destructive',
      });
      return;
    }

    // Vérifier qu'au moins une préférence est sélectionnée
    const hasPreferences = Object.values(preferences).some(Boolean);
    if (!hasPreferences) {
      toast({
        title: 'Préférences requises',
        description: 'Veuillez sélectionner au moins un type de contenu.',
        variant: 'destructive',
      });
      return;
    }

    subscribeMutation.mutate();
  };

  // Si l'utilisateur vient de s'abonner, afficher le message de confirmation
  if (isSubscribed) {
    return (
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Check className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Abonnement confirmé !
            </h3>
            <p className="text-green-700 mb-4">
              Merci de vous être abonné à notre newsletter. Vous recevrez bientôt nos dernières actualités et offres exclusives.
            </p>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setIsSubscribed(false)}
              className="border-green-300 text-green-700 hover:bg-green-100"
            >
              S'abonner avec un autre email
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2 text-blue-800">
          <Mail className="h-5 w-5" />
          Restez informé avec notre newsletter
        </CardTitle>
        <CardDescription className="text-blue-700">
          Recevez nos dernières actualités, promotions exclusives et nouveautés directement dans votre boîte mail
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email input (seulement pour les utilisateurs non connectés) */}
          {!isAuthenticated && (
            <div className="space-y-2">
              <Label htmlFor="newsletter-email">Adresse email</Label>
              <Input
                id="newsletter-email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="border-blue-200 focus:border-blue-400"
              />
            </div>
          )}

          {/* Affichage de l'email pour les utilisateurs connectés */}
          {isAuthenticated && user?.email && (
            <div className="p-3 bg-blue-100 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>Email :</strong> {user.email}
              </p>
            </div>
          )}

          {/* Préférences de contenu */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Que souhaitez-vous recevoir ?</Label>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id="pref-promotions"
                  checked={preferences.promotions}
                  onCheckedChange={(checked) => handlePreferenceChange('promotions', checked as boolean)}
                />
                <div className="flex items-center gap-2">
                  <Label htmlFor="pref-promotions" className="cursor-pointer">
                    Promotions et offres spéciales
                  </Label>
                  <Badge variant="secondary" className="bg-red-100 text-red-700 text-xs">
                    🎉 Populaire
                  </Badge>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  id="pref-products"
                  checked={preferences.new_products}
                  onCheckedChange={(checked) => handlePreferenceChange('new_products', checked as boolean)}
                />
                <div className="flex items-center gap-2">
                  <Label htmlFor="pref-products" className="cursor-pointer">
                    Nouveaux produits et innovations
                  </Label>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs">
                    ✨ Nouveauté
                  </Badge>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  id="pref-newsletters"
                  checked={preferences.newsletters}
                  onCheckedChange={(checked) => handlePreferenceChange('newsletters', checked as boolean)}
                />
                <div className="flex items-center gap-2">
                  <Label htmlFor="pref-newsletters" className="cursor-pointer">
                    Newsletter et actualités
                  </Label>
                  <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs">
                    📰 Hebdo
                  </Badge>
                </div>
              </div>

              {isAuthenticated && (
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="pref-orders"
                    checked={preferences.order_updates}
                    onCheckedChange={(checked) => handlePreferenceChange('order_updates', checked as boolean)}
                  />
                  <div className="flex items-center gap-2">
                    <Label htmlFor="pref-orders" className="cursor-pointer">
                      Mises à jour de commandes
                    </Label>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-700 text-xs">
                      📦 Suivi
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Bouton d'abonnement */}
          <Button 
            type="submit" 
            className="w-full bg-blue-600 hover:bg-blue-700"
            disabled={subscribeMutation.isPending}
          >
            {subscribeMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Abonnement en cours...
              </>
            ) : (
              <>
                <Mail className="mr-2 h-4 w-4" />
                S'abonner à la newsletter
              </>
            )}
          </Button>

          {/* Informations légales */}
          <div className="text-xs text-blue-600 space-y-1">
            <p>• Vous pouvez vous désabonner à tout moment</p>
            <p>• Nous respectons votre vie privée et ne partageons jamais vos données</p>
            <p>• Fréquence : 1-2 emails par semaine maximum</p>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default NewsletterSubscribe;
