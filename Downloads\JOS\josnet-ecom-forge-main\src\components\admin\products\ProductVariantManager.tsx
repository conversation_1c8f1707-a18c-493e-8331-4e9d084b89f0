import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle, 
  Check, 
  X, 
  RefreshCw, 
  Wand2 
} from 'lucide-react';
import { 
  productApi, 
  ProductVariant, 
  ProductAttribute, 
  AttributeValue,
  Inventory 
} from '@/services/productApi';
import { formatPrice } from '@/lib/utils';

interface ProductVariantManagerProps {
  productId: number;
  productPrice: number;
}

const ProductVariantManager: React.FC<ProductVariantManagerProps> = ({ 
  productId, 
  productPrice 
}) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch product variants
  const { 
    data: variants = [], 
    isLoading: isLoadingVariants, 
    isError: isVariantsError 
  } = useQuery({
    queryKey: ['productVariants', productId],
    queryFn: () => productApi.getProductVariants(productId),
  });
  
  // Fetch product attributes
  const { 
    data: attributes = [], 
    isLoading: isLoadingAttributes 
  } = useQuery({
    queryKey: ['productAttributes'],
    queryFn: () => productApi.getProductAttributes(),
  });
  
  // Fetch attribute values
  const { 
    data: attributeValues = [], 
    isLoading: isLoadingAttributeValues 
  } = useQuery({
    queryKey: ['attributeValues'],
    queryFn: () => productApi.getAttributeValues(),
  });
  
  // Create variant mutation
  const createVariantMutation = useMutation({
    mutationFn: (data: any) => productApi.createProductVariant(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variant created',
        description: 'The product variant has been created successfully.',
      });
      setIsCreateDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to create variant',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Generate variants mutation
  const generateVariantsMutation = useMutation({
    mutationFn: (data: any) => productApi.generateProductVariants(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variants generated',
        description: 'The product variants have been generated successfully.',
      });
      setIsGenerateDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to generate variants',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Update variant mutation
  const updateVariantMutation = useMutation({
    mutationFn: (data: { id: number, variant: any }) => 
      productApi.updateProductVariant(data.id, data.variant),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variant updated',
        description: 'The product variant has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to update variant',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Delete variant mutation
  const deleteVariantMutation = useMutation({
    mutationFn: (variantId: number) => productApi.deleteProductVariant(variantId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variant deleted',
        description: 'The product variant has been deleted successfully.',
      });
      setIsDeleteDialogOpen(false);
      setSelectedVariant(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to delete variant',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Handle variant deletion
  const handleDeleteVariant = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle variant status toggle
  const handleToggleStatus = (variant: ProductVariant) => {
    updateVariantMutation.mutate({
      id: variant.id,
      variant: {
        ...variant,
        is_active: !variant.is_active
      }
    });
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Variants</CardTitle>
        <CardDescription>
          Manage product variants with different attributes, prices, and inventory.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={() => setIsCreateDialogOpen(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Variant</span>
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => setIsGenerateDialogOpen(true)}
            className="gap-2"
          >
            <Wand2 className="h-4 w-4" />
            <span>Generate Variants</span>
          </Button>
        </div>
        
        {/* Variants table */}
        {isLoadingVariants ? (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : isVariantsError ? (
          <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span>Failed to load variants. Please try again.</span>
          </div>
        ) : variants.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 bg-gray-50 border border-dashed rounded-md">
            <p className="text-sm text-gray-500 mb-4">No variants yet. Add some variants or generate them automatically.</p>
            <div className="flex gap-2">
              <Button 
                onClick={() => setIsCreateDialogOpen(true)}
                size="sm"
              >
                Add Variant
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setIsGenerateDialogOpen(true)}
                size="sm"
              >
                Generate Variants
              </Button>
            </div>
          </div>
        ) : (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Attributes</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {variants.map((variant) => (
                  <TableRow key={variant.id}>
                    <TableCell className="font-medium">{variant.name}</TableCell>
                    <TableCell>{variant.sku}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {variant.attributes.map((attr) => (
                          <span 
                            key={attr.id} 
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100"
                          >
                            {attr.attribute_name}: {attr.value}
                          </span>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      {variant.price_adjustment !== 0 ? (
                        <div>
                          <div>{formatPrice(variant.price)}</div>
                          <div className="text-xs text-gray-500">
                            {variant.price_adjustment > 0 ? '+' : ''}{formatPrice(variant.price_adjustment)}
                          </div>
                        </div>
                      ) : (
                        formatPrice(variant.price)
                      )}
                    </TableCell>
                    <TableCell>
                      {variant.inventory ? (
                        <div>
                          <div>{variant.inventory.available_quantity} available</div>
                          {variant.inventory.is_low_stock && (
                            <div className="text-xs text-amber-600">Low stock</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500">No inventory</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`gap-2 ${variant.is_active ? 'text-green-600' : 'text-red-600'}`}
                        onClick={() => handleToggleStatus(variant)}
                      >
                        {variant.is_active ? (
                          <>
                            <Check className="h-4 w-4" />
                            <span>Active</span>
                          </>
                        ) : (
                          <>
                            <X className="h-4 w-4" />
                            <span>Inactive</span>
                          </>
                        )}
                      </Button>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedVariant(variant);
                            setIsCreateDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-600"
                          onClick={() => handleDeleteVariant(variant)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      
      {/* Create/Edit Variant Dialog */}
      <VariantFormDialog 
        isOpen={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        productId={productId}
        productPrice={productPrice}
        variant={selectedVariant}
        attributes={attributes}
        attributeValues={attributeValues}
        onSubmit={(data) => {
          if (selectedVariant) {
            updateVariantMutation.mutate({
              id: selectedVariant.id,
              variant: data
            });
          } else {
            createVariantMutation.mutate(data);
          }
        }}
        isLoading={createVariantMutation.isPending || updateVariantMutation.isPending}
        onClose={() => setSelectedVariant(null)}
      />
      
      {/* Generate Variants Dialog */}
      <GenerateVariantsDialog 
        isOpen={isGenerateDialogOpen}
        onOpenChange={setIsGenerateDialogOpen}
        productId={productId}
        attributes={attributes}
        attributeValues={attributeValues}
        onSubmit={(data) => {
          generateVariantsMutation.mutate(data);
        }}
        isLoading={generateVariantsMutation.isPending}
      />
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the variant "{selectedVariant?.name}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedVariant(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedVariant && deleteVariantMutation.mutate(selectedVariant.id)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

interface VariantFormDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  productId: number;
  productPrice: number;
  variant: ProductVariant | null;
  attributes: ProductAttribute[];
  attributeValues: AttributeValue[];
  onSubmit: (data: any) => void;
  isLoading: boolean;
  onClose: () => void;
}

const VariantFormDialog: React.FC<VariantFormDialogProps> = ({
  isOpen,
  onOpenChange,
  productId,
  productPrice,
  variant,
  attributes,
  attributeValues,
  onSubmit,
  isLoading,
  onClose
}) => {
  const [name, setName] = useState('');
  const [sku, setSku] = useState('');
  const [priceAdjustment, setPriceAdjustment] = useState('0');
  const [isActive, setIsActive] = useState(true);
  const [selectedAttributes, setSelectedAttributes] = useState<Record<number, number>>({});
  
  // Reset form when dialog opens or variant changes
  useEffect(() => {
    if (isOpen) {
      if (variant) {
        setName(variant.name);
        setSku(variant.sku);
        setPriceAdjustment(variant.price_adjustment.toString());
        setIsActive(variant.is_active);
        
        // Set selected attributes
        const attrMap: Record<number, number> = {};
        variant.attributes.forEach(attr => {
          attrMap[attr.attribute] = attr.id;
        });
        setSelectedAttributes(attrMap);
      } else {
        setName('');
        setSku('');
        setPriceAdjustment('0');
        setIsActive(true);
        setSelectedAttributes({});
      }
    }
  }, [isOpen, variant]);
  
  const handleClose = () => {
    onOpenChange(false);
    onClose();
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const attributeValueIds = Object.values(selectedAttributes);
    
    onSubmit({
      product: productId,
      name,
      sku: sku || undefined,
      price_adjustment: parseFloat(priceAdjustment) || 0,
      is_active: isActive,
      attribute_values: attributeValueIds
    });
  };
  
  const filteredAttributeValues = (attributeId: number) => {
    return attributeValues.filter(av => av.attribute === attributeId);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{variant ? 'Edit Variant' : 'Add Variant'}</DialogTitle>
          <DialogDescription>
            {variant 
              ? 'Edit the details of this product variant.' 
              : 'Create a new variant for this product with different attributes.'}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder="e.g. Red / Small"
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="sku" className="text-right">
                SKU
              </Label>
              <Input
                id="sku"
                value={sku}
                onChange={(e) => setSku(e.target.value)}
                className="col-span-3"
                placeholder="Leave blank to generate automatically"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="price-adjustment" className="text-right">
                Price Adjustment
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="price-adjustment"
                  type="number"
                  step="0.01"
                  value={priceAdjustment}
                  onChange={(e) => setPriceAdjustment(e.target.value)}
                />
                <div className="text-sm text-gray-500">
                  Final: {formatPrice(productPrice + parseFloat(priceAdjustment || '0'))}
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Status
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Checkbox
                  id="is-active"
                  checked={isActive}
                  onCheckedChange={(checked) => setIsActive(checked as boolean)}
                />
                <label
                  htmlFor="is-active"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Active
                </label>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <Label className="text-right pt-2">
                Attributes
              </Label>
              <div className="col-span-3 space-y-4">
                {attributes.length === 0 ? (
                  <p className="text-sm text-gray-500">No attributes available.</p>
                ) : (
                  attributes.map((attribute) => (
                    <div key={attribute.id} className="space-y-2">
                      <Label htmlFor={`attribute-${attribute.id}`}>
                        {attribute.name}
                      </Label>
                      <Select
                        value={selectedAttributes[attribute.id]?.toString() || ''}
                        onValueChange={(value) => {
                          setSelectedAttributes({
                            ...selectedAttributes,
                            [attribute.id]: parseInt(value)
                          });
                        }}
                      >
                        <SelectTrigger id={`attribute-${attribute.id}`}>
                          <SelectValue placeholder={`Select ${attribute.name}`} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">None</SelectItem>
                          {filteredAttributeValues(attribute.id).map((value) => (
                            <SelectItem key={value.id} value={value.id.toString()}>
                              {value.value}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  {variant ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                variant ? 'Update Variant' : 'Create Variant'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

interface GenerateVariantsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  productId: number;
  attributes: ProductAttribute[];
  attributeValues: AttributeValue[];
  onSubmit: (data: any) => void;
  isLoading: boolean;
}

const GenerateVariantsDialog: React.FC<GenerateVariantsDialogProps> = ({
  isOpen,
  onOpenChange,
  productId,
  attributes,
  attributeValues,
  onSubmit,
  isLoading
}) => {
  const [selectedAttributeGroups, setSelectedAttributeGroups] = useState<Record<number, number[]>>({});
  const [priceAdjustment, setPriceAdjustment] = useState('0');
  const [isActive, setIsActive] = useState(true);
  
  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedAttributeGroups({});
      setPriceAdjustment('0');
      setIsActive(true);
    }
  }, [isOpen]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Convert selected attribute groups to the format expected by the API
    const attributeValueGroups = Object.values(selectedAttributeGroups)
      .filter(group => group.length > 0);
    
    if (attributeValueGroups.length === 0) {
      return;
    }
    
    onSubmit({
      product: productId,
      attribute_values: attributeValueGroups,
      price_adjustment: parseFloat(priceAdjustment) || 0,
      is_active: isActive
    });
  };
  
  const handleAttributeChange = (attributeId: number, valueId: number, checked: boolean) => {
    setSelectedAttributeGroups(prev => {
      const currentValues = prev[attributeId] || [];
      
      if (checked) {
        return {
          ...prev,
          [attributeId]: [...currentValues, valueId]
        };
      } else {
        return {
          ...prev,
          [attributeId]: currentValues.filter(id => id !== valueId)
        };
      }
    });
  };
  
  const filteredAttributeValues = (attributeId: number) => {
    return attributeValues.filter(av => av.attribute === attributeId);
  };
  
  // Calculate total number of variants that will be generated
  const calculateTotalVariants = () => {
    const groups = Object.values(selectedAttributeGroups)
      .filter(group => group.length > 0);
    
    if (groups.length === 0) return 0;
    
    return groups.reduce((total, group) => total * group.length, 1);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Generate Variants</DialogTitle>
          <DialogDescription>
            Generate all possible combinations of variants based on selected attributes.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="price-adjustment" className="text-right">
                Price Adjustment
              </Label>
              <Input
                id="price-adjustment"
                type="number"
                step="0.01"
                value={priceAdjustment}
                onChange={(e) => setPriceAdjustment(e.target.value)}
                className="col-span-3"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Status
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Checkbox
                  id="is-active-generate"
                  checked={isActive}
                  onCheckedChange={(checked) => setIsActive(checked as boolean)}
                />
                <label
                  htmlFor="is-active-generate"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Active
                </label>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <Label className="text-right pt-2">
                Attributes
              </Label>
              <div className="col-span-3">
                <Tabs defaultValue={attributes[0]?.id.toString()}>
                  <TabsList className="mb-2">
                    {attributes.map((attribute) => (
                      <TabsTrigger key={attribute.id} value={attribute.id.toString()}>
                        {attribute.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  
                  {attributes.map((attribute) => (
                    <TabsContent key={attribute.id} value={attribute.id.toString()} className="space-y-4">
                      <div className="grid grid-cols-2 gap-2">
                        {filteredAttributeValues(attribute.id).map((value) => (
                          <div key={value.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`value-${value.id}`}
                              checked={(selectedAttributeGroups[attribute.id] || []).includes(value.id)}
                              onCheckedChange={(checked) => 
                                handleAttributeChange(attribute.id, value.id, checked as boolean)
                              }
                            />
                            <label
                              htmlFor={`value-${value.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {value.value}
                            </label>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <div></div>
              <div className="col-span-3">
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm font-medium">
                    This will generate {calculateTotalVariants()} variants.
                  </p>
                  {calculateTotalVariants() > 20 && (
                    <p className="text-xs text-amber-600 mt-1">
                      Warning: Generating a large number of variants may take some time.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || calculateTotalVariants() === 0}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Variants'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ProductVariantManager;
