<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}JosNet{% endblock %}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .button {
            display: inline-block;
            background-color: #e74c3c;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .button:hover {
            background-color: #c0392b;
        }
        .footer {
            border-top: 1px solid #eee;
            padding-top: 20px;
            margin-top: 30px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .order-details {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">JosNet</div>
            <p>Votre marketplace de confiance</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Bonjour {{ user_name }},
            </div>
            
            {% block content %}
            {% endblock %}
        </div>
        
        <div class="footer">
            <p>Cordialement,<br>L'équipe JosNet</p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
            <p>
                <a href="{{ site_url }}/account/notifications" style="color: #666;">Modifier mes préférences de notification</a> |
                <a href="{{ site_url }}" style="color: #666;">Visiter JosNet</a>
            </p>
            <p>© 2024 JosNet. Tous droits réservés.</p>
        </div>
    </div>
</body>
</html>
