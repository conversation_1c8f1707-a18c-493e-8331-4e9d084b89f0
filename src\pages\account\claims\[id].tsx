import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import AccountLayout from '@/components/account/AccountLayout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Loader2, ChevronLeft, Send, AlertCircle, FileText, Paperclip } from 'lucide-react';
import { accountApi, ClaimDetails, ClaimMessage } from '@/services/accountApi';
import { useToast } from '@/hooks/use-toast';
import { getSafeImageUrl } from '@/utils/imageUtils';

const ClaimDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Vérifier si l'utilisateur est authentifié
  const isAuthenticated = !!localStorage.getItem('token');

  // Fetch claim details
  const {
    data: claimDetails,
    isLoading: isLoadingClaim,
    isError: isErrorClaim,
    refetch: refetchClaim
  } = useQuery({
    queryKey: ['claim', id],
    queryFn: () => accountApi.getClaimDetails(id || ''),
    enabled: !!id && isAuthenticated // Désactiver la requête si l'utilisateur n'est pas authentifié
  });

  // Extract messages from claim details
  const messages = claimDetails?.messages || [];

  // Mutation for sending messages
  const sendMessageMutation = useMutation({
    mutationFn: (message: string) => {
      if (!id) throw new Error('ID de réclamation manquant');
      return accountApi.sendClaimMessage(id, message);
    },
    onSuccess: () => {
      toast({
        title: 'Message envoyé',
        description: 'Votre message a été envoyé avec succès.'
      });
      setNewMessage('');
      refetchClaim();
    },
    onError: () => {
      toast({
        title: 'Erreur',
        description: 'Une erreur est survenue lors de l\'envoi du message.',
        variant: 'destructive'
      });
    }
  });

  // Handle sending a new message
  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;
    
    setIsSending(true);
    try {
      await sendMessageMutation.mutateAsync(newMessage);
    } finally {
      setIsSending(false);
    }
  };

  // Format status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Résolu':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">{status}</Badge>;
      case 'En cours':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">{status}</Badge>;
      case 'Nouveau':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">{status}</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">{status}</Badge>;
    }
  };

  // Handle image error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = '/placeholder.svg';
  };

  if (isLoadingClaim) {
    return (
      <AccountLayout title="Détails de la réclamation">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 text-primary animate-spin" />
          <span className="ml-2">Chargement des détails...</span>
        </div>
      </AccountLayout>
    );
  }

  if (isErrorClaim || !claimDetails) {
    return (
      <AccountLayout title="Détails de la réclamation">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10 text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
            <p className="text-gray-500 max-w-sm mb-4">
              Une erreur est survenue lors du chargement de la réclamation. Veuillez réessayer.
            </p>
            <div className="flex gap-3">
              <Button variant="outline" onClick={() => navigate('/account/claims')}>
                <ChevronLeft className="mr-2 h-4 w-4" />
                Retour
              </Button>
              <Button onClick={() => refetchClaim()}>
                Réessayer
              </Button>
            </div>
          </CardContent>
        </Card>
      </AccountLayout>
    );
  }

  return (
    <AccountLayout title="Détails de la réclamation">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Button variant="outline" onClick={() => navigate('/account/claims')}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Retour aux réclamations
          </Button>
          {getStatusBadge(claimDetails.status)}
        </div>
        
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-xl">{claimDetails.subject}</CardTitle>
                <CardDescription className="mt-1">
                  Commande #{claimDetails.orderId} • Créée le {claimDetails.date}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <p className="text-gray-600">{claimDetails.description}</p>
              </div>
              
              {claimDetails.product && (
                <div>
                  <h3 className="font-medium mb-2">Produit concerné</h3>
                  <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-md">
                    <div className="flex-shrink-0 w-16 h-16 relative">
                      <img 
                        src={getSafeImageUrl(claimDetails.product.image, claimDetails.product.id)} 
                        alt={claimDetails.product.name}
                        className="w-full h-full object-cover rounded-md"
                        onError={handleImageError}
                      />
                    </div>
                    <div>
                      <h4 className="font-medium">{claimDetails.product.name}</h4>
                      <p className="text-sm text-gray-500">ID: {claimDetails.product.id}</p>
                    </div>
                  </div>
                </div>
              )}

              <div>
                <h3 className="font-medium mb-3">Messages</h3>
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <p className="text-gray-500 italic">Aucun message pour le moment.</p>
                  ) : (
                    messages.map((msg) => (
                      <div key={msg.id} className={`flex ${msg.sender === 'customer' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-[80%] ${msg.sender === 'customer' ? 'bg-primary text-primary-foreground' : 'bg-gray-100'} rounded-lg px-4 py-3`}>
                          <div className="flex items-center space-x-2 mb-1">
                            <Avatar className="h-6 w-6">
                              <AvatarFallback>{msg.sender === 'customer' ? 'CL' : 'SP'}</AvatarFallback>
                            </Avatar>
                            <span className="text-xs font-medium">
                              {msg.sender === 'customer' ? 'Vous' : 'Support'}
                            </span>
                            <span className="text-xs opacity-70">{msg.date}</span>
                          </div>
                          <p className={`text-sm ${msg.sender === 'customer' ? 'text-primary-foreground' : 'text-gray-800'}`}>
                            {msg.message}
                          </p>
                          {msg.attachments && msg.attachments.length > 0 && (
                            <div className="mt-2 space-y-1">
                              {msg.attachments.map((attachment, index) => (
                                <div key={index} className="flex items-center text-xs">
                                  <Paperclip className="h-3 w-3 mr-1" />
                                  <span>{attachment}</span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="font-medium mb-3">Répondre</h3>
                <div className="space-y-3">
                  <Textarea 
                    placeholder="Écrivez votre message ici..." 
                    className="min-h-[100px]" 
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                  />
                  <div className="flex justify-end">
                    <Button 
                      onClick={handleSendMessage} 
                      disabled={isSending || !newMessage.trim()}
                    >
                      {isSending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Envoi...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Envoyer
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t pt-6 flex justify-between">
            <div className="text-sm text-gray-500">
              <p>Dernière mise à jour: {claimDetails.lastUpdate}</p>
            </div>
            <div>
              <Button 
                variant="outline" 
                className="flex items-center" 
                onClick={() => navigate('/contact')}>
                Contacter le support
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </AccountLayout>
  );
};

export default ClaimDetail;
