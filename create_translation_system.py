#!/usr/bin/env python3
"""
Script pour créer et initialiser le système de traduction complet
"""

import os
import sys
import subprocess
import json

def run_command(command, description):
    """Exécuter une commande et afficher le résultat"""
    print(f"\n🔧 {description}")
    print(f"Commande: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd='josnet_backend')
        
        if result.returncode == 0:
            print(f"✅ Succès: {description}")
            if result.stdout:
                print(f"Sortie: {result.stdout}")
        else:
            print(f"❌ Erreur: {description}")
            print(f"Erreur: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False
    
    return True

def install_dependencies():
    """Installer les dépendances Python nécessaires"""
    print("📦 INSTALLATION DES DÉPENDANCES")
    print("=" * 50)
    
    dependencies = [
        'django-rosetta',
        'polib',
    ]
    
    for dep in dependencies:
        success = run_command(f"pip install {dep}", f"Installation de {dep}")
        if not success:
            print(f"⚠️ Échec de l'installation de {dep}")

def create_migrations():
    """Créer les migrations pour l'app translations"""
    print("\n📋 CRÉATION DES MIGRATIONS")
    print("=" * 50)
    
    commands = [
        ("python manage.py makemigrations translations", "Création des migrations translations"),
        ("python manage.py migrate", "Application des migrations"),
    ]
    
    for command, description in commands:
        success = run_command(command, description)
        if not success:
            print(f"⚠️ Échec: {description}")
            return False
    
    return True

def create_locale_directories():
    """Créer les répertoires de locale"""
    print("\n📁 CRÉATION DES RÉPERTOIRES DE LOCALE")
    print("=" * 50)
    
    locale_dir = "josnet_backend/locale"
    languages = ['fr', 'en', 'sw', 'rn']
    
    try:
        os.makedirs(locale_dir, exist_ok=True)
        print(f"✅ Répertoire créé: {locale_dir}")
        
        for lang in languages:
            lang_dir = os.path.join(locale_dir, lang, 'LC_MESSAGES')
            os.makedirs(lang_dir, exist_ok=True)
            print(f"✅ Répertoire créé: {lang_dir}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la création des répertoires: {e}")
        return False

def generate_translation_files():
    """Générer les fichiers de traduction Django"""
    print("\n🌐 GÉNÉRATION DES FICHIERS DE TRADUCTION")
    print("=" * 50)
    
    languages = ['en', 'sw', 'rn']  # fr est la langue par défaut
    
    for lang in languages:
        success = run_command(
            f"python manage.py makemessages -l {lang}",
            f"Génération des messages pour {lang}"
        )
        if not success:
            print(f"⚠️ Échec pour la langue {lang}")

def create_initial_translations():
    """Créer les traductions initiales"""
    print("\n📝 CRÉATION DES TRADUCTIONS INITIALES")
    print("=" * 50)
    
    initial_translations = [
        {
            'key': 'common.loading',
            'category': 'interface',
            'description': 'Message de chargement',
            'translations': {
                'fr': 'Chargement...',
                'en': 'Loading...',
                'sw': 'Inapakia...',
                'rn': 'Gushakisha...'
            }
        },
        {
            'key': 'common.error',
            'category': 'interface',
            'description': 'Message d\'erreur générique',
            'translations': {
                'fr': 'Erreur',
                'en': 'Error',
                'sw': 'Hitilafu',
                'rn': 'Ikosa'
            }
        },
        {
            'key': 'common.success',
            'category': 'interface',
            'description': 'Message de succès',
            'translations': {
                'fr': 'Succès',
                'en': 'Success',
                'sw': 'Mafanikio',
                'rn': 'Intsinzi'
            }
        },
        {
            'key': 'common.save',
            'category': 'actions',
            'description': 'Bouton sauvegarder',
            'translations': {
                'fr': 'Enregistrer',
                'en': 'Save',
                'sw': 'Hifadhi',
                'rn': 'Bika'
            }
        },
        {
            'key': 'common.cancel',
            'category': 'actions',
            'description': 'Bouton annuler',
            'translations': {
                'fr': 'Annuler',
                'en': 'Cancel',
                'sw': 'Ghairi',
                'rn': 'Kuraguza'
            }
        },
        {
            'key': 'common.delete',
            'category': 'actions',
            'description': 'Bouton supprimer',
            'translations': {
                'fr': 'Supprimer',
                'en': 'Delete',
                'sw': 'Futa',
                'rn': 'Gusiba'
            }
        },
        {
            'key': 'nav.home',
            'category': 'navigation',
            'description': 'Lien accueil',
            'translations': {
                'fr': 'Accueil',
                'en': 'Home',
                'sw': 'Nyumbani',
                'rn': 'Ahabanza'
            }
        },
        {
            'key': 'nav.products',
            'category': 'navigation',
            'description': 'Lien produits',
            'translations': {
                'fr': 'Produits',
                'en': 'Products',
                'sw': 'Bidhaa',
                'rn': 'Ibicuruzwa'
            }
        },
        {
            'key': 'nav.cart',
            'category': 'navigation',
            'description': 'Lien panier',
            'translations': {
                'fr': 'Panier',
                'en': 'Cart',
                'sw': 'Kikapu',
                'rn': 'Agasanduku'
            }
        },
        {
            'key': 'nav.account',
            'category': 'navigation',
            'description': 'Lien compte',
            'translations': {
                'fr': 'Compte',
                'en': 'Account',
                'sw': 'Akaunti',
                'rn': 'Konti'
            }
        },
        {
            'key': 'auth.login',
            'category': 'authentication',
            'description': 'Connexion',
            'translations': {
                'fr': 'Connexion',
                'en': 'Login',
                'sw': 'Ingia',
                'rn': 'Kwinjira'
            }
        },
        {
            'key': 'auth.register',
            'category': 'authentication',
            'description': 'Inscription',
            'translations': {
                'fr': 'Inscription',
                'en': 'Register',
                'sw': 'Jisajili',
                'rn': 'Kwiyandikisha'
            }
        },
        {
            'key': 'auth.logout',
            'category': 'authentication',
            'description': 'Déconnexion',
            'translations': {
                'fr': 'Déconnexion',
                'en': 'Logout',
                'sw': 'Toka',
                'rn': 'Gusohoka'
            }
        },
        {
            'key': 'product.price',
            'category': 'ecommerce',
            'description': 'Prix',
            'translations': {
                'fr': 'Prix',
                'en': 'Price',
                'sw': 'Bei',
                'rn': 'Igiciro'
            }
        },
        {
            'key': 'product.quantity',
            'category': 'ecommerce',
            'description': 'Quantité',
            'translations': {
                'fr': 'Quantité',
                'en': 'Quantity',
                'sw': 'Kiasi',
                'rn': 'Ingano'
            }
        },
        {
            'key': 'order.status',
            'category': 'ecommerce',
            'description': 'Statut de commande',
            'translations': {
                'fr': 'Statut',
                'en': 'Status',
                'sw': 'Hali',
                'rn': 'Uko'
            }
        }
    ]
    
    # Sauvegarder dans un fichier JSON pour l'import
    with open('josnet_backend/initial_translations.json', 'w', encoding='utf-8') as f:
        json.dump(initial_translations, f, ensure_ascii=False, indent=2)
    
    print("✅ Traductions initiales sauvegardées dans initial_translations.json")
    return True

def create_management_command():
    """Créer une commande de gestion pour importer les traductions"""
    print("\n⚙️ CRÉATION DE LA COMMANDE DE GESTION")
    print("=" * 50)
    
    management_dir = "josnet_backend/translations/management"
    commands_dir = os.path.join(management_dir, "commands")
    
    try:
        os.makedirs(commands_dir, exist_ok=True)
        
        # Créer __init__.py
        with open(os.path.join(management_dir, "__init__.py"), 'w') as f:
            f.write("")
        
        with open(os.path.join(commands_dir, "__init__.py"), 'w') as f:
            f.write("")
        
        # Créer la commande import_translations
        command_content = '''from django.core.management.base import BaseCommand
from translations.utils import bulk_create_translations
import json
import os

class Command(BaseCommand):
    help = 'Importer les traductions depuis un fichier JSON'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='initial_translations.json',
            help='Fichier JSON contenant les traductions'
        )

    def handle(self, *args, **options):
        file_path = options['file']
        
        if not os.path.exists(file_path):
            self.stdout.write(
                self.style.ERROR(f'Fichier non trouvé: {file_path}')
            )
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                translations_data = json.load(f)
            
            stats = bulk_create_translations(translations_data)
            
            self.stdout.write(
                self.style.SUCCESS(f'Import terminé avec succès!')
            )
            self.stdout.write(f'Clés créées: {stats["keys_created"]}')
            self.stdout.write(f'Clés mises à jour: {stats["keys_updated"]}')
            self.stdout.write(f'Traductions créées: {stats["translations_created"]}')
            self.stdout.write(f'Traductions mises à jour: {stats["translations_updated"]}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Erreur lors de l\\'import: {e}')
            )
'''
        
        with open(os.path.join(commands_dir, "import_translations.py"), 'w') as f:
            f.write(command_content)
        
        print("✅ Commande de gestion créée")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de la commande: {e}")
        return False

def main():
    print("🌐 INSTALLATION DU SYSTÈME DE TRADUCTION COMPLET")
    print("=" * 70)
    print("Ce script va installer et configurer le système de traduction pour JosNet")
    print()
    
    steps = [
        ("Installation des dépendances", install_dependencies),
        ("Création des répertoires de locale", create_locale_directories),
        ("Création des migrations", create_migrations),
        ("Génération des fichiers de traduction", generate_translation_files),
        ("Création des traductions initiales", create_initial_translations),
        ("Création de la commande de gestion", create_management_command),
    ]
    
    success_count = 0
    
    for step_name, step_function in steps:
        print(f"\n{'='*20} {step_name.upper()} {'='*20}")
        
        try:
            if step_function():
                success_count += 1
                print(f"✅ {step_name} terminé avec succès")
            else:
                print(f"❌ {step_name} a échoué")
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    print(f"\n{'='*70}")
    print(f"📊 RÉSUMÉ: {success_count}/{len(steps)} étapes réussies")
    
    if success_count == len(steps):
        print("\n🎉 INSTALLATION COMPLÈTE RÉUSSIE!")
        print("\n📋 PROCHAINES ÉTAPES:")
        print("1. Redémarrez le serveur Django")
        print("2. Importez les traductions initiales:")
        print("   cd josnet_backend")
        print("   python manage.py import_translations")
        print("3. Accédez à l'interface d'administration:")
        print("   http://localhost:8000/admin/translations/")
        print("4. Accédez à Rosetta pour la traduction:")
        print("   http://localhost:8000/rosetta/")
        print("5. Testez le sélecteur de langue sur le frontend")
        
        print("\n🔧 FONCTIONNALITÉS DISPONIBLES:")
        print("• Traduction automatique des textes")
        print("• Interface d'administration des traductions")
        print("• API REST pour les traductions")
        print("• Sélecteur de langue dans la navigation")
        print("• Cache des traductions pour les performances")
        print("• Détection automatique des traductions manquantes")
        print("• Support de 4 langues: Français, English, Kiswahili, Kirundi")
        
    else:
        print("\n⚠️ INSTALLATION PARTIELLE")
        print("Certaines étapes ont échoué. Vérifiez les erreurs ci-dessus.")
        print("Vous pouvez relancer le script ou effectuer les étapes manuellement.")

if __name__ == "__main__":
    main()
