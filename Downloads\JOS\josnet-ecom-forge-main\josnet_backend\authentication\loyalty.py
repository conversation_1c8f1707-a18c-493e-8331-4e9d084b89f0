"""
Loyalty program management module.
"""
from django.db import transaction
from django.utils import timezone
from datetime import timedel<PERSON>
from .models import User, LoyaltyTransaction

# Loyalty tiers configuration
LOYALTY_TIERS = {
    'standard': {
        'name': 'Standard',
        'min_points': 0,
        'benefits': 'Basic benefits',
        'earn_rate': 1.0,  # Multiplier for points earned
    },
    'silver': {
        'name': 'Silver',
        'min_points': 1000,
        'benefits': 'Free shipping on orders over $50',
        'earn_rate': 1.2,
    },
    'gold': {
        'name': 'Gold',
        'min_points': 5000,
        'benefits': 'Free shipping on all orders, 5% discount',
        'earn_rate': 1.5,
    },
    'platinum': {
        'name': 'Platinum',
        'min_points': 10000,
        'benefits': 'Free shipping, 10% discount, priority support',
        'earn_rate': 2.0,
    }
}

# Points earning rules
POINTS_PER_DOLLAR = 10
SIGNUP_BONUS_POINTS = 100
REVIEW_POINTS = 50
REFERRAL_POINTS = 200

def update_user_tier(user):
    """
    Update user's loyalty tier based on their points.
    """
    current_points = user.loyalty_points
    current_tier = user.loyalty_tier
    
    # Find the highest tier the user qualifies for
    new_tier = 'standard'
    for tier, config in LOYALTY_TIERS.items():
        if current_points >= config['min_points']:
            new_tier = tier
    
    # Update user tier if changed
    if new_tier != current_tier:
        user.loyalty_tier = new_tier
        user.save(update_fields=['loyalty_tier'])
        return True
    
    return False

@transaction.atomic
def add_points(user, points, description, order=None, expires_in_days=365):
    """
    Add loyalty points to a user's account.
    
    Args:
        user: User object
        points: Number of points to add
        description: Description of the transaction
        order: Optional Order object related to this transaction
        expires_in_days: Number of days until points expire
    
    Returns:
        LoyaltyTransaction object
    """
    if points <= 0:
        raise ValueError("Points must be a positive number")
    
    # Apply tier multiplier if applicable
    tier_config = LOYALTY_TIERS.get(user.loyalty_tier, LOYALTY_TIERS['standard'])
    adjusted_points = int(points * tier_config['earn_rate'])
    
    # Create expiration date
    expires_at = timezone.now() + timedelta(days=expires_in_days) if expires_in_days else None
    
    # Create transaction
    transaction = LoyaltyTransaction.objects.create(
        user=user,
        points=adjusted_points,
        transaction_type='earn',
        description=description,
        order=order,
        expires_at=expires_at
    )
    
    # Update user's points
    user.loyalty_points += adjusted_points
    user.save(update_fields=['loyalty_points'])
    
    # Update user's tier if needed
    update_user_tier(user)
    
    return transaction

@transaction.atomic
def redeem_points(user, points, description, order=None):
    """
    Redeem loyalty points from a user's account.
    
    Args:
        user: User object
        points: Number of points to redeem
        description: Description of the transaction
        order: Optional Order object related to this redemption
    
    Returns:
        LoyaltyTransaction object
    
    Raises:
        ValueError: If user doesn't have enough points
    """
    if points <= 0:
        raise ValueError("Points must be a positive number")
    
    if user.loyalty_points < points:
        raise ValueError("User doesn't have enough points")
    
    # Create transaction (negative points for redemption)
    transaction = LoyaltyTransaction.objects.create(
        user=user,
        points=-points,  # Negative for redemption
        transaction_type='redeem',
        description=description,
        order=order
    )
    
    # Update user's points
    user.loyalty_points -= points
    user.save(update_fields=['loyalty_points'])
    
    # Update user's tier if needed
    update_user_tier(user)
    
    return transaction

def expire_points():
    """
    Expire loyalty points that have reached their expiration date.
    Should be run as a scheduled task.
    """
    now = timezone.now()
    expired_transactions = LoyaltyTransaction.objects.filter(
        transaction_type='earn',
        expires_at__lt=now,
        is_expired=False
    )
    
    for transaction in expired_transactions:
        user = transaction.user
        points = transaction.points
        
        # Create expiration transaction
        LoyaltyTransaction.objects.create(
            user=user,
            points=-points,  # Negative for expiration
            transaction_type='expire',
            description=f"Points expired from transaction on {transaction.created_at.strftime('%Y-%m-%d')}"
        )
        
        # Update user's points
        user.loyalty_points = max(0, user.loyalty_points - points)
        user.save(update_fields=['loyalty_points'])
        
        # Mark original transaction as expired
        transaction.is_expired = True
        transaction.save(update_fields=['is_expired'])
        
        # Update user's tier if needed
        update_user_tier(user)

def calculate_order_points(order_total):
    """
    Calculate points to be earned for an order.
    
    Args:
        order_total: Order total amount
        
    Returns:
        Number of points to be earned
    """
    return int(order_total * POINTS_PER_DOLLAR)

def get_points_value(points):
    """
    Calculate the monetary value of points.
    
    Args:
        points: Number of points
        
    Returns:
        Monetary value of points
    """
    # Example: 100 points = $1
    return points / 100.0
