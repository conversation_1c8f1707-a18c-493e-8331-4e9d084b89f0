import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Search,
  Filter,
  Plus,
  Pin,
  Archive,
  MoreVertical,
  Clock,
  AlertCircle,
  CheckCircle,
  Circle,
  MessageSquare
} from 'lucide-react';
import { format, parseISO, isToday, isYesterday } from 'date-fns';
import { fr } from 'date-fns/locale';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Conversation {
  id: number;
  subject: string;
  status: string;
  priority: string;
  customer: {
    id: number;
    full_name: string;
    email: string;
    profile_picture?: string;
  };
  last_message_preview?: {
    content: string;
    sender_name: string;
    is_from_admin: boolean;
  };
  created_at: string;
  updated_at: string;
  last_message_at: string;
  message_count: number;
  unread_count: number;
  is_read_by_admin: boolean;
  is_read_by_customer: boolean;
  is_pinned?: boolean;
  is_archived?: boolean;
}

interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: number | null;
  onSelectConversation: (conversationId: number) => void;
  onCreateConversation?: () => void;
  isLoading?: boolean;
  className?: string;
}

const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onCreateConversation,
  isLoading = false,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [showArchived, setShowArchived] = useState(false);

  const formatLastMessageTime = (dateString: string) => {
    const date = parseISO(dateString);
    if (isToday(date)) {
      return format(date, 'HH:mm');
    } else if (isYesterday(date)) {
      return 'Hier';
    } else {
      return format(date, 'dd/MM', { locale: fr });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'resolved': return 'bg-green-100 text-green-800 border-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <AlertCircle className="h-3 w-3 text-red-500" />;
      case 'high': return <Circle className="h-3 w-3 text-orange-500" />;
      case 'medium': return <Circle className="h-3 w-3 text-blue-500" />;
      case 'low': return <Circle className="h-3 w-3 text-gray-400" />;
      default: return null;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'new': return 'Nouveau';
      case 'in_progress': return 'En cours';
      case 'resolved': return 'Résolu';
      case 'closed': return 'Fermé';
      default: return status;
    }
  };

  // Filter conversations
  const filteredConversations = conversations.filter(conversation => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSubject = conversation.subject.toLowerCase().includes(query);
      const matchesCustomer = conversation.customer.full_name.toLowerCase().includes(query);
      const matchesEmail = conversation.customer.email.toLowerCase().includes(query);
      const matchesContent = conversation.last_message_preview?.content.toLowerCase().includes(query);
      
      if (!matchesSubject && !matchesCustomer && !matchesEmail && !matchesContent) {
        return false;
      }
    }

    // Status filter
    if (statusFilter !== 'all' && conversation.status !== statusFilter) {
      return false;
    }

    // Priority filter
    if (priorityFilter !== 'all' && conversation.priority !== priorityFilter) {
      return false;
    }

    // Archive filter
    if (!showArchived && conversation.is_archived) {
      return false;
    }

    return true;
  });

  // Sort conversations (pinned first, then by last message time)
  const sortedConversations = [...filteredConversations].sort((a, b) => {
    if (a.is_pinned && !b.is_pinned) return -1;
    if (!a.is_pinned && b.is_pinned) return 1;
    return new Date(b.last_message_at).getTime() - new Date(a.last_message_at).getTime();
  });

  const unreadCount = conversations.filter(c => c.unread_count > 0).length;

  return (
    <div className={cn("flex flex-col h-full bg-white border-r", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold">Messages</h2>
            <p className="text-sm text-gray-500">
              {unreadCount > 0 && (
                <span className="text-blue-600 font-medium">
                  {unreadCount} non lu{unreadCount > 1 ? 's' : ''}
                </span>
              )}
            </p>
          </div>
          {onCreateConversation && (
            <Button onClick={onCreateConversation} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filters */}
        <div className="flex space-x-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Statut" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les statuts</SelectItem>
              <SelectItem value="new">Nouveau</SelectItem>
              <SelectItem value="in_progress">En cours</SelectItem>
              <SelectItem value="resolved">Résolu</SelectItem>
              <SelectItem value="closed">Fermé</SelectItem>
            </SelectContent>
          </Select>

          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Priorité" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes</SelectItem>
              <SelectItem value="urgent">Urgente</SelectItem>
              <SelectItem value="high">Haute</SelectItem>
              <SelectItem value="medium">Moyenne</SelectItem>
              <SelectItem value="low">Basse</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 space-y-3">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-3 p-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                  <div className="w-8 h-3 bg-gray-200 rounded" />
                </div>
              </div>
            ))}
          </div>
        ) : sortedConversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune conversation
            </h3>
            <p className="text-gray-500 mb-4">
              {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all'
                ? 'Aucune conversation ne correspond à vos critères.'
                : 'Commencez une nouvelle conversation.'}
            </p>
            {onCreateConversation && (
              <Button onClick={onCreateConversation} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle conversation
              </Button>
            )}
          </div>
        ) : (
          <AnimatePresence>
            {sortedConversations.map((conversation, index) => (
              <motion.div
                key={conversation.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
                className={cn(
                  "relative cursor-pointer border-b hover:bg-gray-50 transition-colors",
                  selectedConversationId === conversation.id && "bg-blue-50 border-l-4 border-l-blue-500"
                )}
                onClick={() => onSelectConversation(conversation.id)}
              >
                <div className="p-4">
                  <div className="flex items-start space-x-3">
                    {/* Avatar */}
                    <div className="relative">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={conversation.customer.profile_picture} />
                        <AvatarFallback>
                          {conversation.customer.full_name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      {conversation.unread_count > 0 && (
                        <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                          {conversation.unread_count > 9 ? '9+' : conversation.unread_count}
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className={cn(
                          "font-medium truncate",
                          conversation.unread_count > 0 ? "text-gray-900" : "text-gray-700"
                        )}>
                          {conversation.subject}
                        </h3>
                        <div className="flex items-center space-x-1">
                          {conversation.is_pinned && (
                            <Pin className="h-3 w-3 text-gray-400" />
                          )}
                          {getPriorityIcon(conversation.priority)}
                        </div>
                      </div>

                      <p className="text-sm text-gray-600 mb-1">
                        {conversation.customer.full_name}
                      </p>

                      {conversation.last_message_preview && (
                        <p className={cn(
                          "text-sm truncate",
                          conversation.unread_count > 0 ? "text-gray-900 font-medium" : "text-gray-500"
                        )}>
                          {conversation.last_message_preview.is_from_admin && "Vous: "}
                          {conversation.last_message_preview.content}
                        </p>
                      )}

                      <div className="flex items-center justify-between mt-2">
                        <Badge className={getStatusColor(conversation.status)} variant="outline">
                          {getStatusLabel(conversation.status)}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {formatLastMessageTime(conversation.last_message_at)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>{sortedConversations.length} conversation{sortedConversations.length !== 1 ? 's' : ''}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowArchived(!showArchived)}
            className="text-xs"
          >
            <Archive className="h-3 w-3 mr-1" />
            {showArchived ? 'Masquer archivées' : 'Voir archivées'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConversationList;
