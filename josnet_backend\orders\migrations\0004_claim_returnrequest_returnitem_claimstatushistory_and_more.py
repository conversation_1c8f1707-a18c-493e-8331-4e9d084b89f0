# Generated by Django 4.2.7 on 2025-06-02 01:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0003_order_promo_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='Claim',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('claim_number', models.CharField(editable=False, max_length=50, unique=True)),
                ('subject', models.CharField(choices=[('order_issue', 'Problème avec une commande'), ('product_issue', 'Problème avec un produit'), ('shipping_issue', 'Problème de livraison'), ('account_issue', 'Problème de compte'), ('website_issue', 'Problème avec le site web'), ('other', 'Autre')], max_length=50)),
                ('description', models.TextField()),
                ('status', models.Char<PERSON>ield(choices=[('new', 'Nouveau'), ('in_progress', 'En cours'), ('waiting_customer', 'En attente du client'), ('waiting_staff', 'En attente du support'), ('resolved', 'Résolu'), ('closed', 'Fermé')], default='new', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='claims', to='orders.order')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='claims', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Claim',
                'verbose_name_plural': 'Claims',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReturnRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('return_number', models.CharField(editable=False, max_length=50, unique=True)),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('approved', 'Approuvé'), ('rejected', 'Rejeté'), ('processing', 'En traitement'), ('completed', 'Terminé')], default='pending', max_length=20)),
                ('reason', models.CharField(choices=[('defective', 'Article défectueux'), ('wrong_item', 'Article incorrect'), ('size_issue', 'Problème de taille'), ('not_as_described', 'Ne correspond pas à la description'), ('damaged', 'Endommagé à la livraison'), ('other', 'Autre raison')], max_length=50)),
                ('additional_info', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='return_requests', to='orders.order')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='return_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Return Request',
                'verbose_name_plural': 'Return Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(max_length=255)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reason', models.CharField(choices=[('defective', 'Article défectueux'), ('wrong_item', 'Article incorrect'), ('size_issue', 'Problème de taille'), ('not_as_described', 'Ne correspond pas à la description'), ('damaged', 'Endommagé à la livraison'), ('other', 'Autre raison')], max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('order_item', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='return_items', to='orders.orderitem')),
                ('return_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.returnrequest')),
            ],
        ),
        migrations.CreateModel(
            name='ClaimStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(choices=[('new', 'Nouveau'), ('in_progress', 'En cours'), ('waiting_customer', 'En attente du client'), ('waiting_staff', 'En attente du support'), ('resolved', 'Résolu'), ('closed', 'Fermé')], max_length=20)),
                ('new_status', models.CharField(choices=[('new', 'Nouveau'), ('in_progress', 'En cours'), ('waiting_customer', 'En attente du client'), ('waiting_staff', 'En attente du support'), ('resolved', 'Résolu'), ('closed', 'Fermé')], max_length=20)),
                ('comment', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('claim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='orders.claim')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Claim status histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ClaimMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('sender', models.CharField(choices=[('customer', 'Client'), ('support', 'Support'), ('system', 'Système')], max_length=10)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('claim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='orders.claim')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ClaimAttachment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(upload_to='message_attachments/')),
                ('file_name', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField()),
                ('content_type', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='orders.claimmessage')),
            ],
        ),
    ]
