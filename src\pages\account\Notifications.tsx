import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Bell, Settings, Trash2, Send, Tag, ShoppingBag, Info } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import AccountLayout from '@/components/account/AccountLayout';
import notificationApi, { Notification, NotificationPreference } from '@/services/notificationApi';
import SubscriptionManager from '@/components/notifications/SubscriptionManager';

const NotificationsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Récupérer toutes les notifications
  const { data: allNotifications, isLoading: isLoadingNotifications } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => notificationApi.getUserNotifications(),
  });

  // Récupérer les préférences de notification
  const { data: preferences, isLoading: isLoadingPreferences } = useQuery({
    queryKey: ['notificationPreferences'],
    queryFn: () => notificationApi.getNotificationPreferences(),
  });

  // Mutation pour mettre à jour les préférences
  const updatePreferencesMutation = useMutation({
    mutationFn: (newPreferences: Partial<NotificationPreference>) => 
      notificationApi.updateNotificationPreferences(newPreferences),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notificationPreferences'] });
      toast({
        title: 'Préférences mises à jour',
        description: 'Vos préférences de notification ont été enregistrées.',
      });
    },
    onError: () => {
      toast({
        title: 'Erreur',
        description: 'Impossible de mettre à jour vos préférences. Veuillez réessayer.',
        variant: 'destructive',
      });
    },
  });

  // Mutation pour marquer une notification comme lue
  const markAsReadMutation = useMutation({
    mutationFn: (id: number) => notificationApi.markAsRead(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadNotifications'] });
    },
  });

  // Mutation pour supprimer une notification
  const deleteNotificationMutation = useMutation({
    mutationFn: (id: number) => notificationApi.deleteNotification(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadNotifications'] });
      toast({
        title: 'Notification supprimée',
        description: 'La notification a été supprimée avec succès.',
      });
    },
  });

  // Mutation pour marquer toutes les notifications comme lues
  const markAllAsReadMutation = useMutation({
    mutationFn: () => notificationApi.markAllAsRead(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadNotifications'] });
      toast({
        title: 'Notifications mises à jour',
        description: 'Toutes les notifications ont été marquées comme lues.',
      });
    },
  });



  // Filtrer les notifications en fonction de l'onglet actif
  const filteredNotifications = React.useMemo(() => {
    if (!allNotifications?.results) return [];
    
    switch (activeTab) {
      case 'unread':
        return allNotifications.results.filter(n => !n.is_read);
      case 'promotions':
        return allNotifications.results.filter(n => n.type === 'promotion');
      case 'orders':
        return allNotifications.results.filter(n => n.type === 'order');
      case 'system':
        return allNotifications.results.filter(n => n.type === 'system' || n.type === 'info');
      default:
        return allNotifications.results;
    }
  }, [allNotifications, activeTab]);

  // Gérer le clic sur une notification
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.is_read) {
      markAsReadMutation.mutate(notification.id);
    }
    
    if (notification.link) {
      navigate(notification.link);
    }
  };

  // Obtenir l'icône en fonction du type de notification
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'promotion':
        return <Tag className="h-5 w-5 text-green-500" />;
      case 'order':
        return <ShoppingBag className="h-5 w-5 text-blue-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: fr });
    } catch (error) {
      return 'Date inconnue';
    }
  };

  // Mettre à jour une préférence de notification
  const handlePreferenceChange = (key: string, value: boolean) => {
    updatePreferencesMutation.mutate({ [key]: value } as Partial<NotificationPreference>);
  };



  return (
    <AccountLayout
      title="Notifications"
      icon={<Bell className="h-6 w-6" />}
      description="Gérez vos notifications et préférences d'alerte"
    >
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="all">Toutes</TabsTrigger>
            <TabsTrigger value="unread">Non lues</TabsTrigger>
            <TabsTrigger value="promotions">Promotions</TabsTrigger>
            <TabsTrigger value="orders">Commandes</TabsTrigger>
            <TabsTrigger value="system">Système</TabsTrigger>
          </TabsList>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => markAllAsReadMutation.mutate()}
              disabled={markAllAsReadMutation.isPending}
            >
              Tout marquer comme lu
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="mt-0">
          <NotificationsList 
            notifications={filteredNotifications}
            isLoading={isLoadingNotifications}
            onNotificationClick={handleNotificationClick}
            onDeleteClick={(id) => deleteNotificationMutation.mutate(id)}
          />
        </TabsContent>
        
        <TabsContent value="unread" className="mt-0">
          <NotificationsList 
            notifications={filteredNotifications}
            isLoading={isLoadingNotifications}
            onNotificationClick={handleNotificationClick}
            onDeleteClick={(id) => deleteNotificationMutation.mutate(id)}
          />
        </TabsContent>
        
        <TabsContent value="promotions" className="mt-0">
          <NotificationsList 
            notifications={filteredNotifications}
            isLoading={isLoadingNotifications}
            onNotificationClick={handleNotificationClick}
            onDeleteClick={(id) => deleteNotificationMutation.mutate(id)}
          />
        </TabsContent>
        
        <TabsContent value="orders" className="mt-0">
          <NotificationsList 
            notifications={filteredNotifications}
            isLoading={isLoadingNotifications}
            onNotificationClick={handleNotificationClick}
            onDeleteClick={(id) => deleteNotificationMutation.mutate(id)}
          />
        </TabsContent>
        
        <TabsContent value="system" className="mt-0">
          <NotificationsList 
            notifications={filteredNotifications}
            isLoading={isLoadingNotifications}
            onNotificationClick={handleNotificationClick}
            onDeleteClick={(id) => deleteNotificationMutation.mutate(id)}
          />
        </TabsContent>
      </Tabs>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Préférences de notification
          </CardTitle>
          <CardDescription>
            Personnalisez les types de notifications que vous souhaitez recevoir
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingPreferences ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-[150px]" />
                    <Skeleton className="h-3 w-[250px]" />
                  </div>
                  <Skeleton className="h-6 w-12" />
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="font-medium">Promotions et offres</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email_promotions" className="font-medium">Notifications par email</Label>
                      <p className="text-sm text-gray-500">Recevez nos offres et promotions par email</p>
                    </div>
                    <Switch 
                      id="email_promotions" 
                      checked={preferences?.email_promotions} 
                      onCheckedChange={(checked) => handlePreferenceChange('email_promotions', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="push_promotions" className="font-medium">Notifications push</Label>
                      <p className="text-sm text-gray-500">Recevez des alertes sur le site pour les nouvelles promotions</p>
                    </div>
                    <Switch 
                      id="push_promotions" 
                      checked={preferences?.push_promotions} 
                      onCheckedChange={(checked) => handlePreferenceChange('push_promotions', checked)}
                    />
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="font-medium">Commandes</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email_orders" className="font-medium">Notifications par email</Label>
                      <p className="text-sm text-gray-500">Recevez des mises à jour sur vos commandes par email</p>
                    </div>
                    <Switch 
                      id="email_orders" 
                      checked={preferences?.email_orders} 
                      onCheckedChange={(checked) => handlePreferenceChange('email_orders', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="push_orders" className="font-medium">Notifications push</Label>
                      <p className="text-sm text-gray-500">Recevez des alertes sur le site pour les mises à jour de commandes</p>
                    </div>
                    <Switch 
                      id="push_orders" 
                      checked={preferences?.push_orders} 
                      onCheckedChange={(checked) => handlePreferenceChange('push_orders', checked)}
                    />
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="font-medium">Système et informations</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email_system" className="font-medium">Notifications par email</Label>
                      <p className="text-sm text-gray-500">Recevez des informations importantes sur votre compte par email</p>
                    </div>
                    <Switch 
                      id="email_system" 
                      checked={preferences?.email_system} 
                      onCheckedChange={(checked) => handlePreferenceChange('email_system', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="push_system" className="font-medium">Notifications push</Label>
                      <p className="text-sm text-gray-500">Recevez des alertes sur le site pour les informations système</p>
                    </div>
                    <Switch 
                      id="push_system" 
                      checked={preferences?.push_system} 
                      onCheckedChange={(checked) => handlePreferenceChange('push_system', checked)}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Gestion des abonnements newsletter */}
      <SubscriptionManager />
    </AccountLayout>
  );
};

// Composant pour afficher la liste des notifications
interface NotificationsListProps {
  notifications: Notification[];
  isLoading: boolean;
  onNotificationClick: (notification: Notification) => void;
  onDeleteClick: (id: number) => void;
}

const NotificationsList: React.FC<NotificationsListProps> = ({ 
  notifications, 
  isLoading, 
  onNotificationClick,
  onDeleteClick
}) => {
  // Obtenir l'icône en fonction du type de notification
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'promotion':
        return <Tag className="h-5 w-5 text-green-500" />;
      case 'order':
        return <ShoppingBag className="h-5 w-5 text-blue-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: fr });
    } catch (error) {
      return 'Date inconnue';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={i} className="p-4">
            <div className="flex gap-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-3 w-full" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <Card className="p-8 text-center">
        <Bell className="h-12 w-12 mx-auto text-gray-300 mb-3" />
        <h3 className="text-lg font-medium mb-1">Aucune notification</h3>
        <p className="text-gray-500">
          Vous n'avez aucune notification dans cette catégorie pour le moment.
        </p>
      </Card>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.map((notification) => (
        <Card 
          key={notification.id} 
          className={`p-4 transition-colors ${!notification.is_read ? 'bg-blue-50' : ''}`}
        >
          <div className="flex gap-3">
            <div className="mt-1">
              {getNotificationIcon(notification.type)}
            </div>
            <div 
              className="flex-1 cursor-pointer" 
              onClick={() => onNotificationClick(notification)}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{notification.title}</h4>
                  {!notification.is_read && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">
                      Nouveau
                    </Badge>
                  )}
                </div>
                <span className="text-xs text-gray-500">
                  {formatDate(notification.created_at)}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
            </div>
            <Button 
              variant="ghost" 
              size="icon"
              className="h-8 w-8 text-gray-400 hover:text-red-500"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteClick(notification.id);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default NotificationsPage;
