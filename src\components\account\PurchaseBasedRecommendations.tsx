import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ShoppingBag, 
  TrendingUp, 
  Clock, 
  Star,
  ArrowRight,
  Package,
  Eye,
  ShoppingCart,
  RefreshCw
} from 'lucide-react';
import { accountApi } from '@/services/accountApi';
import { formatCurrency } from '@/utils/formatters';
import { Link } from 'react-router-dom';

interface PurchaseHistory {
  id: number;
  productName: string;
  category: string;
  purchaseDate: string;
  price: number;
  image: string;
}

interface RecommendationReason {
  type: 'similar_category' | 'frequently_bought_together' | 'upgrade' | 'complementary';
  description: string;
}

interface SmartRecommendation {
  id: number;
  name: string;
  price: number;
  image: string;
  rating: number;
  category: string;
  reason: RecommendationReason;
  confidence: number; // 0-100
}

const PurchaseBasedRecommendations: React.FC = () => {
  const [activeTab, setActiveTab] = useState('recommendations');

  // Récupérer l'historique d'achats depuis l'API Django
  const { data: purchaseHistory = [], isLoading: isLoadingHistory } = useQuery({
    queryKey: ['purchase-history'],
    queryFn: async (): Promise<PurchaseHistory[]> => {
      try {
        // Appel à l'API Django pour l'historique d'achats
        const data = await accountApi.getUserPurchaseHistory();
        return data;
      } catch (error) {
        console.error('Erreur lors du chargement de l\'historique:', error);

        // Fallback vers des données simulées en cas d'erreur
        return [
        {
          id: 1,
          productName: "Routeur Wi-Fi 6 AX3000",
          category: "Réseaux",
          purchaseDate: "2024-01-15",
          price: 89000,
          image: "https://images.unsplash.com/photo-*************-647eb07f5be2?w=400"
        },
        {
          id: 2,
          productName: "Câble Ethernet Cat 6A 10m",
          category: "Câbles",
          purchaseDate: "2024-01-15",
          price: 15000,
          image: "https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400"
        },
        {
          id: 3,
          productName: "Disque SSD NVMe 1TB",
          category: "Stockage",
          purchaseDate: "2023-12-10",
          price: 120000,
          image: "https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400"
        }
      ];
      }
    },
    staleTime: 300000, // 5 minutes
  });

  // Récupérer les recommandations intelligentes depuis l'API Django
  const {
    data: smartRecommendations = [],
    isLoading: isLoadingRecommendations,
    refetch
  } = useQuery({
    queryKey: ['smart-recommendations', purchaseHistory],
    queryFn: async (): Promise<SmartRecommendation[]> => {
      try {
        // Appel à l'API Django pour les recommandations intelligentes
        const data = await accountApi.getSmartRecommendations();
        return data;
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations:', error);

        // Fallback vers des données simulées en cas d'erreur
        return [
        {
          id: 101,
          name: "Switch Gigabit 24 ports TP-Link",
          price: 180000,
          image: "https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400",
          rating: 4.6,
          category: "Réseaux",
          reason: {
            type: 'similar_category',
            description: "Basé sur votre achat de routeur Wi-Fi"
          },
          confidence: 92
        },
        {
          id: 102,
          name: "Onduleur UPS 1500VA",
          price: 95000,
          image: "https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400",
          rating: 4.5,
          category: "Accessoires",
          reason: {
            type: 'complementary',
            description: "Protège vos équipements réseau"
          },
          confidence: 88
        },
        {
          id: 103,
          name: "Disque SSD NVMe 2TB Samsung 980 PRO",
          price: 220000,
          image: "https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400",
          rating: 4.8,
          category: "Stockage",
          reason: {
            type: 'upgrade',
            description: "Mise à niveau de votre SSD 1TB"
          },
          confidence: 85
        },
        {
          id: 104,
          name: "Testeur de câbles réseau",
          price: 35000,
          image: "https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400",
          rating: 4.3,
          category: "Outils",
          reason: {
            type: 'frequently_bought_together',
            description: "Souvent acheté avec des câbles Ethernet"
          },
          confidence: 78
        }
      ];
      }
    },
    enabled: purchaseHistory.length > 0,
    staleTime: 300000,
  });

  const getReasonIcon = (type: RecommendationReason['type']) => {
    switch (type) {
      case 'similar_category':
        return <TrendingUp className="h-4 w-4" />;
      case 'frequently_bought_together':
        return <ShoppingBag className="h-4 w-4" />;
      case 'upgrade':
        return <ArrowRight className="h-4 w-4" />;
      case 'complementary':
        return <Package className="h-4 w-4" />;
      default:
        return <Star className="h-4 w-4" />;
    }
  };

  const getReasonColor = (type: RecommendationReason['type']) => {
    switch (type) {
      case 'similar_category':
        return 'bg-blue-100 text-blue-800';
      case 'frequently_bought_together':
        return 'bg-green-100 text-green-800';
      case 'upgrade':
        return 'bg-purple-100 text-purple-800';
      case 'complementary':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5 text-primary" />
            Recommandations Intelligentes
          </CardTitle>
          <p className="text-sm text-gray-600">
            Basées sur votre historique d'achats et vos préférences
          </p>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="recommendations">
            Recommandations ({smartRecommendations.length})
          </TabsTrigger>
          <TabsTrigger value="history">
            Historique ({purchaseHistory.length})
          </TabsTrigger>
        </TabsList>

        {/* Onglet Recommandations */}
        <TabsContent value="recommendations" className="space-y-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              Suggestions personnalisées basées sur vos achats précédents
            </p>
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualiser
            </Button>
          </div>

          {isLoadingRecommendations ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <Skeleton className="h-20 w-20 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                        <Skeleton className="h-4 w-1/3" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {smartRecommendations.map((product) => (
                <Card key={product.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      {/* Image */}
                      <div className="relative">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="h-20 w-20 object-cover rounded"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder-product.jpg';
                          }}
                        />
                        {/* Badge de confiance */}
                        <div className="absolute -top-2 -right-2">
                          <Badge 
                            variant="secondary" 
                            className="text-xs px-1 py-0"
                          >
                            {product.confidence}%
                          </Badge>
                        </div>
                      </div>

                      {/* Contenu */}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-sm mb-1 line-clamp-2">
                          {product.name}
                        </h3>
                        
                        {/* Note */}
                        <div className="flex items-center gap-1 mb-2">
                          {renderStars(product.rating)}
                          <span className="text-xs text-gray-600">
                            ({product.rating})
                          </span>
                        </div>

                        {/* Raison de la recommandation */}
                        <div className="flex items-center gap-1 mb-2">
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${getReasonColor(product.reason.type)}`}
                          >
                            {getReasonIcon(product.reason.type)}
                            <span className="ml-1">{product.reason.description}</span>
                          </Badge>
                        </div>

                        {/* Prix et actions */}
                        <div className="flex items-center justify-between">
                          <span className="font-bold text-primary">
                            {formatCurrency(product.price)}
                          </span>
                          <div className="flex gap-1">
                            <Link to={`/product/${product.id}`}>
                              <Button size="sm" variant="outline">
                                <Eye className="h-3 w-3" />
                              </Button>
                            </Link>
                            <Button size="sm">
                              <ShoppingCart className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Onglet Historique */}
        <TabsContent value="history" className="space-y-4">
          <p className="text-sm text-gray-600">
            Vos achats récents utilisés pour générer les recommandations
          </p>

          {isLoadingHistory ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <Skeleton className="h-16 w-16 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                        <Skeleton className="h-4 w-1/3" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {purchaseHistory.map((purchase) => (
                <Card key={purchase.id}>
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <img
                        src={purchase.image}
                        alt={purchase.productName}
                        className="h-16 w-16 object-cover rounded"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder-product.jpg';
                        }}
                      />
                      <div className="flex-1">
                        <h3 className="font-semibold mb-1">
                          {purchase.productName}
                        </h3>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <Badge variant="outline">{purchase.category}</Badge>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {new Date(purchase.purchaseDate).toLocaleDateString('fr-FR')}
                          </div>
                          <span className="font-medium text-primary">
                            {formatCurrency(purchase.price)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PurchaseBasedRecommendations;
