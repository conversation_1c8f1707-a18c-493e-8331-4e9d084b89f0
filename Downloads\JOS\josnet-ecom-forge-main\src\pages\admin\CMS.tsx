
import { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>ist, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  FileText, 
  Search, 
  Plus, 
  Edit, 
  Trash2,
  Image,
  Eye,
  Home,
  FileQuestion,
  FileCode,
  Mail
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

// Données fictives pour les pages
const mockPages = [
  { 
    id: "page-001", 
    title: "Accueil", 
    slug: "/", 
    lastUpdate: "15/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    template: "Homepage"
  },
  { 
    id: "page-002", 
    title: "À propos", 
    slug: "/a-propos", 
    lastUpdate: "12/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    template: "Standard"
  },
  { 
    id: "page-003", 
    title: "Services", 
    slug: "/services", 
    lastUpdate: "10/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    template: "Services"
  },
  { 
    id: "page-004", 
    title: "Politique de confidentialité", 
    slug: "/politique-de-confidentialite", 
    lastUpdate: "05/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    template: "Legal"
  },
  { 
    id: "page-005", 
    title: "Conditions générales de vente", 
    slug: "/cgv", 
    lastUpdate: "05/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    template: "Legal"
  }
];

// Données fictives pour les articles de blog
const mockBlogPosts = [
  { 
    id: "post-001", 
    title: "Les meilleures solutions réseau pour le télétravail", 
    slug: "/blog/solutions-reseau-teletravail", 
    lastUpdate: "14/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    category: "Conseils",
    comments: 8
  },
  { 
    id: "post-002", 
    title: "Comment sécuriser votre réseau domestique", 
    slug: "/blog/securiser-reseau-domestique", 
    lastUpdate: "10/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    category: "Sécurité",
    comments: 12
  },
  { 
    id: "post-003", 
    title: "Comparatif des routeurs WiFi 6", 
    slug: "/blog/comparatif-routeurs-wifi-6", 
    lastUpdate: "08/05/2025", 
    status: "Brouillon",
    author: "<EMAIL>",
    category: "Tests",
    comments: 0
  },
  { 
    id: "post-004", 
    title: "L'avenir de la 5G en France", 
    slug: "/blog/avenir-5g-france", 
    lastUpdate: "02/05/2025", 
    status: "Publié",
    author: "<EMAIL>",
    category: "Actualités",
    comments: 15
  }
];

// Données fictives pour les médias
const mockMedia = [
  { 
    id: "media-001", 
    name: "banner-homepage.jpg", 
    type: "image/jpeg", 
    size: "1.2 MB",
    dimensions: "1920x680",
    uploaded: "15/05/2025",
    uploader: "<EMAIL>"
  },
  { 
    id: "media-002", 
    name: "product-router.png", 
    type: "image/png", 
    size: "850 KB",
    dimensions: "800x600",
    uploaded: "14/05/2025",
    uploader: "<EMAIL>"
  },
  { 
    id: "media-003", 
    name: "network-diagram.pdf", 
    type: "application/pdf", 
    size: "2.4 MB",
    dimensions: "-",
    uploaded: "10/05/2025",
    uploader: "<EMAIL>"
  },
  { 
    id: "media-004", 
    name: "company-logo.svg", 
    type: "image/svg+xml", 
    size: "45 KB",
    dimensions: "scalable",
    uploaded: "01/05/2025",
    uploader: "<EMAIL>"
  }
];

const CMS = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("pages");

  // Fonctions de filtrage pour la recherche selon l'onglet actif
  const getFilteredItems = () => {
    switch (activeTab) {
      case "pages":
        return mockPages.filter(page => 
          page.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          page.slug.toLowerCase().includes(searchQuery.toLowerCase())
        );
      case "blog":
        return mockBlogPosts.filter(post => 
          post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.slug.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.category.toLowerCase().includes(searchQuery.toLowerCase())
        );
      case "media":
        return mockMedia.filter(media => 
          media.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          media.type.toLowerCase().includes(searchQuery.toLowerCase())
        );
      default:
        return [];
    }
  };

  const handleEdit = (id: string) => {
    toast({
      title: "Modification demandée",
      description: `Édition de l'élément ${id} initiée`,
    });
    // Dans une app réelle, cette fonction ouvrirait un formulaire d'édition
  };

  const handleDelete = (id: string) => {
    toast({
      title: "Suppression demandée",
      description: `Suppression de l'élément ${id} initiée`,
      variant: "destructive",
    });
    // Dans une app réelle, cette fonction afficherait une confirmation
  };

  const handleAdd = () => {
    const itemType = activeTab === "pages" 
      ? "page" 
      : activeTab === "blog" 
      ? "article" 
      : "média";
    
    toast({
      title: `Nouveau ${itemType}`,
      description: `Création d'un nouveau ${itemType} initiée`,
    });
    // Dans une app réelle, cette fonction ouvrirait un formulaire de création
  };

  const handleView = (slug: string) => {
    toast({
      title: "Aperçu",
      description: `Visualisation de ${slug} dans un nouvel onglet`,
    });
    // Dans une app réelle, cette fonction ouvrirait un aperçu dans un nouvel onglet
  };

  // Fonction pour obtenir l'icône de la page
  const getPageIcon = (template: string) => {
    switch (template) {
      case "Homepage":
        return <Home size={16} className="mr-2 text-blue-500" />;
      case "Legal":
        return <FileText size={16} className="mr-2 text-orange-500" />;
      case "Services":
        return <FileCode size={16} className="mr-2 text-green-500" />;
      default:
        return <FileQuestion size={16} className="mr-2 text-gray-500" />;
    }
  };

  // Rendu du contenu selon l'onglet actif
  const renderTabContent = () => {
    const filteredItems = getFilteredItems();
    
    switch (activeTab) {
      case "pages":
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Titre</TableHead>
                <TableHead>URL</TableHead>
                <TableHead>Template</TableHead>
                <TableHead>Dernière MàJ</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredItems.map((page: any) => (
                <TableRow key={page.id}>
                  <TableCell>
                    <div className="flex items-center">
                      {getPageIcon(page.template)}
                      <span>{page.title}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{page.slug}</code>
                  </TableCell>
                  <TableCell>{page.template}</TableCell>
                  <TableCell>{page.lastUpdate}</TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                      page.status === "Publié" ? "bg-green-100 text-green-800" : "bg-amber-100 text-amber-800"
                    }`}>
                      {page.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleView(page.slug)}
                      >
                        <Eye size={16} className="text-gray-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleEdit(page.id)}
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleDelete(page.id)}
                      >
                        <Trash2 size={16} className="text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );
      case "blog":
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Titre</TableHead>
                <TableHead>URL</TableHead>
                <TableHead>Catégorie</TableHead>
                <TableHead>Comments</TableHead>
                <TableHead>Dernière MàJ</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredItems.map((post: any) => (
                <TableRow key={post.id}>
                  <TableCell className="font-medium">{post.title}</TableCell>
                  <TableCell>
                    <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{post.slug}</code>
                  </TableCell>
                  <TableCell>{post.category}</TableCell>
                  <TableCell>{post.comments}</TableCell>
                  <TableCell>{post.lastUpdate}</TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                      post.status === "Publié" ? "bg-green-100 text-green-800" : "bg-amber-100 text-amber-800"
                    }`}>
                      {post.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleView(post.slug)}
                      >
                        <Eye size={16} className="text-gray-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleEdit(post.id)}
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleDelete(post.id)}
                      >
                        <Trash2 size={16} className="text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );
      case "media":
        return (
          <div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredItems.map((media: any) => (
                <Card key={media.id} className="overflow-hidden">
                  <div className="h-40 bg-gray-100 flex items-center justify-center">
                    {media.type.startsWith("image/") ? (
                      <div className="bg-gray-200 w-full h-full flex items-center justify-center">
                        <Image size={32} className="text-gray-400" />
                      </div>
                    ) : (
                      <div className="bg-gray-200 w-full h-full flex items-center justify-center">
                        <FileText size={32} className="text-gray-400" />
                      </div>
                    )}
                  </div>
                  <CardContent className="p-3">
                    <div className="font-medium truncate">{media.name}</div>
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>{media.type.split('/')[1].toUpperCase()}</span>
                      <span>{media.size}</span>
                    </div>
                    <div className="flex justify-between mt-3">
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleEdit(media.id)}
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleDelete(media.id)}
                      >
                        <Trash2 size={16} className="text-red-500" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );
      case "templates":
        return (
          <div className="text-center py-12 space-y-3">
            <FileCode size={48} className="mx-auto text-gray-400" />
            <h3 className="text-lg font-medium">Gestion des modèles</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              La gestion des modèles de page est disponible uniquement pour les administrateurs techniques. Contactez l'équipe développement pour créer ou modifier des modèles.
            </p>
          </div>
        );
      case "menus":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Menu principal</CardTitle>
                <CardDescription>Navigation principale du site</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {["Accueil", "Produits", "Services", "Blog", "Contact"].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                      <span>{item}</span>
                      <Button variant="ghost" size="icon">
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full mt-4">
                    <Plus size={16} className="mr-2" />
                    Ajouter un élément
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Menu pied de page</CardTitle>
                <CardDescription>Navigation du pied de page</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {["À propos", "CGV", "Politique de confidentialité", "FAQ", "Contact"].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                      <span>{item}</span>
                      <Button variant="ghost" size="icon">
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full mt-4">
                    <Plus size={16} className="mr-2" />
                    Ajouter un élément
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case "settings":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Paramètres du site</CardTitle>
                <CardDescription>Configuration globale du site</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Titre du site</label>
                    <Input defaultValue="JOSNET - Solutions Informatiques & Réseaux" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <Input defaultValue="Expert en solutions informatiques et réseaux pour particuliers et professionnels" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Logo du site</label>
                    <div className="flex items-center gap-2">
                      <div className="h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
                        <Image size={16} className="text-gray-500" />
                      </div>
                      <Button variant="outline" size="sm">Changer</Button>
                    </div>
                  </div>
                  <Button className="w-full">Enregistrer les modifications</Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Paramètres de contact</CardTitle>
                <CardDescription>Informations de contact et réseaux sociaux</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email de contact</label>
                    <Input defaultValue="<EMAIL>" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                    <Input defaultValue="01 23 45 67 89" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Adresse</label>
                    <Input defaultValue="123 Avenue des Réseaux, 75000 Paris" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                    <Input defaultValue="https://facebook.com/josnet" />
                  </div>
                  <Button className="w-full">Enregistrer les modifications</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case "forms":
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom du formulaire</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Envois</TableHead>
                <TableHead>Dernière soumission</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                { name: "Contact", desc: "Formulaire de contact principal", submissions: 48, lastSubmission: "15/05/2025" },
                { name: "Demande de devis", desc: "Demande de devis professionnels", submissions: 23, lastSubmission: "14/05/2025" },
                { name: "Newsletter", desc: "Inscription à la newsletter", submissions: 156, lastSubmission: "15/05/2025" },
                { name: "Support technique", desc: "Demande d'assistance", submissions: 37, lastSubmission: "15/05/2025" }
              ].map((form, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{form.name}</TableCell>
                  <TableCell>{form.desc}</TableCell>
                  <TableCell>{form.submissions}</TableCell>
                  <TableCell>{form.lastSubmission}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                      >
                        <Mail size={16} className="mr-1" />
                        Voir les messages
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion du Contenu</h1>
          <p className="text-gray-500">Gérez les pages, articles et médias de votre site</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <TabsList>
              <TabsTrigger value="pages">Pages</TabsTrigger>
              <TabsTrigger value="blog">Blog</TabsTrigger>
              <TabsTrigger value="media">Média</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="menus">Menus</TabsTrigger>
              <TabsTrigger value="forms">Formulaires</TabsTrigger>
              <TabsTrigger value="settings">Paramètres</TabsTrigger>
            </TabsList>

            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              <div className="relative w-full sm:w-auto">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Rechercher..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              {(activeTab === "pages" || activeTab === "blog" || activeTab === "media") && (
                <Button onClick={handleAdd} className="flex items-center gap-1 w-full sm:w-auto">
                  <Plus size={16} />
                  Ajouter {activeTab === "pages" ? "une page" : activeTab === "blog" ? "un article" : "un média"}
                </Button>
              )}
            </div>
          </div>

          <div className="border rounded-lg">
            <TabsContent value={activeTab} className="p-0">
              <div className={activeTab === "media" ? "p-4" : ""}>
                {renderTabContent()}
              </div>
            </TabsContent>
          </div>
        </Tabs>

        {(activeTab === "pages" || activeTab === "blog" || activeTab === "media") && (
          <div className="text-sm text-gray-500">
            Affichage de {getFilteredItems().length} éléments sur {
              activeTab === "pages" ? mockPages.length :
              activeTab === "blog" ? mockBlogPosts.length :
              activeTab === "media" ? mockMedia.length : 0
            }
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default CMS;
