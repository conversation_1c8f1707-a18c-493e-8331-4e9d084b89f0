from rest_framework import serializers
from django.utils import timezone
from .models import Order, OrderItem, OrderStatusHistory, OrderRefund, ShippingMethod
from products.serializers import ProductListSerializer, ProductVariantSerializer
from authentication.serializers import UserAddressSerializer

class OrderItemSerializer(serializers.ModelSerializer):
    """Serializer for order items."""
    product_details = ProductListSerializer(source='product', read_only=True)
    variant_details = ProductVariantSerializer(source='variant', read_only=True)

    class Meta:
        model = OrderItem
        fields = [
            'id', 'order', 'product', 'product_details', 'variant', 'variant_details',
            'product_name', 'variant_name', 'sku', 'price', 'quantity', 'subtotal',
            'discount_amount', 'final_price', 'created_at', 'updated_at'
        ]
        read_only_fields = ['subtotal', 'final_price', 'created_at', 'updated_at']

class OrderStatusHistorySerializer(serializers.ModelSerializer):
    """Serializer for order status history."""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_by_name = serializers.SerializerMethodField()

    class Meta:
        model = OrderStatusHistory
        fields = [
            'id', 'order', 'status', 'status_display', 'notes',
            'created_by', 'created_by_name', 'created_at'
        ]
        read_only_fields = ['created_at']

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}".strip() or obj.created_by.email
        return None

class OrderRefundSerializer(serializers.ModelSerializer):
    """Serializer for order refunds."""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    refund_type_display = serializers.CharField(source='get_refund_type_display', read_only=True)
    items_details = OrderItemSerializer(source='items', many=True, read_only=True)
    processed_by_name = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()

    class Meta:
        model = OrderRefund
        fields = [
            'id', 'order', 'refund_number', 'amount', 'status', 'status_display',
            'refund_type', 'refund_type_display', 'reason', 'notes', 'items', 'items_details',
            'processed_by', 'processed_by_name', 'processed_at', 'transaction_id',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['refund_number', 'created_at', 'updated_at']

    def get_processed_by_name(self, obj):
        if obj.processed_by:
            return f"{obj.processed_by.first_name} {obj.processed_by.last_name}".strip() or obj.processed_by.email
        return None

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}".strip() or obj.created_by.email
        return None

class ShippingMethodSerializer(serializers.ModelSerializer):
    """Serializer for shipping methods."""

    class Meta:
        model = ShippingMethod
        fields = [
            'id', 'name', 'description', 'price', 'estimated_delivery_days',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class OrderListSerializer(serializers.ModelSerializer):
    """Serializer for order list view."""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    customer_name = serializers.CharField(source='full_name', read_only=True)
    item_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'user', 'email', 'customer_name', 'status', 'status_display',
            'payment_status', 'payment_status_display', 'payment_method', 'payment_method_display',
            'total', 'item_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class OrderDetailSerializer(serializers.ModelSerializer):
    """Serializer for order detail view."""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    items = OrderItemSerializer(many=True, read_only=True)
    status_history = OrderStatusHistorySerializer(many=True, read_only=True)
    refunds = OrderRefundSerializer(many=True, read_only=True)
    shipping_address_formatted = serializers.CharField(source='formatted_shipping_address', read_only=True)
    billing_address_formatted = serializers.CharField(source='formatted_billing_address', read_only=True)
    billing_address_details = UserAddressSerializer(source='billing_address', read_only=True)
    shipping_address_details = UserAddressSerializer(source='shipping_address', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'user', 'email', 'phone',

            # Address references
            'billing_address', 'billing_address_details',
            'shipping_address', 'shipping_address_details',

            # Billing information
            'billing_first_name', 'billing_last_name', 'billing_company',
            'billing_address_line1', 'billing_address_line2', 'billing_city',
            'billing_state', 'billing_postal_code', 'billing_country',
            'billing_address_formatted',

            # Shipping information
            'shipping_first_name', 'shipping_last_name', 'shipping_company',
            'shipping_address_line1', 'shipping_address_line2', 'shipping_city',
            'shipping_state', 'shipping_postal_code', 'shipping_country',
            'shipping_address_formatted',

            # Order details
            'status', 'status_display', 'payment_status', 'payment_status_display',
            'payment_method', 'payment_method_display', 'payment_reference',

            # Pricing
            'subtotal', 'shipping_cost', 'tax_amount', 'discount_amount', 'promo_code', 'total',

            # Tracking
            'tracking_number', 'shipping_carrier', 'estimated_delivery_date',

            # Notes
            'customer_notes', 'admin_notes',

            # Related data
            'items', 'status_history', 'refunds',

            # Timestamps
            'created_at', 'updated_at', 'paid_at', 'shipped_at', 'delivered_at', 'cancelled_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'paid_at', 'shipped_at', 'delivered_at', 'cancelled_at']

class OrderItemCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating order items (without order field)."""

    class Meta:
        model = OrderItem
        fields = [
            'product', 'variant', 'product_name', 'variant_name', 'sku',
            'price', 'quantity', 'discount_amount'
        ]
        # Note: 'order' field is excluded as it will be set during creation

class OrderCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating orders."""
    items = OrderItemCreateSerializer(many=True)

    class Meta:
        model = Order
        fields = [
            'user', 'email', 'phone',

            # Address references
            'billing_address', 'shipping_address',

            # Billing information
            'billing_first_name', 'billing_last_name', 'billing_company',
            'billing_address_line1', 'billing_address_line2', 'billing_city',
            'billing_state', 'billing_postal_code', 'billing_country',

            # Shipping information
            'shipping_first_name', 'shipping_last_name', 'shipping_company',
            'shipping_address_line1', 'shipping_address_line2', 'shipping_city',
            'shipping_state', 'shipping_postal_code', 'shipping_country',

            # Order details
            'payment_method', 'payment_reference',

            # Pricing
            'subtotal', 'shipping_cost', 'tax_amount', 'discount_amount', 'promo_code', 'total',

            # Notes
            'customer_notes',

            # Items
            'items'
        ]

    def create(self, validated_data):
        items_data = validated_data.pop('items')
        order = Order.objects.create(**validated_data)

        # Create order items
        for item_data in items_data:
            OrderItem.objects.create(order=order, **item_data)

        # Create initial status history
        OrderStatusHistory.objects.create(
            order=order,
            status='pending',
            notes='Commande créée',
            created_by=self.context['request'].user if 'request' in self.context else None
        )

        return order

class OrderUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating orders."""

    class Meta:
        model = Order
        fields = [
            'email', 'phone',

            # Address references
            'billing_address', 'shipping_address',

            # Billing information
            'billing_first_name', 'billing_last_name', 'billing_company',
            'billing_address_line1', 'billing_address_line2', 'billing_city',
            'billing_state', 'billing_postal_code', 'billing_country',

            # Shipping information
            'shipping_first_name', 'shipping_last_name', 'shipping_company',
            'shipping_address_line1', 'shipping_address_line2', 'shipping_city',
            'shipping_state', 'shipping_postal_code', 'shipping_country',

            # Order details
            'status', 'payment_status', 'payment_method', 'payment_reference',

            # Pricing
            'shipping_cost', 'tax_amount', 'discount_amount',

            # Tracking
            'tracking_number', 'shipping_carrier', 'estimated_delivery_date',

            # Notes
            'customer_notes', 'admin_notes',
        ]

    def update(self, instance, validated_data):
        # Check if status has changed
        old_status = instance.status
        new_status = validated_data.get('status', old_status)

        # Update the instance
        order = super().update(instance, validated_data)

        # Create status history if status has changed
        if old_status != new_status:
            OrderStatusHistory.objects.create(
                order=order,
                status=new_status,
                notes=f'Statut changé de {order.get_status_display()} à {dict(Order.STATUS_CHOICES)[new_status]}',
                created_by=self.context['request'].user if 'request' in self.context else None
            )

            # Update timestamp fields based on status
            if new_status == 'paid' and not order.paid_at:
                order.paid_at = timezone.now()
            elif new_status == 'shipped' and not order.shipped_at:
                order.shipped_at = timezone.now()
            elif new_status == 'delivered' and not order.delivered_at:
                order.delivered_at = timezone.now()
            elif new_status == 'cancelled' and not order.cancelled_at:
                order.cancelled_at = timezone.now()

            order.save()

        return order
