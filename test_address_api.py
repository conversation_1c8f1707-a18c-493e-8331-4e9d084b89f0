#!/usr/bin/env python3
"""
Script pour tester l'API des adresses utilisateur
"""

import requests
import json
import sys
import os

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    print("🔐 Authentification...")

    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }

    try:
        response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)

        if response.status_code == 200:
            data = response.json()
            token = data.get('access')
            print(f"✅ Authentification réussie")
            return token
        else:
            print(f"❌ Échec de l'authentification: {response.status_code}")
            print(f"Réponse: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return None

def test_get_addresses(token):
    """Tester la récupération des adresses"""
    print("\n📋 Test de récupération des adresses...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/auth/addresses/", headers=headers)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Debug - Réponse complète: {data}")

            # Vérifier si c'est paginé
            if isinstance(data, dict) and 'results' in data:
                addresses = data['results']
                print(f"✅ Données paginées - {len(addresses)} adresse(s) trouvée(s)")
                print(f"   Total: {data.get('count', 'N/A')}")
            elif isinstance(data, list):
                addresses = data
                print(f"✅ {len(addresses)} adresse(s) trouvée(s)")
            else:
                print(f"❌ Format de données inattendu: {type(data)}")
                return []

            for i, addr in enumerate(addresses, 1):
                try:
                    print(f"\n📍 Adresse {i}:")
                    if isinstance(addr, dict):
                        print(f"   ID: {addr.get('id')}")
                        print(f"   Type: {addr.get('address_type')}")
                        print(f"   Par défaut: {addr.get('is_default')}")
                        print(f"   Nom: {addr.get('first_name')} {addr.get('last_name')}")
                        print(f"   Adresse: {addr.get('address_line1')}")
                        print(f"   Ville: {addr.get('city')}")
                        print(f"   Code postal: {addr.get('postal_code')}")
                        print(f"   Pays: {addr.get('country')}")
                    else:
                        print(f"   ⚠️ Format inattendu: {addr}")
                except Exception as e:
                    print(f"   ❌ Erreur lors de l'affichage: {e}")

            return addresses
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_create_address(token):
    """Tester la création d'une adresse"""
    print("\n➕ Test de création d'adresse...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    address_data = {
        "address_type": "both",
        "is_default": False,
        "first_name": "Test",
        "last_name": "User",
        "address_line1": "123 Rue de Test",
        "city": "Bujumbura",
        "postal_code": "1000",
        "country": "Burundi",
        "phone": "+257 12 34 56 78"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/addresses/", json=address_data, headers=headers)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            address = response.json()
            print(f"✅ Adresse créée avec succès!")
            print(f"   ID: {address.get('id')}")
            print(f"   Adresse complète: {address.get('full_address')}")
            return address
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_update_address(token, address_id):
    """Tester la mise à jour d'une adresse"""
    print(f"\n✏️ Test de mise à jour de l'adresse {address_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    update_data = {
        "address_type": "both",
        "is_default": True,
        "first_name": "Test Updated",
        "last_name": "User Updated",
        "address_line1": "456 Avenue Mise à Jour",
        "city": "Gitega",
        "postal_code": "2000",
        "country": "Burundi",
        "phone": "+257 87 65 43 21"
    }
    
    try:
        response = requests.put(f"{API_BASE_URL}/auth/addresses/{address_id}/", json=update_data, headers=headers)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            address = response.json()
            print(f"✅ Adresse mise à jour avec succès!")
            print(f"   Nouvelle adresse: {address.get('address_line1')}")
            print(f"   Nouvelle ville: {address.get('city')}")
            print(f"   Par défaut: {address.get('is_default')}")
            return address
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_set_default_address(token, address_id):
    """Tester la définition d'une adresse par défaut"""
    print(f"\n⭐ Test de définition de l'adresse {address_id} par défaut...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/addresses/{address_id}/set_default/", headers=headers)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Adresse définie par défaut!")
            print(f"   Réponse: {result}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    print("🧪 TEST DE L'API DES ADRESSES UTILISATEUR")
    print("=" * 50)
    
    # 1. Authentification
    token = get_auth_token()
    if not token:
        print("❌ Impossible de continuer sans authentification")
        return
    
    # 2. Récupérer les adresses existantes
    existing_addresses = test_get_addresses(token)
    
    # 3. Créer une nouvelle adresse
    new_address = test_create_address(token)
    
    # 4. Récupérer les adresses après création
    print("\n📋 Adresses après création:")
    updated_addresses = test_get_addresses(token)
    
    # 5. Tester la mise à jour si on a créé une adresse
    if new_address:
        test_update_address(token, new_address['id'])
        
        # 6. Tester la définition par défaut
        test_set_default_address(token, new_address['id'])
        
        # 7. Vérifier les changements
        print("\n📋 Adresses finales:")
        final_addresses = test_get_addresses(token)
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Adresses initiales: {len(existing_addresses)}")
    print(f"   Adresses finales: {len(updated_addresses)}")
    print(f"   Création: {'✅' if new_address else '❌'}")

if __name__ == "__main__":
    main()
