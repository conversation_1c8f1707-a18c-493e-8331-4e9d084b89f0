from rest_framework import serializers
from .models import (
    Page, BlogCategory, BlogPost, Comment,
    Media, Menu, MenuItem, SiteSetting
)
from django.contrib.auth import get_user_model

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name']

class PageSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='author',
        write_only=True,
        required=False
    )

    class Meta:
        model = Page
        fields = [
            'id', 'title', 'slug', 'content', 'template',
            'status', 'meta_description', 'meta_keywords',
            'author', 'author_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class BlogCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogCategory
        fields = ['id', 'name', 'slug', 'description', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

class BlogPostListSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    categories = BlogCategorySerializer(many=True, read_only=True)

    class Meta:
        model = BlogPost
        fields = [
            'id', 'title', 'slug', 'excerpt', 'status', 'featured',
            'featured_image', 'author', 'categories',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class CommentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    user_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='user',
        write_only=True,
        required=False
    )

    class Meta:
        model = Comment
        fields = [
            'id', 'post', 'author_name', 'author_email',
            'content', 'status', 'user', 'user_id',
            'created_at'
        ]
        read_only_fields = ['created_at', 'status']

class BlogPostDetailSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='author',
        write_only=True,
        required=False
    )
    categories = BlogCategorySerializer(many=True, read_only=True)
    category_ids = serializers.PrimaryKeyRelatedField(
        queryset=BlogCategory.objects.all(),
        source='categories',
        write_only=True,
        many=True,
        required=False
    )
    comments = CommentSerializer(many=True, read_only=True)

    class Meta:
        model = BlogPost
        fields = [
            'id', 'title', 'slug', 'content', 'excerpt',
            'status', 'featured', 'featured_image', 'author', 'author_id',
            'categories', 'category_ids', 'comments',
            'meta_description', 'meta_keywords',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class MediaSerializer(serializers.ModelSerializer):
    uploader = UserSerializer(read_only=True)
    uploader_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='uploader',
        write_only=True,
        required=False
    )
    file_url = serializers.ReadOnlyField()
    file_size = serializers.ReadOnlyField()
    file_name = serializers.ReadOnlyField()

    class Meta:
        model = Media
        fields = [
            'id', 'title', 'file', 'file_type', 'description',
            'alt_text', 'uploader', 'uploader_id', 'file_url',
            'file_size', 'file_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class MenuItemSerializer(serializers.ModelSerializer):
    page_title = serializers.ReadOnlyField(source='page.title')

    class Meta:
        model = MenuItem
        fields = [
            'id', 'menu', 'title', 'url', 'page', 'page_title',
            'parent', 'order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class MenuSerializer(serializers.ModelSerializer):
    items = MenuItemSerializer(many=True, read_only=True)

    class Meta:
        model = Menu
        fields = [
            'id', 'name', 'location', 'items',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class SiteSettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = SiteSetting
        fields = [
            'id', 'site_title', 'site_description', 'contact_email',
            'contact_phone', 'contact_address', 'facebook_url',
            'twitter_url', 'instagram_url', 'linkedin_url',
            'logo', 'favicon', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
