from django.contrib import admin
from .models import Notification, NotificationPreference

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'title', 'type', 'is_read', 'created_at')
    list_filter = ('type', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'user__email')
    readonly_fields = ('created_at',)
    fieldsets = (
        (None, {
            'fields': ('user', 'title', 'message', 'type', 'is_read', 'related_id', 'link')
        }),
        ('Dates', {
            'fields': ('created_at',)
        }),
    )

@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'email_promotions', 'push_promotions', 'email_orders', 'push_orders')
    list_filter = ('email_promotions', 'push_promotions', 'email_orders', 'push_orders')
    search_fields = ('user__email',)
    fieldsets = (
        (None, {
            'fields': ('user',)
        }),
        ('Préférences promotions', {
            'fields': ('email_promotions', 'push_promotions')
        }),
        ('Préférences commandes', {
            'fields': ('email_orders', 'push_orders')
        }),
        ('Préférences système', {
            'fields': ('email_system', 'push_system')
        }),
    )
