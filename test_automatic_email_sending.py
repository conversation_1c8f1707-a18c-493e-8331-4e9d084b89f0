#!/usr/bin/env python
"""
Test spécifique pour vérifier l'envoi automatique d'emails
lors de la publication de promotions et nouveaux produits.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
sys.path.append('josnet_backend')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
# Pas besoin d'importer mail.outbox pour ce test
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category, Promotion

User = get_user_model()


def create_test_data():
    """Créer les données de test nécessaires."""
    print("🔧 Création des données de test...")
    
    # Créer un utilisateur admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Admin',
            'last_name': 'EmailTest',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
        print(f"   ✅ Admin créé: {admin_user.email}")
    else:
        print(f"   ✅ Admin existant: {admin_user.email}")
    
    # Supprimer les anciens abonnements de test
    NewsletterSubscription.objects.filter(
        email__contains='emailtest.com'
    ).delete()
    
    # Créer des abonnés de test
    subscribers_data = [
        ('<EMAIL>', 'Alice', 'Promo', True, False),  # Promotions seulement
        ('<EMAIL>', 'Bob', 'Product', False, True),   # Nouveaux produits seulement
        ('<EMAIL>', 'Charlie', 'All', True, True), # Tout
        ('<EMAIL>', 'Diana', 'None', False, False),  # Rien
    ]
    
    subscribers = []
    for email, first_name, last_name, promo_pref, product_pref in subscribers_data:
        user, created = User.objects.get_or_create(
            email=email,
            defaults={
                'first_name': first_name,
                'last_name': last_name,
                'password': 'testpass123'
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
        
        subscription, created = NewsletterSubscription.objects.get_or_create(
            user=user,
            email=email,
            defaults={
                'preferences_promotions': promo_pref,
                'preferences_new_products': product_pref,
                'preferences_newsletters': True,
                'preferences_order_updates': True
            }
        )
        subscribers.append(subscription)
        
        if created:
            print(f"   ✅ Abonné créé: {email} (Promos: {promo_pref}, Produits: {product_pref})")
        else:
            print(f"   ✅ Abonné existant: {email}")
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Test Email Category',
        defaults={'slug': 'test-email-category'}
    )
    if created:
        print(f"   ✅ Catégorie créée: {category.name}")
    else:
        print(f"   ✅ Catégorie existante: {category.name}")
    
    return admin_user, subscribers, category


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def test_promotion_email_sending():
    """Test d'envoi automatique d'email pour une promotion."""
    print("\n🎉 TEST: Envoi automatique d'email pour promotion")
    print("=" * 60)
    
    admin_user, _, _ = create_test_data()
    
    # Compter les abonnés aux promotions AVANT
    promo_subscribers_count = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_promotions=True
    ).count()
    print(f"📊 Abonnés aux promotions: {promo_subscribers_count}")
    
    # Supprimer les anciennes promotions de test
    Promotion.objects.filter(title__contains='Test Email').delete()
    NewsletterCampaign.objects.filter(title__contains='Test Email').delete()
    
    # Note: Les emails seront affichés dans la console
    
    print("\n🔄 ÉTAPE 1: Création d'une promotion en brouillon")
    promotion = Promotion.objects.create(
        title='🔥 Test Email Promotion - 70% OFF!',
        description='Promotion de test pour vérifier l\'envoi automatique d\'emails. Profitez de 70% de réduction sur tous nos produits !',
        promotion_type='percentage',
        discount_percentage=Decimal('70.00'),
        start_date=timezone.now(),
        end_date=timezone.now() + timedelta(days=10),
        status='draft',  # Créer en brouillon d'abord
        created_by=admin_user,
        send_email_notification=True  # Activer les notifications
    )
    
    print(f"   ✅ Promotion créée: {promotion.title}")
    print(f"   📅 Début: {promotion.start_date.strftime('%d/%m/%Y %H:%M')}")
    print(f"   📅 Fin: {promotion.end_date.strftime('%d/%m/%Y %H:%M')}")
    print(f"   💰 Réduction: {promotion.discount_percentage}%")
    print(f"   📧 Notifications activées: {promotion.send_email_notification}")
    print(f"   📧 Email déjà envoyé: {promotion.email_sent}")
    
    # Vérifier qu'aucun email n'a été envoyé (promotion en brouillon)
    campaigns_before = NewsletterCampaign.objects.filter(
        campaign_type='promotion',
        title__contains='Test Email'
    ).count()
    print(f"   📨 Campagnes avant activation: {campaigns_before}")
    
    print("\n🚀 ÉTAPE 2: Activation de la promotion (déclenchement automatique)")
    promotion.status = 'active'
    promotion.save()
    
    print(f"   ✅ Promotion activée: {promotion.status}")
    
    # Vérifier si l'email a été marqué comme envoyé
    promotion.refresh_from_db()
    print(f"   📧 Email marqué comme envoyé: {promotion.email_sent}")
    if promotion.email_sent:
        print(f"   📅 Email envoyé à: {promotion.email_sent_at}")
    
    # Vérifier les campagnes créées
    campaigns_after = NewsletterCampaign.objects.filter(
        campaign_type='promotion',
        title__contains='Test Email'
    )
    print(f"   📨 Campagnes après activation: {campaigns_after.count()}")
    
    if campaigns_after.exists():
        campaign = campaigns_after.first()
        print(f"\n📧 DÉTAILS DE LA CAMPAGNE:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires ciblés: {campaign.total_recipients}")
        print(f"   ✅ Emails envoyés: {campaign.sent_count}")
        print(f"   ❌ Emails échoués: {campaign.failed_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        print(f"   📅 Envoyé le: {campaign.sent_at}")
        
        # Vérifier que le nombre de destinataires correspond aux abonnés aux promotions
        if campaign.total_recipients == promo_subscribers_count:
            print(f"   ✅ Nombre de destinataires correct!")
        else:
            print(f"   ❌ Nombre de destinataires incorrect: {campaign.total_recipients} vs {promo_subscribers_count}")
        
        return True
    else:
        print(f"   ❌ Aucune campagne créée!")
        return False


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def test_product_email_sending():
    """Test d'envoi automatique d'email pour un nouveau produit."""
    print("\n✨ TEST: Envoi automatique d'email pour nouveau produit")
    print("=" * 60)
    
    admin_user, subscribers, category = create_test_data()
    
    # Compter les abonnés aux nouveaux produits AVANT
    product_subscribers_count = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_new_products=True
    ).count()
    print(f"📊 Abonnés aux nouveaux produits: {product_subscribers_count}")
    
    # Supprimer les anciens produits de test
    Product.objects.filter(name__contains='Test Email').delete()
    NewsletterCampaign.objects.filter(title__contains='Test Email Product').delete()
    
    print("\n🔄 ÉTAPE 1: Création d'un produit en brouillon")
    product = Product.objects.create(
        name='🚀 Test Email Product - iPhone 16 Pro Max',
        slug='test-email-product-iphone-16-pro-max',
        sku='TEP001',
        description='Produit de test pour vérifier l\'envoi automatique d\'emails. Le nouveau iPhone 16 Pro Max avec des fonctionnalités révolutionnaires !',
        short_description='iPhone 16 Pro Max révolutionnaire pour test d\'emails',
        price=Decimal('1499.99'),
        sale_price=Decimal('1299.99'),
        status='draft',  # Créer en brouillon d'abord
        created_by=admin_user
    )
    product.categories.add(category)
    
    print(f"   ✅ Produit créé: {product.name}")
    print(f"   💰 Prix: {product.price}€ (Promo: {product.sale_price}€)")
    print(f"   📱 SKU: {product.sku}")
    print(f"   📂 Catégorie: {category.name}")
    print(f"   📊 Statut: {product.status}")
    
    # Vérifier qu'aucun email n'a été envoyé (produit en brouillon)
    campaigns_before = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Test Email Product'
    ).count()
    print(f"   📨 Campagnes avant publication: {campaigns_before}")
    
    print("\n🚀 ÉTAPE 2: Publication du produit (déclenchement automatique)")
    product.status = 'published'
    product.save()
    
    print(f"   ✅ Produit publié: {product.status}")
    
    # Vérifier les campagnes créées
    campaigns_after = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Test Email Product'
    )
    print(f"   📨 Campagnes après publication: {campaigns_after.count()}")
    
    if campaigns_after.exists():
        campaign = campaigns_after.first()
        print(f"\n📧 DÉTAILS DE LA CAMPAGNE:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires ciblés: {campaign.total_recipients}")
        print(f"   ✅ Emails envoyés: {campaign.sent_count}")
        print(f"   ❌ Emails échoués: {campaign.failed_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        print(f"   📅 Envoyé le: {campaign.sent_at}")
        
        # Vérifier que le nombre de destinataires correspond aux abonnés aux nouveaux produits
        if campaign.total_recipients == product_subscribers_count:
            print(f"   ✅ Nombre de destinataires correct!")
        else:
            print(f"   ❌ Nombre de destinataires incorrect: {campaign.total_recipients} vs {product_subscribers_count}")
        
        return True
    else:
        print(f"   ❌ Aucune campagne créée!")
        return False


def test_email_filtering():
    """Test du filtrage des emails selon les préférences."""
    print("\n🎯 TEST: Filtrage des emails selon les préférences")
    print("=" * 60)
    
    # Compter les abonnés par préférence
    total_subscribers = NewsletterSubscription.objects.filter(is_active=True).count()
    promo_subscribers = NewsletterSubscription.objects.filter(
        is_active=True, preferences_promotions=True
    ).count()
    product_subscribers = NewsletterSubscription.objects.filter(
        is_active=True, preferences_new_products=True
    ).count()
    
    print(f"📊 STATISTIQUES DES ABONNÉS:")
    print(f"   👥 Total abonnés actifs: {total_subscribers}")
    print(f"   🎉 Abonnés promotions: {promo_subscribers}")
    print(f"   ✨ Abonnés nouveaux produits: {product_subscribers}")
    
    # Vérifier les abonnés individuels
    print(f"\n👤 DÉTAIL DES ABONNÉS:")
    for sub in NewsletterSubscription.objects.filter(is_active=True):
        print(f"   📧 {sub.email}:")
        print(f"      🎉 Promotions: {'✅' if sub.preferences_promotions else '❌'}")
        print(f"      ✨ Nouveaux produits: {'✅' if sub.preferences_new_products else '❌'}")
    
    return True


def main():
    """Fonction principale de test."""
    print("🚀 TEST D'ENVOI AUTOMATIQUE D'EMAILS")
    print("=" * 80)
    print("Ce test vérifie que les emails sont envoyés automatiquement")
    print("lorsque l'admin publie des promotions et nouveaux produits.\n")
    
    results = []
    
    try:
        # Test 1: Filtrage des abonnés
        print("🔍 PHASE 1: Vérification du filtrage des abonnés")
        filtering_success = test_email_filtering()
        results.append(("Filtrage des abonnés", filtering_success))
        
        # Test 2: Email automatique pour promotion
        print("\n🎉 PHASE 2: Test d'email automatique pour promotion")
        promotion_success = test_promotion_email_sending()
        results.append(("Email automatique promotion", promotion_success))
        
        # Test 3: Email automatique pour nouveau produit
        print("\n✨ PHASE 3: Test d'email automatique pour nouveau produit")
        product_success = test_product_email_sending()
        results.append(("Email automatique nouveau produit", product_success))
        
        # Résumé final
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ DES TESTS D'ENVOI AUTOMATIQUE")
        print("=" * 80)
        
        passed = 0
        failed = 0
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("\n" + "-" * 80)
        print(f"Total: {len(results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print("\n🎉 TOUS LES TESTS D'ENVOI AUTOMATIQUE SONT PASSÉS !")
            print("✅ Les emails sont bien envoyés automatiquement")
            print("✅ Le filtrage selon les préférences fonctionne")
            print("✅ Les campagnes sont correctement créées")
            print("✅ Le système est opérationnel pour la production")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué.")
            print("Vérifiez la configuration des signaux Django.")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DES TESTS:")
        print(f"   {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
