# 🔧 Guide de correction des notifications - Erreur 401

## ❌ **Problème identifié :**
```
notificationApi.ts:154 ❌ Erreur API getUnreadNotifications: 
AxiosError {message: 'Request failed with status code 401', name: 'AxiosError', code: 'ERR_BAD_REQUEST'}
```

## ✅ **Solutions appliquées :**

### **1. Amélioration de la gestion d'authentification**

**Fichier modifié :** `src/services/notificationApi.ts`

**Changements :**
- ✅ Vérification du token avant l'appel API
- ✅ Gestion spécifique des erreurs 401 (non authentifié)
- ✅ Retour de données vides au lieu d'erreur pour utilisateurs non connectés
- ✅ Messages de log plus clairs

**Avant :**
```typescript
// Appel direct sans vérification
const response = await axios.get(url, {
  headers: { Authorization: `Bearer ${getAuthToken()}` }
});
```

**Après :**
```typescript
// Vérification du token d'abord
const token = getAuthToken();
if (!token) {
  return { count: 0, results: [] }; // Données vides
}

// Gestion spécifique des erreurs 401
if (error.response?.status === 401) {
  console.log('🔐 Token expiré ou invalide');
  return { count: 0, results: [] };
}
```

### **2. Composant de debug créé**

**Nouveau fichier :** `src/components/debug/AuthDebug.tsx`
**Nouvelle page :** `src/pages/debug/AuthDebugPage.tsx`
**Nouvelle route :** `/debug/auth`

**Fonctionnalités :**
- ✅ Affichage de l'état d'authentification
- ✅ Visualisation du token JWT
- ✅ Test en temps réel des notifications
- ✅ Recommandations de correction

## 🧪 **Comment tester la correction :**

### **Étape 1 : Accéder à la page de debug**
1. Ouvrir le navigateur
2. Aller sur : `http://localhost:5173/debug/auth`
3. Examiner l'état d'authentification

### **Étape 2 : Tester sans connexion**
1. Si vous n'êtes pas connecté, vous devriez voir :
   - ❌ "Authentifié: Non"
   - ⚠️ "Aucun token"
   - 📧 Test notifications retourne 0 résultats (normal)

### **Étape 3 : Se connecter et tester**
1. Aller sur `/login`
2. Se connecter avec un compte valide
3. Retourner sur `/debug/auth`
4. Vous devriez voir :
   - ✅ "Authentifié: Oui"
   - 🔑 Token JWT affiché
   - 📧 Test notifications fonctionne

### **Étape 4 : Tester les notifications**
1. Cliquer sur "Tester les notifications"
2. Si connecté : ✅ Succès avec données
3. Si non connecté : ⚠️ Retour de données vides (pas d'erreur)

## 🎯 **Résultats attendus :**

### **Utilisateur NON connecté :**
- ✅ Plus d'erreur 401 dans la console
- ✅ Notifications vides retournées silencieusement
- ✅ Interface fonctionne sans crash

### **Utilisateur connecté :**
- ✅ Notifications chargées normalement
- ✅ Cloche de notification fonctionne
- ✅ Page `/account/notifications` accessible

## 🔍 **Diagnostic des problèmes :**

### **Si l'erreur 401 persiste :**

1. **Vérifier le token :**
   - Aller sur `/debug/auth`
   - Vérifier si le token existe
   - Vérifier si l'utilisateur est connecté

2. **Vérifier l'expiration :**
   - Les tokens JWT expirent après un certain temps
   - Se reconnecter si nécessaire

3. **Vérifier le backend :**
   - S'assurer que le serveur Django fonctionne
   - Vérifier l'endpoint `/api/v1/notifications/notifications/`

### **Si les notifications ne s'affichent pas :**

1. **Créer des notifications de test :**
   ```python
   # Dans le backend Django
   python manage.py shell
   from notifications.models import Notification
   from django.contrib.auth import get_user_model
   User = get_user_model()
   user = User.objects.get(email='<EMAIL>')
   Notification.objects.create(
       user=user,
       title='Test notification',
       message='Ceci est un test',
       type='info'
   )
   ```

2. **Vérifier les permissions :**
   - L'utilisateur doit être connecté
   - Le token doit être valide

## 📋 **Checklist de vérification :**

- [ ] ✅ Plus d'erreur 401 dans la console
- [ ] ✅ Page `/debug/auth` accessible
- [ ] ✅ État d'authentification affiché correctement
- [ ] ✅ Test notifications fonctionne (connecté)
- [ ] ✅ Test notifications retourne vide (non connecté)
- [ ] ✅ Cloche de notification fonctionne
- [ ] ✅ Page `/account/notifications` accessible

## 🚀 **Prochaines étapes :**

1. **Tester la correction :**
   - Utiliser la page `/debug/auth`
   - Vérifier que l'erreur 401 a disparu

2. **Créer des notifications de test :**
   - Utiliser l'admin Django
   - Ou créer via le shell Django

3. **Tester l'expérience utilisateur :**
   - Navigation normale
   - Connexion/déconnexion
   - Réception de notifications

## 🎉 **Résultat final attendu :**

- ✅ **Plus d'erreur 401** dans la console
- ✅ **Notifications fonctionnelles** pour utilisateurs connectés
- ✅ **Gestion gracieuse** pour utilisateurs non connectés
- ✅ **Interface stable** sans crash
- ✅ **Expérience utilisateur fluide**

**La correction est maintenant appliquée ! 🎊**
