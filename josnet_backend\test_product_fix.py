#!/usr/bin/env python
"""
Test simple pour vérifier que la correction des signaux produits fonctionne.
"""

import os
import sys
import django
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category

User = get_user_model()


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def test_product_signal_fix():
    """Test de la correction des signaux produits."""
    print("🧪 TEST: Correction des signaux produits")
    print("=" * 50)
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Fix',
            'last_name': 'Test',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Fix Category',
        defaults={'slug': 'fix-category'}
    )
    
    # Créer un abonné aux nouveaux produits
    subscription, created = NewsletterSubscription.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'preferences_promotions': False,
            'preferences_new_products': True,
            'preferences_newsletters': True,
            'preferences_order_updates': True
        }
    )
    
    print(f"✅ Admin: {admin_user.email}")
    print(f"✅ Catégorie: {category.name}")
    print(f"✅ Abonné: {subscription.email} (Nouveaux produits: {subscription.preferences_new_products})")
    
    # Compter les abonnés aux nouveaux produits
    product_subscribers = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_new_products=True
    ).count()
    print(f"📊 Total abonnés nouveaux produits: {product_subscribers}")
    
    # Nettoyer les anciens produits de test
    Product.objects.filter(name__contains='Fix Product').delete()
    NewsletterCampaign.objects.filter(title__contains='Fix Product').delete()
    
    print(f"\n🔄 Test 1: Création d'un produit directement en publié")
    
    # Test 1: Créer un produit directement en publié
    product1 = Product.objects.create(
        name='Fix Product Direct Published',
        slug='fix-product-direct-published',
        sku='FPD001',
        description='Produit créé directement en publié.',
        short_description='Produit direct publié',
        price=Decimal('199.99'),
        status='published',  # Directement publié
        created_by=admin_user
    )
    product1.categories.add(category)
    
    print(f"✅ Produit créé directement en publié: {product1.name}")
    
    # Vérifier les campagnes
    campaigns1 = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Fix Product Direct'
    )
    print(f"📨 Campagnes pour produit direct: {campaigns1.count()}")
    
    if campaigns1.exists():
        campaign = campaigns1.first()
        print(f"   ✅ Campagne créée: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        test1_success = True
    else:
        print(f"   ❌ Aucune campagne créée pour produit direct")
        test1_success = False
    
    print(f"\n🔄 Test 2: Création en brouillon puis publication")
    
    # Test 2: Créer en brouillon puis publier
    product2 = Product.objects.create(
        name='Fix Product Draft to Published',
        slug='fix-product-draft-to-published',
        sku='FDP001',
        description='Produit créé en brouillon puis publié.',
        short_description='Produit brouillon vers publié',
        price=Decimal('299.99'),
        status='draft',  # Créé en brouillon
        created_by=admin_user
    )
    product2.categories.add(category)
    
    print(f"✅ Produit créé en brouillon: {product2.name}")
    
    # Vérifier qu'aucune campagne n'existe encore
    campaigns2_before = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Fix Product Draft'
    ).count()
    print(f"📨 Campagnes avant publication: {campaigns2_before}")
    
    # Publier le produit
    product2.status = 'published'
    product2.save()
    
    print(f"✅ Produit publié: {product2.status}")
    
    # Vérifier les campagnes après publication
    campaigns2_after = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Fix Product Draft'
    )
    print(f"📨 Campagnes après publication: {campaigns2_after.count()}")
    
    if campaigns2_after.exists():
        campaign = campaigns2_after.first()
        print(f"   ✅ Campagne créée: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        test2_success = True
    else:
        print(f"   ❌ Aucune campagne créée pour produit brouillon→publié")
        test2_success = False
    
    # Résumé
    print(f"\n📊 RÉSUMÉ:")
    print(f"✅ Test 1 (direct publié): {'PASS' if test1_success else 'FAIL'}")
    print(f"✅ Test 2 (brouillon→publié): {'PASS' if test2_success else 'FAIL'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 TOUS LES TESTS SONT PASSÉS!")
        print(f"✅ Les signaux produits fonctionnent correctement")
        return True
    else:
        print(f"\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        return False


def main():
    """Fonction principale."""
    print("🚀 TEST DE CORRECTION DES SIGNAUX PRODUITS")
    print("=" * 60)
    
    try:
        success = test_product_signal_fix()
        
        if success:
            print(f"\n✅ CORRECTION RÉUSSIE: Les signaux produits fonctionnent maintenant!")
        else:
            print(f"\n❌ CORRECTION ÉCHOUÉE: Il reste des problèmes avec les signaux")
            
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
