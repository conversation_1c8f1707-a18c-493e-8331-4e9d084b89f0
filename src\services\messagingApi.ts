import axios, { AxiosError } from 'axios';
import { API_BASE_URL } from '@/config/api';
import { getAuthToken, logout } from '@/utils/auth';
import { toast } from '@/hooks/use-toast';


// Configuration pour activer les données fictives en cas d'erreur API
const USE_MOCK_DATA_ON_ERROR = true;

// Configuration pour désactiver temporairement les appels API problématiques
// Mettre à false pour utiliser les API réelles
const DISABLE_PROBLEMATIC_APIS = false;

// Utiliser les API v2 plus robustes
const USE_V2_API = true;

// Cache pour stocker les résultats des requêtes API
const apiCache: Record<string, { data: any, timestamp: number }> = {};

// Durée de validité du cache en millisecondes (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

// Types
export interface User {
  id: number;
  email: string;
  full_name: string;
  profile_picture?: string;
  role: string;
}

export interface Attachment {
  id: number;
  file: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  created_at: string;
}

export interface Message {
  id: number;
  conversation: number;
  conversation_id?: number; // Pour la compatibilité avec les données fictives
  sender: User;
  content: string;
  created_at: string;
  is_from_admin: boolean;
  attachments: Attachment[];
}

export interface ConversationPreview {
  content: string;
  created_at: string;
  is_from_admin: boolean;
  sender_name: string;
}

export interface Category {
  id: number;
  name: string;
  description?: string; // Optionnel pour les données fictives
  color: string;
  icon?: string; // Optionnel pour les données fictives
  is_active: boolean;
}

export interface Tag {
  id: number;
  name: string;
  color: string;
  is_active: boolean;
}

export interface OrderDetails {
  id: number;
  order_number: string;
  created_at: string;
  status: string;
  total: string;
}

export interface ResponseTemplate {
  id: number;
  name: string;
  content: string;
  category: Category | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: User;
}

export interface TemplateGroup {
  category: Category | null;
  templates: ResponseTemplate[];
}

export interface ConversationStatistics {
  total_conversations: number;
  by_status: Record<string, number>;
  by_priority: Record<string, number>;
  by_category: Record<string, number>;
  avg_response_time_hours: number | null;
  new_conversations: number;
  in_progress_conversations: number;
  resolved_conversations: number;
  average_response_time: number;
  customer_satisfaction: number;
}

export interface Conversation {
  id: number;
  subject: string;
  customer: User;
  status: 'new' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  last_message_at: string;
  last_message_preview?: ConversationPreview;
  unread: boolean;
  unread_count: number;
  message_count: number;
  messages: Message[];
  category?: Category;
  tags?: Tag[];
  assigned_to?: User;
  order_number?: string;
  order_details?: OrderDetails;
  is_read_by_admin: boolean;
  is_read_by_customer: boolean;
}

export interface ConversationListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Conversation[];
}

export interface CreateConversationRequest {
  subject: string;
  initial_message: string;
  customer_email: string;  // Email du client (OBLIGATOIRE)
  customer_name?: string;  // Nom du client (pour nouveaux clients)
  customer_phone?: string; // Téléphone du client (pour nouveaux clients)
  uploaded_files?: File[];
  category_id?: number;
  tag_ids?: number[];
  order_id?: number;
  priority?: string;
}

export interface CreateMessageRequest {
  content: string;
  uploaded_files?: File[];
}

export interface ConversationFilters {
  status?: string | null;
  priority?: string | null;
  category?: number | null;
  tag?: number | null;
  search?: string;
  order?: number | null;
  start_date?: string | null;
  end_date?: string | null;
}

// Données fictives pour les conversations
const mockConversations: Conversation[] = [
  {
    id: 1,
    subject: "Question sur ma commande #12345",
    customer: { id: 1, email: "<EMAIL>", full_name: "Jean Dupont", role: "customer" },
    status: "new",
    priority: "medium",
    created_at: new Date(Date.now() - 3600000 * 24).toISOString(),
    updated_at: new Date(Date.now() - 3600000 * 12).toISOString(),
    last_message_at: new Date(Date.now() - 3600000 * 12).toISOString(),
    unread: true,
    unread_count: 1,
    message_count: 2,
    is_read_by_admin: false,
    is_read_by_customer: true,
    messages: [
      {
        id: 1, conversation: 1, 
        sender: { id: 1, email: "<EMAIL>", full_name: "Jean Dupont", role: "customer" },
        content: "Bonjour, j'ai une question sur ma commande #12345.",
        created_at: new Date(Date.now() - 3600000 * 24).toISOString(),
        is_from_admin: false, attachments: []
      },
      {
        id: 2, conversation: 1,
        sender: { id: 101, email: "<EMAIL>", full_name: "Support Josnet", role: "staff" },
        content: "Bonjour, nous avons vérifié votre commande. Elle est en cours de préparation et sera expédiée demain.",
        created_at: new Date(Date.now() - 3600000 * 12).toISOString(),
        is_from_admin: true, attachments: []
      }
    ],
    last_message_preview: {
      content: "Bonjour, nous avons vérifié votre commande. Elle est en cours de préparation et sera expédiée demain.",
      created_at: new Date(Date.now() - 3600000 * 12).toISOString(),
      is_from_admin: true,
      sender_name: "Support Josnet"
    }
  },
  {
    id: 2,
    subject: "Problème avec mon compte",
    customer: { id: 2, email: "<EMAIL>", full_name: "Marie Martin", role: "customer" },
    status: "in_progress",
    priority: "high",
    created_at: new Date(Date.now() - 3600000 * 48).toISOString(),
    updated_at: new Date(Date.now() - 3600000 * 6).toISOString(),
    last_message_at: new Date(Date.now() - 3600000 * 6).toISOString(),
    unread: false,
    unread_count: 0,
    message_count: 3,
    is_read_by_admin: true,
    is_read_by_customer: true,
    messages: [
      {
        id: 3, conversation: 2,
        sender: { id: 2, email: "<EMAIL>", full_name: "Marie Martin", role: "customer" },
        content: "Bonjour, je n'arrive pas à me connecter à mon compte.",
        created_at: new Date(Date.now() - 3600000 * 48).toISOString(),
        is_from_admin: false, attachments: []
      },
      {
        id: 4, conversation: 2,
        sender: { id: 101, email: "<EMAIL>", full_name: "Support Josnet", role: "staff" },
        content: "Bonjour, nous avons réinitialisé votre mot de passe. Veuillez vérifier vos emails.",
        created_at: new Date(Date.now() - 3600000 * 24).toISOString(),
        is_from_admin: true, attachments: []
      },
      {
        id: 5, conversation: 2,
        sender: { id: 2, email: "<EMAIL>", full_name: "Marie Martin", role: "customer" },
        content: "C'est <EMAIL>. Merci de votre aide.",
        created_at: new Date(Date.now() - 3600000 * 6).toISOString(),
        is_from_admin: false, attachments: []
      }
    ],
    last_message_preview: {
      content: "C'est <EMAIL>. Merci de votre aide.",
      created_at: new Date(Date.now() - 3600000 * 6).toISOString(),
      is_from_admin: false,
      sender_name: "Marie Martin"
    }
  }
];

// Données fictives pour les catégories
const mockCategories: Category[] = [
  {
    id: 1,
    name: "Commandes",
    color: "#4CAF50",
    is_active: true
  },
  {
    id: 2,
    name: "Compte",
    color: "#2196F3",
    is_active: true
  },
  {
    id: 3,
    name: "Produits",
    color: "#FF9800",
    is_active: true
  },
  {
    id: 4,
    name: "Paiement",
    color: "#F44336",
    is_active: true
  }
];

// Données fictives pour les tags
const mockTags: Tag[] = [
  {
    id: 1,
    name: "Urgent",
    color: "#F44336",
    is_active: true
  },
  {
    id: 2,
    name: "Résolu",
    color: "#4CAF50",
    is_active: true
  },
  {
    id: 3,
    name: "En attente",
    color: "#FF9800",
    is_active: true
  },
  {
    id: 4,
    name: "VIP",
    color: "#9C27B0",
    is_active: true
  }
];

// Données fictives pour les modèles de réponse
const mockResponseTemplates: TemplateGroup[] = [
  {
    category: {
      id: 1,
      name: "Commandes",
      color: "#4CAF50",
      is_active: true
    },
    templates: [
      {
        id: 1,
        name: "Confirmation de commande",
        content: "Bonjour, nous avons bien reçu votre commande. Elle est en cours de traitement et sera expédiée dans les plus brefs délais.",
        category: {
          id: 1,
          name: "Commandes",
          color: "#4CAF50",
          is_active: true
        },
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: {
          id: 1,
          email: "<EMAIL>",
          full_name: "Admin",
          role: "admin"
        }
      },
      {
        id: 2,
        name: "Retard de livraison",
        content: "Bonjour, nous sommes désolés pour le retard de livraison de votre commande. Nous faisons tout notre possible pour vous la livrer au plus vite.",
        category: {
          id: 1,
          name: "Commandes",
          color: "#4CAF50",
          is_active: true
        },
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: {
          id: 1,
          email: "<EMAIL>",
          full_name: "Admin",
          role: "admin"
        }
      }
    ]
  },
  {
    category: {
      id: 2,
      name: "Compte",
      color: "#2196F3",
      is_active: true
    },
    templates: [
      {
        id: 3,
        name: "Réinitialisation de mot de passe",
        content: "Bonjour, pour réinitialiser votre mot de passe, veuillez cliquer sur le lien 'Mot de passe oublié' sur la page de connexion.",
        category: {
          id: 2,
          name: "Compte",
          color: "#2196F3",
          is_active: true
        },
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: {
          id: 1,
          email: "<EMAIL>",
          full_name: "Admin",
          role: "admin"
        }
      }
    ]
  }
];

// Fonction utilitaire pour vérifier et récupérer les données du cache
const getCachedData = (cacheKey: string): any | null => {
  const cachedItem = apiCache[cacheKey];
  if (cachedItem && Date.now() - cachedItem.timestamp < CACHE_DURATION) {
    console.log(`Using cached data for ${cacheKey}`);
    return cachedItem.data;
  }
  return null;
};

// Fonction utilitaire pour mettre en cache les données
const setCachedData = (cacheKey: string, data: any): void => {
  apiCache[cacheKey] = {
    data,
    timestamp: Date.now()
  };
};

// Helper function to handle API errors
const handleApiError = (error: any, fallbackData?: any, cacheKey?: string) => {
  // Si nous avons des données en cache, les utiliser en priorité
  if (cacheKey) {
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;

    // Handle 401 Unauthorized errors (expired token)
    if (axiosError.response?.status === 401) {
      toast({
        title: "Session expirée",
        description: "Votre session a expiré. Veuillez vous reconnecter.",
        variant: "destructive"
      });
      logout();
      window.location.href = '/login';
      return fallbackData;
    }

    // Handle 500 Internal Server Error
    if (axiosError.response?.status === 500) {
      // Afficher le toast seulement la première fois
      if (!apiCache['error_toast_shown']) {
        toast({
          title: "Erreur serveur",
          description: "Une erreur est survenue sur le serveur. Utilisation des données de démonstration.",
          variant: "destructive"
        });
        apiCache['error_toast_shown'] = { data: true, timestamp: Date.now() };
      }

      console.error('Server error:', axiosError.message);

      // Si les données fictives sont activées, retourner les données fictives appropriées
      if (USE_MOCK_DATA_ON_ERROR) {
        let mockData: any = null;

        // Afficher un message d'erreur plus informatif dans la console
        if (axios.isAxiosError(error)) {
          const axiosError = error as AxiosError;
          console.warn(`API Error: ${axiosError.message}`);
          if (axiosError.response) {
            console.warn(`Status: ${axiosError.response.status}`);
            console.warn(`Data:`, axiosError.response.data);
          }
        }

        if (fallbackData && typeof fallbackData === 'object') {
          if ('results' in fallbackData) {
            mockData = {
              count: mockConversations.length,
              next: null,
              previous: null,
              results: mockConversations
            };
          } else if ('id' in fallbackData) {
            // Recherche d'une conversation spécifique
            const conversationId = (fallbackData as any).id || 1;
            mockData = mockConversations.find(c => c.id === conversationId) || mockConversations[0];
          } else {
            mockData = fallbackData;
          }

          // Mettre en cache les données fictives si un cacheKey est fourni
          if (cacheKey && mockData) {
            setCachedData(cacheKey, mockData);
          }

          return mockData;
        }
      }

      return fallbackData;
    }

    // Handle 404 Not Found
    if (axiosError.response?.status === 404) {
      toast({
        title: "Ressource non trouvée",
        description: "La ressource demandée n'existe pas ou a été supprimée.",
        variant: "destructive"
      });
      console.error('Resource not found:', axiosError);
      return fallbackData;
    }

    // Get error message from API if available
    let errorMessage = 'Une erreur est survenue';

    if (axiosError.response?.data && typeof axiosError.response.data === 'object') {
      const responseData = axiosError.response.data as Record<string, unknown>;
      if ('detail' in responseData && typeof responseData.detail === 'string') {
        errorMessage = responseData.detail;
      } else if ('message' in responseData && typeof responseData.message === 'string') {
        errorMessage = responseData.message;
      } else if ('error' in responseData && typeof responseData.error === 'string') {
        errorMessage = responseData.error;
      }
    } else if (axiosError.message) {
      errorMessage = axiosError.message;
    }

    toast({
      title: "Erreur",
      description: errorMessage,
      variant: "destructive"
    });

    console.error('API Error:', errorMessage);
    return fallbackData;
  }

  console.error('Unexpected error:', error);
  toast({
    title: "Erreur inattendue",
    description: "Une erreur inattendue est survenue. Veuillez réessayer.",
    variant: "destructive"
  });

  return fallbackData;
};

// Messaging API service
const messagingApi = {
  // Conversations
  getConversations: async (filters?: ConversationFilters): Promise<ConversationListResponse> => {
    // Générer une clé de cache basée sur les filtres
    const filterKey = filters ? JSON.stringify(filters) : 'default';
    const cacheKey = `conversations_${filterKey}`;

    // Vérifier si les données sont en cache
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Si les API problématiques sont désactivées, retourner directement les données fictives
    if (DISABLE_PROBLEMATIC_APIS) {
      const mockData = {
        count: mockConversations.length,
        next: null,
        previous: null,
        results: mockConversations
      };
      setCachedData(cacheKey, mockData);
      return mockData;
    }

    try {
      // Process filters to handle "none" values
      let processedFilters = { ...filters };
      if (processedFilters) {
        if (processedFilters.status === "none") processedFilters.status = null;
        if (processedFilters.priority === "none") processedFilters.priority = null;
        if (processedFilters.category !== undefined && processedFilters.category !== null && String(processedFilters.category) === "none") processedFilters.category = null;
        if (processedFilters.tag !== undefined && processedFilters.tag !== null && String(processedFilters.tag) === "none") processedFilters.tag = null;
      }

      const token = getAuthToken();
      if (!token) {
        console.error('No auth token available');
        return handleApiError(
          new Error('No auth token available'),
          { count: 0, next: null, previous: null, results: [] },
          cacheKey
        );
      }

      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations/' : 'conversations/';

      const response = await axios.get(`${API_BASE_URL}/messaging/${apiPath}`, {
        params: processedFilters,
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // Mettre en cache les données
      setCachedData(cacheKey, response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching conversations:', error);
      // Return a fallback empty response instead of throwing
      return handleApiError(
        error,
        { count: 0, next: null, previous: null, results: [] },
        cacheKey
      );
    }
  },

  getConversation: async (id: number): Promise<Conversation> => {
    const cacheKey = `conversation_${id}`;

    // Vérifier si les données sont en cache
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Si les API problématiques sont désactivées, retourner directement les données fictives
    if (DISABLE_PROBLEMATIC_APIS) {
      const conversation = mockConversations.find(c => c.id === id) || mockConversations[0];
      setCachedData(cacheKey, conversation);
      return conversation;
    }

    try {
      const token = getAuthToken();
      if (!token) {
        console.error('No auth token available');
        return handleApiError(
          new Error('No auth token available'),
          {} as Conversation,
          cacheKey
        );
      }

      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.get(`${API_BASE_URL}/messaging/${apiPath}/${id}/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // Mettre en cache les données
      setCachedData(cacheKey, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching conversation ${id}:`, error);
      return handleApiError(error, {} as Conversation, cacheKey);
    }
  },

  createConversation: async (data: CreateConversationRequest): Promise<Conversation> => {
    try {
      // Create FormData for file uploads
      const formData = new FormData();
      formData.append('subject', data.subject);
      formData.append('initial_message', data.initial_message);
      formData.append('customer_email', data.customer_email); // OBLIGATOIRE

      // Informations du client (pour nouveaux clients)
      if (data.customer_name) {
        formData.append('customer_name', data.customer_name);
      }

      if (data.customer_phone) {
        formData.append('customer_phone', data.customer_phone);
      }

      if (data.uploaded_files) {
        data.uploaded_files.forEach(file => {
          formData.append('uploaded_files', file);
        });
      }

      if (data.category_id) {
        formData.append('category_id', data.category_id.toString());
      }

      if (data.tag_ids && data.tag_ids.length > 0) {
        data.tag_ids.forEach(tagId => {
          formData.append('tag_ids', tagId.toString());
        });
      }

      if (data.order_id) {
        formData.append('order_id', data.order_id.toString());
      }

      if (data.priority) {
        formData.append('priority', data.priority);
      }

      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations/' : 'conversations/';

      const response = await axios.post(`${API_BASE_URL}/messaging/${apiPath}`, formData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  },

  updateConversationStatus: async (id: number, status: string): Promise<Conversation> => {
    try {
      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.post(
        `${API_BASE_URL}/messaging/${apiPath}/${id}/update_status/`,
        { status },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating conversation ${id} status:`, error);
      throw error;
    }
  },

  updateConversationPriority: async (id: number, priority: string): Promise<Conversation> => {
    try {
      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.post(
        `${API_BASE_URL}/messaging/${apiPath}/${id}/update_priority/`,
        { priority },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating conversation ${id} priority:`, error);
      throw error;
    }
  },

  assignConversation: async (id: number, userId: number | null): Promise<Conversation> => {
    try {
      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.post(
        `${API_BASE_URL}/messaging/${apiPath}/${id}/assign/`,
        { user_id: userId },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error assigning conversation ${id}:`, error);
      throw error;
    }
  },

  categorizeConversation: async (id: number, categoryId: number | null, tagIds: number[]): Promise<Conversation> => {
    try {
      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.post(
        `${API_BASE_URL}/messaging/${apiPath}/${id}/categorize/`,
        {
          category_id: categoryId,
          tag_ids: tagIds
        },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error categorizing conversation ${id}:`, error);
      throw error;
    }
  },

  getConversationStatistics: async (startDate?: string, endDate?: string): Promise<ConversationStatistics> => {
    try {
      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.get(
        `${API_BASE_URL}/messaging/${apiPath}/statistics/`,
        {
          params: {
            start_date: startDate,
            end_date: endDate
          },
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching conversation statistics:', error);
      throw error;
    }
  },

  getUnreadCount: async (): Promise<{ unread_count: number }> => {
    try {
      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.get(`${API_BASE_URL}/messaging/${apiPath}/unread_count/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching unread count:', error);
      throw error;
    }
  },

  // Messages
  getMessages: async (conversationId: number): Promise<Message[]> => {
    const cacheKey = `messages_${conversationId}`;

    // Vérifier si les données sont en cache
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Si les API problématiques sont désactivées, retourner les messages de la conversation fictive
    if (DISABLE_PROBLEMATIC_APIS) {
      const conversation = mockConversations.find(c => c.id === conversationId);
      if (conversation && conversation.messages) {
        setCachedData(cacheKey, conversation.messages);
        return conversation.messages;
      }
      return [];
    }

    try {
      const token = getAuthToken();
      if (!token) {
        console.error('No auth token available');
        return [];
      }

      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.get(
        `${API_BASE_URL}/messaging/${apiPath}/${conversationId}/messages/`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Mettre en cache les données
      setCachedData(cacheKey, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching messages for conversation ${conversationId}:`, error);

      // En cas d'erreur, retourner les messages de la conversation fictive
      if (USE_MOCK_DATA_ON_ERROR) {
        const conversation = mockConversations.find(c => c.id === conversationId);
        if (conversation && conversation.messages) {
          setCachedData(cacheKey, conversation.messages);
          return conversation.messages;
        }
      }

      return [];
    }
  },

  createMessage: async (conversationId: number, data: CreateMessageRequest): Promise<Message> => {
    // Générer une clé de cache pour la conversation
    const conversationCacheKey = `conversation_${conversationId}`;

    try {
      // Create FormData for file uploads
      const formData = new FormData();
      formData.append('content', data.content);

      if (data.uploaded_files) {
        data.uploaded_files.forEach(file => {
          formData.append('uploaded_files', file);
        });
      }

      // Si les API problématiques sont désactivées, simuler l'ajout d'un message
      if (DISABLE_PROBLEMATIC_APIS) {
        // Créer un nouveau message fictif
        const newMessage: Message = {
          id: Date.now(),
          conversation: conversationId,
          sender: { id: 99, email: "<EMAIL>", full_name: "Mock User", role: "customer" },
          content: data.content,
          created_at: new Date().toISOString(),
          is_from_admin: false,
          attachments: []
        };

        // Mettre à jour les données en cache
        const cachedConversation = getCachedData(conversationCacheKey) as Conversation;
        if (cachedConversation) {
          // Ajouter le message à la conversation
          const updatedConversation = {
            ...cachedConversation,
            messages: [...(cachedConversation.messages || []), newMessage],
            last_message_at: newMessage.created_at,
            last_message_preview: { 
              content: data.content,
              created_at: newMessage.created_at,
              is_from_admin: newMessage.is_from_admin,
              sender_name: newMessage.sender.full_name
            }
          };

          // Mettre à jour le cache
          setCachedData(conversationCacheKey, updatedConversation);

          // Mettre à jour la liste des conversations en cache
          const conversationsCache = getCachedData('conversations_default');
          if (conversationsCache && conversationsCache.results) {
            const updatedResults = conversationsCache.results.map((conv: Conversation) =>
              conv.id === conversationId
                ? {
                    ...conv,
                    last_message_at: newMessage.created_at,
                    last_message_preview: {
                      content: data.content,
                      created_at: newMessage.created_at,
                      is_from_admin: false
                    }
                  }
                : conv
            );

            setCachedData('conversations_default', {
              ...conversationsCache,
              results: updatedResults
            });
          }
        }

        return newMessage;
      }

      // Utiliser l'API v2 si activée
      const apiPath = USE_V2_API ? 'v2/conversations' : 'conversations';

      const response = await axios.post(
        `${API_BASE_URL}/messaging/${apiPath}/${conversationId}/messages/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      // Invalider les caches pour forcer un rechargement des données
      delete apiCache[conversationCacheKey];
      Object.keys(apiCache).forEach(key => {
        if (key.startsWith('conversations_')) {
          delete apiCache[key];
        }
      });



      return response.data;
    } catch (error) {
      console.error(`Error creating message in conversation ${conversationId}:`, error);

      // En cas d'erreur, créer un message fictif et mettre à jour le cache
      if (USE_MOCK_DATA_ON_ERROR) {
        const newMessage: Message = {
          id: Date.now(),
          conversation: conversationId,
          sender: { id: 99, email: "<EMAIL>", full_name: "Error User", role: "customer" },
          content: data.content,
          created_at: new Date().toISOString(),
          is_from_admin: false,
          attachments: []
        };

        // Invalider les caches pour forcer un rechargement
        delete apiCache[conversationCacheKey];
        Object.keys(apiCache).forEach(key => {
          if (key.startsWith('conversations_')) {
            delete apiCache[key];
          }
        });

        return newMessage;
      }

      throw error;
    }
  },

  // Categories
  getCategories: async (): Promise<Category[]> => {
    const cacheKey = 'categories';

    // Vérifier si les données sont en cache
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Si les API problématiques sont désactivées, retourner directement les données fictives
    if (DISABLE_PROBLEMATIC_APIS) {
      setCachedData(cacheKey, mockCategories);
      return mockCategories;
    }

    try {
      const token = getAuthToken();
      if (!token) {
        console.error('No auth token available');
        const data = USE_MOCK_DATA_ON_ERROR ? mockCategories : [];
        setCachedData(cacheKey, data);
        return data;
      }

      const response = await axios.get(`${API_BASE_URL}/messaging/categories/active/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // Mettre en cache les données
      setCachedData(cacheKey, response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Return mock categories instead of throwing
      const data = USE_MOCK_DATA_ON_ERROR ? mockCategories : [];
      setCachedData(cacheKey, data);
      return data;
    }
  },

  createCategory: async (data: { name: string, description?: string, color?: string, icon?: string }): Promise<Category> => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/messaging/categories/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  },

  updateCategory: async (id: number, data: { name?: string, description?: string, color?: string, icon?: string, is_active?: boolean }): Promise<Category> => {
    try {
      const response = await axios.patch(
        `${API_BASE_URL}/messaging/categories/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating category ${id}:`, error);
      throw error;
    }
  },

  // Tags
  getTags: async (): Promise<Tag[]> => {
    const cacheKey = 'tags';

    // Vérifier si les données sont en cache
    const cachedData = getCachedData(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Si les API problématiques sont désactivées, retourner directement les données fictives
    if (DISABLE_PROBLEMATIC_APIS) {
      setCachedData(cacheKey, mockTags);
      return mockTags;
    }

    try {
      const token = getAuthToken();
      if (!token) {
        console.error('No auth token available');
        const data = USE_MOCK_DATA_ON_ERROR ? mockTags : [];
        setCachedData(cacheKey, data);
        return data;
      }

      const response = await axios.get(`${API_BASE_URL}/messaging/tags/active/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // Mettre en cache les données
      setCachedData(cacheKey, response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching tags:', error);
      // Return mock tags instead of throwing
      const data = USE_MOCK_DATA_ON_ERROR ? mockTags : [];
      setCachedData(cacheKey, data);
      return data;
    }
  },

  createTag: async (data: { name: string, color?: string }): Promise<Tag> => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/messaging/tags/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error creating tag:', error);
      throw error;
    }
  },

  updateTag: async (id: number, data: { name?: string, color?: string, is_active?: boolean }): Promise<Tag> => {
    try {
      const response = await axios.patch(
        `${API_BASE_URL}/messaging/tags/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating tag ${id}:`, error);
      throw error;
    }
  },

  // Response Templates
  getResponseTemplates: async (): Promise<ResponseTemplate[]> => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.error('No auth token available');
        return [];
      }

      const response = await axios.get(`${API_BASE_URL}/messaging/templates/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching response templates:', error);
      return [];
    }
  },

  getResponseTemplatesByCategory: async (): Promise<TemplateGroup[]> => {
    // Si les API problématiques sont désactivées, retourner directement les données fictives
    if (DISABLE_PROBLEMATIC_APIS) {
      return mockResponseTemplates;
    }

    try {
      const token = getAuthToken();
      if (!token) {
        console.error('No auth token available');
        return USE_MOCK_DATA_ON_ERROR ? mockResponseTemplates : [];
      }

      const response = await axios.get(`${API_BASE_URL}/messaging/templates/by_category/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching response templates by category:', error);
      // Return mock data instead of empty array
      return USE_MOCK_DATA_ON_ERROR ? mockResponseTemplates : [];
    }
  },

  createResponseTemplate: async (data: { name: string, content: string, category_id?: number }): Promise<ResponseTemplate> => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/messaging/templates/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error creating response template:', error);
      throw error;
    }
  },

  updateResponseTemplate: async (id: number, data: { name?: string, content?: string, category_id?: number | null, is_active?: boolean }): Promise<ResponseTemplate> => {
    try {
      const response = await axios.patch(
        `${API_BASE_URL}/messaging/templates/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating response template ${id}:`, error);
      throw error;
    }
  },

  // WebSocket connection for real-time updates
  subscribeToConversationUpdates: (onMessage: (data: any) => void) => {
    const token = getAuthToken();
    if (!token) return null;

    const socket = new WebSocket(`${API_BASE_URL.replace('http', 'ws')}/ws/messaging/?token=${token}`);

    socket.onopen = () => {
      console.log('WebSocket connection established');
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    socket.onclose = () => {
      console.log('WebSocket connection closed');
    };

    return {
      close: () => {
        socket.close();
      }
    };
  }
};

export default messagingApi;
