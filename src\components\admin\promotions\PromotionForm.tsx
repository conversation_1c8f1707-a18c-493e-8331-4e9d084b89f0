import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { CalendarIcon } from 'lucide-react';
import { Promotion } from '@/services/promotionApi';
import { productApi } from '@/services/productApi';
import { categoryApi } from '@/services/categoryApi';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Skeleton } from '@/components/ui/skeleton';
import PromotionPreview from './PromotionPreview';

// Schéma de validation Zod
// Créer un schéma dynamique qui sera utilisé avec le formulaire
const createPromotionSchema = (currentDiscountType: string) => z.object({
  name: z.string().min(3, { message: 'Le nom doit contenir au moins 3 caractères' }),
  description: z.string().optional(),
  discount_type: z.enum(['percentage', 'fixed_amount']),
  discount_value: z.coerce
    .number()
    .positive({ message: 'La valeur doit être positive' })
    .superRefine((val, ctx) => {
      // Vérifier si le type de remise est un pourcentage
      const isPercentage = currentDiscountType === 'percentage';
      
      if (isPercentage && val > 90) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'La remise en pourcentage ne peut pas dépasser 90%',
          path: []
        });
      }
    }),
  discount_code: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[A-Z0-9]+$/.test(val),
      { message: 'Le code promo doit contenir uniquement des lettres majuscules et des chiffres, sans espaces' }
    ),
  applies_to: z.enum(['product', 'category', 'cart']),
  target_id: z.coerce.number().optional(),
  min_purchase_amount: z.coerce.number().min(0).optional(),
  start_date: z.date(),
  end_date: z.date().optional(),
  is_active: z.boolean().default(true),
  is_cumulative: z.boolean().default(false),
  usage_limit_per_customer: z.coerce.number().min(0).optional(),
  total_usage_limit: z.coerce.number().min(0).optional(),
});

// Définir un type pour les valeurs du formulaire basé sur le schéma avec un type de remise par défaut
type PromotionFormValues = z.infer<ReturnType<typeof createPromotionSchema>>;

interface PromotionFormProps {
  promotion?: Promotion;
  onSubmit: (data: PromotionFormValues) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

const PromotionForm: React.FC<PromotionFormProps> = ({
  promotion,
  onSubmit,
  onCancel,
  isSubmitting,
}) => {
  const [products, setProducts] = useState<{ id: number; name: string }[]>([]);
  const [categories, setCategories] = useState<{ id: number; name: string }[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  // État local pour suivre le type de remise actuel
  const [currentDiscountType, setCurrentDiscountType] = useState<string>(
    promotion?.discount_type || 'percentage'
  );

  // Créer le schéma avec le type de remise actuel
  const promotionSchema = createPromotionSchema(currentDiscountType);
  
  // Initialiser le formulaire avec les valeurs par défaut ou les valeurs de la promotion existante
  const form = useForm<PromotionFormValues>({
    resolver: zodResolver(promotionSchema),
    defaultValues: promotion
      ? {
          ...promotion,
          start_date: new Date(promotion.start_date),
          end_date: promotion.end_date ? new Date(promotion.end_date) : undefined,
        }
      : {
          name: '',
          description: '',
          discount_type: 'percentage',
          discount_value: 0,
          discount_code: '',
          applies_to: 'cart',
          min_purchase_amount: 0,
          start_date: new Date(),
          is_active: true,
          is_cumulative: false,
          usage_limit_per_customer: 0,
          total_usage_limit: 0,
        },
  });

  const appliesToValue = form.watch('applies_to');
  const discountType = form.watch('discount_type');
  const discountValue = form.watch('discount_value');
  const minPurchaseAmount = form.watch('min_purchase_amount');

  // Charger les produits et catégories
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const response = await productApi.getProducts({ page_size: 100 });
        setProducts(response.results.map(p => ({ id: p.id, name: p.name })));
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const response = await categoryApi.getCategories();
        setCategories(response.map(c => ({ id: c.id, name: c.name })));
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchProducts();
    fetchCategories();
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Nom de la promotion */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom de la promotion</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: Soldes d'été" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Code de promotion */}
          <FormField
            control={form.control}
            name="discount_code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Code de promotion (optionnel)</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: ETE2025" {...field} value={field.value || ''} />
                </FormControl>
                <FormDescription>
                  Code que les clients pourront utiliser lors du paiement
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Décrivez cette promotion..."
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Type de remise */}
          <FormField
            control={form.control}
            name="discount_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type de remise</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    // Mettre à jour l'état local pour le schéma dynamique
                    setCurrentDiscountType(value);
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionnez un type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="percentage">Pourcentage (%)</SelectItem>
                    <SelectItem value="fixed_amount">Montant fixe</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Valeur de la remise */}
          <FormField
            control={form.control}
            name="discount_value"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Valeur de la remise</FormLabel>
                <FormControl>
                  <div className="flex items-center">
                    <Input
                      type="number"
                      min="0"
                      step={form.getValues('discount_type') === 'percentage' ? '1' : '0.01'}
                      {...field}
                    />
                    <span className="ml-2">
                      {form.getValues('discount_type') === 'percentage' ? '%' : 'BIF'}
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* S'applique à */}
          <FormField
            control={form.control}
            name="applies_to"
            render={({ field }) => (
              <FormItem>
                <FormLabel>S'applique à</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.setValue('target_id', undefined);
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionnez une cible" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="cart">Panier entier</SelectItem>
                    <SelectItem value="product">Produit spécifique</SelectItem>
                    <SelectItem value="category">Catégorie</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Cible (produit ou catégorie) */}
          {appliesToValue !== 'cart' && (
            <FormField
              control={form.control}
              name="target_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {appliesToValue === 'product' ? 'Produit' : 'Catégorie'}
                  </FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={`Sélectionnez un${appliesToValue === 'product' ? ' produit' : 'e catégorie'}`} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {appliesToValue === 'product' ? (
                        isLoadingProducts ? (
                          <div className="p-2">
                            <Skeleton className="h-5 w-full mb-2" />
                            <Skeleton className="h-5 w-full mb-2" />
                            <Skeleton className="h-5 w-full" />
                          </div>
                        ) : (
                          products.map((product) => (
                            <SelectItem key={product.id} value={product.id.toString()}>
                              {product.name}
                            </SelectItem>
                          ))
                        )
                      ) : (
                        isLoadingCategories ? (
                          <div className="p-2">
                            <Skeleton className="h-5 w-full mb-2" />
                            <Skeleton className="h-5 w-full mb-2" />
                            <Skeleton className="h-5 w-full" />
                          </div>
                        ) : (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id.toString()}>
                              {category.name}
                            </SelectItem>
                          ))
                        )
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        {/* Montant d'achat minimum */}
        <FormField
          control={form.control}
          name="min_purchase_amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Montant d'achat minimum (optionnel)</FormLabel>
              <FormControl>
                <div className="flex items-center">
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    {...field}
                    value={field.value || ''}
                  />
                  <span className="ml-2">BIF</span>
                </div>
              </FormControl>
              <FormDescription>
                Montant minimum du panier pour que la promotion s'applique
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        {/* Prévisualisation de l'impact de la promotion */}
        <PromotionPreview 
          discountType={discountType} 
          discountValue={discountValue || 0} 
          minPurchaseAmount={minPurchaseAmount || 0} 
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Date de début */}
          <FormField
            control={form.control}
            name="start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Date de début</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className="w-full pl-3 text-left font-normal"
                      >
                        {field.value ? (
                          format(field.value, 'PPP', { locale: fr })
                        ) : (
                          <span>Sélectionnez une date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Date de fin */}
          <FormField
            control={form.control}
            name="end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Date de fin (optionnelle)</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className="w-full pl-3 text-left font-normal"
                      >
                        {field.value ? (
                          format(field.value, 'PPP', { locale: fr })
                        ) : (
                          <span>Sélectionnez une date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value || undefined}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date < new Date(new Date().setHours(0, 0, 0, 0)) ||
                        (form.getValues('start_date') && date < form.getValues('start_date'))
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Si non spécifiée, la promotion n'expirera pas
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Options avancées */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
          <h3 className="text-lg font-medium mb-4">Options avancées</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            {/* Limite d'utilisation par client */}
            <FormField
              control={form.control}
              name="usage_limit_per_customer"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Limite par client</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      placeholder="Illimité"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>
                    Nombre maximum d'utilisations par client (0 = illimité)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Limite d'utilisation totale */}
            <FormField
              control={form.control}
              name="total_usage_limit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Limite totale</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      placeholder="Illimité"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>
                    Nombre maximum d'utilisations au total (0 = illimité)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Cumulable avec d'autres promotions */}
          <FormField
            control={form.control}
            name="is_cumulative"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Cumulable</FormLabel>
                  <FormDescription>
                    Cette promotion peut-elle être cumulée avec d'autres ?
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* Actif */}
        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Promotion active</FormLabel>
                <FormDescription>
                  Activez ou désactivez cette promotion
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Boutons d'action */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Annuler
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Enregistrement...' : promotion ? 'Mettre à jour' : 'Créer'}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default PromotionForm;
