import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  Lock, 
  User, 
  Settings, 
  Eye, 
  CreditCard,
  Info,
  ExternalLink
} from 'lucide-react';
import { Link } from 'react-router-dom';

const StaffRestrictionsNotice: React.FC = () => {
  const { user } = useAuth();

  // Ne montrer que pour les utilisateurs staff
  if (user?.role !== 'staff') {
    return null;
  }

  const restrictedFeatures = [
    {
      icon: <User className="h-4 w-4" />,
      title: "Gestion des Utilisateurs",
      path: "/admin/users",
      description: "Création et modification des comptes utilisateurs"
    },
    {
      icon: <Settings className="h-4 w-4" />,
      title: "Paramètres des Messages",
      path: "/admin/message-settings",
      description: "Configuration des systèmes de messagerie"
    },
    {
      icon: <Eye className="h-4 w-4" />,
      title: "Logs Système",
      path: "/admin/logs",
      description: "Consultation des journaux d'activité"
    },
    {
      icon: <CreditCard className="h-4 w-4" />,
      title: "Méthodes de Paiement",
      path: "/admin/payment-methods",
      description: "Configuration des passerelles de paiement"
    }
  ];

  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg text-amber-800 flex items-center gap-2">
          <Info className="h-5 w-5" />
          Informations Staff
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-start gap-3 p-3 bg-white border border-amber-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm text-amber-800 font-medium mb-1">
                Accès Restreint
              </p>
              <p className="text-xs text-amber-700">
                En tant qu'utilisateur <strong>Staff</strong>, certaines fonctionnalités administratives 
                vous sont restreintes pour des raisons de sécurité.
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium text-amber-800 flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Fonctionnalités Restreintes
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {restrictedFeatures.map((feature, index) => (
                <Link
                  key={index}
                  to={feature.path}
                  className="group flex items-center gap-2 p-2 bg-white border border-amber-200 rounded-md hover:border-amber-300 hover:bg-amber-50 transition-colors"
                >
                  <div className="text-amber-600 group-hover:text-amber-700">
                    {feature.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs font-medium text-amber-800 group-hover:text-amber-900 truncate">
                      {feature.title}
                    </div>
                    <div className="text-xs text-amber-600 group-hover:text-amber-700 truncate">
                      {feature.description}
                    </div>
                  </div>
                  <ExternalLink className="h-3 w-3 text-amber-500 group-hover:text-amber-600 flex-shrink-0" />
                </Link>
              ))}
            </div>
          </div>

          <div className="pt-2 border-t border-amber-200">
            <p className="text-xs text-amber-700">
              💡 <strong>Astuce:</strong> Cliquez sur une fonctionnalité pour voir la page d'avertissement détaillée.
              Pour obtenir des accès supplémentaires, contactez votre administrateur.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StaffRestrictionsNotice;
