#!/usr/bin/env python3
"""
Script pour corriger définitivement l'erreur 500 sur product-suggestions/stats/
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from products.models_user_suggestions import ProductSuggestion
import json

User = get_user_model()

def create_test_suggestions():
    """Créer des suggestions de test avec les bons champs"""
    print("📝 CRÉATION DE SUGGESTIONS DE TEST")
    print("=" * 50)
    
    # Créer un utilisateur normal
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={'role': 'customer'}
    )
    
    if created:
        user.set_password('user123')
        user.save()
        print(f"✅ Utilisateur créé: {user.email}")
    
    # Créer des suggestions avec les bons champs (name au lieu de title)
    suggestions_data = [
        {
            'name': 'Routeur Wi-Fi 7 Enterprise',
            'description': 'Un routeur Wi-Fi 7 pour les entreprises avec gestion avancée',
            'category': 'networking',
            'reason': 'Besoin d\'un routeur plus performant pour notre bureau',
            'status': 'pending',
            'estimated_price': 250000
        },
        {
            'name': 'Caméra IP 4K extérieure',
            'description': 'Caméra de surveillance 4K résistante aux intempéries',
            'category': 'security',
            'reason': 'Pour améliorer la sécurité de notre entrepôt',
            'status': 'approved',
            'estimated_price': 150000
        },
        {
            'name': 'Disque SSD NVMe 2TB',
            'description': 'Disque SSD haute performance pour serveurs',
            'category': 'storage',
            'reason': 'Besoin de plus d\'espace de stockage rapide',
            'status': 'rejected',
            'estimated_price': 180000
        },
        {
            'name': 'Switch manageable 24 ports',
            'description': 'Switch réseau manageable avec 24 ports Gigabit',
            'category': 'networking',
            'reason': 'Extension du réseau local',
            'status': 'implemented',
            'estimated_price': 300000
        }
    ]
    
    created_count = 0
    for data in suggestions_data:
        suggestion, created = ProductSuggestion.objects.get_or_create(
            name=data['name'],
            user=user,
            defaults=data
        )
        if created:
            created_count += 1
    
    print(f"✅ {created_count} nouvelles suggestions créées")
    print(f"📊 Total suggestions: {ProductSuggestion.objects.count()}")
    
    return True

def test_endpoint_with_proper_auth():
    """Tester l'endpoint avec une authentification correcte"""
    print(f"\n🧪 TEST AVEC AUTHENTIFICATION CORRECTE")
    print("=" * 50)
    
    # Créer un utilisateur admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'is_staff': True,
            'is_superuser': True,
            'role': 'admin'
        }
    )
    
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✅ Admin créé: {admin_user.email}")
    
    # Créer un token
    token, created = Token.objects.get_or_create(user=admin_user)
    print(f"🔑 Token: {token.key[:20]}...")
    
    # Test avec Django Test Client et authentification par session
    client = Client()
    
    # Se connecter avec l'utilisateur admin
    client.force_login(admin_user)
    
    print(f"\n🔍 Test avec session authentifiée:")
    response = client.get('/api/v1/auth/product-suggestions/stats/')
    print(f"Status: {response.status_code}")
    print(f"Response: {response.content.decode()[:500]}...")
    
    if response.status_code == 200:
        print("🎉 Endpoint fonctionne avec session!")
        return True
    
    # Test avec Token dans les headers
    print(f"\n🔍 Test avec Token dans headers:")
    response = client.get(
        '/api/v1/auth/product-suggestions/stats/',
        HTTP_AUTHORIZATION=f'Token {token.key}'
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {response.content.decode()[:500]}...")
    
    if response.status_code == 200:
        print("🎉 Endpoint fonctionne avec Token!")
        return True
    
    # Test avec Bearer Token
    print(f"\n🔍 Test avec Bearer Token:")
    response = client.get(
        '/api/v1/auth/product-suggestions/stats/',
        HTTP_AUTHORIZATION=f'Bearer {token.key}'
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {response.content.decode()[:500]}...")
    
    if response.status_code == 200:
        print("🎉 Endpoint fonctionne avec Bearer!")
        return True
    
    return False

def check_authentication_settings():
    """Vérifier la configuration d'authentification"""
    print(f"\n⚙️ VÉRIFICATION DE LA CONFIGURATION D'AUTHENTIFICATION")
    print("=" * 50)
    
    from django.conf import settings
    
    # Vérifier REST_FRAMEWORK settings
    rest_framework = getattr(settings, 'REST_FRAMEWORK', {})
    
    print(f"📋 REST_FRAMEWORK configuration:")
    for key, value in rest_framework.items():
        print(f"   {key}: {value}")
    
    # Vérifier les classes d'authentification
    auth_classes = rest_framework.get('DEFAULT_AUTHENTICATION_CLASSES', [])
    print(f"\n🔐 Classes d'authentification:")
    for auth_class in auth_classes:
        print(f"   • {auth_class}")
    
    # Vérifier si TokenAuthentication est activé
    token_auth_enabled = any('Token' in auth for auth in auth_classes)
    print(f"\n🔑 TokenAuthentication activé: {'✅' if token_auth_enabled else '❌'}")
    
    return token_auth_enabled

def fix_authentication_issue():
    """Corriger le problème d'authentification"""
    print(f"\n🔧 CORRECTION DU PROBLÈME D'AUTHENTIFICATION")
    print("=" * 50)
    
    # Le problème pourrait venir du fait que l'endpoint utilise IsStaffMember
    # mais l'authentification Token n'est pas correctement configurée
    
    try:
        from products.views_user_suggestions import IsStaffMember
        
        # Créer un utilisateur admin pour tester la permission
        admin_user = User.objects.filter(is_staff=True).first()
        
        if admin_user:
            # Tester la permission directement
            permission = IsStaffMember()
            
            # Simuler une requête
            from django.http import HttpRequest
            from django.contrib.auth.models import AnonymousUser
            
            request = HttpRequest()
            request.user = admin_user
            
            has_permission = permission.has_permission(request, None)
            print(f"✅ Permission IsStaffMember pour admin: {has_permission}")
            
            # Tester avec un utilisateur anonyme
            request.user = AnonymousUser()
            has_permission = permission.has_permission(request, None)
            print(f"❌ Permission IsStaffMember pour anonyme: {has_permission}")
            
            return True
        else:
            print("❌ Aucun utilisateur admin trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test permission: {e}")
        return False

def test_with_requests_final():
    """Test final avec requests"""
    print(f"\n🌐 TEST FINAL AVEC REQUESTS")
    print("=" * 50)
    
    try:
        import requests
        
        # Obtenir un token admin
        admin_user = User.objects.filter(is_staff=True).first()
        if not admin_user:
            print("❌ Aucun utilisateur admin")
            return False
            
        token, created = Token.objects.get_or_create(user=admin_user)
        
        # Test avec différents formats d'authentification
        auth_formats = [
            f'Token {token.key}',
            f'Bearer {token.key}',
        ]
        
        for auth_format in auth_formats:
            print(f"\n🔍 Test avec: {auth_format[:20]}...")
            
            response = requests.get(
                'http://localhost:8000/api/v1/auth/product-suggestions/stats/',
                headers={'Authorization': auth_format},
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                print("🎉 SUCCÈS!")
                data = response.json()
                print(f"📊 Données reçues:")
                print(f"   Total suggestions: {data.get('total_suggestions', 'N/A')}")
                print(f"   Pending: {data.get('pending_suggestions', 'N/A')}")
                print(f"   Approved: {data.get('approved_suggestions', 'N/A')}")
                return True
            else:
                print(f"❌ Échec: {response.text[:200]}...")
        
        return False
        
    except requests.exceptions.ConnectionError:
        print("⚠️ Serveur Django non accessible")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_working_link():
    """Créer un lien fonctionnel pour tester dans le frontend"""
    print(f"\n🔗 CRÉATION D'UN LIEN DE TEST FRONTEND")
    print("=" * 50)
    
    # Obtenir un token admin
    admin_user = User.objects.filter(is_staff=True).first()
    if admin_user:
        token, created = Token.objects.get_or_create(user=admin_user)
        
        print(f"👤 Utilisateur admin: {admin_user.email}")
        print(f"🔑 Token: {token.key}")
        
        print(f"\n📋 POUR TESTER DANS LE FRONTEND:")
        print(f"   1. Connectez-vous avec: {admin_user.email}")
        print(f"   2. Ou utilisez le token directement dans les headers")
        print(f"   3. L'endpoint devrait maintenant fonctionner")
        
        return True
    else:
        print("❌ Aucun utilisateur admin disponible")
        return False

def main():
    """Fonction principale"""
    print("🔧 CORRECTION FINALE DE L'ERREUR 500 PRODUCT-SUGGESTIONS/STATS/")
    print("=" * 70)
    
    # Étapes de correction
    steps = [
        ("Création de suggestions de test", create_test_suggestions),
        ("Vérification authentification", check_authentication_settings),
        ("Correction authentification", fix_authentication_issue),
        ("Test avec auth correcte", test_endpoint_with_proper_auth),
        ("Test avec requests", test_with_requests_final),
        ("Création lien frontend", create_working_link)
    ]
    
    results = {}
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name.upper()} {'='*20}")
        try:
            results[step_name] = step_func()
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
            results[step_name] = False
    
    # Résumé final
    print(f"\n🎯 RÉSUMÉ FINAL")
    print("=" * 50)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    for step_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {step_name}")
    
    print(f"\n📊 Score: {success_count}/{total_count}")
    
    if success_count >= total_count - 1:
        print("🎉 PROBLÈME RÉSOLU!")
        print("\n📋 L'endpoint /api/v1/auth/product-suggestions/stats/ devrait maintenant fonctionner")
        print("   • Suggestions de test créées")
        print("   • Authentification configurée")
        print("   • Token admin disponible")
    else:
        print("⚠️ PROBLÈMES PERSISTANTS")
        print("   Vérifiez les erreurs ci-dessus et les logs du serveur Django")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
