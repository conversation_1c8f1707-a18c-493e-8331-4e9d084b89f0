// API configuration
export const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';

// Authentication configuration
export const AUTH_TOKEN_KEY = 'auth_token';
export const AUTH_REFRESH_TOKEN_KEY = 'auth_refresh_token';
export const AUTH_USER_KEY = 'auth_user';

// Pagination configuration
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE = 1;

// Date format configuration
export const DATE_FORMAT = 'dd/MM/yyyy';
export const DATE_TIME_FORMAT = 'dd/MM/yyyy HH:mm';

// Currency configuration
export const CURRENCY = 'EUR';
export const CURRENCY_SYMBOL = '€';

// File upload configuration
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];

// Other configuration
export const APP_NAME = 'JosNet E-Commerce';
export const APP_VERSION = '1.0.0';
export const SUPPORT_EMAIL = '<EMAIL>';
export const SUPPORT_PHONE = '+33 1 23 45 67 89';
