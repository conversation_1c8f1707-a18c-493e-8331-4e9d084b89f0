import stripe
import logging
from django.conf import settings
from django.utils import timezone
from ..models import Transaction

# Set up logger
logger = logging.getLogger(__name__)

# Initialize Stripe with API key
# stripe.api_key = settings.STRIPE_SECRET_KEY

# For development, we'll use a placeholder API key
stripe.api_key = "sk_test_placeholder"

def create_payment_intent(order, payment_method):
    """
    Create a payment intent with Stripe.

    Args:
        order: The order to create a payment intent for
        payment_method: The payment method to use

    Returns:
        dict: The payment intent details
    """
    try:
        # Create a payment intent
        intent = stripe.PaymentIntent.create(
            amount=int(order.total * 100),  # Amount in cents
            currency='eur',
            description=f"Order #{order.id}",
            metadata={
                'order_id': order.id,
                'customer_email': order.email
            }
        )

        # Create a transaction record
        transaction = Transaction.objects.create(
            order=order,
            payment_method=payment_method,
            transaction_id=intent.id,
            amount=order.total,
            currency='EUR',
            status='pending',
            provider_response=intent
        )

        return {
            'success': True,
            'client_secret': intent.client_secret,
            'transaction_id': transaction.id
        }

    except stripe.error.CardError as e:
        # Card errors (e.g. declined card)
        error_detail = e.json_body.get('error', {})
        logger.error(f"Stripe CardError: {error_detail}")
        return {
            'success': False,
            'error': error_detail.get('message', str(e)),
            'error_code': error_detail.get('code'),
            'error_type': 'card_error'
        }

    except stripe.error.RateLimitError as e:
        # Too many requests made to the API too quickly
        logger.error(f"Stripe RateLimitError: {str(e)}")
        return {
            'success': False,
            'error': 'Rate limit exceeded. Please try again later.',
            'error_type': 'rate_limit_error'
        }

    except stripe.error.InvalidRequestError as e:
        # Invalid parameters were supplied to Stripe's API
        logger.error(f"Stripe InvalidRequestError: {str(e)}")
        return {
            'success': False,
            'error': 'Invalid request parameters.',
            'error_type': 'invalid_request_error'
        }

    except stripe.error.AuthenticationError as e:
        # Authentication with Stripe's API failed
        logger.error(f"Stripe AuthenticationError: {str(e)}")
        return {
            'success': False,
            'error': 'Authentication with payment provider failed.',
            'error_type': 'authentication_error'
        }

    except stripe.error.APIConnectionError as e:
        # Network communication with Stripe failed
        logger.error(f"Stripe APIConnectionError: {str(e)}")
        return {
            'success': False,
            'error': 'Network communication with payment provider failed. Please try again.',
            'error_type': 'api_connection_error'
        }

    except stripe.error.StripeError as e:
        # Generic Stripe error
        logger.error(f"Stripe Error: {str(e)}")
        return {
            'success': False,
            'error': 'An error occurred while processing your payment. Please try again.',
            'error_type': 'stripe_error'
        }

    except Exception as e:
        # Handle other errors
        logger.exception(f"Unexpected error in create_payment_intent: {str(e)}")
        return {
            'success': False,
            'error': 'An unexpected error occurred. Please try again later.',
            'error_type': 'server_error'
        }

def process_payment(transaction_id, payment_intent_id):
    """
    Process a payment with Stripe.

    Args:
        transaction_id: The ID of the transaction to process
        payment_intent_id: The ID of the payment intent

    Returns:
        dict: The payment result
    """
    try:
        # Get the transaction
        transaction = Transaction.objects.get(id=transaction_id)

        # Get the payment intent
        intent = stripe.PaymentIntent.retrieve(payment_intent_id)

        # Update the transaction
        transaction.transaction_id = intent.id
        transaction.provider_response = intent

        # Check the payment status
        if intent.status == 'succeeded':
            transaction.status = 'completed'
            transaction.completed_at = timezone.now()

            # Update the order payment status
            order = transaction.order
            order.payment_status = 'paid'
            order.save()

        elif intent.status == 'requires_payment_method':
            transaction.status = 'failed'
            transaction.error_message = 'Payment failed, please try again.'

        else:
            transaction.status = 'processing'

        transaction.save()

        return {
            'success': True,
            'status': transaction.status,
            'transaction_id': transaction.id
        }

    except stripe.error.CardError as e:
        # Card errors (e.g. declined card)
        error_detail = e.json_body.get('error', {})
        logger.error(f"Stripe CardError: {error_detail}")
        return {
            'success': False,
            'error': error_detail.get('message', str(e)),
            'error_code': error_detail.get('code'),
            'error_type': 'card_error'
        }

    except stripe.error.RateLimitError as e:
        # Too many requests made to the API too quickly
        logger.error(f"Stripe RateLimitError: {str(e)}")
        return {
            'success': False,
            'error': 'Rate limit exceeded. Please try again later.',
            'error_type': 'rate_limit_error'
        }

    except stripe.error.InvalidRequestError as e:
        # Invalid parameters were supplied to Stripe's API
        logger.error(f"Stripe InvalidRequestError: {str(e)}")
        return {
            'success': False,
            'error': 'Invalid request parameters.',
            'error_type': 'invalid_request_error'
        }

    except stripe.error.AuthenticationError as e:
        # Authentication with Stripe's API failed
        logger.error(f"Stripe AuthenticationError: {str(e)}")
        return {
            'success': False,
            'error': 'Authentication with payment provider failed.',
            'error_type': 'authentication_error'
        }

    except stripe.error.APIConnectionError as e:
        # Network communication with Stripe failed
        logger.error(f"Stripe APIConnectionError: {str(e)}")
        return {
            'success': False,
            'error': 'Network communication with payment provider failed. Please try again.',
            'error_type': 'api_connection_error'
        }

    except stripe.error.StripeError as e:
        # Generic Stripe error
        logger.error(f"Stripe Error: {str(e)}")
        return {
            'success': False,
            'error': 'An error occurred while processing your payment. Please try again.',
            'error_type': 'stripe_error'
        }

    except Exception as e:
        # Handle other errors
        logger.exception(f"Unexpected error in process_payment: {str(e)}")
        return {
            'success': False,
            'error': 'An unexpected error occurred. Please try again later.',
            'error_type': 'server_error'
        }

def process_refund(transaction, amount, reason):
    """
    Process a refund with Stripe.

    Args:
        transaction: The transaction to refund
        amount: The amount to refund
        reason: The reason for the refund

    Returns:
        dict: The refund result
    """
    try:
        # Get the payment intent
        payment_intent_id = transaction.transaction_id

        # Create a refund
        refund = stripe.Refund.create(
            payment_intent=payment_intent_id,
            amount=int(amount * 100),  # Amount in cents
            reason='requested_by_customer',
            metadata={
                'order_id': transaction.order.id,
                'reason': reason
            }
        )

        return {
            'success': True,
            'transaction_id': refund.id,
            'message': 'Refund processed successfully'
        }

    except stripe.error.CardError as e:
        # Card errors (e.g. declined card)
        error_detail = e.json_body.get('error', {})
        logger.error(f"Stripe CardError in refund: {error_detail}")
        return {
            'success': False,
            'message': error_detail.get('message', str(e)),
            'error_code': error_detail.get('code'),
            'error_type': 'card_error'
        }

    except stripe.error.RateLimitError as e:
        # Too many requests made to the API too quickly
        logger.error(f"Stripe RateLimitError in refund: {str(e)}")
        return {
            'success': False,
            'message': 'Rate limit exceeded. Please try again later.',
            'error_type': 'rate_limit_error'
        }

    except stripe.error.InvalidRequestError as e:
        # Invalid parameters were supplied to Stripe's API
        logger.error(f"Stripe InvalidRequestError in refund: {str(e)}")
        return {
            'success': False,
            'message': 'Invalid request parameters. The payment may not be refundable.',
            'error_type': 'invalid_request_error'
        }

    except stripe.error.AuthenticationError as e:
        # Authentication with Stripe's API failed
        logger.error(f"Stripe AuthenticationError in refund: {str(e)}")
        return {
            'success': False,
            'message': 'Authentication with payment provider failed.',
            'error_type': 'authentication_error'
        }

    except stripe.error.APIConnectionError as e:
        # Network communication with Stripe failed
        logger.error(f"Stripe APIConnectionError in refund: {str(e)}")
        return {
            'success': False,
            'message': 'Network communication with payment provider failed. Please try again.',
            'error_type': 'api_connection_error'
        }

    except stripe.error.StripeError as e:
        # Generic Stripe error
        logger.error(f"Stripe Error in refund: {str(e)}")
        return {
            'success': False,
            'message': 'An error occurred while processing the refund. Please try again.',
            'error_type': 'stripe_error'
        }

    except Exception as e:
        # Handle other errors
        logger.exception(f"Unexpected error in process_refund: {str(e)}")
        return {
            'success': False,
            'message': 'An unexpected error occurred. Please try again later.',
            'error_type': 'server_error'
        }
