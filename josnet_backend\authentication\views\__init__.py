# Import all views to make them available from the views package
from authentication.views.admin_views import AdminUserViewSet
from authentication.views.dashboard import DashboardStatsView

# Import views from the views_original.py file
# This is needed for backward compatibility
from authentication.views.views_original import (
    RegisterView,
    LoginView,
    ProfilePictureUploadView,
    LogoutView,
    UserProfileView,
    PasswordChangeView,
    PasswordResetRequestView,
    PasswordResetConfirmView,
    EmailVerificationView,
    ResendVerificationView,
    UserAddressViewSet,
    LoyaltyTransactionViewSet,
    UserLoyaltyView
)
