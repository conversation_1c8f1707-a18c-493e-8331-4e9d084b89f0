#!/usr/bin/env python3
"""
Script pour tester que le frontend peut récupérer les adresses correctement
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def test_frontend_addresses():
    """Teste que l'API retourne les données dans le format attendu par le frontend"""
    print("🧪 Test de l'API des adresses pour le frontend")
    print("=" * 50)
    
    # 1. Authentification
    print("🔐 Authentification...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access')
            print(f"✅ Authentification réussie")
        else:
            print(f"❌ Échec de l'authentification: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return False
    
    # 2. Test de récupération des adresses
    print(f"\n📋 Test de récupération des adresses...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/auth/addresses/", headers=headers)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Vérifier le format des données
            print(f"📊 Format des données reçues:")
            print(f"   Type: {type(data)}")
            
            if isinstance(data, dict) and 'results' in data:
                addresses = data['results']
                print(f"   ✅ Données paginées détectées")
                print(f"   📦 Nombre d'adresses: {len(addresses)}")
                print(f"   📈 Total: {data.get('count', 'N/A')}")
                
                # Vérifier la structure d'une adresse
                if addresses:
                    first_address = addresses[0]
                    print(f"\n📍 Structure de la première adresse:")
                    required_fields = ['id', 'address_type', 'is_default', 'first_name', 'last_name', 
                                     'address_line1', 'city', 'postal_code', 'country']
                    
                    for field in required_fields:
                        if field in first_address:
                            print(f"   ✅ {field}: {first_address[field]}")
                        else:
                            print(f"   ❌ {field}: MANQUANT")
                
                return True
                
            elif isinstance(data, list):
                print(f"   ✅ Données directes (tableau)")
                print(f"   📦 Nombre d'adresses: {len(data)}")
                return True
            else:
                print(f"   ❌ Format inattendu: {type(data)}")
                print(f"   📄 Contenu: {data}")
                return False
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_address_creation():
    """Teste la création d'une nouvelle adresse"""
    print(f"\n➕ Test de création d'adresse...")
    
    # Authentification
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    token = response.json().get('access')
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Données de test pour une nouvelle adresse
    address_data = {
        "address_type": "both",
        "is_default": False,
        "first_name": "Frontend",
        "last_name": "Test",
        "address_line1": "789 Rue du Frontend",
        "city": "Bujumbura",
        "postal_code": "3000",
        "country": "Burundi",
        "phone": "+257 99 88 77 66"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/addresses/", json=address_data, headers=headers)
        
        if response.status_code == 201:
            address = response.json()
            print(f"✅ Adresse créée avec succès!")
            print(f"   ID: {address.get('id')}")
            print(f"   Nom: {address.get('first_name')} {address.get('last_name')}")
            print(f"   Adresse: {address.get('address_line1')}")
            return address.get('id')
        else:
            print(f"❌ Erreur de création: {response.status_code}")
            print(f"Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

if __name__ == "__main__":
    print("🚀 TEST COMPLET DU FRONTEND DES ADRESSES")
    print("=" * 60)
    
    # Test 1: Récupération des adresses
    success1 = test_frontend_addresses()
    
    # Test 2: Création d'adresse
    new_address_id = test_address_creation()
    
    # Test 3: Vérification après création
    if new_address_id:
        print(f"\n🔄 Vérification après création...")
        success3 = test_frontend_addresses()
    else:
        success3 = False
    
    print(f"\n📊 RÉSUMÉ DES TESTS:")
    print(f"   Récupération initiale: {'✅' if success1 else '❌'}")
    print(f"   Création d'adresse: {'✅' if new_address_id else '❌'}")
    print(f"   Vérification finale: {'✅' if success3 else '❌'}")
    
    if success1 and new_address_id and success3:
        print(f"\n🎉 TOUS LES TESTS RÉUSSIS!")
        print(f"   Le frontend devrait maintenant pouvoir afficher les adresses correctement.")
        print(f"   Allez sur: http://localhost:8080/account/profile?tab=addresses")
    else:
        print(f"\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"   Vérifiez les erreurs ci-dessus.")
