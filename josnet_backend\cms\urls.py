from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'pages', views.PageViewSet)
router.register(r'blog/categories', views.BlogCategoryViewSet)
router.register(r'blog/posts', views.BlogPostViewSet)
router.register(r'blog/comments', views.CommentViewSet)
router.register(r'media', views.MediaViewSet)
router.register(r'menus', views.MenuViewSet)
router.register(r'menu-items', views.MenuItemViewSet)
router.register(r'settings', views.SiteSettingViewSet)

app_name = 'cms'

urlpatterns = [
    path('', include(router.urls)),
]
