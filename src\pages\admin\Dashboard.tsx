
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  BarChart,
  Package,
  ShoppingCart,
  Users,
  TrendingUp,
  Clock,
  AlertTriangle,
  RefreshCw,
  MessageSquare,
  Mail,
  Phone,
  Globe,
  Activity,
  DollarSign,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Settings,
  Bell,
  Zap
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import dashboardApi from "@/services/dashboardApi";
import coreApi from "@/services/coreApi";
import StaffRestrictionsNotice from "@/components/admin/StaffRestrictionsNotice";


const Dashboard = () => {
  // Récupérer les données du tableau de bord via React Query
  const {
    data: dashboardData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['dashboard'],
    queryFn: () => dashboardApi.getDashboardData(),
  });

  // Récupérer les données du site pour le dashboard
  const { data: siteData } = useQuery({
    queryKey: ['homePageData'],
    queryFn: () => coreApi.getHomePageData(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Formater le montant des ventes en Francs Burundais
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-BI', {
      style: 'currency',
      currency: 'BIF',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Statistiques globales
  const stats = dashboardData ? [
    {
      title: "Ventes totales",
      value: formatCurrency(dashboardData.stats.total_sales),
      change: dashboardData.stats.change_sales,
      changeType: dashboardData.stats.change_type_sales,
      icon: <TrendingUp size={24} className="text-green-500" />,
    },
    {
      title: "Commandes",
      value: dashboardData.stats.total_orders.toString(),
      change: dashboardData.stats.change_orders,
      changeType: dashboardData.stats.change_type_orders,
      icon: <ShoppingCart size={24} className="text-blue-500" />,
    },
    {
      title: "Clients",
      value: dashboardData.stats.total_customers.toString(),
      change: dashboardData.stats.change_customers,
      changeType: dashboardData.stats.change_type_customers,
      icon: <Users size={24} className="text-violet-500" />,
    },
    {
      title: "Produits",
      value: dashboardData.stats.total_products.toString(),
      change: dashboardData.stats.change_products,
      changeType: dashboardData.stats.change_type_products,
      icon: <Package size={24} className="text-orange-500" />,
    },
  ] : [];

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header amélioré */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 md:p-8 text-white relative overflow-hidden"
        >
          {/* Éléments décoratifs */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>

          <div className="relative z-10">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold mb-2">
                  Dashboard JOSNET 🚀
                </h1>
                <p className="text-blue-100 text-lg">
                  Gérez votre entreprise technologique avec efficacité
                </p>
                <div className="flex items-center mt-4 space-x-4">
                  <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1">
                    <Activity className="h-4 w-4" />
                    <span className="text-sm">Système actif</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm">{new Date().toLocaleDateString('fr-FR')}</span>
                  </div>
                </div>
              </div>

              <div className="mt-4 md:mt-0 flex items-center space-x-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{siteData?.stats?.clients_count || '1000+'}</div>
                  <div className="text-blue-200 text-sm">Clients</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{siteData?.stats?.projects_count || '5000+'}</div>
                  <div className="text-blue-200 text-sm">Projets</div>
                </div>
                <div className="text-center">
                  <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                    {siteData?.stats?.experience_years || '10'}+ ans
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {dashboardData?.is_mock_data && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="p-4 bg-amber-50 border border-amber-200 text-amber-700 rounded-xl text-sm flex items-center space-x-2"
          >
            <AlertTriangle className="h-4 w-4 flex-shrink-0" />
            <span>Affichage de données simulées - Le backend n'est pas disponible</span>
          </motion.div>
        )}



        {isLoading ? (
          // Affichage des squelettes pendant le chargement
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="w-full">
                      <Skeleton className="h-4 w-24 mb-2" />
                      <Skeleton className="h-8 w-32 mb-2" />
                      <Skeleton className="h-4 w-40" />
                    </div>
                    <Skeleton className="h-12 w-12 rounded-full" />
                  </div>
                </Card>
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="col-span-1 lg:col-span-2 p-6">
                <Skeleton className="h-6 w-40 mb-6" />
                <Skeleton className="h-64 w-full" />
              </Card>
              <div className="col-span-1 space-y-6">
                <Card className="p-6">
                  <Skeleton className="h-6 w-40 mb-4" />
                  <Skeleton className="h-24 w-full" />
                </Card>
                <Card className="p-6">
                  <Skeleton className="h-6 w-40 mb-4" />
                  <Skeleton className="h-24 w-full" />
                </Card>
                <Card className="p-6">
                  <Skeleton className="h-6 w-40 mb-4" />
                  <Skeleton className="h-52 w-full" />
                </Card>
              </div>
            </div>
          </>
        ) : isError ? (
          // Affichage d'une erreur
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            Une erreur est survenue lors du chargement des données du tableau de bord.
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => refetch()}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Réessayer
            </Button>
          </div>
        ) : (
          // Affichage des données
          <>
            {/* Stats Grid Améliorées */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="relative overflow-hidden hover:shadow-xl transition-all duration-300 group cursor-pointer">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <CardContent className="p-6 relative z-10">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                          <h3 className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</h3>
                          <div className="flex items-center space-x-2">
                            {stat.changeType === "positive" ? (
                              <ArrowUpRight className="h-4 w-4 text-green-500" />
                            ) : (
                              <ArrowDownRight className="h-4 w-4 text-red-500" />
                            )}
                            <span className={`text-sm font-medium ${
                              stat.changeType === "positive" ? "text-green-600" : "text-red-600"
                            }`}>
                              {stat.change}
                            </span>
                            <span className="text-sm text-gray-500">ce mois</span>
                          </div>
                        </div>
                        <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          <div className="text-white">
                            {stat.icon}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Staff Restrictions Notice */}
            <StaffRestrictionsNotice />

            {/* Actions rapides */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Zap className="h-5 w-5 mr-2 text-blue-600" />
                Actions rapides
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { title: "Nouvelle commande", icon: <ShoppingCart className="h-5 w-5" />, href: "/admin/orders/create", color: "bg-blue-500" },
                  { title: "Ajouter produit", icon: <Package className="h-5 w-5" />, href: "/admin/products/create", color: "bg-green-500" },
                  { title: "Messages", icon: <MessageSquare className="h-5 w-5" />, href: "/admin/messages", color: "bg-purple-500" },
                  { title: "Paramètres", icon: <Settings className="h-5 w-5" />, href: "/admin/settings", color: "bg-orange-500" }
                ].map((action, index) => (
                  <Card key={index} className="hover:shadow-lg transition-all duration-300 group cursor-pointer">
                    <CardContent className="p-4 text-center">
                      <div className={`w-12 h-12 ${action.color} rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300`}>
                        <div className="text-white">
                          {action.icon}
                        </div>
                      </div>
                      <h3 className="font-semibold text-sm group-hover:text-blue-600 transition-colors">
                        {action.title}
                      </h3>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Recent Orders */}
              <Card className="col-span-1 lg:col-span-2">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-medium flex items-center">
                      <ShoppingCart size={18} className="mr-2 text-gray-500" />
                      Commandes récentes
                    </h2>
                    <a href="/admin/orders" className="text-sm text-primary hover:underline">
                      Voir toutes les commandes
                    </a>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 border-b">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {dashboardData.recent_orders.map((order, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm font-medium text-primary">
                              {order.id}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-700">
                              {order.customer}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {order.date}
                            </td>
                            <td className="px-4 py-3">
                              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                order.status === "Livré"
                                  ? "bg-green-100 text-green-800"
                                  : order.status === "En cours"
                                  ? "bg-blue-100 text-blue-800"
                                  : order.status === "En attente"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                              }`}>
                                {order.status}
                              </span>
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-700">
                              {order.amount}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </Card>

              {/* Quick Actions Column */}
              <div className="col-span-1 space-y-6">
                {/* Low Stock Items */}
                <Card>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-medium flex items-center">
                        <AlertTriangle size={18} className="mr-2 text-amber-500" />
                        Stock faible
                      </h2>
                    </div>
                    <ul className="space-y-3">
                      {dashboardData.low_stock_items.map((item, index) => (
                        <li key={index} className="flex items-center justify-between py-2 border-b last:border-0">
                          <div>
                            <p className="text-sm font-medium">{item.name}</p>
                            <p className="text-xs text-gray-500">ID: {item.id}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-bold text-red-600">{item.stock} restants</p>
                            <p className="text-xs text-gray-500">Seuil: {item.threshold}</p>
                          </div>
                        </li>
                      ))}
                    </ul>
                    <div className="mt-4">
                      <a href="/admin/inventory" className="text-sm text-primary hover:underline">
                        Gérer l'inventaire
                      </a>
                    </div>
                  </div>
                </Card>

                {/* Pending Tasks */}
                <Card>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-medium flex items-center">
                        <Clock size={18} className="mr-2 text-blue-500" />
                        Tâches en attente
                      </h2>
                    </div>
                    <ul className="space-y-2">
                      {dashboardData.pending_tasks.map((task, index) => (
                        <li key={index} className="flex items-center justify-between py-2 border-b last:border-0">
                          <div className="flex items-start">
                            <input
                              type="checkbox"
                              className="mt-1 mr-3"
                              id={`task-${task.id}`}
                            />
                            <label htmlFor={`task-${task.id}`} className="text-sm">{task.task}</label>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            task.priority === "Haute"
                              ? "bg-red-100 text-red-800"
                              : task.priority === "Moyenne"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-green-100 text-green-800"
                          }`}>
                            {task.priority}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </Card>



                {/* Sales Graph */}
                <Card>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-medium flex items-center">
                        <BarChart size={18} className="mr-2 text-purple-500" />
                        Ventes mensuelles
                      </h2>
                    </div>
                    <div className="h-52 flex items-end justify-between px-2">
                      {/* Simple bar chart representation */}
                      {dashboardData.monthly_sales.map((item, i) => {
                        // Calculer la hauteur relative de la barre
                        const maxValue = Math.max(...dashboardData.monthly_sales.map(s => s.value));
                        const height = (item.value / maxValue) * 100;

                        return (
                          <div key={i} className="w-8 group relative">
                            <div
                              className="bg-primary/80 hover:bg-primary rounded-t transition-all"
                              style={{height: `${height}%`}}
                            />
                            <span className="text-xs mt-1 block text-center text-gray-600">
                              {item.month}
                            </span>
                            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                              {formatCurrency(item.value)}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-4">
                      <a href="/admin/statistics" className="text-sm text-primary hover:underline">
                        Voir les statistiques détaillées
                      </a>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default Dashboard;
