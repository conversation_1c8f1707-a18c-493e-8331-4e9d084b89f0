import { useQuery } from '@tanstack/react-query';
import blogApi, { BlogPost, BlogCategory, BlogFilters, BlogListResponse } from '@/services/blogApi';

// Hook pour récupérer tous les articles avec filtres
export const useBlogPosts = (filters: BlogFilters = {}) => {
  return useQuery<BlogListResponse, Error>({
    queryKey: ['blogPosts', filters],
    queryFn: () => blogApi.getPosts(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook pour récupérer un article par son slug
export const useBlogPost = (slug: string) => {
  return useQuery<BlogPost, Error>({
    queryKey: ['blogPost', slug],
    queryFn: () => blogApi.getPostBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hook pour récupérer les articles en vedette
export const useFeaturedPosts = () => {
  return useQuery<BlogPost[], Error>({
    queryKey: ['featuredPosts'],
    queryFn: blogApi.getFeaturedPosts,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hook pour récupérer toutes les catégories
export const useBlogCategories = () => {
  return useQuery<BlogCategory[], Error>({
    queryKey: ['blogCategories'],
    queryFn: blogApi.getCategories,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 heure
  });
};

// Hook pour rechercher des articles
export const useSearchPosts = (query: string) => {
  return useQuery<BlogPost[], Error>({
    queryKey: ['searchPosts', query],
    queryFn: () => blogApi.searchPosts(query),
    enabled: !!query && query.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook personnalisé pour la page blog avec logique métier
export const useBlogPage = (initialFilters: BlogFilters = {}) => {
  const {
    data: postsData,
    isLoading: postsLoading,
    error: postsError,
    refetch: refetchPosts
  } = useBlogPosts(initialFilters);

  const {
    data: categories,
    isLoading: categoriesLoading,
    error: categoriesError
  } = useBlogCategories();

  const {
    data: featuredPosts,
    isLoading: featuredLoading,
    error: featuredError
  } = useFeaturedPosts();

  // Calculer les statistiques
  const stats = {
    totalPosts: postsData?.count || 0,
    totalCategories: categories?.length || 0,
    featuredCount: featuredPosts?.length || 0,
  };

  // État de chargement global
  const isLoading = postsLoading || categoriesLoading || featuredLoading;
  
  // Erreurs globales
  const hasError = postsError || categoriesError || featuredError;

  // Données formatées pour l'affichage
  const posts = postsData?.results || [];
  const pagination = {
    count: postsData?.count || 0,
    next: postsData?.next,
    previous: postsData?.previous,
    hasNext: !!postsData?.next,
    hasPrevious: !!postsData?.previous,
  };

  // Utilitaires
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getAuthorName = (author: BlogPost['author']) => {
    if (!author) return 'Auteur anonyme';
    if (author.first_name && author.last_name) {
      return `${author.first_name} ${author.last_name}`;
    }
    return author.first_name || 'Auteur';
  };

  const getCategoryName = (category: BlogPost['category']) => {
    return category?.name || 'Non catégorisé';
  };

  const getImageUrl = (imagePath?: string) => {
    if (!imagePath) {
      // Image par défaut pour les articles sans image
      return 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=500&auto=format&fit=crop&q=60';
    }
    
    // Si c'est déjà une URL complète, la retourner telle quelle
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    
    // Sinon, construire l'URL complète
    const baseUrl = import.meta.env.VITE_API_URL?.replace('/api/v1', '') || 'http://localhost:8000';
    return `${baseUrl}${imagePath}`;
  };

  const getReadingTime = (post: BlogPost) => {
    if (post.reading_time) return `${post.reading_time} min`;
    
    // Calcul approximatif basé sur le contenu
    const wordsPerMinute = 200;
    const wordCount = post.content.split(' ').length;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return `${readingTime} min`;
  };

  const getTags = (post: BlogPost) => {
    return post.tags?.map(tag => tag.name) || [];
  };

  return {
    // Données
    posts,
    categories: categories || [],
    featuredPosts: featuredPosts || [],
    pagination,
    stats,
    
    // États
    isLoading,
    hasError,
    errors: {
      posts: postsError,
      categories: categoriesError,
      featured: featuredError,
    },
    
    // Actions
    refetchPosts,
    
    // Utilitaires
    formatDate,
    getAuthorName,
    getCategoryName,
    getImageUrl,
    getReadingTime,
    getTags,
  };
};

// Hook pour la page de détail d'un article
export const useBlogPostPage = (slug: string) => {
  const {
    data: post,
    isLoading,
    error,
    refetch
  } = useBlogPost(slug);

  const {
    data: relatedPosts,
    isLoading: relatedLoading
  } = useBlogPosts({
    category: post?.category?.slug,
    page_size: 3
  });

  // Filtrer les articles liés pour exclure l'article actuel
  const filteredRelatedPosts = relatedPosts?.results?.filter(p => p.slug !== slug) || [];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getAuthorName = (author: BlogPost['author']) => {
    if (!author) return 'Auteur anonyme';
    if (author.first_name && author.last_name) {
      return `${author.first_name} ${author.last_name}`;
    }
    return author.first_name || 'Auteur';
  };

  const getImageUrl = (imagePath?: string) => {
    if (!imagePath) {
      return 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&auto=format&fit=crop&q=60';
    }
    
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    
    const baseUrl = import.meta.env.VITE_API_URL?.replace('/api/v1', '') || 'http://localhost:8000';
    return `${baseUrl}${imagePath}`;
  };

  const getReadingTime = () => {
    if (!post) return '0 min';
    if (post.reading_time) return `${post.reading_time} min`;
    
    const wordsPerMinute = 200;
    const wordCount = post.content.split(' ').length;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return `${readingTime} min`;
  };

  return {
    // Données
    post,
    relatedPosts: filteredRelatedPosts,
    
    // États
    isLoading: isLoading || relatedLoading,
    error,
    
    // Actions
    refetch,
    
    // Utilitaires
    formatDate,
    getAuthorName,
    getImageUrl,
    getReadingTime,
  };
};
