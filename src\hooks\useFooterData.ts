/**
 * Hook pour récupérer les données du footer depuis le CMS admin
 */

import { useQuery } from '@tanstack/react-query';
import cmsApi from '@/services/cmsApi';

export interface FooterData {
  siteSettings: any;
  footerPages: any[];
  contactInfo: {
    address: string;
    phone: string;
    email: string;
  };
  socialLinks: {
    facebook: string;
    twitter: string;
    instagram: string;
    linkedin: string;
  };
  companyInfo: {
    name: string;
    description: string;
    logo?: string;
  };
}

const useFooterData = () => {
  const {
    data: footerData,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery<FooterData>({
    queryKey: ['footerData'],
    queryFn: async (): Promise<FooterData> => {
      try {
        // Récupérer les paramètres du site et les pages en parallèle
        const [siteSettings, pages] = await Promise.all([
          cmsApi.getCurrentSiteSettings(),
          cmsApi.getPages().catch(() => ({ data: [] })) // Fallback si pages non disponibles
        ]);

        // Filtrer les pages importantes pour le footer
        const footerPages = pages?.data?.filter((page: any) =>
          page.status === 'published' &&
          ['about', 'contact', 'privacy', 'terms', 'faq'].includes(page.template?.toLowerCase())
        ) || [];

        // Préparer les données du footer
        const footerData: FooterData = {
          siteSettings: siteSettings || {},
          footerPages,
          contactInfo: {
            address: siteSettings?.contact_address || 'Avenue de l\'Indépendance, Quartier Rohero, Bujumbura, Burundi',
            phone: siteSettings?.contact_phone || '+257 22 123 456',
            email: siteSettings?.contact_email || '<EMAIL>'
          },
          socialLinks: {
            facebook: siteSettings?.facebook_url || 'https://facebook.com/josnetnetwork',
            twitter: siteSettings?.twitter_url || 'https://twitter.com/josnetnetwork',
            instagram: siteSettings?.instagram_url || 'https://instagram.com/josnetnetwork',
            linkedin: siteSettings?.linkedin_url || 'https://linkedin.com/company/josnetnetwork'
          },
          companyInfo: {
            name: siteSettings?.site_title || 'JOSNET NETWORK',
            description: siteSettings?.site_description || 'Solutions IT et télécommunications au Burundi - Votre partenaire technologique de confiance depuis plus de 10 ans.',
            logo: siteSettings?.logo
          }
        };

        return footerData;

      } catch (error) {
        // Retourner des données de fallback robustes
        return {
          siteSettings: {},
          footerPages: [],
          contactInfo: {
            address: 'Avenue de l\'Indépendance, Quartier Rohero, Bujumbura, Burundi',
            phone: '+257 22 123 456',
            email: '<EMAIL>'
          },
          socialLinks: {
            facebook: 'https://facebook.com/josnetnetwork',
            twitter: 'https://twitter.com/josnetnetwork',
            instagram: 'https://instagram.com/josnetnetwork',
            linkedin: 'https://linkedin.com/company/josnetnetwork'
          },
          companyInfo: {
            name: 'JOSNET NETWORK',
            description: 'Solutions IT et télécommunications au Burundi - Votre partenaire technologique de confiance depuis plus de 10 ans.'
          }
        };
      }
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: 2
  });

  return {
    footerData,
    isLoading,
    isError,
    error,
    refetch,
    // Données spécifiques facilement accessibles
    siteSettings: footerData?.siteSettings,
    footerPages: footerData?.footerPages || [],
    contactInfo: footerData?.contactInfo,
    socialLinks: footerData?.socialLinks,
    companyInfo: footerData?.companyInfo
  };
};

export default useFooterData;
