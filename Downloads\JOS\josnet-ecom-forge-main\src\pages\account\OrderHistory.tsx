import AccountLayout from "@/components/account/AccountLayout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Eye, Download, Search, Loader2 } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import orderApi from "@/services/orderApi";
import { Card, CardContent } from "@/components/ui/card";

// Status color mapping
const statusColors: Record<string, string> = {
  pending: "bg-yellow-100 text-yellow-800",
  processing: "bg-blue-100 text-blue-800",
  shipped: "bg-indigo-100 text-indigo-800",
  delivered: "bg-green-100 text-green-800",
  cancelled: "bg-red-100 text-red-800",
  refunded: "bg-purple-100 text-purple-800",
  partially_refunded: "bg-purple-100 text-purple-800",
  on_hold: "bg-gray-100 text-gray-800"
};

const OrderHistory = () => {
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch orders
  const {
    data: ordersData,
    isLoading,
    isError
  } = useQuery({
    queryKey: ['userOrders'],
    queryFn: () => orderApi.getOrders({ user: 'current' })
  });

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: fr });
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(price);
  };

  // Filter orders
  const filteredOrders = ordersData?.results?.filter((order: any) =>
    order.order_number.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Loading state
  if (isLoading) {
    return (
      <AccountLayout title="Historique des commandes">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AccountLayout>
    );
  }

  // Error state
  if (isError) {
    return (
      <AccountLayout title="Historique des commandes">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-destructive">Une erreur est survenue lors du chargement de vos commandes.</p>
            <Button className="mt-4" onClick={() => window.location.reload()}>
              Réessayer
            </Button>
          </CardContent>
        </Card>
      </AccountLayout>
    );
  }

  return (
    <AccountLayout title="Historique des commandes">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Rechercher une commande..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="border rounded-md overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>N° Commande</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Articles</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.length > 0 ? (
                filteredOrders.map((order: any) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.order_number}</TableCell>
                    <TableCell>{formatDate(order.created_at)}</TableCell>
                    <TableCell>{formatPrice(order.total)}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${statusColors[order.status] || 'bg-gray-100 text-gray-800'}`}>
                        {order.status_display || order.status}
                      </span>
                    </TableCell>
                    <TableCell>{order.item_count || '—'}</TableCell>
                    <TableCell className="text-right space-x-2">
                      <Link to={`/order/${order.id}`}>
                        <Button variant="outline" size="sm" className="inline-flex items-center">
                          <Eye className="h-4 w-4 mr-1" />
                          <span className="hidden sm:inline">Détails</span>
                        </Button>
                      </Link>
                      {order.status === 'delivered' && (
                        <Link to={`/account/invoices/${order.id}`}>
                          <Button variant="outline" size="sm" className="inline-flex items-center">
                            <Download className="h-4 w-4 mr-1" />
                            <span className="hidden sm:inline">Facture</span>
                          </Button>
                        </Link>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    {searchTerm ? 'Aucune commande trouvée' : 'Vous n\'avez pas encore passé de commande'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </AccountLayout>
  );
};

export default OrderHistory;
