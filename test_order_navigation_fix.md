# Test de la correction de navigation vers les commandes

## 🐛 Problème identifié
- **Symptôme :** Clic sur "Voir commande" → "Commande non trouvée"
- **Cause :** Problème de mapping de l'ID de commande et/ou permissions
- **Impact :** Impossible d'accéder aux détails de la commande depuis un retour

## 🔧 Corrections apportées

### **1. Correction du mapping dans accountApi.ts :**
```typescript
// AVANT (manquant)
return {
  ...data,
  returnItems: data.items || [],
  refundAmount: data.refund_amount || '0',
  status: data.status_display || data.status,
  reason: data.reason_display || data.reason
};

// APRÈS (corrigé)
return {
  ...data,
  orderId: data.order, // ✅ CORRECTION: Mapper 'order' vers 'orderId'
  returnItems: data.items || [],
  refundAmount: data.refund_amount || '0',
  status: data.status_display || data.status,
  reason: data.reason_display || data.reason
};
```

### **2. Correction de la navigation dans ReturnDetail :**
```typescript
// AVANT (fragile)
onClick={() => navigate(`/account/orders/${returnDetails?.orderId}`)}

// APRÈS (robuste)
onClick={() => {
  const orderId = returnDetails?.orderId || returnDetails?.order;
  if (orderId) {
    navigate(`/account/orders/${orderId}`);
  } else {
    console.error('Aucun ID de commande trouvé:', returnDetails);
  }
}}
```

### **3. Amélioration du message d'erreur dans OrderDetail :**
```typescript
// ✅ Ajout d'informations de débogage en mode développement
{process.env.NODE_ENV === 'development' && (
  <div className="text-left bg-gray-50 p-3 rounded mb-4">
    <p className="text-xs text-gray-500 mb-1">Informations de débogage :</p>
    <p className="text-xs">ID recherché : {id}</p>
    <p className="text-xs">Erreur : {error?.message || 'Inconnue'}</p>
  </div>
)}
```

## 🧪 Comment tester la correction

### **1. Test de base :**
1. Allez sur la page des retours : `/account/returns`
2. Cliquez sur un retour pour voir ses détails
3. Cliquez sur "Voir la commande"
4. Vérifiez que vous arrivez sur la page de détail de la commande

### **2. Test avec console (mode développement) :**
1. Ouvrez la console du navigateur (F12)
2. Répétez le test de base
3. Vérifiez les logs :
   ```
   // Si succès : navigation normale
   // Si échec : "Aucun ID de commande trouvé: {...}"
   ```

### **3. Test d'erreur :**
1. Si vous voyez encore "Commande non trouvée"
2. Vérifiez les informations de débogage affichées
3. Notez l'ID recherché et le message d'erreur

## 🔍 Diagnostic en cas de problème persistant

### **Cas 1 : "Aucun ID de commande trouvé"**
- **Cause :** Le backend ne retourne pas l'ID de commande
- **Solution :** Vérifiez que le sérializer backend inclut le champ 'order'

### **Cas 2 : "Commande non trouvée" avec ID valide**
- **Cause :** Problème de permissions ou commande inexistante
- **Solution :** Vérifiez que l'utilisateur a accès à cette commande

### **Cas 3 : Erreur de format d'ID**
- **Cause :** L'ID est peut-être un UUID au lieu d'un entier
- **Solution :** Adaptez la logique pour gérer les deux formats

## 📋 Structure des données attendue

### **Backend (ReturnRequestDetailSerializer) :**
```json
{
  "id": "uuid-du-retour",
  "return_number": "RET-001",
  "order": 123,  // ✅ ID numérique de la commande
  "order_details": {
    "id": 123,
    "order_number": "ORD-001",
    "status": "delivered"
  },
  "items": [...],
  "status": "pending"
}
```

### **Frontend (après mapping) :**
```typescript
{
  id: "uuid-du-retour",
  return_number: "RET-001",
  orderId: 123,  // ✅ Mappé depuis 'order'
  order_details: {...},
  returnItems: [...],  // ✅ Mappé depuis 'items'
  status: "En attente"  // ✅ Mappé depuis 'status_display'
}
```

## 🎯 Résultat attendu

**Le problème "Commande non trouvée" devrait être résolu !**

La navigation devrait maintenant :
- ✅ Récupérer correctement l'ID de la commande
- ✅ Naviguer vers la bonne page de détail
- ✅ Afficher les informations de la commande
- ✅ Fournir des messages d'erreur informatifs

## 💡 Points clés de la correction

1. **Mapping correct :** `data.order` → `orderId`
2. **Navigation robuste :** Fallback vers `returnDetails?.order`
3. **Gestion d'erreur :** Logs et messages informatifs
4. **Débogage :** Informations techniques en mode développement

## ⚠️ Notes importantes

- **Permissions :** L'utilisateur ne peut voir que ses propres commandes
- **Format ID :** Le backend utilise des IDs numériques pour les commandes
- **Endpoint :** Utilise `/orders/orders/` avec filtrage automatique par utilisateur
- **Sécurité :** Pas d'endpoint `/account/orders/` séparé, filtrage côté backend
