import React, { useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import AccountLayout from '@/components/account/AccountLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Download,
  Printer,
  ArrowLeft,
  Building,
  User,
  Calendar,
  CreditCard,
  CheckCircle,
  Loader2,
  AlertCircle,
  RefreshCw,
  Mail,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { accountApi, InvoiceDetail as InvoiceDetailType } from '@/services/accountApi';

const InvoiceDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();

  // Récupérer les détails de la facture
  const {
    data: invoice,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['invoice', id],
    queryFn: () => accountApi.getInvoiceDetails(id || ''),
    enabled: !!id,
    retry: 1,
    retryDelay: 1000,
  });

  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    if (!id || isDownloading) return;
    
    try {
      setIsDownloading(true);
      toast({
        title: 'Téléchargement démarré',
        description: `La facture ${id} est en cours de téléchargement`,
      });
      
      // Télécharger le PDF de la facture
      const blob = await accountApi.downloadInvoice(id);
      
      // Créer un URL pour le blob et déclencher le téléchargement
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `facture-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: 'Téléchargement terminé',
        description: `La facture ${id} a été téléchargée avec succès`,
      });
    } catch (error) {
      console.error('Erreur lors du téléchargement de la facture:', error);
      toast({
        title: 'Erreur de téléchargement',
        description: `Une erreur est survenue lors du téléchargement de la facture ${id}`,
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <AccountLayout title="Détails de la facture">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 text-primary animate-spin" />
          <span className="ml-2">Chargement de la facture...</span>
        </div>
      </AccountLayout>
    );
  }

  if (isError || !invoice) {
    return (
      <AccountLayout title="Détails de la facture">
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
          <p className="text-gray-500 max-w-sm mb-4">
            Une erreur est survenue lors du chargement de la facture. Veuillez réessayer.
          </p>
          <Button variant="outline" onClick={() => refetch()}>
            Réessayer
          </Button>
        </div>
      </AccountLayout>
    );
  }

  return (
    <AccountLayout title="Détails de la facture">
      <div className="space-y-6 print:space-y-4">
        <div className="flex justify-between items-center print:hidden">
          <Button variant="outline" asChild>
            <Link to="/account/invoices" className="flex items-center gap-1">
              <ArrowLeft className="h-4 w-4" />
              Retour aux factures
            </Link>
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handlePrint} className="flex items-center gap-1">
              <Printer className="h-4 w-4" />
              Imprimer
            </Button>
            <Button 
              onClick={handleDownload} 
              className="flex items-center gap-1"
              disabled={isDownloading}
            >
              {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              Télécharger
            </Button>
          </div>
        </div>

        <div className="bg-white p-6 border rounded-lg shadow-sm print:shadow-none print:border-none">
          <div className="flex justify-between items-start mb-8 print:mb-6">
            <div>
              <h1 className="text-2xl font-bold print:text-xl">Facture #{invoice.id}</h1>
              <p className="text-gray-500">Commande #{invoice.orderId}</p>
            </div>
            <div className="text-right">
              <div className="flex items-center justify-end gap-1 text-green-600 mb-1">
                <CheckCircle className="h-4 w-4" />
                <span className="font-medium">{invoice.status}</span>
              </div>
              <div className="flex items-center justify-end gap-1 text-gray-500 text-sm">
                <Calendar className="h-4 w-4" />
                <span>Date: {invoice.date}</span>
              </div>
              <div className="flex items-center justify-end gap-1 text-gray-500 text-sm">
                <CreditCard className="h-4 w-4" />
                <span>Paiement: {invoice.paymentMethod}</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6 mb-8 print:mb-6 print:gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-1 text-gray-700 font-medium">
                <Building className="h-4 w-4" />
                <span>Informations client</span>
              </div>
              <div className="text-sm space-y-1 text-gray-600">
                <p className="font-medium">{invoice.customerInfo.name}</p>
                <div className="flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  <span>{invoice.customerInfo.email}</span>
                </div>
                <p>{invoice.customerInfo.address}</p>
                <p>
                  {invoice.customerInfo.postalCode} {invoice.customerInfo.city}
                </p>
                <p>{invoice.customerInfo.country}</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-1 text-gray-700 font-medium">
                <User className="h-4 w-4" />
                <span>Détails de paiement</span>
              </div>
              <div className="text-sm space-y-1 text-gray-600">
                <p><span className="font-medium">Méthode:</span> {invoice.paymentMethod}</p>
                {invoice.paymentId && <p><span className="font-medium">Référence:</span> {invoice.paymentId}</p>}
                <p><span className="font-medium">Date:</span> {invoice.date}</p>
                <p><span className="font-medium">Statut:</span> {invoice.status}</p>
              </div>
            </div>
          </div>

          <div className="border rounded-md overflow-hidden mb-6 print:mb-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Produit</TableHead>
                  <TableHead className="text-right">Quantité</TableHead>
                  <TableHead className="text-right">Prix unitaire</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoice.items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell className="text-right">{item.quantity}</TableCell>
                    <TableCell className="text-right">{item.unitPrice}</TableCell>
                    <TableCell className="text-right">{item.totalPrice}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="flex justify-end">
            <div className="w-full max-w-xs space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Sous-total:</span>
                <span>{invoice.subtotal}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">TVA:</span>
                <span>{invoice.tax}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Frais de livraison:</span>
                <span>{invoice.shipping}</span>
              </div>
              {invoice.discount && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Remise:</span>
                  <span>-{invoice.discount}</span>
                </div>
              )}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total:</span>
                <span>{invoice.amount}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-500 print:hidden">
          <p>
            Pour toute question concernant cette facture, veuillez contacter notre service client à{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </AccountLayout>
  );
};

export default InvoiceDetail;
