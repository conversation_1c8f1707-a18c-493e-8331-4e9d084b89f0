
import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Eye, EyeOff, User, Mail, Lock, Shield, CheckCircle, ArrowRight, Sparkles, Users, Award } from "lucide-react";
import { motion } from "framer-motion";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";

const registerSchema = z.object({
  firstName: z.string().min(1, "Le prénom est requis"),
  lastName: z.string().min(1, "Le nom est requis"),
  email: z
    .string()
    .min(1, "L'email est requis")
    .email("Format d'email invalide"),
  password: z
    .string()
    .min(1, "Le mot de passe est requis")
    .min(8, "Le mot de passe doit contenir au moins 8 caractères"),
  confirmPassword: z.string().min(1, "Veuillez confirmer votre mot de passe"),
  terms: z.boolean().refine(val => val === true, {
    message: "Vous devez accepter les conditions générales"
  })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Les mots de passe ne correspondent pas",
  path: ["confirmPassword"],
});

type RegisterFormValues = z.infer<typeof registerSchema>;

const Register = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { register } = useAuth();
  const navigate = useNavigate();

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      terms: false,
    },
  });

  const onSubmit = async (data: RegisterFormValues) => {
    setIsLoading(true);

    try {
      // Map form data to API format
      const registerData = {
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
        password: data.password,
        confirm_password: data.confirmPassword
      };

      // Call the register function from our auth context
      const response = await register(registerData);

      // Show success message with email verification info
      toast({
        title: "Compte créé avec succès",
        description: response.message || "Bienvenue chez JOSNET NETWORK ! Votre compte a été créé.",
      });

      // Show email verification message if email was sent
      if (response.email_verification?.sent) {
        toast({
          title: "📧 Email de vérification envoyé",
          description: response.email_verification.message,
          duration: 8000,
        });
      }

      // Account Activation by Email: Redirection vers page d'attente d'activation
      if (response.login_allowed === false && response.activation) {
        toast({
          title: "📧 Activez votre compte",
          description: response.next_step || "Un email d'activation a été envoyé à votre adresse.",
          duration: 10000,
        });

        // Rediriger vers la page d'attente d'activation
        navigate('/login', {
          replace: true,
          state: {
            message: "Inscription réussie ! Consultez votre email et cliquez sur le lien d'activation pour activer votre compte.",
            email: response.user.email,
            accountPending: true,
            showResendActivation: true
          }
        });
      } else if (response.auto_login && response.access) {
        // Mode normal: connexion automatique (si jamais activé)
        navigate('/account', {
          replace: true,
          state: {
            fromRegistration: true,
            emailVerificationRequired: !response.user.is_verified
          }
        });
      } else if (response.user.is_verified) {
        // Si déjà vérifié, aller au compte
        navigate('/account');
      } else {
        // Fallback: rediriger vers login
        navigate('/login', {
          state: {
            message: "Veuillez activer votre compte avant de vous connecter.",
            email: response.user.email,
            accountPending: true
          }
        });
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      console.error("Registration error details:", JSON.stringify(error, null, 2));

      // Déterminer le message d'erreur approprié
      let errorMessage = "Un problème est survenu. Veuillez réessayer.";
      let errorTitle = "Erreur lors de l'inscription";

      if (error.errors) {
        // Afficher les erreurs de validation spécifiques
        const errorMessages = [];
        for (const [field, messages] of Object.entries(error.errors)) {
          if (Array.isArray(messages)) {
            errorMessages.push(`${field}: ${messages.join(', ')}`);
          } else if (typeof messages === 'string') {
            errorMessages.push(`${field}: ${messages}`);
          }
        }
        if (errorMessages.length > 0) {
          errorMessage = errorMessages.join('\n');
        }
      } else if (error.message) {
        if (error.message.includes('mode développement')) {
          errorTitle = "Mode Développement";
          errorMessage = error.message;
        } else {
          errorMessage = error.message;
        }
      }

      // Show error message
      toast({
        title: errorTitle,
        description: errorMessage,
        variant: error.message?.includes('mode développement') ? "default" : "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar />

      <main className="flex-grow flex items-center justify-center px-4 py-8 sm:py-12">
        <div className="w-full max-w-6xl grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Side - Branding & Benefits */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="hidden lg:block space-y-8"
          >
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    JOSNET NETWORK
                  </h1>
                  <p className="text-gray-600">Rejoignez notre communauté</p>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-gray-900">
                  Créez votre compte et découvrez nos avantages
                </h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  Rejoignez plus de <span className="text-blue-600 font-semibold">1000+ clients</span> qui nous font confiance pour leurs besoins technologiques au Burundi.
                </p>
              </div>

              {/* Benefits */}
              <div className="space-y-4">
                {[
                  { icon: <CheckCircle className="h-5 w-5" />, text: "Accès exclusif aux offres spéciales", color: "text-green-600" },
                  { icon: <Users className="h-5 w-5" />, text: "Support client prioritaire 24/7", color: "text-blue-600" },
                  { icon: <Award className="h-5 w-5" />, text: "Programme de fidélité avec points", color: "text-purple-600" },
                  { icon: <Sparkles className="h-5 w-5" />, text: "Livraison gratuite sur vos commandes", color: "text-orange-600" }
                ].map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    className="flex items-center space-x-3 text-gray-700"
                  >
                    <div className={`w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center ${benefit.color}`}>
                      {benefit.icon}
                    </div>
                    <span>{benefit.text}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Testimonial */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg"
            >
              <div className="flex items-center space-x-4 mb-4">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=80&h=80&q=80"
                  alt="Client satisfait"
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <h4 className="font-semibold text-gray-900">Jean-Baptiste M.</h4>
                  <p className="text-sm text-gray-600">Directeur IT, Entreprise locale</p>
                </div>
              </div>
              <p className="text-gray-700 italic">
                "JOSNET nous accompagne depuis 5 ans. Leur expertise et leur service client sont exceptionnels. Je recommande vivement !"
              </p>
              <div className="flex items-center mt-3">
                {[...Array(5)].map((_, i) => (
                  <CheckCircle key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Right Side - Registration Form */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="w-full max-w-md mx-auto lg:mx-0 space-y-6"
          >
            {/* Mobile Header */}
            <div className="text-center lg:hidden">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  JOSNET NETWORK
                </h1>
              </div>
              <h2 className="text-2xl font-bold text-gray-900">Créer un compte</h2>
              <p className="text-gray-600 mt-2">
                Rejoignez notre communauté technologique
              </p>
            </div>

            {/* Desktop Header */}
            <div className="hidden lg:block text-center">
              <h2 className="text-3xl font-bold text-gray-900">Inscription</h2>
              <p className="text-gray-600 mt-2">
                Créez votre compte JOSNET
              </p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-6 sm:p-8 border border-white/20">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">Prénom</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                              <Input
                                placeholder="Jean"
                                disabled={isLoading}
                                className="pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">Nom</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                              <Input
                                placeholder="Dupont"
                                disabled={isLoading}
                                className="pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">Adresse email</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                            <Input
                              placeholder="<EMAIL>"
                              type="email"
                              disabled={isLoading}
                              className="pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">Mot de passe</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                            <Input
                              placeholder="••••••••"
                              type={showPassword ? "text" : "password"}
                              disabled={isLoading}
                              className="pl-10 pr-12 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                              {...field}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? (
                                <EyeOff size={20} />
                              ) : (
                                <Eye size={20} />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                        <p className="text-xs text-gray-500 mt-1">
                          Minimum 8 caractères avec lettres et chiffres
                        </p>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">Confirmer le mot de passe</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                            <Input
                              placeholder="••••••••"
                              type={showConfirmPassword ? "text" : "password"}
                              disabled={isLoading}
                              className="pl-10 pr-12 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                              {...field}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? (
                                <EyeOff size={20} />
                              ) : (
                                <Eye size={20} />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="terms"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4 bg-gray-50 border border-gray-200">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading}
                            className="mt-1"
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm text-gray-700">
                            J'accepte les{" "}
                            <Link to="/terms" className="text-blue-600 hover:text-blue-700 underline font-medium">
                              conditions générales
                            </Link>{" "}
                            et la{" "}
                            <Link to="/privacy" className="text-blue-600 hover:text-blue-700 underline font-medium">
                              politique de confidentialité
                            </Link>
                          </FormLabel>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <span>Création en cours...</span>
                      </>
                    ) : (
                      <>
                        <span>Créer mon compte</span>
                        <ArrowRight className="h-5 w-5" />
                      </>
                    )}
                  </Button>
                </form>
              </Form>

              {/* Additional Info */}
              <div className="mt-8 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Shield className="h-5 w-5 text-blue-600" />
                  <h4 className="font-semibold text-blue-900">Sécurité garantie</h4>
                </div>
                <p className="text-sm text-blue-700">
                  Vos données personnelles sont protégées par un cryptage SSL 256-bit et ne sont jamais partagées avec des tiers.
                </p>
              </div>
            </div>

            <div className="text-center mt-8">
              <p className="text-gray-600">
                Vous avez déjà un compte ?{" "}
                <Link to="/login" className="text-blue-600 hover:text-blue-700 font-medium transition-colors">
                  Se connecter
                </Link>
              </p>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Register;
