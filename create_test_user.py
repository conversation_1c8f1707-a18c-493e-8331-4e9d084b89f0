#!/usr/bin/env python3
"""
Script pour créer un utilisateur de test
"""

import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User

def create_test_user():
    """Créer un utilisateur de test"""
    email = "<EMAIL>"
    password = "testpass123"
    
    try:
        # Vérifier si l'utilisateur existe déjà
        if User.objects.filter(email=email).exists():
            print(f"👤 Utilisateur {email} existe déjà")
            user = User.objects.get(email=email)
            # Mettre à jour le mot de passe
            user.set_password(password)
            user.save()
            print(f"🔄 Mot de passe mis à jour")
        else:
            # Créer un nouvel utilisateur
            user = User.objects.create_user(
                email=email,
                password=password,
                first_name="Test",
                last_name="User",
                is_verified=True
            )
            print(f"✅ Utilisateur {email} créé")
        
        print(f"📧 Email: {email}")
        print(f"🔑 Mot de passe: {password}")
        
        return email, password
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None

def reset_existing_user_password():
    """Réinitialiser le mot de passe de l'utilisateur existant"""
    email = "<EMAIL>"
    new_password = "testpass123"
    
    try:
        user = User.objects.get(email=email)
        user.set_password(new_password)
        user.save()
        
        print(f"✅ Mot de passe réinitialisé pour {email}")
        print(f"🔑 Nouveau mot de passe: {new_password}")
        
        return email, new_password
        
    except User.DoesNotExist:
        print(f"❌ Utilisateur {email} non trouvé")
        return None, None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None

if __name__ == "__main__":
    print("👥 Gestion des utilisateurs de test")
    print("=" * 40)
    
    print("\n1. Réinitialisation du mot de passe de l'utilisateur existant:")
    email1, password1 = reset_existing_user_password()
    
    print("\n2. Création d'un utilisateur de test:")
    email2, password2 = create_test_user()
    
    print(f"\n📋 UTILISATEURS DISPONIBLES:")
    if email1 and password1:
        print(f"   {email1} : {password1}")
    if email2 and password2:
        print(f"   {email2} : {password2}")
