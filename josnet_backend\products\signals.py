"""
Signaux Django pour l'envoi automatique d'emails lors de la publication de promotions.
"""

import logging
from django.db.models.signals import post_save, pre_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from django.conf import settings
from .models import Promotion, Product
from core.services.newsletter_service import NewsletterService

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Promotion)
def send_promotion_email_notification(sender, instance, created, **kwargs):
    """
    Envoie automatiquement un email de notification quand une promotion devient active.
    """
    # Vérifier si l'envoi d'email est activé pour cette promotion
    if not instance.send_email_notification:
        return
    
    # Vérifier si l'email a déjà été envoyé
    if instance.email_sent:
        return
    
    # Vérifier si la promotion est active
    if instance.status != 'active':
        return
    
    # Vérifier si la promotion a commencé
    if instance.start_date > timezone.now():
        return
    
    try:
        # Préparer l'URL de la promotion
        promotion_url = f"{settings.FRONTEND_URL}/promotions/{instance.id}"
        
        # Envoyer la notification
        result = NewsletterService.send_promotion_notification(
            title=instance.title,
            description=instance.description,
            promotion_url=promotion_url,
            created_by=instance.created_by
        )
        
        if result.get('success'):
            # Marquer l'email comme envoyé
            instance.email_sent = True
            instance.email_sent_at = timezone.now()
            instance.save(update_fields=['email_sent', 'email_sent_at'])
            
            logger.info(f"Email de promotion envoyé pour '{instance.title}': {result.get('sent_count')} destinataires")
        else:
            logger.error(f"Échec envoi email promotion '{instance.title}': {result.get('error')}")
            
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de promotion '{instance.title}': {str(e)}")


# Variable globale pour stocker les produits qui doivent recevoir un email
_products_to_email = set()

@receiver(pre_save, sender=Product)
def mark_product_for_email_notification(sender, instance, **kwargs):
    """
    Marque le produit pour l'envoi d'email s'il passe de draft à published.
    """
    if instance.pk:  # Produit existant
        try:
            old_instance = Product.objects.get(pk=instance.pk)
            # Si le statut change de draft/inactive vers published
            if old_instance.status != 'published' and instance.status == 'published':
                _products_to_email.add(instance.pk)
        except Product.DoesNotExist:
            pass


@receiver(post_save, sender=Product)
def send_new_product_email_notification(sender, instance, created, **kwargs):
    """
    Envoie automatiquement un email de notification quand un nouveau produit est publié.
    """
    # Si c'est un nouveau produit créé directement en publié
    if created and instance.status == 'published':
        should_send_email = True
    # Si c'est un produit existant qui vient d'être publié
    elif not created and instance.pk in _products_to_email:
        should_send_email = True
        _products_to_email.discard(instance.pk)  # Retirer de la liste
    else:
        should_send_email = False

    if not should_send_email:
        return
    
    try:
        # Préparer l'URL du produit
        product_url = f"{settings.FRONTEND_URL}/products/{instance.slug}"
        
        # Préparer l'image du produit
        product_image = None
        if instance.images.exists():
            primary_image = instance.images.filter(is_primary=True).first()
            if primary_image:
                product_image = f"{settings.MEDIA_URL}{primary_image.image}"
            else:
                first_image = instance.images.first()
                if first_image:
                    product_image = f"{settings.MEDIA_URL}{first_image.image}"
        
        # Envoyer la notification
        result = NewsletterService.send_new_product_notification(
            product_name=instance.name,
            product_description=instance.short_description or instance.description[:200] + "...",
            product_url=product_url,
            product_image=product_image,
            created_by=instance.created_by
        )
        
        if result.get('success'):
            logger.info(f"Email nouveau produit envoyé pour '{instance.name}': {result.get('sent_count')} destinataires")
        else:
            logger.error(f"Échec envoi email nouveau produit '{instance.name}': {result.get('error')}")
            
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email nouveau produit '{instance.name}': {str(e)}")


# Signal pour détecter les changements de statut
@receiver(pre_save, sender=Promotion)
def track_promotion_status_change(sender, instance, **kwargs):
    """
    Suit les changements de statut des promotions pour déclencher les emails au bon moment.
    """
    if instance.pk:
        try:
            old_instance = Promotion.objects.get(pk=instance.pk)
            
            # Si la promotion passe de draft/scheduled à active
            if (old_instance.status in ['draft', 'scheduled'] and 
                instance.status == 'active' and 
                not instance.email_sent):
                
                # Marquer pour envoi d'email (sera traité par post_save)
                instance._send_email_on_save = True
                
        except Promotion.DoesNotExist:
            pass


@receiver(pre_save, sender=Product)
def track_product_status_change(sender, instance, **kwargs):
    """
    Suit les changements de statut des produits pour déclencher les emails au bon moment.
    """
    if instance.pk:
        try:
            old_instance = Product.objects.get(pk=instance.pk)
            
            # Si le produit passe de draft à published
            if (old_instance.status in ['draft', 'archived'] and 
                instance.status == 'published'):
                
                # Marquer pour envoi d'email (sera traité par post_save)
                instance._send_email_on_save = True
                
        except Product.DoesNotExist:
            pass
