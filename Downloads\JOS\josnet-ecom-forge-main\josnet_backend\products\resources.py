from import_export import resources, fields, widgets
from import_export.widgets import ForeignKeyWidget, ManyToManyWidget
from django.contrib.auth import get_user_model
from .models import (
    Category,
    ProductAttribute,
    AttributeValue,
    Product,
    ProductVariant,
    Inventory
)

User = get_user_model()

class CategoryResource(resources.ModelResource):
    """Resource for importing/exporting categories."""
    
    parent = fields.Field(
        column_name='parent',
        attribute='parent',
        widget=ForeignKeyWidget(Category, 'name')
    )
    
    class Meta:
        model = Category
        import_id_fields = ['slug']
        fields = ('id', 'name', 'slug', 'description', 'parent', 'is_active')
        export_order = fields

class ProductAttributeResource(resources.ModelResource):
    """Resource for importing/exporting product attributes."""
    
    class Meta:
        model = ProductAttribute
        import_id_fields = ['name']
        fields = ('id', 'name', 'description')
        export_order = fields

class AttributeValueResource(resources.ModelResource):
    """Resource for importing/exporting attribute values."""
    
    attribute = fields.Field(
        column_name='attribute',
        attribute='attribute',
        widget=ForeignKeyWidget(ProductAttribute, 'name')
    )
    
    class Meta:
        model = AttributeValue
        import_id_fields = ['attribute', 'value']
        fields = ('id', 'attribute', 'value')
        export_order = fields

class ProductResource(resources.ModelResource):
    """Resource for importing/exporting products."""
    
    categories = fields.Field(
        column_name='categories',
        attribute='categories',
        widget=ManyToManyWidget(Category, field='name', separator='|')
    )
    
    created_by = fields.Field(
        column_name='created_by',
        attribute='created_by',
        widget=ForeignKeyWidget(User, 'username')
    )
    
    updated_by = fields.Field(
        column_name='updated_by',
        attribute='updated_by',
        widget=ForeignKeyWidget(User, 'username')
    )
    
    class Meta:
        model = Product
        import_id_fields = ['slug']
        fields = (
            'id', 'name', 'slug', 'sku', 'description', 'short_description',
            'price', 'sale_price', 'cost_price', 'categories', 'is_featured',
            'is_digital', 'status', 'created_by', 'updated_by', 'created_at', 'updated_at'
        )
        export_order = fields
        skip_unchanged = True
        report_skipped = True
        
    def before_import_row(self, row, **kwargs):
        """
        Handle special cases before import.
        """
        # Set default status if not provided
        if 'status' not in row or not row['status']:
            row['status'] = 'draft'
            
        # Convert boolean fields from string to boolean
        for field in ['is_featured', 'is_digital']:
            if field in row:
                value = row[field].lower()
                if value in ('true', 'yes', '1'):
                    row[field] = True
                elif value in ('false', 'no', '0'):
                    row[field] = False
    
    def after_import_row(self, row, row_result, **kwargs):
        """
        Create inventory for new products.
        """
        if row_result.import_type == 'new':
            try:
                product = Product.objects.get(slug=row['slug'])
                # Create inventory if it doesn't exist
                Inventory.objects.get_or_create(product=product)
            except Product.DoesNotExist:
                pass

class ProductVariantResource(resources.ModelResource):
    """Resource for importing/exporting product variants."""
    
    product = fields.Field(
        column_name='product',
        attribute='product',
        widget=ForeignKeyWidget(Product, 'slug')
    )
    
    attributes = fields.Field(
        column_name='attributes',
        attribute='attributes',
        widget=ManyToManyWidget(AttributeValue, field='value', separator='|')
    )
    
    class Meta:
        model = ProductVariant
        import_id_fields = ['sku']
        fields = (
            'id', 'product', 'name', 'sku', 'price_adjustment',
            'attributes', 'is_active', 'created_at', 'updated_at'
        )
        export_order = fields
        skip_unchanged = True
        report_skipped = True
    
    def before_import_row(self, row, **kwargs):
        """
        Handle special cases before import.
        """
        # Convert boolean fields from string to boolean
        if 'is_active' in row:
            value = row['is_active'].lower()
            if value in ('true', 'yes', '1'):
                row['is_active'] = True
            elif value in ('false', 'no', '0'):
                row['is_active'] = False
    
    def after_import_row(self, row, row_result, **kwargs):
        """
        Create inventory for new variants.
        """
        if row_result.import_type == 'new':
            try:
                variant = ProductVariant.objects.get(sku=row['sku'])
                # Create inventory if it doesn't exist
                Inventory.objects.get_or_create(variant=variant)
            except ProductVariant.DoesNotExist:
                pass

class InventoryResource(resources.ModelResource):
    """Resource for importing/exporting inventory."""
    
    product = fields.Field(
        column_name='product',
        attribute='product',
        widget=ForeignKeyWidget(Product, 'slug')
    )
    
    variant = fields.Field(
        column_name='variant',
        attribute='variant',
        widget=ForeignKeyWidget(ProductVariant, 'sku')
    )
    
    class Meta:
        model = Inventory
        fields = (
            'id', 'product', 'variant', 'quantity', 'reserved_quantity',
            'low_stock_threshold', 'last_checked'
        )
        export_order = fields
        skip_unchanged = True
        report_skipped = True
