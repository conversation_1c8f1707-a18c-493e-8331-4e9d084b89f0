
import { useState, ReactNode } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from '@/contexts/AuthContext';
import {
  LayoutDashboard,
  Package,
  FolderTree,
  ShoppingCart,
  BarChart,
  Users,
  PackageOpen,
  Tag,
  MessageSquare,
  MessageCircle,
  FileText,
  ClipboardList,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  CreditCard,
  Receipt,
  ArrowDownUp,
  Settings,
  Mail,
  Phone,

} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";


import { toast } from "@/hooks/use-toast";

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { user } = useAuth();




  // Menu items accessible to all staff
  const commonMenuItems = [
    {
      title: "Tableau de bord",
      icon: <LayoutDashboard size={20} />,
      href: "/admin/dashboard",
    },
    {
      title: "Produits",
      icon: <Package size={20} />,
      href: "/admin/products",
    },
    {
      title: "Catégories",
      icon: <FolderTree size={20} />,
      href: "/admin/categories",
    },
    {
      title: "Commandes",
      icon: <ShoppingCart size={20} />,
      href: "/admin/orders",
    },
    {
      title: "Retours",
      icon: <ArrowDownUp size={20} />,
      href: "/admin/returns",
    },
    {
      title: "Réclamations",
      icon: <MessageCircle size={20} />,
      href: "/admin/claims",
    },
    {
      title: "Transactions",
      icon: <ArrowDownUp size={20} />,
      href: "/admin/transactions",
    },
    {
      title: "Factures",
      icon: <Receipt size={20} />,
      href: "/admin/invoices",
    },
    {
      title: "Statistiques",
      icon: <BarChart size={20} />,
      href: "/admin/statistics",
    },
    {
      title: "Stocks",
      icon: <PackageOpen size={20} />,
      href: "/admin/inventory",
    },
    {
      title: "Promotions",
      icon: <Tag size={20} />,
      href: "/admin/promotions",
    },
    // Communication section
    {
      title: "Messagerie",
      icon: <MessageSquare size={20} />,
      href: "/admin/messages",
    },
    {
      title: "Modèles de réponse",
      icon: <MessageCircle size={20} />,
      href: "/admin/response-templates",
    },
    {
      title: "Contenu (CMS)",
      icon: <FileText size={20} />,
      href: "/admin/cms",
    },
  ];

  // Admin-only menu items
  const adminOnlyItems = [
    {
      title: "Utilisateurs",
      icon: <Users size={20} />,
      href: "/admin/users",
      adminOnly: true
    },
    {
      title: "Paiements",
      icon: <CreditCard size={20} />,
      href: "/admin/payment-methods",
      adminOnly: true
    },
    {
      title: "Paramètres messages",
      icon: <Settings size={20} />,
      href: "/admin/message-settings",
      adminOnly: true
    },
    {
      title: "Journalisation",
      icon: <ClipboardList size={20} />,
      href: "/admin/logs",
      adminOnly: true
    },
  ];

  // Combine menu items based on user role
  const menuItems = user?.role === 'admin'
    ? [...commonMenuItems, ...adminOnlyItems]
    : commonMenuItems;

  const handleLogout = () => {
    // Implement actual logout logic
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");

    toast({
      title: "Déconnexion réussie",
      description: "À bientôt!",
    });

    // Redirect to login page
    window.location.href = "/login";
  };

  // Fonction pour rendre un élément de menu avec badge optionnel
  const renderMenuItem = (item: any, collapsed: boolean = false) => {
    const isActive = location.pathname === item.href;
    const showNewBadge = item.badge === 'Nouveau';

    // Check if user has access to this item
    const hasAccess = !item.adminOnly || user?.role === 'admin';

    if (!hasAccess) {
      return (
        <button
          key={item.href}
          onClick={() => window.location.href = '/unauthorized'}
          className={`flex items-center px-2 py-2 rounded-md relative text-gray-400 cursor-not-allowed opacity-50 ${
            collapsed ? "justify-center" : ""
          }`}
          title={collapsed ? `${item.title} (Accès restreint)` : "Accès administrateur requis"}
        >
          <span className={collapsed ? "" : "mr-3"}>{item.icon}</span>
          {!collapsed && <span className="flex-1">{item.title}</span>}
          {!collapsed && (
            <Badge className="bg-red-100 text-red-600 text-xs">
              Admin
            </Badge>
          )}
        </button>
      );
    }

    return (
      <Link
        key={item.href}
        to={item.href}
        className={`flex items-center px-2 py-2 rounded-md relative ${
          isActive
            ? "bg-primary/10 text-primary font-medium"
            : "text-gray-700 hover:bg-gray-100"
        } ${collapsed ? "justify-center" : ""}`}
        title={collapsed ? item.title : undefined}
      >
        <span className={collapsed ? "" : "mr-3"}>{item.icon}</span>
        {!collapsed && <span className="flex-1">{item.title}</span>}
        {showNewBadge && (
          <Badge
            className="bg-green-500 text-white text-xs px-1.5 py-0.5"
            style={{ fontSize: '10px' }}
          >
            {item.badge}
          </Badge>
        )}
      </Link>
    );
  };



  return (
    <div className="min-h-screen bg-gray-100 flex">

      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="bg-white shadow-md"
        >
          {mobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>
      </div>

      {/* Sidebar - Mobile */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 bg-black/50 z-40 lg:hidden" onClick={() => setMobileMenuOpen(false)}>
          <div
            className="absolute top-0 left-0 w-64 h-full bg-white shadow-lg overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 flex items-center justify-between border-b">
              <Link to="/admin/dashboard" className="flex items-center">
                <span className="text-xl font-bold text-primary">JOSNET</span>
                <span className="text-xl font-bold text-accent ml-1">ADMIN</span>
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X size={20} />
              </Button>
            </div>
            <div className="p-4">
              <nav className="space-y-1">
                {menuItems.map((item) => (
                  <div key={item.href} onClick={() => setMobileMenuOpen(false)}>
                    {renderMenuItem(item, false)}
                  </div>
                ))}
              </nav>
              <div className="mt-6 pt-6 border-t">
                <Button
                  variant="ghost"
                  className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={handleLogout}
                >
                  <LogOut size={20} className="mr-3" />
                  <span>Déconnexion</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sidebar - Desktop */}
      <div
        className={`hidden lg:block bg-white border-r border-gray-200 transition-all duration-300 ${
          sidebarCollapsed ? "w-20" : "w-64"
        }`}
      >
        <div className="h-full flex flex-col">
          <div className={`p-4 flex ${sidebarCollapsed ? "justify-center" : "justify-between"} items-center border-b`}>
            {!sidebarCollapsed ? (
              <>
                <Link to="/admin/dashboard" className="flex items-center">
                  <span className="text-xl font-bold text-primary">JOSNET</span>
                  <span className="text-xl font-bold text-accent ml-1">ADMIN</span>
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarCollapsed(true)}
                  className="ml-2"
                >
                  <ChevronLeft size={20} />
                </Button>
              </>
            ) : (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarCollapsed(false)}
              >
                <ChevronRight size={20} />
              </Button>
            )}
          </div>
          <div className="flex-grow overflow-y-auto p-4">
            <nav className="space-y-1">
              {menuItems.map((item) => renderMenuItem(item, sidebarCollapsed))}
            </nav>
          </div>
          <div className={`p-4 border-t ${sidebarCollapsed ? "flex justify-center" : ""}`}>
            <Button
              variant="ghost"
              className={`text-red-600 hover:text-red-700 hover:bg-red-50 ${
                sidebarCollapsed ? "w-10 h-10 p-0" : "w-full justify-start"
              }`}
              title={sidebarCollapsed ? "Déconnexion" : undefined}
              onClick={handleLogout}
            >
              <LogOut size={20} className={sidebarCollapsed ? "" : "mr-3"} />
              {!sidebarCollapsed && <span>Déconnexion</span>}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-lg font-semibold text-gray-900">
                {user?.role === 'admin' ? 'Administration' : 'Interface Staff'}
              </h1>
              <Badge className={user?.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}>
                {user?.role === 'admin' ? 'Administrateur' : 'Staff'}
              </Badge>
            </div>

            <div className="relative inline-block text-left">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className="text-sm font-medium">{user?.full_name || 'Utilisateur'}</p>
                    <p className="text-xs text-gray-500">{user?.email || '<EMAIL>'}</p>
                  </div>
                  <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium">
                    {user?.first_name?.[0]}{user?.last_name?.[0]}
                  </div>
                </div>
                <Link
                  to={user?.role === 'admin' ? '/admin/profile' : '/admin/staff-profile'}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Mon Profil
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 overflow-auto bg-gray-50 p-4 sm:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
