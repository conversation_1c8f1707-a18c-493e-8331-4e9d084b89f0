import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authApi, User, LoginCredentials, RegisterData, AuthResponse, ApiError } from '@/services/api';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  register: (userData: RegisterData) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  forceLogout: () => void;
  updateUser: (userData: Partial<User>) => void;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Effectue la logique d'authentification au chargement de l'application
  useEffect(() => {
    const initAuth = async () => {
      const storedUser = localStorage.getItem('user');
      const refreshToken = localStorage.getItem('refreshToken');

      if (storedUser && refreshToken) {
        try {
          const parsedUser = JSON.parse(storedUser);
          console.log('🔄 Tentative de restauration de session...');

          // Tentative de rafraîchir le token pour obtenir un nouvel accessToken valide
          const newTokens = await authApi.refreshToken(refreshToken);

          // Si le rafraîchissement réussit, on met à jour les tokens et l'utilisateur
          localStorage.setItem('accessToken', newTokens.access);
          // Le backend peut renvoyer un nouveau refresh token, mais ce n'est pas toujours le cas
          if (newTokens.refresh) {
            localStorage.setItem('refreshToken', newTokens.refresh);
          }

          // On récupère les dernières informations du profil avec le nouveau token
          const profileResponse = await authApi.getProfile();
          const updatedUser = { ...parsedUser, ...profileResponse };

          localStorage.setItem('user', JSON.stringify(updatedUser));
          setUser(updatedUser);
          console.log('✅ Session restaurée avec succès !');

        } catch (error) {
          // Si le rafraîchissement échoue (ex: refreshToken expiré), on nettoie tout
          console.warn('⚠️ Le rafraîchissement du token a échoué. Déconnexion.', error);
          forceLogout();
        }
      }

      // On termine le chargement initial
      setIsLoading(false);
    };

    initAuth();
  }, []); // Cet effet ne doit s'exécuter qu'une seule fois au montage

  // Login function
  const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authApi.login(credentials);

      // Store tokens and user data
      localStorage.setItem('accessToken', response.access);
      localStorage.setItem('refreshToken', response.refresh);
      localStorage.setItem('user', JSON.stringify(response.user));

      setUser(response.user);
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      setError(apiError.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: RegisterData): Promise<AuthResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authApi.register(userData);

      // Store tokens and user data
      localStorage.setItem('accessToken', response.access);
      localStorage.setItem('refreshToken', response.refresh);
      localStorage.setItem('user', JSON.stringify(response.user));

      setUser(response.user);
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      setError(apiError.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Force logout (pour les erreurs 401)
  const forceLogout = (): void => {
    console.warn('🔑 Déconnexion forcée (token invalide)');
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    setUser(null);
    setError('Votre session a expiré. Veuillez vous reconnecter.');
  };

  // Logout function
  const logout = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const refreshToken = localStorage.getItem('refreshToken') || '';
      // Try to call the logout API, but don't block on failure
      try {
        await authApi.logout(refreshToken);
      } catch (apiError) {
        console.error('API logout error:', apiError);
        // Continue with local logout even if API call fails
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear user data and tokens
      localStorage.removeItem('user');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      // Clear cart data
      localStorage.removeItem('cart');
      setUser(null);
      setIsLoading(false);
    }
  };

  // Update user function
  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    forceLogout,
    updateUser,
    error
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
