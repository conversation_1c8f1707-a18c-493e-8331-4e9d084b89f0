{"phase_1_critique": {"name": "Phase 1 - Éléments Critiques (1-2 jours)", "description": "Navigation, authentification, panier, checkout", "estimated_hours": 16, "text_count": 129, "sample_texts": [{"key": "cart.produit_ajout", "text": "Produit a<PERSON>", "file": "src\\contexts\\CartContext.tsx"}, {"key": "cart.produit_non_trouv", "text": "Produit non trouvé", "file": "src\\contexts\\CartContext.tsx"}, {"key": "cart.produit_indisponible", "text": "Produit indisponible", "file": "src\\contexts\\CartContext.tsx"}, {"key": "cart.ce_produit_n", "text": "Ce produit n", "file": "src\\contexts\\CartContext.tsx"}, {"key": "cart.produit_ajout_au_pan", "text": "Produit ajouté au panier", "file": "src\\contexts\\CartContext.tsx"}, {"key": "cart.api_de_validation_de", "text": "API de validation de code promo\n      const response = await promotionApi.validatePromoCode(code, cartTotal);\n\n      if (response.valid) {\n        // Calculer le pourcentage de réduction\n        const discountPercentage = response.discount_amount / cartTotal;\n        setPromoApplied(true);\n        setPromoCode(code); // ✅ AJOUT: Stocker le code promo\n        setPromoDiscount(discountPercentage);\n        return true;\n      } else {\n        console.log(", "file": "src\\contexts\\CartContext.tsx"}, {"key": "cart.code_promo_appliqu", "text": "Code promo appliqué", "file": "src\\pages\\Cart.tsx"}, {"key": "cart.la_rduction_a_t_appl", "text": "La réduction a été appliquée à votre panier", "file": "src\\pages\\Cart.tsx"}, {"key": "cart.diminuer_la_quantit", "text": "Diminuer la quantité", "file": "src\\pages\\Cart.tsx"}, {"key": "cart.augmenter_la_quantit", "text": "Augmenter la quantité", "file": "src\\pages\\Cart.tsx"}]}, "phase_2_haute": {"name": "Phase 2 - Interface Principale (2-3 jours)", "description": "Produits, recherche, accueil", "estimated_hours": 24, "text_count": 398, "sample_texts": [{"key": "common.rechercher", "text": "<PERSON><PERSON><PERSON>", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.rechercher_un_articl", "text": "Rechercher un article...", "file": "src\\pages\\Blog.tsx"}, {"key": "common.rechercher_un_articl", "text": "Rechercher un article...", "file": "src\\pages\\Blog.tsx"}, {"key": "common.josnet_network_est_u", "text": "JOSNET NETWORK est une entreprise spécialisée dans la fourniture de solutions IT et de télécommunications au Burundi. Nous proposons une gamme complète de produits technologiques de haute qualité pour les particuliers et les entreprises.", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.nous_livrons_partout", "text": "Nous livrons partout au Burundi. Pour les commandes internationales, veuillez nous contacter directement pour discuter des options de livraison disponibles.", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.vous_pouvez_contacte", "text": "Vous pouvez contacter notre service client par téléphone au +257 XX XX XX XX, par email à <EMAIL>, ou en utilisant le formulaire de contact sur notre site. Nous sommes disponibles du lundi au vendredi de 8h00 à 18h00, et le samedi de 9h00 à 13h00.", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.le_d<PERSON>_de_livraison", "text": "Le délai de livraison standard est de 3 à 5 jours ouvrables pour les commandes à Bujumbura, et de 5 à 7 jours ouvrables pour le reste du pays. Certains produits peuvent avoir des délais différents, qui seront indiqués lors de l", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.vous_pouvez_retourne", "text": "Vous pouvez retourner un produit dans les 30 jours suivant la réception si vous n", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.comment_retourner_un", "text": "Comment retourner un produit ?", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.pour_retourner_un_pr", "text": "Pour retourner un produit, contactez d", "file": "src\\pages\\FAQ.tsx"}]}, "phase_3_moyenne": {"name": "Phase 3 - Fonctionnalités Secondaires (2-3 jours)", "description": "Profil, commandes, notifications", "estimated_hours": 20, "text_count": 376, "sample_texts": [{"key": "common.commandes", "text": "Commandes", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.comment_passer_une_c", "text": "Comment passer une commande ?", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.puisje_modifier_ma_c", "text": "Puis-je modifier ma commande après l", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.vous_pouvez_modifier", "text": "Vous pouvez modifier votre commande dans l", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.comment_annuler_ma_c", "text": "Comment annuler ma commande ?", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.pour_annuler_votre_c", "text": "Pour annuler votre commande, contactez notre service client immédiatement. Les commandes peuvent être annulées gratuitement avant qu", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.combien_de_temps_fau", "text": "Combien de temps faut-il pour recevoir ma commande ?", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.comment_suivre_ma_co", "text": "Comment suivre ma commande ?", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.une_fois_votre_comma", "text": "Une fois votre commande expédiée, vous recevrez un email contenant un numéro de suivi. Vous pouvez utiliser ce numéro sur notre page ", "file": "src\\pages\\FAQ.tsx"}, {"key": "common.comment_obtenir_une_", "text": "Comment obtenir une facture pour ma commande ?", "file": "src\\pages\\FAQ.tsx"}]}, "phase_4_basse": {"name": "Phase 4 - Contenu Éditorial (1-2 jours)", "description": "CMS, blog, pages d'aide", "estimated_hours": 12, "text_count": 365, "sample_texts": [{"key": "common.franais", "text": "Français", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.succs", "text": "Su<PERSON>ès", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.prcdent", "text": "Précédent", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.quantit", "text": "Quantité", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.impossible_de_sauveg", "text": "Impossible de sauvegarder la préférence de langue:", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.api_si_connect_try_c", "text": "API (si connecté)\n      try {\n        const token = localStorage.getItem(", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.impossible_de_rcupre", "text": "Impossible de récupérer la préférence de langue utilisateur:", "file": "src\\contexts\\TranslationContext.tsx"}, {"key": "common.copi_", "text": "Copié !", "file": "src\\pages\\BlogPost.tsx"}, {"key": "common.rseaux_connectivit", "text": "Réseaux & Connectivité", "file": "src\\pages\\Contact.tsx"}, {"key": "common.matriel_informatique", "text": "Matériel Informatique", "file": "src\\pages\\Contact.tsx"}]}}