#!/usr/bin/env python3
"""
Script pour déboguer la différence entre le nombre d'avis en base et affiché
"""

import sys
import os
import requests

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from products.models import Product, ProductReview

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"

def check_database_reviews():
    """Vérifier tous les avis en base de données"""
    print("🗄️ VÉRIFICATION EN BASE DE DONNÉES")
    print("=" * 50)
    
    try:
        product = Product.objects.get(id=1)
        print(f"📦 Produit: {product.name}")
        
        # Tous les avis (approuvés et non approuvés)
        all_reviews = ProductReview.objects.filter(product=product)
        print(f"\n📊 TOUS LES AVIS:")
        print(f"   Total: {all_reviews.count()}")
        
        for i, review in enumerate(all_reviews, 1):
            print(f"   {i}. ID: {review.id}")
            print(f"      👤 Utilisateur: {review.user.email}")
            print(f"      ⭐ Note: {review.rating}")
            print(f"      📝 Titre: {review.title}")
            print(f"      ✅ Approuvé: {review.is_approved}")
            print(f"      📅 Date: {review.created_at}")
            print()
        
        # Avis approuvés seulement
        approved_reviews = ProductReview.objects.filter(product=product, is_approved=True)
        print(f"📊 AVIS APPROUVÉS SEULEMENT:")
        print(f"   Total: {approved_reviews.count()}")
        
        for i, review in enumerate(approved_reviews, 1):
            print(f"   {i}. ID: {review.id} - {review.user.email} - {review.title}")
        
        return {
            'total': all_reviews.count(),
            'approved': approved_reviews.count(),
            'all_reviews': all_reviews,
            'approved_reviews': approved_reviews
        }
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def check_api_stats():
    """Vérifier l'API des statistiques"""
    print(f"\n🔌 VÉRIFICATION API STATISTIQUES")
    print("=" * 50)
    
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/stats/", 
            params={'product': 1}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API accessible")
            print(f"📊 Données retournées:")
            print(f"   Total avis: {data.get('total_reviews', 0)}")
            print(f"   Note moyenne: {data.get('average_rating', 0)}")
            print(f"   Achats vérifiés: {data.get('verified_purchases_count', 0)}")
            
            # Distribution des notes
            distribution = data.get('rating_distribution', {})
            print(f"   Distribution des notes:")
            for rating in [5, 4, 3, 2, 1]:
                count = distribution.get(str(rating), 0)
                if count > 0:
                    print(f"     {rating}⭐: {count} avis")
            
            return data
        else:
            print(f"❌ Erreur API: {response.status_code}")
            print(f"Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def check_api_list():
    """Vérifier l'API de liste des avis"""
    print(f"\n📋 VÉRIFICATION API LISTE DES AVIS")
    print("=" * 50)
    
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/", 
            params={'product': 1}
        )
        
        if response.status_code == 200:
            data = response.json()
            reviews = data.get('results', [])
            print(f"✅ API accessible")
            print(f"📦 Avis récupérés: {len(reviews)}")
            print(f"📄 Pagination:")
            print(f"   Count: {data.get('count', 0)}")
            print(f"   Next: {data.get('next')}")
            print(f"   Previous: {data.get('previous')}")
            
            print(f"\n📋 Liste des avis retournés:")
            for i, review in enumerate(reviews, 1):
                print(f"   {i}. ID: {review.get('id')}")
                print(f"      👤 Utilisateur: {review.get('user_name')}")
                print(f"      ⭐ Note: {review.get('rating')}")
                print(f"      📝 Titre: {review.get('title')}")
                print(f"      ✅ Approuvé: {review.get('is_approved', 'N/A')}")
                print(f"      🛡️ Achat vérifié: {review.get('is_verified_purchase')}")
                print()
            
            return data
        else:
            print(f"❌ Erreur API: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def check_frontend_display():
    """Vérifier ce que le frontend devrait afficher"""
    print(f"\n🌐 ANALYSE POUR LE FRONTEND")
    print("=" * 50)
    
    print(f"🔍 Le frontend utilise:")
    print(f"   • API stats pour le nombre total d'avis")
    print(f"   • API liste pour afficher les avis")
    print(f"   • Seuls les avis APPROUVÉS sont comptés et affichés")
    
    print(f"\n💡 EXPLICATION PROBABLE:")
    print(f"   • Base de données: 7 avis au total")
    print(f"   • Avis approuvés: probablement 6")
    print(f"   • Avis non approuvés: probablement 1 (celui de l'admin)")
    print(f"   • Frontend affiche: 6 (seulement les approuvés)")

def main():
    print("🔍 DÉBOGAGE: DIFFÉRENCE NOMBRE D'AVIS")
    print("=" * 60)
    
    # 1. Vérifier en base de données
    db_data = check_database_reviews()
    
    # 2. Vérifier l'API des statistiques
    api_stats = check_api_stats()
    
    # 3. Vérifier l'API de liste
    api_list = check_api_list()
    
    # 4. Analyser pour le frontend
    check_frontend_display()
    
    print(f"\n📊 RÉSUMÉ COMPARATIF:")
    if db_data and api_stats and api_list:
        print(f"   Base de données (total): {db_data['total']}")
        print(f"   Base de données (approuvés): {db_data['approved']}")
        print(f"   API statistiques: {api_stats.get('total_reviews', 0)}")
        print(f"   API liste (count): {api_list.get('count', 0)}")
        print(f"   API liste (résultats): {len(api_list.get('results', []))}")
    
    print(f"\n🎯 CONCLUSION:")
    if db_data:
        if db_data['total'] != db_data['approved']:
            print(f"   ✅ EXPLICATION TROUVÉE!")
            print(f"   📊 Total en base: {db_data['total']} avis")
            print(f"   ✅ Approuvés: {db_data['approved']} avis")
            print(f"   ❌ Non approuvés: {db_data['total'] - db_data['approved']} avis")
            print(f"   🌐 Frontend affiche: {db_data['approved']} (seulement les approuvés)")
            print(f"\n💡 L'avis de l'admin (ID: 2) n'est pas approuvé,")
            print(f"   c'est pourquoi il n'apparaît pas dans le décompte frontend!")
        else:
            print(f"   🤔 Tous les avis sont approuvés, il faut chercher ailleurs...")

if __name__ == "__main__":
    main()
