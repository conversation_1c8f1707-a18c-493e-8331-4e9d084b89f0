#!/usr/bin/env python3
"""
Installation script for Python 3.13 compatibility
This script attempts to install packages with fallback options for compatibility issues
"""

import subprocess
import sys
import os

def run_pip_command(command):
    """Run a pip command and return success status"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Success: {command}")
            return True
        else:
            print(f"✗ Failed: {command}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Exception running {command}: {e}")
        return False

def install_package(package_name, fallback_versions=None):
    """Try to install a package with fallback versions"""
    print(f"\n--- Installing {package_name} ---")
    
    # Try the main package first
    if run_pip_command(f"pip install --no-cache-dir {package_name}"):
        return True
    
    # Try fallback versions if provided
    if fallback_versions:
        for version in fallback_versions:
            print(f"Trying fallback version: {version}")
            if run_pip_command(f"pip install --no-cache-dir {package_name}=={version}"):
                return True
    
    # Try installing without building wheels (use pre-built if available)
    print("Trying with --only-binary=all")
    if run_pip_command(f"pip install --no-cache-dir --only-binary=all {package_name}"):
        return True
    
    # Try installing with --no-build-isolation
    print("Trying with --no-build-isolation")
    if run_pip_command(f"pip install --no-cache-dir --no-build-isolation {package_name}"):
        return True
    
    print(f"❌ Failed to install {package_name}")
    return False

def main():
    """Main installation function"""
    print("🚀 Starting Python 3.13 compatible package installation...")
    
    # Upgrade pip, setuptools, and wheel first
    print("\n=== Upgrading core tools ===")
    run_pip_command("pip install --upgrade pip setuptools wheel")
    
    # Define packages with fallback versions
    packages = [
        ("Django>=4.2.7,<5.0", ["4.2.16", "4.2.15", "4.2.14"]),
        ("djangorestframework>=3.14.0", ["3.15.2", "3.15.1", "3.14.0"]),
        ("djangorestframework-simplejwt>=5.3.0", ["5.3.0"]),
        ("django-cors-headers>=4.3.0", ["4.4.0", "4.3.1", "4.3.0"]),
        ("django-filter>=23.3", ["24.3", "24.2", "23.5"]),
        ("Pillow>=10.1.0", ["11.0.0", "10.4.0", "10.3.0"]),
        ("stripe>=7.5.0", ["11.2.0", "10.12.0", "9.12.0"]),
        ("reportlab>=4.0.7", ["4.2.5", "4.2.4", "4.2.2"]),
        ("python-dateutil>=2.8.2", ["2.9.0", "2.8.2"]),
        ("django-import-export>=3.3.1", ["4.1.1", "4.0.7", "3.3.8"]),
    ]
    
    failed_packages = []
    
    for package, fallbacks in packages:
        if not install_package(package, fallbacks):
            failed_packages.append(package)
    
    print("\n" + "="*50)
    if failed_packages:
        print("❌ Some packages failed to install:")
        for pkg in failed_packages:
            print(f"  - {pkg}")
        print("\nYou may need to install these manually or find alternative versions.")
    else:
        print("✅ All packages installed successfully!")
    
    print("\n🔍 Checking installed packages:")
    run_pip_command("pip list")

if __name__ == "__main__":
    main()
