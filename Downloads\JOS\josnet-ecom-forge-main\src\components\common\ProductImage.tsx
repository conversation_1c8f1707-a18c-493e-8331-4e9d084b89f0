import React, { useState } from 'react';
import { Package } from 'lucide-react';

interface ProductImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackClassName?: string;
}

const ProductImage: React.FC<ProductImageProps> = ({
  src,
  alt,
  className = 'w-full h-full object-cover',
  fallbackClassName = 'w-full h-full flex items-center justify-center bg-gray-100'
}) => {
  const [hasError, setHasError] = useState(false);

  // Placeholder image URL (local asset)
  const placeholderImage = '/assets/images/placeholder-product.jpg';

  // Handle image load error
  const handleError = () => {
    setHasError(true);
  };

  if (hasError) {
    // Try to use local placeholder
    return (
      <div className={fallbackClassName}>
        <Package className="h-12 w-12 text-gray-400" />
      </div>
    );
  }

  return (
    <img
      src={src || placeholderImage}
      alt={alt}
      className={className}
      onError={handleError}
    />
  );
};

export default ProductImage;
