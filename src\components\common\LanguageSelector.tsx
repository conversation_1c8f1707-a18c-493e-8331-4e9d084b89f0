import React, { useState } from 'react';
import { ChevronDown, Globe, Check } from 'lucide-react';
import { useTranslation } from '@/contexts/TranslationContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface LanguageSelectorProps {
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
  showFlag?: boolean;
}

// Mapping des drapeaux pour chaque langue
const languageFlags: Record<string, string> = {
  fr: '🇫🇷',
  en: '🇬🇧',
  sw: '🇹🇿', // Tanzanie pour le Kiswahili
  rn: '🇧🇮', // Burundi pour le Kirundi
};

// Noms natifs des langues
const nativeNames: Record<string, string> = {
  fr: 'Français',
  en: 'English',
  sw: 'Kiswahili',
  rn: 'Kirundi',
};

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'default',
  className,
  showFlag = true,
}) => {
  const { currentLanguage, availableLanguages, changeLanguage, isLoading, t } = useTranslation();
  const [isChanging, setIsChanging] = useState(false);

  const handleLanguageChange = async (languageCode: string) => {
    if (languageCode === currentLanguage || isChanging) return;

    setIsChanging(true);
    try {
      await changeLanguage(languageCode);
    } catch (error) {
      console.error('Erreur lors du changement de langue:', error);
    } finally {
      setIsChanging(false);
    }
  };

  const getCurrentLanguageDisplay = () => {
    const currentLang = availableLanguages.find(lang => lang.code === currentLanguage);
    const flag = showFlag ? languageFlags[currentLanguage] : '';
    const name = nativeNames[currentLanguage] || currentLang?.name || currentLanguage.toUpperCase();
    
    switch (variant) {
      case 'icon-only':
        return flag || <Globe className="h-4 w-4" />;
      case 'compact':
        return (
          <span className="flex items-center gap-1">
            {flag && <span>{flag}</span>}
            <span className="text-sm font-medium">{currentLanguage.toUpperCase()}</span>
          </span>
        );
      default:
        return (
          <span className="flex items-center gap-2">
            {flag && <span>{flag}</span>}
            <span>{name}</span>
          </span>
        );
    }
  };

  if (availableLanguages.length <= 1) {
    return null; // Ne pas afficher le sélecteur s'il n'y a qu'une langue
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={variant === 'compact' ? 'sm' : 'default'}
          className={cn(
            'flex items-center gap-2',
            variant === 'icon-only' && 'px-2',
            isChanging && 'opacity-50 cursor-not-allowed',
            className
          )}
          disabled={isLoading || isChanging}
        >
          {getCurrentLanguageDisplay()}
          {variant !== 'icon-only' && (
            <ChevronDown className="h-4 w-4 opacity-50" />
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-48">
        {availableLanguages.map((language) => {
          const isSelected = language.code === currentLanguage;
          const flag = languageFlags[language.code];
          const nativeName = nativeNames[language.code] || language.name;
          
          return (
            <DropdownMenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={cn(
                'flex items-center justify-between cursor-pointer',
                isSelected && 'bg-accent',
                isChanging && 'opacity-50 cursor-not-allowed'
              )}
              disabled={isChanging}
            >
              <div className="flex items-center gap-2">
                {flag && <span>{flag}</span>}
                <div className="flex flex-col">
                  <span className="font-medium">{nativeName}</span>
                  {language.name !== nativeName && (
                    <span className="text-xs text-muted-foreground">{language.name}</span>
                  )}
                </div>
              </div>
              
              {isSelected && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          );
        })}
        
        {/* Séparateur et lien vers les paramètres */}
        <DropdownMenuItem className="border-t mt-1 pt-1">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Globe className="h-3 w-3" />
            <span>{t('language.manage_preferences', 'Gérer les préférences')}</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Composant de statut de traduction (pour le développement)
export const TranslationStatus: React.FC = () => {
  const { currentLanguage, isLoading, error, translations } = useTranslation();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white text-xs p-2 rounded z-50">
      <div>Langue: {currentLanguage}</div>
      <div>Traductions: {Object.keys(translations).length}</div>
      <div>Statut: {isLoading ? 'Chargement...' : error ? 'Erreur' : 'OK'}</div>
      {error && <div className="text-red-300">Erreur: {error}</div>}
    </div>
  );
};

// Hook pour les traductions avec formatage
export const useFormattedTranslation = () => {
  const { t } = useTranslation();

  const formatCurrency = (amount: number, currency = 'BIF') => {
    return new Intl.NumberFormat(undefined, {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(undefined, options).format(dateObj);
  };

  const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(undefined, options).format(number);
  };

  const pluralize = (count: number, singular: string, plural?: string) => {
    if (count === 1) {
      return t(singular, singular);
    }
    return t(plural || `${singular}_plural`, plural || `${singular}s`);
  };

  return {
    t,
    formatCurrency,
    formatDate,
    formatNumber,
    pluralize,
  };
};
