#!/usr/bin/env python
"""
Test simple pour vérifier l'API d'administration des utilisateurs.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


def test_admin_endpoints():
    """Test simple des endpoints d'administration."""
    print("🔍 TEST SIMPLE DE L'API D'ADMINISTRATION")
    print("=" * 60)
    
    # Créer un utilisateur admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Admin',
            'last_name': 'ApiTest',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
    
    print(f"✅ Admin: {admin_user.email}")
    
    # Créer un utilisateur de test
    test_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'User',
            'last_name': 'ApiTest',
            'is_active': True
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
    
    print(f"✅ Utilisateur de test: {test_user.email} (ID: {test_user.id})")
    
    # Créer un token JWT
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    
    # Configurer le client API
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"\n🔄 Test 1: Liste des utilisateurs")
    response = client.get('/api/v1/auth/admin/users/')
    print(f"   URL: /api/v1/auth/admin/users/")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        users_count = len(data.get('results', []))
        print(f"   ✅ Succès - {users_count} utilisateurs trouvés")
    else:
        print(f"   ❌ Erreur: {response.content.decode()}")
        return False
    
    print(f"\n🔄 Test 2: Détail de l'utilisateur {test_user.id}")
    response = client.get(f'/api/v1/auth/admin/users/{test_user.id}/')
    print(f"   URL: /api/v1/auth/admin/users/{test_user.id}/")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Succès - Utilisateur: {data.get('email')}")
        print(f"   📊 Actif: {data.get('is_active')}")
    else:
        print(f"   ❌ Erreur: {response.content.decode()}")
        return False
    
    print(f"\n🔄 Test 3: Désactivation de l'utilisateur {test_user.id}")
    response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/set_active/', {
        'is_active': False
    }, format='json')
    print(f"   URL: /api/v1/auth/admin/users/{test_user.id}/set_active/")
    print(f"   Data: {{'is_active': False}}")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Succès - Utilisateur désactivé")
        print(f"   📊 Actif: {data.get('is_active')}")
        
        # Vérifier en base de données
        test_user.refresh_from_db()
        print(f"   📊 Actif en DB: {test_user.is_active}")
        
    elif response.status_code == 404:
        print(f"   ❌ Erreur 404: Endpoint non trouvé")
        print(f"   🔍 Vérifiez que l'URL est correcte")
        return False
    else:
        print(f"   ❌ Erreur {response.status_code}: {response.content.decode()}")
        return False
    
    print(f"\n🔄 Test 4: Réactivation de l'utilisateur {test_user.id}")
    response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/set_active/', {
        'is_active': True
    }, format='json')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Succès - Utilisateur réactivé")
        print(f"   📊 Actif: {data.get('is_active')}")
    else:
        print(f"   ❌ Erreur: {response.content.decode()}")
    
    print(f"\n🔄 Test 5: Vérification de l'utilisateur {test_user.id}")
    response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/verify/')
    print(f"   URL: /api/v1/auth/admin/users/{test_user.id}/verify/")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Succès - Utilisateur vérifié")
        print(f"   📊 Vérifié: {data.get('is_verified')}")
    else:
        print(f"   ❌ Erreur: {response.content.decode()}")
    
    return True


def check_available_endpoints():
    """Vérifier les endpoints disponibles."""
    print(f"\n📋 ENDPOINTS DISPONIBLES")
    print("=" * 60)
    
    from django.conf import settings
    from django.urls import get_resolver
    
    resolver = get_resolver()
    auth_patterns = []
    
    def extract_patterns(patterns, prefix=''):
        for pattern in patterns:
            if hasattr(pattern, 'url_patterns'):
                # C'est un include, récurser
                new_prefix = prefix + str(pattern.pattern)
                extract_patterns(pattern.url_patterns, new_prefix)
            else:
                # C'est un pattern final
                full_pattern = prefix + str(pattern.pattern)
                if 'auth' in full_pattern or 'admin' in full_pattern:
                    auth_patterns.append(full_pattern)
    
    extract_patterns(resolver.url_patterns)
    
    print("URLs d'authentification trouvées:")
    for pattern in sorted(auth_patterns):
        print(f"   - {pattern}")
    
    return True


def main():
    """Fonction principale."""
    print("🚀 TEST SIMPLE DE L'API D'ADMINISTRATION")
    print("=" * 80)
    
    try:
        # Vérifier les endpoints disponibles
        check_available_endpoints()
        
        # Tester les endpoints
        success = test_admin_endpoints()
        
        print(f"\n" + "=" * 80)
        print("📊 RÉSUMÉ")
        print("=" * 80)
        
        if success:
            print("✅ TOUS LES TESTS SONT PASSÉS")
            print("✅ L'API d'administration fonctionne correctement")
            print("✅ Le problème 404 vient probablement du frontend")
            print()
            print("🔧 SOLUTIONS POSSIBLES:")
            print("   1. Vérifiez que le serveur Django fonctionne sur le port 8000")
            print("   2. Vérifiez l'URL de base dans le frontend (config.ts)")
            print("   3. Vérifiez les CORS si le frontend est sur un autre port")
        else:
            print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
            print("❌ Il y a un problème avec l'API d'administration")
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
