#!/usr/bin/env python
"""
Test d'envoi d'un email réel à <EMAIL>
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings


def test_simple_email():
    """Test d'envoi d'un email simple."""
    print("📧 TEST D'ENVOI D'EMAIL RÉEL")
    print("=" * 50)
    
    recipient_email = "<EMAIL>"
    
    print(f"📧 Configuration email actuelle:")
    print(f"   Backend: {settings.EMAIL_BACKEND}")
    print(f"   Host: {settings.EMAIL_HOST}")
    print(f"   Port: {settings.EMAIL_PORT}")
    print(f"   TLS: {settings.EMAIL_USE_TLS}")
    print(f"   User: {settings.EMAIL_HOST_USER}")
    print(f"   Password configuré: {'✅' if settings.EMAIL_HOST_PASSWORD else '❌'}")
    
    if settings.EMAIL_BACKEND == 'django.core.mail.backends.console.EmailBackend':
        print("\n⚠️  ATTENTION: Backend console activé")
        print("   Les emails seront affichés dans la console, pas envoyés réellement")
        print("   Pour envoyer de vrais emails, modifiez settings.py")
        print()
    
    try:
        print(f"\n📤 Envoi d'un email de test à {recipient_email}...")
        
        subject = "🎉 Test JosNet - Email automatique fonctionne !"
        message = f"""
Bonjour Alain,

Ceci est un email de test pour confirmer que le système de newsletter automatique de JosNet fonctionne parfaitement !

✅ Les emails de promotions sont envoyés automatiquement
✅ Les emails de nouveaux produits sont envoyés automatiquement  
✅ Le filtrage selon vos préférences fonctionne
✅ Votre adresse {recipient_email} est bien configurée

Vous recevrez désormais automatiquement :
🎉 Les nouvelles promotions (si activé dans vos préférences)
✨ Les nouveaux produits (si activé dans vos préférences)
📰 Les newsletters (si activé dans vos préférences)

Pour gérer vos préférences, connectez-vous à votre compte JosNet.

Cordialement,
L'équipe JosNet

---
Cet email a été envoyé automatiquement par le système JosNet.
"""
        
        from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
        
        result = send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[recipient_email],
            fail_silently=False,
        )
        
        if result == 1:
            print(f"✅ Email envoyé avec succès à {recipient_email} !")
            if settings.EMAIL_BACKEND == 'django.core.mail.backends.smtp.EmailBackend':
                print(f"📬 Vérifiez votre boîte email {recipient_email}")
            else:
                print(f"📺 Email affiché dans la console (backend: {settings.EMAIL_BACKEND})")
        else:
            print(f"❌ Échec de l'envoi de l'email")
            
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi: {e}")
        
        if "authentication" in str(e).lower():
            print("\n🔐 ERREUR D'AUTHENTIFICATION:")
            print("   1. Vérifiez que l'authentification à 2 facteurs est activée")
            print("   2. Générez un nouveau mot de passe d'application Gmail")
            print("   3. Utilisez ce mot de passe dans EMAIL_HOST_PASSWORD")
            
        elif "connection" in str(e).lower():
            print("\n🌐 ERREUR DE CONNEXION:")
            print("   1. Vérifiez votre connexion internet")
            print("   2. Vérifiez les paramètres SMTP (host, port, TLS)")
            
        print(f"\n📋 Configuration actuelle:")
        print(f"   EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
        print(f"   EMAIL_HOST: {settings.EMAIL_HOST}")
        print(f"   EMAIL_PORT: {settings.EMAIL_PORT}")


def show_configuration_instructions():
    """Afficher les instructions de configuration."""
    print("\n" + "=" * 60)
    print("📋 INSTRUCTIONS POUR CONFIGURER L'ENVOI RÉEL")
    print("=" * 60)
    print()
    print("1. 🔐 CONFIGURATION GMAIL:")
    print("   a) Allez sur https://myaccount.google.com/security")
    print("   b) Activez l'authentification à 2 facteurs")
    print("   c) Cherchez 'Mots de passe des applications'")
    print("   d) Générez un mot de passe pour 'JosNet Newsletter'")
    print("   e) Copiez le mot de passe de 16 caractères")
    print()
    print("2. ⚙️  MODIFICATION DU FICHIER settings.py:")
    print("   Dans josnet_backend/josnet_backend/settings.py, décommentez et modifiez:")
    print()
    print("   EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'")
    print("   EMAIL_HOST = 'smtp.gmail.com'")
    print("   EMAIL_PORT = 587")
    print("   EMAIL_USE_TLS = True")
    print("   EMAIL_HOST_USER = '<EMAIL>'")
    print("   EMAIL_HOST_PASSWORD = 'VOTRE-MOT-DE-PASSE-APP'")
    print()
    print("3. 🔄 REDÉMARRAGE:")
    print("   - Redémarrez le serveur Django")
    print("   - Relancez ce test")
    print()
    print("4. ✅ VÉRIFICATION:")
    print("   - Vérifiez votre boî<NAME_EMAIL>")
    print("   - Vous devriez recevoir l'email de test")
    print()


def main():
    """Fonction principale."""
    test_simple_email()
    show_configuration_instructions()


if __name__ == '__main__':
    main()
