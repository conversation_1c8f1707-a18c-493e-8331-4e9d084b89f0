#!/usr/bin/env python3
"""
Script pour diagnostiquer le problème des articles de commande dans les retours
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_order_apis():
    """Tester les différentes APIs de commandes"""
    print("🔍 DIAGNOSTIC DU PROBLÈME DES ARTICLES DE COMMANDE")
    print("=" * 70)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data, timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            print("✅ Authentification réussie")
            
            # 1. Tester l'API getUserOrders (utilisée par le frontend)
            print(f"\n📋 1. TEST API getUserOrders (/orders/orders/):")
            orders_response = requests.get(f"{API_BASE_URL}/orders/orders/", headers=headers, timeout=10)
            
            print(f"   📊 Statut: {orders_response.status_code}")
            
            if orders_response.status_code == 200:
                orders_data = orders_response.json()
                print(f"   ✅ Données récupérées")
                
                # Analyser la structure
                if isinstance(orders_data, dict) and 'results' in orders_data:
                    orders = orders_data['results']
                    print(f"   📦 Commandes trouvées: {len(orders)}")
                elif isinstance(orders_data, list):
                    orders = orders_data
                    print(f"   📦 Commandes trouvées: {len(orders)}")
                else:
                    orders = []
                    print(f"   ⚠️ Structure inattendue: {type(orders_data)}")
                
                if orders:
                    first_order = orders[0]
                    order_id = first_order.get('id')
                    order_number = first_order.get('order_number', 'N/A')
                    
                    print(f"\n   📋 Première commande:")
                    print(f"      🆔 ID: {order_id}")
                    print(f"      📝 Numéro: {order_number}")
                    print(f"      📊 Clés disponibles: {list(first_order.keys())}")
                    
                    # Vérifier les articles
                    items_found = False
                    items_count = 0
                    
                    for key in ['items', 'order_items', 'line_items', 'products']:
                        if key in first_order and isinstance(first_order[key], list):
                            items_count = len(first_order[key])
                            print(f"      📦 Articles dans '{key}': {items_count}")
                            items_found = True
                            
                            if items_count > 0:
                                item = first_order[key][0]
                                print(f"         Premier article: {list(item.keys())}")
                    
                    if not items_found:
                        print(f"      ❌ Aucun article trouvé dans cette commande")
                    
                    # 2. Tester l'API getOrder avec l'ID de cette commande
                    print(f"\n📋 2. TEST API getOrder (/orders/orders/{order_id}/):")
                    order_detail_response = requests.get(f"{API_BASE_URL}/orders/orders/{order_id}/", headers=headers, timeout=10)
                    
                    print(f"   📊 Statut: {order_detail_response.status_code}")
                    
                    if order_detail_response.status_code == 200:
                        order_detail = order_detail_response.json()
                        print(f"   ✅ Détails récupérés")
                        print(f"   📊 Clés disponibles: {list(order_detail.keys())}")
                        
                        # Vérifier les articles dans les détails
                        detail_items_found = False
                        detail_items_count = 0
                        
                        for key in ['items', 'order_items', 'line_items', 'products']:
                            if key in order_detail and isinstance(order_detail[key], list):
                                detail_items_count = len(order_detail[key])
                                print(f"   📦 Articles dans '{key}': {detail_items_count}")
                                detail_items_found = True
                                
                                if detail_items_count > 0:
                                    item = order_detail[key][0]
                                    print(f"      Premier article: {list(item.keys())}")
                                    print(f"      Nom: {item.get('product_name', item.get('name', 'N/A'))}")
                                    print(f"      Quantité: {item.get('quantity', 'N/A')}")
                                    print(f"      Prix: {item.get('price', 'N/A')}")
                        
                        if not detail_items_found:
                            print(f"   ❌ Aucun article trouvé dans les détails")
                        
                        # Comparaison
                        print(f"\n🔍 COMPARAISON:")
                        print(f"   Liste commandes - Articles: {items_count}")
                        print(f"   Détails commande - Articles: {detail_items_count}")
                        
                        if items_count != detail_items_count:
                            print(f"   ⚠️ INCOHÉRENCE DÉTECTÉE!")
                            print(f"   Le nombre d'articles diffère entre la liste et les détails")
                        else:
                            print(f"   ✅ Cohérence confirmée")
                        
                        return {
                            'list_items': items_count,
                            'detail_items': detail_items_count,
                            'order_id': order_id,
                            'consistent': items_count == detail_items_count
                        }
                    else:
                        print(f"   ❌ Erreur détails: {order_detail_response.status_code}")
                        print(f"   📝 Réponse: {order_detail_response.text[:200]}...")
                        return None
                else:
                    print(f"   ⚠️ Aucune commande trouvée")
                    return None
            else:
                print(f"   ❌ Erreur liste: {orders_response.status_code}")
                print(f"   📝 Réponse: {orders_response.text[:200]}...")
                return None
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ SERVEUR NON ACCESSIBLE")
        return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def suggest_solutions():
    """Suggérer des solutions au problème"""
    print(f"\n💡 SOLUTIONS POSSIBLES:")
    print("=" * 50)
    
    print(f"🔧 SOLUTION 1: Utiliser directement les articles de la liste")
    print(f"   • Modifier CreateReturnForm pour ne pas refaire un appel API")
    print(f"   • Utiliser les articles déjà présents dans getUserOrders()")
    print(f"   • Éviter la double récupération")
    
    print(f"\n🔧 SOLUTION 2: Corriger l'API getOrder")
    print(f"   • S'assurer que l'API retourne bien les articles")
    print(f"   • Vérifier les sérializers backend")
    print(f"   • Tester avec des données réelles")
    
    print(f"\n🔧 SOLUTION 3: Utiliser une API spécifique")
    print(f"   • Créer un endpoint dédié pour les retours")
    print(f"   • Optimiser pour récupérer commande + articles")
    print(f"   • Éviter les appels multiples")

def main():
    print("🧪 DIAGNOSTIC: PROBLÈME DES ARTICLES DE COMMANDE")
    print("=" * 70)
    
    # 1. Tester les APIs
    result = test_order_apis()
    
    # 2. Suggérer des solutions
    suggest_solutions()
    
    print(f"\n📊 RÉSUMÉ:")
    if result:
        print(f"   Commande testée: {result['order_id']}")
        print(f"   Articles (liste): {result['list_items']}")
        print(f"   Articles (détails): {result['detail_items']}")
        print(f"   Cohérence: {'✅' if result['consistent'] else '❌'}")
        
        if result['consistent'] and result['detail_items'] > 0:
            print(f"\n🎉 APIS FONCTIONNELLES!")
            print(f"   Le problème vient probablement du frontend")
            print(f"   Vérifiez la logique de CreateReturnForm")
        elif result['list_items'] > 0 and result['detail_items'] == 0:
            print(f"\n⚠️ PROBLÈME IDENTIFIÉ!")
            print(f"   L'API de détails ne retourne pas les articles")
            print(f"   Solution: Utiliser les articles de la liste")
        else:
            print(f"\n❌ PROBLÈME PLUS PROFOND")
            print(f"   Aucune API ne retourne d'articles")
            print(f"   Vérifiez les données en base")
    else:
        print(f"   ❌ Impossible de tester les APIs")
        print(f"   Vérifiez que le serveur Django fonctionne")

if __name__ == "__main__":
    main()
