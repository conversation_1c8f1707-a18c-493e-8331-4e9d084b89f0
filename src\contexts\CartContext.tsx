
import React, { createContext, useState, useContext, useEffect } from "react";
import homeApi, { FeaturedProduct } from "@/services/homeApi";
import promotionApi from "@/services/promotionApi";
import { toast } from "@/hooks/use-toast";
import { useTranslation } from "@/contexts/TranslationContext";

export interface CartItem {
  product: FeaturedProduct;
  quantity: number;
}

interface CartContextType {
  cartItems: CartItem[];
  addToCart: (productId: number, quantity?: number) => Promise<void>;
  removeFromCart: (productId: number) => void;
  updateQuantity: (productId: number, quantity: number) => void;
  clearCart: () => void;
  cartCount: number;
  cartTotal: number;
  applyPromoCode: (code: string) => Promise<boolean>;
  promoApplied: boolean;
  promoCode: string | null; // ✅ AJOUT: Code promo appliqué
  promoDiscount: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider = ({ children }: { children: React.ReactNode }) => {
  const { t } = useTranslation();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [promoApplied, setPromoApplied] = useState(false);
  const [promoCode, setPromoCode] = useState<string | null>(null); // ✅ AJOUT: Code promo
  const [promoDiscount, setPromoDiscount] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load cart from localStorage on initialization
  useEffect(() => {
    const loadCartFromStorage = async () => {
      const savedCart = localStorage.getItem("cart");
      if (savedCart) {
        try {
          const parsedCart = JSON.parse(savedCart);

          // For now, use stored product data without API validation to avoid loops
          const validCartItems: CartItem[] = [];

          for (const item of parsedCart) {
            // Check if item has required structure
            if (item.product && item.product.id && item.quantity && item.product.name && item.product.price) {
              validCartItems.push({
                product: item.product,
                quantity: item.quantity
              });
            }
          }

          setCartItems(validCartItems);

        } catch (e) {
          console.error("Error parsing cart data:", e);
          // Clear invalid cart data
          localStorage.removeItem("cart");
        }
      }

      // Mark as initialized
      setIsInitialized(true);
    };

    loadCartFromStorage();
  }, []);

  // Save cart to localStorage whenever it changes (but not during initialization)
  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem("cart", JSON.stringify(cartItems));
    }
  }, [cartItems, isInitialized]);

  const addToCart = async (productId: number, quantity = 1) => {
    try {
      // Check if product is already in cart
      const existingItem = cartItems.find((item) => item.product.id === productId);

      if (existingItem) {
        // Update quantity if product already in cart
        setCartItems((prev) =>
          prev.map((item) =>
            item.product.id === productId
              ? { ...item, quantity: item.quantity + quantity }
              : item
          )
        );

        toast({
          title: t("cart.product_added", "Produit ajouté"),
          description: `${existingItem.product.name} (${t("cart.quantity_updated", "quantité mise à jour")})`,
        });
        return;
      }

      // Fetch product from API
      const product = await homeApi.getProductById(productId);

      if (!product) {
        toast({
          title: t("common.error", "Erreur"),
          description: t("cart.product_not_found", "Produit non trouvé"),
          variant: "destructive",
        });
        return;
      }

      if (!product.in_stock) {
        toast({
          title: t("cart.product_unavailable", "Produit indisponible"),
          description: t("cart.out_of_stock", "Ce produit n'est actuellement pas en stock"),
          variant: "destructive",
        });
        return;
      }

      // Add new product to cart
      setCartItems((prev) => [...prev, { product, quantity }]);

      toast({
        title: t("cart.added_to_cart", "Produit ajouté au panier"),
        description: `${product.name} ${t("cart.added_to_your_cart", "a été ajouté à votre panier")}`,
      });

    } catch (error) {
      console.error('Error adding product to cart:', error);
      toast({
        title: "Erreur",
        description: "Impossible d'ajouter le produit au panier",
        variant: "destructive",
      });
    }
  };

  const removeFromCart = (productId: number) => {
    setCartItems((prev) => prev.filter((item) => item.product.id !== productId));
  };

  const updateQuantity = (productId: number, quantity: number) => {
    if (quantity < 1) return;

    setCartItems((prev) =>
      prev.map((item) =>
        item.product.id === productId
          ? { ...item, quantity }
          : item
      )
    );
  };

  const clearCart = () => {
    setCartItems([]);
    setPromoApplied(false);
    setPromoCode(null); // ✅ AJOUT: Réinitialiser le code promo
    setPromoDiscount(0);
  };

  const cartCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

  const cartTotal = cartItems.reduce(
    (sum, item) => sum + item.product.price * item.quantity,
    0
  );

  const applyPromoCode = async (code: string): Promise<boolean> => {
    try {
      // Utiliser l'API de validation de code promo
      const response = await promotionApi.validatePromoCode(code, cartTotal);

      if (response.valid) {
        // Calculer le pourcentage de réduction
        const discountPercentage = response.discount_amount / cartTotal;
        setPromoApplied(true);
        setPromoCode(code); // ✅ AJOUT: Stocker le code promo
        setPromoDiscount(discountPercentage);
        return true;
      } else {
        console.log(t("cart.invalid_promo_code", "Code promo invalide") + ':', response.message);
        return false;
      }
    } catch (error) {
      console.error(t("cart.promo_validation_error", "Erreur lors de la validation du code promo") + ':', error);
      return false;
    }
  };

  return (
    <CartContext.Provider
      value={{
        cartItems,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        cartCount,
        cartTotal,
        applyPromoCode,
        promoApplied,
        promoCode, // ✅ AJOUT: Exposer le code promo
        promoDiscount
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
};
