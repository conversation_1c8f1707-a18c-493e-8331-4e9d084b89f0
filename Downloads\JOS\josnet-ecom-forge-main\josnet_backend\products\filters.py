import django_filters
from django.db.models import Q, F
from .models import Product, Category

class ProductFilter(django_filters.FilterSet):
    """
    Filter for products with various options.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    min_price = django_filters.NumberFilter(field_name='price', lookup_expr='gte')
    max_price = django_filters.NumberFilter(field_name='price', lookup_expr='lte')
    category = django_filters.ModelMultipleChoiceFilter(
        field_name='categories',
        queryset=Category.objects.all(),
        to_field_name='slug'
    )
    status = django_filters.ChoiceFilter(choices=Product.STATUS_CHOICES)
    is_featured = django_filters.BooleanFilter()
    is_digital = django_filters.BooleanFilter()
    in_stock = django_filters.BooleanFilter(method='filter_in_stock')
    on_sale = django_filters.BooleanFilter(method='filter_on_sale')

    class Meta:
        model = Product
        fields = [
            'name', 'min_price', 'max_price', 'category',
            'status', 'is_featured', 'is_digital', 'in_stock', 'on_sale'
        ]

    def filter_in_stock(self, queryset, name, value):
        """
        Filter products by stock availability.
        """
        if value:
            # Products with inventory and quantity > 0
            return queryset.filter(
                Q(inventory__quantity__gt=0) |
                Q(variants__inventory__quantity__gt=0)
            ).distinct()
        else:
            # Products with no inventory or quantity = 0
            return queryset.filter(
                Q(inventory__isnull=True) |
                Q(inventory__quantity=0)
            ).distinct()

    def filter_on_sale(self, queryset, name, value):
        """
        Filter products that are on sale.
        """
        if value:
            # Products with sale_price < price
            return queryset.filter(
                sale_price__isnull=False,
                sale_price__lt=F('price')
            )
        else:
            # Products not on sale
            return queryset.filter(
                Q(sale_price__isnull=True) |
                Q(sale_price__gte=F('price'))
            )
