# Generated by Django 4.2.23 on 2025-07-06 07:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewsletterSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('unsubscribe_token', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('subscribed_at', models.DateTimeField(auto_now_add=True)),
                ('unsubscribed_at', models.DateTimeField(blank=True, null=True)),
                ('preferences_promotions', models.<PERSON>olean<PERSON>ield(default=True)),
                ('preferences_new_products', models.<PERSON>oleanField(default=True)),
                ('preferences_newsletters', models.BooleanField(default=True)),
                ('preferences_order_updates', models.<PERSON>oleanField(default=True)),
                ('source', models.CharField(default='website', max_length=50)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='newsletter_subscription', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Abonnement Newsletter',
                'verbose_name_plural': 'Abonnements Newsletter',
                'ordering': ['-subscribed_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsletterCampaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('subject', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('campaign_type', models.CharField(choices=[('promotion', 'Promotion'), ('newsletter', 'Newsletter'), ('announcement', 'Annonce'), ('new_product', 'Nouveau Produit')], max_length=20)),
                ('status', models.CharField(choices=[('draft', 'Brouillon'), ('scheduled', 'Programmée'), ('sending', "En cours d'envoi"), ('sent', 'Envoyée'), ('failed', 'Échec')], default='draft', max_length=20)),
                ('target_preferences', models.JSONField(default=list, help_text='Liste des préférences ciblées')),
                ('target_all', models.BooleanField(default=False, help_text='Envoyer à tous les abonnés actifs')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('total_recipients', models.PositiveIntegerField(default=0)),
                ('sent_count', models.PositiveIntegerField(default=0)),
                ('failed_count', models.PositiveIntegerField(default=0)),
                ('open_count', models.PositiveIntegerField(default=0)),
                ('click_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_campaigns', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Campagne Newsletter',
                'verbose_name_plural': 'Campagnes Newsletter',
                'ordering': ['-created_at'],
            },
        ),
    ]
