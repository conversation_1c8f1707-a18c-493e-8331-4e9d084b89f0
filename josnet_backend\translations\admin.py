from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.http import HttpResponseRedirect
from .models import TranslationKey, Translation, TranslationCache, UserLanguagePreference


class TranslationInline(admin.TabularInline):
    model = Translation
    extra = 0
    fields = ['language_code', 'value', 'is_approved']
    readonly_fields = []

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('key')


@admin.register(TranslationKey)
class TranslationKeyAdmin(admin.ModelAdmin):
    list_display = ['key', 'category', 'description', 'translation_status', 'is_active', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['key', 'description', 'category']
    ordering = ['category', 'key']
    inlines = [TranslationInline]
    
    fieldsets = (
        (_('Informations de base'), {
            'fields': ('key', 'description', 'category', 'is_active')
        }),
        (_('Métadonnées'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']

    def translation_status(self, obj):
        """Afficher le statut des traductions"""
        translations = obj.translations.all()
        total_languages = 4  # fr, en, sw, rn
        completed = translations.filter(is_approved=True).count()
        
        if completed == total_languages:
            color = 'green'
            status = _('Complet')
        elif completed > 0:
            color = 'orange'
            status = f'{completed}/{total_languages}'
        else:
            color = 'red'
            status = _('Aucune')
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, status
        )
    translation_status.short_description = _('Statut des traductions')

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('translations')

    actions = ['create_missing_translations', 'approve_all_translations']

    def create_missing_translations(self, request, queryset):
        """Créer les traductions manquantes avec la valeur de la clé"""
        languages = ['fr', 'en', 'sw', 'rn']
        created_count = 0
        
        for key_obj in queryset:
            for lang in languages:
                translation, created = Translation.objects.get_or_create(
                    key=key_obj,
                    language_code=lang,
                    defaults={'value': key_obj.key, 'is_approved': False}
                )
                if created:
                    created_count += 1
        
        self.message_user(request, f'{created_count} traductions créées.')
    create_missing_translations.short_description = _('Créer les traductions manquantes')

    def approve_all_translations(self, request, queryset):
        """Approuver toutes les traductions"""
        updated = 0
        for key_obj in queryset:
            updated += key_obj.translations.update(is_approved=True)
        
        self.message_user(request, f'{updated} traductions approuvées.')
    approve_all_translations.short_description = _('Approuver toutes les traductions')


@admin.register(Translation)
class TranslationAdmin(admin.ModelAdmin):
    list_display = ['key', 'language_code', 'value_preview', 'is_approved', 'updated_at']
    list_filter = ['language_code', 'is_approved', 'key__category']
    search_fields = ['key__key', 'value', 'key__description']
    ordering = ['key__category', 'key__key', 'language_code']
    
    fieldsets = (
        (_('Traduction'), {
            'fields': ('key', 'language_code', 'value', 'is_approved')
        }),
        (_('Métadonnées'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']

    def value_preview(self, obj):
        """Aperçu de la valeur de traduction"""
        return obj.value[:100] + '...' if len(obj.value) > 100 else obj.value
    value_preview.short_description = _('Aperçu')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('key')

    actions = ['approve_translations', 'unapprove_translations']

    def approve_translations(self, request, queryset):
        """Approuver les traductions sélectionnées"""
        updated = queryset.update(is_approved=True)
        self.message_user(request, f'{updated} traductions approuvées.')
    approve_translations.short_description = _('Approuver les traductions')

    def unapprove_translations(self, request, queryset):
        """Désapprouver les traductions sélectionnées"""
        updated = queryset.update(is_approved=False)
        self.message_user(request, f'{updated} traductions désapprouvées.')
    unapprove_translations.short_description = _('Désapprouver les traductions')


@admin.register(TranslationCache)
class TranslationCacheAdmin(admin.ModelAdmin):
    list_display = ['language_code', 'last_updated', 'translations_count']
    readonly_fields = ['last_updated', 'translations_preview']
    
    fieldsets = (
        (_('Cache'), {
            'fields': ('language_code', 'last_updated')
        }),
        (_('Traductions'), {
            'fields': ('translations_preview',),
            'classes': ('collapse',)
        }),
    )

    def translations_count(self, obj):
        """Compter le nombre de traductions dans le cache"""
        translations = obj.get_translations()
        return len(translations)
    translations_count.short_description = _('Nombre de traductions')

    def translations_preview(self, obj):
        """Aperçu des traductions en cache"""
        translations = obj.get_translations()
        preview = []
        for key, value in list(translations.items())[:10]:
            preview.append(f"{key}: {value}")
        
        result = '\n'.join(preview)
        if len(translations) > 10:
            result += f'\n... et {len(translations) - 10} autres'
        
        return format_html('<pre>{}</pre>', result)
    translations_preview.short_description = _('Aperçu des traductions')

    actions = ['refresh_cache']

    def refresh_cache(self, request, queryset):
        """Actualiser le cache des traductions"""
        from .utils import refresh_translation_cache
        
        for cache_obj in queryset:
            refresh_translation_cache(cache_obj.language_code)
        
        self.message_user(request, f'Cache actualisé pour {queryset.count()} langues.')
    refresh_cache.short_description = _('Actualiser le cache')


@admin.register(UserLanguagePreference)
class UserLanguagePreferenceAdmin(admin.ModelAdmin):
    list_display = ['user', 'language_code', 'updated_at']
    list_filter = ['language_code', 'updated_at']
    search_fields = ['user__email', 'user__first_name', 'user__last_name']
    ordering = ['-updated_at']
    
    fieldsets = (
        (_('Préférence'), {
            'fields': ('user', 'language_code')
        }),
        (_('Métadonnées'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
