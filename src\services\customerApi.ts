import axios from 'axios';
import { API_BASE_URL } from '@/config/api';
import { getAuthToken } from '@/utils/auth';

export interface Customer {
  id: number;
  email: string;
  full_name: string;
  first_name: string;
  last_name: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
  total_orders: number;
  total_spent: string;
  last_order_date?: string;
  last_order_number?: string;
  customer_since: string;
  preferred_language?: string;
  notes?: string;
}

export interface CustomerSearchResult {
  id: number;
  email: string;
  full_name: string;
  phone?: string;
  last_order?: string;
  total_orders: number;
  total_spent: string;
  customer_since: string;
  is_active: boolean;
}

export interface CreateCustomerRequest {
  email: string;
  full_name: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  preferred_language?: string;
  notes?: string;
}

export interface CustomerListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Customer[];
}

// Mock data for development
const mockCustomers: Customer[] = [
  {
    id: 1,
    email: "<EMAIL>",
    full_name: "Jean Dupont",
    first_name: "Jean",
    last_name: "Dupont",
    phone: "+257 79 123 456",
    address: "123 Avenue de l'Indépendance",
    city: "Bujumbura",
    country: "Burundi",
    created_at: "2023-01-15T10:30:00Z",
    last_login: "2024-01-20T14:22:00Z",
    is_active: true,
    total_orders: 5,
    total_spent: "1,250,000",
    last_order_date: "2024-01-15",
    last_order_number: "ORD-2024-001",
    customer_since: "2023-01-15",
    preferred_language: "fr",
    notes: "Client VIP - Préfère les livraisons le matin"
  },
  {
    id: 2,
    email: "<EMAIL>",
    full_name: "Marie Martin",
    first_name: "Marie",
    last_name: "Martin",
    phone: "+257 79 987 654",
    address: "456 Rue de la Paix",
    city: "Bujumbura",
    country: "Burundi",
    created_at: "2023-03-20T09:15:00Z",
    last_login: "2024-01-18T16:45:00Z",
    is_active: true,
    total_orders: 3,
    total_spent: "750,000",
    last_order_date: "2024-01-10",
    last_order_number: "ORD-2024-002",
    customer_since: "2023-03-20",
    preferred_language: "fr"
  },
  {
    id: 3,
    email: "<EMAIL>",
    full_name: "Alain Asifiwe",
    first_name: "Alain",
    last_name: "Asifiwe",
    phone: "+257 79 555 123",
    address: "789 Boulevard de l'Unité",
    city: "Gitega",
    country: "Burundi",
    created_at: "2023-06-10T11:20:00Z",
    last_login: "2024-01-19T08:30:00Z",
    is_active: true,
    total_orders: 8,
    total_spent: "2,100,000",
    last_order_date: "2024-01-18",
    last_order_number: "ORD-2024-003",
    customer_since: "2023-06-10",
    preferred_language: "fr",
    notes: "Excellent client - Commandes régulières"
  },
  {
    id: 4,
    email: "<EMAIL>",
    full_name: "Grace Niyonkuru",
    first_name: "Grace",
    last_name: "Niyonkuru",
    phone: "+257 79 444 789",
    address: "321 Avenue de la République",
    city: "Ngozi",
    country: "Burundi",
    created_at: "2023-08-05T14:45:00Z",
    last_login: "2024-01-17T12:15:00Z",
    is_active: true,
    total_orders: 2,
    total_spent: "450,000",
    last_order_date: "2023-12-20",
    last_order_number: "ORD-2023-045",
    customer_since: "2023-08-05",
    preferred_language: "fr"
  },
  {
    id: 5,
    email: "<EMAIL>",
    full_name: "Pierre Ndayisenga",
    first_name: "Pierre",
    last_name: "Ndayisenga",
    phone: "+257 79 333 456",
    address: "654 Rue de l'Espoir",
    city: "Muyinga",
    country: "Burundi",
    created_at: "2023-11-12T16:30:00Z",
    last_login: "2024-01-16T10:20:00Z",
    is_active: true,
    total_orders: 1,
    total_spent: "180,000",
    last_order_date: "2023-11-25",
    last_order_number: "ORD-2023-038",
    customer_since: "2023-11-12",
    preferred_language: "fr"
  }
];

const customerApi = {
  // Search customers
  searchCustomers: async (query: string): Promise<CustomerSearchResult[]> => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('No auth token available, using mock data');
        return mockCustomers
          .filter(customer => 
            customer.email.toLowerCase().includes(query.toLowerCase()) ||
            customer.full_name.toLowerCase().includes(query.toLowerCase()) ||
            (customer.phone && customer.phone.includes(query))
          )
          .map(customer => ({
            id: customer.id,
            email: customer.email,
            full_name: customer.full_name,
            phone: customer.phone,
            last_order: customer.last_order_date,
            total_orders: customer.total_orders,
            total_spent: customer.total_spent,
            customer_since: customer.customer_since,
            is_active: customer.is_active
          }))
          .slice(0, 10); // Limit to 10 results
      }

      const response = await axios.get(`${API_BASE_URL}/customers/search/`, {
        params: { q: query },
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error searching customers:', error);
      
      // Fallback to mock data
      return mockCustomers
        .filter(customer => 
          customer.email.toLowerCase().includes(query.toLowerCase()) ||
          customer.full_name.toLowerCase().includes(query.toLowerCase()) ||
          (customer.phone && customer.phone.includes(query))
        )
        .map(customer => ({
          id: customer.id,
          email: customer.email,
          full_name: customer.full_name,
          phone: customer.phone,
          last_order: customer.last_order_date,
          total_orders: customer.total_orders,
          total_spent: customer.total_spent,
          customer_since: customer.customer_since,
          is_active: customer.is_active
        }))
        .slice(0, 10);
    }
  },

  // Get customer by ID
  getCustomer: async (id: number): Promise<Customer> => {
    try {
      const token = getAuthToken();
      if (!token) {
        const mockCustomer = mockCustomers.find(c => c.id === id);
        if (!mockCustomer) {
          throw new Error('Customer not found');
        }
        return mockCustomer;
      }

      const response = await axios.get(`${API_BASE_URL}/customers/${id}/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error fetching customer ${id}:`, error);
      
      // Fallback to mock data
      const mockCustomer = mockCustomers.find(c => c.id === id);
      if (!mockCustomer) {
        throw new Error('Customer not found');
      }
      return mockCustomer;
    }
  },

  // Create new customer
  createCustomer: async (data: CreateCustomerRequest): Promise<Customer> => {
    try {
      const token = getAuthToken();
      if (!token) {
        // Create mock customer
        const newCustomer: Customer = {
          id: Math.max(...mockCustomers.map(c => c.id)) + 1,
          email: data.email,
          full_name: data.full_name,
          first_name: data.first_name || data.full_name.split(' ')[0],
          last_name: data.last_name || data.full_name.split(' ').slice(1).join(' '),
          phone: data.phone,
          address: data.address,
          city: data.city,
          country: data.country || 'Burundi',
          created_at: new Date().toISOString(),
          is_active: true,
          total_orders: 0,
          total_spent: "0",
          customer_since: new Date().toISOString().split('T')[0],
          preferred_language: data.preferred_language || 'fr',
          notes: data.notes
        };
        
        mockCustomers.push(newCustomer);
        return newCustomer;
      }

      const response = await axios.post(`${API_BASE_URL}/customers/`, data, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  },

  // Get all customers with pagination
  getCustomers: async (page: number = 1, search?: string): Promise<CustomerListResponse> => {
    try {
      const token = getAuthToken();
      if (!token) {
        let filteredCustomers = mockCustomers;
        
        if (search) {
          filteredCustomers = mockCustomers.filter(customer => 
            customer.email.toLowerCase().includes(search.toLowerCase()) ||
            customer.full_name.toLowerCase().includes(search.toLowerCase())
          );
        }
        
        return {
          count: filteredCustomers.length,
          next: null,
          previous: null,
          results: filteredCustomers
        };
      }

      const response = await axios.get(`${API_BASE_URL}/customers/`, {
        params: { page, search },
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching customers:', error);
      
      // Fallback to mock data
      let filteredCustomers = mockCustomers;
      
      if (search) {
        filteredCustomers = mockCustomers.filter(customer => 
          customer.email.toLowerCase().includes(search.toLowerCase()) ||
          customer.full_name.toLowerCase().includes(search.toLowerCase())
        );
      }
      
      return {
        count: filteredCustomers.length,
        next: null,
        previous: null,
        results: filteredCustomers
      };
    }
  },

  // Update customer
  updateCustomer: async (id: number, data: Partial<CreateCustomerRequest>): Promise<Customer> => {
    try {
      const token = getAuthToken();
      if (!token) {
        const customerIndex = mockCustomers.findIndex(c => c.id === id);
        if (customerIndex === -1) {
          throw new Error('Customer not found');
        }
        
        mockCustomers[customerIndex] = {
          ...mockCustomers[customerIndex],
          ...data,
          full_name: data.full_name || mockCustomers[customerIndex].full_name
        };
        
        return mockCustomers[customerIndex];
      }

      const response = await axios.patch(`${API_BASE_URL}/customers/${id}/`, data, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error updating customer ${id}:`, error);
      throw error;
    }
  }
};

export default customerApi;
