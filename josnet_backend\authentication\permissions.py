from rest_framework import permissions

class IsAdminUser(permissions.BasePermission):
    """
    Permission to only allow admin users to access the view.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role == 'admin'

class IsAdmin(permissions.BasePermission):
    """
    Permission to only allow administrators to access the view.
    (Legacy class - use IsAdminUser instead)
    """

    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_admin)

class IsStaffUser(permissions.BasePermission):
    """
    Permission to only allow staff users to access the view.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role in ['admin', 'staff']

class IsStaffOrAdmin(permissions.BasePermission):
    """
    Permission to only allow staff members or administrators to access the view.
    (Legacy class - use IsStaffUser instead)
    """

    def has_permission(self, request, view):
        return bool(
            request.user and
            request.user.is_authenticated and
            (request.user.is_staff_member or request.user.is_admin)
        )

class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Permission to only allow owners of an object or administrators to access it.
    """

    def has_object_permission(self, request, view, obj):
        # Allow administrators
        if request.user.is_admin:
            return True

        # Check if the object has a user attribute
        if hasattr(obj, 'user'):
            return obj.user == request.user

        # Check if the object is a user
        if hasattr(obj, 'id'):
            return obj.id == request.user.id

        return False

class IsOwnerOrStaff(permissions.BasePermission):
    """
    Permission to only allow owners of an object, staff members, or administrators to access it.
    """

    def has_permission(self, request, view):
        # Allow staff members and administrators to list and create
        if request.user.is_staff_member or request.user.is_admin:
            return True

        # Allow authenticated users for their own data
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Allow staff members and administrators
        if request.user.is_staff_member or request.user.is_admin:
            return True

        # Check if the object has a user attribute
        if hasattr(obj, 'user'):
            return obj.user == request.user

        # Check if the object has an order with a user (for Invoice model)
        if hasattr(obj, 'order') and hasattr(obj.order, 'user'):
            return obj.order.user == request.user

        # Check if the object is a user
        if hasattr(obj, 'id') and hasattr(request.user, 'id'):
            return obj.id == request.user.id

        return False


class IsStaffMember(permissions.BasePermission):
    """
    Permission pour vérifier que l'utilisateur est un membre du staff.
    """

    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            (request.user.is_staff_member or request.user.is_admin)
        )

class IsVerified(permissions.BasePermission):
    """
    Permission to only allow verified users to access the view.
    """

    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_verified)
