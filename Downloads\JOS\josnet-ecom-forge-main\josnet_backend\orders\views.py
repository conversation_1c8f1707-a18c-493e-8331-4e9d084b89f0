from django.db.models import Q, Count, Sum
from django.utils import timezone
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from .models import Order, OrderItem, OrderStatusHistory, OrderRefund, ShippingMethod
from .serializers import (
    OrderListSerializer, OrderDetailSerializer, OrderCreateSerializer, OrderUpdateSerializer,
    OrderItemSerializer, OrderStatusHistorySerializer, OrderRefundSerializer, ShippingMethodSerializer
)
from authentication.permissions import IsStaffOrAdmin, IsOwnerOrStaff

class OrderViewSet(viewsets.ModelViewSet):
    """
    API endpoint for orders.
    """
    queryset = Order.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'payment_status', 'payment_method']
    search_fields = ['order_number', 'email', 'billing_first_name', 'billing_last_name', 'shipping_first_name', 'shipping_last_name']
    ordering_fields = ['created_at', 'updated_at', 'total']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return OrderCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return OrderUpdateSerializer
        elif self.action == 'list':
            return OrderListSerializer
        return OrderDetailSerializer

    def get_permissions(self):
        if self.action in ['create']:
            permission_classes = [IsAuthenticated]
        elif self.action in ['list', 'retrieve', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsOwnerOrStaff]
        else:
            permission_classes = [IsStaffOrAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # Regular users can only see their own orders
        if not user.is_staff and not user.is_superuser:
            queryset = queryset.filter(user=user)

        return queryset

    def perform_create(self, serializer):
        # Set the user if authenticated
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user)
        else:
            serializer.save()

    @action(detail=True, methods=['post'])
    def add_status(self, request, pk=None):
        """
        Add a new status to the order.

        Example request:
        {
            "status": "processing",
            "notes": "Order is being processed"
        }
        """
        order = self.get_object()
        status_value = request.data.get('status')
        notes = request.data.get('notes', '')

        if not status_value:
            return Response({"error": "Status is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Validate status value
        valid_statuses = dict(Order.STATUS_CHOICES).keys()
        if status_value not in valid_statuses:
            return Response(
                {"error": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create status history
        status_history = OrderStatusHistory.objects.create(
            order=order,
            status=status_value,
            notes=notes,
            created_by=request.user
        )

        # Update order status
        old_status = order.status
        order.status = status_value

        # Update timestamp fields based on status
        if status_value == 'paid' and not order.paid_at:
            order.paid_at = timezone.now()
        elif status_value == 'shipped' and not order.shipped_at:
            order.shipped_at = timezone.now()
        elif status_value == 'delivered' and not order.delivered_at:
            order.delivered_at = timezone.now()
        elif status_value == 'cancelled' and not order.cancelled_at:
            order.cancelled_at = timezone.now()

        order.save()

        serializer = OrderStatusHistorySerializer(status_history)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an order.

        Example request:
        {
            "reason": "Customer requested cancellation"
        }
        """
        order = self.get_object()
        reason = request.data.get('reason', 'No reason provided')

        # Check if order can be cancelled
        if order.status in ['delivered', 'refunded', 'cancelled']:
            return Response(
                {"error": f"Cannot cancel order with status '{order.get_status_display()}'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create status history
        status_history = OrderStatusHistory.objects.create(
            order=order,
            status='cancelled',
            notes=f"Order cancelled. Reason: {reason}",
            created_by=request.user
        )

        # Update order
        order.status = 'cancelled'
        order.cancelled_at = timezone.now()
        order.save()

        serializer = self.get_serializer(order)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """Get all items for an order."""
        order = self.get_object()
        items = order.items.all()
        serializer = OrderItemSerializer(items, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def status_history(self, request, pk=None):
        """Get status history for an order."""
        order = self.get_object()
        history = order.status_history.all()
        serializer = OrderStatusHistorySerializer(history, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def refunds(self, request, pk=None):
        """Get refunds for an order."""
        order = self.get_object()
        refunds = order.refunds.all()
        serializer = OrderRefundSerializer(refunds, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def my_orders(self, request):
        """Get orders for the current user."""
        if not request.user.is_authenticated:
            return Response({"error": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

        orders = Order.objects.filter(user=request.user)
        page = self.paginate_queryset(orders)

        if page is not None:
            serializer = OrderListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = OrderListSerializer(orders, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get order statistics for the dashboard."""
        if not request.user.is_staff and not request.user.is_superuser:
            return Response({"error": "Staff privileges required"}, status=status.HTTP_403_FORBIDDEN)

        # Get counts by status
        status_counts = Order.objects.values('status').annotate(count=Count('id'))
        status_data = {item['status']: item['count'] for item in status_counts}

        # Get total revenue
        total_revenue = Order.objects.filter(status__in=['processing', 'shipped', 'delivered']).aggregate(total=Sum('total'))['total'] or 0

        # Get recent orders
        recent_orders = Order.objects.order_by('-created_at')[:5]
        recent_orders_data = OrderListSerializer(recent_orders, many=True).data

        # Get counts by payment method
        payment_method_counts = Order.objects.values('payment_method').annotate(count=Count('id'))
        payment_method_data = {item['payment_method']: item['count'] for item in payment_method_counts}

        return Response({
            'status_counts': status_data,
            'total_revenue': total_revenue,
            'recent_orders': recent_orders_data,
            'payment_method_counts': payment_method_data,
            'total_orders': Order.objects.count()
        })

class OrderItemViewSet(viewsets.ModelViewSet):
    """
    API endpoint for order items.
    """
    queryset = OrderItem.objects.all()
    serializer_class = OrderItemSerializer
    permission_classes = [IsStaffOrAdmin]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['order', 'product', 'variant']

class OrderRefundViewSet(viewsets.ModelViewSet):
    """
    API endpoint for order refunds.
    """
    queryset = OrderRefund.objects.all()
    serializer_class = OrderRefundSerializer
    permission_classes = [IsStaffOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['order', 'status', 'refund_type']
    ordering_fields = ['created_at', 'amount']
    ordering = ['-created_at']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """
        Process a refund.

        Example request:
        {
            "status": "approved",
            "transaction_id": "REF123456",
            "notes": "Refund processed through payment gateway"
        }
        """
        refund = self.get_object()
        status_value = request.data.get('status')
        transaction_id = request.data.get('transaction_id')
        notes = request.data.get('notes')

        if not status_value:
            return Response({"error": "Status is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Validate status value
        valid_statuses = dict(OrderRefund.REFUND_STATUS_CHOICES).keys()
        if status_value not in valid_statuses:
            return Response(
                {"error": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update refund
        refund.status = status_value
        refund.transaction_id = transaction_id

        if notes:
            refund.notes = notes if not refund.notes else f"{refund.notes}\n\n{notes}"

        if status_value in ['approved', 'completed']:
            refund.processed_by = request.user
            refund.processed_at = timezone.now()

            # Update order status if refund is completed
            if status_value == 'completed':
                order = refund.order

                # Check if this is a full refund
                if refund.refund_type == 'full':
                    order.status = 'refunded'
                    order.payment_status = 'refunded'
                else:
                    order.status = 'partially_refunded'
                    order.payment_status = 'partially_refunded'

                order.save()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    status=order.status,
                    notes=f"Order {order.get_status_display()} due to refund #{refund.refund_number}",
                    created_by=request.user
                )

        refund.save()

        serializer = self.get_serializer(refund)
        return Response(serializer.data)

class ShippingMethodViewSet(viewsets.ModelViewSet):
    """
    API endpoint for shipping methods.
    """
    queryset = ShippingMethod.objects.all()
    serializer_class = ShippingMethodSerializer
    permission_classes = [IsStaffOrAdmin]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active']

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active shipping methods."""
        shipping_methods = ShippingMethod.objects.filter(is_active=True)
        serializer = self.get_serializer(shipping_methods, many=True)
        return Response(serializer.data)
