/**
 * This file provides compatibility components to bridge Material UI components
 * with shadcn/ui components. This allows us to gradually migrate from Material UI
 * to shadcn/ui without having to convert all components at once.
 */

import React from 'react';

// Import shadcn/ui components
import { <PERSON><PERSON> as ShadcnButton } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  Table as ShadcnTable,
  TableBody as ShadcnTableBody,
  TableCell as ShadcnTableCell,
  TableHead as ShadcnTableHead,
  TableHeader as ShadcnTableHeader,
  TableRow as ShadcnTableRow
} from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs as ShadcnTabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  Dialog as ShadcnDialog,
  DialogContent as ShadcnDialogContent,
  DialogDescription as ShadcnDialogDescription,
  DialogFooter as ShadcnDialogFooter,
  DialogHeader as ShadcnDialogHeader,
  DialogTitle as ShadcnDialogTitle,
  DialogTrigger as ShadcnDialogTrigger
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Checkbox as ShadcnCheckbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

// Material UI Typography component
export const Typography: React.FC<{
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2' | 'body1' | 'body2' | 'caption' | 'button' | 'overline';
  color?: 'initial' | 'inherit' | 'primary' | 'secondary' | 'textPrimary' | 'textSecondary' | 'error';
  gutterBottom?: boolean;
  align?: 'inherit' | 'left' | 'center' | 'right' | 'justify';
  display?: 'initial' | 'block' | 'inline';
  children: React.ReactNode;
  className?: string;
  sx?: any;
}> = ({ variant = 'body1', color, gutterBottom, align, display, children, className, ...props }) => {
  const classNames = cn(
    className,
    color === 'textSecondary' && 'text-muted-foreground',
    color === 'error' && 'text-destructive',
    color === 'primary' && 'text-primary',
    gutterBottom && 'mb-4',
    align === 'center' && 'text-center',
    align === 'right' && 'text-right',
    align === 'justify' && 'text-justify',
    display === 'block' && 'block',
    display === 'inline' && 'inline'
  );

  switch (variant) {
    case 'h1':
      return <h1 className={cn("scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl", classNames)} {...props}>{children}</h1>;
    case 'h2':
      return <h2 className={cn("scroll-m-20 text-3xl font-semibold tracking-tight", classNames)} {...props}>{children}</h2>;
    case 'h3':
      return <h3 className={cn("scroll-m-20 text-2xl font-semibold tracking-tight", classNames)} {...props}>{children}</h3>;
    case 'h4':
      return <h4 className={cn("scroll-m-20 text-xl font-semibold tracking-tight", classNames)} {...props}>{children}</h4>;
    case 'h5':
      return <h5 className={cn("scroll-m-20 text-lg font-semibold tracking-tight", classNames)} {...props}>{children}</h5>;
    case 'h6':
      return <h6 className={cn("scroll-m-20 text-base font-semibold tracking-tight", classNames)} {...props}>{children}</h6>;
    case 'subtitle1':
      return <p className={cn("text-lg font-medium", classNames)} {...props}>{children}</p>;
    case 'subtitle2':
      return <p className={cn("text-base font-medium", classNames)} {...props}>{children}</p>;
    case 'body1':
      return <p className={cn("leading-7", classNames)} {...props}>{children}</p>;
    case 'body2':
      return <p className={cn("text-sm leading-6", classNames)} {...props}>{children}</p>;
    case 'caption':
      return <p className={cn("text-xs", classNames)} {...props}>{children}</p>;
    case 'button':
      return <span className={cn("font-medium", classNames)} {...props}>{children}</span>;
    case 'overline':
      return <p className={cn("text-xs uppercase tracking-widest", classNames)} {...props}>{children}</p>;
    default:
      return <p className={classNames} {...props}>{children}</p>;
  }
};

// Material UI Box component
export const Box: React.FC<{
  sx?: any;
  children: React.ReactNode;
  className?: string;
}> = ({ sx, children, className, ...props }) => {
  // Convert sx to className
  let sxClassName = '';
  if (sx) {
    if (sx.mb) sxClassName += ` mb-${sx.mb * 2}`;
    if (sx.mt) sxClassName += ` mt-${sx.mt * 2}`;
    if (sx.ml) sxClassName += ` ml-${sx.ml * 2}`;
    if (sx.mr) sxClassName += ` mr-${sx.mr * 2}`;
    if (sx.p) sxClassName += ` p-${sx.p * 2}`;
    if (sx.pt) sxClassName += ` pt-${sx.pt * 2}`;
    if (sx.pb) sxClassName += ` pb-${sx.pb * 2}`;
    if (sx.pl) sxClassName += ` pl-${sx.pl * 2}`;
    if (sx.pr) sxClassName += ` pr-${sx.pr * 2}`;

    if (sx.display === 'flex') sxClassName += ' flex';
    if (sx.flexDirection === 'column') sxClassName += ' flex-col';
    if (sx.justifyContent === 'center') sxClassName += ' justify-center';
    if (sx.justifyContent === 'space-between') sxClassName += ' justify-between';
    if (sx.alignItems === 'center') sxClassName += ' items-center';
    if (sx.gap) sxClassName += ` gap-${sx.gap * 2}`;
  }

  return <div className={cn(className, sxClassName)} {...props}>{children}</div>;
};

// Material UI Grid component
export const Grid: React.FC<{
  container?: boolean;
  item?: boolean;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  spacing?: number;
  children: React.ReactNode;
  className?: string;
  alignItems?: string;
}> = ({ container, item, xs, sm, md, lg, xl, spacing, alignItems, children, className, ...props }) => {
  const classNames = cn(
    className,
    container && 'grid',
    container && spacing && `gap-${spacing * 2}`,
    alignItems === 'center' && 'items-center',
    item && 'col-span-12',
    xs === 12 && 'col-span-12',
    xs === 6 && 'col-span-6',
    xs === 4 && 'col-span-4',
    xs === 3 && 'col-span-3',
    sm === 12 && 'sm:col-span-12',
    sm === 6 && 'sm:col-span-6',
    sm === 4 && 'sm:col-span-4',
    sm === 3 && 'sm:col-span-3',
    md === 12 && 'md:col-span-12',
    md === 8 && 'md:col-span-8',
    md === 6 && 'md:col-span-6',
    md === 4 && 'md:col-span-4',
    md === 3 && 'md:col-span-3',
    lg === 12 && 'lg:col-span-12',
    lg === 6 && 'lg:col-span-6',
    lg === 4 && 'lg:col-span-4',
    lg === 3 && 'lg:col-span-3',
  );

  return <div className={classNames} {...props}>{children}</div>;
};

// Material UI Paper component
export const Paper: React.FC<{
  elevation?: number;
  sx?: any;
  children: React.ReactNode;
  className?: string;
}> = ({ elevation, sx, children, className, ...props }) => {
  // Convert sx to className
  let sxClassName = '';
  if (sx) {
    if (sx.p) sxClassName += ` p-${sx.p * 2}`;
    if (sx.mb) sxClassName += ` mb-${sx.mb * 2}`;
    if (sx.mt) sxClassName += ` mt-${sx.mt * 2}`;
  }

  return (
    <Card className={cn(className, sxClassName)} {...props}>
      <CardContent className="p-4">{children}</CardContent>
    </Card>
  );
};

// Material UI Button component
export const Button: React.FC<{
  variant?: 'text' | 'contained' | 'outlined';
  color?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
  size?: 'small' | 'medium' | 'large';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  fullWidth?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  component?: any;
  to?: string;
  sx?: any;
  children: React.ReactNode;
  className?: string;
}> = ({
  variant = 'text',
  color = 'primary',
  size,
  startIcon,
  endIcon,
  fullWidth,
  disabled,
  onClick,
  component,
  to,
  sx,
  children,
  className,
  ...props
}) => {
  // Convert sx to className
  let sxClassName = '';
  if (sx) {
    if (sx.mr) sxClassName += ` mr-${sx.mr * 2}`;
    if (sx.ml) sxClassName += ` ml-${sx.ml * 2}`;
    if (sx.mt) sxClassName += ` mt-${sx.mt * 2}`;
    if (sx.mb) sxClassName += ` mb-${sx.mb * 2}`;
  }

  const buttonVariant =
    variant === 'contained' ? 'default' as const :
    variant === 'outlined' ? 'outline' as const :
    'ghost' as const;

  const buttonSize = size === 'small' ? 'sm' as const :
                   size === 'large' ? 'lg' as const :
                   'default' as const;

  const buttonProps = {
    variant: buttonVariant,
    size: buttonSize,
    className: cn(
      fullWidth && 'w-full',
      sxClassName,
      className
    ),
    disabled,
    onClick,
    ...props
  };

  // If component is Link, use asChild
  if (component === Link) {
    return (
      <ShadcnButton asChild {...buttonProps}>
        <Link to={to}>
          {startIcon && <span className="mr-2">{startIcon}</span>}
          {children}
          {endIcon && <span className="ml-2">{endIcon}</span>}
        </Link>
      </ShadcnButton>
    );
  }

  return (
    <ShadcnButton {...buttonProps}>
      {startIcon && <span className="mr-2">{startIcon}</span>}
      {children}
      {endIcon && <span className="ml-2">{endIcon}</span>}
    </ShadcnButton>
  );
};

// Material UI Chip component
export const Chip: React.FC<{
  label: string;
  color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  size?: 'small' | 'medium';
  variant?: 'filled' | 'outlined';
  sx?: any;
  className?: string;
}> = ({ label, color = 'default', size, variant, sx, className, ...props }) => {
  // Convert sx to className
  let sxClassName = '';
  if (sx) {
    if (sx.ml) sxClassName += ` ml-${sx.ml * 2}`;
    if (sx.mr) sxClassName += ` mr-${sx.mr * 2}`;
  }

  const badgeVariant = variant === 'outlined' ? 'outline' : 'default';

  return (
    <Badge
      variant={badgeVariant}
      className={cn(
        'capitalize',
        color === 'success' && "bg-green-100 text-green-800 hover:bg-green-100",
        color === 'error' && "bg-red-100 text-red-800 hover:bg-red-100",
        color === 'warning' && "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
        color === 'info' && "bg-blue-100 text-blue-800 hover:bg-blue-100",
        color === 'primary' && "bg-primary/20 text-primary hover:bg-primary/20",
        color === 'secondary' && "bg-secondary/20 text-secondary hover:bg-secondary/20",
        size === 'small' && 'text-xs py-0 px-2',
        sxClassName,
        className
      )}
      {...props}
    >
      {label}
    </Badge>
  );
};

// Material UI CircularProgress component
export const CircularProgress: React.FC<{
  size?: number;
  color?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | 'inherit';
  className?: string;
}> = ({ size = 40, color = 'primary', className, ...props }) => {
  return (
    <div className="flex justify-center items-center">
      <Loader2
        className={cn(
          "animate-spin",
          color === 'primary' && "text-primary",
          color === 'secondary' && "text-secondary",
          color === 'error' && "text-destructive",
          className
        )}
        size={size}
        {...props}
      />
    </div>
  );
};

// Material UI Divider component
export const Divider: React.FC<{
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  sx?: any;
}> = ({ orientation = 'horizontal', className, ...props }) => {
  return (
    <Separator
      orientation={orientation}
      className={cn(
        orientation === 'vertical' ? 'h-full' : 'w-full',
        className
      )}
      {...props}
    />
  );
};

// Export shadcn/ui components with Material UI names
export const Table = ShadcnTable;
export const TableBody = ShadcnTableBody;
export const TableCell = ShadcnTableCell;
export const TableContainer: React.FC<{
  component?: any;
  children: React.ReactNode;
  className?: string;
}> = ({ component: Component = 'div', children, className, ...props }) => {
  return (
    <div className={cn("w-full overflow-auto", className)} {...props}>
      {children}
    </div>
  );
};
export const TableHead = ShadcnTableHeader;
export const TableRow = ShadcnTableRow;

// Material UI Dialog components
export const Dialog = ShadcnDialog;
export const DialogContent = ShadcnDialogContent;
export const DialogDescription = ShadcnDialogDescription;
export const DialogFooter = ShadcnDialogFooter;
export const DialogHeader = ShadcnDialogHeader;
export const DialogTitle = ShadcnDialogTitle;
export const DialogTrigger = ShadcnDialogTrigger;

// Material UI DialogActions component (maps to DialogFooter in shadcn/ui)
export const DialogActions: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className, ...props }) => {
  return <ShadcnDialogFooter className={cn("flex justify-end gap-2", className)} {...props}>{children}</ShadcnDialogFooter>;
};

// Material UI TextField component
export const TextField: React.FC<{
  label?: string;
  variant?: 'outlined' | 'filled' | 'standard';
  size?: 'small' | 'medium';
  fullWidth?: boolean;
  multiline?: boolean;
  rows?: number;
  select?: boolean;
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  children?: React.ReactNode;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  sx?: any;
  className?: string;
  InputProps?: any;
  inputProps?: any;
  name?: string;
  required?: boolean;
  margin?: 'none' | 'dense' | 'normal';
}> = ({
  label,
  variant,
  size,
  fullWidth,
  multiline,
  rows,
  select,
  value,
  onChange,
  children,
  placeholder,
  type,
  disabled,
  error,
  helperText,
  sx,
  className,
  InputProps,
  ...props
}) => {
  // Convert sx to className
  let sxClassName = '';
  if (sx) {
    if (sx.minWidth) sxClassName += ` min-w-[${sx.minWidth}px]`;
  }

  if (select) {
    return (
      <div className={cn("space-y-2", fullWidth && 'w-full', sxClassName, className)}>
        {label && <Label>{label}</Label>}
        <Select value={value?.toString()} onValueChange={(val) => onChange?.({ target: { value: val } } as any)}>
          <SelectTrigger className={cn(error && 'border-destructive')}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {children}
          </SelectContent>
        </Select>
        {helperText && (
          <p className={cn("text-sm", error ? "text-destructive" : "text-muted-foreground")}>
            {helperText}
          </p>
        )}
      </div>
    );
  }

  if (multiline) {
    return (
      <div className={cn("space-y-2", fullWidth && 'w-full', sxClassName, className)}>
        {label && <Label>{label}</Label>}
        <Textarea
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          rows={rows}
          className={cn(error && 'border-destructive')}
          {...props}
        />
        {helperText && (
          <p className={cn("text-sm", error ? "text-destructive" : "text-muted-foreground")}>
            {helperText}
          </p>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", fullWidth && 'w-full', sxClassName, className)}>
      {label && <Label>{label}</Label>}
      <div className="relative">
        {InputProps?.startAdornment && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none">
            {InputProps.startAdornment}
          </div>
        )}
        <Input
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            error && 'border-destructive',
            size === 'small' && 'h-8 text-sm',
            InputProps?.startAdornment && 'pl-10',
            InputProps?.endAdornment && 'pr-10',
          )}
          {...props}
        />
        {InputProps?.endAdornment && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
            {InputProps.endAdornment}
          </div>
        )}
      </div>
      {helperText && (
        <p className={cn("text-sm", error ? "text-destructive" : "text-muted-foreground")}>
          {helperText}
        </p>
      )}
    </div>
  );
};

// Material UI MenuItem component
export const MenuItem: React.FC<{
  value: string | number;
  children: React.ReactNode;
  className?: string;
}> = ({ value, children, className, ...props }) => {
  return (
    <SelectItem value={value.toString()} className={className} {...props}>
      {children}
    </SelectItem>
  );
};

// Material UI IconButton component
export const IconButton: React.FC<{
  color?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onClick?: () => void;
  component?: any;
  to?: string;
  children: React.ReactNode;
  className?: string;
}> = ({
  color = 'primary',
  size,
  disabled,
  onClick,
  component,
  to,
  children,
  className,
  ...props
}) => {
  const buttonProps = {
    variant: 'ghost' as const,
    size: 'icon' as const,
    className,
    disabled,
    onClick,
    ...props
  };

  // If component is Link, use asChild
  if (component === Link) {
    return (
      <ShadcnButton asChild {...buttonProps}>
        <Link to={to}>
          {children}
        </Link>
      </ShadcnButton>
    );
  }

  return (
    <ShadcnButton {...buttonProps}>
      {children}
    </ShadcnButton>
  );
};

// Material UI Pagination component
export const Pagination: React.FC<{
  count: number;
  page: number;
  onChange: (event: React.ChangeEvent<unknown>, value: number) => void;
  color?: 'primary' | 'secondary' | 'standard';
  className?: string;
}> = ({ count, page, onChange, color, className, ...props }) => {
  return (
    <nav className={cn("flex justify-center", className)} {...props}>
      <ul className="flex list-none">
        <li>
          <button
            onClick={(e) => onChange(e, page - 1)}
            disabled={page === 1}
            className={cn(
              "px-3 py-2 rounded-l-md border",
              page === 1 ? "opacity-50 cursor-not-allowed" : "hover:bg-accent"
            )}
          >
            Prev
          </button>
        </li>
        {Array.from({ length: Math.min(5, count) }, (_, i) => {
          let pageNum: number;
          if (count <= 5) {
            pageNum = i + 1;
          } else if (page <= 3) {
            pageNum = i + 1;
          } else if (page >= count - 2) {
            pageNum = count - 4 + i;
          } else {
            pageNum = page - 2 + i;
          }

          return (
            <li key={i}>
              <button
                onClick={(e) => onChange(e, pageNum)}
                className={cn(
                  "px-3 py-2 border-t border-b",
                  page === pageNum ? "bg-primary text-primary-foreground" : "hover:bg-accent"
                )}
              >
                {pageNum}
              </button>
            </li>
          );
        })}
        <li>
          <button
            onClick={(e) => onChange(e, page + 1)}
            disabled={page === count}
            className={cn(
              "px-3 py-2 rounded-r-md border",
              page === count ? "opacity-50 cursor-not-allowed" : "hover:bg-accent"
            )}
          >
            Next
          </button>
        </li>
      </ul>
    </nav>
  );
};

// Material UI Tab components
export const Tab: React.FC<{
  label?: string;
  icon?: React.ReactNode;
  value?: any;
  className?: string;
}> = ({ label, icon, value, className, ...props }) => {
  return (
    <TabsTrigger value={value} className={className} {...props}>
      {icon && <span className="mr-2">{icon}</span>}
      {label}
    </TabsTrigger>
  );
};

export const Tabs: React.FC<{
  value: any;
  onChange: (event: React.SyntheticEvent, newValue: any) => void;
  children: React.ReactNode;
  className?: string;
}> = ({ value, onChange, children, className, ...props }) => {
  // Extract TabsTrigger components from children
  const triggers = React.Children.toArray(children).filter(
    (child) => React.isValidElement(child) && child.type === Tab
  );

  return (
    <ShadcnTabs
      value={value.toString()}
      onValueChange={(newValue: string) => onChange({} as React.SyntheticEvent, parseInt(newValue))}
      className={className}
      {...props}
    >
      <TabsList className="w-full">
        {triggers}
      </TabsList>
    </ShadcnTabs>
  );
};

// Material UI Alert component
export const MuiAlert: React.FC<{
  severity?: 'error' | 'warning' | 'info' | 'success';
  children: React.ReactNode;
  className?: string;
  sx?: any;
}> = ({ severity = 'info', children, className, sx, ...props }) => {
  // Convert sx to className
  let sxClassName = '';
  if (sx) {
    if (sx.mb) sxClassName += ` mb-${sx.mb * 2}`;
    if (sx.mt) sxClassName += ` mt-${sx.mt * 2}`;
    if (sx.ml) sxClassName += ` ml-${sx.ml * 2}`;
    if (sx.mr) sxClassName += ` mr-${sx.mr * 2}`;
  }

  return (
    <Alert
      variant={severity === 'error' ? 'destructive' : undefined}
      className={cn(
        severity === 'warning' && 'border-yellow-500 text-yellow-800',
        severity === 'info' && 'border-blue-500 text-blue-800',
        severity === 'success' && 'border-green-500 text-green-800',
        sxClassName,
        className
      )}
      {...props}
    >
      <AlertDescription>{children}</AlertDescription>
    </Alert>
  );
};

// Export the MuiAlert as Alert to avoid naming conflicts
export { MuiAlert as Alert };

// Material UI Checkbox component
export const Checkbox: React.FC<{
  checked?: boolean;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  className?: string;
}> = ({ checked, onChange, disabled, className, ...props }) => {
  return (
    <input
      type="checkbox"
      checked={checked}
      onChange={onChange}
      disabled={disabled}
      className={cn(
        "h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary",
        className
      )}
      {...props}
    />
  );
};

// Material UI FormControlLabel component
export const FormControlLabel: React.FC<{
  control: React.ReactElement;
  label: React.ReactNode;
  disabled?: boolean;
  className?: string;
}> = ({ control, label, disabled, className, ...props }) => {
  return (
    <label className={cn("flex items-center space-x-2", className)} {...props}>
      {React.cloneElement(control, { disabled })}
      <span className={cn("text-sm", disabled && "text-gray-400")}>{label}</span>
    </label>
  );
};

// Export Link from react-router-dom
import { Link } from 'react-router-dom';
