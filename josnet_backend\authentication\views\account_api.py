from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from orders.models_extension import ReturnRequest, Claim
from orders.serializers_extension import (
    ReturnRequestListSerializer, ReturnRequestDetailSerializer, ReturnRequestCreateSerializer,
    ClaimListSerializer, ClaimDetailSerializer, ClaimCreateSerializer, ClaimMessageSerializer
)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def returns_api(request):
    """
    API endpoint for user return requests.
    GET: List all return requests for the authenticated user.
    POST: Create a new return request.
    """
    if request.method == 'GET':
        returns = ReturnRequest.objects.filter(user=request.user).order_by('-created_at')
        serializer = ReturnRequestListSerializer(returns, many=True, context={'request': request})
        return Response(serializer.data)
    
    elif request.method == 'POST':
        serializer = ReturnRequestCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def return_detail_api(request, pk):
    """
    API endpoint for retrieving a specific return request.
    Staff/Admin can see all returns, regular users only their own.
    """
    if request.user.is_staff or request.user.is_superuser or getattr(request.user, 'is_admin', False):
        # Staff/Admin can see all returns
        return_request = get_object_or_404(ReturnRequest, id=pk)
    else:
        # Regular users can only see their own returns
        return_request = get_object_or_404(ReturnRequest, id=pk, user=request.user)

    serializer = ReturnRequestDetailSerializer(return_request, context={'request': request})
    return Response(serializer.data)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def claims_api(request):
    """
    API endpoint for user claims/support tickets.
    GET: List all claims for the authenticated user.
    POST: Create a new claim.
    """
    if request.method == 'GET':
        claims = Claim.objects.filter(user=request.user).order_by('-created_at')
        serializer = ClaimListSerializer(claims, many=True, context={'request': request})
        return Response(serializer.data)
    
    elif request.method == 'POST':
        serializer = ClaimCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def claim_detail_api(request, pk):
    """
    API endpoint for retrieving a specific claim and adding messages.
    GET: Retrieve claim details.
    POST: Add a message to the claim.
    """
    # Staff/Admin can see all claims, regular users only their own
    if request.user.is_staff or request.user.is_superuser or getattr(request.user, 'is_admin', False):
        claim = get_object_or_404(Claim, id=pk)
    else:
        claim = get_object_or_404(Claim, id=pk, user=request.user)
    
    if request.method == 'GET':
        serializer = ClaimDetailSerializer(claim, context={'request': request})
        return Response(serializer.data)
    
    elif request.method == 'POST':
        # Adding a message to the claim
        serializer = ClaimMessageSerializer(data={
            'claim': claim.id,
            'sender': 'customer',
            'message': request.data.get('message', ''),
            'uploaded_files': request.FILES.getlist('uploaded_files', [])
        }, context={'request': request})
        
        if serializer.is_valid():
            serializer.save()
            
            # Return updated claim
            claim_serializer = ClaimDetailSerializer(claim, context={'request': request})
            return Response(claim_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
