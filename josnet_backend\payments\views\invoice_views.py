from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404

from payments.models import Invoice
from payments.serializers import InvoiceSerializer, InvoiceDetailSerializer

class InvoiceListView(generics.ListAPIView):
    """
    View to list all invoices for the authenticated user.
    """
    serializer_class = InvoiceSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Invoice.objects.filter(order__user=self.request.user)

class InvoiceDetailView(APIView):
    """
    View to retrieve a specific invoice for the authenticated user.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, invoice_id):
        # Get the invoice - try by ID first, then by invoice_number
        try:
            if invoice_id.isdigit():
                # If it's a number, try to get by ID
                invoice = get_object_or_404(Invoice, id=invoice_id, order__user=request.user)
            else:
                # Otherwise, try to get by invoice_number
                invoice = get_object_or_404(Invoice, invoice_number=invoice_id, order__user=request.user)
        except:
            # Fallback: try both methods
            try:
                invoice = get_object_or_404(Invoice, id=invoice_id, order__user=request.user)
            except:
                invoice = get_object_or_404(Invoice, invoice_number=invoice_id, order__user=request.user)
        
        # Serialize the invoice with detailed information
        serializer = InvoiceDetailSerializer(invoice)
        
        return Response(serializer.data)
