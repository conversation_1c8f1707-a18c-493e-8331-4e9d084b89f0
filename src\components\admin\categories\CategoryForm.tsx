import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from 'lucide-react';
import { productApi } from '@/services/productApi';
import { useToast } from '@/hooks/use-toast';
import { Category } from '@/services/productApi';

interface CategoryFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  category?: Category | null;
  onSuccess: () => void;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
  isOpen,
  onOpenChange,
  category,
  onSuccess
}) => {
  const { toast } = useToast();
  const isEditMode = !!category;

  // Form state
  const [formData, setFormData] = useState<Partial<Category>>({
    name: '',
    slug: '',
    description: '',
    parent: null,
    is_active: true
  });

  // Reset form when dialog opens or category changes
  useEffect(() => {
    if (isOpen) {
      if (category) {
        setFormData({
          name: category.name || '',
          slug: category.slug || '',
          description: category.description || '',
          parent: category.parent || null,
          is_active: category.is_active !== undefined ? category.is_active : true
        });
      } else {
        setFormData({
          name: '',
          slug: '',
          description: '',
          parent: null,
          is_active: true
        });
      }
    }
  }, [isOpen, category]);

  // Fetch parent categories for dropdown
  const { data: parentCategories = [] } = useQuery({
    queryKey: ['categories'],
    queryFn: () => productApi.getCategories(),
    select: (data) => {
      // Filter out the current category (can't be its own parent)
      if (category) {
        return data.filter(cat => cat.id !== category.id);
      }
      return data;
    }
  });

  // Create category mutation
  const createCategoryMutation = useMutation({
    mutationFn: (data: Partial<Category>) => productApi.createCategory(data),
    onSuccess: () => {
      toast({
        title: 'Catégorie créée',
        description: 'La catégorie a été créée avec succès.',
      });
      onSuccess();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la création de la catégorie.',
        variant: 'destructive',
      });
    },
  });

  // Update category mutation
  const updateCategoryMutation = useMutation({
    mutationFn: (data: { id: number, category: Partial<Category> }) =>
      productApi.updateCategory(data.id, data.category),
    onSuccess: () => {
      toast({
        title: 'Catégorie mise à jour',
        description: 'La catégorie a été mise à jour avec succès.',
      });
      onSuccess();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la mise à jour de la catégorie.',
        variant: 'destructive',
      });
    },
  });

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Auto-generate slug from name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setFormData(prev => ({
      ...prev,
      name,
      // Only auto-generate slug if it's empty or matches previous auto-generated slug
      slug: prev.slug === slugify(prev.name || '') || prev.slug === ''
        ? slugify(name)
        : prev.slug
    }));
  };

  // Handle checkbox change
  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, is_active: checked }));
  };

  // Handle parent category selection
  const handleParentChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      parent: value === 'none' ? null : parseInt(value)
    }));
  };

  // Fonction pour générer un identifiant aléatoire sans dépendre de bibliothèques externes
  const generateRandomId = (length: number = 8) => {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // Fonction slugify améliorée pour générer des slugs uniques
  const slugify = (text: string) => {
    const baseSlug = text
      .toString()
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')        // Replace spaces with -
      .replace(/&/g, '-and-')      // Replace & with 'and'
      .replace(/[^\w\-]+/g, '')    // Remove all non-word chars
      .replace(/\-\-+/g, '-');     // Replace multiple - with single -
    
    // Ajouter un suffixe unique basé sur le timestamp et un identifiant aléatoire
    const timestamp = Date.now().toString().slice(-6);
    const randomSuffix = generateRandomId(4);
    
    return `${baseSlug}-${timestamp}-${randomSuffix}`;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      toast({
        title: 'Erreur de validation',
        description: 'Le nom de la catégorie est requis.',
        variant: 'destructive',
      });
      return;
    }

    if (isEditMode && category) {
      updateCategoryMutation.mutate({
        id: category.id,
        category: formData
      });
    } else {
      createCategoryMutation.mutate(formData);
    }
  };

  const isPending = createCategoryMutation.isPending || updateCategoryMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Modifier la catégorie' : 'Nouvelle catégorie'}</DialogTitle>
          <DialogDescription>
            {isEditMode
              ? 'Modifiez les détails de la catégorie existante.'
              : 'Créez une nouvelle catégorie pour organiser vos produits.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Nom <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleNameChange}
                className="col-span-3"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="slug" className="text-right">
                Slug
              </Label>
              <Input
                id="slug"
                name="slug"
                value={formData.slug}
                onChange={handleChange}
                className="col-span-3"
                placeholder="auto-generated-slug"
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="col-span-3"
                placeholder="Description de la catégorie"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="parent" className="text-right">
                Catégorie parente
              </Label>
              <Select
                value={formData.parent?.toString() || 'none'}
                onValueChange={handleParentChange}
              >
                <SelectTrigger id="parent" className="col-span-3">
                  <SelectValue placeholder="Aucune (catégorie principale)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Aucune (catégorie principale)</SelectItem>
                  {parentCategories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id.toString()}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-start-2 col-span-3 flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={handleCheckboxChange}
                />
                <Label htmlFor="is_active">
                  Catégorie active
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={isPending || !formData.name}
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditMode ? 'Mise à jour...' : 'Création...'}
                </>
              ) : (
                isEditMode ? 'Mettre à jour' : 'Créer'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CategoryForm;
