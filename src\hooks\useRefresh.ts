import { useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';

export interface RefreshOptions {
  /**
   * Query keys to invalidate and refetch
   */
  queryKeys?: (string | (string | any)[])[];
  
  /**
   * Custom refetch function to call instead of invalidating queries
   */
  refetchFn?: () => Promise<any> | any;
  
  /**
   * Success message to show
   */
  successMessage?: string;
  
  /**
   * Error message to show
   */
  errorMessage?: string;
  
  /**
   * Whether to show toast notifications
   */
  showToast?: boolean;
  
  /**
   * Whether to force refetch even if data is fresh
   */
  force?: boolean;
}

/**
 * Hook for standardized refresh functionality across the application
 */
export const useRefresh = (options: RefreshOptions = {}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();
  
  const {
    queryKeys = [],
    refetchFn,
    successMessage = "Données mises à jour",
    errorMessage = "Erreur lors de l'actualisation",
    showToast = true,
    force = true
  } = options;
  
  const refresh = useCallback(async () => {
    if (isRefreshing) return; // Prevent multiple simultaneous refreshes
    
    setIsRefreshing(true);
    
    try {
      if (refetchFn) {
        // Use custom refetch function
        await refetchFn();
      } else if (queryKeys.length > 0) {
        // Invalidate and refetch specified queries
        const promises = queryKeys.map(queryKey => {
          if (force) {
            // Force refetch by invalidating and then refetching
            queryClient.invalidateQueries(queryKey);
            return queryClient.refetchQueries(queryKey);
          } else {
            // Just invalidate, let React Query decide when to refetch
            return queryClient.invalidateQueries(queryKey);
          }
        });
        
        await Promise.all(promises);
      } else {
        // Fallback: invalidate all queries
        await queryClient.invalidateQueries();
      }
      
      if (showToast) {
        toast({
          title: "Actualisation réussie",
          description: successMessage,
        });
      }
    } catch (error) {
      console.error('Refresh error:', error);
      
      if (showToast) {
        toast({
          title: "Erreur d'actualisation",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setIsRefreshing(false);
    }
  }, [
    isRefreshing,
    refetchFn,
    queryKeys,
    force,
    showToast,
    successMessage,
    errorMessage,
    queryClient
  ]);
  
  return {
    refresh,
    isRefreshing
  };
};

/**
 * Specialized hook for refreshing specific data types
 */
export const useDataRefresh = () => {
  const queryClient = useQueryClient();
  
  return {
    // Products
    refreshProducts: useRefresh({
      queryKeys: [['products']],
      successMessage: "Liste des produits mise à jour"
    }),
    
    // Categories
    refreshCategories: useRefresh({
      queryKeys: [['categories']],
      successMessage: "Liste des catégories mise à jour"
    }),
    
    // Inventory
    refreshInventory: useRefresh({
      queryKeys: [['inventory']],
      successMessage: "Données d'inventaire mises à jour"
    }),
    
    // Promotions
    refreshPromotions: useRefresh({
      queryKeys: [['promotions']],
      successMessage: "Liste des promotions mise à jour"
    }),
    
    // Logs
    refreshLogs: useRefresh({
      queryKeys: [['logs'], ['logs-statistics']],
      successMessage: "Logs mis à jour"
    }),
    
    // CMS
    refreshCMS: useRefresh({
      queryKeys: [['cms-pages'], ['cms-blog'], ['cms-media'], ['cms-menus'], ['cms-settings']],
      successMessage: "Contenu CMS mis à jour"
    }),
    
    // Orders
    refreshOrders: useRefresh({
      queryKeys: [['orders']],
      successMessage: "Liste des commandes mise à jour"
    }),
    
    // Users
    refreshUsers: useRefresh({
      queryKeys: [['users']],
      successMessage: "Liste des utilisateurs mise à jour"
    }),
    
    // Invoices
    refreshInvoices: useRefresh({
      queryKeys: [['invoices']],
      successMessage: "Liste des factures mise à jour"
    }),
    
    // Transactions
    refreshTransactions: useRefresh({
      queryKeys: [['transactions']],
      successMessage: "Liste des transactions mise à jour"
    }),
    
    // Generic refresh for any query
    refreshQuery: (queryKey: string | (string | any)[], message?: string) => 
      useRefresh({
        queryKeys: [queryKey],
        successMessage: message || "Données mises à jour"
      }),
    
    // Refresh everything
    refreshAll: useRefresh({
      refetchFn: () => queryClient.invalidateQueries(),
      successMessage: "Toutes les données ont été mises à jour"
    })
  };
};

/**
 * Hook for refresh button component
 */
export const useRefreshButton = (options: RefreshOptions = {}) => {
  const { refresh, isRefreshing } = useRefresh(options);
  
  return {
    onClick: refresh,
    disabled: isRefreshing,
    isLoading: isRefreshing,
    refresh
  };
};
