from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from .models_activation import AccountActivation
from .utils import send_activation_email, send_welcome_email_after_activation
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class AccountActivationView(APIView):
    """
    Vue pour activer un compte utilisateur
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        token = request.data.get('token')
        
        if not token:
            return Response({
                'error': 'Token d\'activation requis',
                'message': 'Veuillez fournir un token d\'activation valide.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Récupérer l'activation par token
            activation = AccountActivation.objects.get(
                activation_token=token,
                is_activated=False
            )
            
            # Vérifier si le token a expiré
            if activation.is_expired:
                return Response({
                    'error': 'Token expiré',
                    'message': 'Le lien d\'activation a expiré. Demandez un nouveau lien.',
                    'expired': True,
                    'user_email': activation.user.email
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Activer le compte
            if activation.activate_account():
                # Envoyer l'email de bienvenue
                send_welcome_email_after_activation(activation.user)
                
                logger.info(f"Compte activé avec succès pour {activation.user.email}")
                
                return Response({
                    'message': 'Compte activé avec succès !',
                    'success': True,
                    'user': {
                        'id': activation.user.id,
                        'email': activation.user.email,
                        'first_name': activation.user.first_name,
                        'last_name': activation.user.last_name,
                        'is_active': activation.user.is_active,
                        'is_verified': activation.user.is_verified
                    },
                    'next_step': 'Vous pouvez maintenant vous connecter à votre compte.'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Échec de l\'activation',
                    'message': 'Impossible d\'activer le compte. Le token peut être expiré.'
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except AccountActivation.DoesNotExist:
            return Response({
                'error': 'Token invalide',
                'message': 'Token d\'activation invalide ou déjà utilisé.',
                'invalid': True
            }, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            logger.error(f"Erreur lors de l'activation: {str(e)}")
            return Response({
                'error': 'Erreur serveur',
                'message': 'Une erreur est survenue lors de l\'activation.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ResendActivationView(APIView):
    """
    Vue pour renvoyer un email d'activation
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        email = request.data.get('email')
        
        if not email:
            return Response({
                'error': 'Email requis',
                'message': 'Veuillez fournir une adresse email.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Récupérer l'utilisateur
            user = User.objects.get(email=email)
            
            # Vérifier si le compte est déjà activé
            if user.is_active:
                return Response({
                    'error': 'Compte déjà activé',
                    'message': 'Ce compte est déjà activé. Vous pouvez vous connecter.',
                    'already_active': True
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Envoyer l'email d'activation
            success, message = send_activation_email(user)
            
            if success:
                return Response({
                    'message': 'Email d\'activation renvoyé avec succès.',
                    'success': True,
                    'email_sent': True,
                    'next_step': 'Consultez votre boîte email et cliquez sur le lien d\'activation.'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Échec de l\'envoi',
                    'message': message,
                    'email_sent': False
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except User.DoesNotExist:
            return Response({
                'error': 'Utilisateur non trouvé',
                'message': 'Aucun compte trouvé avec cette adresse email.',
                'user_not_found': True
            }, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            logger.error(f"Erreur lors du renvoi d'activation: {str(e)}")
            return Response({
                'error': 'Erreur serveur',
                'message': 'Une erreur est survenue lors du renvoi de l\'email.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ActivationStatusView(APIView):
    """
    Vue pour vérifier le statut d'activation d'un compte
    """
    permission_classes = [AllowAny]
    
    def get(self, request):
        email = request.query_params.get('email')
        
        if not email:
            return Response({
                'error': 'Email requis',
                'message': 'Veuillez fournir une adresse email.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
            
            # Récupérer l'activation si elle existe
            activation = None
            try:
                activation = AccountActivation.objects.get(user=user)
            except AccountActivation.DoesNotExist:
                pass
            
            return Response({
                'user': {
                    'email': user.email,
                    'is_active': user.is_active,
                    'is_verified': user.is_verified
                },
                'activation': {
                    'exists': activation is not None,
                    'is_activated': activation.is_activated if activation else False,
                    'is_expired': activation.is_expired if activation else False,
                    'created_at': activation.created_at if activation else None,
                    'expires_at': activation.expires_at if activation else None
                } if activation else None,
                'can_login': user.is_active and user.is_verified,
                'needs_activation': not user.is_active
            }, status=status.HTTP_200_OK)
            
        except User.DoesNotExist:
            return Response({
                'error': 'Utilisateur non trouvé',
                'message': 'Aucun compte trouvé avec cette adresse email.'
            }, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([AllowAny])
def check_activation_token(request, token):
    """
    Endpoint pour vérifier la validité d'un token d'activation
    """
    try:
        activation = AccountActivation.objects.get(
            activation_token=token,
            is_activated=False
        )
        
        return Response({
            'valid': True,
            'expired': activation.is_expired,
            'user_email': activation.user.email,
            'expires_at': activation.expires_at,
            'message': 'Token valide' if not activation.is_expired else 'Token expiré'
        }, status=status.HTTP_200_OK)
        
    except AccountActivation.DoesNotExist:
        return Response({
            'valid': False,
            'message': 'Token invalide ou déjà utilisé'
        }, status=status.HTTP_404_NOT_FOUND)
