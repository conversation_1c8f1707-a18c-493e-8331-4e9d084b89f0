/**
 * Format a date string to a localized date format
 * @param dateString - ISO date string
 * @returns Formatted date string
 */
export const formatDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

/**
 * Format a number as currency
 * @param amount - Number to format
 * @returns Formatted currency string
 */
export const formatCurrency = (amount?: number): string => {
  if (amount === undefined || amount === null) return 'FBu 0';
  
  // Afficher directement en BIF sans conversion
  try {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'BIF',
      currencyDisplay: 'narrowSymbol',
      maximumFractionDigits: 0
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `FBu ${Math.round(amount)}`;
  }
};

/**
 * Format a number with thousand separators
 * @param number - Number to format
 * @returns Formatted number string
 */
export const formatNumber = (number?: number): string => {
  if (number === undefined || number === null) return '0';
  
  try {
    return new Intl.NumberFormat('fr-FR').format(number);
  } catch (error) {
    console.error('Error formatting number:', error);
    return number.toString();
  }
};

/**
 * Standardise et formate une URL d'image pour qu'elle soit utilisable par le frontend
 * @param imageUrl - URL de l'image à formater (peut être une chaîne, un objet ou undefined)
 * @param defaultImage - URL de l'image par défaut à utiliser si l'image est invalide
 * @param apiBaseUrl - URL de base de l'API pour les chemins relatifs
 * @returns URL d'image formatée et prête à être utilisée
 */
export const formatImageUrl = (imageUrl: any, defaultImage: string = '/images/placeholder-product.jpg', apiBaseUrl?: string): string => {
  // Si l'image est undefined ou null, retourner l'image par défaut
  if (imageUrl === undefined || imageUrl === null) {
    return defaultImage;
  }

  let finalUrl = defaultImage;

  // Traiter différents formats d'image
  if (typeof imageUrl === 'string' && imageUrl.trim() !== '') {
    finalUrl = imageUrl.trim();
  } else if (typeof imageUrl === 'object' && imageUrl !== null) {
    // Extraire l'URL de l'objet image
    if (imageUrl.url && typeof imageUrl.url === 'string') {
      finalUrl = imageUrl.url.trim();
    } else if (imageUrl.src && typeof imageUrl.src === 'string') {
      finalUrl = imageUrl.src.trim();
    } else if (imageUrl.path && typeof imageUrl.path === 'string') {
      finalUrl = imageUrl.path.trim();
    } else if (imageUrl.uri && typeof imageUrl.uri === 'string') {
      finalUrl = imageUrl.uri.trim();
    }
  }

  // S'assurer que l'URL est complète
  if (finalUrl !== defaultImage) {
    if (finalUrl.startsWith('http')) {
      // L'URL est déjà absolue, ne rien faire
    } else if (finalUrl.startsWith('/')) {
      // URL relative au domaine
      if (apiBaseUrl && !apiBaseUrl.endsWith('/')) {
        apiBaseUrl += '/';
      }
      
      // Si un apiBaseUrl est fourni et que l'URL ne commence pas par cet URL
      if (apiBaseUrl && !finalUrl.startsWith(apiBaseUrl)) {
        // Enlever le slash initial pour éviter les doubles slashes
        const cleanPath = finalUrl.startsWith('/') ? finalUrl.substring(1) : finalUrl;
        finalUrl = apiBaseUrl + cleanPath;
      }
    } else {
      // URL sans slash initial, ajouter un slash
      finalUrl = '/' + finalUrl;
    }
  }

  return finalUrl;
};

/**
 * Format a file size in bytes to a human-readable format
 * @param bytes - Size in bytes
 * @returns Formatted file size string
 */
export const formatFileSize = (bytes?: number): string => {
  if (bytes === undefined || bytes === null) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
};

/**
 * Format a phone number to a standard format
 * @param phone - Phone number string
 * @returns Formatted phone number
 */
export const formatPhoneNumber = (phone?: string): string => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Format as XX XX XX XX XX (French format)
  if (digits.length === 10) {
    return digits.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
  }
  
  return phone;
};

/**
 * Truncate a string to a maximum length and add ellipsis if needed
 * @param text - Text to truncate
 * @param maxLength - Maximum length
 * @returns Truncated text
 */
export const truncateText = (text?: string, maxLength: number = 100): string => {
  if (!text) return '';
  
  if (text.length <= maxLength) return text;
  
  return `${text.substring(0, maxLength)}...`;
};
