import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/utils/formatters';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { accountApi } from '@/services/accountApi';
import orderApi from '@/services/orderApi';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Package, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Types pour les commandes et les retours
interface OrderItem {
  id: string | number;
  productId: string;
  name: string;
  quantity: number;
  price: string;
  image?: string;
}

interface Order {
  numericId: number;
  id: string;
  date: string;
  status: string;
  total: string;
  items?: OrderItem[];
}

interface CreateReturnFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

// Helper pour réinitialiser l'état du formulaire
const getInitialState = () => ({
  step: 1,
  selectedOrderId: '',
  selectedItems: [] as string[],
  returnReason: '',
  additionalInfo: '',
  error: null as string | null,
});

export function CreateReturnForm({ isOpen, onOpenChange, onSuccess }: CreateReturnFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [state, setState] = useState(getInitialState());
  const { step, selectedOrderId, selectedItems, returnReason, additionalInfo, error } = state;
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Réinitialiser le formulaire à la fermeture
  useEffect(() => {
    if (!isOpen) {
      const timer = setTimeout(() => setState(getInitialState()), 200);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  const { data: orders = [], isLoading: isLoadingOrders } = useQuery<Order[], Error>({
    queryKey: ['userOrders'],
    queryFn: async () => {
      const result = await accountApi.getUserOrders();
      console.log('getUserOrders result:', result);
      console.log('Orders with items:', result.map(order => ({
        id: order.id,
        numericId: order.numericId,
        hasItems: !!order.items,
        itemsCount: order.items?.length || 0
      })));
      return result;
    },
    enabled: isOpen,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: selectedOrder, isLoading: isLoadingDetails } = useQuery<Order | null, Error>({
    queryKey: ['orderDetails', selectedOrderId],
    queryFn: async () => {
      if (!selectedOrderId) return null;
      const order = orders.find(o => o.id === selectedOrderId);
      if (!order) return null;

      // Si la commande a déjà des articles, les utiliser directement
      if (order.items && order.items.length > 0) {
        console.log(`Utilisation des articles existants pour la commande ${order.id}:`, order.items);
        return order;
      }

      // Sinon, essayer de récupérer les détails via l'API
      if (order.numericId) {
        try {
          const detailedOrder = await orderApi.getOrder(order.numericId);
          console.log(`Articles récupérés via API pour la commande ${order.id}:`, detailedOrder.items);
          return detailedOrder;
        } catch (error) {
          console.error('Erreur lors de la récupération des détails de commande:', error);
          // Retourner la commande originale même sans articles détaillés
          return order;
        }
      }

      return order;
    },
    enabled: !!selectedOrderId && isOpen,
  });

  const updateState = (updates: Partial<typeof state>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const handleItemSelection = (itemId: string) => {
    const newSelectedItems = selectedItems.includes(itemId)
      ? selectedItems.filter(id => id !== itemId)
      : [...selectedItems, itemId];
    updateState({ selectedItems: newSelectedItems });
  };

  const handleNextStep = () => {
    updateState({ error: null });
    if (step === 1 && !selectedOrderId) {
      return updateState({ error: 'Veuillez sélectionner une commande.' });
    }
    if (step === 2 && selectedItems.length === 0) {
      return updateState({ error: 'Veuillez sélectionner au moins un article.' });
    }
    if (step === 3 && !returnReason) {
      return updateState({ error: 'Veuillez sélectionner une raison pour le retour.' });
    }
    updateState({ step: step + 1 });
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    updateState({ error: null });

    try {
      const order = orders.find(o => o.id === selectedOrderId);
      if (!order) {
        return updateState({ error: 'Erreur: Impossible de trouver les détails de la commande sélectionnée.' });
      }

      const returnData = {
        orderId: String(order.numericId), // Utiliser l'ID numérique (converti en string) pour l'API
        items: selectedItems.map(id => {
          const item = selectedOrder?.items?.find(i => String(i.id) === id);
          return { orderItemId: parseInt(id, 10), quantity: item?.quantity || 1 };
        }),
        reason: returnReason,
        additionalInfo: additionalInfo,
      };

      await accountApi.createReturn(returnData);

      toast({ 
        title: 'Demande de retour soumise',
        description: 'Votre demande a été créée avec succès.',
      });
      
      queryClient.invalidateQueries({ queryKey: ['userReturns'] });
      onSuccess();
      onOpenChange(false);

    } catch (err: any) {
      const errorMessage = err.message || 'Une erreur est survenue lors de la soumission.';
      updateState({ error: errorMessage });
      toast({ 
        title: 'Erreur de soumission',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-4">
            <Label htmlFor="order-select">Sélectionner une commande</Label>
            <Select onValueChange={(value) => updateState({ selectedOrderId: value, selectedItems: [] })} value={selectedOrderId}>
              <SelectTrigger id="order-select">
                <SelectValue placeholder="Choisissez une commande..." />
              </SelectTrigger>
              <SelectContent>
                {isLoadingOrders ? (
                  <div className="flex items-center justify-center p-2"><Loader2 className="h-4 w-4 animate-spin" /></div>
                ) : orders.map(order => (
                  <SelectItem key={order.id} value={order.id}>
                    Commande #{order.numericId} - {new Date(order.date).toLocaleDateString()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedOrder && (
              <Card className="bg-secondary/30">
                <CardHeader className="pb-2"><CardTitle className="text-base">Détails de la commande</CardTitle></CardHeader>
                <CardContent className="text-sm space-y-1">
                  <p><strong>Date:</strong> {new Date(selectedOrder.date).toLocaleDateString()}</p>
                  <p><strong>Statut:</strong> {selectedOrder.status}</p>
                  <p><strong>Total:</strong> {selectedOrder.total}</p>
                </CardContent>
              </Card>
            )}
          </div>
        );
      case 2:
        return (
          <div className="space-y-4">
            <Label>Sélectionner les articles à retourner</Label>
            <div className="space-y-3 max-h-[300px] overflow-y-auto rounded-md border p-3">
              {isLoadingDetails ? (
                <div className="text-center py-4"><Loader2 className="h-8 w-8 mx-auto animate-spin text-muted-foreground" /></div>
              ) : (selectedOrder?.items ?? []).length > 0 ? (
                (selectedOrder?.items ?? []).map(item => {
                  const itemIdStr = String(item.id);
                  return (
                    <div key={itemIdStr} className="flex items-center space-x-3 rounded-md p-2 hover:bg-secondary/50 transition-colors">
                      <Checkbox
                        id={itemIdStr}
                        checked={selectedItems.includes(itemIdStr)}
                        onCheckedChange={() => handleItemSelection(itemIdStr)}
                      />
                      <div className="h-12 w-12 bg-muted rounded flex-shrink-0 flex items-center justify-center">
                        {item.image ? <img src={item.image} alt={item.name} className="h-full w-full object-cover rounded" /> : <Package className="h-6 w-6 text-muted-foreground" />}
                      </div>
                      <div className="flex-grow">
                        <p className="font-medium text-sm">{item.name}</p>
                        <p className="text-xs text-muted-foreground">Qté: {item.quantity}</p>
                      </div>
                      <p className="text-sm font-semibold">{formatCurrency(parseFloat(item.price))}</p>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                  <p className="font-medium">Aucun article trouvé dans cette commande</p>
                  <p className="text-xs mt-1">
                    {selectedOrder ?
                      `Commande ${selectedOrder.id} chargée mais sans articles` :
                      'Commande non chargée'
                    }
                  </p>
                  {process.env.NODE_ENV === 'development' && (
                    <details className="mt-2 text-left">
                      <summary className="cursor-pointer text-xs">Débogage (dev)</summary>
                      <pre className="text-xs mt-1 bg-muted p-2 rounded overflow-auto">
                        {JSON.stringify({
                          selectedOrderId,
                          selectedOrder: selectedOrder ? {
                            id: selectedOrder.id,
                            hasItems: !!selectedOrder.items,
                            itemsLength: selectedOrder.items?.length || 0,
                            itemsKeys: selectedOrder.items?.[0] ? Object.keys(selectedOrder.items[0]) : []
                          } : null,
                          isLoadingDetails
                        }, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="returnReason">Raison du retour</Label>
              <Select onValueChange={(value) => updateState({ returnReason: value })} value={returnReason}>
                <SelectTrigger id="returnReason"><SelectValue placeholder="Sélectionnez une raison..." /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="defective">Article défectueux ou endommagé</SelectItem>
                  <SelectItem value="wrong_item">Mauvais article reçu</SelectItem>
                  <SelectItem value="not_as_described">Ne correspond pas à la description</SelectItem>
                  <SelectItem value="no_longer_needed">Plus besoin de l'article</SelectItem>
                  <SelectItem value="better_price">Meilleur prix trouvé ailleurs</SelectItem>
                  <SelectItem value="other">Autre</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="additionalInfo">Informations supplémentaires (optionnel)</Label>
              <Textarea id="additionalInfo" placeholder="Fournissez plus de détails si nécessaire..." value={additionalInfo} onChange={e => updateState({ additionalInfo: e.target.value })} />
            </div>
          </div>
        );
      case 4:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Confirmer votre demande</h3>
            <Card className="bg-secondary/30">
              <CardHeader><CardTitle className="text-base">Résumé du retour</CardTitle></CardHeader>
              <CardContent className="text-sm space-y-2">
                <p><strong>Commande :</strong> #{orders.find(o => o.id === selectedOrderId)?.numericId}</p>
                <p><strong>Articles sélectionnés :</strong> {selectedItems.length}</p>
                <p><strong>Raison :</strong> {returnReason}</p>
                {additionalInfo && <p><strong>Détails :</strong> {additionalInfo}</p>}
              </CardContent>
            </Card>
            <p className="text-xs text-muted-foreground">En soumettant, vous confirmez que les informations sont correctes. Notre équipe examinera votre demande sous peu.</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Créer une demande de retour</DialogTitle>
          <DialogDescription>Étape {step} sur 4: Suivez les instructions pour compléter votre demande.</DialogDescription>
        </DialogHeader>
        
        <div className="py-4 min-h-[250px]">
          {renderStepContent()}
        </div>
        
        {error && <div className="bg-destructive/10 border border-destructive/20 text-destructive text-sm p-3 -mt-2 mb-3 rounded-md flex items-center gap-2"><AlertCircle className="h-4 w-4"/>{error}</div>}
        
        <DialogFooter className="flex flex-row justify-between w-full">
          <Button variant="ghost" onClick={() => onOpenChange(false)} disabled={isSubmitting}>Annuler</Button>
          <div className="flex gap-2">
            {step > 1 && (
              <Button variant="outline" onClick={() => updateState({ step: step - 1 })} disabled={isSubmitting}>Précédent</Button>
            )}
            {step < 4 ? (
              <Button onClick={handleNextStep} disabled={isSubmitting || isLoadingDetails || (step === 1 && !selectedOrderId)}>Suivant</Button>
            ) : (
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Soumission...</> : 'Soumettre la demande'}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
