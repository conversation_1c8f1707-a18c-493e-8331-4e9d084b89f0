import axios from 'axios';
import { API_URL } from '@/config';
import { getAuthToken } from '@/utils/auth';

// Types
export interface PaymentMethod {
  id: number;
  name: string;
  provider: string;
  is_active: boolean;
  is_default: boolean;
  icon: string | null;
  description: string | null;
  position: number;
  fee_fixed: number;
  fee_percentage: number;
  created_at: string;
  updated_at: string;
}

export interface PaymentMethodDetail extends PaymentMethod {
  config: any;
}

export interface Transaction {
  id: number;
  order: number;
  payment_method: number;
  payment_method_name: string;
  transaction_id: string;
  amount: number;
  currency: string;
  status: string;
  status_display: string;
  error_message: string | null;
  created_at: string;
  updated_at: string;
  completed_at: string | null;
}

export interface TransactionDetail extends Transaction {
  provider_response: any;
  order_details: any;
}

export interface Invoice {
  id: number;
  order: number;
  invoice_number: string;
  status: string;
  status_display: string;
  issue_date: string;
  due_date: string | null;
  paid_date: string | null;
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total: number;
  pdf_file: string | null;
  created_at: string;
  updated_at: string;
}

export interface InvoiceDetail extends Invoice {
  notes: string | null;
  order_details: any;
}

export interface CustomerPaymentMethod {
  id: number;
  user: number;
  payment_method: number;
  payment_method_name: string;
  is_default: boolean;
  card_type: string | null;
  last_four: string | null;
  expiry_month: string | null;
  expiry_year: string | null;
  created_at: string;
  updated_at: string;
}

// API service
const paymentApi = {
  // Payment Methods
  getPaymentMethods: async (): Promise<PaymentMethod[]> => {
    const response = await axios.get(`${API_URL}/payments/payment-methods/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    // Check if response.data is an array or an object with results property
    return Array.isArray(response.data) ? response.data : (response.data.results || []);
  },

  getActivePaymentMethods: async (): Promise<PaymentMethod[]> => {
    const response = await axios.get(`${API_URL}/payments/payment-methods/active/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    // Check if response.data is an array or an object with results property
    return Array.isArray(response.data) ? response.data : (response.data.results || []);
  },

  getPaymentMethod: async (id: number): Promise<PaymentMethodDetail> => {
    const response = await axios.get(`${API_URL}/payments/payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  createPaymentMethod: async (data: Partial<PaymentMethodDetail>): Promise<PaymentMethodDetail> => {
    const response = await axios.post(`${API_URL}/payments/payment-methods/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  updatePaymentMethod: async (id: number, data: Partial<PaymentMethodDetail>): Promise<PaymentMethodDetail> => {
    const response = await axios.put(`${API_URL}/payments/payment-methods/${id}/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  deletePaymentMethod: async (id: number): Promise<void> => {
    await axios.delete(`${API_URL}/payments/payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Transactions
  getTransactions: async (params?: any): Promise<{ count: number, results: Transaction[] }> => {
    const response = await axios.get(`${API_URL}/payments/transactions/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      params
    });
    return response.data;
  },

  getMyTransactions: async (params?: any): Promise<{ count: number, results: Transaction[] }> => {
    const response = await axios.get(`${API_URL}/payments/transactions/my_transactions/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      params
    });
    return response.data;
  },

  getTransaction: async (id: number): Promise<TransactionDetail> => {
    const response = await axios.get(`${API_URL}/payments/transactions/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  refundTransaction: async (id: number, data: { amount?: number, reason?: string }): Promise<Transaction> => {
    const response = await axios.post(`${API_URL}/payments/transactions/${id}/refund/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Invoices
  getInvoices: async (params?: any): Promise<{ count: number, results: Invoice[] }> => {
    const response = await axios.get(`${API_URL}/payments/invoices/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      params
    });
    return response.data;
  },

  getMyInvoices: async (params?: any): Promise<{ count: number, results: Invoice[] }> => {
    const response = await axios.get(`${API_URL}/payments/invoices/my_invoices/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      params
    });
    return response.data;
  },

  getInvoice: async (id: number): Promise<InvoiceDetail> => {
    const response = await axios.get(`${API_URL}/payments/invoices/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  downloadInvoice: async (id: number): Promise<Blob> => {
    const response = await axios.get(`${API_URL}/payments/invoices/${id}/download/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      responseType: 'blob'
    });
    return response.data;
  },

  generateInvoiceForOrder: async (orderId: number): Promise<Invoice> => {
    const response = await axios.post(`${API_URL}/payments/invoices/generate_for_order/`, { order_id: orderId }, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Customer Payment Methods
  getCustomerPaymentMethods: async (): Promise<CustomerPaymentMethod[]> => {
    const response = await axios.get(`${API_URL}/payments/customer-payment-methods/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  getCustomerPaymentMethod: async (id: number): Promise<CustomerPaymentMethod> => {
    const response = await axios.get(`${API_URL}/payments/customer-payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  createCustomerPaymentMethod: async (data: any): Promise<CustomerPaymentMethod> => {
    const response = await axios.post(`${API_URL}/payments/customer-payment-methods/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  setDefaultCustomerPaymentMethod: async (id: number): Promise<CustomerPaymentMethod> => {
    const response = await axios.post(`${API_URL}/payments/customer-payment-methods/${id}/set_default/`, {}, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  deleteCustomerPaymentMethod: async (id: number): Promise<void> => {
    await axios.delete(`${API_URL}/payments/customer-payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Payment Processing
  processStripePayment: async (orderId: number, paymentMethodId: number, token: string): Promise<any> => {
    const response = await axios.post(`${API_URL}/payments/process/stripe/`, {
      order_id: orderId,
      payment_method_id: paymentMethodId,
      token
    }, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  processPayPalPayment: async (orderId: number, paymentMethodId: number, payerId: string, paymentId: string): Promise<any> => {
    const response = await axios.post(`${API_URL}/payments/process/paypal/`, {
      order_id: orderId,
      payment_method_id: paymentMethodId,
      payer_id: payerId,
      payment_id: paymentId
    }, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  }
};

export default paymentApi;
