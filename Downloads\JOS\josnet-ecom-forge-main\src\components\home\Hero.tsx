
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";

const Hero = () => {
  return (
    <section className="relative bg-gradient-to-r from-primary-dark to-primary overflow-hidden">
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1581090700227-1e37b190418e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')] bg-cover bg-center opacity-10"></div>
      <div className="container mx-auto px-4 py-24 md:py-32 relative">
        <div className="max-w-3xl">
          <div className="inline-block bg-accent/20 text-accent px-4 py-2 rounded-full text-sm font-semibold mb-6 animate-fade-in">
            Leader des solutions IT & Télécom au Burundi
          </div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 animate-fade-in tracking-tight">
            Solutions IT & Télécom <br />
            <span className="text-accent bg-gradient-to-r from-accent to-yellow-400 bg-clip-text text-transparent">Innovantes</span> pour Tous
          </h1>
          <p className="text-xl text-white/90 mb-8 animate-fade-up">
            Découvrez notre gamme complète de produits et services technologiques de pointe pour particuliers et entreprises.
          </p>
          <div className="flex flex-wrap gap-4 animate-fade-up">
            <Button className="bg-accent hover:bg-accent/80 text-white px-6 py-6 rounded-md flex items-center gap-2">
              Explorer les produits
              <ArrowRight size={18} />
            </Button>
            <Button variant="outline" className="bg-white/10 hover:bg-white/20 text-white border-white/30 px-6 py-6 rounded-md">
              Découvrir nos services
            </Button>
          </div>
          
          <div className="mt-12 flex items-center gap-4">
            <div className="flex -space-x-2">
              <img 
                src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=80&h=80&q=80" 
                alt="Client" 
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <img 
                src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=80&h=80&q=80" 
                alt="Client" 
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <img 
                src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=80&h=80&q=80" 
                alt="Client" 
                className="w-10 h-10 rounded-full border-2 border-white"
              />
            </div>
            <div className="text-white/90 text-sm">
              <span className="font-bold text-white">1000+</span> clients satisfaits
            </div>
          </div>
        </div>
      </div>

      {/* Wave bottom effect */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
          <path fill="#ffffff" fillOpacity="1" d="M0,128L60,117.3C120,107,240,85,360,90.7C480,96,600,128,720,138.7C840,149,960,139,1080,122.7C1200,107,1320,85,1380,74.7L1440,64L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z"></path>
        </svg>
      </div>
    </section>
  );
};

export default Hero;
