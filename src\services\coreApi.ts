/**
 * API pour les données dynamiques du site
 */

import axios from 'axios';
import { API_BASE_URL } from '@/config/api';

// Types pour les données
export interface SiteSettings {
  id: number;
  site_name: string;
  site_description: string;
  site_logo?: string;
  phone_primary: string;
  phone_secondary?: string;
  email_primary: string;
  email_secondary?: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  country: string;
  business_hours: string;
  facebook_url?: string;
  twitter_url?: string;
  linkedin_url?: string;
  instagram_url?: string;
  meta_description?: string;
  meta_keywords?: string;
}

export interface HomePageStats {
  id: number;
  clients_count: number;
  projects_count: number;
  experience_years: number;
  team_members: number;
  products_sold: number;
  cities_served: number;
  support_hours: string;
  satisfaction_rate: number;
  updated_at: string;
}

export interface Service {
  id: number;
  name: string;
  description: string;
  short_description: string;
  icon: string;
  color: string;
  slug: string;
  featured: boolean;
  order: number;
}

export interface Testimonial {
  id: number;
  client_name: string;
  client_title: string;
  client_company: string;
  client_photo_url?: string;
  content: string;
  rating: number;
  featured: boolean;
  order: number;
}

export interface Banner {
  id: number;
  title: string;
  subtitle?: string;
  description?: string;
  image_url?: string;
  button_text?: string;
  button_url?: string;
  position: string;
  is_visible: boolean;
  start_date: string;
  end_date?: string;
  order: number;
}

export interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
  featured: boolean;
  order: number;
}

export interface HomePageData {
  site_settings: SiteSettings | null;
  stats: HomePageStats;
  featured_services: Service[];
  featured_testimonials: Testimonial[];
  hero_banners: Banner[];
  last_updated: string;
}

export interface NewsletterSubscription {
  email: string;
  name?: string;
  frequency?: 'weekly' | 'monthly' | 'quarterly';
}

export interface ContactMessage {
  id?: number;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject: string;
  message: string;
  service_requested?: string;
  status?: 'new' | 'in_progress' | 'replied' | 'closed';
  created_at?: string;
  updated_at?: string;
  replied_at?: string;
}

// Configuration axios
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/core`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification aux requêtes
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);


// Configuration pour forcer l'utilisation des vraies données
const FORCE_REAL_DATA = true;

// Intercepteur pour gérer les erreurs
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.message);
    return Promise.reject(error);
  }
);

// API functions
const coreApi = {
  // Données consolidées de la page d'accueil
  getHomePageData: async (): Promise<HomePageData> => {
    if (FORCE_REAL_DATA) {
      const response = await apiClient.get('/homepage-data/');
      return response.data;
    } else {
      try {
        const response = await apiClient.get('/homepage-data/');
        return response.data;
      } catch (error) {
        console.error('Erreur API - Données réelles requises:', error);
        throw new Error('Impossible de récupérer les données depuis l\'API. Vérifiez que le serveur backend est démarré.');
      }
    }
  },

  // Paramètres du site
  getSiteSettings: async (): Promise<SiteSettings | null> => {
    try {
      const response = await apiClient.get('/site-settings/current/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du site:', error);
      return null;
    }
  },

  // Statistiques
  getStats: async (): Promise<HomePageStats> => {
    try {
      const response = await apiClient.get('/homepage-stats/current/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      // Retourner des stats par défaut
      return {
        id: 1,
        clients_count: 1000,
        projects_count: 5000,
        experience_years: 10,
        team_members: 25,
        products_sold: 10000,
        cities_served: 18,
        support_hours: '24/7',
        satisfaction_rate: 98,
        updated_at: new Date().toISOString()
      };
    }
  },

  // Statistiques en temps réel
  getLiveStats: async (): Promise<any> => {
    try {
      const response = await apiClient.get('/homepage-stats/live/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques en temps réel:', error);
      return null;
    }
  },

  // Services mis en avant
  getFeaturedServices: async (): Promise<Service[]> => {
    const response = await apiClient.get('/services/featured/');
    return response.data;
  },

  // Tous les services
  getServices: async (): Promise<Service[]> => {
    try {
      const response = await apiClient.get('/services/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de tous les services:', error);
      return [];
    }
  },

  // Témoignages mis en avant
  getFeaturedTestimonials: async (): Promise<Testimonial[]> => {
    const response = await apiClient.get('/testimonials/featured/');
    return response.data;
  },

  // Bannières par position
  getBannersByPosition: async (position: string = 'hero'): Promise<Banner[]> => {
    try {
      const response = await apiClient.get(`/banners/by_position/?position=${position}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des bannières:', error);
      return [];
    }
  },

  // FAQ mises en avant
  getFeaturedFAQ: async (): Promise<FAQ[]> => {
    try {
      const response = await apiClient.get('/faq/featured/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des FAQ:', error);
      return [];
    }
  },

  // FAQ par catégorie
  getFAQByCategory: async (category?: string): Promise<FAQ[]> => {
    try {
      const url = category ? `/faq/by_category/?category=${category}` : '/faq/by_category/';
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des FAQ par catégorie:', error);
      return [];
    }
  },

  // S'abonner à la newsletter
  subscribeToNewsletter: async (data: NewsletterSubscription): Promise<{ message: string }> => {
    try {
      const response = await apiClient.post('/newsletter/', data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'abonnement à la newsletter:', error);
      throw error;
    }
  },

  // Récupérer les messages de contact (admin)
  getContactMessages: async (): Promise<ContactMessage[]> => {
    try {
      console.log('🔍 Récupération des messages de contact...');

      // Vérifier si on a un token d'authentification
      const token = localStorage.getItem('accessToken');
      if (!token) {
        console.warn('⚠️ Pas de token d\'authentification - utilisation des données de fallback');
        throw new Error('Non authentifié');
      }

      const response = await apiClient.get('/contact/', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('✅ Messages récupérés depuis l\'API:', response.data);

      const messages = response.data.results || response.data || [];

      // Transformer les données si nécessaire
      return messages.map((msg: any) => ({
        id: msg.id,
        name: msg.name,
        email: msg.email,
        phone: msg.phone,
        company: msg.company,
        subject: msg.subject,
        message: msg.message,
        service_requested: msg.service_requested,
        status: msg.status || 'new',
        created_at: msg.created_at,
        updated_at: msg.updated_at,
        replied_at: msg.replied_at
      }));

    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération des messages:', error);

      if (error.response?.status === 401) {
        console.warn('🔐 Authentification requise - utilisation des données de fallback');
      }

      // Retourner des données de fallback pour le développement
      console.log('📝 Utilisation des données de fallback');
      return [
        {
          id: 1,
          name: 'Jean Baptiste Niyonkuru',
          email: '<EMAIL>',
          phone: '+257 79 123 456',
          company: 'TechCorp Burundi',
          subject: 'Demande de devis pour infrastructure réseau',
          message: 'Bonjour, nous souhaitons moderniser notre infrastructure réseau pour notre entreprise de 50 employés. Pouvez-vous nous proposer une solution complète incluant switches, routeurs et points d\'accès WiFi ?',
          service_requested: 'reseaux',
          status: 'new',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 2,
          name: 'Marie Claire Uwimana',
          email: '<EMAIL>',
          phone: '+257 68 987 654',
          company: 'StartupBI',
          subject: 'Formation équipe IT',
          message: 'Nous cherchons une formation pour notre équipe IT sur les dernières technologies cloud et DevOps. Quels sont vos programmes disponibles ?',
          service_requested: 'formation',
          status: 'in_progress',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date(Date.now() - 3600000).toISOString()
        },
        {
          id: 3,
          name: 'Pierre Ndayisenga',
          email: '<EMAIL>',
          phone: '+257 22 456 789',
          company: 'Hôtel Burundi',
          subject: 'Maintenance système POS',
          message: 'Notre système de point de vente rencontre des problèmes de lenteur. Nous avons besoin d\'une intervention de maintenance urgente.',
          service_requested: 'maintenance',
          status: 'replied',
          created_at: new Date(Date.now() - *********).toISOString(),
          updated_at: new Date(Date.now() - 7200000).toISOString(),
          replied_at: new Date(Date.now() - 7200000).toISOString()
        }
      ];
    }
  },

  // Mettre à jour le statut d'un message de contact
  updateContactMessageStatus: async (id: number, status: string): Promise<{ message: string }> => {
    try {
      const response = await apiClient.patch(`/contact/${id}/`, { status });
      return response.data;
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      // Simulation pour le développement
      return { message: 'Statut mis à jour avec succès' };
    }
  },

  // Envoyer un message de contact
  sendContactMessage: async (data: ContactMessage): Promise<{ message: string }> => {
    try {
      console.log('Envoi du message de contact:', data);
      const response = await apiClient.post('/contact/', data);
      console.log('Réponse du serveur:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Erreur lors de l\'envoi du message de contact:', error);
      console.error('Détails de l\'erreur:', error.response?.data);

      // Gérer les erreurs de validation Django REST Framework
      if (error.response?.data && error.response.status === 400) {
        const validationErrors = error.response.data;

        // Construire un message d'erreur lisible
        const errorMessages: string[] = [];

        Object.keys(validationErrors).forEach(field => {
          const fieldErrors = validationErrors[field];
          if (Array.isArray(fieldErrors)) {
            fieldErrors.forEach(errorMsg => {
              // Traduire les noms de champs en français
              const fieldTranslations: { [key: string]: string } = {
                'name': 'Nom',
                'email': 'Email',
                'phone': 'Téléphone',
                'company': 'Entreprise',
                'subject': 'Sujet',
                'message': 'Message',
                'service_requested': 'Service demandé'
              };

              const fieldName = fieldTranslations[field] || field;
              errorMessages.push(`${fieldName}: ${errorMsg}`);
            });
          }
        });

        if (errorMessages.length > 0) {
          throw new Error(errorMessages.join('\n'));
        }
      }

      // Autres types d'erreurs
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }

      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }

      // Erreur générique
      throw new Error('Une erreur est survenue lors de l\'envoi du message. Veuillez réessayer.');
    }
  },
};

export default coreApi;
