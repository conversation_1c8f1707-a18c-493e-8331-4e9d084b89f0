from rest_framework import viewsets, filters, status, generics, parsers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Avg, F, Sum, Count, Case, When, Value, IntegerField, Max
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from .utils import optimize_image
from tablib import Dataset
from .resources import (
    CategoryResource,
    ProductAttributeResource,
    AttributeValueResource,
    ProductResource,
    ProductVariantResource,
    InventoryResource
)

from .models import (
    Category,
    ProductAttribute,
    AttributeValue,
    Product,
    ProductImage,
    ProductAttributeValue,
    ProductVariant,
    Inventory,
    ProductReview,
    RelatedProduct
)
from .serializers import (
    CategorySerializer,
    CategoryTreeSerializer,
    ProductAttributeSerializer,
    AttributeValueSerializer,
    ProductImageSerializer,
    ProductImageBulkUploadSerializer,
    ProductAttributeValueSerializer,
    ProductVariantSerializer,
    ProductVariantCreateSerializer,
    VariantGeneratorSerializer,
    InventorySerializer,
    InventoryAdjustmentSerializer,
    StockMovementSerializer,
    StockReservationSerializer,
    StockReservationCreateSerializer,
    StockReservationReleaseSerializer,
    ProductReviewSerializer,
    ProductListSerializer,
    ProductDetailSerializer,
    ProductCreateUpdateSerializer,
    RelatedProductSerializer,
    InventoryStatsSerializer,
    InventoryListSerializer,
    BulkInventoryAdjustmentSerializer
)
from authentication.permissions import IsAdmin, IsStaffOrAdmin

class CategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for categories.
    """
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    lookup_field = 'slug'
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['list', 'retrieve', 'tree']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsStaffOrAdmin]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """
        Return a nested tree of categories.
        """
        # Get only root categories (those without a parent)
        root_categories = Category.objects.filter(parent=None, is_active=True)
        serializer = CategoryTreeSerializer(root_categories, many=True)
        return Response(serializer.data)

class ProductAttributeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product attributes.
    """
    queryset = ProductAttribute.objects.all()
    serializer_class = ProductAttributeSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    permission_classes = [IsStaffOrAdmin]

class AttributeValueViewSet(viewsets.ModelViewSet):
    """
    API endpoint for attribute values.
    """
    queryset = AttributeValue.objects.all()
    serializer_class = AttributeValueSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ['value']
    filterset_fields = ['attribute']
    ordering_fields = ['attribute__name', 'value']
    ordering = ['attribute__name', 'value']
    permission_classes = [IsStaffOrAdmin]

class ProductViewSet(viewsets.ModelViewSet):
    """
    API endpoint for products.
    """
    queryset = Product.objects.all()
    lookup_field = 'slug'
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description', 'short_description', 'sku']
    ordering_fields = ['name', 'price', 'created_at']
    ordering = ['-created_at']

    def get_object(self):
        """
        Override to allow lookup by both slug and ID.
        """
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        lookup_value = self.kwargs[lookup_url_kwarg]

        # Try to get by slug first
        try:
            if lookup_value.isdigit():
                # If it's a number, try to get by ID
                return get_object_or_404(self.get_queryset(), id=lookup_value)
            else:
                # Otherwise, get by slug
                return get_object_or_404(self.get_queryset(), slug=lookup_value)
        except:
            # Fallback to original behavior
            return super().get_object()

    def get_serializer_class(self):
        """
        Return appropriate serializer class based on the action.
        """
        if self.action == 'list':
            return ProductListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return ProductCreateUpdateSerializer
        return ProductDetailSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['list', 'retrieve', 'featured', 'search']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsStaffOrAdmin]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """
        Return featured products.
        """
        featured_products = Product.objects.filter(is_featured=True, status='published')
        page = self.paginate_queryset(featured_products)
        if page is not None:
            serializer = ProductListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ProductListSerializer(featured_products, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """
        Search products by query.
        """
        query = request.query_params.get('q', '')
        if not query:
            return Response(
                {"error": "Search query parameter 'q' is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        products = Product.objects.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query) |
            Q(short_description__icontains=query) |
            Q(sku__icontains=query),
            status='published'
        )

        page = self.paginate_queryset(products)
        if page is not None:
            serializer = ProductListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ProductListSerializer(products, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def related(self, request, slug=None):
        """
        Return related products based on categories.
        """
        product = self.get_object()
        categories = product.categories.all()

        # Get products in the same categories, excluding the current product
        related_products = Product.objects.filter(
            categories__in=categories,
            status='published'
        ).exclude(id=product.id).distinct()[:5]

        serializer = ProductListSerializer(related_products, many=True)
        return Response(serializer.data)

class ProductImageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product images.
    """
    queryset = ProductImage.objects.all()
    serializer_class = ProductImageSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['product', 'is_primary']
    permission_classes = [IsStaffOrAdmin]

    def perform_create(self, serializer):
        """
        Process and optimize the image before saving.
        """
        image = self.request.data.get('image')
        if image:
            # Optimize the image
            optimized_image = optimize_image(image)
            serializer.save(image=optimized_image)
        else:
            serializer.save()

    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """
        Set this image as the primary image for the product.
        """
        image = self.get_object()

        # Set all other images for this product as not primary
        ProductImage.objects.filter(product=image.product).update(is_primary=False)

        # Set this image as primary
        image.is_primary = True
        image.save()

        return Response({"status": "primary image set"})

    @action(detail=False, methods=['post'])
    def bulk_upload(self, request):
        """
        Upload multiple images for a product at once.
        """
        serializer = ProductImageBulkUploadSerializer(data=request.data)
        if serializer.is_valid():
            product = serializer.validated_data['product']
            images = serializer.validated_data['images']
            alt_text = serializer.validated_data.get('alt_text', '')

            # Check if this is the first image for the product
            is_first_image = not ProductImage.objects.filter(product=product).exists()

            created_images = []
            for i, image in enumerate(images):
                # Process and optimize the image
                optimized_image = optimize_image(image)

                # Create the image
                product_image = ProductImage.objects.create(
                    product=product,
                    image=optimized_image,
                    alt_text=alt_text,
                    is_primary=is_first_image and i == 0  # Set first image as primary if no images exist
                )
                created_images.append(product_image)

            # Return the created images
            response_serializer = ProductImageSerializer(created_images, many=True)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def reorder(self, request):
        """
        Reorder product images.
        """
        product_id = request.data.get('product')
        image_ids = request.data.get('image_ids', [])

        if not product_id or not image_ids:
            return Response(
                {"error": "Product ID and image IDs are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            product = Product.objects.get(id=product_id)
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Verify all images belong to the product
        product_images = list(ProductImage.objects.filter(product=product))
        product_image_ids = [img.id for img in product_images]

        if not all(img_id in product_image_ids for img_id in image_ids):
            return Response(
                {"error": "Some image IDs do not belong to the specified product"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the order (we'll use a custom field or the database order)
        # For now, we'll just ensure the first image is set as primary
        if image_ids:
            # Set all images as not primary
            ProductImage.objects.filter(product=product).update(is_primary=False)

            # Set the first image in the new order as primary
            ProductImage.objects.filter(id=image_ids[0]).update(is_primary=True)

        return Response({"status": "images reordered successfully"})

class ProductVariantViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product variants.
    """
    queryset = ProductVariant.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['product', 'is_active']
    search_fields = ['name', 'sku']
    permission_classes = [IsStaffOrAdmin]

    def get_serializer_class(self):
        """Return appropriate serializer class based on the action."""
        if self.action == 'create':
            return ProductVariantCreateSerializer
        elif self.action == 'generate_variants':
            return VariantGeneratorSerializer
        return ProductVariantSerializer

    @action(detail=False, methods=['post'])
    def generate_variants(self, request):
        """
        Generate product variants from attribute combinations.

        This endpoint takes a product ID and lists of attribute values,
        and generates all possible combinations of variants.

        Example request:
        {
            "product": 1,
            "attribute_values": [
                [1, 2],  # Color: Red, Blue
                [3, 4]   # Size: S, M
            ],
            "price_adjustment": 0,
            "is_active": true
        }

        This will generate 4 variants: Red/S, Red/M, Blue/S, Blue/M
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            product = serializer.validated_data['product']
            attribute_groups = serializer.validated_data['attribute_values']
            price_adjustment = serializer.validated_data.get('price_adjustment', 0)
            is_active = serializer.validated_data.get('is_active', True)

            # Generate all combinations of attribute values
            import itertools
            combinations = list(itertools.product(*attribute_groups))

            created_variants = []
            for combo in combinations:
                # Check if this combination already exists
                existing_variants = ProductVariant.objects.filter(product=product)

                # For each existing variant, check if it has the same attribute values
                skip_variant = False
                for variant in existing_variants:
                    variant_attrs = set(variant.attributes.values_list('id', flat=True))
                    combo_attrs = set(attr.id for attr in combo)
                    if variant_attrs == combo_attrs:
                        skip_variant = True
                        break

                if skip_variant:
                    continue

                # Generate name from attribute values
                name_parts = [attr.value for attr in combo]
                name = ' / '.join(name_parts)

                # Generate SKU
                import uuid
                sku = f"{product.sku}-{uuid.uuid4().hex[:6].upper()}"

                # Create the variant
                variant = ProductVariant.objects.create(
                    product=product,
                    name=name,
                    sku=sku,
                    price_adjustment=price_adjustment,
                    is_active=is_active
                )

                # Add attribute values
                variant.attributes.set(combo)

                # Create inventory
                Inventory.objects.create(variant=variant)

                created_variants.append(variant)

            # Return the created variants
            response_serializer = ProductVariantSerializer(created_variants, many=True)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """
        Get variants for a specific product.
        """
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response(
                {"error": "Product ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        variants = ProductVariant.objects.filter(product_id=product_id)
        serializer = ProductVariantSerializer(variants, many=True)
        return Response(serializer.data)

class InventoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for inventory management.
    """
    queryset = Inventory.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product', 'variant']
    search_fields = ['product__name', 'variant__name', 'product__sku', 'variant__sku']
    ordering_fields = ['quantity', 'reserved_quantity', 'available_quantity', 'last_checked']
    ordering = ['-last_checked']
    permission_classes = [IsStaffOrAdmin]

    def get_serializer_class(self):
        """Return appropriate serializer class based on the action."""
        if self.action == 'adjust_stock':
            return InventoryAdjustmentSerializer
        elif self.action == 'reserve_stock':
            return StockReservationCreateSerializer
        elif self.action == 'release_reservation':
            return StockReservationReleaseSerializer
        elif self.action == 'list':
            return InventoryListSerializer
        elif self.action == 'stats':
            return InventoryStatsSerializer
        elif self.action == 'bulk_adjust':
            return BulkInventoryAdjustmentSerializer
        return InventorySerializer

    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = super().get_queryset()

        # Filter by stock status
        status = self.request.query_params.get('status')
        if status == 'in_stock':
            queryset = queryset.filter(quantity__gt=0)
        elif status == 'low_stock':
            queryset = queryset.filter(
                quantity__gt=0,
                quantity__lte=F('low_stock_threshold')
            )
        elif status == 'out_of_stock':
            queryset = queryset.filter(quantity=0)

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(
                Q(product__categories__id=category) |
                Q(variant__product__categories__id=category)
            ).distinct()

        return queryset

    @action(detail=True, methods=['post'])
    def adjust_stock(self, request, pk=None):
        """
        Adjust stock quantity.

        Example request:
        {
            "quantity": 10,
            "reason": "purchase",
            "reference": "PO-12345",
            "notes": "Initial stock"
        }
        """
        inventory = self.get_object()
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            quantity = serializer.validated_data['quantity']
            reason = serializer.validated_data['reason']
            reference = serializer.validated_data.get('reference')
            notes = serializer.validated_data.get('notes')

            # Adjust stock and create movement record
            movement = inventory.adjust_stock(
                quantity=quantity,
                reason=reason,
                reference=reference,
                user=request.user
            )

            # Update notes if provided
            if notes:
                movement.notes = notes
                movement.save()

            # Return the updated inventory
            inventory_serializer = InventorySerializer(inventory)
            return Response(inventory_serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def reserve_stock(self, request, pk=None):
        """
        Reserve stock for an order or other purpose.

        Example request:
        {
            "quantity": 5,
            "reference": "ORDER-12345",
            "notes": "Customer order",
            "expires_at": "2023-12-31T23:59:59Z"
        }
        """
        inventory = self.get_object()
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            quantity = serializer.validated_data['quantity']
            reference = serializer.validated_data.get('reference')
            notes = serializer.validated_data.get('notes')
            expires_at = serializer.validated_data.get('expires_at')

            # Check if we have enough stock
            if quantity > inventory.available_quantity:
                return Response(
                    {"error": f"Not enough stock. Available: {inventory.available_quantity}, Requested: {quantity}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create reservation
            reservation = StockReservation.objects.create(
                inventory=inventory,
                quantity=quantity,
                reference=reference,
                notes=notes,
                expires_at=expires_at,
                created_by=request.user
            )

            # Update inventory
            inventory.reserved_quantity += quantity
            inventory.save()

            # Return the reservation
            reservation_serializer = StockReservationSerializer(reservation)
            return Response(reservation_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def release_reservation(self, request, pk=None):
        """
        Release a stock reservation.

        Example request (by reservation ID):
        {
            "reservation_id": 1
        }

        Example request (by reference):
        {
            "reference": "ORDER-12345"
        }

        Example request (by quantity):
        {
            "quantity": 5
        }
        """
        inventory = self.get_object()
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            reservation_id = serializer.validated_data.get('reservation_id')
            reference = serializer.validated_data.get('reference')
            quantity = serializer.validated_data.get('quantity')

            # At least one parameter is required
            if not any([reservation_id, reference, quantity]):
                return Response(
                    {"error": "At least one of reservation_id, reference, or quantity is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Release the reservation
            success = inventory.release_reservation(
                reservation_id=reservation_id,
                reference=reference,
                quantity=quantity,
                user=request.user
            )

            if success:
                # Return the updated inventory
                inventory_serializer = InventorySerializer(inventory)
                return Response(inventory_serializer.data)
            else:
                return Response(
                    {"error": "Failed to release reservation. Check if the reservation exists."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """
        Get products with low stock.
        """
        low_stock_items = Inventory.objects.filter(
            quantity__gt=0,  # Has some stock
            quantity__lte=F('low_stock_threshold')  # But below threshold
        )

        serializer = InventoryListSerializer(low_stock_items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def out_of_stock(self, request):
        """
        Get products that are out of stock.
        """
        out_of_stock_items = Inventory.objects.filter(quantity=0)

        serializer = InventoryListSerializer(out_of_stock_items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Get inventory statistics.
        """
        # Get all inventory items
        inventory_items = Inventory.objects.all()

        # Calculate statistics
        stats = {
            'total_products': inventory_items.filter(product__isnull=False).count(),
            'total_variants': inventory_items.filter(variant__isnull=False).count(),
            'total_quantity': inventory_items.aggregate(total=Sum('quantity'))['total'] or 0,
            'total_reserved': inventory_items.aggregate(total=Sum('reserved_quantity'))['total'] or 0,
            'total_available': inventory_items.aggregate(
                total=Sum(F('quantity') - F('reserved_quantity'))
            )['total'] or 0,
            'in_stock': inventory_items.filter(quantity__gt=0).count(),
            'low_stock': inventory_items.filter(
                quantity__gt=0,
                quantity__lte=F('low_stock_threshold')
            ).count(),
            'out_of_stock': inventory_items.filter(quantity=0).count(),
            'total_value': 0,  # Will calculate below
            'reserved_value': 0  # Will calculate below
        }

        # Calculate total value and reserved value
        total_value = 0
        reserved_value = 0

        for item in inventory_items:
            price = 0
            if item.product:
                price = item.product.current_price
            elif item.variant:
                price = item.variant.price

            total_value += price * item.quantity
            reserved_value += price * item.reserved_quantity

        stats['total_value'] = total_value
        stats['reserved_value'] = reserved_value

        serializer = InventoryStatsSerializer(stats)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_adjust(self, request):
        """
        Adjust stock for multiple inventory items at once.

        Example request:
        {
            "adjustments": [
                {
                    "inventory_id": 1,
                    "quantity": 10,
                    "reason": "purchase",
                    "reference": "PO-12345",
                    "notes": "Initial stock"
                },
                {
                    "inventory_id": 2,
                    "quantity": -5,
                    "reason": "sale",
                    "reference": "ORDER-6789"
                }
            ]
        }
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.info("--- BULK ADJUSTMENT ATTEMPT ---")
        logger.info(f"Request Data: {request.data}")

        serializer = self.get_serializer(data=request.data)
        logger.info(f"Using serializer: {serializer.__class__.__name__}")

        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            logger.error("!!! UNEXPECTED ERROR DURING VALIDATION !!!", exc_info=True)
            return Response(
                {"error": "An unexpected server error occurred during data validation."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        adjustments = serializer.validated_data['adjustments']
        results = []

        for adjustment_data in adjustments:
            inventory_id = None
            try:
                inventory_id = adjustment_data['inventory_id']
                quantity = adjustment_data['quantity']
                reason = adjustment_data.get('reason', 'adjustment')
                reference = adjustment_data.get('reference')
                notes = adjustment_data.get('notes')

                inventory = Inventory.objects.get(id=inventory_id)
                
                movement = inventory.adjust_stock(
                    quantity=quantity,
                    reason=reason,
                    reference=reference,
                    user=request.user
                )

                if notes:
                    movement.notes = notes
                    movement.save()

                results.append({
                    'inventory_id': inventory_id,
                    'success': True,
                    'movement_id': movement.id
                })
            except Inventory.DoesNotExist:
                results.append({
                    'inventory_id': inventory_id,
                    'success': False,
                    'error': f"Inventory with id {inventory_id} not found."
                })
            except KeyError as e:
                results.append({
                    'inventory_id': inventory_id,
                    'success': False,
                    'error': f"Missing key in adjustment data: {str(e)}"
                })
            except Exception as e:
                logger.error(f"Error processing adjustment for inventory_id {inventory_id}: {e}", exc_info=True)
                results.append({
                    'inventory_id': inventory_id,
                    'success': False,
                    'error': f"An unexpected error occurred: {str(e)}"
                })

        return Response(results)

    @action(detail=False, methods=['post'])
    def update_thresholds(self, request):
        """
        Update low stock thresholds for multiple inventory items at once.

        Example request:
        {
            "inventory_ids": [1, 2, 3],
            "threshold": 10
        }
        """
        # Validate input
        if 'inventory_ids' not in request.data or 'threshold' not in request.data:
            return Response(
                {"error": "Both inventory_ids and threshold are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        inventory_ids = request.data['inventory_ids']
        threshold = request.data['threshold']

        # Validate threshold
        try:
            threshold = int(threshold)
            if threshold < 0:
                raise ValueError("Threshold must be a positive integer")
        except (ValueError, TypeError):
            return Response(
                {"error": "Threshold must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update thresholds
        results = []
        for inventory_id in inventory_ids:
            try:
                inventory = Inventory.objects.get(id=inventory_id)
                inventory.low_stock_threshold = threshold
                inventory.save()

                results.append({
                    'inventory_id': inventory_id,
                    'success': True,
                    'new_threshold': threshold
                })
            except Inventory.DoesNotExist:
                results.append({
                    'inventory_id': inventory_id,
                    'success': False,
                    'error': "Inventory not found"
                })
            except Exception as e:
                results.append({
                    'inventory_id': inventory_id,
                    'success': False,
                    'error': str(e)
                })

        return Response(results)


class ImportExportView(generics.GenericAPIView):
    """
    Base view for import/export functionality.
    """
    parser_classes = [parsers.MultiPartParser, parsers.FormParser]
    permission_classes = [IsStaffOrAdmin]
    resource_class = None

    def get(self, request, *args, **kwargs):
        """
        Export data as CSV.
        """
        resource = self.resource_class()
        dataset = resource.export()
        response = HttpResponse(dataset.csv, content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{self.get_filename()}.csv"'
        return response

    def post(self, request, *args, **kwargs):
        """
        Import data from CSV.
        """
        resource = self.resource_class()
        dataset = Dataset()

        if 'file' not in request.FILES:
            return Response(
                {"error": "No file uploaded"},
                status=status.HTTP_400_BAD_REQUEST
            )

        file = request.FILES['file']

        # Check file extension
        if not file.name.endswith('.csv'):
            return Response(
                {"error": "Only CSV files are supported"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Load the dataset from the file
        try:
            dataset.load(file.read().decode('utf-8'), format='csv')
        except Exception as e:
            return Response(
                {"error": f"Error loading CSV file: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Import the data
        result = resource.import_data(dataset, dry_run=False)

        # Return the result
        return Response({
            "total_rows": len(dataset),
            "imported": result.total_rows,
            "added": result.totals['new'],
            "updated": result.totals['update'],
            "deleted": result.totals['delete'],
            "skipped": result.totals['skip'],
            "errors": result.totals['error'],
            "error_messages": [str(err) for err in result.row_errors()]
        })

    def get_filename(self):
        """
        Get the filename for the exported file.
        """
        return self.resource_class.Meta.model.__name__.lower() + 's'

class CategoryImportExportView(ImportExportView):
    """
    Import/export categories.
    """
    resource_class = CategoryResource

class ProductAttributeImportExportView(ImportExportView):
    """
    Import/export product attributes.
    """
    resource_class = ProductAttributeResource

class AttributeValueImportExportView(ImportExportView):
    """
    Import/export attribute values.
    """
    resource_class = AttributeValueResource

class ProductImportExportView(ImportExportView):
    """
    Import/export products.
    """
    resource_class = ProductResource

class ProductVariantImportExportView(ImportExportView):
    """
    Import/export product variants.
    """
    resource_class = ProductVariantResource

class InventoryImportExportView(ImportExportView):
    """
    Import/export inventory.
    """
    resource_class = InventoryResource

class ProductReviewViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product reviews.
    """
    queryset = ProductReview.objects.select_related('user', 'product', 'order').all()
    serializer_class = ProductReviewSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product', 'user', 'is_approved', 'rating', 'is_verified_purchase']
    search_fields = ['title', 'comment', 'pros', 'cons']
    ordering_fields = ['created_at', 'rating', 'helpful_count']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter to show only approved reviews for non-staff users."""
        queryset = super().get_queryset()

        # Pour les utilisateurs non-staff, ne montrer que les avis approuvés
        try:
            if not (self.request.user.is_authenticated and
                    hasattr(self.request.user, 'is_staff_member') and
                    hasattr(self.request.user, 'is_admin') and
                    (self.request.user.is_staff_member or self.request.user.is_admin)):
                queryset = queryset.filter(is_approved=True)
        except AttributeError:
            # Si l'utilisateur n'a pas ces attributs, filtrer sur les avis approuvés
            queryset = queryset.filter(is_approved=True)

        return queryset

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['list', 'retrieve', 'stats']:
            permission_classes = [AllowAny]
        elif self.action in ['create', 'vote_helpful']:
            permission_classes = [IsAuthenticated]
        elif self.action in ['update', 'partial_update']:
            permission_classes = [IsAuthenticated]  # Users can edit their own reviews
        else:
            permission_classes = [IsStaffOrAdmin]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        """
        Set the user when creating a review and check for verified purchase.
        """
        # Vérifier si l'utilisateur a acheté ce produit
        product = serializer.validated_data['product']
        user = self.request.user

        # Vérifier si l'utilisateur a déjà écrit un avis pour ce produit
        existing_review = ProductReview.objects.filter(
            user=user,
            product=product
        ).first()

        if existing_review:
            from rest_framework.exceptions import ValidationError
            raise ValidationError({
                'detail': 'Vous avez déjà écrit un avis pour ce produit. Vous pouvez le modifier depuis votre profil.'
            })

        # Chercher une commande contenant ce produit
        from orders.models import Order, OrderItem
        order = Order.objects.filter(
            user=user,
            status__in=['delivered', 'completed'],
            items__product=product
        ).first()

        try:
            serializer.save(user=user, order=order)
        except Exception as e:
            from rest_framework.exceptions import ValidationError
            if 'UNIQUE constraint failed' in str(e):
                raise ValidationError({
                    'detail': 'Vous avez déjà écrit un avis pour ce produit.'
                })
            raise e

    def update(self, request, *args, **kwargs):
        """Allow users to update only their own reviews."""
        review = self.get_object()
        if review.user != request.user and not (request.user.is_staff_member or request.user.is_admin):
            return Response(
                {'error': 'You can only edit your own reviews'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().update(request, *args, **kwargs)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def vote_helpful(self, request, pk=None):
        """Vote on whether a review is helpful."""
        review = self.get_object()
        is_helpful = request.data.get('is_helpful')

        if is_helpful is None:
            return Response(
                {'error': 'is_helpful field is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Empêcher les utilisateurs de voter sur leurs propres avis
        if review.user == request.user:
            return Response(
                {'error': 'You cannot vote on your own review'},
                status=status.HTTP_400_BAD_REQUEST
            )

        from .models import ReviewHelpfulness

        # Créer ou mettre à jour le vote
        vote, created = ReviewHelpfulness.objects.update_or_create(
            review=review,
            user=request.user,
            defaults={'is_helpful': is_helpful}
        )

        # Mettre à jour les compteurs
        review.helpful_count = ReviewHelpfulness.objects.filter(
            review=review, is_helpful=True
        ).count()
        review.not_helpful_count = ReviewHelpfulness.objects.filter(
            review=review, is_helpful=False
        ).count()
        review.save()

        return Response({
            'message': 'Vote recorded successfully',
            'helpful_count': review.helpful_count,
            'not_helpful_count': review.not_helpful_count
        })

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get review statistics for a product."""
        product_id = request.query_params.get('product')
        if not product_id:
            return Response(
                {'error': 'product parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        from django.db.models import Avg, Count, Q

        reviews = ProductReview.objects.filter(
            product_id=product_id,
            is_approved=True
        )

        stats = reviews.aggregate(
            total_reviews=Count('id'),
            average_rating=Avg('rating'),
            verified_purchases=Count('id', filter=Q(is_verified_purchase=True))
        )

        # Distribution des notes
        rating_distribution = {}
        for i in range(1, 6):
            rating_distribution[str(i)] = reviews.filter(rating=i).count()

        return Response({
            'total_reviews': stats['total_reviews'] or 0,
            'average_rating': round(stats['average_rating'] or 0, 1),
            'rating_distribution': rating_distribution,
            'verified_purchases_count': stats['verified_purchases'] or 0
        })

    @action(detail=False, methods=['get'])
    def my_reviews(self, request):
        """
        Return reviews created by the current user.
        """
        reviews = ProductReview.objects.filter(user=request.user)
        serializer = self.get_serializer(reviews, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve a review.
        """
        review = self.get_object()
        review.is_approved = True
        review.save()
        return Response({"status": "review approved"})

    @action(detail=False, methods=['get'], url_path='can-review', permission_classes=[])
    def can_review(self, request):
        """
        Vérifier si l'utilisateur peut écrire un avis pour un produit.
        """
        if not request.user.is_authenticated:
            return Response({'can_review': False, 'reason': 'Vous devez être connecté pour écrire un avis.'})

        product_id = request.query_params.get('product')
        if not product_id:
            return Response({'error': 'Product ID is required'}, status=400)

        try:
            product = Product.objects.get(id=product_id)
        except Product.DoesNotExist:
            return Response({'error': 'Product not found'}, status=404)

        # Vérifier si l'utilisateur a déjà écrit un avis
        existing_review = ProductReview.objects.filter(
            user=request.user,
            product=product
        ).first()

        if existing_review:
            return Response({
                'can_review': False,
                'reason': 'Vous avez déjà écrit un avis pour ce produit.',
                'existing_review_id': existing_review.id
            })

        return Response({
            'can_review': True,
            'reason': 'Vous pouvez écrire un avis pour ce produit.'
        })

class RelatedProductViewSet(viewsets.ModelViewSet):
    """
    API endpoint for related products.
    """
    queryset = RelatedProduct.objects.all()
    serializer_class = RelatedProductSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['product', 'related_product', 'relation_type']
    permission_classes = [IsStaffOrAdmin]

    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = super().get_queryset()
        product_id = self.request.query_params.get('product')
        if product_id:
            queryset = queryset.filter(product_id=product_id)
        return queryset

    def perform_create(self, serializer):
        """
        Create a related product with position.
        """
        # Verify that the product is not related to itself
        if serializer.validated_data['product'] == serializer.validated_data['related_product']:
            from rest_framework import serializers as drf_serializers
            raise drf_serializers.ValidationError({"error": "A product cannot be related to itself."})

        # Determine the position
        relation_type = serializer.validated_data['relation_type']
        product = serializer.validated_data['product']
        max_position = RelatedProduct.objects.filter(
            product=product,
            relation_type=relation_type
        ).aggregate(max_pos=Max('position'))['max_pos'] or 0

        serializer.save(position=max_position + 1)
