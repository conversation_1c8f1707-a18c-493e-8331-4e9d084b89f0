from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import json

@api_view(['GET'])
@permission_classes([AllowAny])  # Permettre l'accès sans authentification
def dashboard_view(request):
    """
    Vue pour l'endpoint /api/v1/analytics/dashboard/
    Retourne les données du tableau de bord
    """
    try:
        # Données du dashboard en Francs Burundais
        dashboard_data = {
            "stats": {
                "total_sales": 58200000,  # En FBU
                "total_orders": 458,
                "total_customers": 1923,
                "total_products": 156,
                "change_sales": "+15%",
                "change_orders": "+10%",
                "change_customers": "+18%",
                "change_products": "+5",
                "change_type_sales": "positive",
                "change_type_orders": "positive",
                "change_type_customers": "positive",
                "change_type_products": "positive"
            },
            "recent_orders": [
                {
                    "id": "#ORD-12345",
                    "customer": "<PERSON>",
                    "date": "15/05/2025",
                    "status": "Livré",
                    "amount": "769,978 FBU"
                },
                {
                    "id": "#ORD-12344",
                    "customer": "Sophie Martin",
                    "date": "14/05/2025",
                    "status": "En cours",
                    "amount": "285,890 FBU"
                },
                {
                    "id": "#ORD-12343",
                    "customer": "Pierre Durand",
                    "date": "14/05/2025",
                    "status": "En attente",
                    "amount": "196,900 FBU"
                },
                {
                    "id": "#ORD-12342",
                    "customer": "Marie Lambert",
                    "date": "13/05/2025",
                    "status": "Livré",
                    "amount": "438,780 FBU"
                },
                {
                    "id": "#ORD-12341",
                    "customer": "Thomas Bernard",
                    "date": "13/05/2025",
                    "status": "Annulé",
                    "amount": "1,011,780 FBU"
                }
            ],
            "low_stock_items": [
                {
                    "id": "PRD-001",
                    "name": "Routeur Wi-Fi 6",
                    "stock": 3,
                    "threshold": 10
                },
                {
                    "id": "PRD-015",
                    "name": "Câble HDMI 2.1",
                    "stock": 5,
                    "threshold": 15
                },
                {
                    "id": "PRD-023",
                    "name": "Disque SSD 1TB",
                    "stock": 2,
                    "threshold": 8
                },
                {
                    "id": "PRD-042",
                    "name": "Adaptateur USB-C",
                    "stock": 4,
                    "threshold": 12
                }
            ],
            "pending_tasks": [
                {
                    "id": 1,
                    "task": "Mise à jour des descriptions produits",
                    "priority": "Haute"
                },
                {
                    "id": 2,
                    "task": "Validation des retours clients",
                    "priority": "Moyenne"
                },
                {
                    "id": 3,
                    "task": "Répondre aux avis clients récents",
                    "priority": "Basse"
                },
                {
                    "id": 4,
                    "task": "Préparer la promotion de mai",
                    "priority": "Haute"
                }
            ],
            "monthly_sales": [
                {"month": "Jan", "value": 27500000},
                {"month": "Fév", "value": 21560000},
                {"month": "Mar", "value": 35640000},
                {"month": "Avr", "value": 23980000},
                {"month": "Mai", "value": 38500000},
                {"month": "Juin", "value": 31240000}
            ]
        }
        
        return Response(dashboard_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response(
            {
                "error": f"Erreur dashboard: {str(e)}",
                "message": "Erreur interne du serveur"
            }, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# Version alternative avec JsonResponse simple
@csrf_exempt
@require_http_methods(["GET"])
def simple_dashboard_view(request):
    """
    Version simple avec JsonResponse
    """
    try:
        data = {
            "stats": {
                "total_sales": 58200000,
                "total_orders": 458,
                "total_customers": 1923,
                "total_products": 156,
                "change_sales": "+15%",
                "change_orders": "+10%",
                "change_customers": "+18%",
                "change_products": "+5",
                "change_type_sales": "positive",
                "change_type_orders": "positive",
                "change_type_customers": "positive",
                "change_type_products": "positive"
            },
            "recent_orders": [],
            "low_stock_items": [],
            "pending_tasks": [],
            "monthly_sales": []
        }
        
        response = JsonResponse(data)
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        
        return response
        
    except Exception as e:
        error_response = JsonResponse(
            {
                "error": f"Erreur: {str(e)}"
            }, 
            status=500
        )
        error_response["Access-Control-Allow-Origin"] = "*"
        return error_response
