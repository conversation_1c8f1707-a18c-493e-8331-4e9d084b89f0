.ProseMirror {
  outline: none;
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1rem 0;
}

.ProseMirror blockquote {
  border-left: 3px solid #ddd;
  padding-left: 1rem;
  font-style: italic;
  margin: 1rem 0;
}

.ProseMirror pre {
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  padding: 0.75rem;
  font-family: monospace;
  overflow-x: auto;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid #ddd;
  margin: 2rem 0;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5rem;
}

.ProseMirror h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem;
}

.ProseMirror a {
  color: #0070f3;
  text-decoration: underline;
}

.ProseMirror a:hover {
  text-decoration: none;
}
