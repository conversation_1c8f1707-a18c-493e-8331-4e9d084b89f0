// API base URL
export const API_BASE_URL = 'http://localhost:8000/api/v1';

// Generic API client for making HTTP requests
const api = {
  get: async <T>(endpoint: string, options: RequestInit = {}): Promise<T> => {
    const accessToken = localStorage.getItem('accessToken');
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Ajouter l'Authorization header seulement si le token existe
    if (accessToken && accessToken !== 'null' && accessToken !== 'undefined') {
      defaultHeaders['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        ...defaultHeaders,
        ...(options.headers || {}),
      },
      ...options,
    });

    if (!response.ok) {
      throw await handleApiError(response);
    }

    return await response.json();
  },

  post: async <T>(endpoint: string, data: any, options: RequestInit = {}): Promise<T> => {
    const accessToken = localStorage.getItem('accessToken');
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Ajouter l'Authorization header seulement si le token existe
    if (accessToken && accessToken !== 'null' && accessToken !== 'undefined') {
      defaultHeaders['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        ...defaultHeaders,
        ...(options.headers || {}),
      },
      body: JSON.stringify(data),
      ...options,
    });

    if (!response.ok) {
      throw await handleApiError(response);
    }

    return await response.json();
  },

  put: async <T>(endpoint: string, data: any, options: RequestInit = {}): Promise<T> => {
    const accessToken = localStorage.getItem('accessToken');
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Ajouter l'Authorization header seulement si le token existe
    if (accessToken && accessToken !== 'null' && accessToken !== 'undefined') {
      defaultHeaders['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'PUT',
      headers: {
        ...defaultHeaders,
        ...(options.headers || {}),
      },
      body: JSON.stringify(data),
      ...options,
    });

    if (!response.ok) {
      throw await handleApiError(response);
    }

    return await response.json();
  },

  patch: async <T>(endpoint: string, data: any, options: RequestInit = {}): Promise<T> => {
    const accessToken = localStorage.getItem('accessToken');
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Ajouter l'Authorization header seulement si le token existe
    if (accessToken && accessToken !== 'null' && accessToken !== 'undefined') {
      defaultHeaders['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'PATCH',
      headers: {
        ...defaultHeaders,
        ...(options.headers || {}),
      },
      body: JSON.stringify(data),
      ...options,
    });

    if (!response.ok) {
      throw await handleApiError(response);
    }

    return await response.json();
  },

  delete: async <T>(endpoint: string, options: RequestInit = {}): Promise<T> => {
    const accessToken = localStorage.getItem('accessToken');
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Ajouter l'Authorization header seulement si le token existe
    if (accessToken && accessToken !== 'null' && accessToken !== 'undefined') {
      defaultHeaders['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'DELETE',
      headers: {
        ...defaultHeaders,
        ...(options.headers || {}),
      },
      ...options,
    });

    if (!response.ok) {
      throw await handleApiError(response);
    }

    return await response.json();
  },
};

export default api;

// Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  confirm_password: string;
}

export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  is_verified: boolean;
  [key: string]: any; // For additional fields
}

export interface AuthResponse {
  user: User;
  access: string;
  refresh: string;
  message?: string;
}

export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
}

// Helper function to handle API errors
export const handleApiError = async (response: Response): Promise<ApiError> => {
  try {
    const data = await response.json();

    // Si erreur 401 ou 404 sur les endpoints utilisateur, nettoyer le localStorage
    if (response.status === 401) {
      console.warn('🔑 Token expiré ou invalide, nettoyage de la session');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');

      // Rediriger vers login si on n'y est pas déjà
      if (window.location.pathname !== '/login' && window.location.pathname !== '/register') {
        window.location.href = '/login?expired=true';
      }
    } else if (response.status === 404 && response.url?.includes('/auth/profile')) {
      console.warn('🗑️ Utilisateur supprimé de la base de données, nettoyage de la session');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');

      // Rediriger vers login avec message spécifique
      if (window.location.pathname !== '/login' && window.location.pathname !== '/register') {
        window.location.href = '/login?deleted=true';
      }
    }

    return {
      message: data.detail || data.message || 'Une erreur est survenue',
      errors: data
    };
  } catch (error) {
    return {
      message: 'Une erreur est survenue lors de la communication avec le serveur'
    };
  }
};

// Helper function to get auth header
export const getAuthHeader = () => {
  const token = localStorage.getItem('accessToken');
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Authentication API service
export const authApi = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    console.log('🔄 Tentative de connexion via API:', credentials.email);

    const response = await fetch(`${API_BASE_URL}/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      console.error('❌ Erreur de connexion API:', response.status, response.statusText);
      throw await handleApiError(response);
    }

    const result = await response.json();
    console.log('✅ Connexion réussie via API');
    return result;
  },

  // Register user
  register: async (userData: RegisterData): Promise<AuthResponse> => {
    console.log('🔄 Tentative d\'inscription via API:', userData.email);

    const response = await fetch(`${API_BASE_URL}/auth/register/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      console.error('❌ Erreur d\'inscription API:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('❌ Détails de l\'erreur:', errorText);

      // Re-parse the response for error handling
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { detail: errorText };
      }

      throw {
        message: errorData.detail || 'Une erreur est survenue',
        errors: errorData
      };
    }

    const result = await response.json();
    console.log('✅ Inscription réussie via API');
    return result;
  },

  // Logout user
  logout: async (refreshToken: string): Promise<void> => {
    try {
      if (refreshToken) {
        const response = await fetch(`${API_BASE_URL}/auth/logout/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          },
          body: JSON.stringify({ refresh: refreshToken }),
        });

        if (!response.ok) {
          console.warn(`Logout API returned status ${response.status}`);
        } else {
          console.log('✅ Déconnexion réussie via API');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la déconnexion API:', error);
    } finally {
      // Clear tokens from localStorage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      console.log('Tokens cleared from localStorage');
    }
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<{ access: string }> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors du rafraîchissement du token'
      };
    }
  },

  // Get user profile
  getProfile: async (): Promise<User> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération du profil'
      };
    }
  },
};

// Export the api object
export { api };
