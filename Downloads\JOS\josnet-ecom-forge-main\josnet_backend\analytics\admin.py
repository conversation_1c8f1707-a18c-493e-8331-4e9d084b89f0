from django.contrib import admin
from .models import ProductView, CartAction, WishlistAction, SalesData

@admin.register(ProductView)
class ProductViewAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'ip_address', 'device_type', 'created_at')
    list_filter = ('device_type', 'created_at')
    search_fields = ('product__name', 'user__email', 'ip_address')
    date_hierarchy = 'created_at'

@admin.register(CartAction)
class CartActionAdmin(admin.ModelAdmin):
    list_display = ('product', 'variant', 'user', 'action', 'quantity', 'created_at')
    list_filter = ('action', 'created_at')
    search_fields = ('product__name', 'user__email')
    date_hierarchy = 'created_at'

@admin.register(WishlistAction)
class WishlistActionAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'action', 'created_at')
    list_filter = ('action', 'created_at')
    search_fields = ('product__name', 'user__email')
    date_hierarchy = 'created_at'

@admin.register(SalesData)
class SalesDataAdmin(admin.ModelAdmin):
    list_display = ('product', 'variant', 'order_id', 'customer_id', 'quantity', 'unit_price', 'total_price', 'date')
    list_filter = ('date',)
    search_fields = ('product__name', 'order_id', 'customer_id')
    date_hierarchy = 'date'
