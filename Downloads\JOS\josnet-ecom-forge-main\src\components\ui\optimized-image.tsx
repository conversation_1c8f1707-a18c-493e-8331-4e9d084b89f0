import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  placeholderSrc?: string;
  fallbackSrc?: string;
  aspectRatio?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  priority?: boolean;
  blur?: boolean;
  className?: string;
  containerClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * OptimizedImage component with lazy loading, blur-up effect, and error handling
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  placeholderSrc,
  fallbackSrc,
  aspectRatio = '1/1',
  objectFit = 'cover',
  priority = false,
  blur = true,
  className,
  containerClassName,
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isVisible, setIsVisible] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Generate a low-quality placeholder if not provided
  const defaultPlaceholder = placeholderSrc || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNlZWVlZWUiLz48L3N2Zz4=';
  
  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (priority) return; // Skip if priority is true
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '200px', // Start loading when image is 200px from viewport
        threshold: 0.01,
      }
    );
    
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }
    
    return () => {
      observer.disconnect();
    };
  }, [priority]);
  
  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.();
  };
  
  // Handle image error
  const handleError = () => {
    setHasError(true);
    onError?.();
  };
  
  // Determine which image source to use
  const imageSrc = hasError && fallbackSrc ? fallbackSrc : src;
  
  return (
    <div
      ref={containerRef}
      className={cn(
        'relative overflow-hidden bg-gray-100',
        containerClassName
      )}
      style={{
        width: width || '100%',
        height: height || 'auto',
        aspectRatio: aspectRatio,
      }}
    >
      {/* Placeholder or blur-up effect */}
      {(!isLoaded || !isVisible) && blur && (
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-opacity duration-500"
          style={{
            backgroundImage: `url(${defaultPlaceholder})`,
            filter: 'blur(10px)',
            transform: 'scale(1.1)',
            opacity: isLoaded ? 0 : 1,
          }}
        />
      )}
      
      {/* Main image */}
      {isVisible && (
        <img
          ref={imgRef}
          src={imageSrc}
          alt={alt}
          width={typeof width === 'number' ? width : undefined}
          height={typeof height === 'number' ? height : undefined}
          loading={priority ? 'eager' : 'lazy'}
          decoding={priority ? 'sync' : 'async'}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'transition-opacity duration-500',
            isLoaded ? 'opacity-100' : 'opacity-0',
            objectFit === 'contain' && 'object-contain',
            objectFit === 'cover' && 'object-cover',
            objectFit === 'fill' && 'object-fill',
            objectFit === 'none' && 'object-none',
            objectFit === 'scale-down' && 'object-scale-down',
            'w-full h-full',
            className
          )}
          {...props}
        />
      )}
      
      {/* Show fallback UI if image fails to load and no fallback image */}
      {hasError && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export { OptimizedImage };

/**
 * ProductImage component specifically optimized for product images
 */
export const ProductImage: React.FC<Omit<OptimizedImageProps, 'fallbackSrc'> & { size?: 'sm' | 'md' | 'lg' | 'xl' }> = ({
  size = 'md',
  ...props
}) => {
  // Define sizes based on the size prop
  const sizes = {
    sm: { width: 80, height: 80 },
    md: { width: 160, height: 160 },
    lg: { width: 320, height: 320 },
    xl: { width: 640, height: 640 },
  };
  
  const { width, height } = sizes[size];
  
  return (
    <OptimizedImage
      width={width}
      height={height}
      fallbackSrc="/images/product-placeholder.jpg"
      {...props}
    />
  );
};

/**
 * Gallery component for displaying multiple images with lazy loading
 */
export const ImageGallery: React.FC<{
  images: { src: string; alt: string; id?: string | number }[];
  aspectRatio?: string;
  gap?: number;
  columns?: number;
  className?: string;
  imageClassName?: string;
  onImageClick?: (index: number) => void;
}> = ({
  images,
  aspectRatio = '1/1',
  gap = 2,
  columns = 3,
  className,
  imageClassName,
  onImageClick,
}) => {
  return (
    <div
      className={cn(
        'grid',
        `grid-cols-${columns}`,
        `gap-${gap}`,
        className
      )}
    >
      {images.map((image, index) => (
        <OptimizedImage
          key={image.id || index}
          src={image.src}
          alt={image.alt}
          aspectRatio={aspectRatio}
          className={cn(
            'cursor-pointer transition-transform hover:scale-105',
            imageClassName
          )}
          onClick={() => onImageClick?.(index)}
        />
      ))}
    </div>
  );
};
