import { getAuthToken } from '@/utils/auth';
import axios from 'axios';
import { API_URL } from '../config';

// Types
export interface DashboardStats {
  total_sales: number;
  total_orders: number;
  total_customers: number;
  total_products: number;
  change_sales: string;
  change_orders: string;
  change_customers: string;
  change_products: string;
  change_type_sales: 'positive' | 'negative' | 'neutral';
  change_type_orders: 'positive' | 'negative' | 'neutral';
  change_type_customers: 'positive' | 'negative' | 'neutral';
  change_type_products: 'positive' | 'negative' | 'neutral';
}

export interface RecentOrder {
  id: string;
  customer: string;
  date: string;
  status: string;
  amount: string;
}

export interface LowStockItem {
  id: string;
  name: string;
  stock: number;
  threshold: number;
}

export interface PendingTask {
  id: number;
  task: string;
  priority: string;
}

export interface MonthlySales {
  month: string;
  value: number;
}

export interface DashboardData {
  stats: DashboardStats;
  recent_orders: RecentOrder[];
  low_stock_items: LowStockItem[];
  pending_tasks: PendingTask[];
  monthly_sales: MonthlySales[];
  is_mock_data?: boolean;
}

// Dashboard API service
const dashboardApi = {
  // Get dashboard data
  getDashboardData: async (): Promise<DashboardData> => {
    try {
      // Utiliser le bon endpoint pour le tableau de bord
      const url = `${API_URL}/analytics/dashboard/`;
      console.log(`Fetching dashboard data from ${url}`);

      // Vérifier plusieurs clés possibles pour le token
      const token = localStorage.getItem('accessToken') ||
                 localStorage.getItem('authToken') ||
                 localStorage.getItem('token') ||
                 sessionStorage.getItem('accessToken') ||
                 sessionStorage.getItem('authToken') ||
                 getAuthToken();

      console.log('Auth token present:', !!token);

      if (!token) {
        console.warn('No authentication token found - Using mock data');
        const mockData = dashboardApi.getMockDashboardData();
        mockData.is_mock_data = true;
        return mockData;
      }

      // Afficher les 10 premiers caractères du token pour débogage (sans révéler le token complet)
      if (token) {
        const tokenPreview = token.substring(0, 10) + '...';
        console.log(`Using token: ${tokenPreview}`);
      }

      // Essayer d'abord avec le préfixe Bearer
      try {
        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        console.log('Successfully fetched dashboard data from API:', response.status);
        return response.data;
      } catch (bearerError) {
        // Si l'erreur est 401 Unauthorized, essayer sans le préfixe Bearer
        if (bearerError.response && bearerError.response.status === 401) {
          console.log('Bearer token authentication failed, trying without Bearer prefix');

          const response = await axios.get(url, {
            headers: {
              Authorization: token
            }
          });

          console.log('Successfully fetched dashboard data with token only:', response.status);
          return response.data;
        }

        // Si ce n'est pas une erreur 401 ou si la deuxième tentative échoue, propager l'erreur
        throw bearerError;
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);

      // Afficher plus de détails sur l'erreur pour faciliter le débogage
      if (error.response) {
        // La requête a été faite et le serveur a répondu avec un code d'erreur
        console.error('Error details:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers
        });

        // Vérifier si c'est un problème d'authentification
        if (error.response.status === 401) {
          console.error('Authentication error: Invalid or expired token');
          // Essayer de récupérer un nouveau token ici si nécessaire
        }
      } else if (error.request) {
        // La requête a été faite mais aucune réponse n'a été reçue
        console.error('No response received:', error.request);
      } else {
        // Une erreur s'est produite lors de la configuration de la requête
        console.error('Request setup error:', error.message);
      }

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('⚠️ USING MOCK DATA AS FALLBACK - Backend API not available or not configured');
      console.warn('This is expected during development if the backend is not running');

      // Données simulées pour le développement frontend
      const mockData = dashboardApi.getMockDashboardData();

      // Ajouter un indicateur pour montrer que ce sont des données simulées
      mockData.is_mock_data = true;

      return mockData;
    }
  },

  // Helper function to generate mock dashboard data
  getMockDashboardData: (): DashboardData => {
    // Conversion en Francs Burundais (1 EUR ≈ 2200 BIF)
    const exchangeRate = 2200;

    // Fonction pour formater les montants en Francs Burundais
    const formatBIF = (amount: number): string => {
      return new Intl.NumberFormat('fr-BI', {
        style: 'currency',
        currency: 'BIF',
        maximumFractionDigits: 0
      }).format(amount);
    };

    return {
      stats: {
        total_sales: 26450 * exchangeRate,
        total_orders: 458,
        total_customers: 1923,
        total_products: 156,
        change_sales: "+15%",
        change_orders: "+10%",
        change_customers: "+18%",
        change_products: "+5",
        change_type_sales: "positive",
        change_type_orders: "positive",
        change_type_customers: "positive",
        change_type_products: "positive"
      },
      recent_orders: [
        { id: "#ORD-12345", customer: "Jean Dupont", date: "15/05/2025", status: "Livré", amount: formatBIF(349.99 * exchangeRate) },
        { id: "#ORD-12344", customer: "Sophie Martin", date: "14/05/2025", status: "En cours", amount: formatBIF(129.95 * exchangeRate) },
        { id: "#ORD-12343", customer: "Pierre Durand", date: "14/05/2025", status: "En attente", amount: formatBIF(89.50 * exchangeRate) },
        { id: "#ORD-12342", customer: "Marie Lambert", date: "13/05/2025", status: "Livré", amount: formatBIF(199.00 * exchangeRate) },
        { id: "#ORD-12341", customer: "Thomas Bernard", date: "13/05/2025", status: "Annulé", amount: formatBIF(459.90 * exchangeRate) }
      ],
      low_stock_items: [
        { id: "PRD-001", name: "Routeur Wi-Fi 6", stock: 3, threshold: 10 },
        { id: "PRD-015", name: "Câble HDMI 2.1", stock: 5, threshold: 15 },
        { id: "PRD-023", name: "Disque SSD 1TB", stock: 2, threshold: 8 },
        { id: "PRD-042", name: "Adaptateur USB-C", stock: 4, threshold: 12 }
      ],
      pending_tasks: [
        { id: 1, task: "Mise à jour des descriptions produits", priority: "Haute" },
        { id: 2, task: "Validation des retours clients", priority: "Moyenne" },
        { id: 3, task: "Répondre aux avis clients récents", priority: "Basse" },
        { id: 4, task: "Préparer la promotion de mai", priority: "Haute" }
      ],
      monthly_sales: [
        { month: "Jan", value: 12500 * exchangeRate },
        { month: "Fév", value: 9800 * exchangeRate },
        { month: "Mar", value: 16200 * exchangeRate },
        { month: "Avr", value: 10900 * exchangeRate },
        { month: "Mai", value: 17500 * exchangeRate },
        { month: "Juin", value: 14200 * exchangeRate }
      ]
    };
  }
};

export default dashboardApi;
