# Import all serializers to make them available from the serializers package
from authentication.serializers.admin_serializers import (
    AdminUserSerializer,
    AdminUserCreateSerializer,
    AdminUserUpdateSerializer,
    AdminUserAddressSerializer,
    AdminLoyaltyTransactionSerializer,
    AdminUserStatsSerializer
)

# Import serializers from the serializers_original.py file
# This is needed for backward compatibility
from authentication.serializers.serializers_original import (
    UserSerializer,
    UserProfileSerializer,
    LoginSerializer,
    PasswordChangeSerializer,
    PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer,
    EmailVerificationSerializer,
    UserAddressSerializer,
    UserAddressListSerializer,
    LoyaltyTransactionSerializer,
    UserLoyaltySerializer,
    UserProfileExtendedSerializer
)
