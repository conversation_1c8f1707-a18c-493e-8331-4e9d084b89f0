#!/usr/bin/env python
"""
Debug du système de token de reset
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from authentication.models import PasswordReset, User
from django.utils import timezone

def debug_token(token_str):
    """Debug un token spécifique"""
    print(f"🔍 Debug du token: {token_str}\n")
    
    try:
        # Recherche du token
        print("1. Recherche du token dans la base...")
        reset = PasswordReset.objects.filter(token=token_str).first()
        
        if not reset:
            print("❌ Token non trouvé dans la base de données")
            return
            
        print(f"✅ Token trouvé!")
        print(f"   - ID: {reset.id}")
        print(f"   - Utilisateur: {reset.user.email}")
        print(f"   - <PERSON><PERSON><PERSON> le: {reset.created_at}")
        print(f"   - Expire le: {reset.expires_at}")
        print(f"   - Utilisé: {reset.is_used}")
        print(f"   - Maintenant: {timezone.now()}")
        
        # Vérifications
        print("\n2. Vérifications...")
        
        # Vérification d'expiration
        is_expired_by_date = timezone.now() > reset.expires_at
        print(f"   - Expiré par date: {is_expired_by_date}")
        
        # Vérification d'utilisation
        print(f"   - Déjà utilisé: {reset.is_used}")
        
        # Propriété is_expired
        print(f"   - Propriété is_expired: {reset.is_expired}")
        
        # Test de la requête du serializer
        print("\n3. Test de la requête du serializer...")
        try:
            serializer_reset = PasswordReset.objects.get(token=token_str, is_used=False)
            print("✅ Token trouvé avec is_used=False")
            
            if timezone.now() > serializer_reset.expires_at:
                print("❌ Token expiré par date")
            else:
                print("✅ Token valide par date")
                
        except PasswordReset.DoesNotExist:
            print("❌ Token non trouvé avec is_used=False")
            
        # Test de la requête de la vue
        print("\n4. Test de la requête de la vue...")
        try:
            view_reset = PasswordReset.objects.get(token=token_str, is_used=False)
            print("✅ Token trouvé pour la vue")
            
            if view_reset.is_expired:
                print("❌ Token marqué comme expiré par la propriété")
            else:
                print("✅ Token valide pour la vue")
                
        except PasswordReset.DoesNotExist:
            print("❌ Token non trouvé pour la vue")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_serializer_validation(token_str):
    """Test la validation du serializer"""
    print(f"\n🧪 Test de validation du serializer pour: {token_str}")

    from authentication.serializers import PasswordResetConfirmSerializer

    data = {
        'token': token_str,
        'new_password': 'TestPassword123!',
        'confirm_password': 'TestPassword123!'
    }

    serializer = PasswordResetConfirmSerializer(data=data)

    try:
        if serializer.is_valid():
            print("✅ Serializer valide")
            print(f"   Données validées: {serializer.validated_data}")
        else:
            print("❌ Serializer invalide")
            print(f"   Erreurs: {serializer.errors}")
    except Exception as e:
        print(f"❌ Erreur lors de la validation: {e}")

def test_view_logic(token_str):
    """Test la logique de la vue"""
    print(f"\n🔍 Test de la logique de vue pour: {token_str}")

    from authentication.models import PasswordReset
    from django.utils import timezone

    try:
        # Test 1: Récupération du token
        print("1. Recherche du token...")
        reset = PasswordReset.objects.get(token=token_str, is_used=False)
        print(f"   ✅ Token trouvé: {reset}")
        print(f"   📧 Utilisateur: {reset.user.email}")
        print(f"   ⏰ Créé le: {reset.created_at}")
        print(f"   ⏰ Expire le: {reset.expires_at}")
        print(f"   🔒 Utilisé: {reset.is_used}")

        # Test 2: Vérification d'expiration
        print("2. Vérification d'expiration...")
        if reset.is_expired:
            print("   ❌ Token expiré")
            print(f"   ⏰ Maintenant: {timezone.now()}")
            print(f"   ⏰ Expiration: {reset.expires_at}")
            print(f"   🔒 Utilisé: {reset.is_used}")
        else:
            print("   ✅ Token valide")

    except PasswordReset.DoesNotExist:
        print("   ❌ Token non trouvé dans la base de données")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

def get_latest_reset_token():
    """Récupère le dernier token de reset"""
    from authentication.models import PasswordReset

    try:
        latest_reset = PasswordReset.objects.filter(is_used=False).order_by('-created_at').first()
        if latest_reset:
            return latest_reset.token
        return None
    except Exception as e:
        print(f"❌ Erreur lors de la récupération du token: {e}")
        return None

def test_token_validity(token_str):
    """Test la validité du token"""
    print(f"\n🔍 Test de validité du token: {token_str}")

    from authentication.models import PasswordReset
    from django.utils import timezone

    try:
        reset = PasswordReset.objects.get(token=token_str)
        print(f"✅ Token trouvé")
        print(f"   📧 Utilisateur: {reset.user.email}")
        print(f"   ⏰ Créé le: {reset.created_at}")
        print(f"   ⏰ Expire le: {reset.expires_at}")
        print(f"   🔒 Utilisé: {reset.is_used}")
        print(f"   ⏰ Maintenant: {timezone.now()}")

        if reset.is_expired:
            print("❌ Token expiré")
        else:
            print("✅ Token valide")

    except PasswordReset.DoesNotExist:
        print("❌ Token non trouvé")

if __name__ == "__main__":
    # Récupérer le dernier token
    token = get_latest_reset_token()

    if token:
        print(f"🔍 Test du token: {token}")
        test_token_validity(token)
        test_serializer_validation(token)
        test_view_logic(token)
    else:
        print("❌ Aucun token trouvé")
