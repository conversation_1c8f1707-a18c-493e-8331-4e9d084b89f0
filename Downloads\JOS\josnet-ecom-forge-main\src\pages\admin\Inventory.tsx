
import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  PackageOpen,
  Search,
  Filter,
  ChevronDown,
  RefreshCcw,
  AlertTriangle,
  Package,
  Loader2,
  Download,
  Plus,
  ArrowUpDown,
  Settings
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { productApi } from "@/services/productApi";
import InventoryAdjustmentDialog from "@/components/admin/products/InventoryAdjustmentDialog";

const Inventory = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isAdjustDialogOpen, setIsAdjustDialogOpen] = useState(false);
  const [selectedInventoryId, setSelectedInventoryId] = useState<number | null>(null);

  // Fetch inventory data
  const {
    data: inventoryItems = [],
    isLoading,
    refetch,
    error: inventoryError
  } = useQuery({
    queryKey: ['inventory', { status: statusFilter }],
    queryFn: () => productApi.getInventoryItems({ status: statusFilter }),
    retry: 1,
    retryDelay: 1000,
  });

  // Fetch inventory stats
  const {
    data: inventoryStats,
    error: statsError
  } = useQuery({
    queryKey: ['inventoryStats'],
    queryFn: () => productApi.getInventoryStats(),
    retry: 1,
    retryDelay: 1000,
  });

  // Ensure inventoryItems is an array before filtering
  const inventoryItemsArray = Array.isArray(inventoryItems) ? inventoryItems : [];

  // Filter inventory items based on search query
  const filteredInventory = inventoryItemsArray.filter(item =>
    item?.product_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item?.variant_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item?.sku?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate low stock count
  const lowStockCount = filteredInventory.filter(item => item?.is_low_stock).length;

  const handleUpdateStock = (id: number) => {
    setSelectedInventoryId(id);
    setIsAdjustDialogOpen(true);
  };

  const handleOrderMore = (id: number, name: string) => {
    toast({
      title: "Commande initiée",
      description: `Une demande d'approvisionnement pour ${name} a été créée`,
    });
    // Dans une app réelle, cette fonction créerait une demande d'approvisionnement
  };

  const refreshInventory = () => {
    refetch();
    toast({
      title: "Rafraîchissement en cours",
      description: "Données d'inventaire mises à jour",
    });
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold">Gestion des Stocks</h1>
            <p className="text-gray-500">Suivez et gérez l'inventaire de votre boutique</p>
          </div>
          <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              asChild
              className="gap-2"
            >
              <Link to="/admin/products/inventory">
                <Settings className="h-4 w-4" />
                <span>Gestion avancée</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* API Error Message */}
        {(inventoryError || statsError) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3 mb-4">
            <div className="text-red-500">
              <AlertTriangle size={20} />
            </div>
            <div>
              <h3 className="font-medium text-red-800">Erreur de connexion</h3>
              <p className="text-red-700 text-sm mt-1">
                Impossible de se connecter à l'API d'inventaire. Veuillez vérifier que le serveur backend est en cours d'exécution.
              </p>
              <div className="mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    refetch();
                    toast({
                      title: "Tentative de reconnexion",
                      description: "Tentative de reconnexion à l'API d'inventaire...",
                    });
                  }}
                >
                  <RefreshCcw className="h-4 w-4 mr-2" />
                  Réessayer
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-2 rounded-full bg-blue-100 text-blue-600">
                  <Package className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Produits</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {isLoading || statsError ? <Loader2 className="h-6 w-6 animate-spin" /> : inventoryStats?.total_products || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-2 rounded-full bg-green-100 text-green-600">
                  <Package className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">En Stock</p>
                  <p className="text-2xl font-bold text-green-600">
                    {isLoading || statsError ? <Loader2 className="h-6 w-6 animate-spin" /> : inventoryStats?.in_stock || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-2 rounded-full bg-amber-100 text-amber-600">
                  <AlertTriangle className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Stock Bas</p>
                  <p className="text-2xl font-bold text-amber-600">
                    {isLoading || statsError ? <Loader2 className="h-6 w-6 animate-spin" /> : inventoryStats?.low_stock || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-2 rounded-full bg-red-100 text-red-600">
                  <AlertTriangle className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Rupture</p>
                  <p className="text-2xl font-bold text-red-600">
                    {isLoading || statsError ? <Loader2 className="h-6 w-6 animate-spin" /> : inventoryStats?.out_of_stock || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Alerte stock bas */}
        {lowStockCount > 0 && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-start gap-3">
            <div className="text-amber-500">
              <AlertTriangle size={20} />
            </div>
            <div>
              <h3 className="font-medium text-amber-800">Alerte stock bas</h3>
              <p className="text-amber-700 text-sm mt-1">
                {lowStockCount} produit{lowStockCount > 1 ? 's' : ''} {lowStockCount > 1 ? 'sont' : 'est'} en dessous du seuil minimal de stock. Vérifiez le tableau ci-dessous.
              </p>
            </div>
          </div>
        )}

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher un produit..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
            >
              <SelectTrigger className="w-[180px] flex items-center gap-1">
                <Filter size={16} className="mr-1" />
                <SelectValue placeholder="État du stock" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les produits</SelectItem>
                <SelectItem value="in_stock">En stock</SelectItem>
                <SelectItem value="low_stock">Stock bas</SelectItem>
                <SelectItem value="out_of_stock">Rupture de stock</SelectItem>
              </SelectContent>
            </Select>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  <Download size={16} />
                  Export
                  <ChevronDown size={14} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => toast({ title: "Export CSV", description: "Export en cours..." })}>
                  Exporter en CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => toast({ title: "Export PDF", description: "Export en cours..." })}>
                  Exporter en PDF
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button onClick={refreshInventory} className="flex items-center gap-1">
              <RefreshCcw size={16} />
              Rafraîchir
            </Button>
          </div>
        </div>

        {/* Tableau de l'inventaire */}
        <Card>
          <CardHeader>
            <CardTitle>Articles en Stock</CardTitle>
            <CardDescription>
              Gérez les niveaux de stock et les mouvements d'inventaire
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Produit</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="text-center">Stock</TableHead>
                    <TableHead className="text-center">Réservé</TableHead>
                    <TableHead className="text-center">Disponible</TableHead>
                    <TableHead className="text-center">Statut</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2 text-sm text-gray-500">Chargement des données d'inventaire...</p>
                      </TableCell>
                    </TableRow>
                  ) : inventoryError ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <AlertTriangle className="h-6 w-6 mx-auto text-red-500" />
                        <p className="mt-2 text-sm text-red-500">Erreur lors du chargement des données d'inventaire.</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => {
                            refetch();
                            toast({
                              title: "Tentative de reconnexion",
                              description: "Tentative de reconnexion à l'API d'inventaire...",
                            });
                          }}
                        >
                          <RefreshCcw className="h-4 w-4 mr-2" />
                          Réessayer
                        </Button>
                      </TableCell>
                    </TableRow>
                  ) : filteredInventory.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <p className="text-sm text-gray-500">Aucun article d'inventaire trouvé.</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredInventory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div className="font-medium">{item.product_name}</div>
                          {item.variant_name && (
                            <div className="text-sm text-gray-500">{item.variant_name}</div>
                          )}
                        </TableCell>
                        <TableCell>
                          <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{item.sku}</code>
                        </TableCell>
                        <TableCell className="text-center">{item.quantity}</TableCell>
                        <TableCell className="text-center">{item.reserved_quantity}</TableCell>
                        <TableCell className="text-center font-medium">
                          {item.available_quantity}
                        </TableCell>
                        <TableCell className="text-center">
                          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            item.is_in_stock
                              ? item.is_low_stock
                                ? 'bg-amber-100 text-amber-800'
                                : 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {item.is_in_stock
                              ? item.is_low_stock
                                ? 'Stock Bas'
                                : 'En Stock'
                              : 'Rupture'
                            }
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs"
                              onClick={() => handleUpdateStock(item.id)}
                            >
                              Ajuster
                            </Button>
                            {item.is_low_stock && (
                              <Button
                                variant="default"
                                size="sm"
                                className="text-xs"
                                onClick={() => handleOrderMore(item.id, item.product_name || '')}
                              >
                                Commander
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Indication du nombre de produits */}
        <div className="text-sm text-gray-500">
          Affichage de {filteredInventory.length} produits
        </div>

        {/* Dialog for stock adjustment */}
        <InventoryAdjustmentDialog
          isOpen={isAdjustDialogOpen}
          onOpenChange={setIsAdjustDialogOpen}
          inventoryId={selectedInventoryId}
          onSuccess={() => refetch()}
        />
      </div>
    </AdminLayout>
  );
};

export default Inventory;
