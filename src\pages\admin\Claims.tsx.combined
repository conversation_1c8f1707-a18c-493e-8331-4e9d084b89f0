import React, { useState, useEffect } from 'react';
import { <PERSON>, Typo<PERSON>, Card, CardContent, Grid as <PERSON>i<PERSON><PERSON>, <PERSON>, <PERSON>ton, TextField, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip, CircularProgress, Tabs, Tab, Avatar, Divider } from '@mui/material';

// Wrapper pour Grid avec les propriétés correctement typées
const Grid = (props: {
  children: React.ReactNode;
  container?: boolean;
  item?: boolean;
  xs?: number | boolean;
  sm?: number | boolean;
  md?: number | boolean;
  lg?: number | boolean;
  xl?: number | boolean;
  spacing?: number;
  direction?: 'row' | 'row-reverse' | 'column' | 'column-reverse';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  sx?: any;
}) => {
  return <MuiGrid {...props} />;
};
import { useTheme } from '@mui/material/styles';
import AdminLayout from '../../components/admin/AdminLayout';

// Fonction utilitaire pour formater les dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
};

// Fonction utilitaire pour formater l'heure
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
};
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SendIcon from '@mui/icons-material/Send';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import PersonIcon from '@mui/icons-material/Person';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import ComputerIcon from '@mui/icons-material/Computer';

// Types
interface ClaimMessage {
  id: string;
  sender: 'customer' | 'support' | 'system';
  message: string;
  is_read: boolean;
  created_at: string;
  attachments: {
    id: string;
    file_name: string;
    file_size: number;
    content_type: string;
    file_url: string;
  }[];
}

interface ClaimStatusHistory {
  id: string;
  old_status: string;
  new_status: string;
  comment: string | null;
  created_by: {
    id: string;
    email: string;
    full_name: string;
  } | null;
  created_at: string;
}

interface Claim {
  id: string;
  claim_number: string;
  user: {
    id: string;
    email: string;
    full_name: string;
  };
  order: {
    id: string;
    order_number: string;
  } | null;
  subject: string;
  description: string;
  status: 'new' | 'in_progress' | 'waiting_customer' | 'waiting_staff' | 'resolved' | 'closed';
  created_at: string;
  updated_at: string;
  last_update: string;
  messages: ClaimMessage[];
  status_history: ClaimStatusHistory[];
}

const statusColors = {
  new: '#FFA000',                // Amber
  in_progress: '#2196F3',         // Blue
  waiting_customer: '#FF5722',    // Deep Orange
  waiting_staff: '#9C27B0',       // Purple
  resolved: '#4CAF50',            // Green
  closed: '#9E9E9E'               // Grey
};

const statusLabels = {
  new: 'Nouvelle réclamation',
  in_progress: 'Traitement en cours',
  waiting_customer: 'Attente réponse client',
  waiting_staff: 'Attente réponse support',
  resolved: 'Résolue',
  closed: 'Clôturée'
};

const subjectLabels = {
  order_issue: 'Problème de commande',
  product_issue: 'Problème de produit',
  shipping_issue: 'Problème de livraison',
  account_issue: 'Problème de compte',
  website_issue: 'Problème technique',
  payment_issue: 'Problème de paiement',
  other: 'Autre sujet'
};

const AdminClaims: React.FC = () => {
  const theme = useTheme();
  const [claims, setClaims] = useState<Claim[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [subjectFilter, setSubjectFilter] = useState<string>('all');
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [updateStatusOpen, setUpdateStatusOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [adminNote, setAdminNote] = useState('');
  const [replyMessage, setReplyMessage] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  // Fonctions de gestion d'événements
  const handleViewDetails = (claim: Claim) => {
    setSelectedClaim(claim);
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  const handleUpdateStatus = (claim: Claim) => {
    setSelectedClaim(claim);
    setNewStatus(claim.status);
    setUpdateStatusOpen(true);
  };

  const submitStatusUpdate = async () => {
    if (!selectedClaim || !newStatus) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/admin/claims/${selectedClaim.id}/status/`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus,
          comment: adminNote
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      // Update local state
      const updatedClaim = await response.json();
      setClaims(prevClaims => 
        prevClaims.map(item => 
          item.id === updatedClaim.id ? updatedClaim : item
        )
      );

      setUpdateStatusOpen(false);
      setAdminNote('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      console.error('Error updating claim status:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSendReply = async () => {
    if (!selectedClaim || !replyMessage.trim()) return;
    
    try {
      setLoading(true);
      
      const formData = new FormData();
      formData.append('sender', 'support');
      formData.append('message', replyMessage);
      
      // Add files if any
      selectedFiles.forEach(file => {
        formData.append('uploaded_files', file);
      });

      const response = await fetch(`/api/admin/claims/${selectedClaim.id}/messages/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      // Update local state with the updated claim
      const updatedClaim = await response.json();
      setClaims(prevClaims => 
        prevClaims.map(item => 
          item.id === updatedClaim.id ? updatedClaim : item
        )
      );
      
      // If the selected claim is open in the details view, update it
      if (selectedClaim && selectedClaim.id === updatedClaim.id) {
        setSelectedClaim(updatedClaim);
      }

      // Reset form
      setReplyMessage('');
      setSelectedFiles([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      console.error('Error sending reply:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const filesArray = Array.from(event.target.files);
      setSelectedFiles(prevFiles => [...prevFiles, ...filesArray]);
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  const getSenderIcon = (sender: string) => {
    switch (sender) {
      case 'customer':
        return <PersonIcon />;
      case 'support':
        return <SupportAgentIcon />;
      case 'system':
        return <ComputerIcon />;
      default:
        return null;
    }
  };

  const getSenderName = (sender: string) => {
    switch (sender) {
      case 'customer':
        return 'Client';
      case 'support':
        return 'Support';
      case 'system':
        return 'Système';
      default:
        return sender;
    }
  };
  // Fetch claims data
  useEffect(() => {
    const fetchClaims = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/claims/', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
          }
        });

        if (!response.ok) {
          throw new Error(`Erreur ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        setClaims(data);
        setFilteredClaims(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
        console.error('Error fetching claims:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchClaims();
  }, []);

  // Filter claims based on search term, status filter and subject filter
  useEffect(() => {
    let result = [...claims];
    
    if (statusFilter !== 'all') {
      result = result.filter(item => item.status === statusFilter);
    }
    
    if (subjectFilter !== 'all') {
      result = result.filter(item => item.subject === subjectFilter);
    }
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(item => 
        item.claim_number.toLowerCase().includes(term) ||
        item.user.email.toLowerCase().includes(term) ||
        item.user.full_name.toLowerCase().includes(term) ||
        (item.order?.order_number.toLowerCase().includes(term) || false)
      );
    }

    setFilteredClaims(result);
  }, [claims, searchTerm, statusFilter, subjectFilter]);

  return (
    <AdminLayout>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5">Gestion des réclamations</Typography>
        </Box>
        
        <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="status-filter-label">Statut</InputLabel>
            <Select
              labelId="status-filter-label"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              label="Statut"
            >
              <MenuItem value="all">Tous les statuts</MenuItem>
              <MenuItem value="new">Nouveau</MenuItem>
              <MenuItem value="in_progress">En cours</MenuItem>
              <MenuItem value="waiting_customer">En attente du client</MenuItem>
              <MenuItem value="waiting_staff">En attente du support</MenuItem>
              <MenuItem value="resolved">Résolu</MenuItem>
              <MenuItem value="closed">Fermé</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="subject-filter-label">Sujet</InputLabel>
            <Select
              labelId="subject-filter-label"
              value={subjectFilter}
              onChange={(e) => setSubjectFilter(e.target.value)}
              label="Sujet"
            >
              <MenuItem value="all">Tous les sujets</MenuItem>
              <MenuItem value="order_issue">Problème avec une commande</MenuItem>
              <MenuItem value="product_issue">Problème avec un produit</MenuItem>
              <MenuItem value="shipping_issue">Problème de livraison</MenuItem>
              <MenuItem value="account_issue">Problème de compte</MenuItem>
              <MenuItem value="website_issue">Problème avec le site web</MenuItem>
              <MenuItem value="other">Autre</MenuItem>
            </Select>
          </FormControl>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <TextField
              size="small"
              variant="outlined"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Box>
        </Box>
        {/* Error message */}
        {error && (
          <Box sx={{ mb: 2 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}

        {/* Claims Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ mb: 4 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Numéro</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Sujet</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Date de création</TableCell>
                  <TableCell>Dernière mise à jour</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredClaims.length > 0 ? (
                  filteredClaims.map((claim) => (
                    <TableRow key={claim.id}>
                      <TableCell>{claim.claim_number}</TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">{claim.user.full_name}</Typography>
                          <Typography variant="caption" color="text.secondary">{claim.user.email}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {subjectLabels[claim.subject as keyof typeof subjectLabels] || claim.subject}
                        {claim.order && (
                          <Typography variant="caption" display="block" color="text.secondary">
                            Commande: {claim.order.order_number}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={statusLabels[claim.status as keyof typeof statusLabels]}
                          sx={{ 
                            backgroundColor: statusColors[claim.status as keyof typeof statusColors],
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatDate(claim.created_at)}</TableCell>
                      <TableCell>
                        {formatDate(claim.last_update)}
                        <Typography variant="caption" display="block" color="text.secondary">
                          {formatTime(claim.last_update)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Voir les détails">
                          <IconButton size="small" onClick={() => handleViewDetails(claim)}>
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      Aucune réclamation trouvée
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
        {/* Claim Details Dialog */}
        <Dialog 
          open={detailsOpen} 
          onClose={handleCloseDetails}
          maxWidth="md"
          fullWidth
        >
          {selectedClaim && (
            <>
              <DialogTitle>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h6">Réclamation {selectedClaim.claim_number}</Typography>
                  <Chip 
                    label={statusLabels[selectedClaim.status as keyof typeof statusLabels]}
                    sx={{ 
                      backgroundColor: statusColors[selectedClaim.status as keyof typeof statusColors],
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>
              </DialogTitle>
              <DialogContent>
                <Box sx={{ mb: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Client</Typography>
                      <Typography variant="body2">{selectedClaim.user.full_name}</Typography>
                      <Typography variant="body2" color="text.secondary">{selectedClaim.user.email}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Date de création</Typography>
                      <Typography variant="body2">{formatDate(selectedClaim.created_at)} à {formatTime(selectedClaim.created_at)}</Typography>
                    </Grid>
                    {selectedClaim.order && (
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2">Commande associée</Typography>
                        <Typography variant="body2">{selectedClaim.order.order_number}</Typography>
                      </Grid>
                    )}
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Sujet</Typography>
                      <Typography variant="body2">{subjectLabels[selectedClaim.subject as keyof typeof subjectLabels] || selectedClaim.subject}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2">Description</Typography>
                      <Typography variant="body2">{selectedClaim.description}</Typography>
                    </Grid>
                  </Grid>
                </Box>
                
                <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)} sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                  <Tab label="Messages" />
                  <Tab label="Historique des statuts" />
                </Tabs>
                
                {activeTab === 0 && (
                  <>
                    <Box sx={{ mb: 2, maxHeight: '300px', overflowY: 'auto' }}>
                      {selectedClaim.messages.length > 0 ? (
                        selectedClaim.messages.map((message) => (
                          <Card key={message.id} sx={{ mb: 2, bgcolor: message.sender === 'support' ? 'primary.50' : 'background.paper' }}>
                            <CardContent>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <Avatar sx={{ mr: 1, bgcolor: message.sender === 'customer' ? 'secondary.main' : 'primary.main' }}>
                                  {getSenderIcon(message.sender)}
                                </Avatar>
                                <Box>
                                  <Typography variant="subtitle2">{getSenderName(message.sender)}</Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {formatDate(message.created_at)} à {formatTime(message.created_at)}
                                  </Typography>
                                </Box>
                              </Box>
                              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>{message.message}</Typography>
                              
                              {message.attachments.length > 0 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="subtitle2" sx={{ mb: 1 }}>Pièces jointes:</Typography>
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                    {message.attachments.map((attachment) => (
                                      <Chip 
                                        key={attachment.id}
                                        label={attachment.file_name}
                                        component="a"
                                        href={attachment.file_url}
                                        target="_blank"
                                        clickable
                                        icon={<AttachFileIcon />}
                                        variant="outlined"
                                      />
                                    ))}
                                  </Box>
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        ))
                      ) : (
                        <Typography align="center" color="text.secondary">Aucun message</Typography>
                      )}
                    </Box>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>Répondre</Typography>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      placeholder="Votre réponse..."
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      sx={{ mb: 2 }}
                    />
                    
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>
                      <Button
                        variant="outlined"
                        component="label"
                        startIcon={<AttachFileIcon />}
                        sx={{ alignSelf: 'flex-start' }}
                      >
                        Ajouter des fichiers
                        <input
                          type="file"
                          hidden
                          multiple
                          onChange={handleFileChange}
                        />
                      </Button>
                      
                      {selectedFiles.length > 0 && (
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>Fichiers sélectionnés:</Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {selectedFiles.map((file, index) => (
                              <Chip 
                                key={index}
                                label={file.name}
                                onDelete={() => handleRemoveFile(index)}
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Box>
                    
                    <Button
                      variant="contained"
                      color="primary"
                      endIcon={<SendIcon />}
                      onClick={handleSendReply}
                      disabled={!replyMessage.trim() || loading}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Envoyer'}
                    </Button>
                  </>
                )}
                
                {activeTab === 1 && (
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Date</TableCell>
                          <TableCell>Ancien statut</TableCell>
                          <TableCell>Nouveau statut</TableCell>
                          <TableCell>Commentaire</TableCell>
                          <TableCell>Modifié par</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedClaim.status_history.length > 0 ? (
                          selectedClaim.status_history.map((history) => (
                            <TableRow key={history.id}>
                              <TableCell>
                                {formatDate(history.created_at)}
                                <Typography variant="caption" display="block">
                                  {formatTime(history.created_at)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Chip 
                                  label={statusLabels[history.old_status as keyof typeof statusLabels] || history.old_status}
                                  size="small"
                                  sx={{ 
                                    backgroundColor: statusColors[history.old_status as keyof typeof statusColors] || '#9E9E9E',
                                    color: 'white'
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <Chip 
                                  label={statusLabels[history.new_status as keyof typeof statusLabels] || history.new_status}
                                  size="small"
                                  sx={{ 
                                    backgroundColor: statusColors[history.new_status as keyof typeof statusColors] || '#9E9E9E',
                                    color: 'white'
                                  }}
                                />
                              </TableCell>
                              <TableCell>{history.comment || '-'}</TableCell>
                              <TableCell>{history.created_by ? history.created_by.full_name : 'Système'}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} align="center">
                              Aucun historique de statut disponible
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDetailsOpen(false)}>Fermer</Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => {
                    setDetailsOpen(false);
                    handleUpdateStatus(selectedClaim);
                  }}
                >
                  Mettre à jour le statut
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>

        {/* Update Status Dialog */}
        <Dialog open={updateStatusOpen} onClose={() => setUpdateStatusOpen(false)}>
          {selectedClaim && (
            <>
              <DialogTitle>Mettre à jour le statut de la réclamation {selectedClaim.claim_number}</DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1, minWidth: 400 }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel id="new-status-label">Nouveau statut</InputLabel>
                    <Select
                      labelId="new-status-label"
                      value={newStatus}
                      onChange={(e) => setNewStatus(e.target.value)}
                      label="Nouveau statut"
                    >
                      <MenuItem value="new">Nouveau</MenuItem>
                      <MenuItem value="in_progress">En cours</MenuItem>
                      <MenuItem value="waiting_customer">En attente du client</MenuItem>
                      <MenuItem value="waiting_staff">En attente du support</MenuItem>
                      <MenuItem value="resolved">Résolu</MenuItem>
                      <MenuItem value="closed">Fermé</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    label="Note administrative (optionnelle)"
                    multiline
                    rows={4}
                    fullWidth
                    value={adminNote}
                    onChange={(e) => setAdminNote(e.target.value)}
                  />
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setUpdateStatusOpen(false)}>Annuler</Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={submitStatusUpdate}
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Enregistrer'}
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Box>
    </AdminLayout>
  );
};

export default AdminClaims;
