import axios, { AxiosError } from 'axios';
import { API_URL } from '@/config';
import { getAuthToken } from '@/utils/auth';

// Types pour les logs
export interface SystemLog {
  id: string;
  log_id: string;
  timestamp: string;
  formatted_timestamp: string;
  type: 'info' | 'success' | 'warning' | 'error';
  type_display: string;
  module: string;
  module_display: string;
  message: string;
  ip_address?: string;
  user?: number;
  user_email?: string;
  user_display: string;
  additional_data?: any;
}

export interface LogsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SystemLog[];
}

export interface LogsStatistics {
  total_logs: number;
  type_counts: Record<string, number>;
  module_counts: Record<string, number>;
  recent_activity_24h: number;
  error_logs_7d: number;
}

export interface LogsFilters {
  type?: string;
  module?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
  page?: number;
  page_size?: number;
}

// Configuration pour les données fictives
const USE_MOCK_DATA = false;

// Données fictives pour les logs
const mockLogs: SystemLog[] = [
  {
    id: '1',
    log_id: 'LOG-00001',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    formatted_timestamp: new Date(Date.now() - 3600000).toLocaleString('fr-FR'),
    type: 'info',
    type_display: 'Information',
    module: 'utilisateurs',
    module_display: 'Utilisateurs',
    message: 'Nouvel utilisateur enregistré: <EMAIL>',
    ip_address: '*************',
    user_email: '<EMAIL>',
    user_display: '<EMAIL>',
    additional_data: { user_id: 123 }
  },
  {
    id: '2',
    log_id: 'LOG-00002',
    timestamp: new Date(Date.now() - 7200000).toISOString(),
    formatted_timestamp: new Date(Date.now() - 7200000).toLocaleString('fr-FR'),
    type: 'success',
    type_display: 'Succès',
    module: 'commandes',
    module_display: 'Commandes',
    message: 'Commande #12345 traitée avec succès',
    ip_address: '*************',
    user_email: '<EMAIL>',
    user_display: '<EMAIL>',
    additional_data: { order_id: 12345, amount: 99.99 }
  },
  {
    id: '3',
    log_id: 'LOG-00003',
    timestamp: new Date(Date.now() - 10800000).toISOString(),
    formatted_timestamp: new Date(Date.now() - 10800000).toLocaleString('fr-FR'),
    type: 'warning',
    type_display: 'Avertissement',
    module: 'stocks',
    module_display: 'Stocks',
    message: 'Stock faible pour le produit "T-shirt Rouge" (ID: 456)',
    ip_address: '*************',
    user_display: 'Système',
    additional_data: { product_id: 456, current_stock: 5, threshold: 10 }
  },
  {
    id: '4',
    log_id: 'LOG-00004',
    timestamp: new Date(Date.now() - 14400000).toISOString(),
    formatted_timestamp: new Date(Date.now() - 14400000).toLocaleString('fr-FR'),
    type: 'error',
    type_display: 'Erreur',
    module: 'paiement',
    module_display: 'Paiement',
    message: 'Échec du paiement pour la commande #12346',
    ip_address: '*************',
    user_email: '<EMAIL>',
    user_display: '<EMAIL>',
    additional_data: { order_id: 12346, error_code: 'CARD_DECLINED', amount: 149.99 }
  },
  {
    id: '5',
    log_id: 'LOG-00005',
    timestamp: new Date(Date.now() - 18000000).toISOString(),
    formatted_timestamp: new Date(Date.now() - 18000000).toLocaleString('fr-FR'),
    type: 'info',
    type_display: 'Information',
    module: 'cms',
    module_display: 'CMS',
    message: 'Page "À propos" mise à jour',
    ip_address: '*************',
    user_email: '<EMAIL>',
    user_display: '<EMAIL>',
    additional_data: { page_id: 789, changes: ['title', 'content'] }
  }
];

const mockStatistics: LogsStatistics = {
  total_logs: 1247,
  type_counts: {
    info: 856,
    success: 234,
    warning: 89,
    error: 68
  },
  module_counts: {
    utilisateurs: 234,
    commandes: 345,
    paiement: 123,
    stocks: 89,
    cms: 67,
    promotions: 45,
    serveur: 234,
    securite: 56,
    systeme: 34,
    produits: 20
  },
  recent_activity_24h: 45,
  error_logs_7d: 12
};

// API pour les logs
export const logsApi = {
  // Récupérer la liste des logs
  getLogs: async (filters?: LogsFilters): Promise<LogsResponse> => {
    if (USE_MOCK_DATA) {
      // Simuler la pagination et le filtrage
      let filteredLogs = [...mockLogs];

      if (filters?.type) {
        filteredLogs = filteredLogs.filter(log => log.type === filters.type);
      }

      if (filters?.module) {
        filteredLogs = filteredLogs.filter(log => log.module === filters.module);
      }

      if (filters?.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredLogs = filteredLogs.filter(log =>
          log.message.toLowerCase().includes(searchTerm) ||
          log.user_display.toLowerCase().includes(searchTerm) ||
          log.log_id.toLowerCase().includes(searchTerm)
        );
      }

      return {
        count: filteredLogs.length,
        next: null,
        previous: null,
        results: filteredLogs
      };
    }

    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No auth token available');
      }

      const response = await axios.get(`${API_URL}/analytics/logs/`, {
        params: filters,
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching logs:', error);
      throw error;
    }
  },

  // Récupérer les statistiques des logs
  getLogsStatistics: async (): Promise<LogsStatistics> => {
    if (USE_MOCK_DATA) {
      return mockStatistics;
    }

    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No auth token available');
      }

      const response = await axios.get(`${API_URL}/analytics/logs/statistics/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching logs statistics:', error);
      throw error;
    }
  },

  // Récupérer un log spécifique
  getLog: async (id: string): Promise<SystemLog> => {
    if (USE_MOCK_DATA) {
      const log = mockLogs.find(l => l.id === id);
      if (!log) {
        throw new Error('Log not found');
      }
      return log;
    }

    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No auth token available');
      }

      const response = await axios.get(`${API_URL}/analytics/logs/${id}/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error fetching log ${id}:`, error);
      throw error;
    }
  }
};

// Fonction utilitaire pour obtenir la couleur selon le type de log
export const getLogTypeColor = (type: string): string => {
  switch (type) {
    case 'info':
      return 'text-blue-600 bg-blue-50';
    case 'success':
      return 'text-green-600 bg-green-50';
    case 'warning':
      return 'text-yellow-600 bg-yellow-50';
    case 'error':
      return 'text-red-600 bg-red-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
};

// Fonction utilitaire pour obtenir l'icône selon le type de log
export const getLogTypeIcon = (type: string): string => {
  switch (type) {
    case 'info':
      return '📋';
    case 'success':
      return '✅';
    case 'warning':
      return '⚠️';
    case 'error':
      return '❌';
    default:
      return '📄';
  }
};
