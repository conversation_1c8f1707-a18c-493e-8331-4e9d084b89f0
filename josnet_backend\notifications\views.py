from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Q
from django.contrib.auth import get_user_model
from .models import Notification, NotificationPreference
from .serializers import NotificationSerializer, NotificationPreferenceSerializer
from authentication.permissions import IsStaffOrAdmin
from .email_service import EmailNotificationService

User = get_user_model()


class NotificationViewSet(viewsets.ModelViewSet):
    """
    API endpoint pour les notifications.
    """
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Retourne les notifications de l'utilisateur connecté.
        """
        user = self.request.user
        # Retourne les notifications spécifiques à l'utilisateur ou les notifications globales
        return Notification.objects.filter(
            Q(user=user) | Q(user__isnull=True)
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        ).order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def unread(self, request):
        """
        Retourne les notifications non lues de l'utilisateur.
        """
        queryset = self.get_queryset().filter(is_read=False)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['patch'])
    def read(self, request, pk=None):
        """
        Marque une notification comme lue.
        """
        notification = self.get_object()
        notification.is_read = True
        notification.save(update_fields=['is_read'])
        return Response({'status': 'notification marked as read'})
    
    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """
        Marque toutes les notifications de l'utilisateur comme lues.
        """
        self.get_queryset().filter(is_read=False).update(is_read=True)
        return Response({'status': 'all notifications marked as read'})
    
    @action(detail=False, methods=['post'], permission_classes=[IsStaffOrAdmin])
    def create_for_all(self, request):
        """
        Crée une notification pour tous les utilisateurs ou pour les utilisateurs abonnés.
        Réservé aux administrateurs.
        """
        title = request.data.get('title')
        message = request.data.get('message')
        notification_type = request.data.get('type', 'info')
        send_to_all = request.data.get('sendToAll', False)
        related_id = request.data.get('relatedId')
        link = request.data.get('link')
        
        if not title or not message:
            return Response(
                {'error': 'title and message are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Si sendToAll est True, créer une notification globale
        if send_to_all:
            notification = Notification.objects.create(
                user=None,  # Notification globale
                title=title,
                message=message,
                type=notification_type,
                related_id=related_id,
                link=link
            )
            return Response(
                NotificationSerializer(notification).data, 
                status=status.HTTP_201_CREATED
            )
        
        # Sinon, créer des notifications individuelles pour les utilisateurs abonnés
        # selon leurs préférences
        users = User.objects.filter(is_active=True)
        notifications = []
        
        for user in users:
            try:
                preferences = NotificationPreference.objects.get(user=user)
                # Vérifier les préférences selon le type de notification
                if notification_type == 'promotion' and not preferences.push_promotions:
                    continue
                elif notification_type == 'order' and not preferences.push_orders:
                    continue
                elif notification_type == 'system' and not preferences.push_system:
                    continue
            except NotificationPreference.DoesNotExist:
                # Si pas de préférences, utiliser les valeurs par défaut (True)
                pass
            
            notification = Notification.objects.create(
                user=user,
                title=title,
                message=message,
                type=notification_type,
                related_id=related_id,
                link=link
            )
            notifications.append(notification)

            # Envoyer un email selon les préférences de l'utilisateur
            try:
                if notification_type == 'promotion':
                    EmailNotificationService.send_promotion_email(
                        user=user,
                        promotion_title=title,
                        promotion_description=message,
                        promotion_link=link
                    )
                elif notification_type == 'order':
                    # Pour les notifications de commande, nous aurions besoin de l'objet order
                    # Pour l'instant, envoyons un email système
                    EmailNotificationService.send_system_email(
                        user=user,
                        title=title,
                        message_content=message
                    )
                elif notification_type == 'system':
                    EmailNotificationService.send_system_email(
                        user=user,
                        title=title,
                        message_content=message
                    )
            except Exception as e:
                # Log l'erreur mais ne pas faire échouer la création de notification
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Erreur lors de l'envoi d'email à {user.email}: {e}")
        
        return Response(
            {'status': f'created {len(notifications)} notifications'},
            status=status.HTTP_201_CREATED
        )

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def test_email(self, request):
        """
        Endpoint pour tester l'envoi d'emails de notification (pour les utilisateurs connectés)
        """
        notification_type = request.data.get('type', 'system')
        title = request.data.get('title', 'Test de notification')
        message = request.data.get('message', 'Ceci est un test de notification par email.')

        user = request.user

        try:
            if notification_type == 'promotion':
                success = EmailNotificationService.send_promotion_email(
                    user=user,
                    promotion_title=title,
                    promotion_description=message,
                    promotion_link=request.data.get('link')
                )
            elif notification_type == 'order':
                success = EmailNotificationService.send_system_email(
                    user=user,
                    title=title,
                    message_content=message
                )
            else:  # system
                success = EmailNotificationService.send_system_email(
                    user=user,
                    title=title,
                    message_content=message
                )

            if success:
                return Response({
                    'status': 'success',
                    'message': f'Email de test envoyé à {user.email}'
                })
            else:
                return Response({
                    'status': 'error',
                    'message': 'Échec de l\'envoi d\'email'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Erreur: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NotificationPreferenceViewSet(viewsets.ModelViewSet):
    """
    API endpoint pour les préférences de notification.
    """
    serializer_class = NotificationPreferenceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Retourne les préférences de notification de l'utilisateur connecté.
        """
        return NotificationPreference.objects.filter(user=self.request.user)
    
    def list(self, request):
        """
        Retourne les préférences de notification de l'utilisateur ou crée un objet par défaut.
        """
        try:
            preference = NotificationPreference.objects.get(user=request.user)
        except NotificationPreference.DoesNotExist:
            preference = NotificationPreference.objects.create(user=request.user)
        
        serializer = self.get_serializer(preference)
        return Response(serializer.data)
    
    def update(self, request, *args, **kwargs):
        """
        Met à jour les préférences de notification de l'utilisateur.
        """
        try:
            preference = NotificationPreference.objects.get(user=request.user)
        except NotificationPreference.DoesNotExist:
            preference = NotificationPreference.objects.create(user=request.user)
        
        serializer = self.get_serializer(preference, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        
        return Response(serializer.data)
