# 🛒 JosNet E-commerce Platform

<div align="center">

**Plateforme E-commerce Moderne pour le Marché Burundais**

[![React](https://img.shields.io/badge/React-18.0+-61DAFB?style=flat-square&logo=react&logoColor=black)](https://reactjs.org/)
[![Django](https://img.shields.io/badge/Django-4.2+-092E20?style=flat-square&logo=django&logoColor=white)](https://djangoproject.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-3178C6?style=flat-square&logo=typescript&logoColor=white)](https://typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-336791?style=flat-square&logo=postgresql&logoColor=white)](https://postgresql.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg?style=flat-square)](LICENSE)

</div>

---

## 📋 Table des Matières

- [🎯 À Propos](#-à-propos)
- [✨ Fonctionnalités Principales](#-fonctionnalités-principales)
- [🏗️ Architecture](#️-architecture)
- [🚀 Installation Rapide](#-installation-rapide)
- [⚙️ Configuration](#️-configuration)
- [🔧 Développement](#-développement)
- [📱 API Documentation](#-api-documentation)
- [🧪 Tests](#-tests)
- [🚀 Déploiement](#-déploiement)
- [🤝 Contribution](#-contribution)

---

## 🎯 À Propos

**JosNet** est une plateforme e-commerce complète développée spécifiquement pour le marché burundais. Elle combine une interface utilisateur moderne avec des fonctionnalités avancées de gestion des produits, commandes et paiements.

### 🌟 Points Forts

- **🇧🇮 Localisé** : Support des paiements Mobile Money (Ecocash, Lumicash)
- **🚀 Performant** : Architecture Full-Stack moderne (React + Django)
- **🔒 Sécurisé** : Authentification JWT et protection des données
- **📱 Responsive** : Interface optimisée mobile-first
- **⚡ Temps Réel** : Notifications et mises à jour instantanées

---

## ✨ Fonctionnalités Principales

### 👥 **Gestion des Utilisateurs**
- Inscription/Connexion sécurisée avec JWT
- Profils utilisateurs avec gestion des adresses
- Système de rôles (Client, Vendeur, Admin, Super Admin)
- Récupération de mot de passe par email

### 🛍️ **Catalogue Produits**
- Recherche avancée avec filtres intelligents
- Catégories hiérarchiques
- Système d'avis et de notation
- Gestion des stocks en temps réel
- Upload et optimisation d'images

### 🛒 **Processus d'Achat**
- Panier persistant multi-appareils
- Checkout sécurisé en plusieurs étapes
- Calcul automatique des frais de livraison
- Codes promotionnels et réductions

### 💳 **Système de Paiement**
- **Mobile Money** : Ecocash, Lumicash
- **Cartes bancaires** via Stripe
- **Paiement à la livraison**
- Historique des transactions
- Remboursements automatisés

### 📦 **Gestion des Commandes**
- Suivi en temps réel des commandes
- Notifications automatiques (Email/SMS)
- Gestion des retours et réclamations
- Génération de factures PDF

### 👨‍💼 **Administration**
- Dashboard avec analytics en temps réel
- Gestion des utilisateurs et permissions
- Modération du contenu
- Rapports détaillés exportables
- Configuration système avancée

---

## 🏗️ Architecture

### **Frontend (React)**
```
src/
├── components/          # Composants réutilisables
│   ├── ui/             # Composants UI de base
│   ├── layout/         # Composants de mise en page
│   ├── auth/           # Composants d'authentification
│   ├── products/       # Composants produits
│   └── admin/          # Interface d'administration
├── pages/              # Pages de l'application
├── services/           # Services API
├── contexts/           # Contextes React
├── hooks/              # Hooks personnalisés
└── utils/              # Utilitaires
```

### **Backend (Django)**
```
josnet_backend/
├── authentication/     # Gestion des utilisateurs
├── products/          # Gestion des produits
├── orders/            # Gestion des commandes
├── payments/          # Système de paiement
├── messaging/         # Système de messagerie
├── cms/               # Gestion de contenu
├── analytics/         # Analytics et logs
└── core/              # Fonctionnalités communes
```

### **Technologies Utilisées**

**Frontend:**
- **React 18** - Framework UI moderne
- **TypeScript** - Typage statique
- **Vite** - Build tool rapide
- **Tailwind CSS** - Framework CSS utilitaire
- **shadcn/ui** - Composants UI élégants
- **React Query** - Gestion d'état serveur
- **React Router** - Navigation côté client

**Backend:**
- **Django 4.2** - Framework web Python
- **Django REST Framework** - API REST
- **PostgreSQL** - Base de données principale
- **Redis** - Cache et sessions
- **Celery** - Tâches asynchrones
- **JWT** - Authentification sécurisée

**DevOps & Déploiement:**
- **Docker** - Containerisation
- **Nginx** - Serveur web et proxy
- **Gunicorn** - Serveur WSGI
- **GitHub Actions** - CI/CD
- **Cloudflare** - CDN et sécurité

---

## 🚀 Installation Rapide

### **Prérequis**
- **Node.js** 18+ et npm
- **Python** 3.11+
- **PostgreSQL** 15+
- **Redis** 7+
- **Git**

### **1. Cloner le Projet**
```bash
git clone https://github.com/votre-username/josnet-ecommerce.git
cd josnet-ecommerce
```

### **2. Configuration Frontend**
```bash
# Installer les dépendances
npm install

# Copier le fichier de configuration
cp .env.example .env

# Démarrer le serveur de développement
npm run dev
```

### **3. Configuration Backend**
```bash
# Naviguer vers le backend
cd josnet_backend

# Créer un environnement virtuel
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows

# Installer les dépendances
pip install -r requirements.txt

# Configuration de la base de données
python manage.py migrate

# Créer un superutilisateur
python manage.py createsuperuser

# Démarrer le serveur
python manage.py runserver
```

### **4. Accès à l'Application**
- **Frontend** : http://localhost:8080
- **Backend API** : http://localhost:8000
- **Admin Django** : http://localhost:8000/admin

---

## ⚙️ Configuration

### **Variables d'Environnement Frontend (.env)**
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=10000

# Payment Configuration
VITE_STRIPE_PUBLIC_KEY=pk_test_...
VITE_ECOCASH_MERCHANT_ID=your_merchant_id

# App Configuration
VITE_APP_NAME=JosNet E-commerce
VITE_APP_VERSION=1.0.0
```

### **Variables d'Environnement Backend (.env)**
```env
# Django Configuration
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/josnet_db

# Redis
REDIS_URL=redis://localhost:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Payment Gateways
STRIPE_SECRET_KEY=sk_test_...
ECOCASH_API_KEY=your_ecocash_api_key
LUMICASH_API_KEY=your_lumicash_api_key

# File Storage
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_STORAGE_BUCKET_NAME=josnet-media
```

---

## 🔧 Développement

### **Scripts Disponibles**

**Frontend:**
```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run preview      # Prévisualiser le build
npm run lint         # Linter le code
npm run type-check   # Vérification TypeScript
```

**Backend:**
```bash
python manage.py runserver     # Serveur de développement
python manage.py test          # Lancer les tests
python manage.py migrate       # Appliquer les migrations
python manage.py collectstatic # Collecter les fichiers statiques
python manage.py shell         # Shell Django interactif
```

### **Structure des Commits**
Nous utilisons [Conventional Commits](https://www.conventionalcommits.org/) :

```
feat: ajouter système de paiement mobile money
fix: corriger bug de calcul des frais de livraison
docs: mettre à jour la documentation API
style: améliorer le design de la page produit
refactor: optimiser les requêtes de base de données
test: ajouter tests pour l'authentification
```

### **Workflow de Développement**
1. **Fork** le projet
2. **Créer** une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. **Commit** les changements (`git commit -m 'feat: ajouter nouvelle fonctionnalité'`)
4. **Push** vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. **Ouvrir** une Pull Request

---

## 📱 API Documentation

### **Endpoints Principaux**

**Authentication:**
```http
POST /api/auth/register/     # Inscription
POST /api/auth/login/        # Connexion
POST /api/auth/refresh/      # Rafraîchir le token
POST /api/auth/logout/       # Déconnexion
```

**Products:**
```http
GET    /api/products/        # Liste des produits
GET    /api/products/{id}/   # Détail d'un produit
POST   /api/products/        # Créer un produit (vendeur)
PUT    /api/products/{id}/   # Modifier un produit
DELETE /api/products/{id}/   # Supprimer un produit
```

**Orders:**
```http
GET  /api/orders/           # Mes commandes
POST /api/orders/           # Créer une commande
GET  /api/orders/{id}/      # Détail d'une commande
PUT  /api/orders/{id}/      # Modifier une commande
```

**Payments:**
```http
POST /api/payments/stripe/     # Paiement Stripe
POST /api/payments/ecocash/    # Paiement Ecocash
POST /api/payments/lumicash/   # Paiement Lumicash
GET  /api/payments/history/    # Historique des paiements
```

### **Documentation Interactive**
- **Swagger UI** : http://localhost:8000/api/docs/
- **ReDoc** : http://localhost:8000/api/redoc/

---

## 🧪 Tests

### **Frontend Tests**
```bash
npm run test              # Tests unitaires
npm run test:coverage     # Couverture de code
npm run test:e2e          # Tests end-to-end
```

### **Backend Tests**
```bash
python manage.py test                    # Tous les tests
python manage.py test authentication     # Tests d'authentification
python manage.py test products          # Tests produits
python manage.py test orders            # Tests commandes
```

### **Couverture de Code**
- **Frontend** : 85%+ avec Jest et React Testing Library
- **Backend** : 90%+ avec Django Test Framework

---

## 🚀 Déploiement

### **Déploiement avec Docker**

**1. Build des images:**
```bash
# Frontend
docker build -t josnet-frontend .

# Backend
cd josnet_backend
docker build -t josnet-backend .
```

**2. Docker Compose:**
```bash
docker-compose up -d
```

### **Déploiement en Production**

**Frontend (Vercel/Netlify):**
```bash
npm run build
# Déployer le dossier dist/
```

**Backend (Railway/Heroku):**
```bash
# Configurer les variables d'environnement
# Déployer avec Git
git push heroku main
```

### **Variables d'Environnement Production**
```env
DEBUG=False
ALLOWED_HOSTS=yourdomain.com
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
```

---

## 🤝 Contribution

Nous accueillons toutes les contributions ! Voici comment vous pouvez aider :

### **Types de Contributions**
- 🐛 **Bug Reports** : Signaler des bugs
- 💡 **Feature Requests** : Proposer de nouvelles fonctionnalités
- 📖 **Documentation** : Améliorer la documentation
- 🔧 **Code** : Contribuer au code source
- 🎨 **Design** : Améliorer l'interface utilisateur

### **Guidelines**
1. **Lire** le [Code of Conduct](CODE_OF_CONDUCT.md)
2. **Vérifier** les issues existantes
3. **Suivre** les conventions de code
4. **Ajouter** des tests pour les nouvelles fonctionnalités
5. **Mettre à jour** la documentation si nécessaire

### **Développeurs Principaux**
- **[@josnet-team](https://github.com/josnet-team)** - Équipe de développement principale

---

## 📄 Licence

Ce projet est sous licence **MIT**. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

---

## 📞 Support

- **📧 Email** : <EMAIL>
- **💬 Discord** : [Rejoindre notre serveur](https://discord.gg/josnet)
- **📱 WhatsApp** : +257 XX XX XX XX
- **🐛 Issues** : [GitHub Issues](https://github.com/josnet/issues)

---

## 🙏 Remerciements

- **Équipe de développement JosNet**
- **Communauté open source**
- **Contributeurs et testeurs**
- **Partenaires technologiques**

---

<div align="center">

**Fait avec ❤️ au Burundi pour le Burundi**

⭐ **N'oubliez pas de donner une étoile si ce projet vous plaît !** ⭐

</div>
