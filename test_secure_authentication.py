#!/usr/bin/env python3
"""
Script pour tester le système d'authentification sécurisé
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
import json

User = get_user_model()

def test_gmail_validation():
    """Tester la validation Gmail"""
    print("📧 TEST DE LA VALIDATION GMAIL")
    print("=" * 50)
    
    try:
        from authentication.validators import validate_gmail_email, is_gmail_email
        
        # Tests positifs
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        print("✅ Emails Gmail valides:")
        for email in valid_emails:
            try:
                validate_gmail_email(email)
                print(f"   ✓ {email}")
            except Exception as e:
                print(f"   ✗ {email}: {e}")
        
        # Tests négatifs
        invalid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '.<EMAIL>',
            '<EMAIL>'
        ]
        
        print("\n❌ Emails non-Gmail ou invalides:")
        for email in invalid_emails:
            if not is_gmail_email(email):
                print(f"   ✓ {email} (correctement rejeté)")
            else:
                print(f"   ✗ {email} (incorrectement accepté)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def test_registration_api():
    """Tester l'API d'inscription"""
    print(f"\n🔐 TEST DE L'API D'INSCRIPTION")
    print("=" * 50)
    
    client = Client()
    
    # Test avec email Gmail valide
    registration_data = {
        'first_name': 'Test',
        'last_name': 'User',
        'email': '<EMAIL>',
        'password': 'SecurePass123!',
        'confirm_password': 'SecurePass123!'
    }
    
    print("📝 Test inscription avec Gmail valide...")
    response = client.post(
        '/api/v1/auth/register/',
        data=json.dumps(registration_data),
        content_type='application/json'
    )
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 201:
        data = response.json()
        print("✅ Inscription réussie!")
        print(f"   User ID: {data['user']['id']}")
        print(f"   Email: {data['user']['email']}")
        print(f"   Vérifié: {data['user']['is_verified']}")
        print(f"   Auto-login: {data.get('auto_login', False)}")
        print(f"   Access token: {'Oui' if data.get('access') else 'Non'}")
        print(f"   Email envoyé: {data.get('email_verification', {}).get('sent', 'N/A')}")
        
        return True, data
    else:
        print(f"❌ Échec inscription: {response.content.decode()}")
        return False, None

def test_email_verification():
    """Tester la vérification d'email"""
    print(f"\n✉️ TEST DE LA VÉRIFICATION D'EMAIL")
    print("=" * 50)
    
    # Créer un utilisateur de test
    user = User.objects.create_user(
        email='<EMAIL>',
        password='TestPass123!',
        first_name='Verify',
        last_name='Test',
        is_verified=False
    )
    
    print(f"✅ Utilisateur créé: {user.email}")
    
    # Générer un token de vérification
    from django.utils.http import urlsafe_base64_encode
    from django.utils.encoding import force_bytes
    from django.contrib.auth.tokens import default_token_generator
    
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)
    
    # Gérer le cas où uid est déjà une string
    if isinstance(uid, bytes):
        uid_str = uid.decode()
    else:
        uid_str = uid
        
    verification_token = f"{uid_str}-{token}"
    
    print(f"🔑 Token généré: {verification_token[:30]}...")
    
    # Tester la vérification
    client = Client()
    response = client.post(
        '/api/v1/auth/email/verify/',
        data=json.dumps({'token': verification_token}),
        content_type='application/json'
    )
    
    print(f"📡 Status vérification: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Vérification réussie!")
        print(f"   Message: {data.get('message', 'N/A')}")
        
        # Vérifier que l'utilisateur est maintenant vérifié
        user.refresh_from_db()
        print(f"   Utilisateur vérifié: {user.is_verified}")
        
        return True
    else:
        print(f"❌ Échec vérification: {response.content.decode()}")
        return False

def test_email_sending():
    """Tester l'envoi d'emails"""
    print(f"\n📬 TEST D'ENVOI D'EMAILS")
    print("=" * 50)
    
    # Créer un utilisateur de test
    user = User.objects.create_user(
        email='<EMAIL>',
        password='TestPass123!',
        first_name='Email',
        last_name='Test'
    )
    
    print(f"✅ Utilisateur créé: {user.email}")
    
    # Tester l'envoi d'email de vérification
    try:
        from authentication.utils import send_verification_email
        result = send_verification_email(user)
        
        if result:
            print("✅ Email de vérification envoyé avec succès")
        else:
            print("❌ Échec envoi email de vérification")
            
    except Exception as e:
        print(f"❌ Erreur envoi email de vérification: {e}")
        result = False
    
    # Tester l'envoi d'email de bienvenue
    try:
        from authentication.utils import send_welcome_email
        welcome_result = send_welcome_email(user)
        
        if welcome_result:
            print("✅ Email de bienvenue envoyé avec succès")
        else:
            print("❌ Échec envoi email de bienvenue")
            
    except Exception as e:
        print(f"❌ Erreur envoi email de bienvenue: {e}")
        welcome_result = False
    
    return result and welcome_result

def create_test_user_with_gmail():
    """Créer un utilisateur de test avec Gmail pour les tests frontend"""
    print(f"\n👤 CRÉATION D'UTILISATEUR DE TEST")
    print("=" * 50)
    
    # Supprimer l'utilisateur s'il existe
    User.objects.filter(email='<EMAIL>').delete()
    
    user = User.objects.create_user(
        email='<EMAIL>',
        password='FrontendTest123!',
        first_name='Frontend',
        last_name='Test',
        is_verified=False
    )
    
    print(f"✅ Utilisateur de test créé:")
    print(f"   Email: {user.email}")
    print(f"   Mot de passe: FrontendTest123!")
    print(f"   Vérifié: {user.is_verified}")
    
    # Générer un lien de vérification
    from django.utils.http import urlsafe_base64_encode
    from django.utils.encoding import force_bytes
    from django.contrib.auth.tokens import default_token_generator
    from django.conf import settings
    
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)
    
    if isinstance(uid, bytes):
        uid_str = uid.decode()
    else:
        uid_str = uid
        
    verification_token = f"{uid_str}-{token}"
    verification_link = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
    
    print(f"\n🔗 Lien de vérification:")
    print(f"   {verification_link}")
    
    return user, verification_link

def show_testing_instructions():
    """Afficher les instructions de test"""
    print(f"\n📋 INSTRUCTIONS DE TEST FRONTEND")
    print("=" * 50)
    
    print(f"🔧 TESTS À EFFECTUER:")
    print(f"   1. Inscription avec email non-Gmail (doit échouer)")
    print(f"   2. Inscription avec email Gmail valide (doit réussir)")
    print(f"   3. Connexion automatique après inscription")
    print(f"   4. Vérification que les données sont réelles (pas mockées)")
    print(f"   5. Vérification d'email via le lien reçu")
    print(f"   6. Réception de l'email de bienvenue")
    
    print(f"\n📧 EMAILS DE TEST:")
    print(f"   • Valides: <EMAIL>, <EMAIL>")
    print(f"   • Invalides: <EMAIL>, <EMAIL>")
    
    print(f"\n🌐 URLS DE TEST:")
    print(f"   • Inscription: http://localhost:8080/register")
    print(f"   • Connexion: http://localhost:8080/login")
    print(f"   • Compte: http://localhost:8080/account")

def main():
    """Fonction principale"""
    print("🔐 TEST COMPLET DU SYSTÈME D'AUTHENTIFICATION SÉCURISÉ")
    print("=" * 70)
    
    # Tests
    tests = [
        ("Validation Gmail", test_gmail_validation),
        ("API d'inscription", lambda: test_registration_api()[0]),
        ("Vérification d'email", test_email_verification),
        ("Envoi d'emails", test_email_sending),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name.upper()} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results[test_name] = False
    
    # Créer un utilisateur de test
    print(f"\n{'='*20} UTILISATEUR DE TEST {'='*20}")
    user, link = create_test_user_with_gmail()
    
    # Afficher les instructions
    show_testing_instructions()
    
    # Résumé final
    print(f"\n🎯 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    print(f"\n📊 Score: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 SYSTÈME D'AUTHENTIFICATION SÉCURISÉ FONCTIONNEL!")
        print("\n📋 PRÊT POUR LES TESTS FRONTEND:")
        print("   ✅ Validation Gmail active")
        print("   ✅ Emails de vérification fonctionnels")
        print("   ✅ Connexion automatique configurée")
        print("   ✅ Données réelles après inscription")
    else:
        print("\n⚠️ PROBLÈMES DÉTECTÉS")
        print("   Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
