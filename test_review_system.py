#!/usr/bin/env python3
"""
Script pour tester le système d'avis complet
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from products.models import Product, ProductReview
from orders.models import Order, OrderItem

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access')
    return None

def create_test_data():
    """Créer des données de test pour les avis"""
    print("🔧 Création de données de test...")
    
    try:
        # Récupérer l'utilisateur de test
        user = User.objects.get(email=TEST_EMAIL)
        print(f"   👤 Utilisateur: {user.email}")
        
        # Récupérer ou créer un produit de test
        product = Product.objects.first()
        if not product:
            print("   ❌ Aucun produit trouvé dans la base de données")
            return None, None
        
        print(f"   📦 Produit: {product.name} (ID: {product.id})")
        
        # Créer une commande pour permettre les avis vérifiés
        order, created = Order.objects.get_or_create(
            user=user,
            status='delivered',
            defaults={
                'email': user.email,
                'subtotal': 50000,
                'total': 50000,
                'currency': 'BIF'
            }
        )
        
        if created:
            print(f"   ✅ Commande créée (ID: {order.id})")
            
            # Ajouter le produit à la commande
            OrderItem.objects.get_or_create(
                order=order,
                product=product,
                defaults={
                    'quantity': 1,
                    'price': 50000
                }
            )
            print(f"   ✅ Produit ajouté à la commande")
        else:
            print(f"   ✅ Commande existante utilisée (ID: {order.id})")
        
        return user, product
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None, None

def test_review_api(token, product_id):
    """Tester l'API des avis"""
    print(f"\n📝 Test de l'API des avis pour le produit {product_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test 1: Récupérer les statistiques d'avis
    print("1. Test des statistiques d'avis...")
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/stats/",
            params={'product': product_id}
        )
        
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Statistiques récupérées")
            print(f"   📊 Total avis: {stats.get('total_reviews', 0)}")
            print(f"   ⭐ Note moyenne: {stats.get('average_rating', 0)}")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 2: Récupérer les avis du produit
    print("\n2. Test de récupération des avis...")
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/",
            params={'product': product_id}
        )
        
        if response.status_code == 200:
            data = response.json()
            reviews = data.get('results', [])
            print(f"   ✅ Avis récupérés: {len(reviews)} avis")
            
            for review in reviews[:2]:  # Afficher les 2 premiers
                print(f"      - {review.get('title')} ({review.get('rating')}⭐)")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 3: Créer un nouvel avis
    print("\n3. Test de création d'avis...")
    review_data = {
        "product": product_id,
        "rating": 5,
        "title": "Excellent produit !",
        "comment": "Je recommande vivement ce produit. La qualité est au rendez-vous et la livraison a été rapide.",
        "pros": "Qualité excellente, livraison rapide, bon rapport qualité-prix",
        "cons": "Rien à redire"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/products/reviews/", 
            json=review_data, 
            headers=headers
        )
        
        if response.status_code == 201:
            review = response.json()
            print(f"   ✅ Avis créé (ID: {review.get('id')})")
            print(f"   📝 Titre: {review.get('title')}")
            print(f"   ⭐ Note: {review.get('rating')}")
            print(f"   ✅ Achat vérifié: {review.get('is_verified_purchase')}")
            return review.get('id')
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def test_review_voting(token, review_id):
    """Tester le système de vote sur les avis"""
    print(f"\n👍 Test du système de vote pour l'avis {review_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/products/reviews/{review_id}/vote_helpful/",
            json={"is_helpful": True},
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Vote enregistré")
            print(f"   👍 Votes utiles: {result.get('helpful_count', 0)}")
            print(f"   👎 Votes non utiles: {result.get('not_helpful_count', 0)}")
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🧪 TEST COMPLET DU SYSTÈME D'AVIS")
    print("=" * 50)
    
    # 1. Créer des données de test
    user, product = create_test_data()
    if not user or not product:
        print("❌ Impossible de créer les données de test")
        return
    
    # 2. Authentification
    print(f"\n🔐 Authentification...")
    token = get_auth_token()
    if not token:
        print("❌ Impossible de s'authentifier")
        return
    print("✅ Authentification réussie")
    
    # 3. Tester l'API des avis
    review_id = test_review_api(token, product.id)
    
    # 4. Tester le système de vote (si un avis a été créé)
    if review_id:
        vote_success = test_review_voting(token, review_id)
    else:
        vote_success = False
    
    print(f"\n📊 RÉSUMÉ DES TESTS:")
    print(f"   Données de test: {'✅' if user and product else '❌'}")
    print(f"   Authentification: {'✅' if token else '❌'}")
    print(f"   API des avis: {'✅' if review_id else '❌'}")
    print(f"   Système de vote: {'✅' if vote_success else '❌'}")
    
    if all([user, product, token, review_id]):
        print(f"\n🎉 SYSTÈME D'AVIS FONCTIONNEL!")
        print(f"   ✅ Les avis peuvent être créés et consultés")
        print(f"   ✅ Les achats vérifiés sont détectés")
        print(f"   ✅ Le système de vote fonctionne")
        print(f"   🌐 Testez l'interface: http://localhost:8080/product/{product.id}")
        print(f"   📝 Onglet 'Avis' dans la page produit")
    else:
        print(f"\n❌ CERTAINES FONCTIONNALITÉS NE MARCHENT PAS")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
