import React, { useState, useEffect } from 'react';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Filter, X } from "lucide-react";
import { Category, ProductFilterParams } from '@/services/productApi';
import { formatPrice } from '@/lib/utils';

interface ProductFiltersProps {
  categories: Category[];
  filters: ProductFilterParams;
  onFilterChange: (filters: ProductFilterParams) => void;
  maxPrice: number;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  categories,
  filters,
  onFilterChange,
  maxPrice = 1000
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<ProductFilterParams>(filters);
  const [priceRange, setPriceRange] = useState<[number, number]>([
    filters.min_price || 0,
    filters.max_price || maxPrice
  ]);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
    setPriceRange([
      filters.min_price || 0,
      filters.max_price || maxPrice
    ]);
  }, [filters, maxPrice]);

  const handlePriceChange = (value: number[]) => {
    setPriceRange([value[0], value[1]]);
    setLocalFilters({
      ...localFilters,
      min_price: value[0],
      max_price: value[1]
    });
  };

  const handleCategoryChange = (slug: string, checked: boolean) => {
    const currentCategories = localFilters.category || [];
    let newCategories: string[];

    if (checked) {
      newCategories = [...currentCategories, slug];
    } else {
      newCategories = currentCategories.filter(cat => cat !== slug);
    }

    setLocalFilters({
      ...localFilters,
      category: newCategories.length > 0 ? newCategories : undefined
    });
  };

  const handleCheckboxChange = (field: keyof ProductFilterParams, checked: boolean) => {
    setLocalFilters({
      ...localFilters,
      [field]: checked
    });
  };

  const handleStatusChange = (value: string) => {
    setLocalFilters({
      ...localFilters,
      status: value as 'draft' | 'published' | 'archived'
    });
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalFilters({
      ...localFilters,
      name: e.target.value || undefined
    });
  };

  const applyFilters = () => {
    onFilterChange(localFilters);
    setIsOpen(false);
  };

  const resetFilters = () => {
    const resetFilters: ProductFilterParams = {};
    setLocalFilters(resetFilters);
    setPriceRange([0, maxPrice]);
    onFilterChange(resetFilters);
    setIsOpen(false);
  };

  // Count active filters
  const countActiveFilters = () => {
    let count = 0;
    if (localFilters.name) count++;
    if (localFilters.min_price || localFilters.max_price) count++;
    if (localFilters.category && localFilters.category.length > 0) count++;
    if (localFilters.status) count++;
    if (localFilters.is_featured !== undefined) count++;
    if (localFilters.is_digital !== undefined) count++;
    if (localFilters.in_stock !== undefined) count++;
    if (localFilters.on_sale !== undefined) count++;
    return count;
  };

  return (
    <div className="mb-6">
      <div className="flex flex-wrap items-center gap-2">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              <span>Filtres</span>
              {countActiveFilters() > 0 && (
                <Badge className="ml-1 bg-primary">{countActiveFilters()}</Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent className="w-[350px] sm:w-[450px] overflow-y-auto">
            <SheetHeader>
              <SheetTitle>Filtrer les produits</SheetTitle>
              <SheetDescription>
                Affinez votre liste de produits en utilisant les filtres ci-dessous.
              </SheetDescription>
            </SheetHeader>

            <div className="py-6 space-y-6">
              {/* Search by name */}
              <div className="space-y-2">
                <Label htmlFor="name">Recherche par nom</Label>
                <Input
                  id="name"
                  placeholder="Nom du produit"
                  value={localFilters.name || ''}
                  onChange={handleNameChange}
                />
              </div>

              {/* Price range */}
              <div className="space-y-4">
                <div>
                  <Label>Fourchette de prix</Label>
                  <div className="flex items-center justify-between mt-2">
                    <span>{formatPrice(priceRange[0])}</span>
                    <span>{formatPrice(priceRange[1])}</span>
                  </div>
                </div>
                <Slider
                  defaultValue={[priceRange[0], priceRange[1]]}
                  value={[priceRange[0], priceRange[1]]}
                  max={maxPrice}
                  step={1}
                  onValueChange={handlePriceChange}
                />
              </div>

              {/* Categories */}
              <div className="space-y-2">
                <Label>Catégories</Label>
                <div className="space-y-2 max-h-[200px] overflow-y-auto pr-2">
                  {Array.isArray(categories) && categories.length > 0 ? (
                    categories.map((category) => (
                      <div key={category.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`category-${category.id}`}
                          checked={localFilters.category?.includes(category.slug) || false}
                          onCheckedChange={(checked) =>
                            handleCategoryChange(category.slug, checked as boolean)
                          }
                        />
                        <label
                          htmlFor={`category-${category.id}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {category.name}
                        </label>
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-muted-foreground">Chargement des catégories...</div>
                  )}
                </div>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Statut</Label>
                <Select
                  value={localFilters.status || ''}
                  onValueChange={handleStatusChange}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Tous les statuts" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tous les statuts</SelectItem>
                    <SelectItem value="published">Publié</SelectItem>
                    <SelectItem value="draft">Brouillon</SelectItem>
                    <SelectItem value="archived">Archivé</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Other filters */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="featured"
                    checked={localFilters.is_featured || false}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange('is_featured', checked as boolean)
                    }
                  />
                  <label
                    htmlFor="featured"
                    className="text-sm font-medium leading-none"
                  >
                    Produits en vedette
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="digital"
                    checked={localFilters.is_digital || false}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange('is_digital', checked as boolean)
                    }
                  />
                  <label
                    htmlFor="digital"
                    className="text-sm font-medium leading-none"
                  >
                    Produits numériques
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="in_stock"
                    checked={localFilters.in_stock || false}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange('in_stock', checked as boolean)
                    }
                  />
                  <label
                    htmlFor="in_stock"
                    className="text-sm font-medium leading-none"
                  >
                    En stock
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="on_sale"
                    checked={localFilters.on_sale || false}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange('on_sale', checked as boolean)
                    }
                  />
                  <label
                    htmlFor="on_sale"
                    className="text-sm font-medium leading-none"
                  >
                    En promotion
                  </label>
                </div>
              </div>
            </div>

            <SheetFooter className="flex flex-row gap-2 sm:space-x-0">
              <Button variant="outline" onClick={resetFilters} className="flex-1">
                Réinitialiser
              </Button>
              <Button onClick={applyFilters} className="flex-1">
                Appliquer les filtres
              </Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>

        {/* Active filters display */}
        {countActiveFilters() > 0 && (
          <div className="flex flex-wrap gap-2">
            {localFilters.name && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>Nom: {localFilters.name}</span>
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => {
                    setLocalFilters({...localFilters, name: undefined});
                    onFilterChange({...localFilters, name: undefined});
                  }}
                />
              </Badge>
            )}

            {(localFilters.min_price !== undefined || localFilters.max_price !== undefined) && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>
                  Prix: {formatPrice(localFilters.min_price || 0)} - {formatPrice(localFilters.max_price || maxPrice)}
                </span>
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => {
                    setLocalFilters({...localFilters, min_price: undefined, max_price: undefined});
                    onFilterChange({...localFilters, min_price: undefined, max_price: undefined});
                    setPriceRange([0, maxPrice]);
                  }}
                />
              </Badge>
            )}

            {localFilters.status && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>Statut: {localFilters.status}</span>
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => {
                    setLocalFilters({...localFilters, status: undefined});
                    onFilterChange({...localFilters, status: undefined});
                  }}
                />
              </Badge>
            )}

            {/* Reset all filters button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="h-6 px-2 text-xs"
            >
              Effacer tous les filtres
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductFilters;
