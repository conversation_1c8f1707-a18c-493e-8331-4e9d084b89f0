import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Package, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Types pour les commandes
interface OrderItem {
  id: string;
  productName: string;
  quantity: number;
  price: string;
  image: string;
}

interface Order {
  id: string;
  date: string;
  status: string;
  items: OrderItem[];
}

interface CreateReturnFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function CreateReturnForm({ isOpen, onOpenChange, onSuccess }: CreateReturnFormProps) {
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // État du formulaire
  const [selectedOrderId, setSelectedOrderId] = useState<string>('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [returnReason, setReturnReason] = useState<string>('');
  const [additionalInfo, setAdditionalInfo] = useState<string>('');
  
  // Données simulées pour les commandes
  const orders: Order[] = [
    {
      id: "CMD-12345",
      date: "15/05/2025",
      status: "Livré",
      items: [
        {
          id: "ITEM-001",
          productName: "Routeur Wi-Fi 6 Haute Performance",
          quantity: 1,
          price: "129,99 €",
          image: "/placeholder.svg"
        },
        {
          id: "ITEM-002",
          productName: "Câble Ethernet Cat.8 3m",
          quantity: 2,
          price: "14,99 €",
          image: "/placeholder.svg"
        }
      ]
    },
    {
      id: "CMD-12344",
      date: "02/04/2025",
      status: "Livré",
      items: [
        {
          id: "ITEM-003",
          productName: "Disque Dur Externe 2TB",
          quantity: 1,
          price: "89,99 €",
          image: "/placeholder.svg"
        }
      ]
    }
  ];
  
  // Trouver la commande sélectionnée
  const selectedOrder = orders.find(order => order.id === selectedOrderId);
  
  // Gérer la sélection d'un article
  const handleItemSelection = (itemId: string) => {
    setSelectedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  };
  
  // Passer à l'étape suivante
  const handleNextStep = () => {
    if (step === 1 && !selectedOrderId) {
      setError("Veuillez sélectionner une commande");
      return;
    }
    
    if (step === 2 && selectedItems.length === 0) {
      setError("Veuillez sélectionner au moins un article à retourner");
      return;
    }
    
    if (step === 3 && !returnReason) {
      setError("Veuillez sélectionner une raison de retour");
      return;
    }
    
    setError(null);
    setStep(prev => prev + 1);
  };
  
  // Revenir à l'étape précédente
  const handlePreviousStep = () => {
    setError(null);
    setStep(prev => prev - 1);
  };
  
  // Soumettre le formulaire
  const handleSubmit = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: "Demande de retour créée",
        description: "Votre demande de retour a été soumise avec succès.",
      });
      
      onSuccess();
      onOpenChange(false);
      
      // Réinitialiser le formulaire
      setStep(1);
      setSelectedOrderId('');
      setSelectedItems([]);
      setReturnReason('');
      setAdditionalInfo('');
    } catch (err) {
      setError("Une erreur est survenue lors de la création de la demande de retour. Veuillez réessayer.");
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Créer une demande de retour</DialogTitle>
          <DialogDescription>
            Suivez les étapes pour créer une demande de retour pour vos articles.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-start gap-2 mb-4">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}
        
        {step === 1 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Étape 1: Sélectionner une commande</h3>
            
            <div className="space-y-2">
              <Label htmlFor="order">Commande à retourner</Label>
              <Select value={selectedOrderId} onValueChange={setSelectedOrderId}>
                <SelectTrigger id="order">
                  <SelectValue placeholder="Sélectionner une commande" />
                </SelectTrigger>
                <SelectContent>
                  {orders.map(order => (
                    <SelectItem key={order.id} value={order.id}>
                      {order.id} - {order.date}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {selectedOrderId && (
              <div className="border rounded-md p-3 bg-gray-50">
                <p className="text-sm font-medium">Détails de la commande</p>
                <p className="text-sm text-gray-500">Date: {selectedOrder?.date}</p>
                <p className="text-sm text-gray-500">Statut: {selectedOrder?.status}</p>
                <p className="text-sm text-gray-500">Articles: {selectedOrder?.items.length}</p>
              </div>
            )}
          </div>
        )}
        
        {step === 2 && selectedOrder && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Étape 2: Sélectionner les articles à retourner</h3>
            
            <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
              {selectedOrder.items.map(item => (
                <div 
                  key={item.id} 
                  className="flex items-center space-x-3 border rounded-md p-3 hover:bg-gray-50"
                >
                  <Checkbox 
                    id={item.id} 
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={() => handleItemSelection(item.id)}
                  />
                  <div className="h-12 w-12 bg-gray-100 rounded flex-shrink-0 flex items-center justify-center">
                    <Package className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <Label 
                      htmlFor={item.id} 
                      className="text-sm font-medium cursor-pointer"
                    >
                      {item.productName}
                    </Label>
                    <p className="text-xs text-gray-500">
                      Quantité: {item.quantity} | Prix: {item.price}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-sm text-gray-500">
              {selectedItems.length} article(s) sélectionné(s) sur {selectedOrder.items.length}
            </div>
          </div>
        )}
        
        {step === 3 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Étape 3: Raison du retour</h3>
            
            <div className="space-y-2">
              <Label htmlFor="reason">Raison du retour</Label>
              <Select value={returnReason} onValueChange={setReturnReason}>
                <SelectTrigger id="reason">
                  <SelectValue placeholder="Sélectionner une raison" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="defective">Article défectueux</SelectItem>
                  <SelectItem value="wrong_item">Article incorrect</SelectItem>
                  <SelectItem value="not_as_described">Ne correspond pas à la description</SelectItem>
                  <SelectItem value="no_longer_needed">Je n'en ai plus besoin</SelectItem>
                  <SelectItem value="other">Autre raison</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="additionalInfo">Informations supplémentaires (optionnel)</Label>
              <Textarea
                id="additionalInfo"
                placeholder="Décrivez votre problème ou fournissez des détails supplémentaires..."
                value={additionalInfo}
                onChange={(e) => setAdditionalInfo(e.target.value)}
                rows={4}
              />
            </div>
          </div>
        )}
        
        {step === 4 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Étape 4: Confirmation</h3>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Résumé de votre demande</CardTitle>
                <CardDescription>Vérifiez les informations avant de soumettre</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Commande:</span>
                  <span className="font-medium">{selectedOrderId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Articles à retourner:</span>
                  <span className="font-medium">{selectedItems.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Raison:</span>
                  <span className="font-medium">
                    {returnReason === 'defective' && 'Article défectueux'}
                    {returnReason === 'wrong_item' && 'Article incorrect'}
                    {returnReason === 'not_as_described' && 'Ne correspond pas à la description'}
                    {returnReason === 'no_longer_needed' && 'Je n\'en ai plus besoin'}
                    {returnReason === 'other' && 'Autre raison'}
                  </span>
                </div>
                {additionalInfo && (
                  <div className="pt-2">
                    <span className="text-gray-500 block">Informations supplémentaires:</span>
                    <p className="mt-1 text-sm border-l-2 border-gray-200 pl-3 italic">
                      {additionalInfo}
                    </p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="bg-gray-50 text-xs text-gray-500 rounded-b-lg">
                Une fois votre demande soumise, vous recevrez un email de confirmation avec les instructions pour le retour.
              </CardFooter>
            </Card>
          </div>
        )}
        
        <DialogFooter className="flex justify-between sm:justify-between">
          {step > 1 ? (
            <Button 
              type="button" 
              variant="outline" 
              onClick={handlePreviousStep}
              disabled={isLoading}
            >
              Précédent
            </Button>
          ) : (
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Annuler
            </Button>
          )}
          
          {step < 4 ? (
            <Button 
              type="button" 
              onClick={handleNextStep}
              disabled={isLoading}
            >
              Suivant
            </Button>
          ) : (
            <Button 
              type="button" 
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Soumission...
                </>
              ) : (
                'Soumettre la demande'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
