#!/usr/bin/env python3
"""
Script pour tester la correction de l'envoi de messages dans les réclamations
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_claim_message_sending():
    """Tester l'envoi de messages dans les réclamations"""
    print("💬 TEST DE L'ENVOI DE MESSAGES DANS LES RÉCLAMATIONS")
    print("=" * 60)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        print("🔐 Authentification...")
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            print(f"   ✅ Authentification réussie")
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Récupérer les réclamations
            print(f"\n📋 Récupération des réclamations...")
            claims_response = requests.get(f"{API_BASE_URL}/orders/claims/", headers=headers)
            
            if claims_response.status_code == 200:
                claims = claims_response.json().get('results', [])
                print(f"   ✅ {len(claims)} réclamations trouvées")
                
                if claims:
                    claim = claims[0]
                    claim_id = claim['id']
                    claim_number = claim['claim_number']
                    claim_user = claim['user']['email']
                    
                    print(f"\n📝 Test sur la réclamation:")
                    print(f"   🆔 ID: {claim_id}")
                    print(f"   📝 Numéro: {claim_number}")
                    print(f"   👤 Propriétaire: {claim_user}")
                    
                    # Tenter d'envoyer un message
                    print(f"\n📤 Envoi d'un message de test...")
                    message_data = {
                        "message": "Message de test depuis l'admin après correction des permissions. Nous avons bien reçu votre réclamation et nous la traitons."
                    }
                    
                    send_response = requests.post(
                        f"{API_BASE_URL}/orders/claims/{claim_id}/send_message/",
                        json=message_data,
                        headers=headers
                    )
                    
                    print(f"   📊 Statut de la réponse: {send_response.status_code}")

                    if send_response.status_code in [200, 201]:  # 201 = Created
                        response_data = send_response.json()
                        print(f"   ✅ Message envoyé avec succès!")
                        print(f"   📝 ID du message: {response_data.get('id')}")
                        print(f"   👤 Expéditeur: {response_data.get('sender_display')}")
                        print(f"   💬 Message: {response_data.get('message')[:50]}...")
                        
                        # Vérifier que le message apparaît dans la réclamation
                        print(f"\n🔍 Vérification du message dans la réclamation...")
                        detail_response = requests.get(f"{API_BASE_URL}/orders/claims/{claim_id}/", headers=headers)
                        
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            messages = detail_data.get('messages', [])
                            print(f"   📊 Total messages: {len(messages)}")
                            
                            # Trouver notre message
                            our_message = None
                            for msg in messages:
                                if msg.get('message') == message_data['message']:
                                    our_message = msg
                                    break
                            
                            if our_message:
                                print(f"   ✅ Message trouvé dans la réclamation")
                                print(f"   👤 Expéditeur: {our_message.get('sender_display')}")
                                print(f"   📅 Date: {our_message.get('created_at')}")
                            else:
                                print(f"   ⚠️ Message non trouvé dans la réclamation")
                        
                        return True
                    elif send_response.status_code == 403:
                        print(f"   ❌ Erreur 403 - Permissions insuffisantes")
                        print(f"   📝 Réponse: {send_response.text}")
                        return False
                    else:
                        print(f"   ❌ Erreur: {send_response.status_code}")
                        print(f"   📝 Réponse: {send_response.text}")
                        return False
                else:
                    print(f"   ⚠️ Aucune réclamation trouvée pour tester")
                    return False
            else:
                print(f"   ❌ Erreur récupération réclamations: {claims_response.status_code}")
                return False
        else:
            print(f"   ❌ Échec authentification: {login_response.status_code}")
            print(f"   📝 Réponse: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_message_history():
    """Tester la récupération de l'historique des messages"""
    print(f"\n📚 TEST DE L'HISTORIQUE DES MESSAGES")
    print("=" * 50)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Récupérer les réclamations
            claims_response = requests.get(f"{API_BASE_URL}/orders/claims/", headers=headers)
            
            if claims_response.status_code == 200:
                claims = claims_response.json().get('results', [])
                
                if claims:
                    claim_id = claims[0]['id']
                    
                    # Tester l'endpoint des messages
                    messages_response = requests.get(
                        f"{API_BASE_URL}/orders/claims/{claim_id}/messages/",
                        headers=headers
                    )
                    
                    if messages_response.status_code == 200:
                        messages = messages_response.json()
                        print(f"   ✅ Historique accessible")
                        print(f"   📊 Messages trouvés: {len(messages)}")
                        
                        for i, message in enumerate(messages[-3:], 1):  # 3 derniers messages
                            print(f"      {i}. {message.get('sender_display')}: {message.get('message', '')[:30]}...")
                        
                        return True
                    else:
                        print(f"   ❌ Erreur historique: {messages_response.status_code}")
                        return False
                else:
                    print(f"   ⚠️ Aucune réclamation pour tester")
                    return False
            else:
                print(f"   ❌ Erreur réclamations: {claims_response.status_code}")
                return False
        else:
            print(f"   ❌ Échec authentification: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🧪 TEST DE LA CORRECTION DES PERMISSIONS DE RÉCLAMATIONS")
    print("=" * 70)
    
    # 1. Tester l'envoi de messages
    message_ok = test_claim_message_sending()
    
    # 2. Tester l'historique des messages
    history_ok = test_message_history()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Envoi de messages: {'✅' if message_ok else '❌'}")
    print(f"   Historique messages: {'✅' if history_ok else '❌'}")
    
    if message_ok and history_ok:
        print(f"\n🎉 CORRECTION RÉUSSIE!")
        print(f"   ✅ Les admins peuvent maintenant envoyer des messages")
        print(f"   ✅ L'historique des messages fonctionne")
        print(f"   ✅ Les permissions sont correctement configurées")
        
        print(f"\n🌐 TESTEZ MAINTENANT:")
        print(f"   • Page admin réclamations: http://localhost:8080/admin/claims")
        print(f"   • Cliquez sur une réclamation pour voir les détails")
        print(f"   • Envoyez un message de réponse")
        print(f"   • Vérifiez que le message apparaît comme 'Support'")
        
    elif message_ok:
        print(f"\n✅ ENVOI DE MESSAGES CORRIGÉ!")
        print(f"   L'erreur 403 est résolue pour l'envoi de messages.")
    else:
        print(f"\n❌ PROBLÈME PERSISTANT")
        print(f"   L'erreur 403 persiste. Vérifiez les permissions backend.")

if __name__ == "__main__":
    main()
