#!/usr/bin/env python3
"""
Script pour commencer la traduction de la Phase 1 (éléments critiques)
"""
import os
import sys
import json
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from translations.models import TranslationKey, Translation

def load_phase1_translations():
    """Charger les traductions de la Phase 1"""
    try:
        with open('phase_1_critical_translations.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ Fichier phase_1_critical_translations.json non trouvé")
        print("   Exécutez d'abord: python prioritize_translations.py")
        return []

def create_enhanced_translations():
    """Créer des traductions améliorées pour les éléments critiques"""
    
    # Traductions manuelles de qualité pour les éléments critiques
    critical_translations = {
        # Panier et produits
        "cart.product_added": {
            "fr": "Produit ajouté",
            "en": "Product added",
            "sw": "Bidhaa imeongezwa",
            "rn": "Igicuruzwa cyashizwe"
        },
        "cart.product_not_found": {
            "fr": "Produit non trouvé",
            "en": "Product not found",
            "sw": "Bidhaa haijapatikana",
            "rn": "Igicuruzwa ntikiboneka"
        },
        "cart.product_unavailable": {
            "fr": "Produit indisponible",
            "en": "Product unavailable",
            "sw": "Bidhaa haipatikani",
            "rn": "Igicuruzwa ntikiboneka"
        },
        "cart.out_of_stock": {
            "fr": "Ce produit n'est actuellement pas en stock",
            "en": "This product is currently out of stock",
            "sw": "Bidhaa hii haipatikani kwa sasa",
            "rn": "Iki gicuruzwa ntikiboneka ubu"
        },
        "cart.added_to_cart": {
            "fr": "Produit ajouté au panier",
            "en": "Product added to cart",
            "sw": "Bidhaa imeongezwa kwenye kikapu",
            "rn": "Igicuruzwa cyashizwe mu gikoni"
        },
        "cart.promo_applied": {
            "fr": "Code promo appliqué",
            "en": "Promo code applied",
            "sw": "Msimbo wa punguzo umetumika",
            "rn": "Kode ya kugabanya yakoreshejwe"
        },
        "cart.discount_applied": {
            "fr": "La réduction a été appliquée à votre panier",
            "en": "The discount has been applied to your cart",
            "sw": "Punguzo limewekwa kwenye kikapu chako",
            "rn": "Kugabanya kwashizwe mu gikoni cyawe"
        },
        "cart.decrease_quantity": {
            "fr": "Diminuer la quantité",
            "en": "Decrease quantity",
            "sw": "Punguza idadi",
            "rn": "Gabanya umubare"
        },
        "cart.increase_quantity": {
            "fr": "Augmenter la quantité",
            "en": "Increase quantity",
            "sw": "Ongeza idadi",
            "rn": "Yongera umubare"
        },
        
        # Navigation
        "nav.home": {
            "fr": "Accueil",
            "en": "Home",
            "sw": "Nyumbani",
            "rn": "Mu rugo"
        },
        "nav.products": {
            "fr": "Produits",
            "en": "Products",
            "sw": "Bidhaa",
            "rn": "Ibicuruzwa"
        },
        "nav.cart": {
            "fr": "Panier",
            "en": "Cart",
            "sw": "Kikapu",
            "rn": "Igikoni"
        },
        "nav.account": {
            "fr": "Mon compte",
            "en": "My account",
            "sw": "Akaunti yangu",
            "rn": "Konti yanje"
        },
        
        # Authentification
        "auth.login": {
            "fr": "Connexion",
            "en": "Login",
            "sw": "Ingia",
            "rn": "Kwinjira"
        },
        "auth.register": {
            "fr": "Inscription",
            "en": "Register",
            "sw": "Jisajili",
            "rn": "Kwiyandikisha"
        },
        "auth.logout": {
            "fr": "Déconnexion",
            "en": "Logout",
            "sw": "Toka",
            "rn": "Gusohoka"
        },
        "auth.email": {
            "fr": "Email",
            "en": "Email",
            "sw": "Barua pepe",
            "rn": "Imeyili"
        },
        "auth.password": {
            "fr": "Mot de passe",
            "en": "Password",
            "sw": "Nywila",
            "rn": "Ijambo ry'ibanga"
        },
        
        # Actions communes
        "common.save": {
            "fr": "Enregistrer",
            "en": "Save",
            "sw": "Hifadhi",
            "rn": "Bika"
        },
        "common.cancel": {
            "fr": "Annuler",
            "en": "Cancel",
            "sw": "Ghairi",
            "rn": "Kuraguza"
        },
        "common.delete": {
            "fr": "Supprimer",
            "en": "Delete",
            "sw": "Futa",
            "rn": "Gusiba"
        },
        "common.edit": {
            "fr": "Modifier",
            "en": "Edit",
            "sw": "Hariri",
            "rn": "Guhindura"
        },
        "common.search": {
            "fr": "Rechercher",
            "en": "Search",
            "sw": "Tafuta",
            "rn": "Gushakisha"
        },
        "common.loading": {
            "fr": "Chargement...",
            "en": "Loading...",
            "sw": "Inapakia...",
            "rn": "Gushakisha..."
        },
        "common.error": {
            "fr": "Erreur",
            "en": "Error",
            "sw": "Hitilafu",
            "rn": "Ikosa"
        },
        "common.success": {
            "fr": "Succès",
            "en": "Success",
            "sw": "Mafanikio",
            "rn": "Intsinzi"
        },
        
        # Produits
        "product.price": {
            "fr": "Prix",
            "en": "Price",
            "sw": "Bei",
            "rn": "Igiciro"
        },
        "product.add_to_cart": {
            "fr": "Ajouter au panier",
            "en": "Add to cart",
            "sw": "Ongeza kwenye kikapu",
            "rn": "Shira mu gikoni"
        },
        "product.quantity": {
            "fr": "Quantité",
            "en": "Quantity",
            "sw": "Idadi",
            "rn": "Umubare"
        },
        "product.in_stock": {
            "fr": "En stock",
            "en": "In stock",
            "sw": "Ipo",
            "rn": "Irahari"
        },
        "product.out_of_stock": {
            "fr": "Rupture de stock",
            "en": "Out of stock",
            "sw": "Haipatikani",
            "rn": "Ntirahari"
        },
        
        # Commande
        "order.total": {
            "fr": "Total",
            "en": "Total",
            "sw": "Jumla",
            "rn": "Byose"
        },
        "order.checkout": {
            "fr": "Commander",
            "en": "Checkout",
            "sw": "Agiza",
            "rn": "Gutumiza"
        },
        "order.payment": {
            "fr": "Paiement",
            "en": "Payment",
            "sw": "Malipo",
            "rn": "Kwishyura"
        }
    }
    
    return critical_translations

def import_critical_translations():
    """Importer les traductions critiques"""
    print("🚀 IMPORTATION DES TRADUCTIONS CRITIQUES")
    print("=" * 50)
    
    translations = create_enhanced_translations()
    imported_count = 0
    
    for key_name, translations_dict in translations.items():
        # Créer ou récupérer la clé de traduction
        translation_key, created = TranslationKey.objects.get_or_create(
            key=key_name,
            defaults={
                'category': key_name.split('.')[0],  # Première partie comme catégorie
                'description': f"Traduction critique pour: {translations_dict['fr']}",
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Nouvelle clé: {key_name}")
        
        # Ajouter les traductions pour chaque langue
        for lang_code, translation_text in translations_dict.items():
            translation_obj, created = Translation.objects.get_or_create(
                key=translation_key,
                language_code=lang_code,
                defaults={
                    'value': translation_text,
                    'is_approved': True
                }
            )
            
            if created:
                imported_count += 1
            elif translation_obj.value != translation_text:
                # Mettre à jour si différent
                translation_obj.value = translation_text
                translation_obj.save()
                print(f"🔄 Mise à jour: {key_name} ({lang_code})")
    
    print(f"\n📊 RÉSULTATS:")
    print(f"   • Nouvelles traductions importées: {imported_count}")
    print(f"   • Clés de traduction traitées: {len(translations)}")
    
    return imported_count

def show_usage_examples():
    """Afficher des exemples d'utilisation"""
    print(f"\n💡 EXEMPLES D'UTILISATION DANS LE CODE")
    print("=" * 50)
    
    examples = [
        {
            "component": "Cart.tsx",
            "before": '<button>Ajouter au panier</button>',
            "after": '<button>{t("product.add_to_cart", "Ajouter au panier")}</button>'
        },
        {
            "component": "Navigation.tsx", 
            "before": '<Link to="/">Accueil</Link>',
            "after": '<Link to="/">{t("nav.home", "Accueil")}</Link>'
        },
        {
            "component": "Login.tsx",
            "before": '<label>Email</label>',
            "after": '<label>{t("auth.email", "Email")}</label>'
        },
        {
            "component": "ProductCard.tsx",
            "before": '<span>Prix: {price}€</span>',
            "after": '<span>{t("product.price", "Prix")}: {price}€</span>'
        }
    ]
    
    for example in examples:
        print(f"\n📄 {example['component']}:")
        print(f"   ❌ Avant: {example['before']}")
        print(f"   ✅ Après: {example['after']}")

def create_replacement_guide():
    """Créer un guide de remplacement pour les développeurs"""
    print(f"\n📋 CRÉATION DU GUIDE DE REMPLACEMENT")
    print("=" * 50)
    
    guide = {
        "instructions": "Guide pour remplacer les textes en dur par des appels de traduction",
        "import_statement": 'import { useTranslation } from "@/contexts/TranslationContext";',
        "hook_usage": 'const { t } = useTranslation();',
        "replacement_patterns": [
            {
                "pattern": "Texte en dur entre guillemets",
                "example_before": '"Ajouter au panier"',
                "example_after": 't("product.add_to_cart", "Ajouter au panier")',
                "explanation": "Remplacer par t(key, fallback) où key est la clé de traduction et fallback le texte original"
            },
            {
                "pattern": "Texte dans les attributs",
                "example_before": 'placeholder="Rechercher..."',
                "example_after": 'placeholder={t("common.search", "Rechercher...")}',
                "explanation": "Utiliser des accolades pour les expressions JSX"
            },
            {
                "pattern": "Messages conditionnels",
                "example_before": 'error ? "Erreur" : "Succès"',
                "example_after": 'error ? t("common.error", "Erreur") : t("common.success", "Succès")',
                "explanation": "Appliquer la traduction à chaque branche conditionnelle"
            }
        ],
        "critical_files": [
            "src/contexts/CartContext.tsx",
            "src/components/cart/Cart.tsx", 
            "src/components/navigation/",
            "src/components/auth/",
            "src/pages/products/"
        ]
    }
    
    with open('phase1_replacement_guide.json', 'w', encoding='utf-8') as f:
        json.dump(guide, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Guide créé: phase1_replacement_guide.json")

def show_next_steps():
    """Afficher les prochaines étapes"""
    print(f"\n🎯 PROCHAINES ÉTAPES")
    print("=" * 50)
    
    print(f"1. 🔧 CONFIGURATION:")
    print(f"   • Démarrez le serveur Django: cd josnet_backend && python manage.py runserver")
    print(f"   • Accédez à l'admin: http://localhost:8000/admin/translations/")
    
    print(f"\n2. 🌐 VÉRIFICATION:")
    print(f"   • Vérifiez que les traductions sont bien importées")
    print(f"   • Testez le sélecteur de langue dans le frontend")
    
    print(f"\n3. 💻 DÉVELOPPEMENT:")
    print(f"   • Remplacez les textes en dur dans les fichiers critiques")
    print(f"   • Utilisez le guide: phase1_replacement_guide.json")
    print(f"   • Testez chaque modification")
    
    print(f"\n4. 🧪 TESTS:")
    print(f"   • Changez de langue et vérifiez les traductions")
    print(f"   • Testez sur les pages principales")
    print(f"   • Vérifiez la persistance de la langue")
    
    print(f"\n📊 PROGRESSION PHASE 1:")
    print(f"   • Traductions critiques importées: ✅")
    print(f"   • Remplacement dans le code: 🔄 En cours")
    print(f"   • Tests et validation: ⏳ À faire")

def main():
    """Fonction principale"""
    print("🚀 DÉMARRAGE DE LA PHASE 1 - TRADUCTIONS CRITIQUES")
    print("=" * 60)
    
    # Importer les traductions critiques
    imported_count = import_critical_translations()
    
    if imported_count > 0:
        print(f"\n🎉 {imported_count} traductions critiques importées avec succès!")
        
        # Afficher des exemples d'utilisation
        show_usage_examples()
        
        # Créer le guide de remplacement
        create_replacement_guide()
        
        # Afficher les prochaines étapes
        show_next_steps()
        
    else:
        print(f"\n⚠️ Aucune nouvelle traduction importée")
        print(f"   Les traductions critiques sont peut-être déjà présentes")
    
    # Statistiques finales
    total_keys = TranslationKey.objects.count()
    total_translations = Translation.objects.count()
    
    print(f"\n📊 STATISTIQUES ACTUELLES:")
    print(f"   • Total clés: {total_keys}")
    print(f"   • Total traductions: {total_translations}")
    print(f"   • Langues: 4 (fr, en, sw, rn)")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
