
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import AccountLayout from "@/components/account/AccountLayout";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Star, Heart, ShoppingCart, Loader2, AlertCircle, RefreshCw } from "lucide-react";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { accountApi } from "@/services/accountApi";

const Suggestions = () => {
  const { toast } = useToast();
  const [activeFilter, setActiveFilter] = useState("Tous");
  const [favorites, setFavorites] = useState<number[]>([]);

  // Fetch suggested products
  const {
    data: suggestedProducts = [],
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['suggestedProducts'],
    queryFn: () => accountApi.getSuggestedProducts(),
    retry: 1,
    retryDelay: 1000,
  });

  // Extract unique categories from products
  const allCategories = ["Tous", ...new Set(suggestedProducts.map(product => product.category))];

  const filteredProducts = activeFilter === "Tous"
    ? suggestedProducts
    : suggestedProducts.filter(product => product.category === activeFilter);

  const addToFavorites = (productId: number) => {
    if (favorites.includes(productId)) {
      setFavorites(favorites.filter(id => id !== productId));
      toast({
        description: "Produit retiré des favoris",
      });
    } else {
      setFavorites([...favorites, productId]);
      toast({
        description: "Produit ajouté aux favoris",
      });
    }
  };

  const addToCart = (productName: string) => {
    toast({
      title: "Produit ajouté au panier",
      description: `${productName} a été ajouté à votre panier`,
    });
  };

  const handleRefresh = () => {
    refetch();
    toast({
      title: "Rafraîchissement",
      description: "Liste des suggestions mise à jour",
    });
  };

  return (
    <AccountLayout title="Suggestions personnalisées">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-lg font-medium">Produits recommandés pour vous</h2>
            <p className="text-gray-500">Basés sur vos achats précédents et vos préférences</p>
          </div>
          <div className="flex flex-wrap gap-2 items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="flex items-center gap-1"
            >
              <RefreshCw size={14} />
              Actualiser
            </Button>
            {allCategories.map((category) => (
              <Button
                key={category}
                variant={activeFilter === category ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveFilter(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {isLoading ? (
          <Card className="p-8 text-center">
            <CardContent className="flex flex-col items-center justify-center py-6">
              <Loader2 className="h-12 w-12 text-gray-400 mb-4 animate-spin" />
              <h3 className="text-lg font-medium mb-2">Chargement des suggestions...</h3>
              <p className="text-gray-500 max-w-sm">
                Veuillez patienter pendant que nous récupérons vos suggestions personnalisées.
              </p>
            </CardContent>
          </Card>
        ) : isError ? (
          <Card className="p-8 text-center">
            <CardContent className="flex flex-col items-center justify-center py-6">
              <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
              <p className="text-gray-500 max-w-sm mb-4">
                Une erreur est survenue lors du chargement de vos suggestions. Veuillez réessayer.
              </p>
              <Button variant="outline" onClick={handleRefresh}>
                Réessayer
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <CardHeader className="p-0">
                <AspectRatio ratio={4/3}>
                  <img
                    src={product.image}
                    alt={product.name}
                    className="object-cover w-full h-full"
                  />
                </AspectRatio>
              </CardHeader>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <Link to={`/product/${product.id}`}>
                      <h3 className="font-medium hover:text-primary transition-colors">{product.name}</h3>
                    </Link>
                    <p className="text-gray-500 text-sm">{product.category}</p>
                  </div>
                  <button
                    onClick={() => addToFavorites(product.id)}
                    className="text-gray-400 hover:text-red-500 transition-colors"
                  >
                    <Heart
                      className={`h-5 w-5 ${favorites.includes(product.id) ? "fill-red-500 text-red-500" : ""}`}
                    />
                  </button>
                </div>

                <div className="mt-2 flex items-center">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                    <span className="ml-1 text-sm">{product.rating}</span>
                  </div>
                </div>

                <div className="mt-4 flex justify-between items-center">
                  <span className="font-bold text-lg">{product.price.toFixed(2)} €</span>
                  <Button
                    size="sm"
                    className="flex items-center gap-1"
                    onClick={() => addToCart(product.name)}
                  >
                    <ShoppingCart className="h-4 w-4" />
                    Ajouter
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
            {filteredProducts.length === 0 && (
              <Card className="p-8 text-center col-span-full">
                <CardContent className="flex flex-col items-center justify-center py-6">
                  <h3 className="text-lg font-medium mb-2">Aucun produit trouvé</h3>
                  <p className="text-gray-500 max-w-sm">
                    Aucun produit ne correspond à la catégorie sélectionnée.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </AccountLayout>
  );
};

export default Suggestions;
