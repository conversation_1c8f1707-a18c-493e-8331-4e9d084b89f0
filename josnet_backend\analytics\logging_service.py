"""
Service de logging pour l'application.
Permet d'enregistrer facilement des logs système depuis n'importe où dans l'application.
"""

from typing import Optional, Dict, Any
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from .models import SystemLog
import logging

User = get_user_model()

class LoggingService:
    """Service pour enregistrer des logs système."""
    
    @staticmethod
    def log(
        log_type: str,
        module: str,
        message: str,
        user: Optional[User] = None,
        user_email: Optional[str] = None,
        ip_address: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None,
        request: Optional[HttpRequest] = None
    ) -> SystemLog:
        """
        Enregistrer un log système.
        
        Args:
            log_type: Type de log ('info', 'success', 'warning', 'error')
            module: Module concerné
            message: Message du log
            user: Utilisateur concerné (optionnel)
            user_email: Email de l'utilisateur si pas d'objet User (optionnel)
            ip_address: Adresse IP (optionnel)
            additional_data: Données supplémentaires (optionnel)
            request: Objet request Django pour extraire automatiquement l'IP et l'utilisateur (optionnel)
        
        Returns:
            L'objet SystemLog créé
        """
        
        # Extraire les informations de la requête si fournie
        if request:
            if not ip_address:
                ip_address = LoggingService._get_client_ip(request)
            if not user and hasattr(request, 'user') and request.user.is_authenticated:
                user = request.user
        
        # Créer le log
        log = SystemLog.objects.create(
            type=log_type,
            module=module,
            message=message,
            user=user,
            user_email=user_email,
            ip_address=ip_address,
            additional_data=additional_data
        )
        
        # Log également dans les logs Django pour les erreurs
        if log_type == 'error':
            logging.error(f"[{module}] {message}", extra={'additional_data': additional_data})
        elif log_type == 'warning':
            logging.warning(f"[{module}] {message}", extra={'additional_data': additional_data})
        
        return log
    
    @staticmethod
    def info(module: str, message: str, **kwargs) -> SystemLog:
        """Enregistrer un log d'information."""
        return LoggingService.log('info', module, message, **kwargs)
    
    @staticmethod
    def success(module: str, message: str, **kwargs) -> SystemLog:
        """Enregistrer un log de succès."""
        return LoggingService.log('success', module, message, **kwargs)
    
    @staticmethod
    def warning(module: str, message: str, **kwargs) -> SystemLog:
        """Enregistrer un log d'avertissement."""
        return LoggingService.log('warning', module, message, **kwargs)
    
    @staticmethod
    def error(module: str, message: str, **kwargs) -> SystemLog:
        """Enregistrer un log d'erreur."""
        return LoggingService.log('error', module, message, **kwargs)
    
    @staticmethod
    def _get_client_ip(request: HttpRequest) -> str:
        """Obtenir l'adresse IP du client depuis la requête."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or '127.0.0.1'

# Fonctions de convenance pour un usage direct
def log_info(module: str, message: str, **kwargs) -> SystemLog:
    """Enregistrer un log d'information."""
    return LoggingService.info(module, message, **kwargs)

def log_success(module: str, message: str, **kwargs) -> SystemLog:
    """Enregistrer un log de succès."""
    return LoggingService.success(module, message, **kwargs)

def log_warning(module: str, message: str, **kwargs) -> SystemLog:
    """Enregistrer un log d'avertissement."""
    return LoggingService.warning(module, message, **kwargs)

def log_error(module: str, message: str, **kwargs) -> SystemLog:
    """Enregistrer un log d'erreur."""
    return LoggingService.error(module, message, **kwargs)

# Décorateur pour logger automatiquement les actions
def log_action(log_type: str = 'info', module: str = 'systeme'):
    """
    Décorateur pour logger automatiquement l'exécution d'une fonction.
    
    Usage:
        @log_action('success', 'utilisateurs')
        def create_user(request, ...):
            # code de création d'utilisateur
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                
                # Essayer de trouver la requête dans les arguments
                request = None
                for arg in args:
                    if hasattr(arg, 'META') and hasattr(arg, 'user'):
                        request = arg
                        break
                
                message = f"Fonction {func.__name__} exécutée avec succès"
                LoggingService.log(log_type, module, message, request=request)
                
                return result
            except Exception as e:
                # Logger l'erreur
                request = None
                for arg in args:
                    if hasattr(arg, 'META') and hasattr(arg, 'user'):
                        request = arg
                        break
                
                message = f"Erreur dans la fonction {func.__name__}: {str(e)}"
                LoggingService.error(module, message, request=request, additional_data={'error': str(e)})
                
                # Re-lever l'exception
                raise
        
        return wrapper
    return decorator

# Middleware pour logger automatiquement certaines actions
class LoggingMiddleware:
    """Middleware pour logger automatiquement certaines requêtes."""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        response = self.get_response(request)
        
        # Logger les erreurs 4xx et 5xx
        if response.status_code >= 400:
            log_type = 'error' if response.status_code >= 500 else 'warning'
            message = f"Réponse HTTP {response.status_code} pour {request.path}"
            
            LoggingService.log(
                log_type=log_type,
                module='serveur',
                message=message,
                request=request,
                additional_data={
                    'status_code': response.status_code,
                    'path': request.path,
                    'method': request.method
                }
            )
        
        return response
