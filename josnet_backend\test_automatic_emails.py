#!/usr/bin/env python
"""
Script de test pour le système automatique d'envoi d'emails.
Ce script teste l'envoi automatique d'emails lors de la publication de promotions et produits.

Usage:
    python test_automatic_emails.py
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from django.core import mail
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category, Promotion

User = get_user_model()


class AutomaticEmailTester:
    """Classe pour tester l'envoi automatique d'emails."""
    
    def __init__(self):
        self.test_results = []
        self.setup_test_data()
    
    def setup_test_data(self):
        """Créer les données de test."""
        print("🔧 Configuration des données de test pour emails automatiques...")
        
        # Créer un utilisateur admin
        self.admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': 'testpass123',
                'first_name': 'Admin',
                'last_name': 'AutoTest',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            self.admin_user.set_password('testpass123')
            self.admin_user.save()
        
        # Supprimer les anciens abonnements de test
        NewsletterSubscription.objects.filter(
            email__contains='autotest.com'
        ).delete()
        
        # Créer des abonnés de test avec différentes préférences
        self.subscribers = []
        
        # Abonné 1: Promotions seulement
        sub1 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=True,
            preferences_new_products=False,
            preferences_newsletters=False,
            preferences_order_updates=True
        )
        self.subscribers.append(sub1)
        
        # Abonné 2: Nouveaux produits seulement
        sub2 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=False,
            preferences_new_products=True,
            preferences_newsletters=False,
            preferences_order_updates=True
        )
        self.subscribers.append(sub2)
        
        # Abonné 3: Tout
        sub3 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=True,
            preferences_new_products=True,
            preferences_newsletters=True,
            preferences_order_updates=True
        )
        self.subscribers.append(sub3)
        
        # Abonné 4: Rien (pour tester les filtres)
        sub4 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=False,
            preferences_new_products=False,
            preferences_newsletters=False,
            preferences_order_updates=False
        )
        self.subscribers.append(sub4)
        
        # Créer une catégorie de test
        self.category, created = Category.objects.get_or_create(
            name='Auto Test Category',
            defaults={'slug': 'auto-test-category'}
        )
        
        print(f"✅ {len(self.subscribers)} abonnés de test créés")
        print("✅ Données de test configurées")
    
    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_promotion_automatic_email(self):
        """Test d'envoi automatique d'email pour les promotions."""
        print("\n🎉 Test d'envoi automatique d'email pour promotion...")
        
        try:
            # Vider la boîte email de test
            mail.outbox.clear()
            
            # Supprimer les anciennes campagnes de test
            NewsletterCampaign.objects.filter(title__contains='Auto Test').delete()
            
            # Créer une promotion en brouillon
            promotion = Promotion.objects.create(
                title='Auto Test Promotion 50%',
                description='Promotion automatique de test avec 50% de réduction sur tous les produits de la catégorie test !',
                promotion_type='percentage',
                discount_percentage=Decimal('50.00'),
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=7),
                status='draft',
                created_by=self.admin_user,
                send_email_notification=True
            )
            
            print(f"   ✅ Promotion créée en brouillon: {promotion.title}")
            
            # Vérifier qu'aucun email n'a été envoyé
            initial_email_count = len(mail.outbox)
            print(f"   ✅ Emails avant activation: {initial_email_count}")
            
            # Activer la promotion (cela devrait déclencher l'envoi automatique)
            promotion.status = 'active'
            promotion.save()
            
            print(f"   ✅ Promotion activée")
            
            # Vérifier que l'email a été marqué comme envoyé
            promotion.refresh_from_db()
            if promotion.email_sent:
                print(f"   ✅ Email marqué comme envoyé à {promotion.email_sent_at}")
            else:
                print(f"   ⚠️  Email pas encore marqué comme envoyé")
            
            # Vérifier les campagnes créées
            campaigns = NewsletterCampaign.objects.filter(
                campaign_type='promotion',
                title__contains='Auto Test'
            )
            print(f"   ✅ {campaigns.count()} campagne(s) de promotion créée(s)")
            
            if campaigns.exists():
                campaign = campaigns.first()
                print(f"   ✅ Campagne: {campaign.title}")
                print(f"   ✅ Statut: {campaign.status}")
                print(f"   ✅ Destinataires: {campaign.total_recipients}")
                print(f"   ✅ Envoyés: {campaign.sent_count}")
                
                # Vérifier que seuls les abonnés aux promotions ont reçu l'email
                expected_recipients = NewsletterSubscription.objects.filter(
                    is_active=True,
                    preferences_promotions=True
                ).count()
                
                if campaign.total_recipients == expected_recipients:
                    print(f"   ✅ Nombre de destinataires correct: {expected_recipients}")
                    self.test_results.append(("Email automatique promotion", True, f"{expected_recipients} destinataires"))
                else:
                    print(f"   ❌ Nombre de destinataires incorrect: {campaign.total_recipients} vs {expected_recipients}")
                    self.test_results.append(("Email automatique promotion", False, "Nombre destinataires incorrect"))
            else:
                print(f"   ❌ Aucune campagne de promotion créée")
                self.test_results.append(("Email automatique promotion", False, "Aucune campagne créée"))
            
            return promotion
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Email automatique promotion", False, str(e)))
            return None
    
    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_product_automatic_email(self):
        """Test d'envoi automatique d'email pour les nouveaux produits."""
        print("\n✨ Test d'envoi automatique d'email pour nouveau produit...")
        
        try:
            # Vider la boîte email de test
            mail.outbox.clear()
            
            # Supprimer les anciennes campagnes de test
            NewsletterCampaign.objects.filter(title__contains='Auto Test Product').delete()
            
            # Créer un produit en brouillon
            product = Product.objects.create(
                name='Auto Test Product Révolutionnaire',
                slug='auto-test-product-revolutionnaire',
                sku='ATP001',
                description='Un produit révolutionnaire de test pour vérifier les notifications automatiques. Ce produit va changer votre vie !',
                short_description='Produit révolutionnaire de test pour notifications automatiques',
                price=Decimal('199.99'),
                sale_price=Decimal('149.99'),
                status='draft',
                created_by=self.admin_user
            )
            product.categories.add(self.category)
            
            print(f"   ✅ Produit créé en brouillon: {product.name}")
            
            # Vérifier qu'aucun email n'a été envoyé
            initial_email_count = len(mail.outbox)
            print(f"   ✅ Emails avant publication: {initial_email_count}")
            
            # Publier le produit (cela devrait déclencher l'envoi automatique)
            product.status = 'published'
            product.save()
            
            print(f"   ✅ Produit publié")
            
            # Vérifier les campagnes créées
            campaigns = NewsletterCampaign.objects.filter(
                campaign_type='new_product',
                title__contains='Auto Test Product'
            )
            print(f"   ✅ {campaigns.count()} campagne(s) de nouveau produit créée(s)")
            
            if campaigns.exists():
                campaign = campaigns.first()
                print(f"   ✅ Campagne: {campaign.title}")
                print(f"   ✅ Statut: {campaign.status}")
                print(f"   ✅ Destinataires: {campaign.total_recipients}")
                print(f"   ✅ Envoyés: {campaign.sent_count}")
                
                # Vérifier que seuls les abonnés aux nouveaux produits ont reçu l'email
                expected_recipients = NewsletterSubscription.objects.filter(
                    is_active=True,
                    preferences_new_products=True
                ).count()
                
                if campaign.total_recipients == expected_recipients:
                    print(f"   ✅ Nombre de destinataires correct: {expected_recipients}")
                    self.test_results.append(("Email automatique nouveau produit", True, f"{expected_recipients} destinataires"))
                else:
                    print(f"   ❌ Nombre de destinataires incorrect: {campaign.total_recipients} vs {expected_recipients}")
                    self.test_results.append(("Email automatique nouveau produit", False, "Nombre destinataires incorrect"))
            else:
                print(f"   ❌ Aucune campagne de nouveau produit créée")
                self.test_results.append(("Email automatique nouveau produit", False, "Aucune campagne créée"))
            
            return product
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Email automatique nouveau produit", False, str(e)))
            return None
    
    def test_email_not_sent_for_disabled_notification(self):
        """Test que l'email n'est pas envoyé si les notifications sont désactivées."""
        print("\n🔕 Test de non-envoi d'email si notifications désactivées...")
        
        try:
            # Supprimer les anciennes campagnes de test
            NewsletterCampaign.objects.filter(title__contains='No Email Test').delete()
            
            # Créer une promotion avec notifications désactivées
            promotion = Promotion.objects.create(
                title='No Email Test Promotion',
                description='Promotion sans notification email',
                promotion_type='percentage',
                discount_percentage=Decimal('30.00'),
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=5),
                status='active',
                created_by=self.admin_user,
                send_email_notification=False  # Notifications désactivées
            )
            
            print(f"   ✅ Promotion créée avec notifications désactivées: {promotion.title}")
            
            # Vérifier qu'aucune campagne n'a été créée
            campaigns = NewsletterCampaign.objects.filter(
                campaign_type='promotion',
                title__contains='No Email Test'
            )
            
            if campaigns.count() == 0:
                print(f"   ✅ Aucune campagne créée (comportement attendu)")
                self.test_results.append(("Non-envoi si désactivé", True, "Aucune campagne créée"))
            else:
                print(f"   ❌ Campagne créée malgré notifications désactivées")
                self.test_results.append(("Non-envoi si désactivé", False, "Campagne créée à tort"))
            
            # Vérifier que l'email n'est pas marqué comme envoyé
            promotion.refresh_from_db()
            if not promotion.email_sent:
                print(f"   ✅ Email pas marqué comme envoyé (comportement attendu)")
            else:
                print(f"   ❌ Email marqué comme envoyé malgré notifications désactivées")
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Non-envoi si désactivé", False, str(e)))
    
    def test_subscriber_filtering(self):
        """Test du filtrage des abonnés selon leurs préférences."""
        print("\n🎯 Test de filtrage des abonnés...")
        
        try:
            # Compter les abonnés par préférence
            promo_subscribers = NewsletterSubscription.objects.filter(
                is_active=True,
                preferences_promotions=True
            ).count()
            
            product_subscribers = NewsletterSubscription.objects.filter(
                is_active=True,
                preferences_new_products=True
            ).count()
            
            total_subscribers = NewsletterSubscription.objects.filter(
                is_active=True
            ).count()
            
            print(f"   ✅ Total abonnés actifs: {total_subscribers}")
            print(f"   ✅ Abonnés promotions: {promo_subscribers}")
            print(f"   ✅ Abonnés nouveaux produits: {product_subscribers}")
            
            # Vérifier que le filtrage fonctionne
            if promo_subscribers == 2 and product_subscribers == 2:  # everything + promo-lover / everything + product-fan
                print(f"   ✅ Filtrage des préférences correct")
                self.test_results.append(("Filtrage abonnés", True, f"Promo: {promo_subscribers}, Produits: {product_subscribers}"))
            else:
                print(f"   ❌ Filtrage des préférences incorrect")
                self.test_results.append(("Filtrage abonnés", False, "Nombres incorrects"))
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            self.test_results.append(("Filtrage abonnés", False, str(e)))
    
    def run_all_tests(self):
        """Exécuter tous les tests."""
        print("🚀 Démarrage des tests d'emails automatiques\n")
        
        # Test du filtrage des abonnés
        self.test_subscriber_filtering()
        
        # Test d'envoi automatique pour promotion
        promotion = self.test_promotion_automatic_email()
        
        # Test d'envoi automatique pour nouveau produit
        product = self.test_product_automatic_email()
        
        # Test de non-envoi si notifications désactivées
        self.test_email_not_sent_for_disabled_notification()
        
        # Afficher le résumé
        self.print_summary()
        
        return self.test_results
    
    def print_summary(self):
        """Afficher le résumé des tests."""
        print("\n" + "="*60)
        print("📊 RÉSUMÉ DES TESTS D'EMAILS AUTOMATIQUES")
        print("="*60)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}: {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("\n" + "-"*60)
        print(f"Total: {len(self.test_results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print("\n🎉 Tous les tests d'emails automatiques sont passés avec succès !")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué. Vérifiez la configuration.")
        
        print("="*60)


def main():
    """Fonction principale."""
    tester = AutomaticEmailTester()
    results = tester.run_all_tests()
    
    # Code de sortie
    failed_tests = [r for r in results if not r[1]]
    sys.exit(len(failed_tests))


if __name__ == '__main__':
    main()
