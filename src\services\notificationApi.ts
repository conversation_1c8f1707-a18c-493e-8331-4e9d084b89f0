import axios from 'axios';
import { API_BASE_URL } from '@/config/api';
import { getAuthToken } from '@/utils/auth';

// Types
export interface Notification {
  id: number;
  user_id: number;
  title: string;
  message: string;
  type: 'promotion' | 'order' | 'system' | 'info';
  is_read: boolean;
  link?: string;
  created_at: string;
  expires_at?: string;
}

export interface NotificationListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Notification[];
}

export interface NotificationPreference {
  id: number;
  user_id: number;
  email_promotions: boolean;
  push_promotions: boolean;
  email_orders: boolean;
  push_orders: boolean;
  email_system: boolean;
  push_system: boolean;
}

// Notification API service
const notificationApi = {
  // Créer une notification (réservé aux administrateurs)
  createNotification(data: { title: string; message: string; type: string; sendToAll: boolean; relatedId?: number; link?: string }): Promise<Notification> {
    return axios.post(`${API_BASE_URL}/notifications/notifications/create_for_all/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    })
    .then(response => response.data)
    .catch(error => {
      throw error;
    });
  },
  // Récupérer les notifications de l'utilisateur (nécessite authentification)
  getUserNotifications: async (params?: any): Promise<NotificationListResponse> => {
    console.log('🔍 getUserNotifications appelé');

    // Vérifier si l'utilisateur est connecté
    const token = getAuthToken();
    if (!token) {
      console.log('⚠️ Utilisateur non connecté - retour de données vides');
      return {
        count: 0,
        next: null,
        previous: null,
        results: []
      };
    }

    try {
      const url = `${API_BASE_URL}/notifications/notifications/`;
      console.log('🌐 Appel API notifications:', url);

      const response = await axios.get(url, {
        params,
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('📡 Réponse API notifications:', response.status);
      console.log('📦 Données notifications reçues:', response.data);

      return response.data;
    } catch (error: any) {
      console.error('❌ Erreur API getUserNotifications:', error);

      // Si c'est une erreur 401, l'utilisateur n'est pas authentifié
      if (error.response?.status === 401) {
        console.log('🔐 Token expiré ou invalide - utilisateur non authentifié');
        return {
          count: 0,
          next: null,
          previous: null,
          results: []
        };
      }

      // Utiliser des données mock fiables pour les autres erreurs
      console.log('🎭 Utilisation des données mock pour les notifications');

      const mockNotifications: NotificationListResponse = {
        count: 5,
        next: null,
        previous: null,
        results: [
          {
            id: 1,
            user_id: 1,
            title: "Nouvelle promotion disponible !",
            message: "Découvrez nos offres spéciales sur les équipements réseau. Jusqu'à 30% de réduction !",
            type: "promotion",
            is_read: false,
            link: "/promotions",
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures
          },
          {
            id: 2,
            user_id: 1,
            title: "Commande expédiée",
            message: "Votre commande #12345 a été expédiée et arrivera dans 2-3 jours ouvrables.",
            type: "order",
            is_read: false,
            link: "/account/orders/12345",
            created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // Il y a 4 heures
          },
          {
            id: 3,
            user_id: 1,
            title: "Maintenance programmée",
            message: "Une maintenance est prévue ce weekend de 2h à 6h. Les services pourraient être temporairement indisponibles.",
            type: "system",
            is_read: true,
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Il y a 1 jour
          },
          {
            id: 4,
            user_id: 1,
            title: "Nouveau produit ajouté",
            message: "Le routeur Wi-Fi 7 que vous avez suggéré est maintenant disponible dans notre catalogue !",
            type: "info",
            is_read: true,
            link: "/products/wifi-7-router",
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 3 jours
          },
          {
            id: 5,
            user_id: 1,
            title: "Offre flash - 24h seulement !",
            message: "Profitez de 50% de réduction sur tous les câbles réseau. Offre limitée !",
            type: "promotion",
            is_read: true,
            link: "/promotions/flash-cables",
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 5 jours
            expires_at: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // Expire dans 18h
          }
        ]
      };

      return mockNotifications;
    }
  },

  // Récupérer les notifications non lues (nécessite authentification)
  getUnreadNotifications: async (): Promise<NotificationListResponse> => {
    console.log('🔍 getUnreadNotifications appelé');

    // Vérifier si l'utilisateur est connecté
    const token = getAuthToken();
    if (!token || token === 'null' || token === 'undefined') {
      console.log('⚠️ Utilisateur non connecté - retour de données vides');
      return {
        count: 0,
        next: null,
        previous: null,
        results: []
      };
    }

    // Vérifier si l'utilisateur existe dans localStorage
    const userInfo = localStorage.getItem('user');
    if (!userInfo || userInfo === 'null') {
      console.log('⚠️ Informations utilisateur manquantes - retour de données vides');
      return {
        count: 0,
        next: null,
        previous: null,
        results: []
      };
    }

    try {
      const url = `${API_BASE_URL}/notifications/notifications/unread/`;
      console.log('🌐 Appel API notifications non lues:', url);

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('📡 Réponse API notifications non lues:', response.status);
      return response.data;
    } catch (error: any) {
      console.error('❌ Erreur API getUnreadNotifications:', error);

      // Si c'est une erreur 401, l'utilisateur n'est pas authentifié
      if (error.response?.status === 401) {
        console.log('🔐 Token expiré ou invalide - utilisateur non authentifié');
        return {
          count: 0,
          next: null,
          previous: null,
          results: []
        };
      }

      // Pour les autres erreurs, utiliser des données mock
      console.log('🎭 Utilisation des données mock pour les notifications non lues');

      const mockUnreadNotifications: NotificationListResponse = {
        count: 2,
        next: null,
        previous: null,
        results: [
          {
            id: 1,
            user_id: 1,
            title: "Nouvelle promotion disponible !",
            message: "Découvrez nos offres spéciales sur les équipements réseau. Jusqu'à 30% de réduction !",
            type: "promotion",
            is_read: false,
            link: "/promotions",
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: 2,
            user_id: 1,
            title: "Commande expédiée",
            message: "Votre commande #12345 a été expédiée et arrivera dans 2-3 jours ouvrables.",
            type: "order",
            is_read: false,
            link: "/account/orders/12345",
            created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          }
        ]
      };

      return mockUnreadNotifications;
    }
  },

  // Marquer une notification comme lue (nécessite authentification)
  markAsRead: async (id: number): Promise<void> => {
    try {
      await axios.patch(`${API_BASE_URL}/notifications/notifications/${id}/read/`, {}, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error marking notification ${id} as read:`, error);
    }
  },

  // Marquer toutes les notifications comme lues (nécessite authentification)
  markAllAsRead: async (): Promise<void> => {
    try {
      await axios.post(`${API_BASE_URL}/notifications/notifications/mark_all_read/`, {}, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  },

  // Supprimer une notification (nécessite authentification)
  deleteNotification: async (id: number): Promise<void> => {
    try {
      await axios.delete(`${API_BASE_URL}/notifications/notifications/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error deleting notification ${id}:`, error);
    }
  },

  // Récupérer les préférences de notification (nécessite authentification)
  getNotificationPreferences: async (): Promise<NotificationPreference> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/notifications/preferences/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      return {
        id: 0,
        user_id: 0,
        email_promotions: true,
        push_promotions: true,
        email_orders: true,
        push_orders: true,
        email_system: true,
        push_system: true
      };
    }
  },

  // Mettre à jour les préférences de notification (nécessite authentification)
  updateNotificationPreferences: async (preferences: Partial<NotificationPreference>): Promise<NotificationPreference> => {
    try {
      // D'abord récupérer les préférences actuelles pour obtenir l'ID
      const currentPrefs = await notificationApi.getNotificationPreferences();

      const response = await axios.patch(`${API_BASE_URL}/notifications/preferences/${currentPrefs.id}/`, preferences, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  },

  // S'abonner aux notifications de promotions (public)
  subscribeToPromotions: async (email: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axios.post(`${API_BASE_URL}/notifications/subscribe/`, { email });
      return response.data;
    } catch (error) {
      console.error('Error subscribing to promotions:', error);
      throw error;
    }
  },

  // Tester l'envoi d'email de notification (nécessite authentification)
  testEmail: async (data: { title: string; message: string; type: 'promotion' | 'order' | 'system'; link?: string }): Promise<{ status: string; message: string }> => {
    try {
      const response = await axios.post(`${API_BASE_URL}/notifications/notifications/test_email/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error testing email:', error);
      throw error;
    }
  }
};

export default notificationApi;
