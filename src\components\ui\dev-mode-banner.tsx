import React from 'react';
import { Alert<PERSON>riangle, Wifi, WifiOff } from 'lucide-react';

interface DevModeBannerProps {
  isVisible?: boolean;
  onDismiss?: () => void;
}

const DevModeBanner: React.FC<DevModeBannerProps> = ({ 
  isVisible = true, 
  onDismiss 
}) => {
  const [isDismissed, setIsDismissed] = React.useState(false);

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  if (!isVisible || isDismissed) {
    return null;
  }

  return (
    <div className="bg-amber-50 border-l-4 border-amber-400 p-4 mb-4 rounded-r-lg">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-amber-400" />
        </div>
        <div className="ml-3 flex-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-amber-800">
                Mode Développement Actif
              </h3>
              <div className="mt-2 text-sm text-amber-700">
                <p className="mb-2">
                  <WifiOff className="inline h-4 w-4 mr-1" />
                  Le serveur backend n'est pas disponible. L'application utilise des données simulées.
                </p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>L'inscription et la connexion fonctionnent en mode simulation</li>
                  <li>Les données sont stockées localement dans votre navigateur</li>
                  <li>Aucune donnée réelle n'est envoyée au serveur</li>
                  <li>Toutes les fonctionnalités restent testables</li>
                </ul>
              </div>
            </div>
            <button
              onClick={handleDismiss}
              className="ml-4 text-amber-400 hover:text-amber-600 transition-colors"
              aria-label="Fermer la notification"
            >
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DevModeBanner;
