"""
Vues API pour les données dynamiques du site.
"""

from rest_framework import viewsets, status
from rest_framework.decorators import api_view, action, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta

from .models import (
    SiteSettings, HomePageStats, Service, Testimonial,
    NewsletterSubscriber, ContactMessage, FAQ, Banner,
    BlogCategory, BlogPost, BlogComment, NewsletterSubscription, NewsletterCampaign
)
from .serializers import (
    SiteSettingsSerializer, HomePageStatsSerializer, ServiceSerializer,
    TestimonialSerializer, NewsletterSubscriberSerializer, ContactMessageSerializer,
    FAQSerializer, BannerSerializer, BlogCategorySerializer, BlogPostListSerializer,
    BlogPostDetailSerializer, BlogCommentSerializer, BlogSearchSerializer,
    NewsletterSubscriptionSerializer, NewsletterSubscribeSerializer,
    NewsletterUnsubscribeSerializer, NewsletterPreferencesSerializer,
    NewsletterCampaignSerializer, NewsletterSendSerializer
)
from products.models import Product
from orders.models import Order


class SiteSettingsViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les paramètres du site."""
    queryset = SiteSettings.objects.filter(is_active=True)
    serializer_class = SiteSettingsSerializer
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Récupérer les paramètres actuels du site."""
        settings = SiteSettings.objects.filter(is_active=True).first()
        if settings:
            serializer = self.get_serializer(settings)
            return Response(serializer.data)
        return Response({
            'site_name': 'JOSNET NETWORK',
            'site_description': 'Votre partenaire IT & Télécom de confiance au Burundi'
        })


class HomePageStatsViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les statistiques de la page d'accueil."""
    queryset = HomePageStats.objects.filter(is_active=True)
    serializer_class = HomePageStatsSerializer
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Récupérer les statistiques actuelles."""
        stats = HomePageStats.objects.filter(is_active=True).first()
        if not stats:
            # Créer des statistiques par défaut si aucune n'existe
            stats = HomePageStats.objects.create()

        serializer = self.get_serializer(stats)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def live(self, request):
        """Récupérer les statistiques en temps réel depuis la base de données."""
        try:
            # Compter les vraies données depuis la base de données
            total_orders = Order.objects.count()
            total_products = Product.objects.filter(status='published').count()

            # Compter les clients uniques (utilisateurs qui ont passé des commandes)
            total_clients = Order.objects.values('user').distinct().count()

            # Ajouter les clients sans commandes (tous les utilisateurs actifs)
            from django.contrib.auth import get_user_model
            User = get_user_model()
            total_users = User.objects.filter(is_active=True).count()
            total_clients = max(total_clients, total_users)

            # Statistiques des 30 derniers jours
            thirty_days_ago = timezone.now() - timedelta(days=30)
            recent_orders = Order.objects.filter(created_at__gte=thirty_days_ago).count()

            # Calculer les produits vendus (somme des quantités dans les commandes)
            from django.db.models import Sum
            products_sold = Order.objects.aggregate(
                total_sold=Sum('items__quantity')
            )['total_sold'] or 0

            # Calculer le taux de satisfaction basé sur les commandes livrées vs annulées
            delivered_orders = Order.objects.filter(status='delivered').count()
            cancelled_orders = Order.objects.filter(status='cancelled').count()
            total_completed = delivered_orders + cancelled_orders

            if total_completed > 0:
                satisfaction_rate = round((delivered_orders / total_completed) * 100)
            else:
                satisfaction_rate = 100  # Par défaut si pas de données

            # Calculer les années d'expérience (depuis 2014)
            experience_years = datetime.now().year - 2014

            # Compter les villes desservies (basé sur les adresses de livraison)
            cities_served = Order.objects.values('shipping_address__city').distinct().count()
            if cities_served == 0:
                cities_served = 18  # Valeur par défaut

            return Response({
                'clients_count': total_clients,
                'projects_count': total_orders,
                'experience_years': experience_years,
                'team_members': 25,  # Valeur fixe pour l'équipe
                'products_sold': products_sold,
                'cities_served': cities_served,
                'support_hours': '24/7',
                'satisfaction_rate': satisfaction_rate,
                'recent_orders': recent_orders,
                'total_products': total_products,
                'last_updated': timezone.now(),
                'is_live_data': True
            })
        except Exception as e:
            return Response({
                'error': f'Erreur lors du calcul des statistiques: {str(e)}',
                'is_live_data': False
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ServiceViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les services."""
    queryset = Service.objects.filter(is_active=True)
    serializer_class = ServiceSerializer
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Récupérer les services mis en avant."""
        services = Service.objects.filter(is_active=True, featured=True).order_by('order')
        serializer = self.get_serializer(services, many=True)
        return Response(serializer.data)


class TestimonialViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les témoignages."""
    queryset = Testimonial.objects.filter(is_active=True)
    serializer_class = TestimonialSerializer
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Récupérer les témoignages mis en avant."""
        testimonials = Testimonial.objects.filter(is_active=True, featured=True).order_by('order')
        serializer = self.get_serializer(testimonials, many=True)
        return Response(serializer.data)


class NewsletterSubscriberViewSet(viewsets.ModelViewSet):
    """API pour les abonnements newsletter."""
    queryset = NewsletterSubscriber.objects.all()
    serializer_class = NewsletterSubscriberSerializer
    permission_classes = [AllowAny]
    http_method_names = ['post']  # Seulement POST pour s'abonner

    def create(self, request, *args, **kwargs):
        """S'abonner à la newsletter."""
        email = request.data.get('email')
        name = request.data.get('name', '')

        if not email:
            return Response(
                {'error': 'Email requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Vérifier si l'email existe déjà
        subscriber, created = NewsletterSubscriber.objects.get_or_create(
            email=email,
            defaults={'name': name, 'is_active': True}
        )

        if created:
            return Response(
                {'message': 'Inscription réussie à la newsletter'},
                status=status.HTTP_201_CREATED
            )
        elif not subscriber.is_active:
            # Réactiver l'abonnement
            subscriber.is_active = True
            subscriber.save()
            return Response(
                {'message': 'Abonnement réactivé'},
                status=status.HTTP_200_OK
            )
        else:
            return Response(
                {'message': 'Déjà abonné à la newsletter'},
                status=status.HTTP_200_OK
            )


class ContactMessageViewSet(viewsets.ModelViewSet):
    """API pour les messages de contact."""
    queryset = ContactMessage.objects.all().order_by('-created_at')
    serializer_class = ContactMessageSerializer

    def get_permissions(self):
        """
        Permissions différentes selon l'action:
        - POST: Tout le monde peut envoyer un message
        - GET, PATCH, PUT, DELETE: Seulement les admins
        """
        if self.action == 'create':
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """Filtrer les messages selon les paramètres de requête."""
        queryset = ContactMessage.objects.all().order_by('-created_at')

        # Filtrer par statut
        status = self.request.query_params.get('status', None)
        if status:
            queryset = queryset.filter(status=status)

        # Filtrer par service demandé
        service = self.request.query_params.get('service_requested', None)
        if service:
            queryset = queryset.filter(service_requested=service)

        # Recherche textuelle
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(email__icontains=search) |
                Q(subject__icontains=search) |
                Q(message__icontains=search)
            )

        return queryset

    def create(self, request, *args, **kwargs):
        """Envoyer un message de contact."""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            contact_message = serializer.save()

            # Envoyer une notification email à l'admin
            try:
                self.send_admin_notification(contact_message)
            except Exception as e:
                # Log l'erreur mais ne pas faire échouer la création du message
                print(f"Erreur lors de l'envoi de la notification admin: {e}")

            # Envoyer un email de confirmation au client
            try:
                self.send_client_confirmation(contact_message)
            except Exception as e:
                print(f"Erreur lors de l'envoi de la confirmation client: {e}")

            return Response(
                {'message': 'Message envoyé avec succès. Nous vous répondrons dans les plus brefs délais.'},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    def test_data(self, request):
        """Endpoint de test pour vérifier les données sans authentification."""
        messages = ContactMessage.objects.all().order_by('-created_at')[:5]

        data = []
        for msg in messages:
            data.append({
                'id': msg.id,
                'name': msg.name,
                'email': msg.email,
                'subject': msg.subject,
                'status': msg.status,
                'created_at': msg.created_at.isoformat(),
                'service_requested': msg.service_requested
            })

        return Response({
            'count': ContactMessage.objects.count(),
            'recent_messages': data,
            'status_counts': {
                'new': ContactMessage.objects.filter(status='new').count(),
                'in_progress': ContactMessage.objects.filter(status='in_progress').count(),
                'replied': ContactMessage.objects.filter(status='replied').count(),
                'closed': ContactMessage.objects.filter(status='closed').count(),
            }
        })

    def send_admin_notification(self, contact_message):
        """Envoyer une notification email à l'admin."""
        from django.core.mail import send_mail
        from django.conf import settings
        from .models import SiteSettings

        # Récupérer les paramètres du site
        site_settings = SiteSettings.objects.filter(is_active=True).first()
        admin_email = site_settings.email_primary if site_settings else settings.DEFAULT_FROM_EMAIL

        subject = f"[JOSNET] Nouveau message de contact - {contact_message.subject}"

        message = f"""
Nouveau message de contact reçu sur JOSNET NETWORK

Détails du contact:
- Nom: {contact_message.name}
- Email: {contact_message.email}
- Téléphone: {contact_message.phone or 'Non renseigné'}
- Entreprise: {contact_message.company or 'Non renseignée'}
- Service demandé: {contact_message.get_service_requested_display() if contact_message.service_requested else 'Non spécifié'}

Sujet: {contact_message.subject}

Message:
{contact_message.message}

---
Reçu le: {contact_message.created_at.strftime('%d/%m/%Y à %H:%M')}
ID du message: {contact_message.id}

Pour répondre à ce message, connectez-vous à l'administration:
{settings.SITE_URL}/admin/core/contactmessage/{contact_message.id}/change/
        """

        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[admin_email],
            fail_silently=False,
        )

    def send_client_confirmation(self, contact_message):
        """Envoyer un email de confirmation au client."""
        from django.core.mail import send_mail
        from django.conf import settings
        from .models import SiteSettings

        # Récupérer les paramètres du site
        site_settings = SiteSettings.objects.filter(is_active=True).first()
        company_name = site_settings.site_name if site_settings else "JOSNET NETWORK"

        subject = f"Confirmation de réception - {contact_message.subject}"

        message = f"""
Bonjour {contact_message.name},

Nous avons bien reçu votre message concernant "{contact_message.subject}".

Notre équipe va examiner votre demande et vous répondra dans les plus brefs délais, généralement sous 24 heures ouvrables.

Récapitulatif de votre message:
- Sujet: {contact_message.subject}
- Service demandé: {contact_message.get_service_requested_display() if contact_message.service_requested else 'Non spécifié'}
- Date d'envoi: {contact_message.created_at.strftime('%d/%m/%Y à %H:%M')}

Si vous avez des questions urgentes, n'hésitez pas à nous contacter directement:
- Téléphone: {site_settings.phone_primary if site_settings else '+257 XX XX XX XX'}
- Email: {site_settings.email_primary if site_settings else '<EMAIL>'}

Merci de votre confiance.

Cordialement,
L'équipe {company_name}
        """

        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[contact_message.email],
            fail_silently=False,
        )


class FAQViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les FAQ."""
    queryset = FAQ.objects.filter(is_active=True)
    serializer_class = FAQSerializer
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Récupérer les FAQ mises en avant."""
        faqs = FAQ.objects.filter(is_active=True, featured=True).order_by('order')
        serializer = self.get_serializer(faqs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Récupérer les FAQ par catégorie."""
        category = request.query_params.get('category')
        if category:
            faqs = FAQ.objects.filter(is_active=True, category=category).order_by('order')
        else:
            faqs = FAQ.objects.filter(is_active=True).order_by('category', 'order')

        serializer = self.get_serializer(faqs, many=True)
        return Response(serializer.data)


class BannerViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les bannières."""
    queryset = Banner.objects.filter(is_active=True)
    serializer_class = BannerSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """Filtrer les bannières visibles."""
        now = timezone.now()
        return Banner.objects.filter(
            is_active=True,
            start_date__lte=now
        ).filter(
            Q(end_date__isnull=True) | Q(end_date__gte=now)
        ).order_by('order')

    @action(detail=False, methods=['get'])
    def by_position(self, request):
        """Récupérer les bannières par position."""
        position = request.query_params.get('position', 'hero')
        banners = self.get_queryset().filter(position=position)
        serializer = self.get_serializer(banners, many=True)
        return Response(serializer.data)


@api_view(['GET'])
def homepage_data(request):
    """
    API consolidée pour récupérer toutes les données de la page d'accueil.
    """
    try:
        # Paramètres du site
        site_settings = SiteSettings.objects.filter(is_active=True).first()

        # Statistiques
        stats = HomePageStats.objects.filter(is_active=True).first()
        if not stats:
            stats = HomePageStats.objects.create()

        # Services mis en avant
        featured_services = Service.objects.filter(is_active=True, featured=True).order_by('order')[:4]

        # Témoignages mis en avant
        featured_testimonials = Testimonial.objects.filter(is_active=True, featured=True).order_by('order')[:3]

        # Bannières hero
        hero_banners = Banner.objects.filter(
            is_active=True,
            position='hero',
            start_date__lte=timezone.now()
        ).filter(
            Q(end_date__isnull=True) | Q(end_date__gte=timezone.now())
        ).order_by('order')[:1]

        # Statistiques en temps réel
        total_orders = Order.objects.count()
        total_products = Product.objects.filter(status='published').count()
        total_clients = Order.objects.values('user').distinct().count()

        response_data = {
            'site_settings': SiteSettingsSerializer(site_settings).data if site_settings else None,
            'stats': {
                'clients_count': max(total_clients, stats.clients_count),
                'projects_count': max(total_orders, stats.projects_count),
                'experience_years': stats.experience_years,
                'team_members': stats.team_members,
                'products_sold': max(total_products * 100, stats.products_sold),
                'cities_served': stats.cities_served,
                'support_hours': stats.support_hours,
                'satisfaction_rate': stats.satisfaction_rate,
            },
            'featured_services': ServiceSerializer(featured_services, many=True).data,
            'featured_testimonials': TestimonialSerializer(featured_testimonials, many=True).data,
            'hero_banners': BannerSerializer(hero_banners, many=True).data,
            'last_updated': timezone.now()
        }

        return Response(response_data)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la récupération des données: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Vues pour le blog
class BlogCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les catégories de blog."""
    queryset = BlogCategory.objects.filter(is_active=True)
    serializer_class = BlogCategorySerializer
    permission_classes = [AllowAny]
    lookup_field = 'slug'


class BlogPostViewSet(viewsets.ReadOnlyModelViewSet):
    """API pour les articles de blog."""
    queryset = BlogPost.objects.filter(status='published')
    permission_classes = [AllowAny]
    lookup_field = 'slug'

    def get_serializer_class(self):
        """Retourner le bon serializer selon l'action."""
        if self.action == 'retrieve':
            return BlogPostDetailSerializer
        return BlogPostListSerializer

    def get_queryset(self):
        """Filtrer les articles selon les paramètres de recherche."""
        queryset = BlogPost.objects.filter(status='published').select_related('category')

        # Filtrage par catégorie
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)

        # Filtrage par tags
        tags = self.request.query_params.get('tags')
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',')]
            for tag in tag_list:
                queryset = queryset.filter(tags__icontains=tag)

        # Filtrage par auteur
        author = self.request.query_params.get('author')
        if author:
            queryset = queryset.filter(author_name__icontains=author)

        # Recherche dans le titre et le contenu
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(excerpt__icontains=search) |
                Q(content__icontains=search) |
                Q(tags__icontains=search)
            )

        # Articles mis en avant seulement
        featured_only = self.request.query_params.get('featured_only')
        if featured_only and featured_only.lower() == 'true':
            queryset = queryset.filter(featured=True)

        return queryset.order_by('-published_at', '-created_at')

    def retrieve(self, request, *args, **kwargs):
        """Récupérer un article et incrémenter les vues."""
        instance = self.get_object()
        instance.increment_views()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Récupérer les articles mis en avant."""
        posts = self.get_queryset().filter(featured=True)[:3]
        serializer = BlogPostListSerializer(posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def latest(self, request):
        """Récupérer les derniers articles."""
        posts = self.get_queryset()[:6]
        serializer = BlogPostListSerializer(posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Récupérer les articles populaires (plus de vues)."""
        posts = self.get_queryset().order_by('-views_count')[:6]
        serializer = BlogPostListSerializer(posts, many=True, context={'request': request})
        return Response(serializer.data)


class BlogCommentViewSet(viewsets.ModelViewSet):
    """API pour les commentaires de blog."""
    serializer_class = BlogCommentSerializer
    permission_classes = [AllowAny]
    http_method_names = ['get', 'post']

    def get_queryset(self):
        """Retourner les commentaires approuvés pour un article."""
        post_slug = self.kwargs.get('post_slug')
        if post_slug:
            return BlogComment.objects.filter(
                post__slug=post_slug,
                status='approved'
            ).order_by('-created_at')
        return BlogComment.objects.filter(status='approved')

    def create(self, request, *args, **kwargs):
        """Créer un nouveau commentaire."""
        post_slug = kwargs.get('post_slug')
        try:
            post = BlogPost.objects.get(slug=post_slug, status='published')
        except BlogPost.DoesNotExist:
            return Response(
                {'error': 'Article non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Ajouter l'IP de l'utilisateur
            ip_address = request.META.get('REMOTE_ADDR')
            serializer.save(post=post, ip_address=ip_address)
            return Response(
                {'message': 'Commentaire soumis pour modération'},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def blog_search(request):
    """
    API de recherche avancée pour le blog.
    """
    try:
        # Valider les paramètres de recherche
        search_serializer = BlogSearchSerializer(data=request.query_params)
        if not search_serializer.is_valid():
            return Response(search_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        search_data = search_serializer.validated_data

        # Construire la requête
        queryset = BlogPost.objects.filter(status='published').select_related('category')

        # Appliquer les filtres
        if search_data.get('query'):
            query = search_data['query']
            queryset = queryset.filter(
                Q(title__icontains=query) |
                Q(excerpt__icontains=query) |
                Q(content__icontains=query) |
                Q(tags__icontains=query)
            )

        if search_data.get('category'):
            queryset = queryset.filter(category__slug=search_data['category'])

        if search_data.get('author'):
            queryset = queryset.filter(author_name__icontains=search_data['author'])

        if search_data.get('tags'):
            tag_list = [tag.strip() for tag in search_data['tags'].split(',')]
            for tag in tag_list:
                queryset = queryset.filter(tags__icontains=tag)

        if search_data.get('date_from'):
            queryset = queryset.filter(published_at__gte=search_data['date_from'])

        if search_data.get('date_to'):
            queryset = queryset.filter(published_at__lte=search_data['date_to'])

        if search_data.get('featured_only'):
            queryset = queryset.filter(featured=True)

        # Pagination
        from django.core.paginator import Paginator
        page = search_data.get('page', 1)
        page_size = search_data.get('page_size', 12)

        paginator = Paginator(queryset.order_by('-published_at'), page_size)
        page_obj = paginator.get_page(page)

        # Sérialiser les résultats
        serializer = BlogPostListSerializer(page_obj, many=True, context={'request': request})

        return Response({
            'results': serializer.data,
            'pagination': {
                'page': page,
                'pages': paginator.num_pages,
                'per_page': page_size,
                'total': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }
        })

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la recherche: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# ===== NEWSLETTER API VIEWS =====

class NewsletterSubscriptionViewSet(viewsets.ModelViewSet):
    """API pour la gestion des abonnements newsletter."""
    serializer_class = NewsletterSubscriptionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Retourner l'abonnement de l'utilisateur connecté."""
        if self.request.user.is_authenticated:
            return NewsletterSubscription.objects.filter(
                user=self.request.user
            )
        return NewsletterSubscription.objects.none()

    def get_object(self):
        """Récupérer ou créer l'abonnement de l'utilisateur."""
        subscription, created = NewsletterSubscription.objects.get_or_create(
            user=self.request.user,
            defaults={
                'email': self.request.user.email,
                'source': 'account_page'
            }
        )
        return subscription

    @action(detail=False, methods=['get'])
    def subscription(self, request):
        """Récupérer l'abonnement de l'utilisateur connecté."""
        subscription = self.get_object()
        serializer = self.get_serializer(subscription)
        return Response(serializer.data)

    @action(detail=False, methods=['patch'])
    def preferences(self, request):
        """Mettre à jour les préférences d'abonnement."""
        subscription = self.get_object()
        serializer = NewsletterPreferencesSerializer(data=request.data)

        if serializer.is_valid():
            # Mettre à jour les préférences
            for key, value in serializer.validated_data.items():
                setattr(subscription, f'preferences_{key}', value)
            subscription.save()

            # Retourner l'abonnement mis à jour
            response_serializer = self.get_serializer(subscription)
            return Response(response_serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def newsletter_subscribe(request):
    """S'abonner à la newsletter."""
    serializer = NewsletterSubscribeSerializer(data=request.data)

    if serializer.is_valid():
        email = serializer.validated_data['email']
        preferences = serializer.validated_data.get('preferences', {})
        source = serializer.validated_data.get('source', 'website')

        # Vérifier si l'email existe déjà
        existing = NewsletterSubscription.objects.filter(email=email).first()

        if existing:
            if existing.is_active:
                return Response(
                    {'message': 'Cet email est déjà abonné à la newsletter.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            else:
                # Réactiver l'abonnement existant
                existing.reactivate()
                subscription = existing
        else:
            # Créer un nouvel abonnement
            user = None
            if request.user.is_authenticated:
                user = request.user

            subscription = NewsletterSubscription.objects.create(
                user=user,
                email=email,
                source=source,
                preferences_promotions=preferences.get('promotions', True),
                preferences_new_products=preferences.get('new_products', True),
                preferences_newsletters=preferences.get('newsletters', True),
                preferences_order_updates=preferences.get('order_updates', True),
                ip_address=request.META.get('REMOTE_ADDR')
            )

        response_serializer = NewsletterSubscriptionSerializer(subscription)
        return Response({
            'message': 'Abonnement à la newsletter réussi !',
            'subscription': response_serializer.data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def newsletter_unsubscribe(request):
    """Se désabonner de la newsletter."""
    serializer = NewsletterUnsubscribeSerializer(data=request.data)

    if serializer.is_valid():
        subscription = serializer.validated_data['subscription']
        subscription.unsubscribe()

        return Response({
            'message': 'Désabonnement de la newsletter réussi.'
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def newsletter_reactivate(request):
    """Réactiver un abonnement newsletter."""
    email = request.data.get('email')

    if not email:
        return Response(
            {'error': 'Email requis.'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        subscription = NewsletterSubscription.objects.get(email=email)
        subscription.reactivate()

        return Response({
            'message': 'Abonnement réactivé avec succès !'
        })
    except NewsletterSubscription.DoesNotExist:
        return Response(
            {'error': 'Aucun abonnement trouvé pour cet email.'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def newsletter_status(request):
    """Vérifier le statut d'abonnement d'un email."""
    email = request.query_params.get('email')

    if not email:
        return Response(
            {'error': 'Email requis.'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        subscription = NewsletterSubscription.objects.get(email=email)
        return Response({
            'is_subscribed': subscription.is_active,
            'preferences': subscription.preferences_dict if subscription.is_active else None
        })
    except NewsletterSubscription.DoesNotExist:
        return Response({
            'is_subscribed': False
        })


# ===== PROMOTION MANAGEMENT API =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def newsletter_stats(request):
    """Obtenir les statistiques d'abonnement (admin seulement)."""
    if not request.user.is_staff:
        return Response(
            {'error': 'Permission refusée.'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        from django.db.models import Count
        from datetime import datetime, timedelta

        # Statistiques générales
        total_subscribers = NewsletterSubscription.objects.count()
        active_subscribers = NewsletterSubscription.objects.filter(is_active=True).count()

        # Abonnements récents (7 derniers jours)
        week_ago = timezone.now() - timedelta(days=7)
        recent_subscriptions = NewsletterSubscription.objects.filter(
            subscribed_at__gte=week_ago
        ).count()

        # Taux de désabonnement
        total_unsubscribed = NewsletterSubscription.objects.filter(is_active=False).count()
        unsubscribe_rate = (total_unsubscribed / total_subscribers * 100) if total_subscribers > 0 else 0

        # Répartition par préférences
        preferences_stats = {
            'promotions': NewsletterSubscription.objects.filter(
                is_active=True, preferences_promotions=True
            ).count(),
            'new_products': NewsletterSubscription.objects.filter(
                is_active=True, preferences_new_products=True
            ).count(),
            'newsletters': NewsletterSubscription.objects.filter(
                is_active=True, preferences_newsletters=True
            ).count(),
            'order_updates': NewsletterSubscription.objects.filter(
                is_active=True, preferences_order_updates=True
            ).count(),
        }

        return Response({
            'total_subscribers': total_subscribers,
            'active_subscribers': active_subscribers,
            'recent_subscriptions': recent_subscriptions,
            'unsubscribe_rate': round(unsubscribe_rate, 2),
            'preferences_stats': preferences_stats
        })

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la récupération des statistiques: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_newsletter(request):
    """Envoyer une newsletter à tous les abonnés (admin seulement)."""
    if not request.user.is_staff:
        return Response(
            {'error': 'Permission refusée.'},
            status=status.HTTP_403_FORBIDDEN
        )

    serializer = NewsletterSendSerializer(data=request.data)

    if serializer.is_valid():
        try:
            from core.services.newsletter_service import NewsletterService

            # Créer la campagne
            campaign = NewsletterService.create_campaign(
                title=f"Newsletter: {serializer.validated_data['subject']}",
                subject=serializer.validated_data['subject'],
                content=serializer.validated_data['content'],
                campaign_type=serializer.validated_data['campaign_type'],
                target_preferences=serializer.validated_data.get('target_preferences'),
                target_all=serializer.validated_data.get('target_all', False),
                created_by=request.user
            )

            # Envoyer la campagne
            result = NewsletterService.send_campaign(campaign)

            if result.get('success'):
                return Response({
                    'message': 'Newsletter envoyée avec succès !',
                    'sent_count': result.get('sent_count'),
                    'campaign_id': campaign.id
                })
            else:
                return Response(
                    {'error': f'Erreur lors de l\'envoi: {result.get("error")}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        except Exception as e:
            return Response(
                {'error': f'Erreur lors de l\'envoi de la newsletter: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
