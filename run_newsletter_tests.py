#!/usr/bin/env python
"""
Script principal pour exécuter tous les tests du système de newsletter.
Ce script lance tous les tests : modèles, API, services et emails automatiques.

Usage:
    python run_newsletter_tests.py [--verbose] [--test-type=all|unit|integration|api]
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, description, cwd=None):
    """Exécuter une commande et retourner le résultat."""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=cwd
        )
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - SUCCÈS")
            return True
        else:
            print(f"❌ {description} - ÉCHEC (code: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution de '{command}': {str(e)}")
        return False

def run_django_tests():
    """Exécuter les tests Django."""
    backend_dir = Path(__file__).parent / "josnet_backend"
    
    tests = [
        ("python manage.py test core.tests.test_newsletter_subscriptions", "Tests d'abonnements newsletter"),
        ("python manage.py test products.tests.test_automatic_notifications", "Tests de notifications automatiques"),
    ]
    
    results = []
    for command, description in tests:
        success = run_command(command, description, cwd=backend_dir)
        results.append((description, success))
    
    return results

def run_integration_tests():
    """Exécuter les tests d'intégration."""
    backend_dir = Path(__file__).parent / "josnet_backend"
    
    tests = [
        ("python test_newsletter_system.py", "Tests d'intégration du système newsletter"),
        ("python test_automatic_emails.py", "Tests d'emails automatiques"),
    ]
    
    results = []
    for command, description in tests:
        success = run_command(command, description, cwd=backend_dir)
        results.append((description, success))
    
    return results

def run_api_tests():
    """Exécuter les tests API."""
    # Vérifier si Node.js est disponible pour les tests frontend
    try:
        subprocess.run(["node", "--version"], capture_output=True, check=True)
        node_available = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        node_available = False
    
    results = []
    
    if node_available:
        # Installer les dépendances si nécessaire
        if not Path("node_modules").exists():
            print("📦 Installation des dépendances Node.js...")
            install_success = run_command(
                "npm install node-fetch",
                "Installation de node-fetch"
            )
            if not install_success:
                print("⚠️  Impossible d'installer node-fetch, test API ignoré")
                return [("Tests API Frontend", False)]
        
        success = run_command(
            "node test_newsletter_frontend.js",
            "Tests API Frontend"
        )
        results.append(("Tests API Frontend", success))
    else:
        print("⚠️  Node.js non disponible, tests API frontend ignorés")
        results.append(("Tests API Frontend", False))
    
    return results

def check_prerequisites():
    """Vérifier les prérequis."""
    print("🔍 Vérification des prérequis...")
    
    # Vérifier Python
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ requis")
        return False
    
    # Vérifier Django
    try:
        import django
        print(f"✅ Django {django.get_version()} détecté")
    except ImportError:
        print("❌ Django non installé")
        return False
    
    # Vérifier la structure du projet
    backend_dir = Path(__file__).parent / "josnet_backend"
    if not backend_dir.exists():
        print("❌ Répertoire josnet_backend non trouvé")
        return False
    
    manage_py = backend_dir / "manage.py"
    if not manage_py.exists():
        print("❌ manage.py non trouvé")
        return False
    
    print("✅ Prérequis vérifiés")
    return True

def setup_test_environment():
    """Configurer l'environnement de test."""
    print("🔧 Configuration de l'environnement de test...")
    
    backend_dir = Path(__file__).parent / "josnet_backend"
    
    # Migrer la base de données de test
    migrate_success = run_command(
        "python manage.py migrate --run-syncdb",
        "Migration de la base de données",
        cwd=backend_dir
    )
    
    if not migrate_success:
        print("❌ Échec de la migration")
        return False
    
    print("✅ Environnement de test configuré")
    return True

def print_final_summary(all_results):
    """Afficher le résumé final de tous les tests."""
    print("\n" + "="*80)
    print("📊 RÉSUMÉ FINAL DE TOUS LES TESTS")
    print("="*80)
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for category, results in all_results.items():
        print(f"\n📁 {category}:")
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"   {status} {test_name}")
            total_tests += 1
            if success:
                passed_tests += 1
            else:
                failed_tests += 1
    
    print("\n" + "-"*80)
    print(f"📈 STATISTIQUES GLOBALES:")
    print(f"   Total des tests: {total_tests}")
    print(f"   Réussis: {passed_tests}")
    print(f"   Échoués: {failed_tests}")
    print(f"   Taux de réussite: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "   Taux de réussite: 0%")
    
    if failed_tests == 0:
        print("\n🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS !")
        print("   Le système de newsletter fonctionne parfaitement.")
    else:
        print(f"\n⚠️  {failed_tests} TEST(S) ONT ÉCHOUÉ")
        print("   Vérifiez la configuration et les logs ci-dessus.")
    
    print("="*80)
    
    return failed_tests == 0

def main():
    """Fonction principale."""
    parser = argparse.ArgumentParser(description="Exécuter les tests du système de newsletter")
    parser.add_argument(
        "--test-type",
        choices=["all", "unit", "integration", "api"],
        default="all",
        help="Type de tests à exécuter"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Affichage verbeux"
    )
    
    args = parser.parse_args()
    
    print("🚀 LANCEMENT DES TESTS DU SYSTÈME DE NEWSLETTER")
    print("="*80)
    
    # Vérifier les prérequis
    if not check_prerequisites():
        sys.exit(1)
    
    # Configurer l'environnement de test
    if not setup_test_environment():
        sys.exit(1)
    
    # Exécuter les tests selon le type demandé
    all_results = {}
    
    if args.test_type in ["all", "unit"]:
        print("\n🧪 EXÉCUTION DES TESTS UNITAIRES")
        all_results["Tests Unitaires"] = run_django_tests()
    
    if args.test_type in ["all", "integration"]:
        print("\n🔗 EXÉCUTION DES TESTS D'INTÉGRATION")
        all_results["Tests d'Intégration"] = run_integration_tests()
    
    if args.test_type in ["all", "api"]:
        print("\n🌐 EXÉCUTION DES TESTS API")
        all_results["Tests API"] = run_api_tests()
    
    # Afficher le résumé final
    success = print_final_summary(all_results)
    
    # Code de sortie
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
