import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { promotionApi, Promotion } from '@/services/promotionApi';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatPrice } from '@/lib/utils';
import { AlertCircle, Tag, Percent, CreditCard } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface ActivePromotionsProps {
  maxToShow?: number;
  showHeader?: boolean;
}

const ActivePromotions: React.FC<ActivePromotionsProps> = ({ 
  maxToShow = 3,
  showHeader = true
}) => {
  // Récupérer les promotions actives sans authentification
  const { data, isLoading, error } = useQuery({
    queryKey: ['active-promotions'],
    queryFn: () => promotionApi.getActivePromotions({ is_active: true, page_size: maxToShow }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Afficher un état de chargement
  if (isLoading) {
    return (
      <div className="space-y-3">
        {showHeader && <Skeleton className="h-8 w-64 mb-4" />}
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  // Afficher un message d'erreur
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Erreur</AlertTitle>
        <AlertDescription>
          Impossible de charger les promotions en cours.
        </AlertDescription>
      </Alert>
    );
  }

  // Si aucune promotion active
  if (!data?.results?.length) {
    return null; // Ne rien afficher
  }

  // Fonction pour obtenir l'icône selon le type de promotion
  const getPromotionIcon = (promotion: Promotion) => {
    switch (promotion.applies_to) {
      case 'product':
        return <Tag className="h-5 w-5 mr-2" />;
      case 'category':
        return <Tag className="h-5 w-5 mr-2" />;
      case 'cart':
        return <CreditCard className="h-5 w-5 mr-2" />;
      default:
        return <Percent className="h-5 w-5 mr-2" />;
    }
  };

  // Fonction pour formater la description de la promotion
  const getPromotionDescription = (promotion: Promotion) => {
    let description = '';
    
    if (promotion.discount_type === 'percentage') {
      description = `${promotion.discount_value}% de réduction`;
    } else {
      description = `${formatPrice(promotion.discount_value)} de réduction`;
    }
    
    if (promotion.applies_to === 'product' && promotion.target_name) {
      description += ` sur ${promotion.target_name}`;
    } else if (promotion.applies_to === 'category' && promotion.target_name) {
      description += ` sur la catégorie ${promotion.target_name}`;
    }
    
    if (promotion.min_purchase_amount && promotion.min_purchase_amount > 0) {
      description += ` pour tout achat supérieur à ${formatPrice(promotion.min_purchase_amount)}`;
    }
    
    return description;
  };

  return (
    <div className="space-y-4">
      {showHeader && (
        <h2 className="text-2xl font-bold tracking-tight">Offres en cours</h2>
      )}
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {data.results.map((promotion) => (
          <Card key={promotion.id} className="overflow-hidden">
            <CardHeader className="pb-2 bg-muted/50">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{promotion.name}</CardTitle>
                {promotion.discount_code && (
                  <Badge variant="outline" className="font-mono">
                    {promotion.discount_code}
                  </Badge>
                )}
              </div>
              <CardDescription>
                {promotion.end_date && (
                  <span className="text-sm text-muted-foreground">
                    Valable jusqu'au {new Date(promotion.end_date).toLocaleDateString()}
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="flex items-center text-md">
                {getPromotionIcon(promotion)}
                <span>{getPromotionDescription(promotion)}</span>
              </div>
              {promotion.description && (
                <p className="mt-2 text-sm text-muted-foreground">
                  {promotion.description}
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ActivePromotions;
