
import { useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  MessageSquare,
  Users,
  ShoppingCart,
  Package,
  HelpCircle,
  ChevronRight
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

const Contact = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      toast({
        title: "Message envoyé !",
        description: "Nous vous répondrons dans les plus brefs délais.",
      });
      
      // Reset form
      setName("");
      setEmail("");
      setSubject("");
      setMessage("");
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero section */}
        <section className="bg-primary-dark text-white py-12">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Contactez-nous</h1>
            <p className="text-xl max-w-2xl mx-auto">
              Notre équipe est là pour vous aider. N'hésitez pas à nous contacter
              pour toute question ou besoin d'assistance.
            </p>
          </div>
        </section>
        
        {/* Contact info and form */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Contact information */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                  <h2 className="text-xl font-medium mb-6">Coordonnées</h2>
                  
                  <div className="space-y-4">
                    <div className="flex">
                      <MapPin className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="font-medium">Adresse</h3>
                        <p className="text-gray-600">
                          123 Avenue Principale<br />
                          Bujumbura, Burundi
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <Phone className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="font-medium">Téléphone</h3>
                        <p className="text-gray-600">+257 XX XX XX XX</p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <Mail className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="font-medium">Email</h3>
                        <p className="text-gray-600"><EMAIL></p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <Clock className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="font-medium">Heures d'ouverture</h3>
                        <p className="text-gray-600">
                          Lundi - Vendredi: 8h00 - 18h00<br />
                          Samedi: 9h00 - 13h00<br />
                          Dimanche: Fermé
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Quick links */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-medium mb-6">Liens rapides</h2>
                  
                  <ul className="space-y-2">
                    <li>
                      <a href="/faq" className="flex items-center justify-between text-gray-700 hover:text-primary p-2 rounded-md hover:bg-gray-50">
                        <span className="flex items-center">
                          <HelpCircle className="h-4 w-4 mr-3" />
                          FAQ
                        </span>
                        <ChevronRight className="h-4 w-4" />
                      </a>
                    </li>
                    <li>
                      <a href="/order-tracking" className="flex items-center justify-between text-gray-700 hover:text-primary p-2 rounded-md hover:bg-gray-50">
                        <span className="flex items-center">
                          <Package className="h-4 w-4 mr-3" />
                          Suivi de commande
                        </span>
                        <ChevronRight className="h-4 w-4" />
                      </a>
                    </li>
                    <li>
                      <a href="/products" className="flex items-center justify-between text-gray-700 hover:text-primary p-2 rounded-md hover:bg-gray-50">
                        <span className="flex items-center">
                          <ShoppingCart className="h-4 w-4 mr-3" />
                          Produits
                        </span>
                        <ChevronRight className="h-4 w-4" />
                      </a>
                    </li>
                    <li>
                      <a href="/about" className="flex items-center justify-between text-gray-700 hover:text-primary p-2 rounded-md hover:bg-gray-50">
                        <span className="flex items-center">
                          <Users className="h-4 w-4 mr-3" />
                          À propos de nous
                        </span>
                        <ChevronRight className="h-4 w-4" />
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
              
              {/* Contact form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 h-full">
                  <div className="flex items-center mb-6">
                    <MessageSquare className="h-5 w-5 text-primary mr-2" />
                    <h2 className="text-xl font-medium">Envoyez-nous un message</h2>
                  </div>
                  
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium mb-1">
                          Nom complet*
                        </label>
                        <Input
                          id="name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Jean Dupont"
                          required
                        />
                      </div>
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium mb-1">
                          Email*
                        </label>
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium mb-1">
                        Sujet*
                      </label>
                      <Input
                        id="subject"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        placeholder="Comment pouvons-nous vous aider ?"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium mb-1">
                        Message*
                      </label>
                      <textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder="Écrivez votre message ici..."
                        className="w-full min-h-[200px] border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      ></textarea>
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="privacy"
                        className="h-4 w-4 mr-2"
                        required
                      />
                      <label htmlFor="privacy" className="text-sm">
                        J'accepte la <a href="/privacy" className="text-primary hover:underline">politique de confidentialité</a>.
                      </label>
                    </div>
                    
                    <Button 
                      type="submit" 
                      className="w-full md:w-auto" 
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Envoi en cours..." : "Envoyer le message"}
                    </Button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Map section */}
        <section className="bg-gray-50 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold mb-8 text-center">Où nous trouver</h2>
            
            {/* Placeholder for map - in a real app, this would be an interactive map */}
            <div className="aspect-video max-w-4xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <p className="text-gray-600">Carte interactive - Emplacement de JOSNET NETWORK</p>
                {/* In a real app, you'd embed a map here */}
              </div>
            </div>
          </div>
        </section>
        
        {/* FAQ section */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl font-bold mb-2">Des questions fréquentes ?</h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Consultez notre section FAQ pour trouver des réponses rapides à vos questions.
            </p>
            <Button asChild>
              <a href="/faq">Voir la FAQ</a>
            </Button>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Contact;
