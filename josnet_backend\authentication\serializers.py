from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models_activation import AccountActivation
from .validators import validate_gmail_email, is_gmail_email
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _
from .models import UserVerification, PasswordReset, UserAddress, LoyaltyTransaction
from django.utils import timezone
from datetime import timedelta
import uuid

User = get_user_model()

class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer specifically for user registration."""

    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name', 'password', 'confirm_password']
        extra_kwargs = {
            'first_name': {'required': True},
            'last_name': {'required': True},
            'email': {'required': True}
        }

    def validate(self, data):
        """Validate that passwords match."""
        if data['password'] != data['confirm_password']:
            raise serializers.ValidationError({"confirm_password": _("Passwords don't match.")})
        return data

    def create(self, validated_data):
        """Create and return a new user."""
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password')

        user = User.objects.create(**validated_data)
        user.set_password(password)
        user.save()

        # Create verification token
        token = str(uuid.uuid4())
        expires_at = timezone.now() + timedelta(days=1)
        UserVerification.objects.create(user=user, token=token, expires_at=expires_at)

        return user


    def validate_email(self, value):
        """Valide que l'email est un Gmail valide."""
        return validate_gmail_email(value)

class UserSerializer(serializers.ModelSerializer):
    """Serializer for the User model."""

    password = serializers.CharField(write_only=True, required=False)
    confirm_password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'password', 'confirm_password',
            'role', 'phone_number', 'date_of_birth', 'profile_picture',
            'address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country',
            'is_active', 'is_verified', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_active', 'is_verified', 'created_at', 'updated_at']
        extra_kwargs = {
            'password': {'write_only': True}
        }

    def validate(self, data):
        """Validate that passwords match when creating a new user."""
        if 'password' in data and 'confirm_password' in data:
            if data['password'] != data['confirm_password']:
                raise serializers.ValidationError({"confirm_password": _("Passwords don't match.")})
        return data

    def create(self, validated_data):
        """Create and return a new user."""
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password', None)

        user = User.objects.create(**validated_data)

        if password:
            user.set_password(password)
            user.save()

        # Create verification token
        token = str(uuid.uuid4())
        expires_at = timezone.now() + timedelta(days=1)
        UserVerification.objects.create(user=user, token=token, expires_at=expires_at)

        return user

    def update(self, instance, validated_data):
        """Update and return an existing user."""
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()
        return instance

class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile data."""

    full_name = serializers.CharField(read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'phone_number', 'date_of_birth', 'profile_picture',
            'loyalty_points', 'loyalty_tier', 'is_verified'
        ]
        read_only_fields = ['id', 'email', 'loyalty_points', 'loyalty_tier', 'is_verified']

class LoginSerializer(serializers.Serializer):
    """Serializer for user login."""

    email = serializers.EmailField()
    password = serializers.CharField(style={'input_type': 'password'})

    def validate(self, data):
        """Validate and authenticate the user."""
        email = data.get('email')
        password = data.get('password')

        if email and password:
            user = authenticate(request=self.context.get('request'), username=email, password=password)

            if not user:
                msg = _('Unable to log in with provided credentials.')
                raise serializers.ValidationError(msg, code='authorization')

            if not user.is_active:
                raise serializers.ValidationError(_('User account is disabled.'), code='authorization')

            data['user'] = user
            return data
        else:
            msg = _('Must include "email" and "password".')
            raise serializers.ValidationError(msg, code='authorization')

class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change."""

    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)

    def validate(self, data):
        """Validate that new passwords match and old password is correct."""
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError({"confirm_password": _("New passwords don't match.")})
        return data

class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for password reset request."""

    email = serializers.EmailField(required=True)

class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation."""

    token = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)

    def validate(self, data):
        """Validate that new passwords match and token is valid."""
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError({"confirm_password": _("Passwords don't match.")})

        # Check if token exists and is valid
        try:
            from django.utils import timezone
            reset = PasswordReset.objects.get(token=data['token'], is_used=False)
            if timezone.now() > reset.expires_at:
                raise serializers.ValidationError({"token": _("Reset token has expired.")})

            # Store the reset object in validated data for use in the view
            data['reset_object'] = reset

        except PasswordReset.DoesNotExist:
            raise serializers.ValidationError({"token": _("Invalid reset token.")})

        return data

class EmailVerificationSerializer(serializers.Serializer):
    """Serializer for email verification."""

    token = serializers.CharField(required=True)

class UserAddressSerializer(serializers.ModelSerializer):
    """Serializer for user addresses."""

    full_address = serializers.CharField(read_only=True)

    class Meta:
        model = UserAddress
        fields = [
            'id', 'user', 'address_type', 'is_default',
            'first_name', 'last_name', 'company', 'phone',
            'address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country',
            'full_address', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']

    def create(self, validated_data):
        """Create a new address for the current user."""
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)

class UserAddressListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing user addresses."""

    full_address = serializers.CharField(read_only=True)

    class Meta:
        model = UserAddress
        fields = [
            'id', 'address_type', 'is_default', 'first_name', 'last_name',
            'city', 'country', 'full_address'
        ]

class LoyaltyTransactionSerializer(serializers.ModelSerializer):
    """Serializer for loyalty transactions."""

    is_expired = serializers.BooleanField(read_only=True)
    user_email = serializers.EmailField(source='user.email', read_only=True)
    order_number = serializers.CharField(source='order.order_number', read_only=True)

    class Meta:
        model = LoyaltyTransaction
        fields = [
            'id', 'user', 'user_email', 'points', 'transaction_type',
            'description', 'order', 'order_number', 'created_at',
            'expires_at', 'is_expired'
        ]
        read_only_fields = ['id', 'user', 'user_email', 'created_at', 'is_expired']

class UserLoyaltySerializer(serializers.ModelSerializer):
    """Serializer for user loyalty information."""

    class Meta:
        model = User
        fields = ['id', 'email', 'loyalty_points', 'loyalty_tier']
        read_only_fields = ['id', 'email', 'loyalty_points', 'loyalty_tier']

class ProfilePictureUploadSerializer(serializers.Serializer):
    """Serializer for uploading a profile picture."""
    profile_picture = serializers.ImageField()

class UserProfileExtendedSerializer(UserProfileSerializer):
    """Extended serializer for user profile with addresses."""

    addresses = UserAddressListSerializer(source='addresses', many=True, read_only=True)

    class Meta(UserProfileSerializer.Meta):
        fields = UserProfileSerializer.Meta.fields + ['addresses']

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models_activation import AccountActivation

User = get_user_model()

class AccountActivationSerializer(serializers.Serializer):
    """
    Serializer pour l'activation de compte
    """
    token = serializers.UUIDField(required=True)
    
    def validate_token(self, value):
        """Valider le token d'activation"""
        try:
            activation = AccountActivation.objects.get(
                activation_token=value,
                is_activated=False
            )
            
            if activation.is_expired:
                raise serializers.ValidationError(
                    "Le lien d'activation a expiré. Demandez un nouveau lien."
                )
            
            return value
            
        except AccountActivation.DoesNotExist:
            raise serializers.ValidationError(
                "Token d'activation invalide ou déjà utilisé."
            )

class ResendActivationSerializer(serializers.Serializer):
    """
    Serializer pour renvoyer un email d'activation
    """
    email = serializers.EmailField(required=True)
    
    def validate_email(self, value):
        """Valider l'email pour le renvoi d'activation"""
        try:
            user = User.objects.get(email=value)
            
            if user.is_active:
                raise serializers.ValidationError(
                    "Ce compte est déjà activé."
                )
            
            return value
            
        except User.DoesNotExist:
            raise serializers.ValidationError(
                "Aucun compte trouvé avec cette adresse email."
            )
