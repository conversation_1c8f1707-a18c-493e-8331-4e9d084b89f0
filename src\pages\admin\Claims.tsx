import React, { useState, useEffect } from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON>, CardContent, Chip, Button, TextField, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip, CircularProgress, Tabs, Tab, Avatar, Divider } from '@mui/material';
import { Grid } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import AdminLayout from '../../components/admin/AdminLayout';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SendIcon from '@mui/icons-material/Send';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import PersonIcon from '@mui/icons-material/Person';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import ComputerIcon from '@mui/icons-material/Computer';

// Fonction utilitaire pour formater les dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
};

// Fonction utilitaire pour formater l'heure
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
};

// Types
interface ClaimMessage {
  id: string;
  sender: 'customer' | 'support' | 'system';
  message: string;
  is_read: boolean;
  created_at: string;
  attachments: {
    id: string;
    file_name: string;
    file_size: number;
    content_type: string;
    file_url: string;
  }[];
}

interface ClaimStatusHistory {
  id: string;
  old_status: string;
  new_status: string;
  comment: string | null;
  created_by: {
    id: string;
    email: string;
    full_name: string;
  } | null;
  created_at: string;
}

interface Claim {
  id: string;
  claim_number: string;
  user: {
    id: string;
    email: string;
    full_name: string;
  };
  order: {
    id: string;
    order_number: string;
  } | null;
  subject: string;
  description: string;
  status: 'new' | 'in_progress' | 'waiting_customer' | 'waiting_staff' | 'resolved' | 'closed';
  created_at: string;
  updated_at: string;
  last_update: string;
  messages: ClaimMessage[];
  status_history: ClaimStatusHistory[];
}

const statusColors = {
  new: '#FFA000',                // Amber
  in_progress: '#2196F3',         // Blue
  waiting_customer: '#FF5722',    // Deep Orange
  waiting_staff: '#9C27B0',       // Purple
  resolved: '#4CAF50',            // Green
  closed: '#9E9E9E'               // Grey
};

const statusLabels = {
  new: 'Nouvelle réclamation',
  in_progress: 'Traitement en cours',
  waiting_customer: 'Attente réponse client',
  waiting_staff: 'Attente réponse support',
  resolved: 'Résolue',
  closed: 'Clôturée'
};

const subjectLabels = {
  order_issue: 'Problème de commande',
  product_issue: 'Problème de produit',
  shipping_issue: 'Problème de livraison',
  account_issue: 'Problème de compte',
  website_issue: 'Problème technique',
  payment_issue: 'Problème de paiement',
  other: 'Autre sujet'
};

const AdminClaims: React.FC = () => {
  const theme = useTheme();
  const [claims, setClaims] = useState<Claim[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [subjectFilter, setSubjectFilter] = useState<string>('all');
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [updateStatusOpen, setUpdateStatusOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [adminNote, setAdminNote] = useState('');
  const [replyMessage, setReplyMessage] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [userDataCache, setUserDataCache] = useState<{[key: string]: any}>({});

  // Fonction pour enrichir les données avec les informations utilisateur
  const enrichClaimsWithUserData = async (claimsArray: any[]) => {
    const enrichedClaims = [];

    for (const claim of claimsArray) {
      let enrichedClaim = { ...claim };

      // Si les données utilisateur sont manquantes ou incomplètes
      if (!claim.user || !claim.user.full_name) {
        try {
          // Vérifier le cache d'abord
          const userId = claim.user?.id;
          if (userId && userDataCache[userId]) {
            enrichedClaim.user = { ...claim.user, ...userDataCache[userId] };
          } else if (userId) {
            // Récupérer les données utilisateur depuis l'API
            const userResponse = await fetch(`http://localhost:8000/api/v1/auth/users/${userId}/`, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
                'Content-Type': 'application/json'
              }
            });

            if (userResponse.ok) {
              const userData = await userResponse.json();
              // Mettre en cache
              setUserDataCache(prev => ({ ...prev, [userId]: userData }));
              enrichedClaim.user = { ...claim.user, ...userData };
              console.log(`✅ Données utilisateur récupérées pour ${userData.email}`);
            }
          }
        } catch (err) {
          console.warn('Erreur lors de la récupération des données utilisateur:', err);
        }
      }

      enrichedClaims.push(enrichedClaim);
    }

    return enrichedClaims;
  };

  // Fonctions de gestion d'événements
  const handleViewDetails = async (claim: Claim) => {
    try {
      setDetailsLoading(true);
      setDetailsOpen(true);

      // Récupérer les détails complets de la réclamation depuis l'API
      const response = await fetch(`http://localhost:8000/api/v1/orders/claims/${claim.id}/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      const detailedClaim = await response.json();
      console.log('Detailed claim data:', detailedClaim);

      setSelectedClaim(detailedClaim);
    } catch (err) {
      console.error('Error fetching claim details:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors de la récupération des détails');
      // En cas d'erreur, utiliser les données de base disponibles
      setSelectedClaim(claim);
    } finally {
      setDetailsLoading(false);
    }
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  const handleUpdateStatus = (claim: Claim) => {
    setSelectedClaim(claim);
    setNewStatus(claim.status);
    setUpdateStatusOpen(true);
  };

  const submitStatusUpdate = async () => {
    if (!selectedClaim || !newStatus) return;

    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8000/api/v1/orders/claims/${selectedClaim.id}/update_status/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus,
          comment: adminNote
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      // Update local state
      const updatedClaim = await response.json();
      setClaims(prevClaims => 
        prevClaims.map(item => 
          item.id === updatedClaim.id ? updatedClaim : item
        )
      );

      setUpdateStatusOpen(false);
      setAdminNote('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      console.error('Error updating claim status:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSendReply = async () => {
    if (!selectedClaim || !replyMessage.trim()) return;
    
    try {
      setLoading(true);
      
      const formData = new FormData();
      formData.append('sender', 'support');
      formData.append('message', replyMessage);
      
      // Add files if any
      selectedFiles.forEach(file => {
        formData.append('uploaded_files', file);
      });

      const response = await fetch(`http://localhost:8000/api/v1/orders/claims/${selectedClaim.id}/add_message/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      // Update local state with the updated claim
      const updatedClaim = await response.json();
      setClaims(prevClaims => 
        prevClaims.map(item => 
          item.id === updatedClaim.id ? updatedClaim : item
        )
      );
      
      // If the selected claim is open in the details view, update it
      if (selectedClaim && selectedClaim.id === updatedClaim.id) {
        setSelectedClaim(updatedClaim);
      }

      // Reset form
      setReplyMessage('');
      setSelectedFiles([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      console.error('Error sending reply:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const filesArray = Array.from(event.target.files);
      setSelectedFiles(prevFiles => [...prevFiles, ...filesArray]);
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  const getSenderIcon = (sender: string) => {
    switch (sender) {
      case 'customer':
        return <PersonIcon />;
      case 'support':
        return <SupportAgentIcon />;
      case 'system':
        return <ComputerIcon />;
      default:
        return null;
    }
  };

  const getSenderName = (sender: string) => {
    switch (sender) {
      case 'customer':
        return 'Client';
      case 'support':
        return 'Support';
      case 'system':
        return 'Système';
      default:
        return sender;
    }
  };
  // Fetch claims data
  useEffect(() => {
    const fetchClaims = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:8000/api/v1/orders/claims/', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Erreur ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Claims API response:', data);

        // S'assurer que data est un tableau
        const claimsArray = Array.isArray(data) ? data : (data.results ? data.results : []);
        console.log('Claims array:', claimsArray);

        // Enrichir avec les données utilisateur si nécessaires
        const enrichedClaims = await enrichClaimsWithUserData(claimsArray);

        setClaims(enrichedClaims);
        setFilteredClaims(enrichedClaims);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
        console.error('Error fetching claims:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchClaims();
  }, []);

  // Filter claims based on search term, status filter and subject filter
  useEffect(() => {
    // S'assurer que claims est un tableau avant de l'utiliser
    if (!Array.isArray(claims)) {
      console.warn('Claims is not an array:', claims);
      setFilteredClaims([]);
      return;
    }

    let result = [...claims];
    
    if (statusFilter !== 'all') {
      result = result.filter(item => item.status === statusFilter);
    }
    
    if (subjectFilter !== 'all') {
      result = result.filter(item => item.subject === subjectFilter);
    }
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(item =>
        item.claim_number.toLowerCase().includes(term) ||
        (item.user?.email?.toLowerCase().includes(term)) ||
        (item.user?.full_name?.toLowerCase().includes(term)) ||
        (item.order?.order_number?.toLowerCase().includes(term) || false)
      );
    }

    setFilteredClaims(result);
  }, [claims, searchTerm, statusFilter, subjectFilter]);

  return (
    <AdminLayout>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5">Gestion des réclamations</Typography>
        </Box>
        
        <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="status-filter-label">Statut</InputLabel>
            <Select
              labelId="status-filter-label"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              label="Statut"
            >
              <MenuItem value="all">Tous les statuts</MenuItem>
              <MenuItem value="new">Nouveau</MenuItem>
              <MenuItem value="in_progress">En cours</MenuItem>
              <MenuItem value="waiting_customer">En attente du client</MenuItem>
              <MenuItem value="waiting_staff">En attente du support</MenuItem>
              <MenuItem value="resolved">Résolu</MenuItem>
              <MenuItem value="closed">Fermé</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="subject-filter-label">Sujet</InputLabel>
            <Select
              labelId="subject-filter-label"
              value={subjectFilter}
              onChange={(e) => setSubjectFilter(e.target.value)}
              label="Sujet"
            >
              <MenuItem value="all">Tous les sujets</MenuItem>
              <MenuItem value="order_issue">Problème avec une commande</MenuItem>
              <MenuItem value="product_issue">Problème avec un produit</MenuItem>
              <MenuItem value="shipping_issue">Problème de livraison</MenuItem>
              <MenuItem value="account_issue">Problème de compte</MenuItem>
              <MenuItem value="website_issue">Problème avec le site web</MenuItem>
              <MenuItem value="other">Autre</MenuItem>
            </Select>
          </FormControl>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <TextField
              size="small"
              variant="outlined"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Box>
        </Box>
        {/* Error message */}
        {error && (
          <Box sx={{ mb: 2 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}

        {/* Claims Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ mb: 4 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Numéro</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Sujet</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Date de création</TableCell>
                  <TableCell>Dernière mise à jour</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredClaims.length > 0 ? (
                  filteredClaims.map((claim) => (
                    <TableRow key={claim.id}>
                      <TableCell>{claim.claim_number}</TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {claim.user?.full_name || claim.user?.email || 'Utilisateur inconnu'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {claim.user?.email || 'Email non disponible'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {subjectLabels[claim.subject as keyof typeof subjectLabels] || claim.subject}
                        {claim.order && (
                          <Typography variant="caption" display="block" color="text.secondary">
                            Commande: {claim.order.order_number}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={statusLabels[claim.status as keyof typeof statusLabels]}
                          sx={{ 
                            backgroundColor: statusColors[claim.status as keyof typeof statusColors],
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatDate(claim.created_at)}</TableCell>
                      <TableCell>
                        {formatDate(claim.last_update)}
                        <Typography variant="caption" display="block" color="text.secondary">
                          {formatTime(claim.last_update)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Voir les détails">
                          <IconButton size="small" onClick={() => handleViewDetails(claim)}>
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      Aucune réclamation trouvée
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
        {/* Claim Details Dialog */}
        <Dialog
          open={detailsOpen}
          onClose={handleCloseDetails}
          maxWidth="md"
          fullWidth
        >
          {detailsLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>Chargement des détails...</Typography>
            </Box>
          ) : selectedClaim && (
            <>
              <DialogTitle>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h6">Réclamation {selectedClaim.claim_number}</Typography>
                  <Chip 
                    label={statusLabels[selectedClaim.status as keyof typeof statusLabels]}
                    sx={{ 
                      backgroundColor: statusColors[selectedClaim.status as keyof typeof statusColors],
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>
              </DialogTitle>
              <DialogContent>
                <Box sx={{ mb: 3 }}>
                  <Grid container spacing={3}>
                    {/* Informations Client */}
                    <Grid xs={12} md={6}>
                      <Card variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                        <Typography variant="h6" gutterBottom>Informations Client</Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Nom:</Typography>
                            <Typography variant="body2">
                              {selectedClaim.user?.full_name || selectedClaim.user?.email || 'Utilisateur inconnu'}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Email:</Typography>
                            <Typography variant="body2">
                              {selectedClaim.user?.email || 'Email non disponible'}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">ID Client:</Typography>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {selectedClaim.user?.id || 'N/A'}
                            </Typography>
                          </Box>
                        </Box>
                      </Card>
                    </Grid>

                    {/* Informations Réclamation */}
                    <Grid xs={12} md={6}>
                      <Card variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                        <Typography variant="h6" gutterBottom>Détails de la Réclamation</Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">ID Réclamation:</Typography>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {selectedClaim.id}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Créée le:</Typography>
                            <Typography variant="body2">
                              {formatDate(selectedClaim.created_at)} à {formatTime(selectedClaim.created_at)}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Dernière mise à jour:</Typography>
                            <Typography variant="body2">
                              {formatDate(selectedClaim.updated_at)} à {formatTime(selectedClaim.updated_at)}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">Statut actuel:</Typography>
                            <Chip
                              label={statusLabels[selectedClaim.status as keyof typeof statusLabels]}
                              size="small"
                              sx={{
                                backgroundColor: statusColors[selectedClaim.status as keyof typeof statusColors],
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </Box>
                        </Box>
                      </Card>
                    </Grid>

                    {/* Commande Associée */}
                    {selectedClaim.order && (
                      <Grid xs={12} md={6}>
                        <Card variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                          <Typography variant="h6" gutterBottom>Commande Associée</Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Typography variant="body2" color="text.secondary">Numéro de commande:</Typography>
                              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                {selectedClaim.order.order_number}
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Typography variant="body2" color="text.secondary">ID Commande:</Typography>
                              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                {selectedClaim.order.id}
                              </Typography>
                            </Box>
                          </Box>
                        </Card>
                      </Grid>
                    )}

                    {/* Sujet et Description */}
                    <Grid xs={12} md={selectedClaim.order ? 6 : 12}>
                      <Card variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                        <Typography variant="h6" gutterBottom>Sujet et Description</Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                          <Box>
                            <Typography variant="body2" color="text.secondary" gutterBottom>Sujet:</Typography>
                            <Chip
                              label={subjectLabels[selectedClaim.subject as keyof typeof subjectLabels] || selectedClaim.subject}
                              variant="outlined"
                              sx={{ fontWeight: 'medium' }}
                            />
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" gutterBottom>Description:</Typography>
                            <Typography variant="body2" sx={{
                              whiteSpace: 'pre-wrap',
                              p: 1,
                              bgcolor: 'grey.50',
                              borderRadius: 1,
                              border: '1px solid',
                              borderColor: 'grey.200'
                            }}>
                              {selectedClaim.description}
                            </Typography>
                          </Box>
                        </Box>
                      </Card>
                    </Grid>
                  </Grid>
                </Box>
                
                <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)} sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                  <Tab label="Messages" />
                  <Tab label="Historique des statuts" />
                </Tabs>
                
                {activeTab === 0 && (
                  <>
                    <Box sx={{ mb: 2, maxHeight: '400px', overflowY: 'auto' }}>
                      {selectedClaim.messages && Array.isArray(selectedClaim.messages) && selectedClaim.messages.length > 0 ? (
                        <>
                          <Typography variant="subtitle2" sx={{ mb: 2 }}>
                            {selectedClaim.messages.length} message(s) dans cette conversation
                          </Typography>
                          {selectedClaim.messages.map((message) => (
                            <Card
                              key={message.id}
                              sx={{
                                mb: 2,
                                bgcolor: message.sender === 'support' ? 'primary.50' :
                                         message.sender === 'system' ? 'warning.50' : 'background.paper',
                                border: '1px solid',
                                borderColor: message.sender === 'support' ? 'primary.200' :
                                           message.sender === 'system' ? 'warning.200' : 'grey.200'
                              }}
                            >
                              <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <Avatar sx={{
                                    mr: 1,
                                    bgcolor: message.sender === 'customer' ? 'secondary.main' :
                                            message.sender === 'system' ? 'warning.main' : 'primary.main',
                                    width: 32,
                                    height: 32
                                  }}>
                                    {getSenderIcon(message.sender)}
                                  </Avatar>
                                  <Box sx={{ flexGrow: 1 }}>
                                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                                      {getSenderName(message.sender)}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {formatDate(message.created_at)} à {formatTime(message.created_at)}
                                    </Typography>
                                  </Box>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {!message.is_read && (
                                      <Chip
                                        label="Non lu"
                                        size="small"
                                        color="warning"
                                        variant="outlined"
                                      />
                                    )}
                                    <Typography variant="caption" sx={{ fontFamily: 'monospace', color: 'text.secondary' }}>
                                      ID: {message.id}
                                    </Typography>
                                  </Box>
                                </Box>

                                <Typography variant="body2" sx={{
                                  whiteSpace: 'pre-wrap',
                                  p: 1.5,
                                  bgcolor: 'background.paper',
                                  borderRadius: 1,
                                  border: '1px solid',
                                  borderColor: 'grey.200'
                                }}>
                                  {message.message}
                                </Typography>

                                {message.attachments && Array.isArray(message.attachments) && message.attachments.length > 0 && (
                                  <Box sx={{ mt: 2 }}>
                                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                      📎 Pièces jointes ({message.attachments.length}):
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                      {message.attachments.map((attachment) => (
                                        <Chip
                                          key={attachment.id}
                                          label={`${attachment.file_name} (${(attachment.file_size / 1024).toFixed(1)} KB)`}
                                          component="a"
                                          href={attachment.file_url}
                                          target="_blank"
                                          clickable
                                          icon={<AttachFileIcon />}
                                          variant="outlined"
                                          sx={{
                                            '&:hover': {
                                              backgroundColor: 'primary.50'
                                            }
                                          }}
                                        />
                                      ))}
                                    </Box>
                                  </Box>
                                )}
                              </CardContent>
                            </Card>
                          ))}
                        </>
                      ) : (
                        <Box sx={{ textAlign: 'center', py: 4 }}>
                          <Typography color="text.secondary">Aucun message dans cette réclamation</Typography>
                          <Typography variant="caption" color="text.secondary">
                            Les messages apparaîtront ici une fois que la conversation aura commencé
                          </Typography>
                        </Box>
                      )}
                    </Box>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>Répondre</Typography>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      placeholder="Votre réponse..."
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      sx={{ mb: 2 }}
                    />
                    
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>
                      <Button
                        variant="outlined"
                        component="label"
                        startIcon={<AttachFileIcon />}
                        sx={{ alignSelf: 'flex-start' }}
                      >
                        Ajouter des fichiers
                        <input
                          type="file"
                          hidden
                          multiple
                          onChange={handleFileChange}
                        />
                      </Button>
                      
                      {selectedFiles.length > 0 && (
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>Fichiers sélectionnés:</Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {selectedFiles.map((file, index) => (
                              <Chip 
                                key={index}
                                label={file.name}
                                onDelete={() => handleRemoveFile(index)}
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Box>
                    
                    <Button
                      variant="contained"
                      color="primary"
                      endIcon={<SendIcon />}
                      onClick={handleSendReply}
                      disabled={!replyMessage.trim() || loading}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Envoyer'}
                    </Button>
                  </>
                )}
                
                {activeTab === 1 && (
                  <Box>
                    {selectedClaim.status_history && Array.isArray(selectedClaim.status_history) && selectedClaim.status_history.length > 0 ? (
                      <>
                        <Typography variant="subtitle2" sx={{ mb: 2 }}>
                          {selectedClaim.status_history.length} modification(s) de statut
                        </Typography>
                        <TableContainer component={Paper} variant="outlined">
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Date & Heure</TableCell>
                                <TableCell>Changement de Statut</TableCell>
                                <TableCell>Commentaire</TableCell>
                                <TableCell>Modifié par</TableCell>
                                <TableCell>ID</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {selectedClaim.status_history.map((history) => (
                                <TableRow key={history.id}>
                                  <TableCell>
                                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                      {formatDate(history.created_at)}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {formatTime(history.created_at)}
                                    </Typography>
                                  </TableCell>
                                  <TableCell>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <Chip
                                        label={statusLabels[history.old_status as keyof typeof statusLabels] || history.old_status}
                                        size="small"
                                        sx={{
                                          backgroundColor: statusColors[history.old_status as keyof typeof statusColors] || '#9E9E9E',
                                          color: 'white'
                                        }}
                                      />
                                      <Typography variant="body2">→</Typography>
                                      <Chip
                                        label={statusLabels[history.new_status as keyof typeof statusLabels] || history.new_status}
                                        size="small"
                                        sx={{
                                          backgroundColor: statusColors[history.new_status as keyof typeof statusColors] || '#9E9E9E',
                                          color: 'white'
                                        }}
                                      />
                                    </Box>
                                  </TableCell>
                                  <TableCell>
                                    <Typography variant="body2" sx={{
                                      maxWidth: 200,
                                      wordBreak: 'break-word',
                                      whiteSpace: 'pre-wrap'
                                    }}>
                                      {history.comment || '-'}
                                    </Typography>
                                  </TableCell>
                                  <TableCell>
                                    <Box>
                                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                        {history.created_by ? history.created_by.full_name : 'Système'}
                                      </Typography>
                                      {history.created_by && (
                                        <Typography variant="caption" color="text.secondary">
                                          {history.created_by.email}
                                        </Typography>
                                      )}
                                    </Box>
                                  </TableCell>
                                  <TableCell>
                                    <Typography variant="caption" sx={{ fontFamily: 'monospace', color: 'text.secondary' }}>
                                      {history.id}
                                    </Typography>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography color="text.secondary">Aucun historique de statut disponible</Typography>
                        <Typography variant="caption" color="text.secondary">
                          L'historique des modifications de statut apparaîtra ici
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDetailsOpen(false)}>Fermer</Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => {
                    setDetailsOpen(false);
                    handleUpdateStatus(selectedClaim);
                  }}
                >
                  Mettre à jour le statut
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>

        {/* Update Status Dialog */}
        <Dialog open={updateStatusOpen} onClose={() => setUpdateStatusOpen(false)}>
          {selectedClaim && (
            <>
              <DialogTitle>Mettre à jour le statut de la réclamation {selectedClaim.claim_number}</DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1, minWidth: 400 }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel id="new-status-label">Nouveau statut</InputLabel>
                    <Select
                      labelId="new-status-label"
                      value={newStatus}
                      onChange={(e) => setNewStatus(e.target.value)}
                      label="Nouveau statut"
                    >
                      <MenuItem value="new">Nouveau</MenuItem>
                      <MenuItem value="in_progress">En cours</MenuItem>
                      <MenuItem value="waiting_customer">En attente du client</MenuItem>
                      <MenuItem value="waiting_staff">En attente du support</MenuItem>
                      <MenuItem value="resolved">Résolu</MenuItem>
                      <MenuItem value="closed">Fermé</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    label="Note administrative (optionnelle)"
                    multiline
                    rows={4}
                    fullWidth
                    value={adminNote}
                    onChange={(e) => setAdminNote(e.target.value)}
                  />
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setUpdateStatusOpen(false)}>Annuler</Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={submitStatusUpdate}
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Enregistrer'}
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Box>
    </AdminLayout>
  );
};

export default AdminClaims;
