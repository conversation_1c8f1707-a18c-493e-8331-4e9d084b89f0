from django.db import models
from django.conf import settings
from django.db.models import F, Sum, Count, Avg
from products.models import Product, ProductVariant
from datetime import datetime, timedelta
import uuid

class ProductView(models.Model):
    """
    Model for tracking product views.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='views')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    referrer = models.URLField(blank=True, null=True)
    device_type = models.CharField(max_length=20, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"View of {self.product.name} at {self.created_at}"

class CartAction(models.Model):
    """
    Model for tracking cart actions (add, remove, update).
    """
    ACTION_CHOICES = [
        ('add', 'Add to Cart'),
        ('remove', 'Remove from Cart'),
        ('update', 'Update Quantity'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='cart_actions')
    variant = models.ForeignKey(ProductVariant, on_delete=models.SET_NULL, null=True, blank=True, related_name='cart_actions')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True, null=True)
    action = models.CharField(max_length=10, choices=ACTION_CHOICES)
    quantity = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"{self.get_action_display()} {self.quantity} of {self.product.name}"

class WishlistAction(models.Model):
    """
    Model for tracking wishlist actions (add, remove).
    """
    ACTION_CHOICES = [
        ('add', 'Add to Wishlist'),
        ('remove', 'Remove from Wishlist'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='wishlist_actions')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    action = models.CharField(max_length=10, choices=ACTION_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"{self.get_action_display()} {self.product.name}"

class SalesData(models.Model):
    """
    Model for tracking sales data.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='sales_data')
    variant = models.ForeignKey(ProductVariant, on_delete=models.SET_NULL, null=True, blank=True, related_name='sales_data')
    order_id = models.CharField(max_length=100)
    customer_id = models.PositiveIntegerField()
    quantity = models.PositiveIntegerField()
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['product', 'date']),
            models.Index(fields=['customer_id', 'date']),
        ]

    def __str__(self):
        return f"Sale of {self.quantity} {self.product.name} on {self.date}"

    @classmethod
    def get_sales_by_period(cls, product_id, period='month', start_date=None, end_date=None):
        """
        Get sales data aggregated by period (day, week, month).
        """
        if not start_date:
            if period == 'day':
                start_date = datetime.now() - timedelta(days=14)
            elif period == 'week':
                start_date = datetime.now() - timedelta(weeks=12)
            else:  # month
                start_date = datetime.now() - timedelta(days=365)

        if not end_date:
            end_date = datetime.now()

        queryset = cls.objects.filter(
            product_id=product_id,
            date__gte=start_date.date(),
            date__lte=end_date.date()
        )

        if period == 'day':
            return queryset.values('date').annotate(
                period=F('date'),
                quantity=Sum('quantity'),
                revenue=Sum('total_price')
            ).order_by('date')
        elif period == 'week':
            return queryset.extra(select={'week': "EXTRACT(WEEK FROM date)"}).values('week').annotate(
                period=F('week'),
                quantity=Sum('quantity'),
                revenue=Sum('total_price')
            ).order_by('week')
        else:  # month
            return queryset.extra(select={'month': "EXTRACT(YEAR FROM date) || '-' || EXTRACT(MONTH FROM date)"}).values('month').annotate(
                period=F('month'),
                quantity=Sum('quantity'),
                revenue=Sum('total_price')
            ).order_by('month')

class SystemLog(models.Model):
    """
    Model for system logs and activity tracking.
    """
    LOG_TYPES = [
        ('info', 'Information'),
        ('success', 'Success'),
        ('warning', 'Warning'),
        ('error', 'Error'),
    ]

    MODULE_CHOICES = [
        ('utilisateurs', 'Utilisateurs'),
        ('commandes', 'Commandes'),
        ('paiement', 'Paiement'),
        ('stocks', 'Stocks'),
        ('cms', 'CMS'),
        ('promotions', 'Promotions'),
        ('serveur', 'Serveur'),
        ('securite', 'Sécurité'),
        ('systeme', 'Système'),
        ('produits', 'Produits'),
        ('messagerie', 'Messagerie'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    log_id = models.CharField(max_length=20, unique=True, editable=False)
    timestamp = models.DateTimeField(auto_now_add=True)
    type = models.CharField(max_length=10, choices=LOG_TYPES)
    module = models.CharField(max_length=20, choices=MODULE_CHOICES)
    message = models.TextField()
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='system_logs'
    )
    user_email = models.EmailField(blank=True, null=True)
    additional_data = models.JSONField(blank=True, null=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['-timestamp']),
            models.Index(fields=['type']),
            models.Index(fields=['module']),
            models.Index(fields=['user']),
        ]

    def __str__(self):
        return f"{self.log_id} - {self.get_type_display()} - {self.timestamp}"

    def save(self, *args, **kwargs):
        if not self.log_id:
            # Generate a log ID like LOG-00001
            last_log = SystemLog.objects.order_by('-log_id').first()
            if last_log and last_log.log_id.startswith('LOG-'):
                try:
                    last_id = int(last_log.log_id.split('-')[1])
                    self.log_id = f"LOG-{(last_id + 1):05d}"
                except (ValueError, IndexError):
                    self.log_id = f"LOG-00001"
            else:
                self.log_id = "LOG-00001"

        # If user is provided but not user_email, get the email from the user
        if self.user and not self.user_email:
            self.user_email = self.user.email

        super().save(*args, **kwargs)

    @classmethod
    def log(cls, type, module, message, user=None, ip_address=None, additional_data=None):
        """
        Create a new log entry.
        """
        log_entry = cls(
            type=type,
            module=module,
            message=message,
            user=user,
            ip_address=ip_address,
            additional_data=additional_data
        )
        log_entry.save()
        return log_entry


class HomePageContent(models.Model):
    """
    Model for managing dynamic content on the home page.
    """
    SECTION_CHOICES = [
        ('hero', 'Section Hero'),
        ('services', 'Section Services'),
        ('features', 'Section Avantages'),
        ('about', 'Section À Propos'),
        ('testimonials', 'Section Témoignages'),
        ('newsletter', 'Section Newsletter'),
        ('cta', 'Call to Action'),
    ]

    section = models.CharField(max_length=20, choices=SECTION_CHOICES, unique=True)
    title = models.CharField(max_length=200)
    subtitle = models.CharField(max_length=300, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    button_text = models.CharField(max_length=100, blank=True, null=True)
    button_link = models.CharField(max_length=200, blank=True, null=True)
    image_url = models.URLField(blank=True, null=True)
    background_color = models.CharField(max_length=50, blank=True, null=True)
    text_color = models.CharField(max_length=50, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'section']

    def __str__(self):
        return f"{self.get_section_display()} - {self.title}"


class HomePageStats(models.Model):
    """
    Model for managing the statistics displayed on the home page.
    """
    name = models.CharField(max_length=100, unique=True)
    display_name = models.CharField(max_length=100)
    value = models.PositiveIntegerField(default=0)
    suffix = models.CharField(max_length=10, blank=True, null=True)  # e.g., "+", "K", "M"
    icon = models.CharField(max_length=50, blank=True, null=True)  # Lucide icon name
    description = models.CharField(max_length=200, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    auto_update = models.BooleanField(default=False)  # If True, value is calculated automatically
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']

    def __str__(self):
        return f"{self.display_name}: {self.value}{self.suffix or ''}"

    def get_display_value(self):
        """Return formatted value for display."""
        return f"{self.value}{self.suffix or ''}"


class HomePageService(models.Model):
    """
    Model for managing services displayed on the home page.
    """
    name = models.CharField(max_length=100)
    title = models.CharField(max_length=150)
    description = models.TextField()
    icon = models.CharField(max_length=50)  # Lucide icon name
    color = models.CharField(max_length=50, default='blue')  # Color theme
    link = models.CharField(max_length=200, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']

    def __str__(self):
        return self.title


class HomePageTestimonial(models.Model):
    """
    Model for managing testimonials displayed on the home page.
    """
    client_name = models.CharField(max_length=100)
    client_title = models.CharField(max_length=100, blank=True, null=True)
    client_company = models.CharField(max_length=100, blank=True, null=True)
    testimonial = models.TextField()
    rating = models.PositiveIntegerField(default=5, choices=[(i, i) for i in range(1, 6)])
    client_image = models.URLField(blank=True, null=True)
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', '-created_at']

    def __str__(self):
        return f"{self.client_name} - {self.rating}★"


class HomePageFeature(models.Model):
    """
    Model for managing features/advantages displayed on the home page.
    """
    title = models.CharField(max_length=100)
    description = models.TextField()
    icon = models.CharField(max_length=50)  # Lucide icon name
    color = models.CharField(max_length=50, default='primary')
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'title']

    def __str__(self):
        return self.title
