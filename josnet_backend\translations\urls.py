from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'keys', views.TranslationKeyViewSet)

urlpatterns = [
    # ViewSets
    path('', include(router.urls)),
    
    # Endpoints publics
    path('translations/', views.get_translations, name='get-translations'),
    path('languages/', views.get_languages, name='get-languages'),
    path('stats/', views.translation_stats, name='translation-stats'),
    
    # Endpoints utilisateur
    path('user/language/', views.get_user_language, name='get-user-language'),
    path('user/language/set/', views.set_user_language, name='set-user-language'),
    
    # Endpoints admin
    path('export/', views.export_translations_view, name='export-translations'),
    path('detect-missing/', views.detect_missing_translations, name='detect-missing-translations'),
]
