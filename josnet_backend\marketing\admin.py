from django.contrib import admin
from django import forms
from django.utils.html import format_html
# from django.utils.safestring import mark_safe  # Temporarily commented out
from .models import Promotion, Campaign  # Temporarily removed PromotionUsage
from products.models import Product, Category

class PromotionAdminForm(forms.ModelForm):
    product = forms.ModelChoiceField(
        queryset=Product.objects.all(),
        required=False,
        label="Target Product"
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        label="Target Category"
    )

    class Meta:
        model = Promotion
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get('instance')

        # Hide product and category fields initially
        self.fields['product'].widget.attrs['style'] = 'display: none;'
        self.fields['category'].widget.attrs['style'] = 'display: none;'

        if instance:
            if instance.applies_to == 'product' and instance.target_id:
                try:
                    self.fields['product'].initial = Product.objects.get(id=instance.target_id)
                    self.fields['product'].widget.attrs['style'] = ''
                except Product.DoesNotExist:
                    pass
            elif instance.applies_to == 'category' and instance.target_id:
                try:
                    self.fields['category'].initial = Category.objects.get(id=instance.target_id)
                    self.fields['category'].widget.attrs['style'] = ''
                except Category.DoesNotExist:
                    pass

    def clean(self):
        cleaned_data = super().clean()
        applies_to = cleaned_data.get('applies_to')
        product = cleaned_data.get('product')
        category = cleaned_data.get('category')

        if applies_to == 'product' and not product:
            self.add_error('product', 'Please select a product for product-specific promotion')
        elif applies_to == 'category' and not category:
            self.add_error('category', 'Please select a category for category-specific promotion')

        # Set target_id based on selection
        if applies_to == 'product' and product:
            cleaned_data['target_id'] = product.id
        elif applies_to == 'category' and category:
            cleaned_data['target_id'] = category.id
        elif applies_to == 'cart':
            cleaned_data['target_id'] = None

        return cleaned_data

# Temporarily comment out PromotionUsageInline to avoid database errors
# class PromotionUsageInline(admin.TabularInline):
#     model = PromotionUsage
#     extra = 0
#     readonly_fields = ('order', 'user', 'discount_amount', 'created_at')
#     can_delete = False
#     max_num = 10  # Limit the number of items shown
#
#     def has_add_permission(self, request, obj=None):
#         return False

@admin.register(Promotion)
class PromotionAdmin(admin.ModelAdmin):
    form = PromotionAdminForm
    list_display = ('name', 'discount_display', 'applies_to_display', 'validity_period', 'is_active')  # Removed usage_stats
    list_filter = ('discount_type', 'applies_to', 'is_active', 'start_date', 'end_date')
    search_fields = ('name', 'description', 'discount_code')
    date_hierarchy = 'start_date'
    actions = ['activate_promotions', 'deactivate_promotions']
    # Temporarily removed inlines to avoid database errors
    # inlines = [PromotionUsageInline]

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'discount_code', 'is_active')
        }),
        ('Discount Details', {
            'fields': ('discount_type', 'discount_value')
        }),
        ('Target', {
            'fields': ('applies_to', 'product', 'category', 'min_purchase_amount')
        }),
        ('Validity Period', {
            'fields': ('start_date', 'end_date')
        }),
    )

    class Media:
        js = ('js/promotion_admin.js',)

    def discount_display(self, obj):
        if obj.discount_type == 'percentage':
            return f"{obj.discount_value}%"
        else:
            return f"{obj.discount_value} BIF"
    discount_display.short_description = "Discount"

    def applies_to_display(self, obj):
        if obj.applies_to == 'product':
            return format_html(f"Product: <strong>{obj.target_name or 'Unknown'}</strong>")
        elif obj.applies_to == 'category':
            return format_html(f"Category: <strong>{obj.target_name or 'Unknown'}</strong>")
        else:
            return "Cart"
    applies_to_display.short_description = "Applies To"

    def validity_period(self, obj):
        if obj.end_date:
            return format_html(f"{obj.start_date.strftime('%d/%m/%Y')} - {obj.end_date.strftime('%d/%m/%Y')}")
        else:
            return format_html(f"From {obj.start_date.strftime('%d/%m/%Y')} <span style='color: green;'>(No end date)</span>")
    validity_period.short_description = "Validity Period"

    # Temporarily commented out to avoid database errors
    # def usage_stats(self, obj):
    #     usage_count = obj.usage_count
    #     total_discount = obj.total_discount_amount
    #
    #     if usage_count == 0:
    #         return format_html('<span style="color: #888;">Not used yet</span>')
    #
    #     return format_html(
    #         '<span style="color: #007bff; font-weight: bold;">{} uses</span><br>'
    #         '<span style="color: #28a745;">{:,.2f} BIF</span> total discount',
    #         usage_count, total_discount
    #     )
    # usage_stats.short_description = "Usage Statistics"

    def activate_promotions(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} promotions have been activated.")
    activate_promotions.short_description = "Activate selected promotions"

    def deactivate_promotions(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} promotions have been deactivated.")
    deactivate_promotions.short_description = "Deactivate selected promotions"

@admin.register(Campaign)
class CampaignAdmin(admin.ModelAdmin):
    list_display = ('name', 'discount_percentage', 'discount_amount', 'start_date', 'end_date', 'is_active', 'product_count')
    list_filter = ('is_active',)
    search_fields = ('name', 'description', 'discount_code')
    date_hierarchy = 'start_date'
    filter_horizontal = ('products',)
    actions = ['activate_campaigns', 'deactivate_campaigns']

    def activate_campaigns(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} campaigns have been activated.")
    activate_campaigns.short_description = "Activate selected campaigns"

    def deactivate_campaigns(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} campaigns have been deactivated.")
    deactivate_campaigns.short_description = "Deactivate selected campaigns"

# Temporarily commented out to avoid database errors
# @admin.register(PromotionUsage)
# class PromotionUsageAdmin(admin.ModelAdmin):
#     list_display = ('promotion', 'order', 'user', 'discount_amount', 'created_at')
#     list_filter = ('promotion', 'created_at')
#     search_fields = ('promotion__name', 'order__order_number', 'user__email')
#     date_hierarchy = 'created_at'
#     readonly_fields = ('promotion', 'order', 'user', 'discount_amount', 'created_at')
#
#     def has_add_permission(self, request):
#         return False
#
#     def has_change_permission(self, request, obj=None):
#         return False
