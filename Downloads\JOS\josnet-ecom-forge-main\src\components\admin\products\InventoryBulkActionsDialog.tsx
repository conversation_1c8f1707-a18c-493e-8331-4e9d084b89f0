import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Loader2 } from 'lucide-react';
import { productApi } from '@/services/productApi';
import { useToast } from '@/hooks/use-toast';

interface InventoryBulkActionsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedItems: number[];
  onSuccess: () => void;
}

const InventoryBulkActionsDialog: React.FC<InventoryBulkActionsDialogProps> = ({
  isOpen,
  onOpenChange,
  selectedItems,
  onSuccess
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('adjust');
  
  // Adjust stock state
  const [quantity, setQuantity] = useState('0');
  const [reason, setReason] = useState('adjustment');
  const [reference, setReference] = useState('');
  const [notes, setNotes] = useState('');
  
  // Threshold state
  const [threshold, setThreshold] = useState('5');
  
  // Bulk adjust mutation
  const bulkAdjustMutation = useMutation({
    mutationFn: (data: any) => productApi.bulkAdjustInventory(data),
    onSuccess: (data) => {
      const successCount = data.filter((result: any) => result.success).length;
      const errorCount = data.length - successCount;
      
      toast({
        title: 'Ajustement en masse terminé',
        description: `${successCount} produits ajustés avec succès. ${errorCount} erreurs.`,
        variant: errorCount > 0 ? 'destructive' : 'default',
      });
      
      onSuccess();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de l\'ajustement en masse.',
        variant: 'destructive',
      });
    },
  });
  
  // Update threshold mutation
  const updateThresholdMutation = useMutation({
    mutationFn: (data: any) => productApi.bulkUpdateThreshold(data),
    onSuccess: (data) => {
      const successCount = data.filter((result: any) => result.success).length;
      const errorCount = data.length - successCount;
      
      toast({
        title: 'Seuils mis à jour',
        description: `${successCount} seuils mis à jour avec succès. ${errorCount} erreurs.`,
        variant: errorCount > 0 ? 'destructive' : 'default',
      });
      
      onSuccess();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la mise à jour des seuils.',
        variant: 'destructive',
      });
    },
  });
  
  const handleAdjustSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedItems.length === 0) return;
    
    const adjustments = selectedItems.map(id => ({
      inventory_id: id,
      quantity: parseInt(quantity),
      reason,
      reference: reference || undefined,
      notes: notes || undefined
    }));
    
    bulkAdjustMutation.mutate({ adjustments });
  };
  
  const handleThresholdSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedItems.length === 0) return;
    
    updateThresholdMutation.mutate({
      inventory_ids: selectedItems,
      threshold: parseInt(threshold)
    });
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Actions en Masse</DialogTitle>
          <DialogDescription>
            Appliquez des actions à {selectedItems.length} produits sélectionnés.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="adjust">Ajuster le Stock</TabsTrigger>
            <TabsTrigger value="threshold">Modifier les Seuils</TabsTrigger>
          </TabsList>
          
          <TabsContent value="adjust">
            <form onSubmit={handleAdjustSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="bulk-quantity" className="text-right">
                    Quantité
                  </Label>
                  <Input
                    id="bulk-quantity"
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(e.target.value)}
                    className="col-span-3"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="bulk-reason" className="text-right">
                    Raison
                  </Label>
                  <Select value={reason} onValueChange={setReason}>
                    <SelectTrigger id="bulk-reason" className="col-span-3">
                      <SelectValue placeholder="Sélectionner une raison" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="purchase">Achat</SelectItem>
                      <SelectItem value="sale">Vente</SelectItem>
                      <SelectItem value="return">Retour</SelectItem>
                      <SelectItem value="adjustment">Ajustement</SelectItem>
                      <SelectItem value="loss">Perte/Dommage</SelectItem>
                      <SelectItem value="transfer">Transfert</SelectItem>
                      <SelectItem value="other">Autre</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="bulk-reference" className="text-right">
                    Référence
                  </Label>
                  <Input
                    id="bulk-reference"
                    value={reference}
                    onChange={(e) => setReference(e.target.value)}
                    className="col-span-3"
                    placeholder="ex: Commande #12345"
                  />
                </div>
                
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="bulk-notes" className="text-right pt-2">
                    Notes
                  </Label>
                  <Textarea
                    id="bulk-notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="col-span-3"
                    placeholder="Informations supplémentaires"
                    rows={3}
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Annuler
                </Button>
                <Button 
                  type="submit" 
                  disabled={bulkAdjustMutation.isPending || quantity === '0'}
                >
                  {bulkAdjustMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Ajustement...
                    </>
                  ) : (
                    'Ajuster le Stock'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </TabsContent>
          
          <TabsContent value="threshold">
            <form onSubmit={handleThresholdSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="bulk-threshold" className="text-right">
                    Seuil de Stock Bas
                  </Label>
                  <Input
                    id="bulk-threshold"
                    type="number"
                    min="0"
                    value={threshold}
                    onChange={(e) => setThreshold(e.target.value)}
                    className="col-span-3"
                    required
                  />
                </div>
                
                <div className="col-span-4 text-sm text-gray-500 mt-2">
                  <p>
                    Cette action définira le seuil de stock bas pour tous les produits sélectionnés.
                    Lorsque le stock disponible tombe en dessous de ce seuil, le produit sera marqué comme "Stock Bas".
                  </p>
                </div>
              </div>
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Annuler
                </Button>
                <Button 
                  type="submit" 
                  disabled={updateThresholdMutation.isPending}
                >
                  {updateThresholdMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Mise à jour...
                    </>
                  ) : (
                    'Mettre à Jour les Seuils'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryBulkActionsDialog;
