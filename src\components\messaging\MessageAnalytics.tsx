import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Clock,
  MessageSquare,
  Users,
  CheckCircle,
  AlertCircle,
  Star,
  Calendar,
  Download,
  Filter
} from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnalyticsData {
  total_conversations: number;
  new_conversations: number;
  in_progress_conversations: number;
  resolved_conversations: number;
  closed_conversations: number;
  average_response_time: number;
  average_resolution_time: number;
  customer_satisfaction: number;
  staff_performance: Array<{
    staff_id: number;
    staff_name: string;
    conversations_handled: number;
    average_response_time: number;
    customer_rating: number;
  }>;
  daily_stats: Array<{
    date: string;
    new_conversations: number;
    resolved_conversations: number;
    response_time: number;
  }>;
  category_breakdown: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  priority_breakdown: Array<{
    priority: string;
    count: number;
    percentage: number;
  }>;
}

interface MessageAnalyticsProps {
  data?: AnalyticsData;
  className?: string;
}

const MessageAnalytics: React.FC<MessageAnalyticsProps> = ({
  data,
  className
}) => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('conversations');

  // Mock data if none provided
  const mockData: AnalyticsData = {
    total_conversations: 156,
    new_conversations: 23,
    in_progress_conversations: 45,
    resolved_conversations: 78,
    closed_conversations: 10,
    average_response_time: 15.5,
    average_resolution_time: 4.2,
    customer_satisfaction: 4.6,
    staff_performance: [
      {
        staff_id: 1,
        staff_name: "Marie Dubois",
        conversations_handled: 34,
        average_response_time: 12.3,
        customer_rating: 4.8
      },
      {
        staff_id: 2,
        staff_name: "Jean Martin",
        conversations_handled: 28,
        average_response_time: 18.7,
        customer_rating: 4.5
      },
      {
        staff_id: 3,
        staff_name: "Sophie Laurent",
        conversations_handled: 31,
        average_response_time: 14.2,
        customer_rating: 4.7
      }
    ],
    daily_stats: [
      { date: '2024-01-15', new_conversations: 12, resolved_conversations: 15, response_time: 14.2 },
      { date: '2024-01-16', new_conversations: 18, resolved_conversations: 12, response_time: 16.8 },
      { date: '2024-01-17', new_conversations: 15, resolved_conversations: 20, response_time: 13.5 },
      { date: '2024-01-18', new_conversations: 22, resolved_conversations: 18, response_time: 15.1 },
      { date: '2024-01-19', new_conversations: 19, resolved_conversations: 16, response_time: 12.9 },
      { date: '2024-01-20', new_conversations: 25, resolved_conversations: 22, response_time: 17.3 },
      { date: '2024-01-21', new_conversations: 21, resolved_conversations: 19, response_time: 14.8 }
    ],
    category_breakdown: [
      { category: 'Support technique', count: 45, percentage: 28.8 },
      { category: 'Commandes', count: 38, percentage: 24.4 },
      { category: 'Remboursements', count: 32, percentage: 20.5 },
      { category: 'Informations produit', count: 25, percentage: 16.0 },
      { category: 'Autre', count: 16, percentage: 10.3 }
    ],
    priority_breakdown: [
      { priority: 'Basse', count: 62, percentage: 39.7 },
      { priority: 'Moyenne', count: 54, percentage: 34.6 },
      { priority: 'Haute', count: 28, percentage: 17.9 },
      { priority: 'Urgente', count: 12, percentage: 7.7 }
    ]
  };

  const analytics = data || mockData;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'text-yellow-600 bg-yellow-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'resolved': return 'text-green-600 bg-green-100';
      case 'closed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgente': return 'text-red-600 bg-red-100';
      case 'haute': return 'text-orange-600 bg-orange-100';
      case 'moyenne': return 'text-blue-600 bg-blue-100';
      case 'basse': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes.toFixed(1)}min`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}min`;
  };

  const getPerformanceColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 4.0) return 'text-blue-600';
    if (rating >= 3.5) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics Messagerie</h2>
          <p className="text-gray-500">Performances et métriques de votre service client</p>
        </div>
        <div className="flex items-center space-x-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">24 heures</SelectItem>
              <SelectItem value="7d">7 jours</SelectItem>
              <SelectItem value="30d">30 jours</SelectItem>
              <SelectItem value="90d">90 jours</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.total_conversations}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                +12% par rapport à la semaine dernière
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Temps de Réponse Moyen</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(analytics.average_response_time)}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingDown className="h-3 w-3 inline mr-1 text-green-600" />
                -8% amélioration
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Satisfaction Client</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.customer_satisfaction}/5</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                +0.2 points ce mois
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Taux de Résolution</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round((analytics.resolved_conversations / analytics.total_conversations) * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {analytics.resolved_conversations} sur {analytics.total_conversations} résolues
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Répartition par Statut</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-yellow-100 text-yellow-800">Nouveaux</Badge>
                </div>
                <span className="font-medium">{analytics.new_conversations}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-blue-100 text-blue-800">En cours</Badge>
                </div>
                <span className="font-medium">{analytics.in_progress_conversations}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-green-100 text-green-800">Résolus</Badge>
                </div>
                <span className="font-medium">{analytics.resolved_conversations}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-gray-100 text-gray-800">Fermés</Badge>
                </div>
                <span className="font-medium">{analytics.closed_conversations}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Répartition par Priorité</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.priority_breakdown.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getPriorityColor(item.priority)}>
                      {item.priority}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{item.count}</span>
                    <span className="text-sm text-gray-500">({item.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Staff Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Performance de l'Équipe</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.staff_performance.map((staff, index) => (
              <motion.div
                key={staff.staff_id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">{staff.staff_name}</h4>
                    <p className="text-sm text-gray-500">
                      {staff.conversations_handled} conversations traitées
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 text-sm">
                  <div className="text-center">
                    <div className="font-medium">{formatTime(staff.average_response_time)}</div>
                    <div className="text-gray-500">Temps moyen</div>
                  </div>
                  <div className="text-center">
                    <div className={cn("font-medium", getPerformanceColor(staff.customer_rating))}>
                      {staff.customer_rating}/5
                    </div>
                    <div className="text-gray-500">Note client</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Category Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Répartition par Catégorie</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.category_breakdown.map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="font-medium">{category.category}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${category.percentage}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium w-12 text-right">
                    {category.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MessageAnalytics;
