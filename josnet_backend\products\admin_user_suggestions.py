from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count
from .models_user_suggestions import (
    ProductSuggestion, 
    ProductSuggestionVote, 
    ProductSuggestionComment, 
    ProductSuggestionAttachment
)


class ProductSuggestionVoteInline(admin.TabularInline):
    """Inline pour les votes sur les suggestions."""
    model = ProductSuggestionVote
    extra = 0
    readonly_fields = ['user', 'created_at']
    can_delete = False


class ProductSuggestionCommentInline(admin.TabularInline):
    """Inline pour les commentaires sur les suggestions."""
    model = ProductSuggestionComment
    extra = 0
    readonly_fields = ['user', 'created_at', 'updated_at']
    fields = ['user', 'content', 'is_admin_comment', 'created_at']


class ProductSuggestionAttachmentInline(admin.TabularInline):
    """Inline pour les pièces jointes des suggestions."""
    model = ProductSuggestionAttachment
    extra = 0
    readonly_fields = ['uploaded_by', 'file_size', 'content_type', 'created_at']
    fields = ['file', 'filename', 'uploaded_by', 'file_size', 'created_at']


@admin.register(ProductSuggestion)
class ProductSuggestionAdmin(admin.ModelAdmin):
    """Administration des suggestions de produits."""
    
    list_display = [
        'name', 'user_email', 'category', 'status_badge', 'priority_badge',
        'votes_count', 'days_since_creation', 'created_at'
    ]
    list_filter = [
        'status', 'priority', 'category', 'created_at', 'response_date'
    ]
    search_fields = [
        'name', 'description', 'user__email', 'user__first_name', 'user__last_name'
    ]
    readonly_fields = [
        'user', 'created_at', 'updated_at', 'days_since_creation',
        'response_time_days', 'votes_count'
    ]
    
    fieldsets = (
        ('Informations de base', {
            'fields': (
                'user', 'name', 'category', 'description', 
                'estimated_price', 'reason'
            )
        }),
        ('Statut et gestion', {
            'fields': (
                'status', 'priority', 'admin_response', 
                'reviewed_by', 'response_date'
            )
        }),
        ('Implémentation', {
            'fields': (
                'implementation_notes', 'estimated_implementation_time'
            ),
            'classes': ('collapse',)
        }),
        ('Métadonnées', {
            'fields': (
                'votes_count', 'created_at', 'updated_at',
                'days_since_creation', 'response_time_days'
            ),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [
        ProductSuggestionVoteInline,
        ProductSuggestionCommentInline,
        ProductSuggestionAttachmentInline
    ]
    
    actions = ['approve_suggestions', 'reject_suggestions', 'mark_as_implemented']
    
    def get_queryset(self, request):
        """Optimise les requêtes avec select_related et prefetch_related."""
        return super().get_queryset(request).select_related(
            'user', 'reviewed_by'
        ).prefetch_related(
            'votes', 'comments', 'attachments'
        ).annotate(
            votes_count_annotated=Count('votes')
        )
    
    def user_email(self, obj):
        """Affiche l'email de l'utilisateur."""
        return obj.user.email
    user_email.short_description = 'Utilisateur'
    user_email.admin_order_field = 'user__email'
    
    def status_badge(self, obj):
        """Affiche le statut avec une couleur."""
        colors = {
            'pending': '#ffc107',
            'approved': '#28a745',
            'rejected': '#dc3545',
            'implemented': '#17a2b8'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Statut'
    status_badge.admin_order_field = 'status'
    
    def priority_badge(self, obj):
        """Affiche la priorité avec une couleur."""
        colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545'
        }
        color = colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_badge.short_description = 'Priorité'
    priority_badge.admin_order_field = 'priority'
    
    def save_model(self, request, obj, form, change):
        """Sauvegarde avec gestion automatique des champs admin."""
        if change:
            # Si le statut change vers approved/rejected, assigner l'admin
            if 'status' in form.changed_data and obj.status in ['approved', 'rejected']:
                if not obj.reviewed_by:
                    obj.reviewed_by = request.user
                if not obj.response_date:
                    obj.response_date = timezone.now()
        
        super().save_model(request, obj, form, change)
    
    def approve_suggestions(self, request, queryset):
        """Action pour approuver plusieurs suggestions."""
        count = 0
        for suggestion in queryset.filter(status='pending'):
            suggestion.status = 'approved'
            suggestion.reviewed_by = request.user
            suggestion.response_date = timezone.now()
            suggestion.admin_response = "Suggestion approuvée en lot"
            suggestion.save()
            count += 1
        
        self.message_user(
            request, 
            f"{count} suggestion(s) approuvée(s) avec succès."
        )
    approve_suggestions.short_description = "Approuver les suggestions sélectionnées"
    
    def reject_suggestions(self, request, queryset):
        """Action pour rejeter plusieurs suggestions."""
        count = 0
        for suggestion in queryset.filter(status='pending'):
            suggestion.status = 'rejected'
            suggestion.reviewed_by = request.user
            suggestion.response_date = timezone.now()
            suggestion.admin_response = "Suggestion rejetée en lot"
            suggestion.save()
            count += 1
        
        self.message_user(
            request, 
            f"{count} suggestion(s) rejetée(s) avec succès."
        )
    reject_suggestions.short_description = "Rejeter les suggestions sélectionnées"
    
    def mark_as_implemented(self, request, queryset):
        """Action pour marquer comme implémentées."""
        count = queryset.filter(status='approved').update(
            status='implemented'
        )
        
        self.message_user(
            request, 
            f"{count} suggestion(s) marquée(s) comme implémentée(s)."
        )
    mark_as_implemented.short_description = "Marquer comme implémentées"


@admin.register(ProductSuggestionComment)
class ProductSuggestionCommentAdmin(admin.ModelAdmin):
    """Administration des commentaires sur les suggestions."""
    
    list_display = [
        'suggestion_name', 'user_email', 'is_admin_comment', 
        'content_preview', 'created_at'
    ]
    list_filter = ['is_admin_comment', 'created_at']
    search_fields = [
        'suggestion__name', 'user__email', 'content'
    ]
    readonly_fields = ['created_at', 'updated_at']
    
    def get_queryset(self, request):
        """Optimise les requêtes."""
        return super().get_queryset(request).select_related(
            'suggestion', 'user'
        )
    
    def suggestion_name(self, obj):
        """Affiche le nom de la suggestion."""
        return obj.suggestion.name
    suggestion_name.short_description = 'Suggestion'
    suggestion_name.admin_order_field = 'suggestion__name'
    
    def user_email(self, obj):
        """Affiche l'email de l'utilisateur."""
        return obj.user.email
    user_email.short_description = 'Utilisateur'
    user_email.admin_order_field = 'user__email'
    
    def content_preview(self, obj):
        """Affiche un aperçu du contenu."""
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    content_preview.short_description = 'Contenu'


@admin.register(ProductSuggestionVote)
class ProductSuggestionVoteAdmin(admin.ModelAdmin):
    """Administration des votes sur les suggestions."""
    
    list_display = [
        'suggestion_name', 'user_email', 'created_at'
    ]
    list_filter = ['created_at']
    search_fields = [
        'suggestion__name', 'user__email'
    ]
    readonly_fields = ['created_at']
    
    def get_queryset(self, request):
        """Optimise les requêtes."""
        return super().get_queryset(request).select_related(
            'suggestion', 'user'
        )
    
    def suggestion_name(self, obj):
        """Affiche le nom de la suggestion."""
        return obj.suggestion.name
    suggestion_name.short_description = 'Suggestion'
    suggestion_name.admin_order_field = 'suggestion__name'
    
    def user_email(self, obj):
        """Affiche l'email de l'utilisateur."""
        return obj.user.email
    user_email.short_description = 'Utilisateur'
    user_email.admin_order_field = 'user__email'


@admin.register(ProductSuggestionAttachment)
class ProductSuggestionAttachmentAdmin(admin.ModelAdmin):
    """Administration des pièces jointes des suggestions."""
    
    list_display = [
        'filename', 'suggestion_name', 'uploaded_by_email', 
        'file_size_mb', 'content_type', 'created_at'
    ]
    list_filter = ['content_type', 'created_at']
    search_fields = [
        'filename', 'suggestion__name', 'uploaded_by__email'
    ]
    readonly_fields = ['file_size', 'content_type', 'created_at']
    
    def get_queryset(self, request):
        """Optimise les requêtes."""
        return super().get_queryset(request).select_related(
            'suggestion', 'uploaded_by'
        )
    
    def suggestion_name(self, obj):
        """Affiche le nom de la suggestion."""
        return obj.suggestion.name
    suggestion_name.short_description = 'Suggestion'
    suggestion_name.admin_order_field = 'suggestion__name'
    
    def uploaded_by_email(self, obj):
        """Affiche l'email de l'utilisateur qui a téléchargé."""
        return obj.uploaded_by.email
    uploaded_by_email.short_description = 'Téléchargé par'
    uploaded_by_email.admin_order_field = 'uploaded_by__email'
