import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Loader2, Smartphone, Laptop, ExternalLink } from "lucide-react";
import { Page, BlogPost } from "@/services/cmsApi";

interface PreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  content: Page | BlogPost | null;
  contentType: "page" | "blog";
}

const PreviewDialog = ({
  open,
  onOpenChange,
  content,
  contentType,
}: PreviewDialogProps) => {
  const [activeDevice, setActiveDevice] = useState("desktop");
  const [isLoading, setIsLoading] = useState(true);

  // Simuler un temps de chargement pour la prévisualisation
  useEffect(() => {
    if (open) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [open, activeDevice]);

  // Générer l'URL de prévisualisation
  const getPreviewUrl = () => {
    if (!content) return "";
    
    if (contentType === "page") {
      return `/${(content as Page).slug}`;
    } else {
      return `/blog/${(content as BlogPost).slug}`;
    }
  };

  // Ouvrir la prévisualisation dans un nouvel onglet
  const openPreviewInNewTab = () => {
    const url = getPreviewUrl();
    if (url) {
      window.open(`${window.location.origin}${url}?preview=true`, "_blank");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Prévisualisation</DialogTitle>
          <DialogDescription>
            Prévisualisez votre contenu avant publication
          </DialogDescription>
        </DialogHeader>

        <Tabs
          defaultValue="desktop"
          value={activeDevice}
          onValueChange={setActiveDevice}
          className="w-full flex-1 flex flex-col"
        >
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="mobile" className="flex items-center gap-1">
                <Smartphone className="h-4 w-4" />
                <span className="hidden sm:inline">Mobile</span>
              </TabsTrigger>
              <TabsTrigger value="desktop" className="flex items-center gap-1">
                <Laptop className="h-4 w-4" />
                <span className="hidden sm:inline">Desktop</span>
              </TabsTrigger>
            </TabsList>

            <Button
              variant="outline"
              size="sm"
              onClick={openPreviewInNewTab}
              className="flex items-center gap-1"
            >
              <ExternalLink className="h-4 w-4" />
              <span className="hidden sm:inline">Ouvrir dans un nouvel onglet</span>
            </Button>
          </div>

          <div className="flex-1 overflow-hidden border rounded-md">
            <TabsContent
              value="mobile"
              className="h-full flex items-center justify-center"
            >
              {isLoading ? (
                <div className="flex flex-col items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                  <p>Chargement de la prévisualisation...</p>
                </div>
              ) : (
                <div className="border-8 border-gray-800 rounded-[32px] h-[600px] w-[320px] overflow-hidden shadow-xl">
                  <div className="w-full h-full overflow-auto bg-white">
                    <iframe
                      src={`${getPreviewUrl()}?preview=true`}
                      className="w-full h-full border-0"
                      title="Mobile Preview"
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent
              value="desktop"
              className="h-full flex items-center justify-center"
            >
              {isLoading ? (
                <div className="flex flex-col items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                  <p>Chargement de la prévisualisation...</p>
                </div>
              ) : (
                <div className="border-8 border-t-[24px] border-gray-800 rounded-[8px] h-[600px] w-full overflow-hidden shadow-xl">
                  <div className="w-full h-full overflow-auto bg-white">
                    <iframe
                      src={`${getPreviewUrl()}?preview=true`}
                      className="w-full h-full border-0"
                      title="Desktop Preview"
                    />
                  </div>
                </div>
              )}
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Fermer</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PreviewDialog;
