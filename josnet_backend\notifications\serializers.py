from rest_framework import serializers
from .models import Notification, NotificationPreference


class NotificationSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour le modèle Notification.
    """
    class Meta:
        model = Notification
        fields = [
            'id', 'user_id', 'title', 'message', 'type', 
            'is_read', 'link', 'created_at', 'expires_at', 'related_id'
        ]
        read_only_fields = ['id', 'created_at']


class NotificationPreferenceSerializer(serializers.ModelSerializer):
    """
    Sérialiseur pour le modèle NotificationPreference.
    """
    class Meta:
        model = NotificationPreference
        fields = [
            'id', 'user_id', 'email_promotions', 'push_promotions',
            'email_orders', 'push_orders', 'email_system', 'push_system'
        ]
        read_only_fields = ['id', 'user_id']
