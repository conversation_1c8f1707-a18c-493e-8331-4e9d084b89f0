from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Message, Conversation
from notifications.models import Notification


@receiver(post_save, sender=Message)
def update_conversation_on_message_save(sender, instance, created, **kwargs):
    """
    Update conversation's last_message_at and read status when a message is saved.
    """
    import logging
    logger = logging.getLogger(__name__)

    if created:
        try:
            conversation = instance.conversation
            logger.info(f"Updating conversation {conversation.id} after new message {instance.id}")

            # Update conversation's last_message_at
            conversation.last_message_at = instance.created_at

            # Update read status based on sender
            if instance.is_from_admin:
                conversation.is_read_by_admin = True
                conversation.is_read_by_customer = False
                conversation.status = 'responded'  # Mettre à jour le statut à 'responded'
                logger.info(f"Message is from admin, marking as read by admin, unread by customer, and status as 'responded'")

                # Créer une notification pour le client
                try:
                    Notification.objects.create(
                        user=conversation.customer,
                        title=f"Nouvelle réponse de l'administrateur",
                        message=f"Vous avez reçu une nouvelle réponse dans votre conversation : '{conversation.subject}'.",
                        type='message',
                        related_id=conversation.id,
                        link=f'/messaging/conversation/{conversation.id}'
                    )
                    logger.info(f"Notification created for customer {conversation.customer.id} for conversation {conversation.id}")
                except Exception as e:
                    logger.error(f"Error creating notification for customer: {e}", exc_info=True)
            else:
                conversation.is_read_by_admin = False
                conversation.is_read_by_customer = True
                logger.info(f"Message is from customer, marking as unread by admin and read by customer")

            # If conversation is closed and a new message is added, reopen it
            if conversation.status == 'closed':
                logger.info(f"Reopening closed conversation {conversation.id}")
                conversation.status = 'in_progress'

            conversation.save(update_fields=[
                'last_message_at',
                'is_read_by_admin',
                'is_read_by_customer',
                'status'
            ])
            logger.info(f"Conversation {conversation.id} updated successfully")
        except Exception as e:
            logger.error(f"Error updating conversation after message save: {e}", exc_info=True)
            # Don't re-raise the exception to prevent the transaction from failing
