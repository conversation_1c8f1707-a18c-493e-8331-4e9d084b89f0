"""
Service pour l'envoi de newsletters et emails promotionnels.
"""

import logging
from typing import List, Dict, Any, Optional
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from ..models import NewsletterSubscription, NewsletterCampaign

logger = logging.getLogger(__name__)


class NewsletterService:
    """Service pour gérer l'envoi de newsletters."""
    
    @staticmethod
    def get_active_subscribers(preferences: List[str] = None) -> List[NewsletterSubscription]:
        """
        Récupère les abonnés actifs selon les préférences.
        
        Args:
            preferences: Liste des préférences à cibler (ex: ['promotions', 'new_products'])
        
        Returns:
            Liste des abonnements actifs
        """
        queryset = NewsletterSubscription.objects.filter(is_active=True)
        
        if preferences:
            # Construire les filtres dynamiquement
            filters = {}
            for pref in preferences:
                if pref in ['promotions', 'new_products', 'newsletters', 'order_updates']:
                    filters[f'preferences_{pref}'] = True
            
            if filters:
                # Utiliser OR pour que l'utilisateur reçoive l'email s'il a au moins une préférence activée
                from django.db.models import Q
                q_objects = Q()
                for key, value in filters.items():
                    q_objects |= Q(**{key: value})
                queryset = queryset.filter(q_objects)
        
        return list(queryset.select_related('user'))
    
    @staticmethod
    def create_campaign(
        title: str,
        subject: str,
        content: str,
        campaign_type: str,
        target_preferences: List[str] = None,
        target_all: bool = False,
        created_by=None
    ) -> NewsletterCampaign:
        """
        Crée une nouvelle campagne de newsletter.
        
        Args:
            title: Titre de la campagne
            subject: Sujet de l'email
            content: Contenu HTML de l'email
            campaign_type: Type de campagne ('promotion', 'newsletter', etc.)
            target_preferences: Préférences ciblées
            target_all: Envoyer à tous les abonnés actifs
            created_by: Utilisateur qui crée la campagne
        
        Returns:
            Instance de NewsletterCampaign
        """
        campaign = NewsletterCampaign.objects.create(
            title=title,
            subject=subject,
            content=content,
            campaign_type=campaign_type,
            target_preferences=target_preferences or [],
            target_all=target_all,
            created_by=created_by,
            status='draft'
        )
        
        return campaign
    
    @staticmethod
    def send_campaign(campaign: NewsletterCampaign) -> Dict[str, Any]:
        """
        Envoie une campagne de newsletter.
        
        Args:
            campaign: Instance de NewsletterCampaign
        
        Returns:
            Dictionnaire avec les résultats de l'envoi
        """
        try:
            with transaction.atomic():
                # Marquer la campagne comme en cours d'envoi
                campaign.status = 'sending'
                campaign.save()
                
                # Récupérer les destinataires
                if campaign.target_all:
                    subscribers = NewsletterService.get_active_subscribers()
                else:
                    subscribers = NewsletterService.get_active_subscribers(campaign.target_preferences)
                
                campaign.total_recipients = len(subscribers)
                campaign.save()
                
                # Envoyer les emails
                sent_count = 0
                failed_count = 0
                
                for subscriber in subscribers:
                    try:
                        success = NewsletterService.send_email_to_subscriber(
                            subscriber=subscriber,
                            subject=campaign.subject,
                            content=campaign.content,
                            campaign_type=campaign.campaign_type
                        )
                        
                        if success:
                            sent_count += 1
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        logger.error(f"Erreur envoi email à {subscriber.email}: {str(e)}")
                        failed_count += 1
                
                # Mettre à jour les statistiques
                campaign.sent_count = sent_count
                campaign.failed_count = failed_count
                campaign.sent_at = timezone.now()
                campaign.status = 'sent' if failed_count == 0 else 'failed'
                campaign.save()
                
                logger.info(f"Campagne {campaign.id} envoyée: {sent_count} succès, {failed_count} échecs")
                
                return {
                    'success': True,
                    'sent_count': sent_count,
                    'failed_count': failed_count,
                    'total_recipients': campaign.total_recipients
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de la campagne {campaign.id}: {str(e)}")
            campaign.status = 'failed'
            campaign.save()
            
            return {
                'success': False,
                'error': str(e),
                'sent_count': 0,
                'failed_count': campaign.total_recipients
            }
    
    @staticmethod
    def send_email_to_subscriber(
        subscriber: NewsletterSubscription,
        subject: str,
        content: str,
        campaign_type: str = 'newsletter'
    ) -> bool:
        """
        Envoie un email à un abonné spécifique.
        
        Args:
            subscriber: Instance de NewsletterSubscription
            subject: Sujet de l'email
            content: Contenu HTML
            campaign_type: Type de campagne
        
        Returns:
            True si l'envoi a réussi, False sinon
        """
        try:
            # Préparer le contexte pour le template
            context = {
                'subscriber': subscriber,
                'user': subscriber.user,
                'content': content,
                'unsubscribe_url': f"{settings.FRONTEND_URL}/newsletter/unsubscribe?email={subscriber.email}&token={subscriber.unsubscribe_token}",
                'site_name': getattr(settings, 'SITE_NAME', 'JosNet'),
                'site_url': settings.FRONTEND_URL,
                'campaign_type': campaign_type,
            }
            
            # Rendre le template HTML
            html_content = render_to_string('emails/newsletter_template.html', context)
            text_content = render_to_string('emails/newsletter_template.txt', context)
            
            # Créer l'email
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[subscriber.email]
            )
            email.attach_alternative(html_content, "text/html")
            
            # Envoyer l'email
            email.send()
            
            logger.info(f"Email envoyé avec succès à {subscriber.email}")
            return True
            
        except Exception as e:
            logger.error(f"Erreur envoi email à {subscriber.email}: {str(e)}")
            return False
    
    @staticmethod
    def send_promotion_notification(
        title: str,
        description: str,
        promotion_url: str = None,
        created_by=None
    ) -> Dict[str, Any]:
        """
        Envoie une notification de promotion à tous les abonnés intéressés.
        
        Args:
            title: Titre de la promotion
            description: Description de la promotion
            promotion_url: URL vers la promotion
            created_by: Utilisateur qui crée la promotion
        
        Returns:
            Résultats de l'envoi
        """
        # Créer le contenu HTML de l'email
        content = f"""
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">🎉 Nouvelle Promotion !</h1>
            </div>
            
            <div style="padding: 30px; background: #f8f9fa;">
                <h2 style="color: #333; margin-bottom: 20px;">{title}</h2>
                <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
                    {description}
                </p>
                
                {f'<div style="text-align: center; margin: 30px 0;"><a href="{promotion_url}" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Voir la promotion</a></div>' if promotion_url else ''}
                
                <p style="color: #999; font-size: 14px; margin-top: 30px;">
                    Ne manquez pas cette offre exceptionnelle ! Connectez-vous à votre compte pour en profiter.
                </p>
            </div>
        </div>
        """
        
        # Créer et envoyer la campagne
        campaign = NewsletterService.create_campaign(
            title=f"Promotion: {title}",
            subject=f"🎉 {title} - Offre spéciale JosNet",
            content=content,
            campaign_type='promotion',
            target_preferences=['promotions'],
            created_by=created_by
        )
        
        return NewsletterService.send_campaign(campaign)
    
    @staticmethod
    def send_new_product_notification(
        product_name: str,
        product_description: str,
        product_url: str = None,
        product_image: str = None,
        created_by=None
    ) -> Dict[str, Any]:
        """
        Envoie une notification de nouveau produit.
        
        Args:
            product_name: Nom du produit
            product_description: Description du produit
            product_url: URL vers le produit
            product_image: URL de l'image du produit
            created_by: Utilisateur qui ajoute le produit
        
        Returns:
            Résultats de l'envoi
        """
        # Créer le contenu HTML
        image_html = f'<img src="{product_image}" alt="{product_name}" style="max-width: 100%; height: auto; border-radius: 8px; margin: 20px 0;">' if product_image else ''
        
        content = f"""
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">✨ Nouveau Produit !</h1>
            </div>
            
            <div style="padding: 30px; background: #f8f9fa;">
                <h2 style="color: #333; margin-bottom: 20px;">{product_name}</h2>
                {image_html}
                <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
                    {product_description}
                </p>
                
                {f'<div style="text-align: center; margin: 30px 0;"><a href="{product_url}" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Découvrir le produit</a></div>' if product_url else ''}
                
                <p style="color: #999; font-size: 14px; margin-top: 30px;">
                    Soyez parmi les premiers à découvrir nos nouveautés !
                </p>
            </div>
        </div>
        """
        
        # Créer et envoyer la campagne
        campaign = NewsletterService.create_campaign(
            title=f"Nouveau produit: {product_name}",
            subject=f"✨ Découvrez {product_name} - Nouveau sur JosNet",
            content=content,
            campaign_type='new_product',
            target_preferences=['new_products'],
            created_by=created_by
        )
        
        return NewsletterService.send_campaign(campaign)
