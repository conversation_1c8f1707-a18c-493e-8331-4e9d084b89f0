import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  TrendingUp,
  ShoppingCart,
  Truck,
  CheckCircle,
  X,
  RefreshCcw,
  CreditCard,
  Loader2
} from "lucide-react";
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from 'recharts';
import orderApi, { Order, OrderDashboardData } from '../../services/orderApi';
import { formatDate, formatCurrency } from '../../utils/formatters';

const OrderDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<OrderDashboardData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const statusColors: Record<string, string> = {
    pending: '#ff9800',
    processing: '#2196f3',
    shipped: '#3f51b5',
    delivered: '#4caf50',
    cancelled: '#f44336',
    refunded: '#9c27b0',
    partially_refunded: '#9c27b0',
    on_hold: '#757575',
  };

  const paymentMethodColors: Record<string, string> = {
    credit_card: '#2196f3',
    paypal: '#3f51b5',
    bank_transfer: '#4caf50',
    cash_on_delivery: '#ff9800',
    other: '#757575',
  };

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        const data = await orderApi.getDashboardData();
        setDashboardData(data);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert>
        <AlertTitle>Warning</AlertTitle>
        <AlertDescription>No dashboard data available</AlertDescription>
      </Alert>
    );
  }

  // Prepare data for charts
  const statusData = Object.entries(dashboardData.status_counts).map(([status, count]) => ({
    name: status,
    value: count
  }));

  const paymentMethodData = Object.entries(dashboardData.payment_method_counts).map(([method, count]) => ({
    name: method,
    value: count
  }));

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold tracking-tight">Order Dashboard</h2>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Summary Cards */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.total_orders}</div>
            <p className="text-xs text-muted-foreground">All orders</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(dashboardData.total_revenue)}</div>
            <p className="text-xs text-muted-foreground">All time revenue</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.status_counts.pending || 0}</div>
            <p className="text-xs text-muted-foreground">Awaiting processing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered Orders</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.status_counts.delivered || 0}</div>
            <p className="text-xs text-muted-foreground">Successfully delivered</p>
          </CardContent>
        </Card>

        {/* Order Status Chart */}
        <div className="col-span-2 lg:col-span-1">
          <Card className="h-[400px]">
            <CardHeader>
              <CardTitle>Orders by Status</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={statusColors[entry.name] || '#757575'} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} orders`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Payment Method Chart */}
        <div className="col-span-2 lg:col-span-1">
          <Card className="h-[400px]">
            <CardHeader>
              <CardTitle>Orders by Payment Method</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={paymentMethodData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {paymentMethodData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={paymentMethodColors[entry.name] || '#757575'} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} orders`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Recent Orders */}
        <div className="col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order #</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dashboardData.recent_orders.map((order: Order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <Link to={`/admin/orders/${order.id}`} className="text-primary hover:underline">
                          {order.order_number}
                        </Link>
                      </TableCell>
                      <TableCell>{formatDate(order.created_at)}</TableCell>
                      <TableCell>
                        <div>
                          {order.customer_name || `${order.billing_first_name} ${order.billing_last_name}`}
                          <p className="text-xs text-muted-foreground">{order.email}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className="capitalize"
                          style={{
                            backgroundColor: statusColors[order.status || 'pending'],
                            color: 'white'
                          }}
                        >
                          {order.status_display || order.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatCurrency(order.total)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        {/* Order Status Summary */}
        <div className="col-span-2 lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Order Status Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <ShoppingCart style={{ color: statusColors.pending }} className="h-4 w-4" />
                  <span>Pending: {dashboardData.status_counts.pending || 0}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CreditCard style={{ color: statusColors.processing }} className="h-4 w-4" />
                  <span>Processing: {dashboardData.status_counts.processing || 0}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Truck style={{ color: statusColors.shipped }} className="h-4 w-4" />
                  <span>Shipped: {dashboardData.status_counts.shipped || 0}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle style={{ color: statusColors.delivered }} className="h-4 w-4" />
                  <span>Delivered: {dashboardData.status_counts.delivered || 0}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <X style={{ color: statusColors.cancelled }} className="h-4 w-4" />
                  <span>Cancelled: {dashboardData.status_counts.cancelled || 0}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <RefreshCcw style={{ color: statusColors.refunded }} className="h-4 w-4" />
                  <span>Refunded: {dashboardData.status_counts.refunded || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Method Summary */}
        <div className="col-span-2 lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Payment Method Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(dashboardData.payment_method_counts).map(([method, count]) => (
                  <div key={method} className="flex items-center space-x-2">
                    <CreditCard style={{ color: paymentMethodColors[method] }} className="h-4 w-4" />
                    <span className="capitalize">{method.replace('_', ' ')}: {count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OrderDashboard;
