#!/usr/bin/env python
"""
Test rapide pour vérifier que l'API d'administration fonctionne après correction.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


def quick_test():
    """Test rapide de l'API d'administration."""
    print("🚀 TEST RAPIDE DE L'API D'ADMINISTRATION")
    print("=" * 60)
    
    # Créer un utilisateur admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Quick',
            'last_name': 'Admin',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'role': 'admin'  # IMPORTANT: Dé<PERSON>ir le rôle admin
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
    else:
        # Mettre à jour le rôle si l'utilisateur existe déjà
        admin_user.role = 'admin'
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.save()
    
    # Créer un utilisateur de test
    test_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Quick',
            'last_name': 'User',
            'is_active': True
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
    
    print(f"✅ Admin: {admin_user.email}")
    print(f"✅ Utilisateur: {test_user.email} (ID: {test_user.id})")
    
    # Créer un token JWT
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    
    # Configurer le client API
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"\n🔄 Test de l'endpoint set_active")
    response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/set_active/', {
        'is_active': False
    }, format='json')
    
    print(f"   URL: /api/v1/auth/admin/users/{test_user.id}/set_active/")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ SUCCÈS ! Utilisateur désactivé")
        print(f"   📊 Actif: {data.get('is_active')}")
        
        # Réactiver
        response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/set_active/', {
            'is_active': True
        }, format='json')
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCÈS ! Utilisateur réactivé")
            print(f"   📊 Actif: {data.get('is_active')}")
            return True
        else:
            print(f"   ❌ Erreur réactivation: {response.status_code}")
            return False
            
    elif response.status_code == 404:
        print(f"   ❌ ERREUR 404: Endpoint non trouvé")
        print(f"   🔍 L'URL n'existe pas ou le serveur Django n'est pas démarré")
        return False
    else:
        print(f"   ❌ ERREUR {response.status_code}: {response.content.decode()}")
        return False


def main():
    """Fonction principale."""
    try:
        success = quick_test()
        
        print(f"\n" + "=" * 60)
        print("📊 RÉSULTAT")
        print("=" * 60)
        
        if success:
            print("🎉 ✅ L'API D'ADMINISTRATION FONCTIONNE PARFAITEMENT !")
            print("✅ Le problème 404 est résolu")
            print("✅ L'endpoint set_active fonctionne")
            print()
            print("🔧 SOLUTION APPLIQUÉE:")
            print("   - ALLOWED_HOSTS corrigé dans settings.py")
            print("   - Ajout de 'localhost', '127.0.0.1', 'testserver', '*'")
            print()
            print("🚀 PROCHAINES ÉTAPES:")
            print("   1. Redémarrez le serveur Django si nécessaire")
            print("   2. Testez depuis le frontend")
            print("   3. L'erreur 404 devrait être résolue")
        else:
            print("❌ IL Y A ENCORE UN PROBLÈME")
            print("🔍 Vérifiez que le serveur Django fonctionne")
            
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
