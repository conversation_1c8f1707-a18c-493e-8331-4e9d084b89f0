
import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { products } from "@/utils/data";
import { Star, ShoppingCart, Truck, ShieldCheck, ArrowLeft } from "lucide-react";

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [quantity, setQuantity] = useState(1);
  
  // Find the product based on the ID from the URL
  const product = products.find(p => p.id === Number(id));
  
  // If product doesn't exist, show a message
  if (!product) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-12 flex flex-col items-center justify-center">
          <h2 className="text-2xl font-bold mb-4">Produit non trouvé</h2>
          <p className="text-gray-600 mb-6">Le produit que vous recherchez n'existe pas ou a été retiré.</p>
          <Button asChild>
            <Link to="/products">Retourner au catalogue</Link>
          </Button>
        </main>
        <Footer />
      </div>
    );
  }

  // Handle quantity change
  const increaseQuantity = () => setQuantity(prev => prev + 1);
  const decreaseQuantity = () => setQuantity(prev => (prev > 1 ? prev - 1 : 1));
  
  // Handle add to cart
  const handleAddToCart = () => {
    alert(`Ajouté au panier: ${quantity} x ${product.name}`);
    // This would be connected to a cart state/context in a real implementation
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link to="/products" className="text-gray-600 hover:text-primary flex items-center">
            <ArrowLeft size={16} className="mr-2" />
            Retour aux produits
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Image */}
          <div className="bg-white rounded-lg overflow-hidden">
            <img 
              src={product.image} 
              alt={product.name} 
              className="w-full h-auto object-cover"
              style={{ maxHeight: '500px' }}
            />
          </div>
          
          {/* Product Info */}
          <div>
            <div className="flex items-center">
              <span className="bg-accent text-white px-3 py-1 rounded text-sm font-medium">{product.category}</span>
              {product.featured && (
                <span className="ml-2 bg-primary-dark text-white px-3 py-1 rounded text-sm font-medium">
                  Populaire
                </span>
              )}
            </div>
            
            <h1 className="text-3xl font-bold mt-4">{product.name}</h1>
            
            <div className="flex items-center mt-2 mb-4">
              {/* Star rating - this would be dynamic in a real app */}
              <div className="flex mr-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star 
                    key={star} 
                    size={16} 
                    className={star <= 4 ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
                  />
                ))}
              </div>
              <span className="text-gray-600">(24 avis)</span>
            </div>
            
            <p className="text-2xl font-bold text-primary-dark mb-4">
              {product.price.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}
            </p>
            
            <p className="text-gray-700 mb-6">{product.description}</p>
            
            {/* Stock info */}
            <div className="mb-6">
              <span className={`font-medium ${product.inStock ? 'text-green-500' : 'text-red-500'}`}>
                {product.inStock ? 'En stock' : 'Indisponible'}
              </span>
            </div>
            
            {/* Quantity selector */}
            {product.inStock && (
              <div className="flex items-center mb-6">
                <span className="mr-4">Quantité:</span>
                <div className="flex items-center border border-gray-300 rounded">
                  <button 
                    onClick={decreaseQuantity}
                    className="px-3 py-1 border-r border-gray-300"
                    disabled={quantity <= 1}
                  >
                    -
                  </button>
                  <span className="px-4 py-1">{quantity}</span>
                  <button 
                    onClick={increaseQuantity}
                    className="px-3 py-1 border-l border-gray-300"
                  >
                    +
                  </button>
                </div>
              </div>
            )}
            
            {/* Add to cart button */}
            <Button
              onClick={handleAddToCart}
              disabled={!product.inStock}
              className="w-full py-6 text-lg font-medium flex items-center justify-center"
            >
              <ShoppingCart className="mr-2" />
              Ajouter au panier
            </Button>
            
            {/* Delivery options */}
            <div className="mt-6 space-y-4">
              <div className="flex items-start">
                <Truck className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium">Livraison gratuite</h4>
                  <p className="text-sm text-gray-600">Pour les commandes supérieures à 50€</p>
                </div>
              </div>
              <div className="flex items-start">
                <ShieldCheck className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium">Garantie 1 an</h4>
                  <p className="text-sm text-gray-600">Retour et échange faciles</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Product details tabs */}
        <div className="mt-12">
          <Tabs defaultValue="description">
            <TabsList className="w-full border-b grid grid-cols-3 mb-8">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="specs">Caractéristiques</TabsTrigger>
              <TabsTrigger value="reviews">Avis clients</TabsTrigger>
            </TabsList>
            
            <TabsContent value="description" className="px-1">
              <h3 className="text-xl font-bold mb-4">Description du produit</h3>
              <p className="text-gray-700 mb-4">{product.description}</p>
              <p className="text-gray-700">
                Le {product.name} est conçu pour répondre à vos besoins technologiques avec une qualité exceptionnelle.
                Fabriqué avec des matériaux durables et testé rigoureusement, ce produit vous offrira
                des performances fiables pendant des années.
              </p>
            </TabsContent>
            
            <TabsContent value="specs" className="px-1">
              <h3 className="text-xl font-bold mb-4">Caractéristiques techniques</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-medium mb-2">Spécifications générales</h4>
                  <table className="w-full">
                    <tbody>
                      <tr className="border-b border-gray-200">
                        <td className="py-2 text-gray-600">Marque</td>
                        <td className="py-2 font-medium">JOSNET NETWORK</td>
                      </tr>
                      <tr className="border-b border-gray-200">
                        <td className="py-2 text-gray-600">Modèle</td>
                        <td className="py-2 font-medium">{product.name}</td>
                      </tr>
                      <tr className="border-b border-gray-200">
                        <td className="py-2 text-gray-600">Catégorie</td>
                        <td className="py-2 font-medium">{product.category}</td>
                      </tr>
                      <tr>
                        <td className="py-2 text-gray-600">Garantie</td>
                        <td className="py-2 font-medium">1 an</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-medium mb-2">Caractéristiques techniques</h4>
                  <p className="text-gray-600">
                    Ces informations varient selon le produit spécifique. Veuillez contacter
                    notre service client pour des détails supplémentaires.
                  </p>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="reviews" className="px-1">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold">Avis clients</h3>
                <Button>Écrire un avis</Button>
              </div>
              
              <div className="space-y-6">
                {/* Example reviews - would be dynamic in a real app */}
                <div className="border-b pb-6">
                  <div className="flex items-center mb-2">
                    <div className="flex mr-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star 
                          key={star} 
                          size={14} 
                          className={star <= 5 ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
                        />
                      ))}
                    </div>
                    <span className="font-medium">Excellent produit</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">Par Jean D. - 15 avril 2025</p>
                  <p className="text-gray-700">
                    J'ai acheté ce produit il y a un mois et j'en suis très satisfait.
                    La qualité est excellente et il répond parfaitement à mes besoins.
                  </p>
                </div>
                
                <div className="border-b pb-6">
                  <div className="flex items-center mb-2">
                    <div className="flex mr-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star 
                          key={star} 
                          size={14} 
                          className={star <= 4 ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
                        />
                      ))}
                    </div>
                    <span className="font-medium">Bon rapport qualité-prix</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">Par Marie L. - 2 avril 2025</p>
                  <p className="text-gray-700">
                    Ce produit offre un excellent rapport qualité-prix. Je l'utilise quotidiennement
                    depuis plusieurs semaines et il fonctionne très bien.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ProductDetail;
