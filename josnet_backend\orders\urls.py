from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import OrderViewSet, OrderItemViewSet, OrderRefundViewSet, ShippingMethodViewSet
from .views_extension import ReturnRequestViewSet, ClaimViewSet

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'orders', OrderViewSet)
router.register(r'order-items', OrderItemViewSet)
router.register(r'refunds', OrderRefundViewSet)
router.register(r'shipping-methods', ShippingMethodViewSet)
router.register(r'returns', ReturnRequestViewSet)
router.register(r'claims', ClaimViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
