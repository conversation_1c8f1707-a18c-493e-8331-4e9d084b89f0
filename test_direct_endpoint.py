#!/usr/bin/env python3
"""
Test direct de l'endpoint problématique
"""

import requests
import time

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"
RETURN_ID = "2c4ca05e-ae4d-4e14-b3be-ea59df140f17"

def test_direct_endpoint():
    """Test direct de l'endpoint problématique"""
    print("🔍 TEST DIRECT DE L'ENDPOINT PROBLÉMATIQUE")
    print("=" * 60)
    
    # Attendre que le serveur soit prêt
    print("⏳ Attente du démarrage du serveur...")
    time.sleep(3)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        print("🔐 Authentification...")
        login_response = requests.get(f"{API_BASE_URL}/auth/login/", timeout=5)
        print(f"   Test de connexion serveur: {login_response.status_code}")
        
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data, timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            print("✅ Authentification réussie")
            
            # Test de l'endpoint problématique
            problematic_url = f"{API_BASE_URL}/account/returns/{RETURN_ID}/"
            print(f"\n🎯 Test de l'URL problématique:")
            print(f"   URL: {problematic_url}")
            
            try:
                response = requests.get(problematic_url, headers=headers, timeout=10)
                print(f"   📊 Statut: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ SUCCÈS!")
                    print(f"   📝 Numéro retour: {data.get('return_number', 'N/A')}")
                    print(f"   👤 Utilisateur: {data.get('user', {}).get('email', 'N/A')}")
                    return True
                elif response.status_code == 404:
                    print(f"   ❌ ERREUR 404 - Endpoint non trouvé")
                    print(f"   📝 Réponse: {response.text[:200]}...")
                    return False
                else:
                    print(f"   ⚠️ Autre erreur: {response.status_code}")
                    print(f"   📝 Réponse: {response.text[:200]}...")
                    return False
                    
            except requests.exceptions.Timeout:
                print(f"   ⏰ TIMEOUT - Le serveur ne répond pas")
                return False
            except requests.exceptions.ConnectionError:
                print(f"   🔌 ERREUR DE CONNEXION - Le serveur n'est pas accessible")
                return False
                
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ SERVEUR NON ACCESSIBLE")
        print(f"   Le serveur Django n'est probablement pas démarré")
        print(f"   Vérifiez que le serveur tourne sur http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_alternative_endpoints():
    """Tester des endpoints alternatifs"""
    print(f"\n🔄 TEST D'ENDPOINTS ALTERNATIFS")
    print("=" * 50)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data, timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Tester différentes variantes de l'URL
            urls_to_test = [
                f"{API_BASE_URL}/orders/returns/{RETURN_ID}/",  # API admin
                f"{API_BASE_URL}/account/returns/",  # Liste account
                f"{API_BASE_URL}/auth/account/returns/{RETURN_ID}/",  # Via auth prefix
            ]
            
            for url in urls_to_test:
                try:
                    response = requests.get(url, headers=headers, timeout=5)
                    status = response.status_code
                    icon = "✅" if status == 200 else "❌"
                    print(f"   {icon} {url}: {status}")
                    
                    if status == 200 and 'returns' in url:
                        data = response.json()
                        if isinstance(data, dict) and 'return_number' in data:
                            print(f"      📝 Retour: {data.get('return_number')}")
                        elif isinstance(data, dict) and 'results' in data:
                            print(f"      📊 Résultats: {len(data['results'])}")
                        
                except Exception as e:
                    print(f"   ❌ {url}: Erreur - {e}")
            
            return True
        else:
            print(f"❌ Échec authentification")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def check_server_status():
    """Vérifier le statut du serveur"""
    print(f"\n🏥 VÉRIFICATION DU STATUT DU SERVEUR")
    print("=" * 50)
    
    try:
        # Test de base
        response = requests.get("http://localhost:8000/", timeout=5)
        print(f"   📊 Serveur Django: {response.status_code}")
        
        # Test API de base
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        print(f"   📊 API base: {response.status_code}")
        
        # Test endpoint auth
        response = requests.get(f"{API_BASE_URL}/auth/", timeout=5)
        print(f"   📊 Auth endpoint: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print(f"   ❌ SERVEUR NON ACCESSIBLE")
        print(f"   🔧 Actions à faire:")
        print(f"      1. Vérifiez que le serveur Django tourne")
        print(f"      2. Redémarrez le serveur: python manage.py runserver")
        print(f"      3. Vérifiez les URLs dans josnet_backend/urls.py")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🧪 DIAGNOSTIC DE L'ERREUR 404 PERSISTANTE")
    print("=" * 70)
    
    # 1. Vérifier le serveur
    server_ok = check_server_status()
    
    if server_ok:
        # 2. Tester l'endpoint problématique
        endpoint_ok = test_direct_endpoint()
        
        # 3. Tester des alternatives
        alternatives_ok = test_alternative_endpoints()
        
        print(f"\n📊 DIAGNOSTIC:")
        print(f"   Serveur accessible: {'✅' if server_ok else '❌'}")
        print(f"   Endpoint problématique: {'✅' if endpoint_ok else '❌'}")
        print(f"   Endpoints alternatifs: {'✅' if alternatives_ok else '❌'}")
        
        if endpoint_ok:
            print(f"\n🎉 PROBLÈME RÉSOLU!")
            print(f"   L'endpoint fonctionne maintenant")
            print(f"   Le frontend devrait fonctionner")
        elif alternatives_ok:
            print(f"\n⚠️ PROBLÈME PARTIEL")
            print(f"   Certains endpoints fonctionnent")
            print(f"   Vérifiez la configuration des URLs")
        else:
            print(f"\n❌ PROBLÈME PERSISTANT")
            print(f"   Aucun endpoint ne fonctionne")
            print(f"   Redémarrez le serveur Django")
    else:
        print(f"\n🔧 ACTIONS REQUISES:")
        print(f"   1. Redémarrez le serveur Django")
        print(f"   2. Vérifiez que le port 8000 est libre")
        print(f"   3. Relancez ce test")

if __name__ == "__main__":
    main()
