import axios from 'axios';
import { API_URL } from '@/config';
import { getAuthToken } from '@/utils/auth';

// Types
export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parent?: number;
  parent_name?: string;
  image?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CategoryTree extends Pick<Category, 'id' | 'name' | 'slug'> {
  children: CategoryTree[];
}

// Category API service
export const categoryApi = {
  // Get all categories
  getCategories: async (): Promise<Category[]> => {
    try {
      const response = await axios.get(`${API_URL}/products/categories/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return Array.isArray(response.data) ? response.data : (response.data.results || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      return getMockCategories();
    }
  },

  // Get category by ID
  getCategory: async (id: number): Promise<Category> => {
    try {
      const response = await axios.get(`${API_URL}/products/categories/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching category ${id}:`, error);
      throw error;
    }
  },

  // Get category by slug
  getCategoryBySlug: async (slug: string): Promise<Category> => {
    try {
      const response = await axios.get(`${API_URL}/products/categories/${slug}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching category with slug ${slug}:`, error);
      throw error;
    }
  },

  // Get category tree
  getCategoryTree: async (): Promise<CategoryTree[]> => {
    try {
      const response = await axios.get(`${API_URL}/products/categories/tree/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching category tree:', error);
      return [];
    }
  },

  // Create category
  createCategory: async (data: Partial<Category>): Promise<Category> => {
    try {
      const response = await axios.post(`${API_URL}/products/categories/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  },

  // Update category
  updateCategory: async (id: number, data: Partial<Category>): Promise<Category> => {
    try {
      const response = await axios.patch(`${API_URL}/products/categories/${id}/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating category ${id}:`, error);
      throw error;
    }
  },

  // Delete category
  deleteCategory: async (id: number): Promise<void> => {
    try {
      await axios.delete(`${API_URL}/products/categories/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error deleting category ${id}:`, error);
      throw error;
    }
  }
};

// Mock data for development/testing
const getMockCategories = (): Category[] => {
  return [
    {
      id: 1,
      name: 'Électronique',
      slug: 'electronique',
      description: 'Produits électroniques et gadgets',
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    },
    {
      id: 2,
      name: 'Vêtements',
      slug: 'vetements',
      description: 'Vêtements et accessoires de mode',
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    },
    {
      id: 3,
      name: 'Maison',
      slug: 'maison',
      description: 'Articles pour la maison et le jardin',
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    },
    {
      id: 4,
      name: 'Téléphones',
      slug: 'telephones',
      description: 'Smartphones et accessoires',
      parent: 1,
      parent_name: 'Électronique',
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    },
    {
      id: 5,
      name: 'Ordinateurs',
      slug: 'ordinateurs',
      description: 'Ordinateurs portables et de bureau',
      parent: 1,
      parent_name: 'Électronique',
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    }
  ];
};

// Exporter également comme export par défaut pour la compatibilité
export default { ...categoryApi };
