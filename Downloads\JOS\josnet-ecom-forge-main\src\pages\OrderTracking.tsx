
import { useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Package, Clock, Truck, CheckCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

const OrderTracking = () => {
  const [orderNumber, setOrderNumber] = useState("");
  const [email, setEmail] = useState("");
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [orderFound, setOrderFound] = useState(false);
  
  // Mock order details - in a real app this would be fetched from an API
  const mockOrder = {
    number: "JN-2025-0042",
    date: "12/05/2025",
    status: "En transit",
    items: [
      { name: "Routeur Wi-Fi Pro X500", quantity: 1, price: 89.99 },
      { name: "Smartphone NextGen S22", quantity: 2, price: 1399.98 }
    ],
    total: 1489.97,
    shippingAddress: "123 Rue Example, Bujumbura, Burundi",
    trackingEvents: [
      { date: "15/05/2025 - 08:15", status: "Commande livrée", description: "Votre commande a été livrée" },
      { date: "14/05/2025 - 10:30", status: "En transit", description: "Votre commande est en cours de livraison" },
      { date: "13/05/2025 - 15:45", status: "Expédiée", description: "Votre commande a été expédiée" },
      { date: "12/05/2025 - 14:20", status: "Préparation", description: "Votre commande est en cours de préparation" },
      { date: "12/05/2025 - 09:05", status: "Confirmée", description: "Votre commande a été confirmée" }
    ]
  };

  // Handle order search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchPerformed(true);
    
    // Simple validation
    if (orderNumber === mockOrder.number && email) {
      setOrderFound(true);
    } else {
      setOrderFound(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-4">Suivi de commande</h1>
        <p className="text-gray-600 mb-8">
          Entrez votre numéro de commande et email pour suivre l'état de votre commande.
        </p>
        
        {/* Search Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <form onSubmit={handleSearch} className="space-y-4">
            <div>
              <label htmlFor="orderNumber" className="block text-sm font-medium mb-1">
                Numéro de commande*
              </label>
              <Input
                id="orderNumber"
                placeholder="ex: JN-2025-0042"
                value={orderNumber}
                onChange={(e) => setOrderNumber(e.target.value)}
                required
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">
                Email associé à la commande*
              </label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <Button type="submit">Rechercher ma commande</Button>
          </form>
          
          {/* Error message if not found */}
          {searchPerformed && !orderFound && (
            <Alert className="mt-4 bg-red-50 border-red-200">
              <AlertDescription>
                Aucune commande trouvée avec ces informations. Veuillez vérifier et réessayer.
              </AlertDescription>
            </Alert>
          )}
        </div>
        
        {/* Order details if found */}
        {orderFound && (
          <div className="space-y-8">
            {/* Order Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex flex-col md:flex-row justify-between mb-6">
                <div>
                  <h2 className="text-xl font-medium">Commande #{mockOrder.number}</h2>
                  <p className="text-gray-600">Placée le {mockOrder.date}</p>
                </div>
                <div className="mt-4 md:mt-0 flex items-center">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
                    {mockOrder.status}
                  </span>
                </div>
              </div>
              
              <div className="border-t border-gray-200 pt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium mb-2">Livraison à:</h3>
                  <p className="text-gray-700">{mockOrder.shippingAddress}</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Récapitulatif:</h3>
                  <p className="text-gray-700">
                    {mockOrder.items.length} articles pour {mockOrder.total.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Tracking Timeline */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-medium mb-6">Statut de livraison</h2>
              
              {/* Visual progress */}
              <div className="mb-8">
                <div className="flex justify-between mb-2">
                  <div className="text-center">
                    <CheckCircle className="h-8 w-8 mx-auto text-green-500" />
                    <p className="text-xs mt-1">Confirmée</p>
                  </div>
                  <div className="text-center">
                    <Package className="h-8 w-8 mx-auto text-green-500" />
                    <p className="text-xs mt-1">Préparation</p>
                  </div>
                  <div className="text-center">
                    <Clock className="h-8 w-8 mx-auto text-green-500" />
                    <p className="text-xs mt-1">Expédiée</p>
                  </div>
                  <div className="text-center">
                    <Truck className="h-8 w-8 mx-auto text-blue-500" />
                    <p className="text-xs mt-1">En transit</p>
                  </div>
                  <div className="text-center">
                    <CheckCircle className="h-8 w-8 mx-auto text-gray-300" />
                    <p className="text-xs mt-1">Livrée</p>
                  </div>
                </div>
                <div className="overflow-hidden h-2 mb-4 flex rounded bg-gray-200">
                  <div className="bg-blue-500" style={{ width: "80%" }}></div>
                </div>
              </div>
              
              {/* Timeline */}
              <div className="space-y-6">
                {mockOrder.trackingEvents.map((event, index) => (
                  <div key={index} className="flex">
                    <div className="mr-4 flex flex-col items-center">
                      <div className={`rounded-full h-4 w-4 ${index === 0 ? 'bg-blue-500' : 'bg-green-500'}`}></div>
                      {index < mockOrder.trackingEvents.length - 1 && (
                        <div className="h-full w-0.5 bg-gray-200"></div>
                      )}
                    </div>
                    <div className={`pb-6 ${index === mockOrder.trackingEvents.length - 1 ? '' : ''}`}>
                      <p className="text-sm text-gray-500">{event.date}</p>
                      <h3 className={`font-medium ${index === 0 ? 'text-blue-600' : ''}`}>{event.status}</h3>
                      <p className="text-gray-600">{event.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Order Items */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-medium mb-4">Articles commandés</h2>
              <ul className="divide-y divide-gray-200">
                {mockOrder.items.map((item, index) => (
                  <li key={index} className="py-4 flex justify-between">
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-gray-600">Quantité: {item.quantity}</p>
                    </div>
                    <p className="font-medium">
                      {item.price.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}
                    </p>
                  </li>
                ))}
              </ul>
              <div className="pt-4 mt-4 border-t border-gray-200 flex justify-between">
                <span className="font-medium">Total</span>
                <span className="font-bold">
                  {mockOrder.total.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}
                </span>
              </div>
            </div>
            
            {/* Need help box */}
            <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
              <h3 className="font-medium mb-2">Besoin d'aide avec votre commande?</h3>
              <p className="text-gray-700 mb-4">
                Si vous avez des questions sur votre commande, n'hésitez pas à contacter notre service client.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button variant="outline" asChild>
                  <a href="/contact">Contacter le support</a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/faq">Consulter la FAQ</a>
                </Button>
              </div>
            </div>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default OrderTracking;
