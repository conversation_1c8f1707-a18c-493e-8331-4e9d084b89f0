import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  X,
  File,
  Image as ImageIcon,
  Video,
  Music,
  FileText,
  Archive,
  Download,
  Eye,
  AlertCircle,
  Check,
  Loader2
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FileItem {
  id: string;
  file: File;
  preview?: string;
  uploadProgress: number;
  uploadStatus: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  onFileRemove: (fileId: string) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  className?: string;
  disabled?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFilesSelected,
  onFileRemove,
  maxFiles = 10,
  maxFileSize = 10, // 10MB
  acceptedTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.txt'],
  className,
  disabled = false
}) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get file icon based on type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <ImageIcon className="h-5 w-5 text-blue-500" />;
    if (fileType.startsWith('video/')) return <Video className="h-5 w-5 text-purple-500" />;
    if (fileType.startsWith('audio/')) return <Music className="h-5 w-5 text-green-500" />;
    if (fileType.includes('pdf')) return <FileText className="h-5 w-5 text-red-500" />;
    if (fileType.includes('zip') || fileType.includes('rar')) return <Archive className="h-5 w-5 text-orange-500" />;
    return <File className="h-5 w-5 text-gray-500" />;
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // Validate file
  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `Le fichier est trop volumineux (max: ${maxFileSize}MB)`;
    }

    // Check file type
    const isAccepted = acceptedTypes.some(type => {
      if (type.includes('*')) {
        return file.type.startsWith(type.replace('*', ''));
      }
      return file.type === type || file.name.toLowerCase().endsWith(type);
    });

    if (!isAccepted) {
      return 'Type de fichier non autorisé';
    }

    return null;
  };

  // Create file preview for images
  const createPreview = (file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  };

  // Process files
  const processFiles = useCallback(async (fileList: FileList | File[]) => {
    const newFiles: FileItem[] = [];
    const validFiles: File[] = [];

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList instanceof FileList ? fileList[i] : fileList[i];
      
      // Check if we've reached max files
      if (files.length + newFiles.length >= maxFiles) {
        break;
      }

      const error = validateFile(file);
      const preview = await createPreview(file);
      
      const fileItem: FileItem = {
        id: `${Date.now()}-${i}`,
        file,
        preview,
        uploadProgress: 0,
        uploadStatus: error ? 'error' : 'pending',
        error
      };

      newFiles.push(fileItem);
      
      if (!error) {
        validFiles.push(file);
      }
    }

    setFiles(prev => [...prev, ...newFiles]);
    
    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }
  }, [files.length, maxFiles, onFilesSelected]);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files);
    }
  };

  // Handle drag events
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  }, [disabled, processFiles]);

  // Remove file
  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
    onFileRemove(fileId);
  };

  // Simulate upload progress (replace with actual upload logic)
  const simulateUpload = (fileId: string) => {
    setFiles(prev => prev.map(f => 
      f.id === fileId 
        ? { ...f, uploadStatus: 'uploading' as const }
        : f
    ));

    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 30;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        setFiles(prev => prev.map(f => 
          f.id === fileId 
            ? { ...f, uploadProgress: 100, uploadStatus: 'completed' as const }
            : f
        ));
      } else {
        setFiles(prev => prev.map(f => 
          f.id === fileId 
            ? { ...f, uploadProgress: progress }
            : f
        ));
      }
    }, 200);
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Drop Zone */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          isDragOver 
            ? "border-blue-500 bg-blue-50" 
            : "border-gray-300 hover:border-gray-400",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileInputChange}
          accept={acceptedTypes.join(',')}
          disabled={disabled}
        />

        <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-600 mb-2">
          Glissez-déposez vos fichiers ici ou{' '}
          <Button
            variant="link"
            className="p-0 h-auto text-blue-600"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
          >
            parcourez
          </Button>
        </p>
        <p className="text-xs text-gray-500">
          Max {maxFiles} fichiers, {maxFileSize}MB par fichier
        </p>
        <div className="flex flex-wrap justify-center gap-1 mt-2">
          {acceptedTypes.slice(0, 3).map((type, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {type}
            </Badge>
          ))}
          {acceptedTypes.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{acceptedTypes.length - 3} autres
            </Badge>
          )}
        </div>
      </div>

      {/* File List */}
      <AnimatePresence>
        {files.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2"
          >
            {files.map((fileItem) => (
              <motion.div
                key={fileItem.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={cn(
                  "flex items-center space-x-3 p-3 border rounded-lg",
                  fileItem.uploadStatus === 'error' && "border-red-200 bg-red-50",
                  fileItem.uploadStatus === 'completed' && "border-green-200 bg-green-50"
                )}
              >
                {/* Preview/Icon */}
                <div className="flex-shrink-0">
                  {fileItem.preview ? (
                    <img
                      src={fileItem.preview}
                      alt={fileItem.file.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                  ) : (
                    <div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded">
                      {getFileIcon(fileItem.file.type)}
                    </div>
                  )}
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {fileItem.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(fileItem.file.size)}
                  </p>
                  
                  {/* Progress Bar */}
                  {fileItem.uploadStatus === 'uploading' && (
                    <Progress value={fileItem.uploadProgress} className="mt-1" />
                  )}
                  
                  {/* Error Message */}
                  {fileItem.error && (
                    <p className="text-xs text-red-600 mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {fileItem.error}
                    </p>
                  )}
                </div>

                {/* Status & Actions */}
                <div className="flex items-center space-x-2">
                  {fileItem.uploadStatus === 'pending' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => simulateUpload(fileItem.id)}
                      disabled={!!fileItem.error}
                    >
                      <Upload className="h-3 w-3" />
                    </Button>
                  )}
                  
                  {fileItem.uploadStatus === 'uploading' && (
                    <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                  )}
                  
                  {fileItem.uploadStatus === 'completed' && (
                    <Check className="h-4 w-4 text-green-500" />
                  )}
                  
                  {fileItem.uploadStatus === 'error' && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}

                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeFile(fileItem.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Summary */}
      {files.length > 0 && (
        <div className="text-xs text-gray-500 text-center">
          {files.length} fichier{files.length > 1 ? 's' : ''} sélectionné{files.length > 1 ? 's' : ''} 
          ({files.filter(f => f.uploadStatus === 'completed').length} téléchargé{files.filter(f => f.uploadStatus === 'completed').length > 1 ? 's' : ''})
        </div>
      )}
    </div>
  );
};

export default FileUpload;
