#!/usr/bin/env python3
"""
Test de la correction de la création des retours
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from orders.models import Order, OrderItem
from authentication.models import User

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_return_creation_structure():
    """Tester la structure de création des retours"""
    print("🔧 TEST DE LA STRUCTURE DE CRÉATION DES RETOURS")
    print("=" * 60)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data, timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            print("✅ Authentification réussie")
            
            # Récupérer une commande pour tester
            print(f"\n📦 RÉCUPÉRATION D'UNE COMMANDE POUR TEST:")
            orders_response = requests.get(f"{API_BASE_URL}/orders/", headers=headers, timeout=10)
            
            if orders_response.status_code == 200:
                orders_data = orders_response.json()
                orders = orders_data.get('results', [])
                
                if orders:
                    order = orders[0]
                    order_id = order['id']
                    order_number = order['order_number']
                    
                    print(f"   ✅ Commande trouvée: {order_number} (ID: {order_id})")
                    
                    # Récupérer les détails de la commande pour avoir les articles
                    order_detail_response = requests.get(f"{API_BASE_URL}/orders/{order_id}/", headers=headers, timeout=10)
                    
                    if order_detail_response.status_code == 200:
                        order_detail = order_detail_response.json()
                        items = order_detail.get('items', [])
                        
                        print(f"   📊 Articles dans la commande: {len(items)}")
                        
                        if items:
                            # Préparer les données de retour avec la BONNE structure
                            print(f"\n🔧 PRÉPARATION DES DONNÉES DE RETOUR:")
                            
                            # Structure AVANT correction (incorrecte)
                            old_structure = {
                                "order": order_id,
                                "reason": "defective",
                                "description": "Test de retour",  # ❌ INCORRECT
                                "items": [
                                    {
                                        "order_item": items[0]['id'],  # ❌ INCORRECT
                                        "quantity": 1,
                                        "reason": "defective"
                                    }
                                ]
                            }
                            
                            # Structure APRÈS correction (correcte)
                            new_structure = {
                                "order": order_id,
                                "reason": "defective",
                                "additional_info": "Test de retour",  # ✅ CORRECT
                                "items": [
                                    {
                                        "order_item_id": items[0]['id'],  # ✅ CORRECT
                                        "quantity": 1,
                                        "reason": "defective"
                                    }
                                ]
                            }
                            
                            print(f"   ❌ Ancienne structure (incorrecte):")
                            print(f"      - 'description' au lieu de 'additional_info'")
                            print(f"      - 'order_item' au lieu de 'order_item_id'")
                            
                            print(f"   ✅ Nouvelle structure (correcte):")
                            print(f"      - 'additional_info': {new_structure['additional_info']}")
                            print(f"      - 'order_item_id': {new_structure['items'][0]['order_item_id']}")
                            
                            # Tester la nouvelle structure
                            print(f"\n🧪 TEST DE LA NOUVELLE STRUCTURE:")
                            test_response = requests.post(
                                f"{API_BASE_URL}/orders/returns/",
                                json=new_structure,
                                headers=headers,
                                timeout=10
                            )
                            
                            print(f"   📊 Statut de la réponse: {test_response.status_code}")
                            
                            if test_response.status_code == 201:
                                print(f"   🎉 SUCCÈS! Retour créé avec succès")
                                response_data = test_response.json()
                                print(f"   📝 ID du retour: {response_data.get('id')}")
                                print(f"   📝 Numéro: {response_data.get('return_number')}")
                                return True
                            elif test_response.status_code == 400:
                                print(f"   ❌ ERREUR 400 - Données invalides")
                                try:
                                    error_data = test_response.json()
                                    print(f"   📝 Détails: {error_data}")
                                except:
                                    print(f"   📝 Réponse: {test_response.text}")
                                return False
                            else:
                                print(f"   ⚠️ Autre erreur: {test_response.status_code}")
                                print(f"   📝 Réponse: {test_response.text}")
                                return False
                        else:
                            print(f"   ⚠️ Aucun article dans la commande")
                            return False
                    else:
                        print(f"   ❌ Erreur détails commande: {order_detail_response.status_code}")
                        return False
                else:
                    print(f"   ⚠️ Aucune commande trouvée")
                    return False
            else:
                print(f"   ❌ Erreur récupération commandes: {orders_response.status_code}")
                return False
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ SERVEUR NON ACCESSIBLE")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def check_backend_expectations():
    """Vérifier ce que le backend attend exactement"""
    print(f"\n📋 VÉRIFICATION DES ATTENTES DU BACKEND")
    print("=" * 50)
    
    print(f"✅ STRUCTURE ATTENDUE PAR LE BACKEND:")
    print(f"   📦 Champ principal:")
    print(f"      - 'order': ID numérique de la commande")
    print(f"      - 'reason': Raison du retour")
    print(f"      - 'additional_info': Informations supplémentaires")
    print(f"      - 'items': Liste des articles à retourner")
    
    print(f"\n   📊 Structure des articles:")
    print(f"      - 'order_item_id': ID de l'article de commande")
    print(f"      - 'quantity': Quantité à retourner")
    print(f"      - 'reason': Raison spécifique (optionnel)")
    print(f"      - 'description': Description spécifique (optionnel)")
    
    print(f"\n❌ ERREURS COMMUNES:")
    print(f"   - Utiliser 'order_item' au lieu de 'order_item_id'")
    print(f"   - Utiliser 'description' au lieu de 'additional_info'")
    print(f"   - Oublier 'quantity' ou 'order_item_id'")

def main():
    print("🧪 TEST DE LA CORRECTION DE CRÉATION DES RETOURS")
    print("=" * 70)
    
    # 1. Vérifier les attentes du backend
    check_backend_expectations()
    
    # 2. Tester la structure corrigée
    creation_ok = test_return_creation_structure()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Structure corrigée: {'✅' if creation_ok else '❌'}")
    
    if creation_ok:
        print(f"\n🎉 CORRECTION RÉUSSIE!")
        print(f"   ✅ L'erreur 400 'order_item_id' et 'quantity' est corrigée")
        print(f"   ✅ La structure des données correspond aux attentes du backend")
        print(f"   ✅ Les retours peuvent maintenant être créés avec succès")
        
        print(f"\n🔧 CORRECTIONS APPORTÉES:")
        print(f"   • 'order_item' → 'order_item_id'")
        print(f"   • 'description' → 'additional_info'")
        print(f"   • Validation des champs requis")
        
        print(f"\n🌐 TESTEZ MAINTENANT:")
        print(f"   • Allez sur une page de commande")
        print(f"   • Cliquez sur 'Demander un retour'")
        print(f"   • Remplissez le formulaire")
        print(f"   • La création devrait maintenant fonctionner!")
        
    else:
        print(f"\n❌ PROBLÈME PERSISTANT")
        print(f"   Vérifiez les erreurs ci-dessus")
        print(f"   Assurez-vous que le serveur Django fonctionne")

if __name__ == "__main__":
    main()
