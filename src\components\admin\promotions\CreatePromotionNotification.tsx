import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import notificationApi from '@/services/notificationApi';
import { Promotion } from '@/services/promotionApi';

interface CreatePromotionNotificationProps {
  promotion: Promotion;
}

const CreatePromotionNotification: React.FC<CreatePromotionNotificationProps> = ({ promotion }) => {
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState(`Nouvelle promotion: ${promotion.name}`);
  const [message, setMessage] = useState(
    `Profitez de notre nouvelle offre: ${promotion.description || promotion.name}. ${
      promotion.discount_type === 'percentage' 
        ? `${promotion.discount_value}% de réduction` 
        : `${promotion.discount_value.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })} de réduction`
    }${promotion.discount_code ? ` avec le code "${promotion.discount_code}"` : ''}.`
  );
  const [sendToAll, setSendToAll] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Mutation pour créer une notification
  const createNotificationMutation = useMutation({
    mutationFn: (data: { title: string; message: string; type: string; sendToAll: boolean; relatedId?: number; link?: string }) => 
      notificationApi.createNotification(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: 'Notification envoyée',
        description: 'La notification a été envoyée avec succès aux utilisateurs.',
      });
      setOpen(false);
      setIsLoading(false);
    },
    onError: () => {
      toast({
        title: 'Erreur',
        description: 'Impossible d\'envoyer la notification. Veuillez réessayer.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  });
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    createNotificationMutation.mutate({
      title,
      message,
      type: 'promotion',
      sendToAll,
      relatedId: promotion.id,
      link: `/promotions/${promotion.id}`
    });
  };
  
  return (
    <>
      <Button 
        variant="outline" 
        size="sm" 
        className="flex items-center gap-1" 
        onClick={() => setOpen(true)}
      >
        <Bell className="h-4 w-4" />
        <span>Notifier les clients</span>
      </Button>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Envoyer une notification de promotion</DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titre de la notification</Label>
              <Input 
                id="title" 
                value={title} 
                onChange={(e) => setTitle(e.target.value)} 
                required 
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea 
                id="message" 
                value={message} 
                onChange={(e) => setMessage(e.target.value)} 
                required 
                className="min-h-[100px]"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="sendToAll" 
                checked={sendToAll} 
                onCheckedChange={(checked) => setSendToAll(checked as boolean)} 
              />
              <Label htmlFor="sendToAll" className="cursor-pointer">
                Envoyer à tous les utilisateurs (sinon, uniquement aux abonnés)
              </Label>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Annuler
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Envoi en cours...' : 'Envoyer la notification'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CreatePromotionNotification;
