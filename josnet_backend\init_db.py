import os
import sys
import django
from django.db import connection
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

def reset_db():
    """Reset the database by dropping all tables and recreating them."""
    print("Resetting database...")

    # Get all table names
    with connection.cursor() as cursor:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name!='sqlite_sequence';")
        tables = cursor.fetchall()

    # Drop all tables
    with connection.cursor() as cursor:
        for table in tables:
            cursor.execute(f"DROP TABLE IF EXISTS {table[0]};")

    print("All tables dropped.")

def run_migrations():
    """Run all migrations."""
    print("Running migrations...")
    call_command('makemigrations')
    call_command('migrate')
    print("Migrations completed.")

def create_superuser():
    """Create a superuser for the admin interface."""
    from django.contrib.auth import get_user_model
    User = get_user_model()

    if not User.objects.filter(email='<EMAIL>').exists():
        print("Creating superuser...")
        User.objects.create_superuser(
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='User'
        )
        print("Superuser created.")
    else:
        print("Superuser already exists.")

def create_sample_data():
    """Create sample data for testing."""
    print("Creating sample data...")

    # Create sample products
    from products.models import Category, Product, ProductVariant, ProductAttribute, AttributeValue

    # Create categories
    if not Category.objects.exists():
        electronics = Category.objects.create(name="Électronique", slug="electronique")
        clothing = Category.objects.create(name="Vêtements", slug="vetements")
        books = Category.objects.create(name="Livres", slug="livres")
        print("Categories created.")

    # Create attributes
    if not ProductAttribute.objects.exists():
        color = ProductAttribute.objects.create(name="Couleur")
        size = ProductAttribute.objects.create(name="Taille")
        material = ProductAttribute.objects.create(name="Matériau")
        print("Attributes created.")

    # Create attribute values
    if not AttributeValue.objects.exists():
        # Colors
        AttributeValue.objects.create(attribute=color, value="Rouge")
        AttributeValue.objects.create(attribute=color, value="Bleu")
        AttributeValue.objects.create(attribute=color, value="Noir")

        # Sizes
        AttributeValue.objects.create(attribute=size, value="S")
        AttributeValue.objects.create(attribute=size, value="M")
        AttributeValue.objects.create(attribute=size, value="L")

        # Materials
        AttributeValue.objects.create(attribute=material, value="Coton")
        AttributeValue.objects.create(attribute=material, value="Polyester")
        AttributeValue.objects.create(attribute=material, value="Cuir")
        print("Attribute values created.")

    # Create sample orders
    from orders.models import Order, OrderItem, OrderStatusHistory, ShippingMethod
    from django.utils import timezone
    import random

    # Create shipping methods
    if not ShippingMethod.objects.exists():
        ShippingMethod.objects.create(
            name="Standard",
            description="Livraison standard en 3-5 jours ouvrés",
            price=5.99,
            estimated_delivery_days=5,
            is_active=True
        )
        ShippingMethod.objects.create(
            name="Express",
            description="Livraison express en 1-2 jours ouvrés",
            price=12.99,
            estimated_delivery_days=2,
            is_active=True
        )
        ShippingMethod.objects.create(
            name="Gratuit",
            description="Livraison gratuite en 5-7 jours ouvrés",
            price=0.00,
            estimated_delivery_days=7,
            is_active=True
        )
        print("Shipping methods created.")

    # Create sample orders
    if not Order.objects.exists():
        # Get a user
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = User.objects.first()

        # Create orders
        statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled']
        payment_methods = ['credit_card', 'paypal', 'bank_transfer', 'cash_on_delivery']

        for i in range(10):
            status = random.choice(statuses)
            payment_method = random.choice(payment_methods)

            order = Order.objects.create(
                user=user,
                email=user.email,
                phone="+***********",

                # Billing information
                billing_first_name=user.first_name,
                billing_last_name=user.last_name,
                billing_address_line1="123 Rue de Paris",
                billing_city="Paris",
                billing_postal_code="75001",
                billing_country="France",

                # Shipping information
                shipping_first_name=user.first_name,
                shipping_last_name=user.last_name,
                shipping_address_line1="123 Rue de Paris",
                shipping_city="Paris",
                shipping_postal_code="75001",
                shipping_country="France",

                # Order details
                status=status,
                payment_status='paid' if status in ['processing', 'shipped', 'delivered'] else 'pending',
                payment_method=payment_method,

                # Pricing
                subtotal=random.uniform(50, 500),
                shipping_cost=random.choice([0, 5.99, 12.99]),
                tax_amount=random.uniform(5, 50),
                discount_amount=random.uniform(0, 20),
                total=random.uniform(50, 500),
            )

            # Update calculated fields
            order.total = order.subtotal + order.shipping_cost + order.tax_amount - order.discount_amount
            order.save()

            # Create order status history
            OrderStatusHistory.objects.create(
                order=order,
                status=status,
                notes=f"Order {status}",
                created_by=user if user.is_staff else None
            )

            # Create order items
            products = Product.objects.all()[:3]
            for product in products:
                OrderItem.objects.create(
                    order=order,
                    product=product,
                    product_name=product.name,
                    sku=product.sku,
                    price=product.current_price,
                    quantity=random.randint(1, 3),
                    discount_amount=0,
                    subtotal=product.current_price * random.randint(1, 3),
                    final_price=product.current_price * random.randint(1, 3)
                )

        print("Sample orders created.")

def main():
    """Main function to initialize the database."""
    if len(sys.argv) > 1 and sys.argv[1] == '--reset':
        reset_db()

    run_migrations()
    create_superuser()
    create_sample_data()

    print("Database initialization completed.")

if __name__ == "__main__":
    main()
