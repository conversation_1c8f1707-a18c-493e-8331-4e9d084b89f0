#!/usr/bin/env python3
"""
Script pour déboguer les permissions des réclamations
"""

import sys
import os
import requests

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from orders.models_extension import Claim

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def check_admin_permissions():
    """Vérifier les permissions de l'admin"""
    print("👤 VÉRIFICATION DES PERMISSIONS ADMIN")
    print("=" * 50)
    
    try:
        admin_user = User.objects.get(email=ADMIN_EMAIL)
        print(f"   📧 Email: {admin_user.email}")
        print(f"   🔑 Superuser: {admin_user.is_superuser}")
        print(f"   👨‍💼 Staff: {admin_user.is_staff}")
        print(f"   🏢 Admin: {getattr(admin_user, 'is_admin', 'N/A')}")
        print(f"   👨‍💼 Staff Member: {getattr(admin_user, 'is_staff_member', 'N/A')}")
        print(f"   ✅ Active: {admin_user.is_active}")
        
        return admin_user
    except User.DoesNotExist:
        print(f"   ❌ Utilisateur admin non trouvé")
        return None

def check_claim_ownership():
    """Vérifier la propriété de la réclamation"""
    print(f"\n📋 VÉRIFICATION DE LA PROPRIÉTÉ DE LA RÉCLAMATION")
    print("=" * 50)
    
    try:
        admin_user = User.objects.get(email=ADMIN_EMAIL)
        claim = Claim.objects.first()
        
        if claim:
            print(f"   🆔 ID réclamation: {claim.id}")
            print(f"   👤 Propriétaire: {claim.user.email}")
            print(f"   🔍 Admin = Propriétaire: {claim.user == admin_user}")
            print(f"   📝 Statut: {claim.status}")
            
            return claim
        else:
            print(f"   ❌ Aucune réclamation trouvée")
            return None
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def test_send_message_api():
    """Tester l'API d'envoi de message"""
    print(f"\n💬 TEST DE L'API D'ENVOI DE MESSAGE")
    print("=" * 50)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            print(f"   ✅ Authentification réussie")
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Récupérer la première réclamation
            claims_response = requests.get(f"{API_BASE_URL}/orders/claims/", headers=headers)
            
            if claims_response.status_code == 200:
                claims = claims_response.json().get('results', [])
                if claims:
                    claim_id = claims[0]['id']
                    print(f"   📋 Réclamation trouvée: {claim_id}")
                    
                    # Tenter d'envoyer un message
                    message_data = {
                        "message": "Test de message depuis l'admin"
                    }
                    
                    send_response = requests.post(
                        f"{API_BASE_URL}/orders/claims/{claim_id}/send_message/",
                        json=message_data,
                        headers=headers
                    )
                    
                    print(f"   📤 Statut envoi: {send_response.status_code}")
                    
                    if send_response.status_code == 200:
                        print(f"   ✅ Message envoyé avec succès")
                        return True
                    else:
                        print(f"   ❌ Erreur: {send_response.status_code}")
                        print(f"   Réponse: {send_response.text}")
                        return False
                else:
                    print(f"   ❌ Aucune réclamation trouvée")
                    return False
            else:
                print(f"   ❌ Erreur récupération réclamations: {claims_response.status_code}")
                return False
        else:
            print(f"   ❌ Échec authentification: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def fix_admin_staff_status():
    """Corriger le statut staff de l'admin si nécessaire"""
    print(f"\n🔧 CORRECTION DU STATUT STAFF")
    print("=" * 50)
    
    try:
        admin_user = User.objects.get(email=ADMIN_EMAIL)
        
        if not admin_user.is_staff:
            print(f"   ⚠️ L'admin n'est pas staff, correction en cours...")
            admin_user.is_staff = True
            admin_user.save()
            print(f"   ✅ Statut staff activé")
            return True
        else:
            print(f"   ✅ L'admin est déjà staff")
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🔍 DÉBOGAGE DES PERMISSIONS DE RÉCLAMATIONS")
    print("=" * 60)
    
    # 1. Vérifier les permissions admin
    admin_user = check_admin_permissions()
    
    # 2. Vérifier la propriété de la réclamation
    claim = check_claim_ownership()
    
    # 3. Corriger le statut staff si nécessaire
    staff_fixed = fix_admin_staff_status()
    
    # 4. Tester l'API d'envoi de message
    message_ok = test_send_message_api()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Admin trouvé: {'✅' if admin_user else '❌'}")
    print(f"   Réclamation trouvée: {'✅' if claim else '❌'}")
    print(f"   Statut staff corrigé: {'✅' if staff_fixed else '❌'}")
    print(f"   Envoi de message: {'✅' if message_ok else '❌'}")
    
    if admin_user and claim:
        print(f"\n🎯 ANALYSE DES PERMISSIONS:")
        
        # Analyser pourquoi l'erreur 403 se produit
        if admin_user.is_staff or admin_user.is_superuser:
            print(f"   ✅ L'admin a les permissions staff/superuser")
        else:
            print(f"   ❌ L'admin n'a pas les permissions staff/superuser")
        
        if claim.user == admin_user:
            print(f"   ✅ L'admin est propriétaire de la réclamation")
        else:
            print(f"   ❌ L'admin n'est pas propriétaire de la réclamation")
            print(f"   👤 Propriétaire: {claim.user.email}")
            print(f"   👤 Admin: {admin_user.email}")
    
    if message_ok:
        print(f"\n🎉 PROBLÈME RÉSOLU!")
        print(f"   ✅ L'envoi de messages fonctionne maintenant")
        print(f"   🌐 Testez sur: http://localhost:8080/admin/claims")
    else:
        print(f"\n⚠️ PROBLÈME PERSISTANT")
        print(f"   Vérifiez les permissions et la logique de validation")

if __name__ == "__main__":
    main()
