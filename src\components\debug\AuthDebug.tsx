import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  User, 
  Key, 
  Bell, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthToken, getUserInfo, isAuthenticated } from '@/utils/auth';
import notificationApi from '@/services/notificationApi';

const AuthDebug: React.FC = () => {
  const { user, isAuthenticated: authContextAuthenticated } = useAuth();
  const [tokenVisible, setTokenVisible] = useState(false);
  const [notificationTest, setNotificationTest] = useState<{
    loading: boolean;
    success: boolean;
    error: string | null;
    data: any;
  }>({
    loading: false,
    success: false,
    error: null,
    data: null
  });

  // Informations d'authentification
  const token = getAuthToken();
  const userInfo = getUserInfo();
  const isAuth = isAuthenticated();

  const testNotifications = async () => {
    setNotificationTest({ loading: true, success: false, error: null, data: null });
    
    try {
      console.log('🧪 Test des notifications...');
      const data = await notificationApi.getUnreadNotifications();
      
      setNotificationTest({
        loading: false,
        success: true,
        error: null,
        data
      });
      
      console.log('✅ Test notifications réussi:', data);
    } catch (error: any) {
      setNotificationTest({
        loading: false,
        success: false,
        error: error.message || 'Erreur inconnue',
        data: null
      });
      
      console.error('❌ Test notifications échoué:', error);
    }
  };

  const formatToken = (token: string | null) => {
    if (!token) return 'Aucun token';
    if (!tokenVisible) return `${token.substring(0, 20)}...`;
    return token;
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center gap-2">
        <Key className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Debug Authentification & Notifications</h2>
      </div>

      {/* État d'authentification */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            État d'authentification
          </CardTitle>
          <CardDescription>
            Informations sur l'état de connexion de l'utilisateur
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">Authentifié (utils):</span>
                <Badge variant={isAuth ? "default" : "destructive"}>
                  {isAuth ? (
                    <><CheckCircle className="h-3 w-3 mr-1" /> Oui</>
                  ) : (
                    <><XCircle className="h-3 w-3 mr-1" /> Non</>
                  )}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="font-medium">Authentifié (context):</span>
                <Badge variant={authContextAuthenticated ? "default" : "destructive"}>
                  {authContextAuthenticated ? (
                    <><CheckCircle className="h-3 w-3 mr-1" /> Oui</>
                  ) : (
                    <><XCircle className="h-3 w-3 mr-1" /> Non</>
                  )}
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">Utilisateur (context):</span>
                <span className="text-sm text-gray-600">
                  {user?.email || 'Non connecté'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="font-medium">Utilisateur (localStorage):</span>
                <span className="text-sm text-gray-600">
                  {userInfo?.email || 'Non trouvé'}
                </span>
              </div>
            </div>
          </div>

          {/* Token */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-medium">Token JWT:</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTokenVisible(!tokenVisible)}
              >
                {tokenVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            <div className="p-3 bg-gray-50 rounded-md">
              <code className="text-xs break-all">
                {formatToken(token)}
              </code>
            </div>
          </div>

          {/* Informations utilisateur détaillées */}
          {userInfo && (
            <div className="space-y-2">
              <span className="font-medium">Détails utilisateur:</span>
              <div className="p-3 bg-gray-50 rounded-md">
                <pre className="text-xs">
                  {JSON.stringify(userInfo, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test des notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Test des notifications
          </CardTitle>
          <CardDescription>
            Tester l'accès aux notifications avec le token actuel
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testNotifications}
            disabled={notificationTest.loading}
            className="w-full"
          >
            {notificationTest.loading ? (
              <><RefreshCw className="h-4 w-4 mr-2 animate-spin" /> Test en cours...</>
            ) : (
              <><Bell className="h-4 w-4 mr-2" /> Tester les notifications</>
            )}
          </Button>

          {/* Résultat du test */}
          {notificationTest.success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>✅ Test réussi !</strong>
                <br />
                Notifications récupérées: {notificationTest.data?.count || 0}
                <br />
                Résultats: {notificationTest.data?.results?.length || 0} notifications
              </AlertDescription>
            </Alert>
          )}

          {notificationTest.error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>❌ Test échoué !</strong>
                <br />
                Erreur: {notificationTest.error}
              </AlertDescription>
            </Alert>
          )}

          {/* Données de test */}
          {notificationTest.data && (
            <div className="space-y-2">
              <span className="font-medium">Données reçues:</span>
              <div className="p-3 bg-gray-50 rounded-md max-h-60 overflow-y-auto">
                <pre className="text-xs">
                  {JSON.stringify(notificationTest.data, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recommandations */}
      <Card>
        <CardHeader>
          <CardTitle>Recommandations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            {!isAuth && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Problème d'authentification détecté</strong>
                  <br />
                  • Connectez-vous pour accéder aux notifications
                  • Vérifiez que le token JWT est valide
                </AlertDescription>
              </Alert>
            )}
            
            {isAuth && !authContextAuthenticated && (
              <Alert>
                <AlertDescription>
                  <strong>Incohérence d'état</strong>
                  <br />
                  • Le token existe mais le contexte d'auth n'est pas synchronisé
                  • Essayez de rafraîchir la page
                </AlertDescription>
              </Alert>
            )}
            
            {isAuth && authContextAuthenticated && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>✅ Authentification OK</strong>
                  <br />
                  • L'utilisateur est correctement authentifié
                  • Les notifications devraient fonctionner
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthDebug;
