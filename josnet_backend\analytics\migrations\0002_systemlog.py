# Generated by Django 4.2.7 on 2025-05-23 22:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('analytics', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('log_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('type', models.CharField(choices=[('info', 'Information'), ('success', 'Success'), ('warning', 'Warning'), ('error', 'Error')], max_length=10)),
                ('module', models.CharField(choices=[('utilisateurs', 'Utilisateurs'), ('commandes', 'Commandes'), ('paiement', 'Paiement'), ('stocks', 'Stocks'), ('cms', 'CMS'), ('promotions', 'Promotions'), ('serveur', 'Serveur'), ('securite', 'Sécurité'), ('systeme', 'Système'), ('produits', 'Produits'), ('messagerie', 'Messagerie')], max_length=20)),
                ('message', models.TextField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('additional_data', models.JSONField(blank=True, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['-timestamp'], name='analytics_s_timesta_7c9221_idx'), models.Index(fields=['type'], name='analytics_s_type_2fa064_idx'), models.Index(fields=['module'], name='analytics_s_module_f4f274_idx'), models.Index(fields=['user'], name='analytics_s_user_id_4ca1f9_idx')],
            },
        ),
    ]
