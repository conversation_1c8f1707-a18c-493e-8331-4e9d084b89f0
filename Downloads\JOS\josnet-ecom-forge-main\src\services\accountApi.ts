import { API_BASE_URL } from '@/config/api';
import { ApiError, handleApiError } from '@/utils/apiUtils';

// API service for account-related operations

// Types pour les retours
export interface ReturnItem {
  id: string;
  orderId: string;
  date: string;
  status: string;
  items: number;
  reason: string;
  refundAmount: string;
}

// Types pour les réclamations
export interface Claim {
  id: string;
  orderId: string;
  date: string;
  status: string;
  subject: string;
  description: string;
  lastUpdate: string;
  attachments?: string[];
}

export interface ClaimMessage {
  id: string;
  claimId: string;
  sender: 'customer' | 'support';
  message: string;
  date: string;
  isRead: boolean;
  attachments?: string[];
}

export interface ClaimStatusUpdate {
  id: string;
  claimId: string;
  oldStatus: string;
  newStatus: string;
  date: string;
  comment?: string;
  updatedBy: string;
}

export interface ClaimCreateData {
  orderId: string;
  subject: string;
  description: string;
  attachments?: File[];
}

// Types pour les factures
export interface Invoice {
  id: string;
  orderId: string;
  date: string;
  amount: string;
  status: string;
}

export interface InvoiceDetail extends Invoice {
  customerInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    postalCode: string;
    country: string;
  };
  items: {
    id: string;
    name: string;
    quantity: number;
    unitPrice: string;
    totalPrice: string;
  }[];
  subtotal: string;
  tax: string;
  shipping: string;
  discount?: string;
  paymentMethod: string;
  paymentId?: string;
}

// Types pour les suggestions de produits
export interface SuggestedProduct {
  id: number;
  name: string;
  price: number;
  image: string;
  rating: number;
  category: string;
}

// Helper function to get auth header
const getAuthHeader = () => {
  const token = localStorage.getItem('accessToken');
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Account API service
export const accountApi = {
  // Get user returns
  getReturns: async (): Promise<ReturnItem[]> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data
        return [
          {
            id: "RET-001",
            orderId: "CMD-12345",
            date: "10/05/2025",
            status: "Approuvé",
            items: 1,
            reason: "Article défectueux",
            refundAmount: "89,99 €"
          },
          {
            id: "RET-002",
            orderId: "CMD-12344",
            date: "20/04/2025",
            status: "En cours",
            items: 2,
            reason: "Taille incorrecte",
            refundAmount: "124,50 €"
          }
        ];
      }

      const response = await fetch(`${API_BASE_URL}/account/returns/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching returns:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des retours'
      };
    }
  },

  // Get user claims
  getClaims: async (): Promise<Claim[]> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data
        return [
          {
            id: "REC-001",
            orderId: "CMD-12343",
            date: "05/03/2025",
            status: "Résolu",
            subject: "Produit endommagé à l'arrivée",
            description: "Le produit est arrivé avec des dommages visibles sur l'emballage et le produit lui-même.",
            lastUpdate: "10/03/2025",
            attachments: ["damage-photo-1.jpg", "damage-photo-2.jpg"]
          },
          {
            id: "REC-002",
            orderId: "CMD-12345",
            date: "15/04/2025",
            status: "En cours",
            subject: "Produit non conforme à la description",
            description: "Le produit reçu ne correspond pas à la description sur le site. Les spécifications techniques sont différentes.",
            lastUpdate: "16/04/2025"
          },
          {
            id: "REC-003",
            orderId: "CMD-12346",
            date: "20/04/2025",
            status: "Nouveau",
            subject: "Pièce manquante dans le colis",
            description: "Il manque un câble d'alimentation dans le colis reçu.",
            lastUpdate: "20/04/2025"
          }
        ];
      }

      const response = await fetch(`${API_BASE_URL}/account/claims/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching claims:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des réclamations'
      };
    }
  },

  // Get claim details
  getClaimDetails: async (claimId: string): Promise<Claim> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data based on claim ID
        const mockClaims = [
          {
            id: "REC-001",
            orderId: "CMD-12343",
            date: "05/03/2025",
            status: "Résolu",
            subject: "Produit endommagé à l'arrivée",
            description: "Le produit est arrivé avec des dommages visibles sur l'emballage et le produit lui-même.",
            lastUpdate: "10/03/2025",
            attachments: ["damage-photo-1.jpg", "damage-photo-2.jpg"]
          },
          {
            id: "REC-002",
            orderId: "CMD-12345",
            date: "15/04/2025",
            status: "En cours",
            subject: "Produit non conforme à la description",
            description: "Le produit reçu ne correspond pas à la description sur le site. Les spécifications techniques sont différentes.",
            lastUpdate: "16/04/2025"
          },
          {
            id: "REC-003",
            orderId: "CMD-12346",
            date: "20/04/2025",
            status: "Nouveau",
            subject: "Pièce manquante dans le colis",
            description: "Il manque un câble d'alimentation dans le colis reçu.",
            lastUpdate: "20/04/2025"
          }
        ];

        const claim = mockClaims.find(c => c.id === claimId);
        if (!claim) {
          throw new Error('Réclamation non trouvée');
        }

        return claim;
      }

      const response = await fetch(`${API_BASE_URL}/account/claims/${claimId}/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching claim details:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des détails de la réclamation'
      };
    }
  },

  // Create a new claim
  createClaim: async (data: ClaimCreateData): Promise<Claim> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Generate a new claim with mock data
        const newClaim: Claim = {
          id: `REC-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
          orderId: data.orderId,
          date: new Date().toISOString(),
          status: "Nouveau",
          subject: data.subject,
          description: data.description,
          lastUpdate: new Date().toISOString(),
          attachments: data.attachments ? data.attachments.map(file => file.name) : undefined
        };

        return newClaim;
      }

      // Create form data for file uploads
      const formData = new FormData();
      formData.append('order_id', data.orderId);
      formData.append('subject', data.subject);
      formData.append('description', data.description);

      if (data.attachments) {
        data.attachments.forEach(file => {
          formData.append('attachments', file);
        });
      }

      const response = await fetch(`${API_BASE_URL}/account/claims/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
        },
        body: formData,
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating claim:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création de la réclamation'
      };
    }
  },

  // Get claim messages
  getClaimMessages: async (claimId: string): Promise<ClaimMessage[]> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data based on claim ID
        if (claimId === "REC-001") {
          return [
            {
              id: "MSG-001",
              claimId: "REC-001",
              sender: "customer",
              message: "Bonjour, j'ai reçu mon colis aujourd'hui et le produit est endommagé. Veuillez voir les photos en pièce jointe.",
              date: "05/03/2025 10:15",
              isRead: true,
              attachments: ["damage-photo-1.jpg", "damage-photo-2.jpg"]
            },
            {
              id: "MSG-002",
              claimId: "REC-001",
              sender: "support",
              message: "Bonjour, nous sommes désolés pour ce désagrément. Nous allons traiter votre demande dans les plus brefs délais. Pouvez-vous nous confirmer si l'emballage était également endommagé ?",
              date: "05/03/2025 14:30",
              isRead: true
            },
            {
              id: "MSG-003",
              claimId: "REC-001",
              sender: "customer",
              message: "Oui, l'emballage présentait également des signes de dommages sur le côté droit.",
              date: "06/03/2025 09:45",
              isRead: true
            },
            {
              id: "MSG-004",
              claimId: "REC-001",
              sender: "support",
              message: "Merci pour ces informations. Nous avons décidé de procéder à un remboursement complet. Vous recevrez le remboursement dans les 3-5 jours ouvrables.",
              date: "08/03/2025 11:20",
              isRead: true
            },
            {
              id: "MSG-005",
              claimId: "REC-001",
              sender: "customer",
              message: "Merci beaucoup pour votre réactivité et votre professionnalisme.",
              date: "08/03/2025 15:10",
              isRead: true
            }
          ];
        } else if (claimId === "REC-002") {
          return [
            {
              id: "MSG-006",
              claimId: "REC-002",
              sender: "customer",
              message: "Le produit que j'ai reçu ne correspond pas à la description sur le site. Les spécifications techniques sont différentes.",
              date: "15/04/2025 16:20",
              isRead: true
            },
            {
              id: "MSG-007",
              claimId: "REC-002",
              sender: "support",
              message: "Bonjour, nous sommes désolés pour ce problème. Pourriez-vous préciser quelles spécifications diffèrent de la description ?",
              date: "16/04/2025 09:15",
              isRead: true
            }
          ];
        } else if (claimId === "REC-003") {
          return [
            {
              id: "MSG-008",
              claimId: "REC-003",
              sender: "customer",
              message: "Il manque un câble d'alimentation dans le colis que j'ai reçu.",
              date: "20/04/2025 14:45",
              isRead: true
            }
          ];
        }

        return [];
      }

      const response = await fetch(`${API_BASE_URL}/account/claims/${claimId}/messages/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching claim messages:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des messages'
      };
    }
  },

  // Send a message for a claim
  sendClaimMessage: async (claimId: string, message: string, attachments?: File[]): Promise<ClaimMessage> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Generate a new message with mock data
        const newMessage: ClaimMessage = {
          id: `MSG-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
          claimId: claimId,
          sender: "customer",
          message: message,
          date: new Date().toISOString(),
          isRead: false,
          attachments: attachments ? attachments.map(file => file.name) : undefined
        };

        return newMessage;
      }

      // Create form data for file uploads
      const formData = new FormData();
      formData.append('message', message);

      if (attachments) {
        attachments.forEach(file => {
          formData.append('attachments', file);
        });
      }

      const response = await fetch(`${API_BASE_URL}/account/claims/${claimId}/messages/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
        },
        body: formData,
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error sending claim message:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de l\'envoi du message'
      };
    }
  },

  // Get claim status history
  getClaimStatusHistory: async (claimId: string): Promise<ClaimStatusUpdate[]> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data based on claim ID
        if (claimId === "REC-001") {
          return [
            {
              id: "STA-001",
              claimId: "REC-001",
              oldStatus: "",
              newStatus: "Nouveau",
              date: "05/03/2025 10:15",
              updatedBy: "system"
            },
            {
              id: "STA-002",
              claimId: "REC-001",
              oldStatus: "Nouveau",
              newStatus: "En cours",
              date: "05/03/2025 14:30",
              comment: "Prise en charge par le service client",
              updatedBy: "support"
            },
            {
              id: "STA-003",
              claimId: "REC-001",
              oldStatus: "En cours",
              newStatus: "Résolu",
              date: "08/03/2025 11:20",
              comment: "Remboursement approuvé",
              updatedBy: "support"
            }
          ];
        } else if (claimId === "REC-002") {
          return [
            {
              id: "STA-004",
              claimId: "REC-002",
              oldStatus: "",
              newStatus: "Nouveau",
              date: "15/04/2025 16:20",
              updatedBy: "system"
            },
            {
              id: "STA-005",
              claimId: "REC-002",
              oldStatus: "Nouveau",
              newStatus: "En cours",
              date: "16/04/2025 09:15",
              comment: "Prise en charge par le service client",
              updatedBy: "support"
            }
          ];
        } else if (claimId === "REC-003") {
          return [
            {
              id: "STA-006",
              claimId: "REC-003",
              oldStatus: "",
              newStatus: "Nouveau",
              date: "20/04/2025 14:45",
              updatedBy: "system"
            }
          ];
        }

        return [];
      }

      const response = await fetch(`${API_BASE_URL}/account/claims/${claimId}/status-history/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching claim status history:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération de l\'historique des statuts'
      };
    }
  },

  // Get user invoices
  getInvoices: async (): Promise<Invoice[]> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data
        return [
          {
            id: "FAC-12345",
            orderId: "CMD-12345",
            date: "15/05/2025",
            amount: "249,99 €",
            status: "Payée"
          },
          {
            id: "FAC-12344",
            orderId: "CMD-12344",
            date: "02/04/2025",
            amount: "124,50 €",
            status: "Payée"
          },
          {
            id: "FAC-12343",
            orderId: "CMD-12343",
            date: "15/03/2025",
            amount: "349,90 €",
            status: "Payée"
          },
          {
            id: "FAC-12342",
            orderId: "CMD-12342",
            date: "28/02/2025",
            amount: "74,99 €",
            status: "Payée"
          },
          {
            id: "FAC-12341",
            orderId: "CMD-12341",
            date: "15/01/2025",
            amount: "179,99 €",
            status: "Payée"
          }
        ];
      }

      const response = await fetch(`${API_BASE_URL}/account/invoices/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching invoices:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des factures'
      };
    }
  },

  // Get suggested products
  getSuggestedProducts: async (): Promise<SuggestedProduct[]> => {
    try {
      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data
        return [
          {
            id: 1,
            name: "Routeur Wi-Fi 6 Haute Performance",
            price: 129.99,
            image: "/placeholder.svg",
            rating: 4.8,
            category: "Réseaux"
          },
          {
            id: 2,
            name: "Extension de Réseau Maillé",
            price: 89.99,
            image: "/placeholder.svg",
            rating: 4.6,
            category: "Réseaux"
          },
          {
            id: 3,
            name: "Switch Ethernet 8 Ports Gigabit",
            price: 49.99,
            image: "/placeholder.svg",
            rating: 4.5,
            category: "Réseaux"
          },
          {
            id: 4,
            name: "Caméra de Sécurité IP WiFi",
            price: 79.99,
            image: "/placeholder.svg",
            rating: 4.7,
            category: "Sécurité"
          },
          {
            id: 5,
            name: "Disque Dur Externe 2TB",
            price: 89.99,
            image: "/placeholder.svg",
            rating: 4.4,
            category: "Stockage"
          },
          {
            id: 6,
            name: "Câble Ethernet Cat.8 3m",
            price: 14.99,
            image: "/placeholder.svg",
            rating: 4.9,
            category: "Accessoires"
          }
        ];
      }

      const response = await fetch(`${API_BASE_URL}/account/suggestions/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching suggested products:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des suggestions de produits'
      };
    }
  },

  // Get invoice details
  getInvoiceDetails: async (invoiceId: string): Promise<InvoiceDetail> => {
    // Pour le développement, utiliser des données simulées
    if (process.env.NODE_ENV === 'development') {
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Retourner des données simulées basées sur l'ID de la facture
      const mockInvoices = [
        {
          id: "FAC-12345",
          orderId: "CMD-12345",
          date: "15/05/2025",
          amount: "249,99 €",
          status: "Payée",
          customerInfo: {
            name: "Jean Dupont",
            email: "<EMAIL>",
            address: "123 Rue de la Paix",
            city: "Paris",
            postalCode: "75001",
            country: "France"
          },
          items: [
            {
              id: "PROD-001",
              name: "Routeur Wi-Fi 6 Haute Performance",
              quantity: 1,
              unitPrice: "129,99 €",
              totalPrice: "129,99 €"
            },
            {
              id: "PROD-002",
              name: "Extension de Réseau Maillé",
              quantity: 1,
              unitPrice: "89,99 €",
              totalPrice: "89,99 €"
            }
          ],
          subtotal: "219,98 €",
          tax: "20,00 €",
          shipping: "10,00 €",
          paymentMethod: "Carte bancaire",
          paymentId: "PAY-12345678"
        },
        {
          id: "FAC-12344",
          orderId: "CMD-12344",
          date: "02/04/2025",
          amount: "124,50 €",
          status: "Payée",
          customerInfo: {
            name: "Jean Dupont",
            email: "<EMAIL>",
            address: "123 Rue de la Paix",
            city: "Paris",
            postalCode: "75001",
            country: "France"
          },
          items: [
            {
              id: "PROD-003",
              name: "Switch Ethernet 8 Ports Gigabit",
              quantity: 1,
              unitPrice: "49,99 €",
              totalPrice: "49,99 €"
            },
            {
              id: "PROD-006",
              name: "Câble Ethernet Cat.8 3m",
              quantity: 5,
              unitPrice: "14,99 €",
              totalPrice: "74,95 €"
            }
          ],
          subtotal: "124,94 €",
          tax: "12,49 €",
          shipping: "0,00 €",
          discount: "12,93 €",
          paymentMethod: "PayPal",
          paymentId: "PAY-87654321"
        }
      ];

      // Si l'ID n'est pas spécifié, retourner la première facture
      if (!invoiceId) {
        return mockInvoices[0];
      }

      // Chercher la facture correspondante
      const invoice = mockInvoices.find(inv => inv.id === invoiceId);
      if (!invoice) {
        // Si la facture n'est pas trouvée mais que l'ID est spécifié, retourner la première facture
        // avec l'ID modifié pour correspondre à la demande
        const defaultInvoice = { ...mockInvoices[0], id: invoiceId };
        return defaultInvoice;
      }

      return invoice;
    }

    // En production, appeler l'API réelle
    try {
      const response = await fetch(`${API_BASE_URL}/account/invoices/${invoiceId}/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching invoice details:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des détails de la facture'
      };
    }
  }
};
