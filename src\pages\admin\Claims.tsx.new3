        {/* Claim Details Dialog */}
        <Dialog 
          open={detailsOpen} 
          onClose={handleCloseDetails}
          maxWidth="md"
          fullWidth
        >
          {selectedClaim && (
            <>
              <DialogTitle>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h6">Réclamation {selectedClaim.claim_number}</Typography>
                  <Chip 
                    label={statusLabels[selectedClaim.status as keyof typeof statusLabels]}
                    sx={{ 
                      backgroundColor: statusColors[selectedClaim.status as keyof typeof statusColors],
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>
              </DialogTitle>
              <DialogContent>
                <Box sx={{ mb: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Client</Typography>
                      <Typography variant="body2">{selectedClaim.user.full_name}</Typography>
                      <Typography variant="body2" color="text.secondary">{selectedClaim.user.email}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Date de création</Typography>
                      <Typography variant="body2">{formatDate(selectedClaim.created_at)} à {formatTime(selectedClaim.created_at)}</Typography>
                    </Grid>
                    {selectedClaim.order && (
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2">Commande associée</Typography>
                        <Typography variant="body2">{selectedClaim.order.order_number}</Typography>
                      </Grid>
                    )}
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Sujet</Typography>
                      <Typography variant="body2">{subjectLabels[selectedClaim.subject as keyof typeof subjectLabels] || selectedClaim.subject}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2">Description</Typography>
                      <Typography variant="body2">{selectedClaim.description}</Typography>
                    </Grid>
                  </Grid>
                </Box>
                
                <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)} sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                  <Tab label="Messages" />
                  <Tab label="Historique des statuts" />
                </Tabs>
                
                {activeTab === 0 && (
                  <>
                    <Box sx={{ mb: 2, maxHeight: '300px', overflowY: 'auto' }}>
                      {selectedClaim.messages.length > 0 ? (
                        selectedClaim.messages.map((message) => (
                          <Card key={message.id} sx={{ mb: 2, bgcolor: message.sender === 'support' ? 'primary.50' : 'background.paper' }}>
                            <CardContent>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <Avatar sx={{ mr: 1, bgcolor: message.sender === 'customer' ? 'secondary.main' : 'primary.main' }}>
                                  {getSenderIcon(message.sender)}
                                </Avatar>
                                <Box>
                                  <Typography variant="subtitle2">{getSenderName(message.sender)}</Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {formatDate(message.created_at)} à {formatTime(message.created_at)}
                                  </Typography>
                                </Box>
                              </Box>
                              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>{message.message}</Typography>
                              
                              {message.attachments.length > 0 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="subtitle2" sx={{ mb: 1 }}>Pièces jointes:</Typography>
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                    {message.attachments.map((attachment) => (
                                      <Chip 
                                        key={attachment.id}
                                        label={attachment.file_name}
                                        component="a"
                                        href={attachment.file_url}
                                        target="_blank"
                                        clickable
                                        icon={<AttachFileIcon />}
                                        variant="outlined"
                                      />
                                    ))}
                                  </Box>
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        ))
                      ) : (
                        <Typography align="center" color="text.secondary">Aucun message</Typography>
                      )}
                    </Box>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>Répondre</Typography>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      placeholder="Votre réponse..."
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      sx={{ mb: 2 }}
                    />
                    
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>
                      <Button
                        variant="outlined"
                        component="label"
                        startIcon={<AttachFileIcon />}
                        sx={{ alignSelf: 'flex-start' }}
                      >
                        Ajouter des fichiers
                        <input
                          type="file"
                          hidden
                          multiple
                          onChange={handleFileChange}
                        />
                      </Button>
                      
                      {selectedFiles.length > 0 && (
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>Fichiers sélectionnés:</Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {selectedFiles.map((file, index) => (
                              <Chip 
                                key={index}
                                label={file.name}
                                onDelete={() => handleRemoveFile(index)}
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Box>
                    
                    <Button
                      variant="contained"
                      color="primary"
                      endIcon={<SendIcon />}
                      onClick={handleSendReply}
                      disabled={!replyMessage.trim() || loading}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Envoyer'}
                    </Button>
                  </>
                )}
                
                {activeTab === 1 && (
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Date</TableCell>
                          <TableCell>Ancien statut</TableCell>
                          <TableCell>Nouveau statut</TableCell>
                          <TableCell>Commentaire</TableCell>
                          <TableCell>Modifié par</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedClaim.status_history.length > 0 ? (
                          selectedClaim.status_history.map((history) => (
                            <TableRow key={history.id}>
                              <TableCell>
                                {formatDate(history.created_at)}
                                <Typography variant="caption" display="block">
                                  {formatTime(history.created_at)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Chip 
                                  label={statusLabels[history.old_status as keyof typeof statusLabels] || history.old_status}
                                  size="small"
                                  sx={{ 
                                    backgroundColor: statusColors[history.old_status as keyof typeof statusColors] || '#9E9E9E',
                                    color: 'white'
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <Chip 
                                  label={statusLabels[history.new_status as keyof typeof statusLabels] || history.new_status}
                                  size="small"
                                  sx={{ 
                                    backgroundColor: statusColors[history.new_status as keyof typeof statusColors] || '#9E9E9E',
                                    color: 'white'
                                  }}
                                />
                              </TableCell>
                              <TableCell>{history.comment || '-'}</TableCell>
                              <TableCell>{history.created_by ? history.created_by.full_name : 'Système'}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} align="center">
                              Aucun historique de statut disponible
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDetailsOpen(false)}>Fermer</Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => {
                    setDetailsOpen(false);
                    handleUpdateStatus(selectedClaim);
                  }}
                >
                  Mettre à jour le statut
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>

        {/* Update Status Dialog */}
        <Dialog open={updateStatusOpen} onClose={() => setUpdateStatusOpen(false)}>
          {selectedClaim && (
            <>
              <DialogTitle>Mettre à jour le statut de la réclamation {selectedClaim.claim_number}</DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1, minWidth: 400 }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel id="new-status-label">Nouveau statut</InputLabel>
                    <Select
                      labelId="new-status-label"
                      value={newStatus}
                      onChange={(e) => setNewStatus(e.target.value)}
                      label="Nouveau statut"
                    >
                      <MenuItem value="new">Nouveau</MenuItem>
                      <MenuItem value="in_progress">En cours</MenuItem>
                      <MenuItem value="waiting_customer">En attente du client</MenuItem>
                      <MenuItem value="waiting_staff">En attente du support</MenuItem>
                      <MenuItem value="resolved">Résolu</MenuItem>
                      <MenuItem value="closed">Fermé</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    label="Note administrative (optionnelle)"
                    multiline
                    rows={4}
                    fullWidth
                    value={adminNote}
                    onChange={(e) => setAdminNote(e.target.value)}
                  />
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setUpdateStatusOpen(false)}>Annuler</Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={submitStatusUpdate}
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Enregistrer'}
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Box>
    </AdminLayout>
  );
};

export default AdminClaims;
