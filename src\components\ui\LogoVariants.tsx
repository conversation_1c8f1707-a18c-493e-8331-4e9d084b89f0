import React from 'react';
import { cn } from '@/lib/utils';

interface LogoVariantsProps {
  className?: string;
}

// Logo SVG intégré pour éviter les problèmes de chargement
export const JosNetIcon: React.FC<LogoVariantsProps> = ({ className }) => (
  <svg 
    width="32" 
    height="32" 
    viewBox="0 0 32 32" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={cn("inline-block", className)}
  >
    <rect width="32" height="32" rx="6" fill="#2563eb"/>
    <path d="M10 8 L10 18 Q10 22 14 22 Q18 22 18 18 L18 15" stroke="white" strokeWidth="2.5" fill="none" strokeLinecap="round"/>
    <circle cx="22" cy="12" r="1.5" fill="#f59e0b"/>
    <circle cx="6" cy="16" r="1" fill="#f59e0b"/>
    <circle cx="16" cy="26" r="1" fill="#f59e0b"/>
    <line x1="12" y1="10" x2="20" y2="13" stroke="#f59e0b" strokeWidth="1" opacity="0.8"/>
    <line x1="8" y1="16" x2="6" y2="16" stroke="#f59e0b" strokeWidth="1" opacity="0.8"/>
  </svg>
);

export const JosNetCompact: React.FC<LogoVariantsProps> = ({ className }) => (
  <svg 
    width="120" 
    height="40" 
    viewBox="0 0 120 40" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={cn("inline-block", className)}
  >
    <rect x="2" y="2" width="36" height="36" rx="8" fill="#2563eb" opacity="0.1"/>
    <path d="M12 10 L12 22 Q12 28 18 28 Q24 28 24 22 L24 18" stroke="#2563eb" strokeWidth="2.5" fill="none" strokeLinecap="round"/>
    <circle cx="28" cy="14" r="1.5" fill="#f59e0b"/>
    <circle cx="8" cy="18" r="1" fill="#f59e0b"/>
    <circle cx="20" cy="30" r="1" fill="#f59e0b"/>
    <line x1="14" y1="12" x2="26" y2="16" stroke="#f59e0b" strokeWidth="0.8" opacity="0.7"/>
    <line x1="10" y1="18" x2="8" y2="18" stroke="#f59e0b" strokeWidth="0.8" opacity="0.7"/>
    <text x="45" y="18" fontFamily="Inter, system-ui, sans-serif" fontSize="14" fontWeight="700" fill="#2563eb">
      JOSNET
    </text>
    <text x="45" y="30" fontFamily="Inter, system-ui, sans-serif" fontSize="8" fontWeight="500" fill="#f59e0b" letterSpacing="0.5px">
      NETWORK
    </text>
  </svg>
);

export const JosNetFull: React.FC<LogoVariantsProps> = ({ className }) => (
  <svg 
    width="200" 
    height="60" 
    viewBox="0 0 200 60" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={cn("inline-block", className)}
  >
    <circle cx="25" cy="30" r="20" fill="#2563eb" opacity="0.1"/>
    <path d="M20 15 L20 35 Q20 45 30 45 Q40 45 40 35 L40 30" stroke="#2563eb" strokeWidth="3" fill="none" strokeLinecap="round"/>
    <circle cx="20" cy="15" r="2" fill="#2563eb"/>
    <circle cx="35" cy="20" r="2" fill="#f59e0b"/>
    <circle cx="15" cy="25" r="1.5" fill="#f59e0b"/>
    <circle cx="30" cy="40" r="1.5" fill="#f59e0b"/>
    <line x1="22" y1="17" x2="33" y2="22" stroke="#f59e0b" strokeWidth="1" opacity="0.6"/>
    <line x1="18" y1="25" x2="15" y2="25" stroke="#f59e0b" strokeWidth="1" opacity="0.6"/>
    <line x1="25" y1="35" x2="28" y2="38" stroke="#f59e0b" strokeWidth="1" opacity="0.6"/>
    <text x="55" y="25" fontFamily="Inter, system-ui, sans-serif" fontSize="18" fontWeight="700" fill="#2563eb">
      JOSNET
    </text>
    <text x="55" y="42" fontFamily="Inter, system-ui, sans-serif" fontSize="12" fontWeight="500" fill="#f59e0b" letterSpacing="1px">
      NETWORK
    </text>
    <text x="55" y="52" fontFamily="Inter, system-ui, sans-serif" fontSize="8" fontWeight="400" fill="#6b7280">
      IT & Télécom Solutions
    </text>
  </svg>
);

// Composant Logo amélioré avec SVG intégrés
interface LogoProps {
  variant?: 'full' | 'compact' | 'icon' | 'text';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showTagline?: boolean;
}

export const LogoInline: React.FC<LogoProps> = ({ 
  variant = 'compact', 
  size = 'md', 
  className,
  showTagline = false
}) => {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12'
  };

  switch (variant) {
    case 'full':
      return <JosNetFull className={cn(sizeClasses[size], "w-auto", className)} />;
    
    case 'compact':
      return <JosNetCompact className={cn(sizeClasses[size], "w-auto", className)} />;
    
    case 'icon':
      return <JosNetIcon className={cn(sizeClasses[size], "w-auto", className)} />;
    
    case 'text':
    default:
      return (
        <div className={cn("flex items-center", className)}>
          <div className="flex flex-col">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-primary">JOSNET</span>
              <span className="text-2xl font-bold text-accent ml-1">NETWORK</span>
            </div>
            {showTagline && (
              <span className="text-xs text-gray-500 -mt-1">IT & Télécom Solutions</span>
            )}
          </div>
        </div>
      );
  }
};
