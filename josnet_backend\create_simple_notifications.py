#!/usr/bin/env python
"""
Script simple pour créer des notifications de test sans WebSockets.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()


def create_notifications_directly():
    """Créer des notifications directement en base sans signaux."""
    print("📧 CRÉATION DIRECTE DE NOTIFICATIONS DE TEST")
    print("=" * 60)
    
    # Import direct du modèle
    from notifications.models import Notification
    
    # Récupérer les utilisateurs
    real_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    notifications_created = 0
    
    with transaction.atomic():
        for email in real_emails:
            user = User.objects.filter(email=email).first()
            
            if user:
                print(f"🔄 Création pour: {email}")
                
                # Supprimer les anciennes notifications de test
                old_count = Notification.objects.filter(
                    user=user,
                    title__contains='Test Simple'
                ).delete()[0]
                
                if old_count > 0:
                    print(f"   🗑️  {old_count} anciennes notifications supprimées")
                
                # Créer directement en base (sans signaux)
                notifications_data = [
                    {
                        'title': '🎉 Test Simple - Promotion Flash !',
                        'message': 'Profitez de 70% de réduction sur tous nos ordinateurs portables. Offre valable 24h seulement !',
                        'type': 'promotion',
                        'is_read': False,
                        'link': '/promotions'
                    },
                    {
                        'title': '📦 Test Simple - Commande confirmée',
                        'message': 'Votre commande #TEST001 a été confirmée. Livraison prévue sous 2-3 jours ouvrables.',
                        'type': 'order',
                        'is_read': False,
                        'link': '/account/orders'
                    },
                    {
                        'title': 'ℹ️ Test Simple - Nouveaux services',
                        'message': 'Découvrez nos nouveaux services de cybersécurité et de cloud computing. Contactez-nous !',
                        'type': 'info',
                        'is_read': True,
                        'link': '/services'
                    }
                ]
                
                for notif_data in notifications_data:
                    # Insertion directe en base
                    notification = Notification(
                        user=user,
                        **notif_data
                    )
                    notification.save()
                    notifications_created += 1
                    print(f"   ✅ {notif_data['title'][:30]}...")
                
            else:
                print(f"   ⚠️  Utilisateur non trouvé: {email}")
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   ✅ Total notifications créées: {notifications_created}")
    
    return notifications_created


def verify_notifications_simple():
    """Vérifier les notifications créées."""
    print(f"\n🔍 VÉRIFICATION DES NOTIFICATIONS")
    print("=" * 60)
    
    from notifications.models import Notification
    
    # Statistiques générales
    total = Notification.objects.count()
    test_simple = Notification.objects.filter(title__contains='Test Simple').count()
    unread = Notification.objects.filter(is_read=False).count()
    
    print(f"📊 Statistiques:")
    print(f"   📧 Total notifications: {total}")
    print(f"   🧪 Test Simple: {test_simple}")
    print(f"   📬 Non lues: {unread}")
    
    # Par utilisateur
    real_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    print(f"\n👥 Par utilisateur:")
    for email in real_emails:
        user = User.objects.filter(email=email).first()
        if user:
            user_total = Notification.objects.filter(user=user).count()
            user_unread = Notification.objects.filter(user=user, is_read=False).count()
            print(f"   📧 {email}: {user_total} total, {user_unread} non lues")
        else:
            print(f"   ⚠️  {email}: Non trouvé")


def test_api_access():
    """Tester l'accès API aux notifications."""
    print(f"\n🌐 TEST D'ACCÈS API")
    print("=" * 60)
    
    from rest_framework.test import APIClient
    from rest_framework_simplejwt.tokens import RefreshToken
    
    # Tester avec un utilisateur
    user = User.objects.filter(email='<EMAIL>').first()
    
    if user:
        # Créer un token
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # Client API
        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Test des endpoints
        endpoints = [
            '/api/v1/notifications/notifications/',
            '/api/v1/notifications/notifications/unread/'
        ]
        
        for endpoint in endpoints:
            try:
                response = client.get(endpoint)
                print(f"   ✅ {endpoint}: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    count = data.get('count', len(data.get('results', [])))
                    print(f"      📊 Notifications: {count}")
                
            except Exception as e:
                print(f"   ❌ {endpoint}: Erreur - {e}")
    else:
        print(f"   ⚠️  Utilisateur de test non trouvé")


def main():
    """Fonction principale."""
    print("🚀 CRÉATION SIMPLE DE NOTIFICATIONS DE TEST")
    print("=" * 80)
    print("Version simplifiée sans WebSockets")
    print("=" * 80)
    
    try:
        # Créer les notifications
        count = create_notifications_directly()
        
        # Vérifier
        verify_notifications_simple()
        
        # Tester l'API
        test_api_access()
        
        print(f"\n" + "=" * 80)
        print("🎉 NOTIFICATIONS DE TEST CRÉÉES !")
        print("=" * 80)
        print(f"✅ {count} notifications créées")
        print(f"\n🧪 TESTS RECOMMANDÉS :")
        print(f"   1. Page de debug: http://localhost:5173/debug/auth")
        print(f"   2. Se connecter avec: <EMAIL>")
        print(f"   3. Tester les notifications dans l'interface")
        print(f"   4. Vérifier la cloche de notification")
        print(f"\n🔧 ENDPOINTS API :")
        print(f"   • GET /api/v1/notifications/notifications/")
        print(f"   • GET /api/v1/notifications/notifications/unread/")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
