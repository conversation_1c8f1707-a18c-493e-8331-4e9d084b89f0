// API error handling utilities

export interface ApiError {
  message: string;
  status?: number;
  details?: Record<string, string[]>;
}

/**
 * Handle API error responses
 * @param response - Fetch Response object
 * @returns ApiError object
 */
export async function handleApiError(response: Response): Promise<ApiError> {
  const status = response.status;
  
  try {
    // Try to parse the error response as JSON
    const data = await response.json();
    
    // Handle different error formats
    if (data.message) {
      return {
        message: data.message,
        status,
        details: data.details
      };
    } else if (data.error) {
      return {
        message: data.error,
        status,
        details: data.details
      };
    } else if (data.detail) {
      return {
        message: data.detail,
        status
      };
    } else if (typeof data === 'string') {
      return {
        message: data,
        status
      };
    }
    
    // Default error message
    return {
      message: `Erreur ${status}: ${response.statusText}`,
      status
    };
  } catch (error) {
    // If response is not JSON, return a generic error
    return {
      message: `Erreur ${status}: ${response.statusText}`,
      status
    };
  }
}
