# Generated by Django 4.2.23 on 2025-07-03 14:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TranslationKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=255, unique=True, verbose_name='Clé')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('category', models.CharField(blank=True, max_length=100, null=True, verbose_name='Catégorie')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='<PERSON><PERSON><PERSON> le')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Modi<PERSON><PERSON> le')),
            ],
            options={
                'verbose_name': 'Clé de traduction',
                'verbose_name_plural': 'Clés de traduction',
                'ordering': ['category', 'key'],
            },
        ),
        migrations.CreateModel(
            name='UserLanguagePreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(choices=[('fr', 'Français'), ('en', 'English'), ('sw', 'Kiswahili'), ('rn', 'Kirundi')], default='fr', max_length=5, verbose_name='Langue préférée')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Créé le')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Modifié le')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='language_preference', to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
            ],
            options={
                'verbose_name': 'Préférence de langue',
                'verbose_name_plural': 'Préférences de langue',
            },
        ),
        migrations.CreateModel(
            name='TranslationCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(max_length=5, verbose_name='Langue')),
                ('translations_json', models.TextField(verbose_name='Traductions JSON')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='Dernière mise à jour')),
            ],
            options={
                'verbose_name': 'Cache de traduction',
                'verbose_name_plural': 'Caches de traduction',
                'unique_together': {('language_code',)},
            },
        ),
        migrations.CreateModel(
            name='Translation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(choices=[('fr', 'Français'), ('en', 'English'), ('sw', 'Kiswahili'), ('rn', 'Kirundi')], max_length=5, verbose_name='Langue')),
                ('value', models.TextField(verbose_name='Traduction')),
                ('is_approved', models.BooleanField(default=False, verbose_name='Approuvé')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Créé le')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Modifié le')),
                ('key', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='translations.translationkey', verbose_name='Clé')),
            ],
            options={
                'verbose_name': 'Traduction',
                'verbose_name_plural': 'Traductions',
                'ordering': ['key__category', 'key__key', 'language_code'],
                'unique_together': {('key', 'language_code')},
            },
        ),
    ]
