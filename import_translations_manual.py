#!/usr/bin/env python3
"""
Script pour importer manuellement les traductions
"""
import os
import sys
import json
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from translations.models import TranslationKey, Translation

def import_translations_from_json(json_file_path):
    """Importer les traductions depuis un fichier JSON"""
    print(f"📥 IMPORTATION DES TRADUCTIONS DEPUIS {json_file_path}")
    print("=" * 60)
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            translations_data = json.load(f)
        
        imported_keys = 0
        imported_translations = 0
        
        for item in translations_data:
            key_name = item['key']
            category = item.get('category', '')
            description = item.get('description', '')
            translations = item.get('translations', {})
            
            # <PERSON><PERSON><PERSON> ou récupérer la clé de traduction
            translation_key, created = TranslationKey.objects.get_or_create(
                key=key_name,
                defaults={
                    'category': category,
                    'description': description,
                    'is_active': True
                }
            )
            
            if created:
                imported_keys += 1
                print(f"✅ Nouvelle clé: {key_name}")
            
            # Ajouter les traductions pour chaque langue
            for lang_code, translation_text in translations.items():
                if lang_code in ['fr', 'en', 'sw', 'rn']:  # Langues supportées
                    translation_obj, created = Translation.objects.get_or_create(
                        key=translation_key,
                        language_code=lang_code,
                        defaults={
                            'value': translation_text,
                            'is_approved': True
                        }
                    )
                    
                    if created:
                        imported_translations += 1
                    elif translation_obj.value != translation_text:
                        # Mettre à jour si différent
                        translation_obj.value = translation_text
                        translation_obj.save()
                        print(f"🔄 Mise à jour: {key_name} ({lang_code})")
        
        print(f"\n📊 RÉSULTATS:")
        print(f"   • Nouvelles clés importées: {imported_keys}")
        print(f"   • Nouvelles traductions importées: {imported_translations}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'importation: {e}")
        import traceback
        traceback.print_exc()
        return False

def import_all_translation_files():
    """Importer tous les fichiers de traduction disponibles"""
    base_path = 'josnet_backend'
    
    translation_files = [
        'initial_translations.json',
        'demo_translations.json',
        'additional_translations.json'
    ]
    
    total_success = 0
    
    for filename in translation_files:
        file_path = os.path.join(base_path, filename)
        if os.path.exists(file_path):
            print(f"\n🔄 Traitement de {filename}...")
            if import_translations_from_json(file_path):
                total_success += 1
            else:
                print(f"❌ Échec pour {filename}")
        else:
            print(f"⚠️ Fichier non trouvé: {filename}")
    
    return total_success

def show_final_stats():
    """Afficher les statistiques finales"""
    print(f"\n🌐 STATISTIQUES FINALES")
    print("=" * 50)
    
    total_keys = TranslationKey.objects.count()
    total_translations = Translation.objects.count()
    
    print(f"📊 TOTAUX:")
    print(f"   • Clés de traduction: {total_keys}")
    print(f"   • Traductions totales: {total_translations}")
    
    # Statistiques par langue
    languages = [
        ('fr', 'Français', '🇫🇷'),
        ('en', 'English', '🇬🇧'),
        ('sw', 'Kiswahili', '🇹🇿'),
        ('rn', 'Kirundi', '🇧🇮'),
    ]
    
    print(f"\n🌍 PAR LANGUE:")
    for code, name, flag in languages:
        count = Translation.objects.filter(language_code=code).count()
        approved = Translation.objects.filter(language_code=code, is_approved=True).count()
        percentage = (count / total_keys * 100) if total_keys > 0 else 0
        print(f"   {flag} {name}: {count}/{total_keys} ({percentage:.1f}%) - {approved} approuvées")

def create_additional_ecommerce_translations():
    """Créer des traductions supplémentaires pour l'e-commerce"""
    print(f"\n🛒 AJOUT DE TRADUCTIONS E-COMMERCE")
    print("=" * 50)
    
    ecommerce_translations = [
        {
            "key": "nav.home",
            "category": "navigation",
            "description": "Lien d'accueil",
            "translations": {
                "fr": "Accueil",
                "en": "Home",
                "sw": "Nyumbani",
                "rn": "Mu rugo"
            }
        },
        {
            "key": "nav.products",
            "category": "navigation", 
            "description": "Lien produits",
            "translations": {
                "fr": "Produits",
                "en": "Products",
                "sw": "Bidhaa",
                "rn": "Ibicuruzwa"
            }
        },
        {
            "key": "nav.cart",
            "category": "navigation",
            "description": "Panier",
            "translations": {
                "fr": "Panier",
                "en": "Cart",
                "sw": "Kikapu",
                "rn": "Igikoni"
            }
        },
        {
            "key": "product.price",
            "category": "product",
            "description": "Prix du produit",
            "translations": {
                "fr": "Prix",
                "en": "Price",
                "sw": "Bei",
                "rn": "Igiciro"
            }
        },
        {
            "key": "product.add_to_cart",
            "category": "product",
            "description": "Ajouter au panier",
            "translations": {
                "fr": "Ajouter au panier",
                "en": "Add to cart",
                "sw": "Ongeza kwenye kikapu",
                "rn": "Shira mu gikoni"
            }
        },
        {
            "key": "order.total",
            "category": "order",
            "description": "Total de la commande",
            "translations": {
                "fr": "Total",
                "en": "Total",
                "sw": "Jumla",
                "rn": "Byose"
            }
        }
    ]
    
    added_count = 0
    
    for item in ecommerce_translations:
        key_name = item['key']
        
        # Vérifier si la clé existe déjà
        if not TranslationKey.objects.filter(key=key_name).exists():
            translation_key = TranslationKey.objects.create(
                key=key_name,
                category=item['category'],
                description=item['description'],
                is_active=True
            )
            
            # Ajouter les traductions
            for lang_code, translation_text in item['translations'].items():
                Translation.objects.create(
                    key=translation_key,
                    language_code=lang_code,
                    value=translation_text,
                    is_approved=True
                )
            
            added_count += 1
            print(f"✅ Ajouté: {key_name}")
    
    print(f"\n📊 {added_count} nouvelles traductions e-commerce ajoutées")

if __name__ == "__main__":
    try:
        print("🚀 IMPORTATION MANUELLE DES TRADUCTIONS")
        print("=" * 60)
        
        # Importer tous les fichiers de traduction
        success_count = import_all_translation_files()
        
        # Ajouter des traductions e-commerce supplémentaires
        create_additional_ecommerce_translations()
        
        # Afficher les statistiques finales
        show_final_stats()
        
        if success_count > 0:
            print(f"\n🎉 IMPORTATION RÉUSSIE!")
            print(f"\n🌐 PROCHAINES ÉTAPES:")
            print(f"   1. Accédez à l'admin Django: http://localhost:8000/admin/translations/")
            print(f"   2. Ou utilisez Rosetta: http://localhost:8000/rosetta/")
            print(f"   3. Testez le sélecteur de langue dans le frontend")
            print(f"   4. Remplacez les textes en dur par t('key', 'fallback')")
        else:
            print(f"\n❌ Aucune importation réussie")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
