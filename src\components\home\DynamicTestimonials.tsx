/**
 * Composant dynamique pour afficher les témoignages depuis l'API
 */

import React from 'react';
import { Star, Quote, Loader2 } from "lucide-react";
import { useTestimonials } from '@/hooks/useHomePageData';

interface TestimonialCardProps {
  testimonial: {
    id: number;
    client_name: string;
    client_title: string;
    client_company: string;
    client_photo_url?: string;
    content: string;
    rating: number;
  };
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({ testimonial }) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
      {/* Quote icon */}
      <div className="flex justify-center mb-4">
        <Quote className="h-8 w-8 text-primary/30" />
      </div>

      {/* Rating */}
      <div className="flex justify-center mb-4">
        {renderStars(testimonial.rating)}
      </div>

      {/* Content */}
      <p className="text-gray-600 text-center mb-6 leading-relaxed italic">
        "{testimonial.content}"
      </p>

      {/* Client info */}
      <div className="text-center">
        {testimonial.client_photo_url && (
          <div className="w-12 h-12 mx-auto mb-3 rounded-full overflow-hidden bg-gray-200">
            <img
              src={testimonial.client_photo_url}
              alt={testimonial.client_name}
              className="w-full h-full object-cover"
              onError={(e) => {
                // Fallback en cas d'erreur de chargement d'image
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          </div>
        )}

        <h4 className="font-semibold text-gray-800 mb-1">
          {testimonial.client_name}
        </h4>
        <p className="text-sm text-gray-500 mb-1">
          {testimonial.client_title}
        </p>
        <p className="text-sm text-primary font-medium">
          {testimonial.client_company}
        </p>
      </div>
    </div>
  );
};

const DynamicTestimonials: React.FC = () => {
  const { testimonials, loading, error } = useTestimonials();

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Ce que disent nos clients</h2>
            <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
          </div>

          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600">Chargement des témoignages...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error || testimonials.length === 0) {
    // Données de fallback temporaires
    const fallbackTestimonials = [
      {
        id: 1,
        client_name: "Jean Baptiste Niyongabo",
        client_title: "Directeur IT",
        client_company: "Banque Commerciale du Burundi",
        content: "JOSNET NETWORK a transformé notre infrastructure IT. Leur expertise et leur professionnalisme sont remarquables. Je recommande vivement leurs services.",
        rating: 5
      },
      {
        id: 2,
        client_name: "Marie Claire Ndayishimiye",
        client_title: "CEO",
        client_company: "TechStart Burundi",
        content: "Une équipe exceptionnelle qui comprend vraiment les besoins des entreprises burundaises. Leur support technique est disponible 24/7.",
        rating: 5
      },
      {
        id: 3,
        client_name: "Pierre Hakizimana",
        client_title: "Responsable Informatique",
        client_company: "Université du Burundi",
        content: "Grâce à JOSNET, nous avons modernisé tout notre réseau universitaire. Les étudiants et le personnel bénéficient maintenant d'une connectivité excellente.",
        rating: 5
      }
    ];

    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Ce que disent nos clients</h2>
            <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              La satisfaction de nos clients est notre priorité. Découvrez leurs témoignages sur notre service et notre expertise.
            </p>
          </div>

          {/* Avertissement API */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8 max-w-2xl mx-auto">
            <p className="text-yellow-800 text-sm text-center">
              ⚠️ Données temporaires affichées - API backend non disponible
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {fallbackTestimonials.map((testimonial) => (
              <TestimonialCard
                key={testimonial.id}
                testimonial={testimonial}
              />
            ))}
          </div>

          {/* Call to action */}
          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">
              Vous aussi, rejoignez nos clients satisfaits !
            </p>
            <div className="flex justify-center space-x-2">
              {Array.from({ length: 5 }, (_, index) => (
                <Star
                  key={index}
                  className="h-6 w-6 text-yellow-400 fill-current"
                />
              ))}
            </div>
            <p className="text-sm text-gray-500 mt-2">
              Note moyenne: 5/5 basée sur {fallbackTestimonials.length} avis
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ce que disent nos clients</h2>
          <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
          <p className="text-gray-600 max-w-2xl mx-auto text-lg">
            La satisfaction de nos clients est notre priorité. Découvrez leurs témoignages sur notre service et notre expertise.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <TestimonialCard
              key={testimonial.id}
              testimonial={testimonial}
            />
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">
            Vous aussi, rejoignez nos clients satisfaits !
          </p>
          <div className="flex justify-center space-x-2">
            {Array.from({ length: 5 }, (_, index) => (
              <Star
                key={index}
                className="h-6 w-6 text-yellow-400 fill-current"
              />
            ))}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Note moyenne: 5/5 basée sur {testimonials.length} avis
          </p>
        </div>
      </div>
    </section>
  );
};

export default DynamicTestimonials;
