import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, AlertCircle, Upload, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { accountApi, ClaimCreateData } from '@/services/accountApi';

// Types pour les commandes
interface Order {
  id: string;
  date: string;
  status: string;
  total: string;
}

interface CreateClaimFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function CreateClaimForm({ isOpen, onOpenChange, onSuccess }: CreateClaimFormProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // État du formulaire
  const [formData, setFormData] = useState<ClaimCreateData>({
    orderId: '',
    subject: '',
    description: '',
    attachments: []
  });

  // État pour les fichiers
  const [files, setFiles] = useState<File[]>([]);
  const [fileError, setFileError] = useState<string | null>(null);

  // Vérifier l'authentification de manière cohérente
  const token = localStorage.getItem('accessToken') || 
               localStorage.getItem('authToken') || 
               localStorage.getItem('token') || 
               sessionStorage.getItem('accessToken') || 
               sessionStorage.getItem('authToken');
  const isAuthenticated = !!token;
  
  // Afficher les informations de débogage dans la console
  console.log('Token dans CreateClaimForm:', token);
  console.log('isAuthenticated dans CreateClaimForm:', isAuthenticated);

  // Récupérer les commandes de l'utilisateur depuis l'API réelle
  const { data, isLoading: isLoadingOrders, error: ordersError } = useQuery({
    queryKey: ['userOrders'],
    queryFn: async () => {
      try {
        // Appel à l'API réelle pour récupérer les commandes de l'utilisateur
        // Nous supposons que cette fonction existe dans accountApi
        const orders = await accountApi.getUserOrders();
        return orders.map(order => ({
          id: order.id,
          date: order.date,
          status: order.status,
          total: order.total
        })) as Order[];
      } catch (error) {
        console.error('Erreur lors de la récupération des commandes:', error);
        throw error;
      }
    },
    enabled: isOpen && isAuthenticated,
    retry: 2, // Réessayer 2 fois en cas d'échec
    staleTime: 24 * 60 * 60 * 1000, // Données considérées fraîches pendant 24 heures
    // Pas de données simulées pour forcer l'utilisation des données réelles
  });

  // S'assurer que orders est toujours un tableau
  const orders = data || [];

  // Mutation pour créer une réclamation
  const createClaimMutation = useMutation({
    mutationFn: (data: ClaimCreateData) => accountApi.createClaim(data),
    onSuccess: () => {
      toast({
        title: 'Réclamation créée',
        description: 'Votre réclamation a été soumise avec succès.',
      });
      onSuccess();
      onOpenChange(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la création de la réclamation.',
        variant: 'destructive',
      });
      setIsLoading(false);
    },
  });

  // Gérer les changements dans le formulaire
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setError(null);
  };

  // Gérer la sélection d'une commande
  const handleOrderSelect = (value: string) => {
    // Ignorer les valeurs spéciales comme "loading", "error", "no-orders"
    if (value === "loading" || value === "error" || value === "no-orders") {
      return;
    }

    setFormData(prev => ({ ...prev, orderId: value }));
    setError(null);
  };

  // Gérer l'upload de fichiers
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles) return;

    // Vérifier la taille des fichiers (max 5MB par fichier)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const invalidFiles = Array.from(selectedFiles).filter(file => file.size > maxSize);

    if (invalidFiles.length > 0) {
      setFileError(`Certains fichiers dépassent la taille maximale de 5MB: ${invalidFiles.map(f => f.name).join(', ')}`);
      return;
    }

    // Vérifier le nombre total de fichiers (max 5)
    if (files.length + selectedFiles.length > 5) {
      setFileError('Vous ne pouvez pas ajouter plus de 5 fichiers.');
      return;
    }

    setFileError(null);
    setFiles(prev => [...prev, ...Array.from(selectedFiles)]);
    setFormData(prev => ({ ...prev, attachments: [...(prev.attachments || []), ...Array.from(selectedFiles)] }));
  };

  // Supprimer un fichier
  const handleRemoveFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments ? prev.attachments.filter((_, i) => i !== index) : []
    }));
    setFileError(null);
  };

  // Soumettre le formulaire
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // Validation
    if (!formData.orderId) {
      setError('Veuillez sélectionner une commande.');
      setIsLoading(false);
      return;
    }

    if (!formData.subject) {
      setError('Veuillez saisir un sujet pour votre réclamation.');
      setIsLoading(false);
      return;
    }

    if (!formData.description) {
      setError('Veuillez décrire votre problème.');
      setIsLoading(false);
      return;
    }

    // Soumettre la réclamation
    createClaimMutation.mutate(formData);
  };

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      orderId: '',
      subject: '',
      description: '',
      attachments: []
    });
    setFiles([]);
    setFileError(null);
    setError(null);
    setIsLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) resetForm();
      onOpenChange(open);
    }}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Nouvelle réclamation</DialogTitle>
          <DialogDescription>
            Créez une nouvelle réclamation pour signaler un problème avec votre commande.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-start gap-2 mb-4">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="order">Commande concernée</Label>
            <Select value={formData.orderId} onValueChange={handleOrderSelect}>
              <SelectTrigger id="order" className="w-full">
                <SelectValue placeholder="Sélectionner une commande" />
              </SelectTrigger>
              <SelectContent>
                {isLoadingOrders ? (
                  <SelectItem value="loading" disabled>
                    Chargement des commandes...
                  </SelectItem>
                ) : ordersError ? (
                  <SelectItem value="error" disabled>
                    Erreur lors du chargement des commandes
                  </SelectItem>
                ) : Array.isArray(orders) && orders.length > 0 ? (
                  orders.map(order => (
                    <SelectItem key={order.id} value={order.id} textValue={`Commande ${order.id} du ${order.date}`}>
                      Commande {order.id} - {order.date} ({order.total})
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-orders" disabled>
                    Aucune commande disponible
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="subject">Sujet</Label>
            <Input
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              placeholder="Ex: Produit endommagé, Article manquant..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description du problème</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Décrivez votre problème en détail..."
              rows={5}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="attachments">Pièces jointes (optionnel)</Label>
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('file-upload')?.click()}
                className="flex items-center gap-1"
              >
                <Upload className="h-4 w-4" />
                Ajouter des fichiers
              </Button>
              <Input
                id="file-upload"
                type="file"
                multiple
                className="hidden"
                onChange={handleFileChange}
                accept="image/jpeg,image/png,image/gif,application/pdf"
              />
              <p className="text-xs text-gray-500">
                Max 5 fichiers, 5MB par fichier (JPG, PNG, GIF, PDF)
              </p>
            </div>

            {fileError && (
              <p className="text-sm text-red-500 mt-1">{fileError}</p>
            )}

            {files.length > 0 && (
              <div className="mt-2 space-y-2">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="font-medium truncate max-w-[200px]">{file.name}</span>
                      <span className="text-gray-500">({(file.size / 1024).toFixed(1)} KB)</span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveFile(index)}
                      className="h-6 w-6"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Soumission...
                </>
              ) : (
                'Soumettre la réclamation'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
