import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Edit, Trash2, Star, StarOff } from "lucide-react";
import user<PERSON><PERSON>, { UserAddress, AddressCreateUpdate } from "@/services/userApi";

const AddressManager = () => {
  const [isAddressDialogOpen, setIsAddressDialogOpen] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<UserAddress | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<AddressCreateUpdate>({
    address_type: 'both',
    is_default: false,
    first_name: '',
    last_name: '',
    address_line1: '',
    city: '',
    postal_code: '',
    country: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch addresses
  const { data: addresses, isLoading } = useQuery({
    queryKey: ['userAddresses'],
    queryFn: userApi.getAddresses
  });

  // Create address mutation
  const createAddressMutation = useMutation({
    mutationFn: userApi.createAddress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      toast({
        title: "Adresse créée",
        description: "Votre adresse a été ajoutée avec succès.",
      });
      setIsAddressDialogOpen(false);
      resetForm();
    },
    onError: (error) => {
      toast({
        title: "Erreur",
        description: "Impossible de créer l'adresse. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  });

  // Update address mutation
  const updateAddressMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<AddressCreateUpdate> }) => 
      userApi.updateAddress(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      toast({
        title: "Adresse mise à jour",
        description: "Votre adresse a été mise à jour avec succès.",
      });
      setIsAddressDialogOpen(false);
      resetForm();
    },
    onError: (error) => {
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour l'adresse. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  });

  // Delete address mutation
  const deleteAddressMutation = useMutation({
    mutationFn: userApi.deleteAddress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      toast({
        title: "Adresse supprimée",
        description: "Votre adresse a été supprimée avec succès.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer l'adresse. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  });

  // Set default address mutation
  const setDefaultAddressMutation = useMutation({
    mutationFn: userApi.setDefaultAddress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      toast({
        title: "Adresse par défaut",
        description: "Votre adresse par défaut a été mise à jour.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erreur",
        description: "Impossible de définir l'adresse par défaut. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, is_default: checked }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (currentAddress) {
      updateAddressMutation.mutate({ 
        id: currentAddress.id, 
        data: formData 
      });
    } else {
      createAddressMutation.mutate(formData);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      address_type: 'both',
      is_default: false,
      first_name: '',
      last_name: '',
      address_line1: '',
      city: '',
      postal_code: '',
      country: ''
    });
    setCurrentAddress(null);
    setIsSubmitting(false);
  };

  // Open dialog for editing
  const handleEditAddress = (address: UserAddress) => {
    setCurrentAddress(address);
    setFormData({
      address_type: address.address_type,
      is_default: address.is_default,
      first_name: address.first_name,
      last_name: address.last_name,
      company: address.company || undefined,
      address_line1: address.address_line1,
      address_line2: address.address_line2 || undefined,
      city: address.city,
      state: address.state || undefined,
      postal_code: address.postal_code,
      country: address.country,
      phone: address.phone || undefined
    });
    setIsAddressDialogOpen(true);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsAddressDialogOpen(false);
    resetForm();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Mes adresses</h2>
        <Button onClick={() => setIsAddressDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Ajouter une adresse
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : addresses && addresses.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map((address) => (
            <Card key={address.id} className="relative">
              {address.is_default && (
                <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-md text-xs">
                  Par défaut
                </div>
              )}
              <CardHeader>
                <CardTitle>{address.first_name} {address.last_name}</CardTitle>
                <CardDescription>
                  {address.address_type === 'shipping' ? 'Adresse de livraison' : 
                   address.address_type === 'billing' ? 'Adresse de facturation' : 
                   'Adresse de livraison et facturation'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{address.address_line1}</p>
                {address.address_line2 && <p className="text-sm">{address.address_line2}</p>}
                <p className="text-sm">{address.postal_code} {address.city}</p>
                {address.state && <p className="text-sm">{address.state}</p>}
                <p className="text-sm">{address.country}</p>
                {address.phone && <p className="text-sm mt-2">Tél: {address.phone}</p>}
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleEditAddress(address)}
                  >
                    <Edit className="h-4 w-4 mr-1" /> Modifier
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="text-destructive" 
                    onClick={() => deleteAddressMutation.mutate(address.id)}
                  >
                    <Trash2 className="h-4 w-4 mr-1" /> Supprimer
                  </Button>
                </div>
                {!address.is_default && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setDefaultAddressMutation.mutate(address.id)}
                  >
                    <Star className="h-4 w-4 mr-1" /> Définir par défaut
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">Vous n'avez pas encore d'adresse enregistrée.</p>
            <Button className="mt-4" onClick={() => setIsAddressDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Ajouter une adresse
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Address Dialog */}
      <Dialog open={isAddressDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {currentAddress ? "Modifier l'adresse" : "Ajouter une adresse"}
            </DialogTitle>
            <DialogDescription>
              {currentAddress 
                ? "Modifiez les informations de votre adresse ci-dessous." 
                : "Remplissez les informations pour ajouter une nouvelle adresse."}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="first_name">Prénom</Label>
                  <Input
                    id="first_name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="last_name">Nom</Label>
                  <Input
                    id="last_name"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="company">Entreprise (optionnel)</Label>
                <Input
                  id="company"
                  name="company"
                  value={formData.company || ''}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address_line1">Adresse</Label>
                <Input
                  id="address_line1"
                  name="address_line1"
                  value={formData.address_line1}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address_line2">Complément d'adresse (optionnel)</Label>
                <Input
                  id="address_line2"
                  name="address_line2"
                  value={formData.address_line2 || ''}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="postal_code">Code postal</Label>
                  <Input
                    id="postal_code"
                    name="postal_code"
                    value={formData.postal_code}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="city">Ville</Label>
                  <Input
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="state">État/Province (optionnel)</Label>
                  <Input
                    id="state"
                    name="state"
                    value={formData.state || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="country">Pays</Label>
                  <Input
                    id="country"
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Téléphone (optionnel)</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone || ''}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address_type">Type d'adresse</Label>
                <Select 
                  name="address_type" 
                  value={formData.address_type} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, address_type: value as 'shipping' | 'billing' | 'both' }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="shipping">Adresse de livraison</SelectItem>
                    <SelectItem value="billing">Adresse de facturation</SelectItem>
                    <SelectItem value="both">Les deux</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="is_default" 
                  checked={formData.is_default} 
                  onCheckedChange={handleCheckboxChange} 
                />
                <Label htmlFor="is_default">Définir comme adresse par défaut</Label>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleDialogClose}>
                Annuler
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Enregistrement...
                  </>
                ) : currentAddress ? (
                  "Mettre à jour"
                ) : (
                  "Ajouter"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AddressManager;
