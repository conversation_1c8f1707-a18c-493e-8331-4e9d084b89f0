import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Percent, 
  DollarSign, 
  Calendar, 
  Tag, 
  ShoppingCart, 
  RefreshCw, 
  AlertCircle 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';
import { formatCurrency } from '@/utils/currency';

// Types
interface Promotion {
  id: number;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  applies_to: 'product' | 'category' | 'cart';
  target_id?: number;
  target_name?: string;
  min_purchase_amount?: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Mock API service (replace with actual API calls)
const promotionApi = {
  getPromotions: async (): Promise<Promotion[]> => {
    // Simulate API call
    return [
      {
        id: 1,
        name: 'Soldes d\'été',
        description: 'Remise de 20% sur tous les produits d\'été',
        discount_type: 'percentage',
        discount_value: 20,
        applies_to: 'category',
        target_id: 5,
        target_name: 'Été',
        start_date: '2023-06-01T00:00:00Z',
        end_date: '2023-08-31T23:59:59Z',
        is_active: true,
        created_at: '2023-05-15T10:00:00Z',
        updated_at: '2023-05-15T10:00:00Z'
      },
      {
        id: 2,
        name: 'Remise sur le produit vedette',
        description: `Remise fixe de ${formatCurrency(15)} sur le produit vedette`,
        discount_type: 'fixed_amount',
        discount_value: 15,
        applies_to: 'product',
        target_id: 42,
        target_name: 'Produit Vedette XYZ',
        start_date: '2023-07-01T00:00:00Z',
        is_active: true,
        created_at: '2023-06-15T14:30:00Z',
        updated_at: '2023-06-15T14:30:00Z'
      },
      {
        id: 3,
        name: 'Remise panier',
        description: `Remise de 10% sur les commandes de plus de ${formatCurrency(100)}`,
        discount_type: 'percentage',
        discount_value: 10,
        applies_to: 'cart',
        min_purchase_amount: 100,
        start_date: '2023-07-15T00:00:00Z',
        end_date: '2023-09-15T23:59:59Z',
        is_active: true,
        created_at: '2023-07-01T09:15:00Z',
        updated_at: '2023-07-01T09:15:00Z'
      }
    ];
  },
  createPromotion: async (promotion: Omit<Promotion, 'id' | 'created_at' | 'updated_at'>): Promise<Promotion> => {
    // Simulate API call
    return {
      id: Math.floor(Math.random() * 1000) + 10,
      ...promotion,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  },
  updatePromotion: async (id: number, promotion: Partial<Promotion>): Promise<Promotion> => {
    // Simulate API call
    return {
      id,
      name: 'Updated Promotion',
      description: 'Updated description',
      discount_type: promotion.discount_type || 'percentage',
      discount_value: promotion.discount_value || 0,
      applies_to: promotion.applies_to || 'product',
      start_date: promotion.start_date || new Date().toISOString(),
      is_active: promotion.is_active !== undefined ? promotion.is_active : true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: new Date().toISOString()
    };
  },
  deletePromotion: async (id: number): Promise<void> => {
    // Simulate API call
    return Promise.resolve();
  }
};

interface PromotionManagerProps {
  productId?: number;
  categoryId?: number;
}

const PromotionManager: React.FC<PromotionManagerProps> = ({ productId, categoryId }) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPromotion, setSelectedPromotion] = useState<Promotion | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch promotions
  const { 
    data: promotions = [], 
    isLoading, 
    isError 
  } = useQuery({
    queryKey: ['promotions'],
    queryFn: () => promotionApi.getPromotions(),
  });
  
  // Create promotion mutation
  const createPromotionMutation = useMutation({
    mutationFn: (data: Omit<Promotion, 'id' | 'created_at' | 'updated_at'>) => 
      promotionApi.createPromotion(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: 'Promotion créée',
        description: 'La promotion a été créée avec succès.',
      });
      setIsCreateDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la création de la promotion.',
        variant: 'destructive',
      });
    },
  });
  
  // Update promotion mutation
  const updatePromotionMutation = useMutation({
    mutationFn: (data: { id: number, promotion: Partial<Promotion> }) => 
      promotionApi.updatePromotion(data.id, data.promotion),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: 'Promotion mise à jour',
        description: 'La promotion a été mise à jour avec succès.',
      });
      setIsCreateDialogOpen(false);
      setSelectedPromotion(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la mise à jour de la promotion.',
        variant: 'destructive',
      });
    },
  });
  
  // Delete promotion mutation
  const deletePromotionMutation = useMutation({
    mutationFn: (id: number) => promotionApi.deletePromotion(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: 'Promotion supprimée',
        description: 'La promotion a été supprimée avec succès.',
      });
      setIsDeleteDialogOpen(false);
      setSelectedPromotion(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la suppression de la promotion.',
        variant: 'destructive',
      });
    },
  });
  
  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Indéfini';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };
  
  // Filter promotions based on props
  const filteredPromotions = promotions.filter(promo => {
    if (productId && promo.applies_to === 'product' && promo.target_id === productId) {
      return true;
    }
    if (categoryId && promo.applies_to === 'category' && promo.target_id === categoryId) {
      return true;
    }
    if (!productId && !categoryId) {
      return true;
    }
    return false;
  });
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Promotions</CardTitle>
            <CardDescription>
              Gérez les promotions et les remises pour vos produits
            </CardDescription>
          </div>
          <Button onClick={() => {
            setSelectedPromotion(null);
            setIsCreateDialogOpen(true);
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle promotion
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : isError ? (
          <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span>Une erreur est survenue lors du chargement des promotions.</span>
          </div>
        ) : filteredPromotions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Tag className="h-12 w-12 mx-auto mb-2 text-gray-400" />
            <p>Aucune promotion trouvée.</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => {
                setSelectedPromotion(null);
                setIsCreateDialogOpen(true);
              }}
            >
              Créer une promotion
            </Button>
          </div>
        ) : (
          <div className="border rounded-md overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Valeur</TableHead>
                  <TableHead>Période</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPromotions.map((promotion) => (
                  <TableRow key={promotion.id}>
                    <TableCell>
                      <div className="font-medium">{promotion.name}</div>
                      {promotion.description && (
                        <div className="text-sm text-gray-500">{promotion.description}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {promotion.applies_to === 'product' && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          <span>Produit</span>
                        </Badge>
                      )}
                      {promotion.applies_to === 'category' && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          <span>Catégorie</span>
                        </Badge>
                      )}
                      {promotion.applies_to === 'cart' && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <ShoppingCart className="h-3 w-3" />
                          <span>Panier</span>
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {promotion.discount_type === 'percentage' ? (
                        <div className="flex items-center">
                          <Percent className="h-4 w-4 mr-1 text-green-600" />
                          <span>{promotion.discount_value}%</span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                          <span>{formatPrice(promotion.discount_value)}</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                        <span>
                          {formatDate(promotion.start_date)}
                          {promotion.end_date && ` - ${formatDate(promotion.end_date)}`}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {promotion.is_active ? (
                        <Badge className="bg-green-500">Actif</Badge>
                      ) : (
                        <Badge variant="outline">Inactif</Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedPromotion(promotion);
                            setIsCreateDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-600"
                          onClick={() => {
                            setSelectedPromotion(promotion);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      
      {/* Create/Edit Promotion Dialog */}
      <PromotionFormDialog 
        isOpen={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        promotion={selectedPromotion}
        onSubmit={(data) => {
          if (selectedPromotion) {
            updatePromotionMutation.mutate({
              id: selectedPromotion.id,
              promotion: data
            });
          } else {
            createPromotionMutation.mutate(data as any);
          }
        }}
        isLoading={createPromotionMutation.isPending || updatePromotionMutation.isPending}
        productId={productId}
        categoryId={categoryId}
      />
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer la promotion "{selectedPromotion?.name}" ?
              Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Annuler
            </Button>
            <Button 
              variant="destructive"
              onClick={() => selectedPromotion && deletePromotionMutation.mutate(selectedPromotion.id)}
              disabled={deletePromotionMutation.isPending}
            >
              {deletePromotionMutation.isPending ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Suppression...
                </>
              ) : (
                'Supprimer'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

interface PromotionFormDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  promotion: Promotion | null;
  onSubmit: (data: any) => void;
  isLoading: boolean;
  productId?: number;
  categoryId?: number;
}

const PromotionFormDialog: React.FC<PromotionFormDialogProps> = ({
  isOpen,
  onOpenChange,
  promotion,
  onSubmit,
  isLoading,
  productId,
  categoryId
}) => {
  // Form state
  const [name, setName] = useState(promotion?.name || '');
  const [description, setDescription] = useState(promotion?.description || '');
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed_amount'>(
    promotion?.discount_type || 'percentage'
  );
  const [discountValue, setDiscountValue] = useState(
    promotion?.discount_value?.toString() || '0'
  );
  const [appliesTo, setAppliesTo] = useState<'product' | 'category' | 'cart'>(
    promotion?.applies_to || (productId ? 'product' : categoryId ? 'category' : 'product')
  );
  const [minPurchaseAmount, setMinPurchaseAmount] = useState(
    promotion?.min_purchase_amount?.toString() || ''
  );
  const [startDate, setStartDate] = useState(
    promotion?.start_date ? new Date(promotion.start_date).toISOString().split('T')[0] : 
    new Date().toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState(
    promotion?.end_date ? new Date(promotion.end_date).toISOString().split('T')[0] : ''
  );
  const [isActive, setIsActive] = useState(promotion?.is_active !== false);
  
  // Reset form when dialog opens or promotion changes
  React.useEffect(() => {
    if (isOpen) {
      setName(promotion?.name || '');
      setDescription(promotion?.description || '');
      setDiscountType(promotion?.discount_type || 'percentage');
      setDiscountValue(promotion?.discount_value?.toString() || '0');
      setAppliesTo(promotion?.applies_to || (productId ? 'product' : categoryId ? 'category' : 'product'));
      setMinPurchaseAmount(promotion?.min_purchase_amount?.toString() || '');
      setStartDate(
        promotion?.start_date ? new Date(promotion.start_date).toISOString().split('T')[0] : 
        new Date().toISOString().split('T')[0]
      );
      setEndDate(
        promotion?.end_date ? new Date(promotion.end_date).toISOString().split('T')[0] : ''
      );
      setIsActive(promotion?.is_active !== false);
    }
  }, [isOpen, promotion, productId, categoryId]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const formData = {
      name,
      description: description || undefined,
      discount_type: discountType,
      discount_value: parseFloat(discountValue),
      applies_to: appliesTo,
      target_id: appliesTo === 'product' ? productId : appliesTo === 'category' ? categoryId : undefined,
      min_purchase_amount: appliesTo === 'cart' && minPurchaseAmount ? parseFloat(minPurchaseAmount) : undefined,
      start_date: `${startDate}T00:00:00Z`,
      end_date: endDate ? `${endDate}T23:59:59Z` : undefined,
      is_active: isActive
    };
    
    onSubmit(formData);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>{promotion ? 'Modifier la promotion' : 'Créer une promotion'}</DialogTitle>
          <DialogDescription>
            {promotion 
              ? 'Modifiez les détails de la promotion existante.' 
              : 'Créez une nouvelle promotion pour vos produits.'}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Nom
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Input
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="discount-type" className="text-right">
                Type de remise
              </Label>
              <Select
                value={discountType}
                onValueChange={(value: 'percentage' | 'fixed_amount') => setDiscountType(value)}
              >
                <SelectTrigger id="discount-type" className="col-span-3">
                  <SelectValue placeholder="Sélectionnez un type de remise" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Pourcentage (%)</SelectItem>
                  <SelectItem value="fixed_amount">Montant fixe (BIF)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="discount-value" className="text-right">
                Valeur de remise
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="discount-value"
                  type="number"
                  min="0"
                  step={discountType === 'percentage' ? '1' : '0.01'}
                  value={discountValue}
                  onChange={(e) => setDiscountValue(e.target.value)}
                  required
                />
                <span>{discountType === 'percentage' ? '%' : 'BIF'}</span>
              </div>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="applies-to" className="text-right">
                S'applique à
              </Label>
              <Select
                value={appliesTo}
                onValueChange={(value: 'product' | 'category' | 'cart') => setAppliesTo(value)}
                disabled={!!productId || !!categoryId}
              >
                <SelectTrigger id="applies-to" className="col-span-3">
                  <SelectValue placeholder="Sélectionnez où s'applique la remise" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="product">Produit spécifique</SelectItem>
                  <SelectItem value="category">Catégorie</SelectItem>
                  <SelectItem value="cart">Panier entier</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {appliesTo === 'cart' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="min-purchase" className="text-right">
                  Montant minimum
                </Label>
                <div className="col-span-3 flex items-center gap-2">
                  <Input
                    id="min-purchase"
                    type="number"
                    min="0"
                    step="0.01"
                    value={minPurchaseAmount}
                    onChange={(e) => setMinPurchaseAmount(e.target.value)}
                    placeholder="Optionnel"
                  />
                  <span>BIF</span>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="start-date" className="text-right">
                Date de début
              </Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="end-date" className="text-right">
                Date de fin
              </Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="col-span-3"
                placeholder="Optionnel"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Statut
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Checkbox
                  id="is-active"
                  checked={isActive}
                  onCheckedChange={(checked) => setIsActive(!!checked)}
                />
                <label
                  htmlFor="is-active"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Actif
                </label>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  {promotion ? 'Mise à jour...' : 'Création...'}
                </>
              ) : (
                promotion ? 'Mettre à jour' : 'Créer'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PromotionManager;
