
import { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  ClipboardList, 
  Search, 
  Filter,
  ChevronDown,
  AlertCircle,
  Info,
  CheckCircle,
  Download,
  RefreshCcw
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

// Données fictives pour les logs
const mockLogs = [
  { 
    id: "LOG-00001", 
    timestamp: "15/05/2025 15:42:18", 
    type: "info", 
    module: "Utilisateurs",
    message: "Connexion utilisateur : <EMAIL>",
    ip: "***********5",
    user: "<EMAIL>"
  },
  { 
    id: "LOG-00002", 
    timestamp: "15/05/2025 15:30:45", 
    type: "success", 
    module: "Commandes",
    message: "Commande #ORD-12345 validée et en cours de préparation",
    ip: "***********",
    user: "<EMAIL>"
  },
  { 
    id: "LOG-00003", 
    timestamp: "15/05/2025 14:56:33", 
    type: "error", 
    module: "Paiement",
    message: "Échec de la transaction - ID#TRX-7890 - Carte refusée",
    ip: "************",
    user: "<EMAIL>"
  },
  { 
    id: "LOG-00004", 
    timestamp: "15/05/2025 14:23:11", 
    type: "warning", 
    module: "Stocks",
    message: "Produit #PRD-004 en rupture de stock",
    ip: "***********",
    user: "Système"
  },
  { 
    id: "LOG-00005", 
    timestamp: "15/05/2025 13:15:44", 
    type: "info", 
    module: "CMS",
    message: "Page 'À propos' mise à jour",
    ip: "***********",
    user: "<EMAIL>"
  },
  { 
    id: "LOG-00006", 
    timestamp: "15/05/2025 12:48:22", 
    type: "success", 
    module: "Promotions",
    message: "Nouveau code promo 'ETE25' créé",
    ip: "***********",
    user: "<EMAIL>"
  },
  { 
    id: "LOG-00007", 
    timestamp: "15/05/2025 11:36:07", 
    type: "error", 
    module: "Serveur",
    message: "Erreur connexion base de données - Résolue après 12s",
    ip: "***********",
    user: "Système"
  },
  { 
    id: "LOG-00008", 
    timestamp: "15/05/2025 10:22:51", 
    type: "info", 
    module: "Utilisateurs",
    message: "Nouvel utilisateur créé : <EMAIL>",
    ip: "************",
    user: "<EMAIL>"
  },
  { 
    id: "LOG-00009", 
    timestamp: "15/05/2025 09:14:38", 
    type: "warning", 
    module: "Sécurité",
    message: "Tentatives multiples de connexion échouées - IP bloquée",
    ip: "************",
    user: "Système"
  },
  { 
    id: "LOG-00010", 
    timestamp: "15/05/2025 08:05:12", 
    type: "info", 
    module: "Système",
    message: "Sauvegarde quotidienne terminée avec succès",
    ip: "***********",
    user: "Système"
  },
];

const Logs = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState<string | null>(null);

  // Fonction de filtrage pour la recherche et le type
  const filteredLogs = mockLogs.filter(log => {
    const matchesSearch = searchQuery === "" || 
      log.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.module.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.ip.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = selectedType === null || log.type === selectedType;
    
    return matchesSearch && matchesType;
  });

  const handleRefreshLogs = () => {
    toast({
      title: "Logs rafraîchis",
      description: "La liste des logs a été mise à jour",
    });
    // Dans une app réelle, cette fonction rechargerait les logs du serveur
  };

  const handleDownloadLogs = () => {
    toast({
      title: "Téléchargement des logs",
      description: "Les logs sont en cours d'export au format CSV",
    });
    // Dans une app réelle, cette fonction téléchargerait un fichier CSV
  };

  const handleFilterByType = (type: string | null) => {
    setSelectedType(type);
  };

  const getLogIcon = (type: string) => {
    switch (type) {
      case "info":
        return <Info size={16} className="text-blue-500" />;
      case "success":
        return <CheckCircle size={16} className="text-green-500" />;
      case "warning":
        return <AlertCircle size={16} className="text-amber-500" />;
      case "error":
        return <AlertCircle size={16} className="text-red-500" />;
      default:
        return <Info size={16} className="text-gray-500" />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Journalisation des Activités</h1>
          <p className="text-gray-500">Consultez les journaux d'activité et les événements du système</p>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { type: "info", count: mockLogs.filter(l => l.type === "info").length, color: "bg-blue-100 text-blue-800" },
            { type: "success", count: mockLogs.filter(l => l.type === "success").length, color: "bg-green-100 text-green-800" },
            { type: "warning", count: mockLogs.filter(l => l.type === "warning").length, color: "bg-amber-100 text-amber-800" },
            { type: "error", count: mockLogs.filter(l => l.type === "error").length, color: "bg-red-100 text-red-800" }
          ].map(stat => (
            <div 
              key={stat.type} 
              className={`p-4 rounded-lg border flex items-center justify-between ${
                selectedType === stat.type ? stat.color : "bg-white"
              }`}
              onClick={() => handleFilterByType(selectedType === stat.type ? null : stat.type)}
              role="button"
            >
              <div>
                <p className="text-sm font-medium capitalize">{stat.type}</p>
                <p className="text-2xl font-bold">{stat.count}</p>
              </div>
              <div>{getLogIcon(stat.type)}</div>
            </div>
          ))}
        </div>

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher dans les logs..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-1">
              <Filter size={16} />
              Module
              <ChevronDown size={14} />
            </Button>
            <Button onClick={handleRefreshLogs} variant="outline" className="flex items-center gap-1">
              <RefreshCcw size={16} />
              Rafraîchir
            </Button>
            <Button onClick={handleDownloadLogs} className="flex items-center gap-1">
              <Download size={16} />
              Exporter
            </Button>
          </div>
        </div>

        {/* Tableau des logs */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Horodatage</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Module</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>IP</TableHead>
                <TableHead>Utilisateur</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="font-mono text-xs">{log.id}</TableCell>
                  <TableCell className="whitespace-nowrap">{log.timestamp}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1.5">
                      {getLogIcon(log.type)}
                      <span className="capitalize">{log.type}</span>
                    </div>
                  </TableCell>
                  <TableCell>{log.module}</TableCell>
                  <TableCell className="max-w-xs truncate">{log.message}</TableCell>
                  <TableCell className="font-mono text-xs">{log.ip}</TableCell>
                  <TableCell>{log.user}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Indication du nombre de logs */}
        <div className="text-sm text-gray-500">
          Affichage de {filteredLogs.length} logs sur {mockLogs.length} (dernières 24 heures)
        </div>
      </div>
    </AdminLayout>
  );
};

export default Logs;
