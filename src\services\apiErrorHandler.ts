import { toast } from '@/hooks/use-toast';

/**
 * Service centralisé pour la gestion des erreurs API
 */

export interface ApiErrorResponse {
  message: string;
  status: number;
  details?: any;
}

export interface ApiErrorHandlerOptions {
  showToast?: boolean;
  fallbackData?: any;
  context?: string;
  retryable?: boolean;
}

/**
 * Gère les erreurs API de manière centralisée
 */
export const handleApiError = (
  error: any,
  options: ApiErrorHandlerOptions = {}
): ApiErrorResponse => {
  const {
    showToast = true,
    fallbackData = null,
    context = '',
    retryable = false
  } = options;

  console.group(`❌ Erreur API ${context ? `- ${context}` : ''}`);
  console.error('Erreur complète:', error);

  let errorResponse: ApiErrorResponse;

  // Analyser le type d'erreur
  if (error.response) {
    // Erreur de réponse HTTP (4xx, 5xx)
    const status = error.response.status;
    const data = error.response.data;

    console.log('Status:', status);
    console.log('Data:', data);

    switch (status) {
      case 401:
        errorResponse = {
          message: 'Session expirée. Veuillez vous reconnecter.',
          status,
          details: data
        };
        break;
      case 403:
        errorResponse = {
          message: 'Accès non autorisé à cette ressource.',
          status,
          details: data
        };
        break;
      case 404:
        errorResponse = {
          message: 'Ressource non trouvée.',
          status,
          details: data
        };
        break;
      case 422:
        errorResponse = {
          message: data?.message || 'Données invalides.',
          status,
          details: data
        };
        break;
      case 500:
        errorResponse = {
          message: 'Erreur interne du serveur. Veuillez réessayer plus tard.',
          status,
          details: data
        };
        break;
      default:
        errorResponse = {
          message: data?.message || `Erreur HTTP ${status}`,
          status,
          details: data
        };
    }
  } else if (error.request) {
    // Erreur de réseau (pas de réponse)
    console.log('Erreur de réseau:', error.request);
    errorResponse = {
      message: 'Impossible de contacter le serveur. Vérifiez votre connexion.',
      status: 0,
      details: { type: 'network_error' }
    };
  } else {
    // Autre erreur
    console.log('Autre erreur:', error.message);
    errorResponse = {
      message: error.message || 'Une erreur inattendue s\'est produite.',
      status: -1,
      details: { type: 'unknown_error' }
    };
  }

  console.groupEnd();

  // Afficher un toast si demandé
  if (showToast) {
    toast({
      title: "Erreur",
      description: errorResponse.message,
      variant: "destructive",
    });
  }

  return errorResponse;
};

/**
 * Wrapper pour les appels API avec gestion d'erreur automatique
 */
export const withErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  options: ApiErrorHandlerOptions & { fallbackData: T }
): Promise<T> => {
  try {
    console.log(`🔍 Appel API: ${options.context || 'Non spécifié'}`);
    const result = await apiCall();
    console.log(`✅ Succès API: ${options.context || 'Non spécifié'}`);
    return result;
  } catch (error) {
    console.log(`❌ Échec API: ${options.context || 'Non spécifié'}`);
    
    const errorResponse = handleApiError(error, {
      ...options,
      showToast: options.showToast !== false // Par défaut true
    });

    // Retourner les données de fallback si disponibles
    if (options.fallbackData !== undefined) {
      console.log(`🎭 Utilisation des données de fallback pour: ${options.context}`);
      return options.fallbackData;
    }

    // Sinon, relancer l'erreur
    throw errorResponse;
  }
};

/**
 * Détermine si une erreur est récupérable (peut être retentée)
 */
export const isRetryableError = (error: ApiErrorResponse): boolean => {
  // Erreurs de réseau et erreurs serveur 5xx sont généralement récupérables
  return error.status === 0 || (error.status >= 500 && error.status < 600);
};

/**
 * Détermine si une erreur nécessite une reconnexion
 */
export const requiresReauth = (error: ApiErrorResponse): boolean => {
  return error.status === 401;
};

/**
 * Formate un message d'erreur pour l'affichage utilisateur
 */
export const formatErrorMessage = (error: ApiErrorResponse, context?: string): string => {
  let message = error.message;
  
  if (context) {
    message = `${context}: ${message}`;
  }

  // Ajouter des suggestions d'action si approprié
  if (error.status === 0) {
    message += ' Vérifiez votre connexion internet.';
  } else if (error.status >= 500) {
    message += ' Veuillez réessayer dans quelques instants.';
  }

  return message;
};

/**
 * Log détaillé d'une erreur pour le débogage
 */
export const logErrorDetails = (error: any, context: string) => {
  if (process.env.NODE_ENV !== 'development') return;

  console.group(`🐛 Debug Erreur - ${context}`);
  console.log('Type d\'erreur:', typeof error);
  console.log('Message:', error.message);
  console.log('Stack:', error.stack);
  
  if (error.response) {
    console.log('Response Status:', error.response.status);
    console.log('Response Headers:', error.response.headers);
    console.log('Response Data:', error.response.data);
  }
  
  if (error.request) {
    console.log('Request:', error.request);
  }
  
  console.log('Config:', error.config);
  console.groupEnd();
};

export default {
  handleApiError,
  withErrorHandling,
  isRetryableError,
  requiresReauth,
  formatErrorMessage,
  logErrorDetails
};
