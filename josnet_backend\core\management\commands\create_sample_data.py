"""
Commande pour créer des données d'exemple pour tester les statistiques dynamiques.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from core.models import (
    SiteSettings, HomePageStats, Service, Testimonial,
    NewsletterSubscriber, ContactMessage, FAQ, Banner
)
from products.models import Product, Category
from orders.models import Order, OrderItem

User = get_user_model()


class Command(BaseCommand):
    help = 'Crée des données d\'exemple pour tester les statistiques dynamiques'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Supprime toutes les données existantes avant de créer les nouvelles',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Suppression des données existantes...')
            self.reset_data()

        self.stdout.write('Création des données d\'exemple...')
        
        # Créer les paramètres du site
        self.create_site_settings()
        
        # Créer les services
        self.create_services()
        
        # Créer les témoignages
        self.create_testimonials()
        
        # Créer des utilisateurs
        self.create_users()
        
        # Créer des catégories et produits
        self.create_products()
        
        # Créer des commandes
        self.create_orders()
        
        # Créer des abonnés newsletter
        self.create_newsletter_subscribers()
        
        # Créer des messages de contact
        self.create_contact_messages()
        
        # Créer des FAQ
        self.create_faqs()
        
        # Créer des bannières
        self.create_banners()

        self.stdout.write(
            self.style.SUCCESS('Données d\'exemple créées avec succès!')
        )

    def reset_data(self):
        """Supprime toutes les données existantes."""
        Order.objects.all().delete()
        Product.objects.all().delete()
        Category.objects.all().delete()
        User.objects.filter(is_superuser=False).delete()
        SiteSettings.objects.all().delete()
        Service.objects.all().delete()
        Testimonial.objects.all().delete()
        NewsletterSubscriber.objects.all().delete()
        ContactMessage.objects.all().delete()
        FAQ.objects.all().delete()
        Banner.objects.all().delete()

    def create_site_settings(self):
        """Crée les paramètres du site."""
        SiteSettings.objects.get_or_create(
            defaults={
                'site_name': 'JOSNET NETWORK',
                'site_description': 'Votre partenaire IT & Télécom de confiance au Burundi',
                'phone_primary': '+257 12 345 678',
                'email_primary': '<EMAIL>',
                'address_line1': '123 Avenue Principale',
                'city': 'Bujumbura',
                'country': 'Burundi',
                'business_hours': 'Lun-Ven: 8h00-18h00, Sam: 8h00-13h00',
                'is_active': True
            }
        )

    def create_services(self):
        """Crée les services."""
        services_data = [
            {
                'name': 'Réseaux & Connectivité',
                'description': 'Installation et maintenance de réseaux informatiques, Wi-Fi, fibre optique et solutions de connectivité avancées pour entreprises et particuliers.',
                'short_description': 'Installation et maintenance de réseaux informatiques, Wi-Fi, fibre optique.',
                'icon': 'Wifi',
                'color': 'blue',
                'slug': 'reseaux-connectivite',
                'featured': True,
                'order': 1
            },
            {
                'name': 'Matériel Informatique',
                'description': 'Vente d\'ordinateurs, serveurs, équipements réseau et accessoires informatiques de qualité professionnelle.',
                'short_description': 'Vente d\'ordinateurs, serveurs, équipements réseau et accessoires.',
                'icon': 'Monitor',
                'color': 'green',
                'slug': 'materiel-informatique',
                'featured': True,
                'order': 2
            },
            {
                'name': 'Solutions Mobiles',
                'description': 'Smartphones, tablettes, accessoires mobiles et solutions de mobilité pour entreprises et particuliers.',
                'short_description': 'Smartphones, tablettes, accessoires mobiles et solutions de mobilité.',
                'icon': 'Smartphone',
                'color': 'purple',
                'slug': 'solutions-mobiles',
                'featured': True,
                'order': 3
            },
            {
                'name': 'Infrastructure IT',
                'description': 'Conception, installation et maintenance d\'infrastructures IT complètes pour entreprises de toutes tailles.',
                'short_description': 'Conception, installation et maintenance d\'infrastructures IT complètes.',
                'icon': 'Server',
                'color': 'orange',
                'slug': 'infrastructure-it',
                'featured': True,
                'order': 4
            }
        ]

        for service_data in services_data:
            Service.objects.get_or_create(
                slug=service_data['slug'],
                defaults=service_data
            )

    def create_testimonials(self):
        """Crée les témoignages."""
        testimonials_data = [
            {
                'client_name': 'Jean Baptiste Niyongabo',
                'client_title': 'Directeur IT',
                'client_company': 'Banque Commerciale du Burundi',
                'content': 'JOSNET NETWORK a transformé notre infrastructure IT. Leur expertise et leur professionnalisme sont remarquables. Je recommande vivement leurs services.',
                'rating': 5,
                'featured': True,
                'order': 1
            },
            {
                'client_name': 'Marie Claire Ndayishimiye',
                'client_title': 'CEO',
                'client_company': 'TechStart Burundi',
                'content': 'Une équipe exceptionnelle qui comprend vraiment les besoins des entreprises burundaises. Leur support technique est disponible 24/7.',
                'rating': 5,
                'featured': True,
                'order': 2
            },
            {
                'client_name': 'Pierre Hakizimana',
                'client_title': 'Responsable Informatique',
                'client_company': 'Université du Burundi',
                'content': 'Grâce à JOSNET, nous avons modernisé tout notre réseau universitaire. Les étudiants et le personnel bénéficient maintenant d\'une connectivité excellente.',
                'rating': 5,
                'featured': True,
                'order': 3
            }
        ]

        for testimonial_data in testimonials_data:
            Testimonial.objects.get_or_create(
                client_name=testimonial_data['client_name'],
                defaults=testimonial_data
            )

    def create_users(self):
        """Crée des utilisateurs de test."""
        for i in range(50):
            User.objects.get_or_create(
                email=f'user{i}@example.com',
                defaults={
                    'first_name': f'User{i}',
                    'last_name': f'Test{i}',
                    'is_active': True
                }
            )

    def create_products(self):
        """Crée des catégories et produits."""
        # Créer des catégories
        categories = [
            'Ordinateurs',
            'Smartphones',
            'Réseaux',
            'Serveurs',
            'Accessoires'
        ]

        for cat_name in categories:
            Category.objects.get_or_create(
                name=cat_name,
                defaults={'slug': cat_name.lower()}
            )

        # Créer des produits
        categories_objs = Category.objects.all()
        for i in range(100):
            category = random.choice(categories_objs)
            Product.objects.get_or_create(
                name=f'Produit {i+1}',
                defaults={
                    'description': f'Description du produit {i+1}',
                    'price': random.uniform(100, 5000),
                    'category': category,
                    'status': 'published',
                    'stock_quantity': random.randint(0, 100)
                }
            )

    def create_orders(self):
        """Crée des commandes de test."""
        users = User.objects.filter(is_superuser=False)
        products = Product.objects.all()
        
        statuses = ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']
        
        for i in range(200):
            user = random.choice(users)
            status = random.choice(statuses)
            
            # Créer la commande avec une date aléatoire dans les 6 derniers mois
            created_date = timezone.now() - timedelta(days=random.randint(0, 180))
            
            order = Order.objects.create(
                user=user,
                status=status,
                total_amount=0,
                created_at=created_date
            )
            
            # Ajouter des items à la commande
            total = 0
            for _ in range(random.randint(1, 5)):
                product = random.choice(products)
                quantity = random.randint(1, 3)
                
                OrderItem.objects.create(
                    order=order,
                    product=product,
                    quantity=quantity,
                    price=product.price
                )
                total += product.price * quantity
            
            order.total_amount = total
            order.save()

    def create_newsletter_subscribers(self):
        """Crée des abonnés newsletter."""
        for i in range(30):
            NewsletterSubscriber.objects.get_or_create(
                email=f'subscriber{i}@example.com',
                defaults={
                    'name': f'Subscriber {i}',
                    'is_active': True,
                    'confirmed': True
                }
            )

    def create_contact_messages(self):
        """Crée des messages de contact."""
        services = ['reseaux', 'materiel', 'mobile', 'infrastructure', 'maintenance']
        
        for i in range(20):
            ContactMessage.objects.create(
                name=f'Contact {i}',
                email=f'contact{i}@example.com',
                subject=f'Demande d\'information {i}',
                message=f'Message de contact numéro {i}',
                service_requested=random.choice(services),
                status='new'
            )

    def create_faqs(self):
        """Crée des FAQ."""
        faqs_data = [
            {
                'question': 'Quels sont vos horaires d\'ouverture ?',
                'answer': 'Nous sommes ouverts du lundi au vendredi de 8h00 à 18h00, et le samedi de 8h00 à 13h00.',
                'category': 'general',
                'featured': True
            },
            {
                'question': 'Proposez-vous un service de maintenance ?',
                'answer': 'Oui, nous proposons des contrats de maintenance pour tous nos équipements avec intervention sur site.',
                'category': 'support',
                'featured': True
            }
        ]

        for faq_data in faqs_data:
            FAQ.objects.get_or_create(
                question=faq_data['question'],
                defaults=faq_data
            )

    def create_banners(self):
        """Crée des bannières."""
        Banner.objects.get_or_create(
            title='Bienvenue chez JOSNET',
            defaults={
                'subtitle': 'Votre partenaire IT de confiance',
                'description': 'Découvrez nos solutions technologiques innovantes',
                'position': 'hero',
                'is_active': True,
                'order': 1
            }
        )
