
import { useState } from "react";
import { useQuery, useQueryClient } from '@tanstack/react-query';
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  FolderTree,
  Search,
  Plus,
  Edit,
  Trash2,
  ChevronRight,
  Loader2,
  AlertTriangle,
  RefreshCw,
  Check,
  X
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { productApi, Category } from "@/services/productApi";
import CategoryForm from "@/components/admin/categories/CategoryForm";
import DeleteCategoryDialog from "@/components/admin/categories/DeleteCategoryDialog";

// Données fictives pour les catégories en cas d'erreur API
const mockCategories = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    slug: "reseaux",
    description: "Équipe<PERSON> réseau",
    parent: null,
    parent_name: null,
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z"
  },
  {
    id: 2,
    name: "Composants PC",
    slug: "composants-pc",
    description: "Composants pour ordinateurs",
    parent: null,
    parent_name: null,
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z"
  },
  {
    id: 3,
    name: "Périphériques",
    slug: "peripheriques",
    description: "Périphériques informatiques",
    parent: null,
    parent_name: null,
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z"
  }
];

const Categories = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Fetch categories from API
  const {
    data: categories = [],
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => productApi.getCategories(),
    retry: 1,
    retryDelay: 1000,
  });

  // Ensure categories is an array before filtering
  const categoriesArray = Array.isArray(categories) ? categories : [];

  // Fonction de filtrage pour la recherche
  const filteredCategories = categoriesArray.filter(category =>
    category?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category?.slug?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Fallback to mock data if API fails
  const displayCategories = isError ? mockCategories : filteredCategories;

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsCategoryFormOpen(true);
  };

  const handleDeleteCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsDeleteDialogOpen(true);
  };

  const handleAddCategory = () => {
    setSelectedCategory(null);
    setIsCategoryFormOpen(true);
  };

  const handleRefresh = () => {
    refetch();
    toast({
      title: "Rafraîchissement",
      description: "Liste des catégories mise à jour",
    });
  };

  const handleCategorySuccess = () => {
    // Invalidate categories query to refetch data
    queryClient.invalidateQueries({ queryKey: ['categories'] });
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Catégories</h1>
          <p className="text-gray-500">Organisez votre catalogue en catégories et sous-catégories</p>
        </div>

        {/* API Error Message */}
        {isError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3 mb-4">
            <div className="text-red-500">
              <AlertTriangle size={20} />
            </div>
            <div>
              <h3 className="font-medium text-red-800">Erreur de connexion</h3>
              <p className="text-red-700 text-sm mt-1">
                Impossible de se connecter à l'API des catégories. Affichage des données fictives.
              </p>
              <div className="mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Réessayer
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher une catégorie..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              className="flex items-center gap-1"
            >
              <RefreshCw size={16} />
              Rafraîchir
            </Button>
            <Button onClick={handleAddCategory} className="flex items-center gap-1">
              <Plus size={16} />
              Nouvelle Catégorie
            </Button>
          </div>
        </div>

        {/* Tableau des catégories */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Nom</TableHead>
                <TableHead>Slug</TableHead>
                <TableHead>Parent</TableHead>
                <TableHead className="text-center">Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                    <p className="mt-2 text-sm text-gray-500">Chargement des catégories...</p>
                  </TableCell>
                </TableRow>
              ) : displayCategories.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <p className="text-sm text-gray-500">Aucune catégorie trouvée.</p>
                  </TableCell>
                </TableRow>
              ) : (
                displayCategories.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell className="font-medium">{category.id}</TableCell>
                    <TableCell>{category.name}</TableCell>
                    <TableCell>
                      <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{category.slug}</code>
                    </TableCell>
                    <TableCell>
                      {category.parent_name ? (
                        <span className="text-sm text-gray-600">{category.parent_name}</span>
                      ) : (
                        <span className="text-sm text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      {category.is_active ? (
                        <div className="flex items-center justify-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <Check className="mr-1 h-3 w-3" />
                            Active
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <X className="mr-1 h-3 w-3" />
                            Inactive
                          </span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditCategory(category)}
                          title="Modifier la catégorie"
                        >
                          <Edit size={16} className="text-blue-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteCategory(category)}
                          title="Supprimer la catégorie"
                        >
                          <Trash2 size={16} className="text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Indication du nombre de catégories */}
        <div className="text-sm text-gray-500">
          {isLoading ? (
            "Chargement des catégories..."
          ) : isError ? (
            `Affichage de ${displayCategories.length} catégories (données fictives)`
          ) : (
            `Affichage de ${filteredCategories.length} catégories sur ${categoriesArray.length}`
          )}
        </div>

        {/* Category Form Dialog */}
        <CategoryForm
          isOpen={isCategoryFormOpen}
          onOpenChange={setIsCategoryFormOpen}
          category={selectedCategory}
          onSuccess={handleCategorySuccess}
        />

        {/* Delete Category Dialog */}
        <DeleteCategoryDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          categoryId={selectedCategory?.id || null}
          categoryName={selectedCategory?.name || ''}
          onSuccess={handleCategorySuccess}
        />
      </div>
    </AdminLayout>
  );
};

export default Categories;
