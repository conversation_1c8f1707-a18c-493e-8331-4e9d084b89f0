import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Loader2, Mail, RefreshCw, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { api } from '@/services/api';

const ActivateAccount = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired' | 'invalid'>('loading');
  const [message, setMessage] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [resendEmail, setResendEmail] = useState('');
  const [isResending, setIsResending] = useState(false);
  
  // Récupérer le token depuis l'URL
  const token = searchParams.get('token');

  useEffect(() => {
    if (token) {
      activateAccount(token);
    } else {
      setStatus('invalid');
      setMessage('Token d\'activation manquant dans l\'URL.');
    }
  }, [token]);

  const activateAccount = async (activationToken: string) => {
    try {
      setStatus('loading');

      const data = await api.post('/auth/account/activate/', {
        token: activationToken
      });

      // Si on arrive ici, la requête a réussi (pas d'exception)
      setStatus('success');
      setMessage(data.message || 'Compte activé avec succès !');
      setUserEmail(data.user?.email || '');

      toast({
        title: "🎉 Compte activé !",
        description: "Votre compte a été activé avec succès. Vous pouvez maintenant vous connecter.",
        duration: 8000,
      });

      // Rediriger vers la page de connexion après 3 secondes
      setTimeout(() => {
        navigate('/login', {
          state: {
            message: "Votre compte est maintenant activé ! Vous pouvez vous connecter.",
            email: data.user?.email
          }
        });
      }, 3000);
    } catch (error: any) {
      console.error('Erreur activation:', error);

      // Gestion spécifique des erreurs d'activation
      if (error.errors) {
        const errorData = error.errors;

        if (errorData.expired) {
          setStatus('expired');
          setUserEmail(errorData.user_email || '');
          setMessage(errorData.message || 'Le lien d\'activation a expiré.');
        } else if (errorData.invalid) {
          setStatus('invalid');
          setMessage(errorData.message || 'Lien d\'activation invalide.');
        } else {
          setStatus('error');
          setMessage(errorData.message || error.message || 'Erreur lors de l\'activation');
        }
      } else {
        setStatus('error');
        setMessage(error.message || 'Erreur de connexion au serveur');
      }
    }
  };

  const handleResendActivation = async () => {
    if (!resendEmail) {
      toast({
        title: "❌ Email requis",
        description: "Veuillez saisir votre adresse email",
        variant: "destructive"
      });
      return;
    }

    setIsResending(true);
    
    try {
      const data = await api.post('/auth/account/activation/resend/', {
        email: resendEmail
      });

      toast({
        title: "📧 Email renvoyé",
        description: data.message || "Email d'activation renvoyé avec succès",
        duration: 8000,
      });
      setResendEmail('');
    } catch (error: any) {
      toast({
        title: "❌ Erreur",
        description: error.message || "Erreur lors du renvoi de l'email",
        variant: "destructive"
      });
    } finally {
      setIsResending(false);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-16 w-16 text-green-500" />;
      case 'error':
      case 'expired':
      case 'invalid':
        return <XCircle className="h-16 w-16 text-red-500" />;
      default:
        return <AlertCircle className="h-16 w-16 text-yellow-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
      case 'expired':
      case 'invalid':
        return 'text-red-600';
      default:
        return 'text-yellow-600';
    }
  };

  const getStatusTitle = () => {
    switch (status) {
      case 'loading':
        return 'Activation en cours...';
      case 'success':
        return 'Compte activé !';
      case 'expired':
        return 'Lien expiré';
      case 'invalid':
        return 'Lien invalide';
      case 'error':
        return 'Erreur d\'activation';
      default:
        return 'Activation de compte';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {getStatusIcon()}
          </div>
          <CardTitle className={`text-2xl font-bold ${getStatusColor()}`}>
            {getStatusTitle()}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              {message}
            </p>
            
            {userEmail && (
              <p className="font-medium text-blue-600 bg-blue-50 p-3 rounded-lg border">
                {userEmail}
              </p>
            )}
          </div>
          
          {status === 'success' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <p className="text-green-800 text-sm font-medium mb-1">
                    Activation réussie !
                  </p>
                  <p className="text-green-700 text-sm">
                    Votre compte est maintenant actif. Vous allez être redirigé vers la page de connexion...
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {(status === 'expired' || status === 'invalid' || status === 'error') && (
            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <XCircle className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="text-red-800 text-sm font-medium mb-1">
                      {status === 'expired' ? 'Lien expiré' : status === 'invalid' ? 'Lien invalide' : 'Erreur'}
                    </p>
                    <p className="text-red-700 text-sm">
                      {status === 'expired' 
                        ? 'Le lien d\'activation a expiré. Demandez un nouveau lien ci-dessous.'
                        : status === 'invalid'
                        ? 'Le lien d\'activation est invalide ou a déjà été utilisé.'
                        : 'Une erreur est survenue lors de l\'activation.'
                      }
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-blue-800 font-medium mb-3">Demander un nouveau lien d'activation</h3>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="resend-email">Adresse email</Label>
                    <Input
                      id="resend-email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={resendEmail}
                      onChange={(e) => setResendEmail(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <Button 
                    onClick={handleResendActivation}
                    disabled={isResending}
                    className="w-full"
                  >
                    {isResending ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Envoi en cours...
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        Renvoyer le lien d'activation
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
          
          <div className="text-center space-y-3">
            {status === 'success' && (
              <Button 
                onClick={() => navigate('/login')}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                Se connecter maintenant
              </Button>
            )}
            
            <Button 
              onClick={() => navigate('/')}
              variant="ghost"
              className="w-full"
            >
              Retour à l'accueil
            </Button>
          </div>
          
          <div className="text-center">
            <p className="text-xs text-gray-500">
              Problème avec l'activation ? Contactez notre support technique.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ActivateAccount;
