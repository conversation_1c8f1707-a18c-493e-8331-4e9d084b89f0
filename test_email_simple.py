#!/usr/bin/env python3
"""
Test simple pour vérifier l'envoi d'emails
"""

import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from django.core.mail import send_mail
from django.conf import settings
from authentication.models import User
from authentication.utils import send_password_reset_email

def test_simple_email():
    """Test d'envoi d'email simple"""
    print("📧 Test d'envoi d'email simple...")
    
    try:
        result = send_mail(
            subject='Test JosNet - Réinitialisation',
            message='Ceci est un test d\'email de réinitialisation de mot de passe.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False,
        )
        
        print(f"✅ Email envoyé! Résultat: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'envoi: {e}")
        return False

def test_reset_email():
    """Test d'envoi d'email de réinitialisation"""
    print("\n🔄 Test d'envoi d'email de réinitialisation...")
    
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"👤 Utilisateur: {user.email}")
        
        send_password_reset_email(user)
        print("✅ Email de réinitialisation envoyé!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def show_email_config():
    """Affiche la configuration email"""
    print("⚙️ Configuration Email:")
    print(f"   Backend: {settings.EMAIL_BACKEND}")
    print(f"   Host: {settings.EMAIL_HOST}")
    print(f"   Port: {settings.EMAIL_PORT}")
    print(f"   TLS: {settings.EMAIL_USE_TLS}")
    print(f"   User: {settings.EMAIL_HOST_USER}")
    print(f"   From: {settings.DEFAULT_FROM_EMAIL}")

if __name__ == "__main__":
    print("🧪 Test d'envoi d'emails")
    print("=" * 40)
    
    show_email_config()
    
    # Test 1: Email simple
    success1 = test_simple_email()
    
    # Test 2: Email de réinitialisation
    success2 = test_reset_email()
    
    print(f"\n📋 Résultats:")
    print(f"   Email simple: {'✅' if success1 else '❌'}")
    print(f"   Email reset: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print(f"\n✅ Les emails sont envoyés correctement!")
        print(f"📬 Vérifiez votre boîte mail: <EMAIL>")
    else:
        print(f"\n❌ Problème avec l'envoi d'emails")
