import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { Check, Loader2, Search } from "lucide-react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { accountApi, ProductNameSuggestion } from "@/services/accountApi";
import { FormControl } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface ProductNameAutocompleteProps {
  value: string;
  onValueChange: (value: string, categoryValue?: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: boolean;
  allowFreeInput?: boolean; // Permettre la saisie libre sans autocomplétion obligatoire
}

export function ProductNameAutocomplete({
  value,
  onValueChange,
  placeholder = "Rechercher un produit...",
  disabled = false,
  className,
  error = false,
  allowFreeInput = true
}: ProductNameAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value || "");
  const [suggestions, setSuggestions] = useState<ProductNameSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const debouncedSearchTerm = useDebounce(inputValue, 300);
  const inputRef = useRef<HTMLInputElement>(null);

  // Fetch suggestions when search term changes
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (debouncedSearchTerm.length < 2) {
        setSuggestions([]);
        return;
      }

      setLoading(true);
      try {
        const results = await accountApi.getProductNameSuggestions(debouncedSearchTerm);
        setSuggestions(results);
      } catch (error) {
        console.error("Error fetching product suggestions:", error);
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSuggestions();
  }, [debouncedSearchTerm]);

  // Update input value when external value changes
  useEffect(() => {
    if (value !== inputValue) {
      setInputValue(value || "");
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Toujours mettre à jour la valeur du formulaire, même si l'utilisateur tape directement
    onValueChange(newValue, undefined);
  };

  const handleSelect = (suggestion: ProductNameSuggestion) => {
    setInputValue(suggestion.name);
    onValueChange(suggestion.name, suggestion.category);
    setOpen(false);
  };

  return (
    <div className={cn("relative w-full", className)}>
      <div className="relative">
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "w-full pr-10",
            error && "border-red-500 focus-visible:ring-red-500"
          )}
          onFocus={() => setOpen(true)}
          onBlur={(e) => {
            // S'assurer que la valeur finale est transmise au formulaire
            const finalValue = e.target.value;
            if (finalValue !== value) {
              onValueChange(finalValue, undefined);
            }

            // Small delay to allow click on suggestion
            setTimeout(() => setOpen(false), 200);
          }}
          onKeyDown={(e) => {
            // Gérer la touche Entrée pour valider la saisie
            if (e.key === 'Enter') {
              e.preventDefault();
              const currentValue = inputValue.trim();
              if (currentValue && currentValue !== value) {
                onValueChange(currentValue, undefined);
              }
              setOpen(false);
            }
            // Gérer la touche Échap pour fermer les suggestions
            if (e.key === 'Escape') {
              setOpen(false);
            }
          }}
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          ) : (
            <Search className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
      </div>

      {open && (
        <div className="absolute z-50 w-full mt-1 bg-popover rounded-md border shadow-md">
          {loading ? (
            <div className="p-4 text-center">
              <Loader2 className="h-5 w-5 animate-spin mx-auto text-primary" />
              <p className="text-sm text-muted-foreground mt-2">Recherche en cours...</p>
            </div>
          ) : suggestions.length > 0 ? (
            <ul className="max-h-60 overflow-auto p-1">
              {suggestions.map((suggestion) => (
                <li
                  key={suggestion.id}
                  onClick={() => handleSelect(suggestion)}
                  className={cn(
                    "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none",
                    "hover:bg-accent hover:text-accent-foreground",
                    "data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  )}
                >
                  <span className="flex-1 truncate">{suggestion.name}</span>
                  {suggestion.category && (
                    <span className="ml-2 text-xs text-muted-foreground">
                      {suggestion.category}
                    </span>
                  )}
                </li>
              ))}
            </ul>
          ) : debouncedSearchTerm.length >= 2 ? (
            <div className="p-4 text-center">
              <p className="text-sm text-muted-foreground">Aucun produit trouvé</p>
              {allowFreeInput && (
                <p className="text-xs text-muted-foreground mt-1">
                  Vous pouvez continuer à taper pour créer une nouvelle suggestion
                </p>
              )}
            </div>
          ) : (
            <div className="p-4 text-center">
              <p className="text-sm text-muted-foreground">
                {allowFreeInput
                  ? "Tapez le nom du produit ou recherchez dans nos suggestions"
                  : "Saisissez au moins 2 caractères pour rechercher"
                }
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// Hook for debouncing input value
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
