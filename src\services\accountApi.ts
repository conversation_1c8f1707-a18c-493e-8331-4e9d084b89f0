import { API_BASE_URL } from '@/config/api';
import { ApiError, handleApiError } from '@/utils/apiUtils';
import { formatCurrency, formatImageUrl } from '@/utils/formatters';

// API service for account-related operations

// Types pour les retours
export interface ReturnItem {
  id: string;
  orderId: string;
  date: string;
  status: string;
  items: number;
  reason: string;
  refundAmount: string;
}

export interface ReturnCreateData {
  orderId: string;
  reason?: string;
  additionalInfo?: string;
  items: Array<{
    orderItemId: number;
    quantity: number;
    reason?: string;
    description?: string;
    price?: number;
  }>;
}

// Interface pour les articles retournés
export interface ReturnItemDetail {
  id: string;
  productName: string;
  quantity: number;
  price: string;
  reason: string;
  image: string;
}

// Interface pour les détails d'un retour
export interface ReturnDetails extends ReturnItem {
  returnItems: ReturnItemDetail[];
  refundMethod?: string;
  refundDate?: string;
  trackingNumber?: string;
  notes?: string;
}

// Types pour les réclamations
export interface Claim {
  id: string;
  orderId: string;
  date: string;
  status: string;
  subject: string;
  description: string;
  lastUpdate: string;
  attachments?: string[];
}

// Interface pour les détails d'une réclamation
export interface ClaimDetails extends Claim {
  messages: ClaimMessage[];
  product?: {
    id: string;
    name: string;
    image: string;
  };
}

export interface ClaimMessage {
  id: string;
  claimId: string;
  sender: 'customer' | 'support';
  message: string;
  date: string;
  isRead: boolean;
  attachments?: string[];
}

export interface ClaimStatusUpdate {
  id: string;
  claimId: string;
  oldStatus: string;
  newStatus: string;
  date: string;
  comment?: string;
  updatedBy: string;
}

export interface ClaimCreateData {
  orderId: string;
  subject: string;
  description: string;
  attachments?: File[];
}

// Types pour les factures
export interface Invoice {
  id: string;
  orderId: string;
  date: string;
  amount: string;
  status: string;
}

export interface InvoiceDetail extends Invoice {
  customerInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    postalCode: string;
    country: string;
  };
  items: {
    id: string;
    name: string;
    quantity: number;
    unitPrice: string;
    totalPrice: string;
  }[];
  subtotal: string;
  tax: string;
  shipping: string;
  discount?: string;
  paymentMethod: string;
  paymentId?: string;
}

// Types pour les suggestions de produits
export interface SuggestedProduct {
  id: number;
  name: string;
  price: number;
  image: string;
  rating: number;
  category: string;
}

// Type pour les suggestions de noms de produits
export interface ProductNameSuggestion {
  id: number;
  name: string;
  category?: string;
}

// Types pour les commandes utilisateur
export interface Order {
  id: string;
  numericId: number;
  date: string;
  status: string;
  total: string;
  items?: OrderItem[];
  shippingAddress?: string;
  paymentMethod?: string;
  trackingNumber?: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  quantity: number;
  price: string;
  image?: string;
}

// Types pour les suggestions de produits créées par l'utilisateur
export interface ProductSuggestion {
  id: number;
  name: string;
  category: string;
  description: string;
  estimated_price: string;
  reason: string;
  status: string;
  created_at: string;
  updated_at: string;
  admin_response?: string;
  response_date?: string;
}

export interface CreateProductSuggestionRequest {
  name: string;
  category: string;
  description: string;
  estimated_price: string;
  reason: string;
}

export interface UpdateProductSuggestionRequest {
  name?: string;
  category?: string;
  description?: string;
  estimated_price?: string;
  reason?: string;
}

// Fonction helper pour obtenir les headers d'authentification
const getAuthHeader = () => {
  // Vu00e9rifier plusieurs clu00e9s possibles pour le token
  const token = localStorage.getItem('accessToken') || 
               localStorage.getItem('authToken') || 
               localStorage.getItem('token') || 
               sessionStorage.getItem('accessToken') || 
               sessionStorage.getItem('authToken');
  
  console.log('Auth token found:', token ? 'yes' : 'no');
  console.log('Token value:', token);
  
  // Retourner un objet vide au lieu de null pour u00e9viter les erreurs
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Account API service
export const accountApi = {
  // Get returns for the authenticated user
  getReturns: async (filter?: string): Promise<ReturnItem[]> => {
    try {
      const headers = getAuthHeader();
      
      // Vérifier si les headers d'authentification sont disponibles
      if (headers) {
        // Appel API réel
        try {
          // Utiliser l'URL /orders/returns/ au lieu de /account/returns/ pour être cohérent avec l'API des réclamations
          console.log('Appel API getReturns:', `${API_BASE_URL}/orders/returns/`);
          const response = await fetch(`${API_BASE_URL}/orders/returns/${filter && filter !== 'all' ? `?status=${filter}` : ''}`, {
            method: 'GET',
            headers
          });
          
          if (response.ok) {
            const data = await response.json();
            console.log('Données retours reçues:', data);
            // Stocker les données dans localStorage pour une utilisation hors ligne
            localStorage.setItem('userReturns', JSON.stringify(data));
            
            // Vérifier la structure des données reçues
            const returnsArray = Array.isArray(data) ? data : 
                              data.results ? data.results : 
                              data.returns ? data.returns : 
                              [];
            
            console.log('Structure des données de retours:', { 
              isArray: Array.isArray(data), 
              hasResults: !!data.results, 
              hasReturns: !!data.returns,
              returnsArray
            });
            
            // Transformer les données au format attendu par le frontend
            const transformedData = returnsArray.map((item: any) => ({
              id: item.id,
              orderId: item.order || item.order_id || item.orderId,
              date: item.date || new Date(item.created_at).toLocaleDateString('fr-FR'),
              status: item.status_display || item.status,
              items: item.items_count || (item.items ? item.items.length : 0),
              reason: item.reason_display || item.reason,
              refundAmount: item.refund_amount || formatCurrency(0)
            }));
            
            // Filtrer les données si un filtre est fourni et différent de 'all'
            if (filter && filter !== '' && filter !== 'all') {
              return transformedData.filter(item => item.status === filter);
            }
            
            return transformedData;
          } else {
            console.error('Erreur API getReturns:', response.status);
            throw { message: 'Erreur lors de la récupération des retours', status: response.status };
          }
        } catch (error) {
          console.warn('API getReturns a échoué, utilisation des données stockées localement', error);
          
          // Récupérer les données stockées localement
          const storedReturns = localStorage.getItem('userReturns');
          if (storedReturns) {
            console.log('Utilisation des données de retours stockées localement');
            const parsedData = JSON.parse(storedReturns);
            
            // Vérifier la structure des données stockées
            const returnsArray = Array.isArray(parsedData) ? parsedData : 
                              parsedData.results ? parsedData.results : 
                              parsedData.returns ? parsedData.returns : 
                              [];
            
            console.log('Structure des données de retours stockées:', { 
              isArray: Array.isArray(parsedData), 
              hasResults: !!parsedData.results, 
              hasReturns: !!parsedData.returns,
              returnsArray
            });
            
            const transformedData = returnsArray.map((item: any) => ({
              id: item.id,
              orderId: item.order || item.order_id || item.orderId,
              date: item.date || new Date(item.created_at).toLocaleDateString('fr-FR'),
              status: item.status_display || item.status,
              items: item.items_count || (item.items ? item.items.length : 0),
              reason: item.reason_display || item.reason,
              refundAmount: item.refund_amount || formatCurrency(0)
            }));
            
            // Appliquer le filtre aux données stockées localement
            if (filter && filter !== '' && filter !== 'all') {
              return transformedData.filter(item => item.status === filter);
            }
            
            return transformedData;
          }
          
          // Toujours utiliser les données dynamiques, même en développement
          console.error('Aucune donnée de retour stockée et l\'API n\'est pas disponible');
          throw new Error('Impossible de récupérer les données de retours. Veuillez vérifier la connexion au serveur.');
        }
      } else {
        throw new Error('Authentication required');
      }
    } catch (error) {
      console.error('Error fetching returns:', error);
      throw error;
    }
  },

  // Get return details
  getReturnDetails: async (returnId: string): Promise<ReturnDetails> => {
    try {
      const headers = getAuthHeader();
      
      if (headers) {
        // Real API call when backend is ready
        try {
          const response = await fetch(`${API_BASE_URL}/account/returns/${returnId}/`, {
            headers
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch return details: ${response.status}`);
          }

          const data = await response.json();

          // Adapter la structure des données du backend vers le frontend
          return {
            ...data,
            orderId: data.order, // ✅ CORRECTION: Mapper 'order' vers 'orderId'
            returnItems: data.items || [], // Mapper 'items' vers 'returnItems'
            refundAmount: data.refund_amount || '0',
            status: data.status_display || data.status,
            reason: data.reason_display || data.reason
          };
        } catch (apiError) {
          console.error('API call failed', apiError);
          throw new Error(`Impossible de récupérer les détails du retour ${returnId}. Veuillez vérifier la connexion au serveur.`);
        }
      } else {
        throw new Error('Authentication required');
      }
    } catch (error) {
      console.error('Error fetching return details:', error);
      throw error;
    }
  },
  
  // Get user orders
  getUserOrders: async (): Promise<Order[]> => {
    try {
      const headers = getAuthHeader();
      
      // Vérifier si un token d'authentification est disponible
      if (Object.keys(headers).length === 0) {
        console.error('Aucun token d\'authentification trouvé pour récupérer les commandes');
        throw new Error('Authentication required');
      }
      
      // Afficher les en-têtes pour le débogage (sans exposer le token complet)
      console.log('En-têtes d\'authentification présents:', !!headers.Authorization);
      if (headers.Authorization) {
        const tokenPreview = headers.Authorization.substring(0, 15) + '...';
        console.log('Aperçu du token:', tokenPreview);
      }
      
      // Appel API réel
      // Utiliser l'URL spécifique pour les commandes utilisateur
      const ordersUrl = `${API_BASE_URL}/orders/orders/`;
      console.log('Appel API getUserOrders:', ordersUrl);
      
      const response = await fetch(ordersUrl, {
        method: 'GET',
        headers
      });
      
      // Vérifier le statut de la réponse
      console.log('Statut de la réponse:', response.status);
      
      if (!response.ok) {
        // Gérer les différents codes d'erreur
        if (response.status === 401 || response.status === 403) {
          console.error('Erreur d\'authentification lors de la récupération des commandes');
          throw new Error('Authentification invalide. Veuillez vous reconnecter.');
        } else {
          console.error('Erreur API:', response.status);
          throw new Error(`Erreur lors de la récupération des commandes (${response.status})`);
        }
      }
      
      // Traiter les données reçues
      const data = await response.json();
      console.log('Données commandes reçues:', data);
      
      // Analyser la structure des données pour le débogage
      console.log('Type des données reçues:', typeof data);
      console.log('Est un tableau:', Array.isArray(data));
      
      if (typeof data === 'object' && data !== null) {
        console.log('Clés disponibles dans les données:', Object.keys(data));
        
        // Vérifier si les données contiennent une propriété results ou count (pagination Django REST)
        if ('results' in data) {
          console.log('Nombre de résultats dans data.results:', Array.isArray(data.results) ? data.results.length : 'Non disponible');
        }
        
        if ('count' in data) {
          console.log('Nombre total d\'entrées (count):', data.count);
        }
      }
      
      // Stocker les données dans localStorage pour une utilisation hors ligne
      localStorage.setItem('userOrders', JSON.stringify(data));
      
      // Vérifier la structure des données reçues
      let ordersArray = [];
      
      // S'assurer que ordersArray est toujours un tableau valide
      if (Array.isArray(data)) {
        ordersArray = data;
      } else if (data && data.results && Array.isArray(data.results)) {
        ordersArray = data.results;
      } else if (data && data.orders && Array.isArray(data.orders)) {
        ordersArray = data.orders;
      } else {
        console.warn('Aucun tableau de commandes trouvé dans les données reçues:', data);
        // Forcer un tableau vide
        ordersArray = [];
      }
      
      console.log('Structure des données de commandes:', { 
        isArray: Array.isArray(data), 
        hasResults: data && !!data.results, 
        hasOrders: data && !!data.orders,
        ordersCount: ordersArray.length
      });
      
      // Transformer les données pour correspondre à l'interface Order
      const transformedData = ordersArray.map((item: any) => {
        // Vérifier que les données essentielles sont présentes
        if (!item || (!item.id && !item.order_id)) {
          console.warn('Données de commande incomplètes:', item);
        }
        
        // Analyser la structure pour trouver les articles (différentes API peuvent utiliser différentes propriétés)
        let orderItems = [];
        
        if (item.items && Array.isArray(item.items)) {
          orderItems = item.items;
          console.log(`Commande ${item.id || item.order_id}: ${item.items.length} articles trouvés dans 'items'`);
        } else if (item.order_items && Array.isArray(item.order_items)) {
          orderItems = item.order_items;
          console.log(`Commande ${item.id || item.order_id}: ${item.order_items.length} articles trouvés dans 'order_items'`);
        } else if (item.line_items && Array.isArray(item.line_items)) {
          orderItems = item.line_items;
          console.log(`Commande ${item.id || item.order_id}: ${item.line_items.length} articles trouvés dans 'line_items'`);
        } else if (item.products && Array.isArray(item.products)) {
          orderItems = item.products;
          console.log(`Commande ${item.id || item.order_id}: ${item.products.length} articles trouvés dans 'products'`);
        } else {
          console.warn(`Commande ${item.id || item.order_id}: Aucun article trouvé. Structure de données:`, 
            Object.keys(item).filter(key => typeof item[key] === 'object'));
        }
        
        // Transformer les articles pour avoir un format uniforme
        const transformedItems = orderItems.map((orderItem: any) => {
          // Vérifier les données d'articles
          if (!orderItem) {
            console.warn('Données d\'article manquantes dans la commande', item.id || item.order_id);
            return null;
          }
          
          // Gérer les images de produits pour éviter les problèmes d'affichage
          // Utiliser la fonction formatImageUrl pour standardiser les URLs d'images
          let imageSource = null;
          
          // Déterminer la source d'image la plus pertinente
          if (orderItem.image) {
            imageSource = orderItem.image;
          } else if (orderItem.product_image) {
            imageSource = orderItem.product_image;
          } else if (orderItem.images && Array.isArray(orderItem.images) && orderItem.images.length > 0) {
            imageSource = orderItem.images[0];
          } else if (orderItem.image_url) {
            imageSource = orderItem.image_url;
          }
          
          // Formater l'URL de l'image avec notre fonction utilitaire
          const productImage = formatImageUrl(imageSource, '/images/placeholder-product.jpg', API_BASE_URL);
          
          // Journaliser la source d'image utilisée pour le débogage
          if (productImage !== '/images/placeholder-product.jpg') {
            console.log(`Image formatée pour ${orderItem.product_name || orderItem.name || 'produit'}: ${productImage.substring(0, 30)}...`);
          } else {
            console.log(`Aucune image valide trouvée pour ${orderItem.product_name || orderItem.name || 'produit'}, utilisation de l'image par défaut`);
          }
          
          return {
            id: orderItem.id || `item-${Math.random().toString(36).substring(2, 9)}`,
            productId: orderItem.product_id || orderItem.productId || 'unknown-product',
            name: orderItem.product_name || orderItem.name || 'Produit sans nom',
            quantity: orderItem.quantity || 1,
            price: orderItem.price || formatCurrency(orderItem.price_amount || 0),
            image: productImage
          };
        }).filter(Boolean); // Filtrer les articles null
        
        // Si aucun article n'est trouvé mais que la commande a un montant, créer un article fictif
        // IMPORTANT: Ne pas créer d'articles fictifs car ils causent des erreurs lors des retours
        // Les commandes sans articles détaillés ne peuvent pas être retournées article par article
        if (transformedItems.length === 0 && (item.total || item.total_amount)) {
          console.log('Aucun article trouvé pour la commande avec montant. Commande sans détails d\'articles.');
          // Ne pas créer d'article fictif pour éviter les erreurs lors des retours
          // La commande sera marquée comme non-retournable au niveau article
        }
        
        return {
          numericId: item.id,
          id: item.order_number || item.id.toString(),
          date: item.date || (item.created_at ? new Date(item.created_at).toLocaleDateString('fr-FR') : 'Date inconnue'),
          status: item.status_display || item.status || 'Statut inconnu',
          total: item.total || formatCurrency(item.total_amount || 0),
          items: transformedItems
        };
      });
      
      console.log('Données transformées:', transformedData.length, 'commandes');
      return transformedData;
    } catch (error) {
      console.error('Error fetching user orders:', error);
      // Essayer de récupérer les données stockées localement en cas d'erreur
      try {
        const storedData = localStorage.getItem('userOrders');
        if (storedData) {
          console.log('Utilisation des données de commandes stockées localement');
          const parsedData = JSON.parse(storedData);
          // S'assurer que ordersArray est toujours un tableau valide
          let ordersArray = [];
          
          if (Array.isArray(parsedData)) {
            ordersArray = parsedData;
          } else if (parsedData && parsedData.results && Array.isArray(parsedData.results)) {
            ordersArray = parsedData.results;
          } else if (parsedData && parsedData.orders && Array.isArray(parsedData.orders)) {
            ordersArray = parsedData.orders;
          } else {
            console.warn('Aucun tableau de commandes trouvé dans les données stockées:', parsedData);
            // Forcer un tableau vide
            ordersArray = [];
          }
          
          console.log('Type de données stockées:', typeof parsedData, 'Est un tableau:', Array.isArray(parsedData));
          
          const transformedData = ordersArray.map((item: any) => ({
            numericId: item.id,
            id: item.order_number || item.id?.toString() || 'unknown-id',
            date: item.date || (item.created_at ? new Date(item.created_at).toLocaleDateString('fr-FR') : 'Date inconnue'),
            status: item.status_display || item.status || 'Statut inconnu',
            total: item.total || formatCurrency(item.total_amount || 0),
            items: item.items ? item.items.map((orderItem: any) => {
              // Gérer les images de produits pour éviter les problèmes d'affichage
              let productImage = null;
              
              // Vérifier si l'image existe et n'est pas undefined
              if (orderItem.image) {
                productImage = orderItem.image;
              } else if (orderItem.product_image) {
                productImage = orderItem.product_image;
              } else if (orderItem.images && Array.isArray(orderItem.images) && orderItem.images.length > 0) {
                productImage = orderItem.images[0];
              } else {
                // Image par défaut si aucune image n'est disponible
                productImage = '/images/placeholder-product.jpg';
              }
              
              // Vérifier si l'ID est valide (numérique)
              const itemId = orderItem.id;
              if (!itemId || isNaN(parseInt(itemId))) {
                // Si l'ID n'est pas valide, ne pas inclure cet article
                // pour éviter les erreurs lors des retours
                console.warn('Article avec ID invalide ignoré:', orderItem);
                return null;
              }

              return {
                id: itemId,
                productId: orderItem.product_id || orderItem.productId || 'unknown-product',
                name: orderItem.product_name || orderItem.name || 'Produit sans nom',
                quantity: orderItem.quantity || 1,
                price: orderItem.price || formatCurrency(orderItem.price_amount || 0),
                image: productImage
              };
            }).filter(Boolean) : [] // Filtrer les articles null
          }));
          
          return transformedData;
        }
      } catch (storageError) {
        console.error('Erreur lors de la récupération des données stockées:', storageError);
      }
      
      // Si tout échoue, propager l'erreur originale
      throw error;
    }
  },
  
  // Get claim details
  getClaimDetails: async (claimId: string): Promise<ClaimDetails> => {
    try {
      const headers = getAuthHeader();
      
      if (headers) {
        // Real API call when backend is ready
        try {
          const response = await fetch(`${API_BASE_URL}/orders/claims/${claimId}/`, {
            headers
          });
          
          if (!response.ok) {
            throw new Error(`Failed to fetch claim details: ${response.status}`);
          }
          
          return await response.json();
        } catch (apiError) {
          console.warn('API call failed, using mock data', apiError);
          
          // For development/testing - mock API response
          if (process.env.NODE_ENV === 'development') {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Return mock data
            return {
              id: "CLAIM-001",
              orderId: "CMD-12345",
              date: "10/05/2025",
              status: "En cours",
              subject: "Problème avec ma commande",
              description: "J'ai reçu un produit différent de celui commandé.",
              lastUpdate: "12/05/2025",
              messages: [
                {
                  id: "MSG-001",
                  claimId: "CLAIM-001",
                  date: "10/05/2025",
                  sender: "customer",
                  message: "J'ai reçu un produit différent de celui commandé. J'ai commandé un routeur Wi-Fi 6 mais j'ai reçu un routeur Wi-Fi 5.",
                  isRead: true,
                  attachments: []
                },
                {
                  id: "MSG-002",
                  claimId: "CLAIM-001",
                  date: "11/05/2025",
                  sender: "support",
                  message: "Bonjour, nous sommes désolés pour cette erreur. Pouvez-vous nous envoyer une photo du produit reçu ?",
                  isRead: true,
                  attachments: []
                },
                {
                  id: "MSG-003",
                  claimId: "CLAIM-001",
                  date: "12/05/2025",
                  sender: "customer",
                  message: "Voici une photo du produit reçu.",
                  isRead: true,
                  attachments: ["photo-produit.jpg"]
                }
              ],
              product: {
                id: "PROD-001",
                name: "Routeur Wi-Fi 6 Haute Performance",
                image: "/placeholder.svg"
              }
            };
          }

          return null;
        }
      } else {
        throw new Error('Authentication required');
      }
    } catch (error) {
      console.error('Error fetching claim details:', error);
      throw new Error(`Impossible de récupérer les détails de la réclamation ${claimId}. Veuillez vérifier la connexion au serveur.`);
    }
  },

  // Get user claims
  getClaims: async (filter?: string): Promise<Claim[]> => {
    try {
      // Toujours utiliser les données dynamiques, même en développement
      console.log('Récupération des données dynamiques de réclamations...');

      const headers = getAuthHeader();
      if (!headers) {
        throw new Error('Authentication required');
      }

      // Le backend ne supporte pas le filtrage par statut, donc nous récupérons toutes les réclamations
      // et filtrons côté client
      const response = await fetch(`${API_BASE_URL}/orders/claims/`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();
      localStorage.setItem('userClaims', JSON.stringify(data));
      
      // Vérifier la structure des données reçues
      const claimsArray = Array.isArray(data) ? data : 
                        data.results ? data.results : 
                        data.claims ? data.claims : 
                        [];
      
      console.log('Structure des données de réclamations:', { 
        isArray: Array.isArray(data), 
        hasResults: !!data.results, 
        hasClaims: !!data.claims,
        claimsArray
      });
      
      // Transformer les données pour qu'elles correspondent au format attendu par le frontend
      const transformedData = claimsArray.map((item: any) => ({
        id: item.id,
        orderId: item.order || item.order_id || item.orderId,
        date: item.date || new Date(item.created_at).toLocaleDateString('fr-FR'),
        status: item.status_display || item.status,
        subject: item.subject || item.title,
        description: item.description || item.content,
        lastUpdate: item.last_update || new Date(item.updated_at).toLocaleDateString('fr-FR'),
        attachments: item.attachments || []
      }));
      
      // Filtrer les données si un filtre est fourni et différent de 'all'
      if (filter && filter !== '' && filter !== 'all') {
        return transformedData.filter(item => item.status === filter);
      }
      
      return transformedData;
    } catch (error) {
      console.error('Error fetching claims:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des réclamations'
      };
    }
  },

  // Create a claim
  createClaim: async (data: ClaimCreateData): Promise<Claim> => {
    try {
      // Vérifier si le token d'authentification est disponible
      const authHeader = getAuthHeader();
      if (!authHeader) {
        console.error('Erreur d\'authentification: Token manquant');
        throw new Error('Vous devez être connecté pour créer une réclamation');
      }

      let response;

      // Mapper le sujet à une valeur valide pour le backend
      // D'après l'erreur, le backend attend des valeurs spécifiques pour le sujet
      // Valeurs possibles basées sur les types de réclamations courants
      const validSubjects = {
        'produit_endommagé': 'damaged_product',
        'produit_défectueux': 'defective_product',
        'article_manquant': 'missing_item',
        'erreur_de_livraison': 'delivery_error',
        'retard_de_livraison': 'delivery_delay',
        'problème_de_qualité': 'quality_issue',
        'autre': 'other'
      };
      
      // Convertir le sujet en minuscules et sans accents pour la correspondance
      const normalizedSubject = data.subject?.toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/\s+/g, '_') || '';
      
      // Trouver la meilleure correspondance ou utiliser 'other' par défaut
      let mappedSubject = 'other';
      
      // Vérifier si le sujet contient des mots-clés spécifiques
      if (normalizedSubject.includes('endommag') || normalizedSubject.includes('casse')) {
        mappedSubject = 'damaged_product';
      } else if (normalizedSubject.includes('defectueu') || normalizedSubject.includes('fonctionne_pas')) {
        mappedSubject = 'defective_product';
      } else if (normalizedSubject.includes('manquant') || normalizedSubject.includes('absent')) {
        mappedSubject = 'missing_item';
      } else if (normalizedSubject.includes('livraison') && (normalizedSubject.includes('erreur') || normalizedSubject.includes('probleme'))) {
        mappedSubject = 'delivery_error';
      } else if (normalizedSubject.includes('retard') || normalizedSubject.includes('delai')) {
        mappedSubject = 'delivery_delay';
      } else if (normalizedSubject.includes('qualite')) {
        mappedSubject = 'quality_issue';
      } else if (normalizedSubject.includes('livre')) {
        // Cas spécifique mentionné dans l'erreur
        mappedSubject = 'defective_product'; // ou une autre valeur appropriée
      }
      
      console.log(`Sujet original: "${data.subject}", normalisé: "${normalizedSubject}", mappé: "${mappedSubject}"`);
      
      // Essayer d'abord avec JSON pour les requêtes sans fichiers
      if (!data.attachments || data.attachments.length === 0) {
        // Utiliser JSON pour les requêtes sans fichiers
        // Utiliser le sujet mappé pour résoudre l'erreur 400
        const jsonData: { [key: string]: any } = {
          subject: mappedSubject,
          title: data.subject || '',
          description: data.description || '',
          content: data.description || '',
        };

        if (data.orderId) {
          const orderIdNum = parseInt(data.orderId, 10);
          if (!isNaN(orderIdNum)) {
            jsonData.order_id = orderIdNum;
          }
        }
        
        console.log('Tentative d\'appel API createClaim avec JSON:', `${API_BASE_URL}/orders/claims/`);
        console.log('Données JSON de la réclamation (détaillé):', JSON.stringify(jsonData, null, 2));
        
        try {
          response = await fetch(`${API_BASE_URL}/orders/claims/`, {
            method: 'POST',
            headers: {
              ...authHeader,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonData),
          });
        } catch (fetchError) {
          console.error('Erreur lors de la requête fetch pour createClaim (JSON):', fetchError);
          throw new Error('Erreur de connexion au serveur. Veuillez vérifier votre connexion internet.');
        }
      } else {
        // Create form data for file uploads
        const formData = new FormData();
        if (data.orderId) {
          formData.append('order_id', data.orderId);
        }
        formData.append('subject', mappedSubject); // Utiliser la valeur mappée
        formData.append('title', data.subject || ''); // Garder le titre original pour l'affichage
        formData.append('description', data.description || '');
        formData.append('content', data.description || ''); // Format alternatif

        if (data.attachments) {
          data.attachments.forEach(file => {
            formData.append('attachments', file);
          });
        }
        
        console.log('Tentative d\'appel API createClaim avec FormData:', `${API_BASE_URL}/orders/claims/`);
        console.log('Données de la réclamation:', {
          order: data.orderId,
          order_id: data.orderId,
          subject: data.subject,
          title: data.subject,
          description: data.description,
          content: data.description,
          attachments: data.attachments ? data.attachments.length : 0
        });
        console.log('Headers d\'authentification:', authHeader);
        
        try {
          response = await fetch(`${API_BASE_URL}/orders/claims/`, {
            method: 'POST',
            headers: {
              ...authHeader,
              // Ne pas définir Content-Type pour FormData, le navigateur le fait automatiquement
            },
            body: formData,
          });
        } catch (fetchError) {
          console.error('Erreur lors de la requête fetch pour createClaim (FormData):', fetchError);
          throw new Error('Erreur de connexion au serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      
      console.log('Réponse du serveur (status):', response.status);
      console.log('Réponse du serveur (statusText):', response.statusText);

      if (!response.ok) {
        console.error('Erreur API createClaim:', response.status, response.statusText);
        try {
          // Tenter de récupérer le corps de la réponse pour plus de détails
          const errorBody = await response.text();
          console.error('Détails de l\'erreur API:', errorBody);
          
          // Essayer de parser en JSON si possible
          try {
            const errorJson = JSON.parse(errorBody);
            console.error('Erreur JSON:', errorJson);
          } catch (parseError) {
            // Le corps n'est pas du JSON valide, on garde le texte brut
            console.error('Corps de réponse non-JSON');
          }
        } catch (readError) {
          console.error('Impossible de lire le corps de la réponse d\'erreur:', readError);
        }
        
        throw await handleApiError(response);
      }

      const newClaim = await response.json();
      console.log('Réponse API createClaim:', newClaim);
      
      // Formater la réclamation pour le frontend
      const formattedClaim = {
        id: newClaim.id,
        orderId: newClaim.order || newClaim.order_id || newClaim.orderId,
        date: newClaim.date || new Date(newClaim.created_at).toLocaleDateString('fr-FR'),
        status: newClaim.status_display || newClaim.status,
        subject: newClaim.subject || newClaim.title,
        description: newClaim.description || newClaim.content,
        lastUpdate: newClaim.last_update || new Date(newClaim.updated_at).toLocaleDateString('fr-FR'),
        attachments: newClaim.attachments || []
      };
      
      // Mettre à jour le localStorage avec la nouvelle réclamation
      try {
        const storedClaims = localStorage.getItem('userClaims');
        if (storedClaims) {
          const parsedData = JSON.parse(storedClaims);
          
          // Déterminer la structure des données stockées
          if (Array.isArray(parsedData)) {
            // Ajouter la nouvelle réclamation au début du tableau
            parsedData.unshift(newClaim);
            localStorage.setItem('userClaims', JSON.stringify(parsedData));
          } else if (parsedData.results) {
            // Ajouter la nouvelle réclamation au début des résultats
            parsedData.results.unshift(newClaim);
            localStorage.setItem('userClaims', JSON.stringify(parsedData));
          } else if (parsedData.claims) {
            // Ajouter la nouvelle réclamation au début des réclamations
            parsedData.claims.unshift(newClaim);
            localStorage.setItem('userClaims', JSON.stringify(parsedData));
          } else {
            // Créer un nouveau tableau avec la réclamation
            localStorage.setItem('userClaims', JSON.stringify([newClaim]));
          }
          console.log('LocalStorage mis à jour avec la nouvelle réclamation');
        } else {
          // Aucune donnée existante, créer un nouveau tableau
          localStorage.setItem('userClaims', JSON.stringify([newClaim]));
          console.log('Nouveau localStorage créé avec la réclamation');
        }
      } catch (error) {
        console.error('Erreur lors de la mise à jour du localStorage:', error);
        // Ne pas bloquer le processus en cas d'erreur de localStorage
      }
      
      return formattedClaim;
    } catch (error) {
      console.error('Error creating claim:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création de la réclamation'
      };
    }
  },

  // Get claim messages
  getClaimMessages: async (claimId: string): Promise<ClaimMessage[]> => {
    try {
      console.log('🔄 Récupération des messages pour la réclamation:', claimId);

      const response = await fetch(`${API_BASE_URL}/orders/claims/${claimId}/messages/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();
      console.log('✅ Messages récupérés:', data.length, 'messages');

      // Transformer les données pour correspondre à l'interface frontend
      return data.map((message: any) => ({
        id: message.id,
        claimId: claimId,
        message: message.message,
        sender: message.sender_name || message.sender_display || 'Expéditeur inconnu',
        date: message.created_at,
        isRead: message.is_read || true,
        attachments: message.attachments || []
      }));

    } catch (error) {
      console.error('Error fetching claim messages:', error);

      // Fallback vers des données mock en cas d'erreur
      console.warn('⚠️ Utilisation de données mock en fallback');
      return [
        {
          id: '1',
          claimId: claimId,
          message: 'Votre réclamation a été reçue et est en cours de traitement.',
          sender: 'support',
          date: new Date().toISOString(),
          isRead: true,
          attachments: []
        }
      ];
    }
  },

  // Send a message for a claim
  sendClaimMessage: async (claimId: string, message: string, attachments?: File[]): Promise<ClaimMessage> => {
    try {
      console.log('📤 Envoi d\'un message pour la réclamation:', claimId);

      // Create form data for file uploads
      const formData = new FormData();
      formData.append('message', message);

      if (attachments && attachments.length > 0) {
        attachments.forEach(file => {
          formData.append('attachments', file);
        });
        console.log('📎 Pièces jointes:', attachments.length, 'fichiers');
      }

      const response = await fetch(`${API_BASE_URL}/orders/claims/${claimId}/send_message/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
        },
        body: formData,
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();
      console.log('✅ Message envoyé avec succès');

      // Transformer la réponse pour correspondre à l'interface frontend
      return {
        id: data.id,
        claimId: claimId,
        message: data.message,
        sender: data.sender_name || data.sender_display || 'Vous',
        date: data.created_at,
        isRead: data.is_read || true,
        attachments: data.attachments || []
      };

    } catch (error) {
      console.error('Error sending claim message:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de l\'envoi du message'
      };
    }
  },

  // Create a return request
  createReturn: async (returnData: ReturnCreateData): Promise<ReturnItem> => {
    try {
      // Toujours utiliser les données dynamiques, même en développement
      console.log('Création d\'un retour avec les données:', returnData);

      // Adapter le format des données pour le backend
      // Essayons plusieurs formats possibles pour résoudre l'erreur 500
      
      // Format 1: Utiliser order_id au lieu de order (certaines API utilisent des conventions différentes)
      // Format 2: S'assurer que quantity est un nombre (pas une chaîne)
      // Format 3: Garantir que tous les champs obligatoires sont présents avec des valeurs par défaut
      
      // Vérifier que l'orderId est valide (numérique)
      const orderIdNumber = parseInt(returnData.orderId);
      if (isNaN(orderIdNumber)) {
        throw new Error('ID de commande invalide');
      }

      // Filtrer et valider les articles pour exclure les IDs fictifs
      const validItems = returnData.items.filter(item => {
        // Exclure les IDs fictifs comme 'default-item-63'
        if (String(item.orderItemId).startsWith('default-item-')) {
          console.warn('ID d\'article fictif ignoré:', item.orderItemId);
          return false;
        }

        // Vérifier que l'orderItemId est valide (numérique)
        const itemIdNumber = item.orderItemId;
        if (isNaN(itemIdNumber)) {
          console.warn('ID d\'article invalide ignoré:', item.orderItemId);
          return false;
        }
        return true;
      });

      console.log(`Articles valides trouvés: ${validItems.length} sur ${returnData.items.length}`);

      let apiReturnData;

      if (validItems.length === 0) {
        // Si aucun article valide, créer une demande de retour générale pour toute la commande
        console.log('Aucun article valide trouvé, création d\'une demande de retour générale');

        apiReturnData = {
          order: orderIdNumber,
          reason: returnData.items[0]?.reason || 'defective',
          additional_info: returnData.additionalInfo || 'Demande de retour pour la commande complète', // ✅ CORRECTION
          // Ne pas inclure d'articles spécifiques
        };
      } else {
        // Préparer les données pour l'API backend avec articles spécifiques
        apiReturnData = {
          order: orderIdNumber, // Utiliser l'ID numérique de la commande
          reason: returnData.items[0]?.reason || 'defective', // Utiliser la raison du premier article ou une valeur par défaut
          additional_info: returnData.additionalInfo || '', // ✅ CORRECTION: additional_info au lieu de description
          items: validItems.map(item => ({
            order_item_id: item.orderItemId, // ✅ CORRECTION: order_item_id au lieu de order_item
            quantity: parseInt(item.quantity.toString()) || 1, // S'assurer que c'est un nombre
            reason: item.reason || 'defective',
            description: item.description || ''
          }))
        };
      }

      // Supprimer les champs null ou undefined pour éviter des problèmes de validation
      Object.keys(apiReturnData).forEach(key => {
        if (apiReturnData[key] === null || apiReturnData[key] === undefined) {
          delete apiReturnData[key];
        }
      });

      // Log détaillé pour le débogage
      console.log('Données formatées pour l\'API (détaillé):', JSON.stringify(apiReturnData, null, 2));

      // Vérifier si le token d'authentification est disponible
      const authHeader = getAuthHeader();
      if (!authHeader) {
        console.error('Erreur d\'authentification: Token manquant');
        throw new Error('Vous devez être connecté pour créer une demande de retour');
      }
      
      // Utiliser l'URL /orders/returns/ au lieu de /account/returns/ pour être cohérent avec l'API des réclamations
      console.log('Tentative d\'appel API createReturn:', `${API_BASE_URL}/orders/returns/`);
      console.log('Données formatées pour l\'API:', apiReturnData);
      console.log('Headers d\'authentification:', authHeader);

      let response;
      try {
        response = await fetch(`${API_BASE_URL}/orders/returns/`, {
          method: 'POST',
          headers: {
            ...authHeader,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiReturnData),
        });
      } catch (fetchError) {
        console.error('Erreur lors de la requête fetch:', fetchError);
        throw new Error('Erreur de connexion au serveur. Veuillez vérifier votre connexion internet.');
      }

      if (!response.ok) {
        console.error('Erreur API createReturn:', response.status);
        try {
          // Tenter de récupérer le corps de la réponse pour plus de détails
          const errorBody = await response.text();
          console.error('Détails de l\'erreur API:', errorBody);
          
          // Essayer de parser en JSON si possible
          try {
            const errorJson = JSON.parse(errorBody);
            console.error('Erreur JSON:', errorJson);
          } catch (parseError) {
            // Le corps n'est pas du JSON valide, on garde le texte brut
            console.error('Corps de réponse non-JSON');
          }
        } catch (readError) {
          console.error('Impossible de lire le corps de la réponse d\'erreur:', readError);
        }
        
        throw await handleApiError(response);
      }

      const newReturn = await response.json();
      console.log('Réponse API createReturn:', newReturn);

      // Adapter le format pour le frontend
      const formattedReturn = {
        id: newReturn.id,
        orderId: newReturn.order || newReturn.order_id,
        status: newReturn.status_display || newReturn.status,
        date: newReturn.date || new Date(newReturn.created_at).toLocaleDateString('fr-FR'),
        items: newReturn.items_count || (newReturn.items ? newReturn.items.length : 0),
        reason: newReturn.reason_display || newReturn.reason,
        refundAmount: newReturn.refund_amount || '0,00 BIF'
      };
      
      // Mettre à jour le localStorage avec le nouveau retour
      try {
        const storedReturns = localStorage.getItem('userReturns');
        if (storedReturns) {
          const parsedData = JSON.parse(storedReturns);
          
          // Déterminer la structure des données stockées
          if (Array.isArray(parsedData)) {
            // Ajouter le nouveau retour au début du tableau
            parsedData.unshift(newReturn);
            localStorage.setItem('userReturns', JSON.stringify(parsedData));
          } else if (parsedData.results) {
            // Ajouter le nouveau retour au début des résultats
            parsedData.results.unshift(newReturn);
            localStorage.setItem('userReturns', JSON.stringify(parsedData));
          } else if (parsedData.returns) {
            // Ajouter le nouveau retour au début des retours
            parsedData.returns.unshift(newReturn);
            localStorage.setItem('userReturns', JSON.stringify(parsedData));
          } else {
            // Créer un nouveau tableau avec le retour
            localStorage.setItem('userReturns', JSON.stringify([newReturn]));
          }
          console.log('LocalStorage mis à jour avec le nouveau retour');
        } else {
          // Aucune donnée existante, créer un nouveau tableau
          localStorage.setItem('userReturns', JSON.stringify([newReturn]));
          console.log('Nouveau localStorage créé avec le retour');
        }
      } catch (error) {
        console.error('Erreur lors de la mise à jour du localStorage:', error);
        // Ne pas bloquer le processus en cas d'erreur de localStorage
      }
      
      return formattedReturn;
    } catch (error) {
      console.error('Error creating return:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création de la demande de retour'
      };
    }
  },

  // Get claim status history
  getClaimStatusHistory: async (claimId: string): Promise<ClaimStatusUpdate[]> => {
    try {
      console.log('🔄 Récupération de l\'historique des statuts pour la réclamation:', claimId);

      const response = await fetch(`${API_BASE_URL}/orders/claims/${claimId}/status_history/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();
      console.log('✅ Historique des statuts récupéré:', data.length, 'entrées');

      // Transformer les données pour correspondre à l'interface frontend
      return data.map((history: any) => ({
        id: history.id,
        claimId: claimId,
        oldStatus: history.old_status,
        newStatus: history.new_status,
        date: history.created_at,
        updatedBy: history.created_by_name || 'Système',
        comment: history.comment || ''
      }));

    } catch (error) {
      console.error('Error fetching claim status history:', error);

      // Fallback vers des données mock en cas d'erreur
      console.warn('⚠️ Utilisation de données mock en fallback pour l\'historique');
      return [
        {
          id: '1',
          claimId: claimId,
          oldStatus: '',
          newStatus: 'new',
          date: new Date().toISOString(),
          updatedBy: 'Système',
          comment: 'Réclamation créée'
        }
      ];
    }
  },

  // Get user invoices
  getInvoices: async (): Promise<Invoice[]> => {
    try {
      console.log('Récupération des factures depuis le backend...');
      const response = await fetch(`${API_BASE_URL}/payments/invoices/my_invoices/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Erreur de réponse API:', response.status, response.statusText);
        throw await handleApiError(response);
      }

      const data = await response.json();
      console.log('Données de factures reçues:', data);

      // Vérifier si data est un objet ou un tableau
      let invoicesArray = [];
      
      if (Array.isArray(data)) {
        console.log('Données reçues sous forme de tableau');
        invoicesArray = data;
      } else if (data && typeof data === 'object') {
        console.log('Données reçues sous forme d\'objet');
        // Vérifier si l'objet a une propriété results (format pagination DRF)
        if (Array.isArray(data.results)) {
          console.log('Utilisation de data.results');
          invoicesArray = data.results;
        } else {
          // Essayer de trouver une autre propriété qui pourrait contenir les factures
          const possibleArrayProps = Object.keys(data).filter(key => Array.isArray(data[key]));
          if (possibleArrayProps.length > 0) {
            console.log(`Utilisation de data.${possibleArrayProps[0]}`);
            invoicesArray = data[possibleArrayProps[0]];
          } else {
            console.warn('Aucun tableau trouvé dans les données reçues');
          }
        }
      }

      // Si aucune facture n'est trouvée, retourner un tableau vide
      if (!invoicesArray || invoicesArray.length === 0) {
        console.warn('Aucune facture trouvée dans le backend');
        return [];
      }

      // Transformer les données du backend au format attendu par le frontend
      return invoicesArray.map((invoice: any) => ({
        id: invoice.invoice_number || invoice.id,
        orderId: invoice.order_id || invoice.order,
        date: new Date(invoice.issue_date || new Date()).toLocaleDateString('fr-FR'),
        amount: `${(invoice.total || 0).toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}`,
        status: invoice.status_display || invoice.status || 'Inconnue'
      }));
    } catch (error) {
      console.error('Error fetching invoices:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      // En cas d'erreur, renvoyer des données fictives pour ne pas bloquer l'interface
      console.warn('Erreur lors de la récupération des factures, utilisation de données fictives');
      return [
        {
          id: "FAC-12345",
          orderId: "CMD-12345",
          date: "15/05/2025",
          amount: "549,780 BIF",
          status: "Payée"
        },
        {
          id: "FAC-12344",
          orderId: "CMD-12344",
          date: "02/04/2025",
          amount: "273,900 BIF",
          status: "Payée"
        }
      ];
    }
  },

  // Get suggested products
  getSuggestedProducts: async (category?: string, options?: {
    priceRange?: { min: number; max: number };
    rating?: number;
    sortBy?: string;
    limit?: number;
  }): Promise<SuggestedProduct[]> => {
    console.log('🔍 getSuggestedProducts appelé avec:', { category, options });

    try {
      // Utiliser l'API Django de recommandations intelligentes
      let url = `${API_BASE_URL}/products/products/`;

      // Construire les paramètres de requête
      const params = new URLSearchParams();
      if (category && category !== "Tous") {
        params.append('category', category);
      }
      if (options?.priceRange) {
        params.append('min_price', options.priceRange.min.toString());
        params.append('max_price', options.priceRange.max.toString());
      }
      if (options?.rating) {
        params.append('min_rating', options.rating.toString());
      }
      if (options?.sortBy) {
        params.append('sort_by', options.sortBy);
      }
      if (options?.limit) {
        params.append('limit', options.limit.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      console.log('🌐 Appel API:', url);

      // Appel à l'API pour récupérer les produits
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
      });

      console.log('📡 Réponse API:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      console.log('📦 Données reçues:', data);

      const results = Array.isArray(data) ? data : (data.results || []);

      // Transformation des données au format attendu
      const transformedProducts = results.map((product: any) => ({
        id: product.id,
        name: product.name,
        price: parseFloat(product.price) || 0,
        image: product.primary_image || product.image || `/images/products/placeholder.jpg`,
        rating: product.rating || product.average_rating || 4.0,
        category: product.category?.name || product.category_name || ''
      }));

      console.log('✅ Produits transformés:', transformedProducts.length);
      return transformedProducts;

    } catch (error) {
      console.error('❌ Erreur API getSuggestedProducts:', error);

      // Utiliser des données mock fiables
      console.log('🎭 Utilisation des données mock');

      const mockProducts: SuggestedProduct[] = [
        {
          id: 1,
          name: "Routeur Wi-Fi 6E Pro",
          price: 299.99,
          image: "/images/products/router-wifi6e.jpg",
          rating: 4.8,
          category: "Réseaux"
        },
        {
          id: 2,
          name: "Switch Gigabit 24 ports",
          price: 189.99,
          image: "/images/products/switch-24port.jpg",
          rating: 4.6,
          category: "Réseaux"
        },
        {
          id: 3,
          name: "Firewall Enterprise",
          price: 599.99,
          image: "/images/products/firewall-enterprise.jpg",
          rating: 4.9,
          category: "Sécurité"
        },
        {
          id: 4,
          name: "NAS 4 baies",
          price: 449.99,
          image: "/images/products/nas-4bay.jpg",
          rating: 4.7,
          category: "Stockage"
        },
        {
          id: 5,
          name: "Câble Ethernet Cat 6A",
          price: 29.99,
          image: "/images/products/cable-cat6a.jpg",
          rating: 4.5,
          category: "Accessoires"
        },
        {
          id: 6,
          name: "Serveur Rack 1U",
          price: 1299.99,
          image: "/images/products/server-1u.jpg",
          rating: 4.8,
          category: "Serveurs"
        }
      ];

      // Filtrer par catégorie si spécifiée
      if (category && category !== "Tous") {
        const filtered = mockProducts.filter(p => p.category === category);
        console.log(`🎭 Produits filtrés pour "${category}":`, filtered.length);
        return filtered;
      }

      console.log('🎭 Tous les produits mock retournés:', mockProducts.length);
      return mockProducts;
    }
  },
  
  // Get product categories
  getProductCategories: async (): Promise<string[]> => {
    console.log('🔍 getProductCategories appelé');

    try {
      // Appel à l'API pour récupérer les catégories depuis Django
      const url = `${API_BASE_URL}/products/categories/`;
      console.log('🌐 Appel API catégories:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
      });

      console.log('📡 Réponse API catégories:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      console.log('📦 Données catégories reçues:', data);

      const categories = Array.isArray(data) ? data : (data.results || []);

      // Extraire les noms des catégories
      const categoryNames = categories.map((category: any) => category.name || category);
      console.log('✅ Catégories transformées:', categoryNames);

      return categoryNames;
    } catch (error) {
      console.error('❌ Erreur API getProductCategories:', error);

      // Utiliser des catégories mock fiables
      console.log('🎭 Utilisation des catégories mock');
      const mockCategories = ["Réseaux", "Sécurité", "Stockage", "Accessoires", "Serveurs", "Logiciels"];

      return mockCategories;
    }
  },

  // Get user product suggestions
  getUserProductSuggestions: async (): Promise<ProductSuggestion[]> => {
    console.log('🔍 getUserProductSuggestions appelé');

    try {
      // Call the real API with correct Django URL
      const url = `${API_BASE_URL}/auth/product-suggestions/my_suggestions/`;
      console.log('🌐 Appel API suggestions utilisateur:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      console.log('📡 Réponse API suggestions:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      console.log('📦 Données suggestions reçues:', data);

      return data;
    } catch (error) {
      console.error('❌ Erreur API getUserProductSuggestions:', error);

      // Fallback to mock data in case of error
      console.log('🎭 Utilisation des données mock pour les suggestions utilisateur');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Return mock data
      const mockSuggestions: ProductSuggestion[] = [
        {
          id: 1,
          name: "Routeur Wi-Fi 7 Enterprise",
          category: "Réseaux",
          description: "Un routeur Wi-Fi 7 pour les entreprises avec des fonctionnalités avancées de sécurité et de gestion.",
          estimated_price: "299.99",
          reason: "Les routeurs Wi-Fi 7 commencent à apparaître sur le marché et seraient utiles pour les environnements professionnels exigeants.",
          status: "En attente",
          created_at: "2025-05-15T10:30:00Z",
          updated_at: "2025-05-15T10:30:00Z"
        },
        {
          id: 2,
          name: "Kit de démarrage pour réseau domestique",
          category: "Réseaux",
          description: "Un kit complet pour configurer un réseau domestique, comprenant un routeur, des câbles et un guide d'installation.",
          estimated_price: "149.99",
          reason: "Ce serait idéal pour les débutants qui veulent configurer leur réseau domestique sans tracas.",
          status: "Approuvé",
          created_at: "2025-04-20T14:15:00Z",
          updated_at: "2025-05-01T09:45:00Z",
          admin_response: "Excellente suggestion ! Nous allons ajouter ce kit à notre catalogue prochainement.",
          response_date: "2025-05-01T09:45:00Z"
        },
        {
          id: 3,
          name: "Adaptateur réseau USB-C vers Ethernet 2.5G",
          category: "Accessoires",
          description: "Un adaptateur compact permettant de connecter un ordinateur portable à un réseau filaire via USB-C.",
          estimated_price: "39.99",
          reason: "De nombreux ordinateurs portables modernes n'ont plus de port Ethernet intégré, et cet adaptateur serait très utile.",
          status: "Refusé",
          created_at: "2025-03-10T11:20:00Z",
          updated_at: "2025-03-25T16:30:00Z",
          admin_response: "Nous proposons déjà des produits similaires dans notre catalogue. Merci pour votre suggestion.",
          response_date: "2025-03-25T16:30:00Z"
        },
        {
          id: 4,
          name: "Point d'accès Wi-Fi 6E extérieur",
          category: "Réseaux",
          description: "Un point d'accès Wi-Fi 6E résistant aux intempéries pour étendre la couverture réseau à l'extérieur.",
          estimated_price: "199.99",
          reason: "Beaucoup de clients demandent des solutions pour étendre leur réseau Wi-Fi dans leur jardin ou terrasse.",
          status: "En cours d'évaluation",
          created_at: "2025-06-01T09:15:00Z",
          updated_at: "2025-06-01T09:15:00Z"
        }
      ];

      console.log('🎭 Suggestions mock retournées:', mockSuggestions.length);
      return mockSuggestions;
    }
  },

  // Create a product suggestion
  createProductSuggestion: async (data: CreateProductSuggestionRequest): Promise<ProductSuggestion> => {
    try {
      // Call the real API with correct Django URL
      const response = await fetch(`${API_BASE_URL}/auth/product-suggestions/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating product suggestion:', error);

      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data
        return {
          id: Math.floor(Math.random() * 1000) + 10,
          name: data.name,
          category: data.category,
          description: data.description,
          estimated_price: data.estimated_price,
          reason: data.reason,
          status: "En attente",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      throw {
        message: 'Une erreur est survenue lors de la création de la suggestion de produit'
      };
    }
  },

  // Update a product suggestion
  updateProductSuggestion: async (id: number, data: UpdateProductSuggestionRequest): Promise<ProductSuggestion> => {
    try {
      // Call the real API with correct Django URL
      const response = await fetch(`${API_BASE_URL}/auth/product-suggestions/${id}/`, {
        method: 'PATCH',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating product suggestion:', error);

      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return mock data
        return {
          id: id,
          name: data.name || "Nom inconnu",
          category: data.category || "Catégorie inconnue",
          description: data.description || "Description inconnue",
          estimated_price: data.estimated_price || "0",
          reason: data.reason || "Raison inconnue",
          status: "En attente",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      throw {
        message: 'Une erreur est survenue lors de la mise à jour de la suggestion de produit'
      };
    }
  },

  // Delete a product suggestion
  deleteProductSuggestion: async (id: number): Promise<void> => {
    try {
      // Call the real API with correct Django URL
      const response = await fetch(`${API_BASE_URL}/auth/product-suggestions/${id}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return;
    } catch (error) {
      console.error('Error deleting product suggestion:', error);

      // For development/testing - mock API response
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        return;
      }

      throw {
        message: 'Une erreur est survenue lors de la suppression de la suggestion de produit'
      };
    }
  },

  // Get product name suggestions for autocomplete
  getProductNameSuggestions: async (query: string): Promise<ProductNameSuggestion[]> => {
    try {
      // Appel à l'API Django pour récupérer les noms de produits depuis la base de données
      const response = await fetch(`${API_BASE_URL}/products/products/?search=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();
      
      // Transformation des données au format attendu par l'interface ProductNameSuggestion
      return data.map((product: any) => ({
        id: product.id,
        name: product.name,
        category: product.category?.name || product.category_name || '',
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération des suggestions de noms de produits:', error);
      
      // En cas d'erreur, essayer un autre endpoint
      try {
        const fallbackResponse = await fetch(`${API_BASE_URL}/products?limit=10&search=${encodeURIComponent(query)}`, {
          method: 'GET',
          headers: {
            ...getAuthHeader(),
          },
        });

        if (!fallbackResponse.ok) {
          throw await handleApiError(fallbackResponse);
        }

        const fallbackData = await fallbackResponse.json();
        const results = Array.isArray(fallbackData) ? fallbackData : (fallbackData.results || []);
        
        return results.map((product: any) => ({
          id: product.id,
          name: product.name,
          category: product.category?.name || product.category_name || '',
        }));
      } catch (fallbackError) {
        console.error('Erreur lors de la récupération des suggestions (fallback):', fallbackError);
        return [];
      }
    }
  },

  // Get suggestion statistics
  getSuggestionStats: async (): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/product-suggestions/stats/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching suggestion stats:', error);

      // Fallback to mock data
      return {
        totalSuggestions: 12,
        approvedSuggestions: 7,
        pendingSuggestions: 3,
        rejectedSuggestions: 2,
        implementedSuggestions: 4,
        averageResponseTime: 5.2,
        satisfactionRate: 87,
        topCategories: [
          { name: 'Réseaux', count: 5, percentage: 42 },
          { name: 'Stockage', count: 3, percentage: 25 },
          { name: 'Accessoires', count: 2, percentage: 17 },
          { name: 'Sécurité', count: 2, percentage: 17 }
        ],
        recentActivity: [
          {
            type: 'suggestion_implemented',
            date: '2024-01-15',
            productName: 'Kit de démarrage réseau domestique'
          },
          {
            type: 'suggestion_approved',
            date: '2024-01-10',
            productName: 'Routeur Wi-Fi 7 Enterprise'
          }
        ]
      };
    }
  },

  // Vote on a suggestion
  voteOnSuggestion: async (suggestionId: number, voteType: 'up' | 'down'): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/product-suggestions/${suggestionId}/vote/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ vote_type: voteType }),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error voting on suggestion:', error);
      throw {
        message: 'Une erreur est survenue lors du vote'
      };
    }
  },

  // Get user notifications
  getUserNotifications: async (): Promise<any[]> => {
    try {
      // Pour l'instant, utiliser les suggestions comme base pour les notifications
      const suggestions = await accountApi.getUserProductSuggestions();

      // Transformer les suggestions en notifications
      const notifications = suggestions.map((suggestion, index) => ({
        id: suggestion.id,
        type: suggestion.status === 'implemented' ? 'suggestion_implemented' :
              suggestion.status === 'approved' ? 'suggestion_approved' :
              suggestion.status === 'rejected' ? 'suggestion_rejected' :
              'suggestion_created',
        title: suggestion.status === 'implemented' ? 'Suggestion implémentée !' :
               suggestion.status === 'approved' ? 'Suggestion approuvée' :
               suggestion.status === 'rejected' ? 'Suggestion non retenue' :
               'Suggestion créée',
        message: suggestion.status === 'implemented' ?
                 `Votre suggestion "${suggestion.name}" a été ajoutée à notre catalogue.` :
                 suggestion.status === 'approved' ?
                 `Votre suggestion "${suggestion.name}" a été approuvée par notre équipe.` :
                 suggestion.status === 'rejected' ?
                 `Votre suggestion "${suggestion.name}" n'a pas été retenue.` :
                 `Votre suggestion "${suggestion.name}" a été créée avec succès.`,
        date: suggestion.created_at,
        isRead: suggestion.status === 'pending', // Les suggestions en attente sont "non lues"
        metadata: {
          productName: suggestion.name,
          category: suggestion.category
        }
      }));

      // Ajouter quelques notifications de recommandations
      if (notifications.length > 0) {
        notifications.push({
          id: 9999,
          type: 'new_recommendation',
          title: 'Nouvelles recommandations',
          message: 'De nouveaux produits correspondant à vos préférences sont disponibles.',
          date: new Date().toISOString(),
          isRead: true,
          metadata: {
            productName: 'Produits recommandés',
            category: 'Réseaux'
          }
        });
      }

      return notifications;
    } catch (error) {
      console.error('Error fetching notifications:', error);

      // Fallback vers des données simulées
      return [
        {
          id: 1,
          type: 'suggestion_implemented',
          title: 'Suggestion implémentée !',
          message: 'Votre suggestion "Kit de démarrage réseau domestique" a été ajoutée à notre catalogue.',
          date: '2024-01-15T10:30:00Z',
          isRead: false,
          metadata: {
            productName: 'Kit de démarrage réseau domestique'
          }
        },
        {
          id: 2,
          type: 'suggestion_approved',
          title: 'Suggestion approuvée',
          message: 'Votre suggestion "Routeur Wi-Fi 7 Enterprise" a été approuvée par notre équipe.',
          date: '2024-01-10T14:20:00Z',
          isRead: true,
          metadata: {
            productName: 'Routeur Wi-Fi 7 Enterprise'
          }
        }
      ];
    }
  },

  // Get user purchase history for recommendations
  getUserPurchaseHistory: async (): Promise<any[]> => {
    try {
      // Appel à l'API pour récupérer l'historique des commandes
      const response = await fetch(`${API_BASE_URL}/orders/orders/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const orders = await response.json();

      // Transformer les commandes en historique d'achats
      const purchaseHistory = [];

      if (orders.results) {
        for (const order of orders.results) {
          if (order.items) {
            for (const item of order.items) {
              purchaseHistory.push({
                id: item.id,
                productName: item.product_name || item.product?.name || 'Produit',
                category: item.product?.category || 'Général',
                purchaseDate: order.created_at,
                price: parseFloat(item.price || 0),
                image: item.product?.image || '/placeholder-product.jpg',
                orderId: order.id
              });
            }
          }
        }
      }

      return purchaseHistory;
    } catch (error) {
      console.error('Error fetching purchase history:', error);

      // Fallback vers des données simulées
      return [
        {
          id: 1,
          productName: 'Routeur Wi-Fi 6 AX3000',
          category: 'Réseaux',
          purchaseDate: '2024-01-10T10:00:00Z',
          price: 180000,
          image: '/placeholder-product.jpg'
        },
        {
          id: 2,
          productName: 'Câble Ethernet Cat 6A 10m',
          category: 'Accessoires',
          purchaseDate: '2024-01-05T15:30:00Z',
          price: 25000,
          image: '/placeholder-product.jpg'
        },
        {
          id: 3,
          productName: 'Disque SSD NVMe 1TB',
          category: 'Stockage',
          purchaseDate: '2023-12-20T09:15:00Z',
          price: 220000,
          image: '/placeholder-product.jpg'
        }
      ];
    }
  },

  // Get smart recommendations based on purchase history
  getSmartRecommendations: async (): Promise<any[]> => {
    try {
      // Utiliser l'API de recommandations intelligentes Django
      const recommendations = await accountApi.getSuggestedProducts(undefined, {
        limit: 8
      });

      // Transformer en recommandations intelligentes avec raisons
      return recommendations.map((product, index) => ({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
        rating: product.rating,
        category: product.category,
        reason: {
          type: index % 4 === 0 ? 'similar_category' :
                index % 4 === 1 ? 'frequently_bought_together' :
                index % 4 === 2 ? 'upgrade' : 'complementary',
          description: index % 4 === 0 ? `Basé sur votre achat dans la catégorie ${product.category}` :
                      index % 4 === 1 ? 'Souvent acheté avec vos produits précédents' :
                      index % 4 === 2 ? 'Mise à niveau recommandée de vos équipements' :
                      'Complément idéal pour vos équipements actuels'
        },
        confidence: Math.floor(Math.random() * 20) + 75 // 75-95%
      }));
    } catch (error) {
      console.error('Error fetching smart recommendations:', error);

      // Fallback vers des données simulées
      return [
        {
          id: 101,
          name: 'Switch Gigabit 24 ports',
          price: 320000,
          image: '/placeholder-product.jpg',
          rating: 4.6,
          category: 'Réseaux',
          reason: {
            type: 'similar_category',
            description: 'Basé sur votre achat de routeur Wi-Fi'
          },
          confidence: 92
        },
        {
          id: 102,
          name: 'Onduleur UPS 1500VA',
          price: 280000,
          image: '/placeholder-product.jpg',
          rating: 4.4,
          category: 'Sécurité',
          reason: {
            type: 'complementary',
            description: 'Protège vos équipements réseau'
          },
          confidence: 88
        }
      ];
    }
  },

  // Submit recommendation feedback
  submitRecommendationFeedback: async (feedbackData: {
    rating: number;
    relevance: string;
    feedback: string;
    wouldRecommend: boolean;
  }): Promise<any> => {
    try {
      // Pour l'instant, on peut créer un endpoint dédié ou utiliser les commentaires
      const response = await fetch(`${API_BASE_URL}/auth/feedback/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'recommendation_feedback',
          rating: feedbackData.rating,
          relevance: feedbackData.relevance,
          comment: feedbackData.feedback,
          would_recommend: feedbackData.wouldRecommend,
          created_at: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error submitting feedback:', error);

      // Simuler une réponse réussie pour le fallback
      return {
        id: Date.now(),
        message: 'Feedback enregistré avec succès',
        status: 'success'
      };
    }
  },

  // Get feedback statistics
  getFeedbackStats: async (): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/feedback/stats/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching feedback stats:', error);

      // Fallback vers des statistiques simulées
      return {
        satisfactionRate: 95,
        totalFeedbacks: 1247,
        averageRating: 4.2,
        recommendationRate: 87
      };
    }
  },

  // Télécharger une facture au format PDF
  downloadInvoice: async (invoiceId: string): Promise<Blob> => {
    try {
      console.log(`Téléchargement de la facture ${invoiceId}...`);

      // Si l'invoiceId est un invoice_number (FAC-XXXXX), récupérer d'abord l'ID numérique
      let numericId = invoiceId;

      if (invoiceId.startsWith('FAC-')) {
        console.log(`Conversion de l'invoice_number ${invoiceId} en ID numérique...`);

        // Récupérer les détails de la facture pour obtenir l'ID numérique
        const detailResponse = await fetch(`${API_BASE_URL}/payments/account/invoices/${invoiceId}/`, {
          method: 'GET',
          headers: {
            ...getAuthHeader(),
          },
        });

        if (detailResponse.ok) {
          const invoiceData = await detailResponse.json();
          numericId = invoiceData.id.toString();
          console.log(`ID numérique trouvé: ${numericId}`);
        } else {
          console.error('Impossible de récupérer l\'ID numérique de la facture');
          throw await handleApiError(detailResponse);
        }
      }

      // Maintenant télécharger avec l'ID numérique
      const response = await fetch(`${API_BASE_URL}/payments/invoices/${numericId}/download/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Erreur de réponse API:', response.status, response.statusText);
        throw await handleApiError(response);
      }

      return await response.blob();
    } catch (error) {
      console.error('Error downloading invoice:', error);
      throw {
        message: 'Une erreur est survenue lors du téléchargement de la facture'
      };
    }
  },

  // Télécharger toutes les factures au format ZIP
  downloadAllInvoices: async (): Promise<Blob> => {
    try {
      const headers = getAuthHeader();
      
      if (headers) {
        // Real API call when backend is ready
        try {
          const response = await fetch(`${API_BASE_URL}/account/invoices/download-all`, {
            method: 'GET',
            headers
          });
          
          if (response.ok) {
            return await response.blob();
          } else {
            throw { message: 'Erreur lors du téléchargement des factures', status: response.status };
          }
        } catch (error) {
          handleApiError(error);
          throw error;
        }
      } else {
        throw new Error('Authentication required');
      }
    } catch (error) {
      console.error('Error downloading all invoices:', error);
      throw error;
    }
  },

  // Note: La fonction getUserOrders a été déplacée plus haut dans le fichier
  
  
  // Get invoice details
  getInvoiceDetails: async (invoiceId: string): Promise<InvoiceDetail> => {
    try {
      // Toujours utiliser les données dynamiques, même en développement
      console.log(`Récupération des détails de la facture ${invoiceId}...`);
      
      const response = await fetch(`${API_BASE_URL}/payments/account/invoices/${invoiceId}/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const data = await response.json();
      
      // Log des données reçues pour debug
      console.log('Données facture reçues du backend:', data);

      // Transformer les données pour qu'elles correspondent au format attendu par le frontend
      return {
        id: data.invoice_number || data.id,
        orderId: data.order_id || data.order,
        date: data.issue_date ? new Date(data.issue_date).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR'),
        amount: data.total ? `${data.total.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}` : '0 BIF',
        status: data.status_display || data.status || 'Inconnue',
        customerInfo: {
          // Utiliser la structure customer_info du sérialiseur backend
          name: data.customer_info?.name || '',
          email: data.customer_info?.email || '',
          address: data.customer_info?.address || '',
          city: data.customer_info?.city || '',
          postalCode: data.customer_info?.postalCode || '',
          country: data.customer_info?.country || ''
        },
        items: Array.isArray(data.items) ? data.items.map((item: any) => ({
          id: item.id,
          name: item.product_name,
          quantity: item.quantity,
          unitPrice: item.price ? `${item.price.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}` : '0 BIF',
          totalPrice: item.final_price ? `${item.final_price.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}` : '0 BIF'
        })) : [],
        subtotal: data.subtotal ? `${data.subtotal.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}` : '0 BIF',
        tax: data.tax_amount ? `${data.tax_amount.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}` : '0 BIF',
        shipping: data.shipping_amount ? `${data.shipping_amount.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}` : '0 BIF',
        discount: data.discount_amount ? `${data.discount_amount.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}` : undefined,
        paymentMethod: data.payment_method || 'Inconnue',
        paymentId: data.payment_id
      };
    } catch (error) {
      console.error('Error fetching invoice details:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des détails de la facture'
      };
    }
  }
};
