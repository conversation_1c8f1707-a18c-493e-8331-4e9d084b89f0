import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  RefreshCw, 
  FileText, 
  Download,
  ShoppingCart,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  User,
  MapPin,
  Mail,
  Phone
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import paymentApi, { InvoiceDetail as IInvoiceDetail } from '@/services/paymentApi';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate, formatCurrency } from '@/utils/formatters';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const InvoiceDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [invoice, setInvoice] = useState<IInvoiceDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    if (id) {
      fetchInvoice();
    }
  }, [id]);

  const fetchInvoice = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const data = await paymentApi.getInvoice(Number(id));
      setInvoice(data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch invoice');
      toast({
        title: "Error",
        description: "Failed to fetch invoice",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!id) return;
    
    try {
      const blob = await paymentApi.downloadInvoice(Number(id));
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${invoice?.invoice_number || id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err: any) {
      toast({
        title: "Error",
        description: "Failed to download invoice",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'issued':
        return <Clock className="h-6 w-6 text-blue-500" />;
      case 'draft':
        return <FileText className="h-6 w-6 text-gray-500" />;
      case 'cancelled':
        return <XCircle className="h-6 w-6 text-red-500" />;
      default:
        return <FileText className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>;
      case 'issued':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Issued</Badge>;
      case 'paid':
        return <Badge variant="default" className="bg-green-100 text-green-800">Paid</Badge>;
      case 'cancelled':
        return <Badge variant="destructive" className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Invoice Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !invoice) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Invoice Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-4">
            <p className="text-red-500">{error || 'Invoice not found'}</p>
            <Button onClick={fetchInvoice} className="mt-2">Retry</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Invoice {invoice.invoice_number}</CardTitle>
          <CardDescription>
            Issued on {formatDate(invoice.issue_date)}
          </CardDescription>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate('/admin/invoices')} className="flex items-center gap-1">
            <ArrowLeft size={16} />
            Back to List
          </Button>
          <Button variant="outline" onClick={fetchInvoice} className="flex items-center gap-1">
            <RefreshCw size={16} />
            Refresh
          </Button>
          <Button onClick={handleDownload} className="flex items-center gap-1">
            <Download size={16} />
            Download PDF
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="text-lg">Invoice Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(invoice.status)}
                    <span className="font-medium">Status</span>
                  </div>
                  <div>
                    {getStatusBadge(invoice.status)}
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">Issue Date</span>
                  </div>
                  <div>
                    {formatDate(invoice.issue_date)}
                  </div>
                </div>
                
                {invoice.due_date && (
                  <>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-gray-500" />
                        <span className="font-medium">Due Date</span>
                      </div>
                      <div>
                        {formatDate(invoice.due_date)}
                      </div>
                    </div>
                  </>
                )}
                
                {invoice.paid_date && (
                  <>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-gray-500" />
                        <span className="font-medium">Paid Date</span>
                      </div>
                      <div>
                        {formatDate(invoice.paid_date)}
                      </div>
                    </div>
                  </>
                )}
                
                <Separator />
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">Order</span>
                  </div>
                  <div>
                    <Button 
                      variant="link" 
                      onClick={() => navigate(`/admin/orders/${invoice.order}`)}
                      className="p-0 h-auto"
                    >
                      #{invoice.order}
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="mt-8">
                <h3 className="text-lg font-medium mb-4">Invoice Items</h3>
                
                {invoice.items?.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                        <TableHead className="text-right">Price</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invoice.items.map((item: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell>{item.product_name}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">{formatCurrency(item.price)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(item.subtotal)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <p className="text-gray-500 text-center">No items found</p>
                )}
                
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Subtotal</span>
                    <span>{formatCurrency(invoice.subtotal)}</span>
                  </div>
                  
                  {invoice.tax_amount > 0 && (
                    <div className="flex justify-between">
                      <span className="font-medium">Tax</span>
                      <span>{formatCurrency(invoice.tax_amount)}</span>
                    </div>
                  )}
                  
                  {invoice.shipping_amount > 0 && (
                    <div className="flex justify-between">
                      <span className="font-medium">Shipping</span>
                      <span>{formatCurrency(invoice.shipping_amount)}</span>
                    </div>
                  )}
                  
                  {invoice.discount_amount > 0 && (
                    <div className="flex justify-between">
                      <span className="font-medium">Discount</span>
                      <span>-{formatCurrency(invoice.discount_amount)}</span>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total</span>
                    <span>{formatCurrency(invoice.total)}</span>
                  </div>
                </div>
              </div>
              
              {invoice.notes && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-2">Notes</h3>
                  <p className="text-gray-700 p-3 bg-gray-50 rounded-md">{invoice.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customer Information</CardTitle>
            </CardHeader>
            <CardContent>
              {invoice.order_details ? (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <User className="h-5 w-5 text-gray-500" />
                      <span className="font-medium">Customer</span>
                    </div>
                    <p className="ml-7">
                      {invoice.order_details.billing_first_name} {invoice.order_details.billing_last_name}
                    </p>
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Mail className="h-5 w-5 text-gray-500" />
                      <span className="font-medium">Email</span>
                    </div>
                    <p className="ml-7">{invoice.order_details.email}</p>
                  </div>
                  
                  {invoice.order_details.phone && (
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <Phone className="h-5 w-5 text-gray-500" />
                        <span className="font-medium">Phone</span>
                      </div>
                      <p className="ml-7">{invoice.order_details.phone}</p>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <MapPin className="h-5 w-5 text-gray-500" />
                      <span className="font-medium">Billing Address</span>
                    </div>
                    <div className="ml-7">
                      <p>{invoice.order_details.billing_address_line1}</p>
                      {invoice.order_details.billing_address_line2 && (
                        <p>{invoice.order_details.billing_address_line2}</p>
                      )}
                      <p>
                        {invoice.order_details.billing_city}, {invoice.order_details.billing_postal_code}
                      </p>
                      <p>{invoice.order_details.billing_country}</p>
                    </div>
                  </div>
                  
                  {invoice.order_details.shipping_address_line1 && (
                    <>
                      <Separator />
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <MapPin className="h-5 w-5 text-gray-500" />
                          <span className="font-medium">Shipping Address</span>
                        </div>
                        <div className="ml-7">
                          <p>{invoice.order_details.shipping_address_line1}</p>
                          {invoice.order_details.shipping_address_line2 && (
                            <p>{invoice.order_details.shipping_address_line2}</p>
                          )}
                          <p>
                            {invoice.order_details.shipping_city}, {invoice.order_details.shipping_postal_code}
                          </p>
                          <p>{invoice.order_details.shipping_country}</p>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 text-center">No customer information available</p>
              )}
              
              <div className="mt-6">
                <Button 
                  variant="outline" 
                  className="w-full flex items-center gap-1"
                  onClick={() => navigate(`/admin/orders/${invoice.order}`)}
                >
                  <ShoppingCart size={16} />
                  View Order Details
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
};

export default InvoiceDetail;
