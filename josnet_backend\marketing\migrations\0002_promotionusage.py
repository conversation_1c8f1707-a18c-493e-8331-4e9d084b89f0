from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0001_initial'),
        ('authentication', '0001_initial'),
        ('marketing', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PromotionUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discount_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotion_usages', to='orders.order')),
                ('promotion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usages', to='marketing.promotion')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='promotion_usages', to='authentication.user')),
            ],
            options={
                'verbose_name': 'Promotion Usage',
                'verbose_name_plural': 'Promotion Usages',
                'ordering': ['-created_at'],
                'unique_together': {('promotion', 'order')},
            },
        ),
    ]
