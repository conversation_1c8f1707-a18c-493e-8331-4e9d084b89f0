import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { 
  Search, 
  X, 
  RefreshCw, 
  Filter, 
  ChevronDown, 
  ChevronUp, 
  AlertCircle 
} from 'lucide-react';
import { productApi } from '@/services/productApi';

// Types
interface SearchFacet {
  name: string;
  field: string;
  values: {
    value: string;
    count: number;
    selected?: boolean;
  }[];
}

interface FacetedSearchProps {
  onSearch: (query: string, selectedFacets: Record<string, string[]>) => void;
  initialQuery?: string;
  initialFacets?: Record<string, string[]>;
}

const FacetedSearch: React.FC<FacetedSearchProps> = ({
  onSearch,
  initialQuery = '',
  initialFacets = {},
}) => {
  // State
  const [query, setQuery] = useState(initialQuery);
  const [selectedFacets, setSelectedFacets] = useState<Record<string, string[]>>(initialFacets);
  const [expandedFacets, setExpandedFacets] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  
  // Fetch facets
  const { 
    data: facets = [], 
    isLoading: isLoadingFacets,
    isError: isFacetsError,
    refetch: refetchFacets
  } = useQuery({
    queryKey: ['productFacets', { query, selectedFacets }],
    queryFn: () => productApi.getProductFacets({ query, facets: selectedFacets }),
    enabled: false, // Don't fetch automatically
  });
  
  // Initialize expanded facets
  useEffect(() => {
    if (facets.length > 0 && expandedFacets.length === 0) {
      // Expand all facets by default
      setExpandedFacets(facets.map(facet => facet.field));
    }
  }, [facets, expandedFacets]);
  
  // Handle search
  const handleSearch = () => {
    setIsSearching(true);
    refetchFacets().then(() => {
      setIsSearching(false);
      onSearch(query, selectedFacets);
    });
  };
  
  // Handle facet selection
  const handleFacetSelection = (field: string, value: string, selected: boolean) => {
    setSelectedFacets(prev => {
      const currentValues = prev[field] || [];
      const newValues = selected
        ? [...currentValues, value]
        : currentValues.filter(v => v !== value);
      
      return {
        ...prev,
        [field]: newValues.length > 0 ? newValues : []
      };
    });
  };
  
  // Reset all facets
  const resetFacets = () => {
    setSelectedFacets({});
  };
  
  // Count selected facets
  const countSelectedFacets = () => {
    return Object.values(selectedFacets).reduce(
      (count, values) => count + values.length, 
      0
    );
  };
  
  // Get facet display name
  const getFacetDisplayName = (field: string): string => {
    switch (field) {
      case 'category':
        return 'Catégorie';
      case 'brand':
        return 'Marque';
      case 'price_range':
        return 'Gamme de prix';
      case 'rating':
        return 'Évaluation';
      case 'status':
        return 'Statut';
      case 'in_stock':
        return 'Disponibilité';
      case 'on_sale':
        return 'Promotion';
      default:
        // Handle attribute facets
        if (field.startsWith('attr_')) {
          return field.replace('attr_', '').replace(/_/g, ' ');
        }
        return field.replace(/_/g, ' ');
    }
  };
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Recherche par facettes</CardTitle>
        <CardDescription>
          Recherchez des produits et filtrez par facettes
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search input */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Rechercher des produits..."
              className="pl-8"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
            {query && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 h-7 w-7"
                onClick={() => setQuery('')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <Button 
            onClick={handleSearch}
            disabled={isSearching}
          >
            {isSearching ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Search className="h-4 w-4" />
            )}
            <span className="ml-2">Rechercher</span>
          </Button>
        </div>
        
        {/* Selected facets summary */}
        {countSelectedFacets() > 0 && (
          <div className="flex flex-wrap gap-2 pt-2">
            {Object.entries(selectedFacets).map(([field, values]) => 
              values.map(value => (
                <Badge 
                  key={`${field}-${value}`}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  <span>{getFacetDisplayName(field)}: {value}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => handleFacetSelection(field, value, false)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))
            )}
            
            <Button
              variant="outline"
              size="sm"
              className="h-6"
              onClick={resetFacets}
            >
              <X className="h-3 w-3 mr-1" />
              Effacer tout
            </Button>
          </div>
        )}
        
        {/* Facets */}
        <div className="pt-2">
          {isLoadingFacets ? (
            <div className="space-y-4">
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : isFacetsError ? (
            <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
              <AlertCircle className="h-5 w-5" />
              <span>Une erreur est survenue lors du chargement des facettes.</span>
            </div>
          ) : facets.length === 0 ? (
            <div className="text-center py-8 text-gray-500 border border-dashed rounded-md">
              <Filter className="h-12 w-12 mx-auto mb-2 text-gray-400" />
              <p>Aucune facette disponible.</p>
              <p className="text-sm">Effectuez une recherche pour voir les facettes.</p>
            </div>
          ) : (
            <Accordion
              type="multiple"
              value={expandedFacets}
              onValueChange={setExpandedFacets}
              className="w-full"
            >
              {facets.map(facet => (
                <AccordionItem 
                  key={facet.field} 
                  value={facet.field}
                  className="border-b"
                >
                  <AccordionTrigger className="py-2">
                    <div className="flex items-center">
                      <span className="text-sm font-medium">{getFacetDisplayName(facet.field)}</span>
                      {(selectedFacets[facet.field] || []).length > 0 && (
                        <Badge className="ml-2">
                          {(selectedFacets[facet.field] || []).length}
                        </Badge>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-1 max-h-40 overflow-y-auto">
                      {facet.values.map(({ value, count, selected }) => (
                        <div key={value} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`facet-${facet.field}-${value}`}
                              checked={(selectedFacets[facet.field] || []).includes(value)}
                              onCheckedChange={(checked) => 
                                handleFacetSelection(facet.field, value, !!checked)
                              }
                            />
                            <Label 
                              htmlFor={`facet-${facet.field}-${value}`}
                              className="text-sm"
                            >
                              {value}
                            </Label>
                          </div>
                          <span className="text-xs text-gray-500">({count})</span>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="border-t pt-4 flex justify-between">
        <div className="text-sm text-gray-500">
          {countSelectedFacets()} facette{countSelectedFacets() !== 1 ? 's' : ''} sélectionnée{countSelectedFacets() !== 1 ? 's' : ''}
        </div>
        
        <div className="flex gap-2">
          {countSelectedFacets() > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={resetFacets}
            >
              Réinitialiser
            </Button>
          )}
          
          <Button 
            size="sm" 
            onClick={handleSearch}
            disabled={isSearching}
          >
            {isSearching ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            Appliquer
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

// Mock API implementation for facets
const mockFacets: SearchFacet[] = [
  {
    name: 'Catégorie',
    field: 'category',
    values: [
      { value: 'Électronique', count: 120 },
      { value: 'Vêtements', count: 85 },
      { value: 'Maison', count: 64 },
      { value: 'Jardin', count: 32 },
      { value: 'Sports', count: 28 },
    ]
  },
  {
    name: 'Marque',
    field: 'brand',
    values: [
      { value: 'Apple', count: 45 },
      { value: 'Samsung', count: 38 },
      { value: 'Nike', count: 27 },
      { value: 'Adidas', count: 22 },
      { value: 'Sony', count: 18 },
    ]
  },
  {
    name: 'Gamme de prix',
    field: 'price_range',
    values: [
      { value: 'Moins de 50€', count: 87 },
      { value: '50€ - 100€', count: 64 },
      { value: '100€ - 200€', count: 43 },
      { value: '200€ - 500€', count: 32 },
      { value: 'Plus de 500€', count: 18 },
    ]
  },
  {
    name: 'Couleur',
    field: 'attr_color',
    values: [
      { value: 'Noir', count: 75 },
      { value: 'Blanc', count: 68 },
      { value: 'Bleu', count: 42 },
      { value: 'Rouge', count: 38 },
      { value: 'Vert', count: 25 },
    ]
  },
  {
    name: 'Taille',
    field: 'attr_size',
    values: [
      { value: 'S', count: 45 },
      { value: 'M', count: 62 },
      { value: 'L', count: 58 },
      { value: 'XL', count: 32 },
      { value: 'XXL', count: 18 },
    ]
  },
  {
    name: 'Disponibilité',
    field: 'in_stock',
    values: [
      { value: 'En stock', count: 245 },
      { value: 'Rupture de stock', count: 38 },
    ]
  },
  {
    name: 'Promotion',
    field: 'on_sale',
    values: [
      { value: 'En promotion', count: 87 },
      { value: 'Prix normal', count: 196 },
    ]
  },
];

// Add mock implementation to productApi
if (!productApi.getProductFacets) {
  (productApi as any).getProductFacets = async ({ query, facets }: { query: string, facets: Record<string, string[]> }) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return mock facets
    return mockFacets.map(facet => ({
      ...facet,
      values: facet.values.map(value => ({
        ...value,
        selected: (facets[facet.field] || []).includes(value.value)
      }))
    }));
  };
}

export default FacetedSearch;
