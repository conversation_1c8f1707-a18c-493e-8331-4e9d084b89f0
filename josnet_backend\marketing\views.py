from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from .models import Promotion, Campaign
from .serializers import PromotionSerializer, CampaignSerializer
from products.models import Product
from authentication.permissions import IsStaffOrAdmin

# Devise utilisée dans l'application
CURRENCY = "BIF"

class PromotionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for promotions.
    """
    queryset = Promotion.objects.all()
    serializer_class = PromotionSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active', 'applies_to', 'target_id']
    permission_classes = [IsStaffOrAdmin]
    
    @action(detail=False, methods=['get'], permission_classes=[])
    def active(self):
        """
        Liste des promotions actives pour les clients.
        Ne nécessite pas d'authentification.
        """
        promotions = Promotion.objects.filter(is_active=True)
        serializer = self.get_serializer(promotions, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'], permission_classes=[], url_path='public')
    def public_detail(self, request, pk=None):
        """
        Détails d'une promotion spécifique pour les clients.
        Ne nécessite pas d'authentification.
        """
        try:
            promotion = Promotion.objects.get(pk=pk, is_active=True)
            serializer = self.get_serializer(promotion)
            return Response(serializer.data)
        except Promotion.DoesNotExist:
            return Response(
                {"detail": "Promotion non trouvée ou non active."}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['post'], permission_classes=[], url_path='validate-code')
    def validate_code(self, request):
        """
        Valide un code promo et retourne le montant de la remise applicable.
        Ne nécessite pas d'authentification.
        """
        code = request.data.get('code')
        cart_total = request.data.get('cart_total', 0)
        
        if not code:
            return Response(
                {"detail": "Le code promo est requis."}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Vérifier si le code promo existe et est actif
            promotion = Promotion.objects.get(
                discount_code=code,
                is_active=True,
                start_date__lte=timezone.now()
            )
            
            # Vérifier si la promotion n'est pas expirée
            if promotion.end_date and promotion.end_date < timezone.now():
                return Response(
                    {"valid": False, "message": "Ce code promo a expiré.", "discount_amount": 0, "promotion_id": promotion.id}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Vérifier le montant minimum d'achat si applicable
            if promotion.min_purchase_amount and float(cart_total) < float(promotion.min_purchase_amount):
                return Response(
                    {
                        "valid": False, 
                        "message": f"Ce code promo nécessite un montant minimum d'achat de {promotion.min_purchase_amount} {CURRENCY}.", 
                        "discount_amount": 0, 
                        "promotion_id": promotion.id
                    }, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Calculer le montant de la remise
            discount_amount = 0
            if promotion.discount_type == 'percentage':
                discount_amount = float(cart_total) * float(promotion.discount_value) / 100
            else:  # fixed_amount
                discount_amount = float(promotion.discount_value)
            
            # S'assurer que la remise ne dépasse pas le montant du panier
            discount_amount = min(discount_amount, float(cart_total))
            
            return Response({
                "valid": True,
                "message": "Code promo appliqué avec succès.",
                "discount_amount": discount_amount,
                "promotion_id": promotion.id,
                "promotion": self.get_serializer(promotion).data
            })
            
        except Promotion.DoesNotExist:
            return Response(
                {"valid": False, "message": "Code promo invalide. Le code promo que vous avez saisi n'est pas valide.", "discount_amount": 0, "promotion_id": 0}, 
                status=status.HTTP_404_NOT_FOUND
            )

class CampaignViewSet(viewsets.ModelViewSet):
    """
    API endpoint for marketing campaigns.
    """
    queryset = Campaign.objects.all()
    serializer_class = CampaignSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active']
    permission_classes = [IsStaffOrAdmin]
    
    @action(detail=True, methods=['post'])
    def add_product(self, request, pk=None):
        """
        Add a product to the campaign.
        
        Example request:
        {
            "product_id": 1
        }
        """
        campaign = self.get_object()
        product_id = request.data.get('product_id')
        
        if not product_id:
            return Response(
                {"error": "product_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            product = Product.objects.get(pk=product_id)
            campaign.products.add(product)
            return Response({"status": "product added to campaign"})
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['post'])
    def remove_product(self, request, pk=None):
        """
        Remove a product from the campaign.
        
        Example request:
        {
            "product_id": 1
        }
        """
        campaign = self.get_object()
        product_id = request.data.get('product_id')
        
        if not product_id:
            return Response(
                {"error": "product_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            product = Product.objects.get(pk=product_id)
            campaign.products.remove(product)
            return Response({"status": "product removed from campaign"})
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """
        Get campaigns for a specific product.
        
        Example request:
        GET /api/v1/marketing/campaigns/by_product/?product_id=1
        """
        product_id = request.query_params.get('product_id')
        
        if not product_id:
            return Response(
                {"error": "product_id query parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        campaigns = self.get_queryset().filter(products__id=product_id)
        serializer = self.get_serializer(campaigns, many=True)
        return Response(serializer.data)
