/**
 * API pour les articles de blog depuis le CMS
 */

import axios from 'axios';
import { API_BASE_URL } from '@/config/api';

// Types pour les articles de blog
export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image?: string;
  author: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  };
  category: {
    id: number;
    name: string;
    slug: string;
  };
  tags: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  published_at: string;
  created_at: string;
  updated_at: string;
  meta_title?: string;
  meta_description?: string;
  reading_time?: number;
  view_count?: number;
}

export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  post_count?: number;
}

export interface BlogTag {
  id: number;
  name: string;
  slug: string;
  post_count?: number;
}

export interface BlogListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BlogPost[];
}

export interface BlogFilters {
  search?: string;
  category?: string;
  tag?: string;
  featured?: boolean;
  page?: number;
  page_size?: number;
}

// Configuration axios
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/cms`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Données de fallback pour le développement
const fallbackPosts: BlogPost[] = [
  {
    id: 1,
    title: "Les tendances technologiques à surveiller en 2025",
    slug: "tendances-technologiques-2025",
    content: "Contenu complet de l'article...",
    excerpt: "Découvrez les innovations qui vont façonner l'avenir de la technologie et transformer nos vies quotidiennes.",
    featured_image: "https://images.unsplash.com/photo-1607799279861-4dd421887fb3?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8dGVjaCUyMHRyZW5kc3xlbnwwfHwwfHx8MA%3D%3D",
    author: {
      id: 1,
      first_name: "Jean",
      last_name: "Dupont",
      email: "<EMAIL>"
    },
    category: {
      id: 1,
      name: "Technologie",
      slug: "technologie"
    },
    tags: [
      { id: 1, name: "IA", slug: "ia" },
      { id: 2, name: "IoT", slug: "iot" },
      { id: 3, name: "5G", slug: "5g" },
      { id: 4, name: "Tendances", slug: "tendances" }
    ],
    status: 'published',
    is_featured: true,
    featured: true,
    published_at: "2025-05-12T10:00:00Z",
    created_at: "2025-05-10T10:00:00Z",
    updated_at: "2025-05-12T10:00:00Z",
    reading_time: 5,
    view_count: 150
  },
  {
    id: 2,
    title: "Comment renforcer la sécurité de votre réseau domestique",
    slug: "securite-reseau-domestique",
    content: "Contenu complet de l'article...",
    excerpt: "Protégez vos appareils et vos données personnelles avec ces conseils pratiques pour sécuriser votre réseau Wi-Fi.",
    featured_image: "https://images.unsplash.com/photo-1614064641938-3bbee52942c7?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8bmV0d29yayUyMHNlY3VyaXR5fGVufDB8fDB8fHww",
    author: {
      id: 2,
      first_name: "Marie",
      last_name: "Lambert",
      email: "<EMAIL>"
    },
    category: {
      id: 2,
      name: "Cybersécurité",
      slug: "cybersecurite"
    },
    tags: [
      { id: 5, name: "Sécurité", slug: "securite" },
      { id: 6, name: "Wi-Fi", slug: "wifi" },
      { id: 7, name: "Réseau", slug: "reseau" },
      { id: 8, name: "Protection", slug: "protection" }
    ],
    status: 'published',
    is_featured: false,
    featured: true,
    published_at: "2025-05-05T10:00:00Z",
    created_at: "2025-05-03T10:00:00Z",
    updated_at: "2025-05-05T10:00:00Z",
    reading_time: 7,
    view_count: 89
  },
  {
    id: 3,
    title: "Les meilleurs outils bureautiques pour le télétravail",
    slug: "outils-bureautiques-teletravail",
    content: "Contenu complet de l'article...",
    excerpt: "Maximisez votre productivité à distance avec cette sélection d'outils de collaboration et de gestion de projet.",
    featured_image: "https://images.unsplash.com/photo-1598257006458-087169a1f08d?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8cmVtb3RlJTIwd29ya3xlbnwwfHwwfHx8MA%3D%3D",
    author: {
      id: 3,
      first_name: "Thomas",
      last_name: "Martin",
      email: "<EMAIL>"
    },
    category: {
      id: 3,
      name: "Productivité",
      slug: "productivite"
    },
    tags: [
      { id: 9, name: "Télétravail", slug: "teletravail" },
      { id: 10, name: "Collaboration", slug: "collaboration" },
      { id: 11, name: "Outils", slug: "outils" },
      { id: 12, name: "Productivité", slug: "productivite" }
    ],
    status: 'published',
    is_featured: false,
    featured: false,
    published_at: "2025-04-28T10:00:00Z",
    created_at: "2025-04-26T10:00:00Z",
    updated_at: "2025-04-28T10:00:00Z",
    reading_time: 6,
    view_count: 124
  }
];

const fallbackCategories: BlogCategory[] = [
  { id: 1, name: "Technologie", slug: "technologie", post_count: 5 },
  { id: 2, name: "Cybersécurité", slug: "cybersecurite", post_count: 3 },
  { id: 3, name: "Productivité", slug: "productivite", post_count: 4 },
  { id: 4, name: "Entreprise", slug: "entreprise", post_count: 2 },
  { id: 5, name: "Télécommunications", slug: "telecommunications", post_count: 3 },
  { id: 6, name: "Conseils d'achat", slug: "conseils-achat", post_count: 2 }
];

// API functions
const blogApi = {
  // Récupérer tous les articles publiés
  getPosts: async (filters: BlogFilters = {}): Promise<BlogListResponse> => {
    try {
      const params = new URLSearchParams();

      // Ajouter les filtres
      if (filters.search) params.append('search', filters.search);
      if (filters.category) params.append('category', filters.category);
      if (filters.tag) params.append('tag', filters.tag);
      if (filters.featured !== undefined) params.append('featured', filters.featured.toString());
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.page_size) params.append('page_size', filters.page_size.toString());

      // Toujours filtrer les articles publiés
      params.append('status', 'published');

      const response = await apiClient.get(`/blog/posts/?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.warn('API CMS non disponible, utilisation des données de fallback:', error);

      // Filtrer les données de fallback
      let filteredPosts = [...fallbackPosts];

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredPosts = filteredPosts.filter(post =>
          post.title.toLowerCase().includes(searchLower) ||
          post.excerpt.toLowerCase().includes(searchLower) ||
          post.tags.some(tag => tag.name.toLowerCase().includes(searchLower))
        );
      }

      if (filters.category) {
        filteredPosts = filteredPosts.filter(post =>
          post.category.slug === filters.category
        );
      }

      if (filters.featured !== undefined) {
        filteredPosts = filteredPosts.filter(post =>
          post.featured === filters.featured
        );
      }

      return {
        count: filteredPosts.length,
        next: null,
        previous: null,
        results: filteredPosts
      };
    }
  },

  // Récupérer un article par son slug
  getPostBySlug: async (slug: string): Promise<BlogPost> => {
    try {
      const response = await apiClient.get(`/blog/posts/${slug}/`);
      return response.data;
    } catch (error) {
      console.warn('API CMS non disponible, recherche dans les données de fallback:', error);

      const post = fallbackPosts.find(p => p.slug === slug);
      if (!post) {
        throw new Error('Article non trouvé');
      }
      return post;
    }
  },

  // Récupérer les articles mis en avant
  getFeaturedPosts: async (): Promise<BlogPost[]> => {
    try {
      const response = await apiClient.get('/blog/posts/?featured=true&status=published');
      return response.data.results;
    } catch (error) {
      console.warn('API CMS non disponible, utilisation des données de fallback:', error);
      return fallbackPosts.filter(post => post.is_featured);
    }
  },

  // Récupérer toutes les catégories
  getCategories: async (): Promise<BlogCategory[]> => {
    try {
      const response = await apiClient.get('/blog/categories/');
      return response.data.results || response.data;
    } catch (error) {
      console.warn('API CMS non disponible, utilisation des catégories de fallback:', error);
      return fallbackCategories;
    }
  },

  // Récupérer tous les tags
  getTags: async (): Promise<BlogTag[]> => {
    try {
      const response = await apiClient.get('/blog/tags/');
      return response.data.results || response.data;
    } catch (error) {
      console.warn('API CMS non disponible:', error);
      return [];
    }
  },

  // Rechercher des articles
  searchPosts: async (query: string): Promise<BlogPost[]> => {
    try {
      const response = await apiClient.get(`/blog/posts/?search=${encodeURIComponent(query)}&status=published`);
      return response.data.results;
    } catch (error) {
      console.warn('API CMS non disponible, recherche dans les données de fallback:', error);

      const searchLower = query.toLowerCase();
      return fallbackPosts.filter(post =>
        post.title.toLowerCase().includes(searchLower) ||
        post.excerpt.toLowerCase().includes(searchLower) ||
        post.content.toLowerCase().includes(searchLower) ||
        post.tags.some(tag => tag.name.toLowerCase().includes(searchLower))
      );
    }
  }
};

export default blogApi;
