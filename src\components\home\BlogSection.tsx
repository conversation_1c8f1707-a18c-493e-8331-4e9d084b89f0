import { Link } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Calendar, User, ArrowRight, Clock, AlertCircle } from "lucide-react";
import { useHomePageBlog } from "@/hooks/useHomePageCms";

const BlogSection = () => {
  // Récupérer les articles depuis le CMS admin
  const { 
    featuredPosts,
    recentPosts,
    totalPosts,
    isLoading,
    isError
  } = useHomePageBlog();

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Fonction pour obtenir le nom de l'auteur
  const getAuthorName = (author: any) => {
    if (!author) return 'Équipe JOSNET';
    if (author.first_name && author.last_name) {
      return `${author.first_name} ${author.last_name}`;
    }
    return author.first_name || author.username || 'Équipe JOSNET';
  };

  // Fonction pour obtenir l'URL de l'image
  const getImageUrl = (imagePath?: string) => {
    if (!imagePath) {
      return 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=500&auto=format&fit=crop&q=60';
    }
    
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    
    const baseUrl = import.meta.env.VITE_API_URL?.replace('/api/v1', '') || 'http://localhost:8000';
    return `${baseUrl}${imagePath}`;
  };

  // Fonction pour calculer le temps de lecture
  const getReadingTime = (post: any) => {
    if (post.reading_time) return `${post.reading_time} min`;
    
    const wordsPerMinute = 200;
    const wordCount = post.content?.split(' ').length || 0;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return `${readingTime} min`;
  };

  // Fonction pour obtenir le nom de la catégorie
  const getCategoryName = (post: any) => {
    if (post.categories && post.categories.length > 0) {
      return post.categories[0].name;
    }
    return 'Actualités';
  };

  if (isError) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">Section Blog Temporairement Indisponible</h2>
            <p className="text-gray-600">Les articles du blog seront bientôt disponibles depuis le CMS admin.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* En-tête de section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Actualités & Conseils
          </h2>
          <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
          <p className="text-gray-600 max-w-2xl mx-auto text-lg">
            Découvrez nos derniers articles sur les technologies, la cybersécurité et les solutions IT
          </p>
        </div>

        {/* Articles mis en avant */}
        {(featuredPosts && featuredPosts.length > 0) || isLoading ? (
          <div className="mb-16">
            <h3 className="text-2xl font-bold mb-8 text-center">
              Articles à la Une
              {totalPosts > 0 && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  ({totalPosts} articles au total)
                </span>
              )}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {isLoading ? (
                // Skeleton pour les articles mis en avant
                Array.from({ length: 3 }).map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <Skeleton className="h-48" />
                    <CardContent className="p-6 space-y-3">
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-3 w-24" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                featuredPosts.slice(0, 3).map(post => (
                  <Card key={post.id} className="overflow-hidden group hover:shadow-lg transition-all duration-300">
                    <Link to={`/blog/${post.slug}`} className="block">
                      <div className="h-48 overflow-hidden">
                        <img
                          src={getImageUrl(post.featured_image)}
                          alt={post.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                          <span className="flex items-center">
                            <Calendar size={12} className="mr-1" />
                            {formatDate(post.created_at)}
                          </span>
                          <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                            À la Une
                          </span>
                        </div>
                        <h4 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors line-clamp-2">
                          {post.title}
                        </h4>
                        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                          {post.excerpt}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span className="flex items-center">
                            <User size={12} className="mr-1" />
                            {getAuthorName(post.author)}
                          </span>
                          <span className="flex items-center">
                            <Clock size={12} className="mr-1" />
                            {getReadingTime(post)}
                          </span>
                        </div>
                      </CardContent>
                    </Link>
                  </Card>
                ))
              )}
            </div>
          </div>
        ) : null}

        {/* Derniers articles */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold mb-8 text-center">
            Derniers Articles
            <span className="block text-sm font-normal text-gray-500 mt-1">
              Directement depuis notre CMS admin
            </span>
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {isLoading ? (
              // Skeleton pour les derniers articles
              Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="h-48" />
                  <CardContent className="p-6 space-y-3">
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-3 w-24" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : recentPosts.length > 0 ? (
              recentPosts.slice(0, 6).map(post => (
                <Card key={post.id} className="overflow-hidden group hover:shadow-lg transition-all duration-300">
                  <Link to={`/blog/${post.slug}`} className="block">
                    <div className="h-48 overflow-hidden">
                      <img
                        src={getImageUrl(post.featured_image)}
                        alt={post.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                        <span className="flex items-center">
                          <Calendar size={12} className="mr-1" />
                          {formatDate(post.created_at)}
                        </span>
                        <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                          {getCategoryName(post)}
                        </span>
                      </div>
                      <h4 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors line-clamp-2">
                        {post.title}
                      </h4>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className="flex items-center">
                          <User size={12} className="mr-1" />
                          {getAuthorName(post.author)}
                        </span>
                        <span className="flex items-center">
                          <Clock size={12} className="mr-1" />
                          {getReadingTime(post)}
                        </span>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 text-lg mb-2">Aucun article disponible pour le moment.</p>
                <p className="text-gray-400 text-sm">
                  Les articles créés dans le CMS admin apparaîtront ici automatiquement.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Bouton pour voir tous les articles */}
        <div className="text-center">
          <Link to="/blog">
            <Button 
              size="lg" 
              className="bg-primary hover:bg-primary/90 text-white px-8 py-3"
            >
              Voir tous les articles
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;
