from django.urls import path, include
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework.routers import DefaultRouter
from .views import (
    RegisterView,
    LoginView,
    LogoutView,
    UserProfileView,
    PasswordChangeView,
    PasswordResetRequestView,
    PasswordResetConfirmView,
    EmailVerificationView,
    ResendVerificationView,
    UserAddressViewSet,
    LoyaltyTransactionViewSet,
    UserLoyaltyView,
    AdminUserViewSet,
    DashboardStatsView
)

# Create a router for ViewSets
router = DefaultRouter()
router.register(r'addresses', UserAddressViewSet, basename='user-address')
router.register(r'loyalty/transactions', LoyaltyTransactionViewSet, basename='loyalty-transaction')

# Create a router for admin ViewSets
admin_router = DefaultRouter()
admin_router.register(r'users', AdminUserViewSet, basename='admin-user')

urlpatterns = [
    # Authentication endpoints
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # User profile
    path('profile/', UserProfileView.as_view(), name='profile'),

    # Password management
    path('password/change/', PasswordChangeView.as_view(), name='password_change'),
    path('password/reset/request/', PasswordResetRequestView.as_view(), name='password_reset_request'),
    path('password/reset/confirm/', PasswordResetConfirmView.as_view(), name='password_reset_confirm'),

    # Email verification
    path('email/verify/', EmailVerificationView.as_view(), name='email_verify'),
    path('email/verify/resend/', ResendVerificationView.as_view(), name='email_verify_resend'),

    # User addresses and loyalty
    path('', include(router.urls)),
    path('loyalty/', UserLoyaltyView.as_view(), name='user-loyalty'),

    # Dashboard
    path('dashboard/', DashboardStatsView.as_view(), name='dashboard-stats'),

    # Admin endpoints
    path('admin/', include(admin_router.urls)),
]
