
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  MessageSquare,
  Users,
  ShoppingCart,
  Package,
  HelpCircle,
  ChevronRight
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { motion } from "framer-motion";
import coreApi, { ContactMessage } from "@/services/coreApi";
import cmsApi from "@/services/cmsApi";

const Contact = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [company, setCompany] = useState("");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [serviceRequested, setServiceRequested] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Récupérer les paramètres du site depuis l'API
  const { data: siteSettings, isLoading: isLoadingSettings } = useQuery({
    queryKey: ['siteSettings'],
    queryFn: () => cmsApi.getCurrentSiteSettings(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Services disponibles pour le formulaire
  const services = [
    { value: "reseaux", label: "Réseaux & Connectivité" },
    { value: "materiel", label: "Matériel Informatique" },
    { value: "mobile", label: "Solutions Mobiles" },
    { value: "infrastructure", label: "Infrastructure IT" },
    { value: "maintenance", label: "Maintenance" },
    { value: "formation", label: "Formation" },
    { value: "autre", label: "Autre" }
  ];

  // Handle form submission avec API réelle
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validation côté client
      const validationErrors: string[] = [];

      if (!name.trim()) {
        validationErrors.push("Le nom est obligatoire");
      } else if (name.trim().length < 2) {
        validationErrors.push("Le nom doit contenir au moins 2 caractères");
      }

      if (!email.trim()) {
        validationErrors.push("L'email est obligatoire");
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        validationErrors.push("L'email n'est pas valide");
      }

      if (!subject.trim()) {
        validationErrors.push("Le sujet est obligatoire");
      } else if (subject.trim().length < 5) {
        validationErrors.push("Le sujet doit contenir au moins 5 caractères");
      }

      if (!message.trim()) {
        validationErrors.push("Le message est obligatoire");
      } else if (message.trim().length < 10) {
        validationErrors.push("Le message doit contenir au moins 10 caractères");
      }

      if (phone && phone.trim() && !/^[\+]?[0-9\s\-\(\)]{8,}$/.test(phone.trim())) {
        validationErrors.push("Le numéro de téléphone n'est pas valide");
      }

      // Si erreurs de validation, les afficher
      if (validationErrors.length > 0) {
        toast({
          title: "Erreurs de validation",
          description: (
            <div className="space-y-1">
              {validationErrors.map((error: string, index: number) => (
                <div key={index} className="text-sm">• {error}</div>
              ))}
            </div>
          ),
          variant: "destructive",
          duration: 6000,
        });
        setIsSubmitting(false);
        return;
      }

      const contactData: ContactMessage = {
        name: name.trim(),
        email: email.trim(),
        phone: phone?.trim() || undefined,
        company: company?.trim() || undefined,
        subject: subject.trim(),
        message: message.trim(),
        service_requested: serviceRequested || undefined
      };

      // Envoyer le message via l'API
      const response = await coreApi.sendContactMessage(contactData);

      toast({
        title: "Message envoyé avec succès !",
        description: response.message || "Nous vous répondrons dans les plus brefs délais.",
        duration: 5000,
      });

      // Reset form
      setName("");
      setEmail("");
      setPhone("");
      setCompany("");
      setSubject("");
      setMessage("");
      setServiceRequested("");

    } catch (error: any) {
      console.error("Erreur lors de l'envoi du message:", error);

      // Afficher les erreurs de validation de manière plus claire
      const errorMessage = error.message || "Une erreur est survenue. Veuillez réessayer.";

      toast({
        title: "Erreur lors de l'envoi",
        description: (
          <div className="space-y-1">
            {errorMessage.split('\n').map((line: string, index: number) => (
              <div key={index} className="text-sm">{line}</div>
            ))}
          </div>
        ),
        variant: "destructive",
        duration: 8000, // Plus long pour lire les erreurs multiples
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-16"
        >
          <div className="container mx-auto px-4 text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
            >
              Contactez-nous
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl max-w-3xl mx-auto leading-relaxed"
            >
              Notre équipe d'experts est là pour vous accompagner dans tous vos projets technologiques.
              N'hésitez pas à nous contacter pour toute question ou besoin d'assistance.
            </motion.p>

            {/* Indicateurs de contact rapide */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex flex-wrap justify-center gap-6 mt-8"
            >
              <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <Phone className="h-4 w-4" />
                <span className="text-sm">
                  {isLoadingSettings ? "..." : siteSettings?.contact_phone || "+257 XX XX XX XX"}
                </span>
              </div>
              <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <Mail className="h-4 w-4" />
                <span className="text-sm">
                  {isLoadingSettings ? "..." : siteSettings?.contact_email || "<EMAIL>"}
                </span>
              </div>
              <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <Clock className="h-4 w-4" />
                <span className="text-sm">Réponse sous 24h</span>
              </div>
            </motion.div>
          </div>
        </motion.section>
        
        {/* Contact info and form */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Contact information */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="lg:col-span-1"
              >
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
                  <h2 className="text-xl font-semibold mb-6 flex items-center">
                    <MapPin className="h-5 w-5 text-blue-600 mr-2" />
                    Nos Coordonnées
                  </h2>

                  <div className="space-y-6">
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="flex items-start space-x-4 p-4 bg-blue-50 rounded-xl"
                    >
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <MapPin className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">Adresse</h3>
                        <p className="text-gray-600 leading-relaxed">
                          {isLoadingSettings ? "Chargement..." : siteSettings?.contact_address || "123 Avenue Principale, Bujumbura, Burundi"}
                        </p>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex items-start space-x-4 p-4 bg-green-50 rounded-xl"
                    >
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Phone className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">Téléphone</h3>
                        <p className="text-gray-600">
                          {isLoadingSettings ? "Chargement..." : siteSettings?.contact_phone || "+257 XX XX XX XX"}
                        </p>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                      className="flex items-start space-x-4 p-4 bg-purple-50 rounded-xl"
                    >
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Mail className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">Email</h3>
                        <p className="text-gray-600">
                          {isLoadingSettings ? "Chargement..." : siteSettings?.contact_email || "<EMAIL>"}
                        </p>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="flex items-start space-x-4 p-4 bg-orange-50 rounded-xl"
                    >
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Clock className="h-5 w-5 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">Heures d'ouverture</h3>
                        <div className="text-gray-600 space-y-1">
                          <p>Lundi - Vendredi: 8h00 - 18h00</p>
                          <p>Samedi: 9h00 - 13h00</p>
                          <p>Dimanche: Fermé</p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Quick links */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6"
                >
                  <h2 className="text-xl font-semibold mb-6 flex items-center">
                    <HelpCircle className="h-5 w-5 text-blue-600 mr-2" />
                    Liens Rapides
                  </h2>

                  <ul className="space-y-3">
                    <li>
                      <a href="/faq" className="flex items-center justify-between text-gray-700 hover:text-blue-600 p-3 rounded-xl hover:bg-blue-50 transition-all duration-200 group">
                        <span className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors">
                            <HelpCircle className="h-4 w-4 text-blue-600" />
                          </div>
                          <span className="font-medium">FAQ</span>
                        </span>
                        <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </li>
                    <li>
                      <a href="/order-tracking" className="flex items-center justify-between text-gray-700 hover:text-green-600 p-3 rounded-xl hover:bg-green-50 transition-all duration-200 group">
                        <span className="flex items-center">
                          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors">
                            <Package className="h-4 w-4 text-green-600" />
                          </div>
                          <span className="font-medium">Suivi de commande</span>
                        </span>
                        <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </li>
                    <li>
                      <a href="/products" className="flex items-center justify-between text-gray-700 hover:text-purple-600 p-3 rounded-xl hover:bg-purple-50 transition-all duration-200 group">
                        <span className="flex items-center">
                          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200 transition-colors">
                            <ShoppingCart className="h-4 w-4 text-purple-600" />
                          </div>
                          <span className="font-medium">Produits</span>
                        </span>
                        <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </li>
                    <li>
                      <a href="/about" className="flex items-center justify-between text-gray-700 hover:text-orange-600 p-3 rounded-xl hover:bg-orange-50 transition-all duration-200 group">
                        <span className="flex items-center">
                          <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-orange-200 transition-colors">
                            <Users className="h-4 w-4 text-orange-600" />
                          </div>
                          <span className="font-medium">À propos de nous</span>
                        </span>
                        <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </li>
                  </ul>
                </motion.div>
              </motion.div>
              
              {/* Contact form */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="lg:col-span-2"
              >
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 h-full">
                  <div className="flex items-center mb-8">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <MessageSquare className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-semibold text-gray-900">Envoyez-nous un message</h2>
                      <p className="text-gray-600">Nous vous répondrons dans les plus brefs délais</p>
                    </div>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                          Nom complet *
                        </label>
                        <Input
                          id="name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Jean Dupont"
                          className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                          required
                        />
                      </div>
                      <div>
                        <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                          Email *
                        </label>
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="<EMAIL>"
                          className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-2">
                          Téléphone
                        </label>
                        <Input
                          id="phone"
                          type="tel"
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          placeholder="+257 XX XX XX XX"
                          className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                        />
                      </div>
                      <div>
                        <label htmlFor="company" className="block text-sm font-semibold text-gray-700 mb-2">
                          Entreprise
                        </label>
                        <Input
                          id="company"
                          value={company}
                          onChange={(e) => setCompany(e.target.value)}
                          placeholder="Nom de votre entreprise"
                          className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="service" className="block text-sm font-semibold text-gray-700 mb-2">
                        Service demandé
                      </label>
                      <select
                        id="service"
                        value={serviceRequested}
                        onChange={(e) => setServiceRequested(e.target.value)}
                        className="w-full h-12 border border-gray-200 rounded-xl px-4 focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                      >
                        <option value="">Sélectionnez un service</option>
                        {services.map((service) => (
                          <option key={service.value} value={service.value}>
                            {service.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="subject" className="block text-sm font-semibold text-gray-700 mb-2">
                        Sujet * <span className="text-xs text-gray-500">(minimum 5 caractères)</span>
                      </label>
                      <Input
                        id="subject"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        placeholder="Ex: Demande de devis pour infrastructure réseau"
                        className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                        required
                        minLength={5}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {subject.length}/5 caractères minimum
                      </p>
                    </div>

                    <div>
                      <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-2">
                        Message * <span className="text-xs text-gray-500">(minimum 10 caractères)</span>
                      </label>
                      <textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder="Décrivez votre projet ou votre besoin en détail. Plus vous nous donnez d'informations, mieux nous pourrons vous aider..."
                        className="w-full min-h-[150px] border border-gray-200 rounded-xl px-4 py-3 focus:border-blue-500 focus:ring-blue-500 focus:outline-none resize-none"
                        required
                        minLength={10}
                      ></textarea>
                      <p className="text-xs text-gray-500 mt-1">
                        {message.length}/10 caractères minimum
                      </p>
                    </div>

                    <div className="flex items-start space-x-3 p-4 bg-gray-50 rounded-xl">
                      <input
                        type="checkbox"
                        id="privacy"
                        className="h-4 w-4 mt-1 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        required
                      />
                      <label htmlFor="privacy" className="text-sm text-gray-700 leading-relaxed">
                        J'accepte la <a href="/privacy" className="text-blue-600 hover:text-blue-700 underline font-medium">politique de confidentialité</a> et
                        j'autorise JOSNET NETWORK à traiter mes données personnelles pour répondre à ma demande.
                      </label>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 pt-4">
                      <Button
                        type="submit"
                        className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                            Envoi en cours...
                          </>
                        ) : (
                          <>
                            <MessageSquare className="h-5 w-5 mr-2" />
                            Envoyer le message
                          </>
                        )}
                      </Button>

                      <Button
                        type="button"
                        variant="outline"
                        className="h-12 px-6 border-gray-200 hover:bg-gray-50 rounded-xl"
                        onClick={() => {
                          setName("");
                          setEmail("");
                          setPhone("");
                          setCompany("");
                          setSubject("");
                          setMessage("");
                          setServiceRequested("");
                        }}
                      >
                        Réinitialiser
                      </Button>
                    </div>
                  </form>
                </div>
              </motion.div>
            </div>
          </div>
        </section>
        
        {/* Map section */}
        <section className="bg-gray-50 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold mb-8 text-center">Où nous trouver</h2>
            
            {/* Placeholder for map - in a real app, this would be an interactive map */}
            <div className="aspect-video max-w-4xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <p className="text-gray-600">Carte interactive - Emplacement de JOSNET NETWORK</p>
                {/* In a real app, you'd embed a map here */}
              </div>
            </div>
          </div>
        </section>
        
        {/* FAQ section */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl font-bold mb-2">Des questions fréquentes ?</h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Consultez notre section FAQ pour trouver des réponses rapides à vos questions.
            </p>
            <Button asChild>
              <a href="/faq">Voir la FAQ</a>
            </Button>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Contact;
