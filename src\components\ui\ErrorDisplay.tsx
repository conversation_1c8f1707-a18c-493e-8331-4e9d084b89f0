/**
 * Composant pour afficher les erreurs de manière élégante
 */

import React from 'react';
import { AlertTriangle, RefreshCw, Server } from 'lucide-react';
import { Button } from './button';

interface ErrorDisplayProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  showServerHint?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title = "Erreur de chargement",
  message,
  onRetry,
  showServerHint = true
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="text-center max-w-md">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {title}
        </h3>
        
        <p className="text-gray-600 mb-6">
          {message}
        </p>

        {showServerHint && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <Server className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <p className="text-sm font-medium text-blue-900 mb-1">
                  Serveur backend requis
                </p>
                <p className="text-xs text-blue-700">
                  Assurez-vous que le serveur Django est démarré sur le port 8000
                </p>
              </div>
            </div>
          </div>
        )}

        {onRetry && (
          <Button
            onClick={onRetry}
            className="bg-primary hover:bg-primary/90 text-white"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Réessayer
          </Button>
        )}
      </div>
    </div>
  );
};

export default ErrorDisplay;
