import { getAuthToken } from '@/utils/auth';
import axios from 'axios';
import { API_URL } from '../config';

// Types
export interface UserProfile {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone_number: string | null;
  date_of_birth: string | null;
  profile_picture: string | null;
  loyalty_points: number;
  loyalty_tier: string;
  is_verified: boolean;
  addresses: UserAddress[];
}

export interface UserAddress {
  id: number;
  address_type: 'shipping' | 'billing' | 'both';
  is_default: boolean;
  first_name: string;
  last_name: string;
  company: string | null;
  address_line1: string;
  address_line2: string | null;
  city: string;
  state: string | null;
  postal_code: string;
  country: string;
  phone: string | null;
  full_address: string;
  created_at?: string;
  updated_at?: string;
}

export interface AddressCreateUpdate {
  address_type: 'shipping' | 'billing' | 'both';
  is_default: boolean;
  first_name: string;
  last_name: string;
  company?: string | null;
  address_line1: string;
  address_line2?: string | null;
  city: string;
  state?: string | null;
  postal_code: string;
  country: string;
  phone?: string | null;
}

export interface LoyaltyTransaction {
  id: number;
  points: number;
  transaction_type: 'earn' | 'redeem' | 'expire' | 'adjust';
  description: string;
  order_number: string | null;
  created_at: string;
  expires_at: string | null;
  is_expired: boolean;
}

export interface LoyaltyInfo {
  id: number;
  email: string;
  loyalty_points: number;
  loyalty_tier: string;
}

export interface DashboardStats {
  orders_count: number;
  pending_orders_count: number;
  returns_count: number;
  loyalty_points: number;
  recent_order?: {
    id: string;
    order_number: string;
    date: string;
    status: string;
    status_display: string;
    total: string;
  };
}

// User API service
const userApi = {
  // Get user profile
  getProfile: async (): Promise<UserProfile> => {
    const response = await axios.get(`${API_URL}/auth/profile/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Update user profile
  updateProfile: async (profileData: Partial<UserProfile>): Promise<UserProfile> => {
    const response = await axios.put(`${API_URL}/auth/profile/`, profileData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Get user addresses
  getAddresses: async (): Promise<UserAddress[]> => {
    const response = await axios.get(`${API_URL}/auth/addresses/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Get a single address
  getAddress: async (id: number): Promise<UserAddress> => {
    const response = await axios.get(`${API_URL}/auth/addresses/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Create a new address
  createAddress: async (addressData: AddressCreateUpdate): Promise<UserAddress> => {
    const response = await axios.post(`${API_URL}/auth/addresses/`, addressData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Update an address
  updateAddress: async (id: number, addressData: Partial<AddressCreateUpdate>): Promise<UserAddress> => {
    const response = await axios.put(`${API_URL}/auth/addresses/${id}/`, addressData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Delete an address
  deleteAddress: async (id: number): Promise<void> => {
    await axios.delete(`${API_URL}/auth/addresses/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Set an address as default
  setDefaultAddress: async (id: number): Promise<void> => {
    await axios.post(`${API_URL}/auth/addresses/${id}/set_default/`, {}, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Get loyalty information
  getLoyaltyInfo: async (): Promise<LoyaltyInfo> => {
    const response = await axios.get(`${API_URL}/auth/loyalty/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Get loyalty transactions
  getLoyaltyTransactions: async (): Promise<LoyaltyTransaction[]> => {
    const response = await axios.get(`${API_URL}/auth/loyalty/transactions/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Get dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    // Pour le développement, utiliser des données simulées
    if (process.env.NODE_ENV === 'development') {
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 800));

      // Retourner des données simulées
      return {
        orders_count: 5,
        pending_orders_count: 1,
        returns_count: 2,
        loyalty_points: 450,
        recent_order: {
          id: "123",
          order_number: "CMD-12345",
          date: "15/05/2025",
          status: "delivered",
          status_display: "Livrée",
          total: "249,99 €"
        }
      };
    }

    // En production, appeler l'API réelle
    try {
      const response = await axios.get(`${API_URL}/users/dashboard/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }
};

export default userApi;
