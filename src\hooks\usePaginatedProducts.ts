import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { productApi, ProductFilterParams } from '@/services/productApi';

interface UsePaginatedProductsOptions {
  initialPage?: number;
  initialPageSize?: number;
  initialFilters?: Omit<ProductFilterParams, 'page' | 'page_size'>;
  keepPreviousData?: boolean;
}

/**
 * Custom hook for paginated product data with filtering
 */
const usePaginatedProducts = (options: UsePaginatedProductsOptions = {}) => {
  const {
    initialPage = 1,
    initialPageSize = 20,
    initialFilters = {},
    keepPreviousData = true,
  } = options;

  const [page, setPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [filters, setFilters] = useState<Omit<ProductFilterParams, 'page' | 'page_size'>>(initialFilters);

  // Combine pagination with filters
  const queryParams: ProductFilterParams = {
    ...filters,
    page,
    page_size: pageSize,
  };

  // Fetch products with current pagination and filters
  const {
    data,
    isLoading,
    isError,
    error,
    isFetching,
    isPreviousData,
    refetch,
  } = useQuery({
    queryKey: ['products', queryParams],
    queryFn: () => productApi.getProducts(queryParams),
    keepPreviousData,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Calculate pagination details
  const totalPages = data ? Math.ceil(data.count / pageSize) : 0;
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  // Update page
  const goToPage = useCallback((newPage: number) => {
    setPage(Math.max(1, Math.min(newPage, totalPages || 1)));
  }, [totalPages]);

  // Go to next page
  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setPage(old => old + 1);
    }
  }, [hasNextPage]);

  // Go to previous page
  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      setPage(old => old - 1);
    }
  }, [hasPreviousPage]);

  // Update page size
  const setItemsPerPage = useCallback((size: number) => {
    setPageSize(size);
    setPage(1); // Reset to first page when changing page size
  }, []);

  // Update filters
  const updateFilters = useCallback((newFilters: Omit<ProductFilterParams, 'page' | 'page_size'>) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when changing filters
  }, []);

  // Reset all filters and pagination
  const resetFilters = useCallback(() => {
    setFilters({});
    setPage(initialPage);
    setPageSize(initialPageSize);
  }, [initialPage, initialPageSize]);

  // Generate page numbers for pagination UI
  const getPageNumbers = useCallback((maxVisible: number = 5) => {
    if (!totalPages || totalPages <= maxVisible) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Always show first, last, and pages around current
    const halfVisible = Math.floor((maxVisible - 2) / 2);
    let startPage = Math.max(2, page - halfVisible);
    let endPage = Math.min(totalPages - 1, page + halfVisible);

    // Adjust if we're near the beginning
    if (startPage <= 2) {
      endPage = Math.min(totalPages - 1, maxVisible - 1);
      startPage = 2;
    }

    // Adjust if we're near the end
    if (endPage >= totalPages - 1) {
      startPage = Math.max(2, totalPages - maxVisible + 2);
      endPage = totalPages - 1;
    }

    // Build the array of page numbers
    const pages = [1];
    
    // Add ellipsis after first page if needed
    if (startPage > 2) {
      pages.push(-1); // -1 represents an ellipsis
    }
    
    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      pages.push(-1); // -1 represents an ellipsis
    }
    
    // Add last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  }, [page, totalPages]);

  return {
    // Data
    products: data?.results || [],
    totalCount: data?.count || 0,
    
    // Pagination state
    page,
    pageSize,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    
    // Filter state
    filters,
    
    // Loading states
    isLoading,
    isError,
    error,
    isFetching,
    isPreviousData,
    
    // Actions
    goToPage,
    nextPage,
    previousPage,
    setItemsPerPage,
    updateFilters,
    resetFilters,
    refetch,
    
    // Helpers
    getPageNumbers,
  };
};

export default usePaginatedProducts;
