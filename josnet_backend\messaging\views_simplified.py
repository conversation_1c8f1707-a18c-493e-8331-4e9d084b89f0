"""
Simplified views for the messaging app.
These views are designed to be more robust and handle errors gracefully.
"""
import logging
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count
from django.shortcuts import get_object_or_404

from .models import Conversation, Message, Category, Tag, ResponseTemplate

# Configure logger
logger = logging.getLogger(__name__)
from .serializers import (
    ConversationListSerializer,
    ConversationDetailSerializer,
    ConversationCreateSerializer,
    MessageSerializer,
    CategorySerializer,
    TagSerializer,
    ResponseTemplateSerializer
)


class IsAdminOrOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object or admins to access it.
    """
    def has_object_permission(self, request, view, obj):
        # Admin permissions
        if request.user.is_staff or request.user.is_superuser:
            return True

        # Check if the object has a customer attribute (Conversation)
        if hasattr(obj, 'customer'):
            return obj.customer == request.user

        # Check if the object has a conversation attribute (Message)
        if hasattr(obj, 'conversation'):
            return obj.conversation.customer == request.user

        # Default deny
        return False


class ConversationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for conversations.
    """
    permission_classes = [permissions.IsAuthenticated, IsAdminOrOwner]

    def get_queryset(self):
        """
        Get the list of conversations for the current user.
        For customers: only their own conversations
        For staff/admin: all conversations with additional filtering options
        """
        try:
            user = self.request.user
            logger.info(f"Getting conversations for user {user.id}")

            # Utiliser select_related pour optimiser les requêtes
            queryset = Conversation.objects.select_related(
                'customer', 'category', 'assigned_to'
            ).prefetch_related('messages', 'tags')

            # Filter based on user role
            if user.is_staff or user.is_superuser:
                logger.info(f"User {user.id} is staff/admin, returning all conversations")
                return queryset
            else:
                logger.info(f"User {user.id} is customer, returning their conversations")
                return queryset.filter(customer=user)
        except Exception as e:
            logger.error(f"Error in get_queryset: {e}", exc_info=True)
            # Return an empty queryset instead of crashing
            return Conversation.objects.none()

    def get_serializer_class(self):
        """
        Return appropriate serializer class based on action.
        """
        if self.action == 'create':
            return ConversationCreateSerializer
        elif self.action in ['retrieve', 'update', 'partial_update']:
            return ConversationDetailSerializer
        return ConversationListSerializer

    def retrieve(self, request, *args, **kwargs):
        """
        Mark conversation as read when retrieved.
        """
        try:
            instance = self.get_object()
            logger.info(f"Retrieving conversation {instance.id} for user {request.user.id}")

            # Mark as read based on user role
            if request.user.is_staff or request.user.is_superuser:
                instance.mark_as_read_by_admin()
                logger.info(f"Marked conversation {instance.id} as read by admin")
            else:
                instance.mark_as_read_by_customer()
                logger.info(f"Marked conversation {instance.id} as read by customer")

            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving conversation: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def list(self, request, *args, **kwargs):
        """
        List conversations with error handling.
        """
        try:
            return super().list(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Error listing conversations: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """
        Update the status of a conversation.
        """
        try:
            conversation = self.get_object()
            status_value = request.data.get('status')
            logger.info(f"Updating status of conversation {conversation.id} to {status_value}")

            if status_value not in [s[0] for s in Conversation.STATUSES]:
                logger.warning(f"Invalid status value: {status_value}")
                return Response(
                    {'error': 'Invalid status value'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            conversation.update_status(status_value)
            logger.info(f"Status updated successfully")

            serializer = ConversationDetailSerializer(conversation, context={'request': request})
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error updating conversation status: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """
        Get the count of unread conversations.
        """
        try:
            user = request.user
            logger.info(f"Getting unread count for user {user.id}")

            if user.is_staff or user.is_superuser:
                count = Conversation.objects.filter(is_read_by_admin=False).count()
                logger.info(f"Admin unread count: {count}")
            else:
                count = Conversation.objects.filter(
                    customer=user,
                    is_read_by_customer=False
                ).count()
                logger.info(f"Customer unread count: {count}")

            return Response({'unread_count': count})
        except Exception as e:
            logger.error(f"Error getting unread count: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def update_priority(self, request, pk=None):
        """
        Update the priority of a conversation.
        """
        try:
            conversation = self.get_object()
            priority_value = request.data.get('priority')
            logger.info(f"Updating priority of conversation {conversation.id} to {priority_value}")

            if priority_value not in [p[0] for p in Conversation.PRIORITIES]:
                logger.warning(f"Invalid priority value: {priority_value}")
                return Response(
                    {'error': 'Invalid priority value'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            conversation.priority = priority_value
            conversation.save(update_fields=['priority'])
            logger.info(f"Priority updated successfully")

            serializer = ConversationDetailSerializer(conversation, context={'request': request})
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error updating conversation priority: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """
        Assign a conversation to a user.
        """
        try:
            conversation = self.get_object()
            user_id = request.data.get('user_id')
            logger.info(f"Assigning conversation {conversation.id} to user {user_id}")

            if user_id:
                try:
                    from django.contrib.auth import get_user_model
                    User = get_user_model()
                    user = User.objects.get(id=user_id)
                    conversation.assigned_to = user
                except Exception as e:
                    logger.error(f"Error finding user {user_id}: {e}")
                    return Response(
                        {"error": f"User not found: {str(e)}"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                conversation.assigned_to = None

            conversation.save(update_fields=['assigned_to'])
            logger.info(f"Assignment updated successfully")

            serializer = ConversationDetailSerializer(conversation, context={'request': request})
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error assigning conversation: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def categorize(self, request, pk=None):
        """
        Categorize a conversation.
        """
        try:
            conversation = self.get_object()
            category_id = request.data.get('category_id')
            tag_ids = request.data.get('tag_ids', [])
            logger.info(f"Categorizing conversation {conversation.id} with category {category_id} and tags {tag_ids}")

            # Update category
            if category_id:
                try:
                    category = Category.objects.get(id=category_id)
                    conversation.category = category
                except Exception as e:
                    logger.error(f"Error finding category {category_id}: {e}")
                    return Response(
                        {"error": f"Category not found: {str(e)}"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                conversation.category = None

            conversation.save(update_fields=['category'])

            # Update tags
            conversation.tags.clear()
            if tag_ids:
                try:
                    tags = Tag.objects.filter(id__in=tag_ids)
                    conversation.tags.add(*tags)
                except Exception as e:
                    logger.error(f"Error finding tags {tag_ids}: {e}")
                    return Response(
                        {"error": f"Error with tags: {str(e)}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            logger.info(f"Categorization updated successfully")

            serializer = ConversationDetailSerializer(conversation, context={'request': request})
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error categorizing conversation: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        Get conversation statistics.
        """
        try:
            logger.info(f"Getting conversation statistics")

            # Get query parameters
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            # Base queryset
            queryset = Conversation.objects.all()

            # Apply date filters if provided
            if start_date:
                queryset = queryset.filter(created_at__gte=start_date)
            if end_date:
                queryset = queryset.filter(created_at__lte=end_date)

            # Calculate statistics
            total_conversations = queryset.count()

            # Group by status
            status_counts = {}
            for status_choice, _ in Conversation.STATUSES:
                status_counts[status_choice] = queryset.filter(status=status_choice).count()

            # Group by priority
            priority_counts = {}
            for priority_choice, _ in Conversation.PRIORITIES:
                priority_counts[priority_choice] = queryset.filter(priority=priority_choice).count()

            # Group by category
            category_counts = {}
            categories = Category.objects.all()
            for category in categories:
                category_counts[category.id] = queryset.filter(category=category).count()

            # Average response time calculation would require more complex logic
            # For now, we'll return null
            avg_response_time = None

            statistics = {
                'total_conversations': total_conversations,
                'by_status': status_counts,
                'by_priority': priority_counts,
                'by_category': category_counts,
                'avg_response_time_hours': avg_response_time
            }

            logger.info(f"Statistics calculated successfully")
            return Response(statistics)
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}", exc_info=True)
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for conversation categories.
    """
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Only admins can create, update or delete categories.
        """
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get only active categories.
        """
        categories = Category.objects.filter(is_active=True)
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data)


class TagViewSet(viewsets.ModelViewSet):
    """
    API endpoint for conversation tags.
    """
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Only admins can create, update or delete tags.
        """
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get only active tags.
        """
        tags = Tag.objects.filter(is_active=True)
        serializer = self.get_serializer(tags, many=True)
        return Response(serializer.data)


class ResponseTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint for response templates.
    """
    queryset = ResponseTemplate.objects.all()
    serializer_class = ResponseTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)


class MessageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for messages.
    """
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminOrOwner]

    def get_queryset(self):
        """
        Get the list of messages for a specific conversation.
        """
        conversation_id = self.kwargs.get('conversation_pk')
        return Message.objects.filter(conversation_id=conversation_id)

    def create(self, request, *args, **kwargs):
        """
        Create a new message in a conversation.
        """
        try:
            conversation_id = self.kwargs.get('conversation_pk')
            logger.info(f"Creating message for conversation {conversation_id}")

            try:
                conversation = get_object_or_404(Conversation, id=conversation_id)
            except Exception as e:
                logger.error(f"Error finding conversation {conversation_id}: {e}")
                return Response(
                    {"error": f"Conversation not found: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check permissions
            if not IsAdminOrOwner().has_object_permission(request, self, conversation):
                logger.warning(f"Permission denied for user {request.user.id} on conversation {conversation_id}")
                return Response(
                    {"error": "You don't have permission to add messages to this conversation"},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Add conversation to data
            data = request.data.copy()
            data['conversation'] = conversation_id

            # Log request data for debugging (excluding file content)
            safe_data = {k: v for k, v in data.items() if k != 'uploaded_files'}
            logger.info(f"Message data: {safe_data}")

            serializer = self.get_serializer(data=data)

            if not serializer.is_valid():
                logger.error(f"Serializer validation errors: {serializer.errors}")
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

            self.perform_create(serializer)
            logger.info(f"Message created successfully with ID {serializer.data.get('id')}")

            headers = self.get_success_headers(serializer.data)
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED,
                headers=headers
            )
        except Exception as e:
            logger.error(f"Unexpected error creating message: {e}", exc_info=True)
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
