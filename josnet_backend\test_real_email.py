#!/usr/bin/env python
"""
Test avec une vraie adresse email : <EMAIL>
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category, Promotion

User = get_user_model()

# Adresse email réelle à tester
REAL_EMAIL = "<EMAIL>"


def setup_real_email_test():
    """Configurer le test avec l'adresse email réelle."""
    print("🔧 Configuration du test avec adresse email réelle")
    print("=" * 60)
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Admin',
            'last_name': 'RealTest',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
        print(f"   ✅ Admin créé: {admin_user.email}")
    else:
        print(f"   ✅ Admin existant: {admin_user.email}")
    
    # Créer un utilisateur avec l'adresse email réelle
    real_user, created = User.objects.get_or_create(
        email=REAL_EMAIL,
        defaults={
            'password': 'testpass123',
            'first_name': 'Alain',
            'last_name': 'Asifiwe'
        }
    )
    if created:
        real_user.set_password('testpass123')
        real_user.save()
        print(f"   ✅ Utilisateur réel créé: {real_user.email}")
    else:
        print(f"   ✅ Utilisateur réel existant: {real_user.email}")
    
    # Créer ou mettre à jour l'abonnement newsletter
    subscription, created = NewsletterSubscription.objects.get_or_create(
        email=REAL_EMAIL,
        defaults={
            'user': real_user,
            'preferences_promotions': True,
            'preferences_new_products': True,
            'preferences_newsletters': True,
            'preferences_order_updates': True
        }
    )
    
    if not created:
        # Mettre à jour les préférences si l'abonnement existe déjà
        subscription.user = real_user
        subscription.preferences_promotions = True
        subscription.preferences_new_products = True
        subscription.preferences_newsletters = True
        subscription.preferences_order_updates = True
        subscription.is_active = True
        subscription.save()
        print(f"   ✅ Abonnement mis à jour: {subscription.email}")
    else:
        print(f"   ✅ Abonnement créé: {subscription.email}")
    
    print(f"   📧 Email: {subscription.email}")
    print(f"   🎉 Promotions: {'✅' if subscription.preferences_promotions else '❌'}")
    print(f"   ✨ Nouveaux produits: {'✅' if subscription.preferences_new_products else '❌'}")
    print(f"   📰 Newsletters: {'✅' if subscription.preferences_newsletters else '❌'}")
    print(f"   📦 Commandes: {'✅' if subscription.preferences_order_updates else '❌'}")
    print(f"   🔄 Actif: {'✅' if subscription.is_active else '❌'}")
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Test Real Email',
        defaults={'slug': 'test-real-email'}
    )
    
    return admin_user, real_user, subscription, category


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def test_promotion_to_real_email():
    """Test d'envoi de promotion à l'adresse email réelle."""
    print("\n🎉 TEST: Envoi de promotion à l'adresse email réelle")
    print("=" * 60)
    
    admin_user, real_user, subscription, category = setup_real_email_test()
    
    # Nettoyer les anciennes promotions de test
    Promotion.objects.filter(title__contains='Real Email Test').delete()
    NewsletterCampaign.objects.filter(title__contains='Real Email Test').delete()
    
    print(f"\n🔄 Création d'une promotion spéciale...")
    
    # Créer une promotion spéciale
    promotion = Promotion.objects.create(
        title='🎉 Real Email Test - Promotion Spéciale 60% OFF!',
        description=f'Promotion spéciale de test pour {REAL_EMAIL}. Profitez de 60% de réduction sur tous nos produits tech !',
        promotion_type='percentage',
        discount_percentage=Decimal('60.00'),
        start_date=timezone.now(),
        end_date=timezone.now() + timedelta(days=14),
        status='draft',
        created_by=admin_user,
        send_email_notification=True
    )
    
    print(f"   ✅ Promotion créée: {promotion.title}")
    print(f"   💰 Réduction: {promotion.discount_percentage}%")
    print(f"   📅 Valide jusqu'au: {promotion.end_date.strftime('%d/%m/%Y')}")
    print(f"   📧 Notifications activées: {promotion.send_email_notification}")
    
    print(f"\n🚀 Activation de la promotion (envoi automatique à {REAL_EMAIL})...")
    
    # Activer la promotion pour déclencher l'envoi automatique
    promotion.status = 'active'
    promotion.save()
    
    print(f"   ✅ Promotion activée!")
    
    # Vérifier si l'email a été marqué comme envoyé
    promotion.refresh_from_db()
    print(f"   📧 Email marqué comme envoyé: {promotion.email_sent}")
    if promotion.email_sent:
        print(f"   📅 Email envoyé à: {promotion.email_sent_at}")
    
    # Vérifier les campagnes créées
    campaigns = NewsletterCampaign.objects.filter(
        campaign_type='promotion',
        title__contains='Real Email Test'
    )
    
    if campaigns.exists():
        campaign = campaigns.first()
        print(f"\n📧 CAMPAGNE EMAIL:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        print(f"   📅 Envoyé le: {campaign.sent_at}")
        
        print(f"\n🎯 L'email a été envoyé à {REAL_EMAIL} !")
        return True
    else:
        print(f"\n❌ Aucune campagne créée")
        return False


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def test_product_to_real_email():
    """Test d'envoi de nouveau produit à l'adresse email réelle."""
    print("\n✨ TEST: Envoi de nouveau produit à l'adresse email réelle")
    print("=" * 60)
    
    admin_user, real_user, subscription, category = setup_real_email_test()
    
    # Nettoyer les anciens produits de test
    Product.objects.filter(name__contains='Real Email Test').delete()
    NewsletterCampaign.objects.filter(title__contains='Real Email Test Product').delete()
    
    print(f"\n🔄 Création d'un nouveau produit spécial...")
    
    # Créer un nouveau produit spécial
    product = Product.objects.create(
        name='🚀 Real Email Test - iPhone 16 Pro Max Ultra',
        slug='real-email-test-iphone-16-pro-max-ultra',
        sku='RET001',
        description=f'Nouveau produit spécial de test pour {REAL_EMAIL}. Le dernier iPhone 16 Pro Max Ultra avec des fonctionnalités révolutionnaires !',
        short_description='iPhone 16 Pro Max Ultra - Le smartphone le plus avancé jamais créé',
        price=Decimal('1899.99'),
        sale_price=Decimal('1599.99'),
        status='draft',
        created_by=admin_user
    )
    product.categories.add(category)
    
    print(f"   ✅ Produit créé: {product.name}")
    print(f"   💰 Prix: {product.price}€ (Promo: {product.sale_price}€)")
    print(f"   📱 SKU: {product.sku}")
    
    print(f"\n🚀 Publication du produit (envoi automatique à {REAL_EMAIL})...")
    
    # Publier le produit pour déclencher l'envoi automatique
    product.status = 'published'
    product.save()
    
    print(f"   ✅ Produit publié!")
    
    # Vérifier les campagnes créées
    campaigns = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Real Email Test Product'
    )
    
    if campaigns.exists():
        campaign = campaigns.first()
        print(f"\n📧 CAMPAGNE EMAIL:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        print(f"   📅 Envoyé le: {campaign.sent_at}")
        
        print(f"\n🎯 L'email a été envoyé à {REAL_EMAIL} !")
        return True
    else:
        print(f"\n❌ Aucune campagne créée")
        return False


def main():
    """Fonction principale."""
    print("🚀 TEST AVEC ADRESSE EMAIL RÉELLE")
    print("=" * 80)
    print(f"📧 Adresse email de test: {REAL_EMAIL}")
    print("=" * 80)
    
    try:
        # Test 1: Promotion
        promotion_success = test_promotion_to_real_email()
        
        # Test 2: Nouveau produit
        product_success = test_product_to_real_email()
        
        # Résumé
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ DES TESTS AVEC ADRESSE RÉELLE")
        print("=" * 80)
        
        tests = [
            ("Email promotion automatique", promotion_success),
            ("Email nouveau produit automatique", product_success)
        ]
        
        passed = sum(1 for _, success in tests if success)
        failed = len(tests) - passed
        
        for test_name, success in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\nTotal: {len(tests)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print(f"\n🎉 TOUS LES TESTS SONT PASSÉS!")
            print(f"✅ Les emails automatiques ont été envoyés à {REAL_EMAIL}")
            print(f"✅ Le système fonctionne parfaitement avec de vraies adresses email")
            print(f"✅ Vérifiez la boîte email de {REAL_EMAIL} pour voir les emails reçus")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
