# This file contains the original serializers from authentication/serializers.py
# It is used for backward compatibility

from rest_framework import serializers
from rest_framework.validators import UniqueValidator
from django.contrib.auth import get_user_model
from ..models import UserAddress, LoyaltyTransaction

User = get_user_model()

class ProfilePictureSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('profile_picture',)
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _
from ..models import UserAddress, LoyaltyTransaction

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    """Serializer for user registration."""
    
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True, required=True)
    
    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name', 'password', 'confirm_password']
        extra_kwargs = {
            'first_name': {'required': True},
            'last_name': {'required': True}
        }
    
    def validate(self, attrs):
        if attrs['password'] != attrs.pop('confirm_password'):
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs
    
    def create(self, validated_data):
        user = User.objects.create(
            email=validated_data['email'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name']
        )
        
        user.set_password(validated_data['password'])
        user.save()
        
        return user

class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile."""
    
    full_name = serializers.CharField(read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'phone_number', 'date_of_birth', 'profile_picture',
            'is_active', 'is_verified', 'role',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'email', 'is_active', 'is_verified', 'role', 'created_at', 'updated_at']

class UserProfileExtendedSerializer(UserProfileSerializer):
    """Extended serializer for user profile with loyalty information."""
    
    loyalty_points = serializers.IntegerField(read_only=True)
    loyalty_tier = serializers.CharField(read_only=True)
    
    class Meta(UserProfileSerializer.Meta):
        fields = UserProfileSerializer.Meta.fields + ['loyalty_points', 'loyalty_tier']

class LoginSerializer(serializers.Serializer):
    """Serializer for user login."""
    
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True, write_only=True)

class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change."""
    
    old_password = serializers.CharField(required=True, write_only=True)
    new_password = serializers.CharField(required=True, write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(required=True, write_only=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError({"new_password": "Password fields didn't match."})
        return attrs

class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for password reset request."""
    
    email = serializers.EmailField(required=True)

class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation."""
    
    token = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(required=True, write_only=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError({"new_password": "Password fields didn't match."})
        return attrs

class EmailVerificationSerializer(serializers.Serializer):
    """Serializer for email verification."""
    
    token = serializers.CharField(required=True)

class UserAddressSerializer(serializers.ModelSerializer):
    """Serializer for user addresses."""
    
    class Meta:
        model = UserAddress
        fields = '__all__'
        read_only_fields = ['user', 'created_at', 'updated_at']

class UserAddressListSerializer(serializers.ModelSerializer):
    """Serializer for listing user addresses."""
    
    formatted_address = serializers.CharField(read_only=True)
    
    class Meta:
        model = UserAddress
        fields = [
            'id', 'address_type', 'is_default', 'first_name', 'last_name',
            'address_line1', 'city', 'postal_code', 'country', 'formatted_address'
        ]

class LoyaltyTransactionSerializer(serializers.ModelSerializer):
    """Serializer for loyalty transactions."""
    
    transaction_type_display = serializers.CharField(source='get_transaction_type_display', read_only=True)
    
    class Meta:
        model = LoyaltyTransaction
        fields = [
            'id', 'points', 'transaction_type', 'transaction_type_display',
            'description', 'order', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

class UserLoyaltySerializer(serializers.ModelSerializer):
    """Serializer for user loyalty information."""
    
    transactions = LoyaltyTransactionSerializer(many=True, read_only=True)
    
    class Meta:
        model = User
        fields = ['loyalty_points', 'loyalty_tier', 'transactions']
        read_only_fields = ['loyalty_points', 'loyalty_tier', 'transactions']
