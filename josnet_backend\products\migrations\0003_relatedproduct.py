# Generated by Django 4.2.10 on 2025-05-17 16:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0002_stockreservation_stockmovement'),
    ]

    operations = [
        migrations.CreateModel(
            name='RelatedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('relation_type', models.CharField(choices=[('related', 'Related'), ('upsell', 'Upsell'), ('cross_sell', 'Cross Sell'), ('accessory', 'Accessory')], max_length=20)),
                ('position', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='related_products', to='products.product')),
                ('related_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='related_to_products', to='products.product')),
            ],
            options={
                'ordering': ['relation_type', 'position'],
                'unique_together': {('product', 'related_product', 'relation_type')},
            },
        ),
    ]
