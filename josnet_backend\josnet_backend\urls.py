from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('rosetta/', include('rosetta.urls')),  # ✅ Interface de traduction Rosetta

    # API endpoints
    path('api/v1/core/', include('core.urls')),
    path('api/v1/products/', include('products.urls')),
    path('api/v1/marketing/', include('marketing.urls')),
    path('api/v1/analytics/', include('analytics.urls')),
    path('api/v1/orders/', include('orders.urls')),
    path('api/v1/payments/', include('payments.urls')),
    path('api/v1/messaging/', include('messaging.urls')),
    path('api/v1/cms/', include('cms.urls')),
    path('api/v1/notifications/', include('notifications.urls')),
    path('api/v1/translations/', include('translations.urls')),  # ✅ API de traduction

    # Authentication endpoints
    path('api/v1/auth/', include('authentication.urls')),
    path('api/v1/', include('authentication.urls')),  # Include account endpoints at root level
    path('api/v1/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/v1/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
