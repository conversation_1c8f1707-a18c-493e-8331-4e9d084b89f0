#!/usr/bin/env python3
"""
Script pour tester les corrections des erreurs d'avis
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def get_auth_token():
    """Obtenir un token d'authentification"""
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access')
    return None

def test_can_review_api():
    """Tester la nouvelle API can-review"""
    print("🔍 Test de l'API can-review...")
    
    token = get_auth_token()
    if not token:
        print("   ❌ Impossible de s'authentifier")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/can-review/", 
            params={'product': 1}, 
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API accessible")
            print(f"   🔍 Peut écrire un avis: {data.get('can_review')}")
            print(f"   📝 Raison: {data.get('reason')}")
            if 'existing_review_id' in data:
                print(f"   🆔 ID avis existant: {data.get('existing_review_id')}")
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_create_review_error_handling():
    """Tester la gestion d'erreur lors de la création d'avis"""
    print(f"\n✍️ Test de la gestion d'erreur pour la création d'avis...")
    
    token = get_auth_token()
    if not token:
        print("   ❌ Impossible de s'authentifier")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    review_data = {
        "product": 1,
        "rating": 5,
        "title": "Test d'avis en double",
        "comment": "Ceci est un test pour vérifier la gestion des avis en double.",
        "pros": "Test",
        "cons": "Test"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/products/reviews/", 
            json=review_data, 
            headers=headers
        )
        
        if response.status_code == 201:
            print(f"   ✅ Avis créé avec succès")
            review = response.json()
            print(f"   📝 Titre: {review.get('title')}")
            return True
        elif response.status_code == 400:
            error_data = response.json()
            print(f"   ✅ Erreur gérée correctement")
            print(f"   📝 Message: {error_data.get('detail', 'Erreur de validation')}")
            return True
        else:
            print(f"   ❌ Erreur inattendue: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_review_stats_api():
    """Tester l'API des statistiques d'avis"""
    print(f"\n📊 Test de l'API des statistiques d'avis...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/stats/", 
            params={'product': 1}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API accessible")
            print(f"   📈 Total avis: {data.get('total_reviews', 0)}")
            print(f"   ⭐ Note moyenne: {data.get('average_rating', 0)}")
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_review_list_api():
    """Tester l'API de liste des avis"""
    print(f"\n📋 Test de l'API de liste des avis...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/products/reviews/", 
            params={'product': 1}
        )
        
        if response.status_code == 200:
            data = response.json()
            reviews = data.get('results', [])
            print(f"   ✅ API accessible")
            print(f"   📦 Avis récupérés: {len(reviews)}")
            return True
        else:
            print(f"   ❌ Erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🧪 TEST DES CORRECTIONS D'ERREURS D'AVIS")
    print("=" * 60)
    
    # 1. Test de l'API can-review
    can_review_ok = test_can_review_api()
    
    # 2. Test de la gestion d'erreur pour la création
    error_handling_ok = test_create_review_error_handling()
    
    # 3. Test des APIs existantes
    stats_ok = test_review_stats_api()
    list_ok = test_review_list_api()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   API can-review: {'✅' if can_review_ok else '❌'}")
    print(f"   Gestion d'erreur: {'✅' if error_handling_ok else '❌'}")
    print(f"   API statistiques: {'✅' if stats_ok else '❌'}")
    print(f"   API liste avis: {'✅' if list_ok else '❌'}")
    
    total_tests = 4
    passed_tests = sum([can_review_ok, error_handling_ok, stats_ok, list_ok])
    
    print(f"\n🎯 SCORE: {passed_tests}/{total_tests} tests réussis")
    
    if passed_tests == total_tests:
        print(f"\n🎉 TOUTES LES CORRECTIONS RÉUSSIES!")
        print(f"   ✅ L'API can-review fonctionne")
        print(f"   ✅ Les erreurs de contrainte d'unicité sont gérées")
        print(f"   ✅ Les APIs d'avis fonctionnent correctement")
        
        print(f"\n🌐 FONCTIONNALITÉS CORRIGÉES:")
        print(f"   • Vérification avant création d'avis")
        print(f"   • Messages d'erreur clairs pour les avis en double")
        print(f"   • Gestion gracieuse des contraintes d'unicité")
        print(f"   • Interface utilisateur informative")
        
        print(f"\n🔧 TESTEZ MAINTENANT:")
        print(f"   1. Allez sur: http://localhost:8080/product/1")
        print(f"   2. Cliquez sur 'Écrire un avis'")
        print(f"   3. Si vous avez déjà un avis, vous verrez un message informatif")
        print(f"   4. Sinon, vous pourrez créer un nouvel avis")
        
    elif passed_tests >= 3:
        print(f"\n✅ CORRECTIONS LARGEMENT RÉUSSIES!")
        print(f"   La plupart des problèmes ont été corrigés.")
    else:
        print(f"\n⚠️ CERTAINS PROBLÈMES PERSISTENT")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
