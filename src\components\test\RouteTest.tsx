import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  ExternalLink,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

const RouteTest: React.FC = () => {
  const testRoutes = [
    {
      path: '/messaging/conversation/1',
      description: 'Route principale de conversation',
      status: 'primary'
    },
    {
      path: '/messaging/conversation/2',
      description: 'Route qui causait l\'erreur 404',
      status: 'fixed'
    },
    {
      path: '/conversation/1',
      description: 'Route alternative courte',
      status: 'alternative'
    },
    {
      path: '/messages/conversation/1',
      description: 'Route alternative avec messages',
      status: 'alternative'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'primary':
        return <Badge className="bg-blue-100 text-blue-800">Principale</Badge>;
      case 'fixed':
        return <Badge className="bg-green-100 text-green-800">Corrigée</Badge>;
      case 'alternative':
        return <Badge className="bg-gray-100 text-gray-800">Alternative</Badge>;
      default:
        return <Badge variant="outline">Test</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'primary':
      case 'fixed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'alternative':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <MessageSquare className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="h-5 w-5 mr-2" />
          Test des routes de conversation
        </CardTitle>
        <CardDescription>
          Testez les différentes routes de conversation pour vérifier qu'elles fonctionnent correctement
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {testRoutes.map((route, index) => (
            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                {getStatusIcon(route.status)}
                <div>
                  <div className="font-medium">{route.path}</div>
                  <div className="text-sm text-gray-500">{route.description}</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusBadge(route.status)}
                <Button asChild variant="outline" size="sm">
                  <Link to={route.path}>
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Tester
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h4 className="font-medium text-green-800 mb-2">✅ Problème résolu !</h4>
          <p className="text-sm text-green-700">
            L'erreur 404 pour <code>/messaging/conversation/2</code> a été corrigée. 
            Toutes les routes de conversation sont maintenant fonctionnelles.
          </p>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">ℹ️ Information</h4>
          <p className="text-sm text-blue-700">
            Ces routes sont protégées par authentification. Assurez-vous d'être connecté 
            avant de tester les liens.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default RouteTest;
