import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
import { CalendarIcon, Filter, X } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';

export interface ReturnFiltersState {
  orderId: string;
  status: string;
  dateFrom: Date | undefined;
  dateTo: Date | undefined;
}

interface ReturnFiltersProps {
  filters?: ReturnFiltersState;
  onFilterChange: (filters: ReturnFiltersState) => void;
  onReset: () => void;
}

export function ReturnFilters({ filters, onFilterChange, onReset }: ReturnFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<ReturnFiltersState>(filters || {
    orderId: '',
    status: 'all',
    dateFrom: undefined,
    dateTo: undefined,
  });
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLocalFilters((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleSelectChange = (name: string, value: string) => {
    setLocalFilters((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleDateChange = (name: 'dateFrom' | 'dateTo', date: Date | undefined) => {
    setLocalFilters((prev) => ({ ...prev, [name]: date }));
  };
  
  const handleApplyFilters = () => {
    onFilterChange(localFilters);
    setIsOpen(false);
  };
  
  const handleResetFilters = () => {
    const resetFilters = {
      orderId: '',
      status: 'all',
      dateFrom: undefined,
      dateTo: undefined,
    };
    setLocalFilters(resetFilters);
    onReset();
    setIsOpen(false);
  };
  
  const hasActiveFilters = 
    filters.orderId !== '' || 
    filters.status !== 'all' || 
    filters.dateFrom !== undefined || 
    filters.dateTo !== undefined;
  
  return (
    <div className="flex items-center gap-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={cn(
              "flex items-center gap-1",
              hasActiveFilters && "bg-primary/10"
            )}
          >
            <Filter className="h-4 w-4" />
            Filtres
            {hasActiveFilters && (
              <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                {Object.values(filters).filter(v => v !== '' && v !== undefined).length}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="start">
          <div className="space-y-4">
            <h4 className="font-medium">Filtres avancés</h4>
            
            <div className="space-y-2">
              <Label htmlFor="orderId">N° de commande</Label>
              <Input
                id="orderId"
                name="orderId"
                placeholder="ex: CMD-12345"
                value={localFilters.orderId}
                onChange={handleInputChange}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status">Statut</Label>
              <Select
                value={localFilters.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="approved">Approuvé</SelectItem>
                  <SelectItem value="pending">En cours</SelectItem>
                  <SelectItem value="rejected">Refusé</SelectItem>
                  <SelectItem value="cancelled">Annulé</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label htmlFor="dateFrom">Date de début</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="dateFrom"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !localFilters.dateFrom && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {localFilters.dateFrom ? (
                        format(localFilters.dateFrom, 'P', { locale: fr })
                      ) : (
                        <span>Sélectionner</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={localFilters.dateFrom}
                      onSelect={(date) => handleDateChange('dateFrom', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="dateTo">Date de fin</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="dateTo"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !localFilters.dateTo && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {localFilters.dateTo ? (
                        format(localFilters.dateTo, 'P', { locale: fr })
                      ) : (
                        <span>Sélectionner</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={localFilters.dateTo}
                      onSelect={(date) => handleDateChange('dateTo', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            <div className="flex justify-between pt-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleResetFilters}
                className="flex items-center gap-1"
              >
                <X className="h-4 w-4" />
                Réinitialiser
              </Button>
              <Button 
                size="sm" 
                onClick={handleApplyFilters}
              >
                Appliquer
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {hasActiveFilters && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleResetFilters}
          className="h-8 px-2 text-xs"
        >
          <X className="h-3 w-3 mr-1" />
          Effacer les filtres
        </Button>
      )}
    </div>
  );
}
