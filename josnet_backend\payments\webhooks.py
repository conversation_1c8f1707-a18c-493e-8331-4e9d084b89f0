import json
import hmac
import hashlib
import base64
import stripe
from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.utils import timezone

from .models import Transaction, PaymentMethod
from orders.models import Order

# Initialize Stripe with API key
# stripe.api_key = settings.STRIPE_SECRET_KEY

# For development, we'll use a placeholder API key
stripe.api_key = "sk_test_placeholder"

@csrf_exempt
@require_POST
def stripe_webhook(request):
    """
    Webhook handler for Stripe events.

    This endpoint receives webhook events from Stripe and processes them accordingly.
    """
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

    try:
        # Verify the webhook signature
        # In production, you would use a webhook secret from settings
        # event = stripe.Webhook.construct_event(payload, sig_header, settings.STRIPE_WEBHOOK_SECRET)

        # For development, we'll parse the payload directly
        event = json.loads(payload)

        # Handle the event based on its type
        if event['type'] == 'payment_intent.succeeded':
            handle_payment_intent_succeeded(event['data']['object'])
        elif event['type'] == 'payment_intent.payment_failed':
            handle_payment_intent_failed(event['data']['object'])
        elif event['type'] == 'charge.refunded':
            handle_charge_refunded(event['data']['object'])
        # Add more event types as needed

        return HttpResponse(status=200)

    except ValueError as e:
        # Invalid payload
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        return HttpResponse(status=400)
    except Exception as e:
        # Generic error handling
        return HttpResponse(content=str(e), status=500)

def handle_payment_intent_succeeded(payment_intent):
    """
    Handle a successful payment intent.

    Args:
        payment_intent: The payment intent object from Stripe
    """
    # Find the transaction by payment intent ID
    try:
        transaction = Transaction.objects.get(transaction_id=payment_intent['id'])

        # Update transaction status
        transaction.status = 'completed'
        transaction.completed_at = timezone.now()
        transaction.provider_response = payment_intent
        transaction.save()

        # Update order payment status
        order = transaction.order
        order.payment_status = 'paid'
        order.save()

        # Create an invoice for the order if it doesn't exist
        from .services.invoice_service import create_invoice_for_order
        if not order.invoices.exists():
            create_invoice_for_order(order)

    except Transaction.DoesNotExist:
        # If transaction doesn't exist, it might be a payment created outside our system
        # Try to find the order from metadata
        if 'metadata' in payment_intent and 'order_id' in payment_intent['metadata']:
            try:
                order_id = payment_intent['metadata']['order_id']
                order = Order.objects.get(id=order_id)

                # Find the appropriate payment method
                payment_method = PaymentMethod.objects.filter(provider='stripe', is_active=True).first()

                if payment_method:
                    # Create a new transaction
                    transaction = Transaction.objects.create(
                        order=order,
                        payment_method=payment_method,
                        transaction_id=payment_intent['id'],
                        amount=payment_intent['amount'] / 100,  # Convert from cents
                        currency=payment_intent['currency'].upper(),
                        status='completed',
                        completed_at=timezone.now(),
                        provider_response=payment_intent
                    )

                    # Update order payment status
                    order.payment_status = 'paid'
                    order.save()

                    # Create an invoice for the order
                    from .services.invoice_service import create_invoice_for_order
                    if not order.invoices.exists():
                        create_invoice_for_order(order)

            except Order.DoesNotExist:
                # Order not found, log this event
                pass
            except Exception as e:
                # Log other errors
                pass

def handle_payment_intent_failed(payment_intent):
    """
    Handle a failed payment intent.

    Args:
        payment_intent: The payment intent object from Stripe
    """
    # Find the transaction by payment intent ID
    try:
        transaction = Transaction.objects.get(transaction_id=payment_intent['id'])

        # Update transaction status
        transaction.status = 'failed'
        transaction.provider_response = payment_intent

        # Get error message
        if 'last_payment_error' in payment_intent and payment_intent['last_payment_error']:
            transaction.error_message = payment_intent['last_payment_error'].get('message', 'Payment failed')
        else:
            transaction.error_message = 'Payment failed'

        transaction.save()

        # Update order payment status
        order = transaction.order
        order.payment_status = 'failed'
        order.save()

    except Transaction.DoesNotExist:
        # Transaction not found, log this event
        pass
    except Exception as e:
        # Log other errors
        pass

def handle_charge_refunded(charge):
    """
    Handle a refunded charge.

    Args:
        charge: The charge object from Stripe
    """
    # Find the transaction by charge ID or payment intent ID
    try:
        # Try to find by charge ID first
        transaction = Transaction.objects.filter(
            provider_response__id=charge['id']
        ).first()

        if not transaction:
            # Try to find by payment intent ID
            transaction = Transaction.objects.filter(
                transaction_id=charge['payment_intent']
            ).first()

        if transaction:
            # Check if it's a full or partial refund
            refunded_amount = charge['amount_refunded'] / 100  # Convert from cents

            if refunded_amount == transaction.amount:
                transaction.status = 'refunded'
            else:
                transaction.status = 'partially_refunded'

            transaction.provider_response = charge
            transaction.save()

            # Update order payment status
            order = transaction.order
            order.payment_status = transaction.status
            order.save()

            # Create a refund transaction
            Transaction.objects.create(
                order=order,
                payment_method=transaction.payment_method,
                transaction_id=f"refund_{charge['id']}",
                amount=-refunded_amount,  # Negative amount for refunds
                currency=charge['currency'].upper(),
                status='completed',
                completed_at=timezone.now(),
                provider_response=charge
            )

    except Exception as e:
        # Log errors
        pass

@csrf_exempt
@require_POST
def paypal_webhook(request):
    """
    Webhook handler for PayPal events.

    This endpoint receives webhook events from PayPal and processes them accordingly.
    """
    payload = request.body

    # Verify the webhook signature
    # In production, you would verify the webhook signature using PayPal's webhook ID
    # https://developer.paypal.com/docs/api/webhooks/v1/#verify-webhook-signature

    try:
        # Parse the payload
        event_data = json.loads(payload)

        # Get the event type
        event_type = event_data.get('event_type')

        # Handle the event based on its type
        if event_type == 'PAYMENT.CAPTURE.COMPLETED':
            handle_paypal_payment_completed(event_data)
        elif event_type == 'PAYMENT.CAPTURE.DENIED':
            handle_paypal_payment_denied(event_data)
        elif event_type == 'PAYMENT.CAPTURE.REFUNDED':
            handle_paypal_payment_refunded(event_data)
        # Add more event types as needed

        return HttpResponse(status=200)

    except ValueError as e:
        # Invalid payload
        return HttpResponse(status=400)
    except Exception as e:
        # Generic error handling
        return HttpResponse(content=str(e), status=500)

def handle_paypal_payment_completed(event_data):
    """
    Handle a completed PayPal payment.

    Args:
        event_data: The event data from PayPal
    """
    resource = event_data.get('resource', {})
    payment_id = resource.get('id')

    if not payment_id:
        return

    # Find the transaction by payment ID
    try:
        transaction = Transaction.objects.get(transaction_id=payment_id)

        # Update transaction status
        transaction.status = 'completed'
        transaction.completed_at = timezone.now()
        transaction.provider_response = resource
        transaction.save()

        # Update order payment status
        order = transaction.order
        order.payment_status = 'paid'
        order.save()

        # Create an invoice for the order if it doesn't exist
        from .services.invoice_service import create_invoice_for_order
        if not order.invoices.exists():
            create_invoice_for_order(order)

    except Transaction.DoesNotExist:
        # If transaction doesn't exist, it might be a payment created outside our system
        # Try to find the order from custom ID
        custom_id = resource.get('custom_id')
        if custom_id and custom_id.startswith('order_'):
            try:
                order_id = custom_id.replace('order_', '')
                order = Order.objects.get(id=order_id)

                # Find the appropriate payment method
                payment_method = PaymentMethod.objects.filter(provider='paypal', is_active=True).first()

                if payment_method:
                    # Get the amount
                    amount = float(resource.get('amount', {}).get('value', 0))
                    currency = resource.get('amount', {}).get('currency_code', 'USD')

                    # Create a new transaction
                    transaction = Transaction.objects.create(
                        order=order,
                        payment_method=payment_method,
                        transaction_id=payment_id,
                        amount=amount,
                        currency=currency,
                        status='completed',
                        completed_at=timezone.now(),
                        provider_response=resource
                    )

                    # Update order payment status
                    order.payment_status = 'paid'
                    order.save()

                    # Create an invoice for the order
                    from .services.invoice_service import create_invoice_for_order
                    if not order.invoices.exists():
                        create_invoice_for_order(order)

            except Order.DoesNotExist:
                # Order not found, log this event
                pass
            except Exception as e:
                # Log other errors
                pass
    except Exception as e:
        # Log errors
        pass

def handle_paypal_payment_denied(event_data):
    """
    Handle a denied PayPal payment.

    Args:
        event_data: The event data from PayPal
    """
    resource = event_data.get('resource', {})
    payment_id = resource.get('id')

    if not payment_id:
        return

    # Find the transaction by payment ID
    try:
        transaction = Transaction.objects.get(transaction_id=payment_id)

        # Update transaction status
        transaction.status = 'failed'
        transaction.provider_response = resource
        transaction.error_message = resource.get('status_details', {}).get('reason', 'Payment denied')
        transaction.save()

        # Update order payment status
        order = transaction.order
        order.payment_status = 'failed'
        order.save()

    except Transaction.DoesNotExist:
        # Transaction not found, log this event
        pass
    except Exception as e:
        # Log other errors
        pass

def handle_paypal_payment_refunded(event_data):
    """
    Handle a refunded PayPal payment.

    Args:
        event_data: The event data from PayPal
    """
    resource = event_data.get('resource', {})
    refund_id = resource.get('id')
    payment_id = resource.get('links', [{}])[0].get('href', '').split('/')[-1]

    if not payment_id:
        return

    # Find the transaction by payment ID
    try:
        transaction = Transaction.objects.get(transaction_id=payment_id)

        # Get the refund amount
        refund_amount = float(resource.get('amount', {}).get('value', 0))
        currency = resource.get('amount', {}).get('currency_code', 'USD')

        # Check if it's a full or partial refund
        if refund_amount == transaction.amount:
            transaction.status = 'refunded'
        else:
            transaction.status = 'partially_refunded'

        transaction.provider_response = resource
        transaction.save()

        # Update order payment status
        order = transaction.order
        order.payment_status = transaction.status
        order.save()

        # Create a refund transaction
        Transaction.objects.create(
            order=order,
            payment_method=transaction.payment_method,
            transaction_id=refund_id,
            amount=-refund_amount,  # Negative amount for refunds
            currency=currency,
            status='completed',
            completed_at=timezone.now(),
            provider_response=resource
        )

    except Transaction.DoesNotExist:
        # Transaction not found, log this event
        pass
    except Exception as e:
        # Log errors
        pass
