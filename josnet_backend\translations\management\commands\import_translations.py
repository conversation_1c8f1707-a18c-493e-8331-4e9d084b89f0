from django.core.management.base import BaseCommand
from translations.utils import bulk_create_translations
import json
import os

class Command(BaseCommand):
    help = 'Importer les traductions depuis un fichier JSON'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='initial_translations.json',
            help='Fichier JSON contenant les traductions'
        )

    def handle(self, *args, **options):
        file_path = options['file']
        
        if not os.path.exists(file_path):
            self.stdout.write(
                self.style.ERROR(f'Fichier non trouvé: {file_path}')
            )
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                translations_data = json.load(f)
            
            stats = bulk_create_translations(translations_data)
            
            self.stdout.write(
                self.style.SUCCESS(f'Import terminé avec succès!')
            )
            self.stdout.write(f'Clés créées: {stats["keys_created"]}')
            self.stdout.write(f'Clés mises à jour: {stats["keys_updated"]}')
            self.stdout.write(f'Traductions créées: {stats["translations_created"]}')
            self.stdout.write(f'Traductions mises à jour: {stats["translations_updated"]}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Erreur lors de l\'import: {e}')
            )
