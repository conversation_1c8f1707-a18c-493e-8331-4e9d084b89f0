from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from .resources import (
    CategoryResource,
    ProductAttributeResource,
    AttributeValueResource,
    ProductResource,
    ProductVariantResource,
    InventoryResource
)
from .models import (
    Category,
    ProductAttribute,
    AttributeValue,
    Product,
    ProductImage,
    ProductAttributeValue,
    ProductVariant,
    Inventory,
    StockMovement,
    StockReservation,
    ProductReview
)

class CategoryAdmin(ImportExportModelAdmin):
    resource_class = CategoryResource
    list_display = ('name', 'slug', 'parent', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'slug')
    prepopulated_fields = {'slug': ('name',)}

class ProductAttributeAdmin(ImportExportModelAdmin):
    resource_class = ProductAttributeResource
    list_display = ('name', 'description')
    search_fields = ('name',)

class AttributeValueAdmin(ImportExportModelAdmin):
    resource_class = AttributeValueResource
    list_display = ('attribute', 'value')
    list_filter = ('attribute',)
    search_fields = ('value',)

class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1

class ProductAttributeValueInline(admin.TabularInline):
    model = ProductAttributeValue
    extra = 1

class ProductVariantInline(admin.TabularInline):
    model = ProductVariant
    extra = 1

class InventoryInline(admin.StackedInline):
    model = Inventory
    extra = 0
    max_num = 1

class ProductAdmin(ImportExportModelAdmin):
    resource_class = ProductResource
    list_display = ('name', 'sku', 'price', 'sale_price', 'status', 'is_featured', 'created_at')
    list_filter = ('status', 'is_featured', 'is_digital', 'created_at')
    search_fields = ('name', 'sku', 'description')
    prepopulated_fields = {'slug': ('name',)}
    inlines = [ProductImageInline, ProductAttributeValueInline, ProductVariantInline, InventoryInline]
    filter_horizontal = ('categories',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'slug', 'sku', 'description', 'short_description')
        }),
        ('Pricing', {
            'fields': ('price', 'sale_price', 'cost_price')
        }),
        ('Categories', {
            'fields': ('categories',)
        }),
        ('Status', {
            'fields': ('status', 'is_featured', 'is_digital')
        }),
        ('Metadata', {
            'fields': ('created_by', 'updated_by', 'created_at', 'updated_at')
        }),
    )

class ProductVariantAdmin(ImportExportModelAdmin):
    resource_class = ProductVariantResource
    list_display = ('product', 'name', 'sku', 'price_adjustment', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'sku', 'product__name')
    inlines = [InventoryInline]

class InventoryAdmin(ImportExportModelAdmin):
    resource_class = InventoryResource
    list_display = ('get_product_name', 'quantity', 'reserved_quantity', 'available_quantity', 'is_in_stock', 'is_low_stock')
    list_filter = ('quantity', 'reserved_quantity')
    search_fields = ('product__name', 'variant__name')

    def get_product_name(self, obj):
        if obj.product:
            return obj.product.name
        elif obj.variant:
            return f"{obj.variant.product.name} - {obj.variant.name}"
        return "Unknown"
    get_product_name.short_description = 'Product'

class ProductReviewAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'rating', 'title', 'is_approved', 'created_at')
    list_filter = ('rating', 'is_approved', 'created_at')
    search_fields = ('product__name', 'user__email', 'title', 'comment')
    actions = ['approve_reviews']

    def approve_reviews(self, request, queryset):
        queryset.update(is_approved=True)
    approve_reviews.short_description = "Approve selected reviews"

class StockMovementAdmin(admin.ModelAdmin):
    list_display = ('inventory', 'quantity_before', 'quantity_after', 'quantity_changed', 'reason', 'reference', 'created_at', 'created_by')
    list_filter = ('reason', 'created_at')
    search_fields = ('reference', 'notes')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'

class StockReservationAdmin(admin.ModelAdmin):
    list_display = ('inventory', 'quantity', 'reference', 'created_at', 'expires_at', 'created_by')
    list_filter = ('created_at', 'expires_at')
    search_fields = ('reference', 'notes')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'

# Register models
admin.site.register(Category, CategoryAdmin)
admin.site.register(ProductAttribute, ProductAttributeAdmin)
admin.site.register(AttributeValue, AttributeValueAdmin)
admin.site.register(Product, ProductAdmin)
admin.site.register(ProductVariant, ProductVariantAdmin)
admin.site.register(Inventory, InventoryAdmin)
admin.site.register(StockMovement, StockMovementAdmin)
admin.site.register(StockReservation, StockReservationAdmin)
admin.site.register(ProductReview, ProductReviewAdmin)
