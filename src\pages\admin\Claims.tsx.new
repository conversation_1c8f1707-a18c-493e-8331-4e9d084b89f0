import React, { useState, useEffect } from 'react';
import { <PERSON>, Typo<PERSON>, Card, CardContent, Grid as <PERSON>i<PERSON><PERSON>, <PERSON>, <PERSON>ton, TextField, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip, CircularProgress, Tabs, Tab, Avatar, Divider } from '@mui/material';

// Wrapper pour Grid avec les propriétés correctement typées
const Grid = (props: {
  children: React.ReactNode;
  container?: boolean;
  item?: boolean;
  xs?: number | boolean;
  sm?: number | boolean;
  md?: number | boolean;
  lg?: number | boolean;
  xl?: number | boolean;
  spacing?: number;
  direction?: 'row' | 'row-reverse' | 'column' | 'column-reverse';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  sx?: any;
}) => {
  return <MuiGrid {...props} />;
};
import { useTheme } from '@mui/material/styles';
import AdminLayout from '../../components/admin/AdminLayout';

// Fonction utilitaire pour formater les dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
};

// Fonction utilitaire pour formater l'heure
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
};
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SendIcon from '@mui/icons-material/Send';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import PersonIcon from '@mui/icons-material/Person';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import ComputerIcon from '@mui/icons-material/Computer';

// Types
interface ClaimMessage {
  id: string;
  sender: 'customer' | 'support' | 'system';
  message: string;
  is_read: boolean;
  created_at: string;
  attachments: {
    id: string;
    file_name: string;
    file_size: number;
    content_type: string;
    file_url: string;
  }[];
}

interface ClaimStatusHistory {
  id: string;
  old_status: string;
  new_status: string;
  comment: string | null;
  created_by: {
    id: string;
    email: string;
    full_name: string;
  } | null;
  created_at: string;
}

interface Claim {
  id: string;
  claim_number: string;
  user: {
    id: string;
    email: string;
    full_name: string;
  };
  order: {
    id: string;
    order_number: string;
  } | null;
  subject: string;
  description: string;
  status: 'new' | 'in_progress' | 'waiting_customer' | 'waiting_staff' | 'resolved' | 'closed';
  created_at: string;
  updated_at: string;
  last_update: string;
  messages: ClaimMessage[];
  status_history: ClaimStatusHistory[];
}

const statusColors = {
  new: '#FFA000',                // Amber
  in_progress: '#2196F3',         // Blue
  waiting_customer: '#FF5722',    // Deep Orange
  waiting_staff: '#9C27B0',       // Purple
  resolved: '#4CAF50',            // Green
  closed: '#9E9E9E'               // Grey
};

const statusLabels = {
  new: 'Nouvelle réclamation',
  in_progress: 'Traitement en cours',
  waiting_customer: 'Attente réponse client',
  waiting_staff: 'Attente réponse support',
  resolved: 'Résolue',
  closed: 'Clôturée'
};

const subjectLabels = {
  order_issue: 'Problème de commande',
  product_issue: 'Problème de produit',
  shipping_issue: 'Problème de livraison',
  account_issue: 'Problème de compte',
  website_issue: 'Problème technique',
  payment_issue: 'Problème de paiement',
  other: 'Autre sujet'
};

const AdminClaims: React.FC = () => {
  const theme = useTheme();
  const [claims, setClaims] = useState<Claim[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [subjectFilter, setSubjectFilter] = useState<string>('all');
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [updateStatusOpen, setUpdateStatusOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [adminNote, setAdminNote] = useState('');
  const [replyMessage, setReplyMessage] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  // Fonctions de gestion d'événements
  const handleViewDetails = (claim: Claim) => {
    setSelectedClaim(claim);
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  const handleUpdateStatus = (claim: Claim) => {
    setSelectedClaim(claim);
    setNewStatus(claim.status);
    setUpdateStatusOpen(true);
  };

  const submitStatusUpdate = async () => {
    if (!selectedClaim || !newStatus) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/admin/claims/${selectedClaim.id}/status/`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus,
          comment: adminNote
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      // Update local state
      const updatedClaim = await response.json();
      setClaims(prevClaims => 
        prevClaims.map(item => 
          item.id === updatedClaim.id ? updatedClaim : item
        )
      );

      setUpdateStatusOpen(false);
      setAdminNote('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      console.error('Error updating claim status:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSendReply = async () => {
    if (!selectedClaim || !replyMessage.trim()) return;
    
    try {
      setLoading(true);
      
      const formData = new FormData();
      formData.append('sender', 'support');
      formData.append('message', replyMessage);
      
      // Add files if any
      selectedFiles.forEach(file => {
        formData.append('uploaded_files', file);
      });

      const response = await fetch(`/api/admin/claims/${selectedClaim.id}/messages/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      // Update local state with the updated claim
      const updatedClaim = await response.json();
      setClaims(prevClaims => 
        prevClaims.map(item => 
          item.id === updatedClaim.id ? updatedClaim : item
        )
      );
      
      // If the selected claim is open in the details view, update it
      if (selectedClaim && selectedClaim.id === updatedClaim.id) {
        setSelectedClaim(updatedClaim);
      }

      // Reset form
      setReplyMessage('');
      setSelectedFiles([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      console.error('Error sending reply:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const filesArray = Array.from(event.target.files);
      setSelectedFiles(prevFiles => [...prevFiles, ...filesArray]);
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  const getSenderIcon = (sender: string) => {
    switch (sender) {
      case 'customer':
        return <PersonIcon />;
      case 'support':
        return <SupportAgentIcon />;
      case 'system':
        return <ComputerIcon />;
      default:
        return null;
    }
  };

  const getSenderName = (sender: string) => {
    switch (sender) {
      case 'customer':
        return 'Client';
      case 'support':
        return 'Support';
      case 'system':
        return 'Système';
      default:
        return sender;
    }
  };
