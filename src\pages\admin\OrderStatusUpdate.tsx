import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  AlertCircle,
  Package,
  Truck,
  MapPin,
  CreditCard,
  User,
  Calendar,
  FileText,
  Save,
  RefreshCw,
  History
} from 'lucide-react';
import orderApi from '@/services/orderApi';
import { toast } from '@/hooks/use-toast';
import { formatDate, formatCurrency } from '@/utils/formatters';

const OrderStatusUpdate = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [selectedStatus, setSelectedStatus] = useState('');
  const [notes, setNotes] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch order details
  const {
    data: order,
    isLoading: orderLoading,
    error: orderError
  } = useQuery({
    queryKey: ['admin-order', id],
    queryFn: () => orderApi.getOrder(parseInt(id!)),
    enabled: !!id,
  });

  // Status options with descriptions and colors (matching Django model choices)
  const statusOptions = [
    {
      value: 'pending',
      label: 'En Attente',
      description: 'Commande reçue, en attente de traitement',
      color: 'bg-yellow-100 text-yellow-800',
      icon: Clock
    },
    {
      value: 'processing',
      label: 'En Traitement',
      description: 'Commande en cours de préparation',
      color: 'bg-blue-100 text-blue-800',
      icon: Package
    },
    {
      value: 'shipped',
      label: 'Expédiée',
      description: 'Commande expédiée, en transit',
      color: 'bg-purple-100 text-purple-800',
      icon: Truck
    },
    {
      value: 'delivered',
      label: 'Livrée',
      description: 'Commande livrée au client',
      color: 'bg-green-100 text-green-800',
      icon: CheckCircle
    },
    {
      value: 'cancelled',
      label: 'Annulée',
      description: 'Commande annulée',
      color: 'bg-red-100 text-red-800',
      icon: AlertCircle
    },
    {
      value: 'refunded',
      label: 'Remboursée',
      description: 'Commande entièrement remboursée',
      color: 'bg-gray-100 text-gray-800',
      icon: RefreshCw
    },
    {
      value: 'partially_refunded',
      label: 'Partiellement Remboursée',
      description: 'Commande partiellement remboursée',
      color: 'bg-gray-100 text-gray-800',
      icon: RefreshCw
    },
    {
      value: 'on_hold',
      label: 'En Attente de Paiement',
      description: 'Commande mise en attente de paiement',
      color: 'bg-orange-100 text-orange-800',
      icon: Clock
    }
  ];

  // Get current status info
  const getCurrentStatusInfo = () => {
    return statusOptions.find(status => status.value === order?.status) || statusOptions[0];
  };

  // Get selected status info
  const getSelectedStatusInfo = () => {
    return statusOptions.find(status => status.value === selectedStatus);
  };

  // Handle status update
  const handleUpdateStatus = async () => {
    if (!id || !selectedStatus) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un statut.",
        variant: "destructive",
      });
      return;
    }

    if (selectedStatus === order?.status) {
      toast({
        title: "Information",
        description: "Le statut sélectionné est identique au statut actuel.",
        variant: "default",
      });
      return;
    }

    setIsUpdating(true);
    try {
      await orderApi.addOrderStatus(parseInt(id), selectedStatus, notes);

      toast({
        title: "Statut mis à jour",
        description: `Le statut de la commande a été mis à jour vers "${getSelectedStatusInfo()?.label}".`,
      });

      // Refresh order data
      queryClient.invalidateQueries({ queryKey: ['admin-order', id] });

      // Navigate back to order detail
      navigate(`/admin/orders/${id}`);

    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Impossible de mettre à jour le statut.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Set initial status when order loads
  useEffect(() => {
    if (order && !selectedStatus) {
      setSelectedStatus(order.status || 'pending');
    }
  }, [order, selectedStatus]);

  if (orderLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </AdminLayout>
    );
  }

  if (orderError || !order) {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="py-8 text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h3 className="text-lg font-semibold mb-2">Commande non trouvée</h3>
            <p className="text-gray-600 mb-4">La commande #{id} n'existe pas.</p>
            <Link to="/admin/orders">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour aux commandes
              </Button>
            </Link>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  const currentStatusInfo = getCurrentStatusInfo();
  const selectedStatusInfo = getSelectedStatusInfo();

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Link to={`/admin/orders/${id}`}>
              <Button variant="outline" size="sm" className="mb-2">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour au détail
              </Button>
            </Link>
            <h1 className="text-3xl font-bold">Mise à Jour du Statut</h1>
            <p className="text-gray-600">Commande #{order.order_number}</p>
          </div>
          <Badge variant="outline" className="text-lg px-4 py-2">
            <currentStatusInfo.icon className="h-4 w-4 mr-2" />
            {currentStatusInfo.label}
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Status Update Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Current Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Statut Actuel
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg ${currentStatusInfo.color}`}>
                  <currentStatusInfo.icon className="h-5 w-5" />
                  <span className="font-semibold">{currentStatusInfo.label}</span>
                </div>
                <p className="text-sm text-gray-600 mt-2">{currentStatusInfo.description}</p>
              </CardContent>
            </Card>

            {/* New Status Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <RefreshCw className="h-5 w-5" />
                  Nouveau Statut
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Sélectionner le nouveau statut</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choisir un statut" />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          <div className="flex items-center gap-2">
                            <status.icon className="h-4 w-4" />
                            <span>{status.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedStatusInfo && (
                  <div className={`p-3 rounded-lg ${selectedStatusInfo.color}`}>
                    <div className="flex items-center gap-2 mb-1">
                      <selectedStatusInfo.icon className="h-4 w-4" />
                      <span className="font-semibold">{selectedStatusInfo.label}</span>
                    </div>
                    <p className="text-sm">{selectedStatusInfo.description}</p>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes (optionnel)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Ajouter des notes sur ce changement de statut..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={4}
                  />
                </div>

                {selectedStatus !== order.status && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Le statut passera de "{currentStatusInfo.label}" à "{selectedStatusInfo?.label}".
                      Cette action sera enregistrée dans l'historique de la commande.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex gap-3">
                  <Button
                    onClick={handleUpdateStatus}
                    disabled={isUpdating || selectedStatus === order.status}
                    className="flex-1"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isUpdating ? 'Mise à jour...' : 'Mettre à jour le statut'}
                  </Button>
                  <Link to={`/admin/orders/${id}`}>
                    <Button variant="outline">
                      Annuler
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary Sidebar */}
          <div className="space-y-6">
            {/* Order Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Informations Commande
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{formatDate(order.created_at)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{formatCurrency(order.total)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{order.items?.length || 0} article(s)</span>
                </div>
              </CardContent>
            </Card>

            {/* Customer Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Client
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="font-semibold">
                  {order.billing_first_name} {order.billing_last_name}
                </p>
                <p className="text-sm text-gray-600">{order.email}</p>
                {order.phone && (
                  <p className="text-sm text-gray-600">{order.phone}</p>
                )}
              </CardContent>
            </Card>

            {/* Status History */}
            {order.status_history && order.status_history.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <History className="h-5 w-5" />
                    Historique
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {order.status_history.slice(0, 3).map((history, index) => {
                      const statusInfo = statusOptions.find(s => s.value === history.status);
                      return (
                        <div key={history.id} className="flex items-start gap-3">
                          <div className={`p-1 rounded-full ${statusInfo?.color || 'bg-gray-100'}`}>
                            {statusInfo?.icon && <statusInfo.icon className="h-3 w-3" />}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium">{statusInfo?.label || history.status}</p>
                            <p className="text-xs text-gray-500">{formatDate(history.created_at)}</p>
                            {history.notes && (
                              <p className="text-xs text-gray-600 mt-1">{history.notes}</p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default OrderStatusUpdate;
