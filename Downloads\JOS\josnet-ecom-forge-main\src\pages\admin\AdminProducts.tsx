import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Helmet } from 'react-helmet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import ProductsTable from '@/components/admin/products/ProductsTable';
import ProductFilters from '@/components/admin/products/ProductFilters';
import { productApi, ProductFilterParams, ProductListItem, Category } from '@/services/productApi';
import { Plus, Search, RefreshCcw, PackageOpen } from 'lucide-react';

const AdminProducts: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ProductFilterParams>({
    page: 1,
    page_size: 10
  });
  const [maxPrice, setMaxPrice] = useState(1000);

  // Fetch products with filters
  const {
    data: productsData,
    isLoading: isLoadingProducts,
    isError: isProductsError,
    refetch: refetchProducts
  } = useQuery({
    queryKey: ['products', filters],
    queryFn: () => productApi.getProducts(filters),
  });

  // Fetch categories for filters
  const {
    data: categoriesData,
    isLoading: isLoadingCategories
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => productApi.getCategories(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Ensure categories is always an array
  const categories = Array.isArray(categoriesData) ? categoriesData : [];

  // Delete product mutation
  const deleteProductMutation = useMutation({
    mutationFn: (slug: string) => productApi.deleteProduct(slug),
    onSuccess: () => {
      toast({
        title: 'Produit supprimé',
        description: 'Le produit a été supprimé avec succès.',
      });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la suppression du produit.',
        variant: 'destructive',
      });
    },
  });

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters({
      ...filters,
      name: searchTerm || undefined,
      page: 1, // Reset to first page on new search
    });
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: ProductFilterParams) => {
    setFilters({
      ...newFilters,
      page: 1, // Reset to first page on filter change
    });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters({
      ...filters,
      page,
    });
    window.scrollTo(0, 0);
  };

  // Handle product deletion
  const handleDeleteProduct = (slug: string) => {
    deleteProductMutation.mutate(slug);
  };

  // Calculate max price for filter slider
  useEffect(() => {
    if (productsData?.results) {
      const highestPrice = Math.max(
        ...productsData.results.map(p => p.price),
        ...productsData.results.map(p => p.sale_price || 0)
      );
      setMaxPrice(Math.ceil(highestPrice * 1.2)); // Add 20% buffer
    }
  }, [productsData]);

  // Generate pagination items
  const renderPaginationItems = () => {
    if (!productsData) return null;

    const totalPages = Math.ceil(productsData.count / (filters.page_size || 10));
    const currentPage = filters.page || 1;

    // Show 5 pages max, centered around current page
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    // Adjust if we're near the end
    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }

    const pages = [];
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <PaginationItem key={i}>
          <PaginationLink
            isActive={i === currentPage}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return pages;
  };

  return (
    <AdminLayout>
      <Helmet>
        <title>Gestion des produits | Admin JosNet</title>
      </Helmet>

      <div className="container mx-auto py-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Produits</h1>
            <p className="text-muted-foreground">
              Gérez votre catalogue de produits
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => refetchProducts()}
              disabled={isLoadingProducts}
              className="gap-2"
            >
              <RefreshCcw className="h-4 w-4" />
              <span>Actualiser</span>
            </Button>
            <Button asChild variant="outline" className="gap-2">
              <Link to="/admin/products/inventory">
                <PackageOpen className="h-4 w-4" />
                <span>Gestion des stocks</span>
              </Link>
            </Button>
            <Button asChild className="gap-2">
              <Link to="/admin/products/new">
                <Plus className="h-4 w-4" />
                <span>Nouveau produit</span>
              </Link>
            </Button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row md:items-center gap-4 mb-6">
          <form onSubmit={handleSearch} className="flex-1 flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Rechercher un produit..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">Rechercher</Button>
          </form>
        </div>

        {/* Filters */}
        <ProductFilters
          categories={categories}
          filters={filters}
          onFilterChange={handleFilterChange}
          maxPrice={maxPrice}
        />

        {/* Products table */}
        {isLoadingProducts ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-96 w-full" />
          </div>
        ) : isProductsError ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            Une erreur est survenue lors du chargement des produits. Veuillez réessayer.
          </div>
        ) : productsData?.results.length === 0 ? (
          <div className="bg-gray-50 border border-gray-200 text-gray-700 px-4 py-10 rounded-md text-center">
            <h3 className="text-lg font-medium mb-2">Aucun produit trouvé</h3>
            <p className="text-gray-500 mb-4">
              Aucun produit ne correspond à vos critères de recherche ou de filtrage.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setFilters({ page: 1, page_size: 10 });
                setSearchTerm('');
              }}
            >
              Réinitialiser les filtres
            </Button>
          </div>
        ) : (
          <>
            <ProductsTable
              products={productsData.results}
              onDelete={handleDeleteProduct}
            />

            {/* Pagination */}
            {productsData.count > (filters.page_size || 10) && (
              <Pagination className="mt-6">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, (filters.page || 1) - 1))}
                      disabled={(filters.page || 1) <= 1}
                    />
                  </PaginationItem>

                  {renderPaginationItems()}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange((filters.page || 1) + 1)}
                      disabled={(filters.page || 1) >= Math.ceil(productsData.count / (filters.page_size || 10))}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminProducts;
