import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Package,
  Search,
  Filter,
  AlertTriangle,
  RefreshCw,
  Download,
  Upload,
  ChevronDown,
  Loader2
} from 'lucide-react';
import { productApi } from '@/services/productApi';
import InventoryAdjustmentDialog from '@/components/admin/products/InventoryAdjustmentDialog';
import InventoryFilterDialog from '@/components/admin/products/InventoryFilterDialog';
import InventoryStatsCard from '@/components/admin/products/InventoryStatsCard';
import InventoryBulkActionsDialog from '@/components/admin/products/InventoryBulkActionsDialog';
import ProductImage from '@/components/common/ProductImage';
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const InventoryManagement: React.FC = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    category: '',
    lowStock: false
  });
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);
  const [isAdjustDialogOpen, setIsAdjustDialogOpen] = useState(false);
  const [isBulkActionsDialogOpen, setIsBulkActionsDialogOpen] = useState(false);
  const [selectedInventoryId, setSelectedInventoryId] = useState<number | null>(null);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // Fetch inventory data
  const {
    data: inventoryItems = [],
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['inventory', filters],
    queryFn: () => productApi.getInventoryItems(filters),
  });

  // Fetch inventory stats
  const { data: inventoryStats } = useQuery({
    queryKey: ['inventoryStats'],
    queryFn: () => productApi.getInventoryStats(),
  });

  // Filter inventory items based on search query
  const filteredItems = inventoryItems.filter(item =>
    item.product_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.variant_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.sku?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle row selection
  const toggleItemSelection = (id: number) => {
    if (selectedItems.includes(id)) {
      setSelectedItems(selectedItems.filter(itemId => itemId !== id));
    } else {
      setSelectedItems([...selectedItems, id]);
    }
  };

  // Handle select all
  const toggleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  // Handle export
  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Exporting inventory data to CSV...",
    });
    // In a real app, this would trigger an API call to export data
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Stocks</h1>
          <p className="text-gray-500">Suivez et gérez l'inventaire de votre boutique</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <InventoryStatsCard
            title="Total Produits"
            value={inventoryStats?.total_products || 0}
            icon={<Package className="h-5 w-5" />}
            description="Produits dans l'inventaire"
          />
          <InventoryStatsCard
            title="En Stock"
            value={inventoryStats?.in_stock || 0}
            icon={<Package className="h-5 w-5" />}
            description="Produits disponibles"
            color="green"
          />
          <InventoryStatsCard
            title="Stock Bas"
            value={inventoryStats?.low_stock || 0}
            icon={<AlertTriangle className="h-5 w-5" />}
            description="Produits sous le seuil"
            color="amber"
          />
          <InventoryStatsCard
            title="Rupture de Stock"
            value={inventoryStats?.out_of_stock || 0}
            icon={<AlertTriangle className="h-5 w-5" />}
            description="Produits à zéro"
            color="red"
          />
        </div>

        {/* Action Bar */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher un produit..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsFilterDialogOpen(true)}
              className="flex items-center gap-1"
            >
              <Filter className="h-4 w-4" />
              Filtres
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  <Download className="h-4 w-4" />
                  Export
                  <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={handleExport}>
                  Exporter tout
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleExport}
                  disabled={selectedItems.length === 0}
                >
                  Exporter la sélection
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button
              variant="outline"
              onClick={() => setIsBulkActionsDialogOpen(true)}
              disabled={selectedItems.length === 0}
              className="flex items-center gap-1"
            >
              Actions groupées
            </Button>
            <Button
              onClick={() => refetch()}
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-4 w-4" />
              Rafraîchir
            </Button>
          </div>
        </div>

        {/* Inventory Table */}
        <Card>
          <CardHeader>
            <CardTitle>Articles en Stock</CardTitle>
            <CardDescription>
              Gérez les niveaux de stock et les mouvements d'inventaire
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <input
                        type="checkbox"
                        checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
                        onChange={toggleSelectAll}
                      />
                    </TableHead>
                    <TableHead>Produit</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="text-center">Stock</TableHead>
                    <TableHead className="text-center">Réservé</TableHead>
                    <TableHead className="text-center">Disponible</TableHead>
                    <TableHead className="text-center">Statut</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2 text-sm text-gray-500">Chargement des données d'inventaire...</p>
                      </TableCell>
                    </TableRow>
                  ) : filteredItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <p className="text-sm text-gray-500">Aucun article d'inventaire trouvé.</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(item.id)}
                            onChange={() => toggleItemSelection(item.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{item.product_name}</div>
                          {item.variant_name && (
                            <div className="text-sm text-gray-500">{item.variant_name}</div>
                          )}
                        </TableCell>
                        <TableCell>
                          <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{item.sku}</code>
                        </TableCell>
                        <TableCell className="text-center">{item.quantity}</TableCell>
                        <TableCell className="text-center">{item.reserved_quantity}</TableCell>
                        <TableCell className="text-center font-medium">
                          {item.available_quantity}
                        </TableCell>
                        <TableCell className="text-center">
                          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            item.is_in_stock
                              ? item.is_low_stock
                                ? 'bg-amber-100 text-amber-800'
                                : 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {item.is_in_stock
                              ? item.is_low_stock
                                ? 'Stock Bas'
                                : 'En Stock'
                              : 'Rupture'
                            }
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedInventoryId(item.id);
                              setIsAdjustDialogOpen(true);
                            }}
                          >
                            Ajuster
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Dialogs */}
        <InventoryFilterDialog
          isOpen={isFilterDialogOpen}
          onOpenChange={setIsFilterDialogOpen}
          filters={filters}
          onApplyFilters={setFilters}
        />

        <InventoryAdjustmentDialog
          isOpen={isAdjustDialogOpen}
          onOpenChange={setIsAdjustDialogOpen}
          inventoryId={selectedInventoryId}
          onSuccess={() => refetch()}
        />

        <InventoryBulkActionsDialog
          isOpen={isBulkActionsDialogOpen}
          onOpenChange={setIsBulkActionsDialogOpen}
          selectedItems={selectedItems}
          onSuccess={() => {
            refetch();
            setSelectedItems([]);
          }}
        />
      </div>
    </AdminLayout>
  );
};

export default InventoryManagement;
