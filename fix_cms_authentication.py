#!/usr/bin/env python3
"""
Script pour diagnostiquer et corriger les problèmes d'authentification CMS
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
User = get_user_model()
# Token importé localement pour éviter les conflits
from cms.models import SiteSetting, Media
from django.conf import settings

def check_cms_authentication():
    """Vérifier l'authentification CMS"""
    print("🔐 DIAGNOSTIC DE L'AUTHENTIFICATION CMS")
    print("=" * 50)
    
    # Vérifier les utilisateurs admin
    admin_users = User.objects.filter(is_staff=True, is_superuser=True)
    print(f"👥 Utilisateurs admin: {admin_users.count()}")
    
    for user in admin_users:
        print(f"   • {user.email} (actif: {user.is_active})")
        
        # Vérifier le token
        try:
            from rest_framework.authtoken.models import Token as AuthToken
            token, created = AuthToken.objects.get_or_create(user=user)
            if created:
                print(f"     ✅ Token créé: {token.key[:10]}...")
            else:
                print(f"     Token: {token.key[:10]}...")
        except Exception as e:
            print(f"     ❌ Erreur token: {e}")
    
    return admin_users.exists()

def check_cms_models():
    """Vérifier les modèles CMS"""
    print(f"\n📊 ÉTAT DES MODÈLES CMS")
    print("=" * 50)
    
    # SiteSetting
    site_settings_count = SiteSetting.objects.count()
    print(f"⚙️ SiteSetting: {site_settings_count}")

    if site_settings_count == 0:
        print("   ⚠️ Aucun SiteSetting trouvé - Création...")
        create_default_site_settings()
    else:
        settings_obj = SiteSetting.objects.first()
        print(f"   • Nom du site: {settings_obj.site_title}")
        print(f"   • Email: {settings_obj.contact_email}")

    # Media
    media_count = Media.objects.count()
    print(f"🖼️ Media: {media_count}")
    
    if media_count == 0:
        print("   ⚠️ Aucun média trouvé - Création d'exemples...")
        create_sample_media()

def create_default_site_settings():
    """Créer les paramètres de site par défaut"""
    try:
        site_settings = SiteSetting.objects.create(
            site_title="JosNet E-commerce",
            site_description="Plateforme e-commerce moderne",
            contact_email="<EMAIL>",
            contact_phone="+257 12 34 56 78",
            contact_address="Bujumbura, Burundi",
            facebook_url="https://facebook.com/josnet",
            twitter_url="https://twitter.com/josnet",
            instagram_url="https://instagram.com/josnet",
            linkedin_url="https://linkedin.com/company/josnet"
        )
        print(f"   ✅ SiteSetting créé: {site_settings.site_title}")
        return site_settings
    except Exception as e:
        print(f"   ❌ Erreur lors de la création: {e}")
        return None

def create_sample_media():
    """Créer des médias d'exemple"""
    try:
        # User déjà importé en haut

        # Obtenir un utilisateur admin
        admin_user = User.objects.filter(is_staff=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                email='<EMAIL>',
                password='admin123',
                is_staff=True,
                is_superuser=True,
                role='admin'
            )

        # Logo (sans fichier réel pour l'instant)
        logo = Media.objects.create(
            title="Logo JosNet",
            description="Logo principal de la plateforme",
            file_type="image",
            alt_text="Logo JosNet",
            uploader=admin_user
        )

        # Banner
        banner = Media.objects.create(
            title="Banner Accueil",
            description="Banner principal de la page d'accueil",
            file_type="image",
            alt_text="Banner Accueil",
            uploader=admin_user
        )

        print(f"   ✅ Médias créés: {Media.objects.count()}")
        return True

    except Exception as e:
        print(f"   ❌ Erreur lors de la création des médias: {e}")
        return False

def check_cms_permissions():
    """Vérifier les permissions CMS"""
    print(f"\n🔑 VÉRIFICATION DES PERMISSIONS")
    print("=" * 50)
    
    from django.contrib.auth.models import Permission
    from django.contrib.contenttypes.models import ContentType
    
    # Permissions CMS
    cms_permissions = [
        'cms.add_sitesetting',
        'cms.change_sitesetting',
        'cms.view_sitesetting',
        'cms.add_media',
        'cms.change_media',
        'cms.view_media',
    ]
    
    admin_users = User.objects.filter(is_staff=True, is_superuser=True)
    
    for user in admin_users:
        print(f"👤 {user.email}:")
        
        for perm_code in cms_permissions:
            try:
                app_label, codename = perm_code.split('.')
                permission = Permission.objects.get(
                    content_type__app_label=app_label,
                    codename=codename
                )
                
                has_perm = user.has_perm(perm_code)
                status = "✅" if has_perm else "❌"
                print(f"   {status} {perm_code}")
                
            except Permission.DoesNotExist:
                print(f"   ⚠️ Permission non trouvée: {perm_code}")

def test_cms_api():
    """Tester l'API CMS"""
    print(f"\n🌐 TEST DE L'API CMS")
    print("=" * 50)
    
    try:
        import requests
        
        # Obtenir un token admin
        admin_user = User.objects.filter(is_staff=True, is_superuser=True).first()
        if not admin_user:
            print("❌ Aucun utilisateur admin trouvé")
            return False
            
        from rest_framework.authtoken.models import Token as AuthToken
        token, created = AuthToken.objects.get_or_create(user=admin_user)
        
        # Tester l'endpoint des paramètres
        headers = {'Authorization': f'Token {token.key}'}
        
        response = requests.get(
            'http://localhost:8000/api/v1/cms/settings/current/',
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ API CMS accessible")
            data = response.json()
            print(f"   Site: {data.get('site_title', 'N/A')}")
            return True
        else:
            print(f"❌ API CMS erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️ Serveur Django non démarré")
        return False
    except Exception as e:
        print(f"❌ Erreur API: {e}")
        return False

def fix_cms_issues():
    """Corriger les problèmes CMS"""
    print(f"\n🔧 CORRECTION DES PROBLÈMES CMS")
    print("=" * 50)
    
    # 1. Vérifier/créer les utilisateurs admin
    admin_exists = check_cms_authentication()
    
    if not admin_exists:
        print("   Création d'un utilisateur admin...")
        admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='admin123'
        )
        from rest_framework.authtoken.models import Token as AuthToken
        AuthToken.objects.create(user=admin_user)
        print("   ✅ Utilisateur admin créé")
    
    # 2. Vérifier/créer les données CMS
    check_cms_models()
    
    # 3. Vérifier les permissions
    check_cms_permissions()
    
    # 4. Tester l'API
    api_ok = test_cms_api()
    
    return api_ok

def show_frontend_fix():
    """Afficher comment corriger le frontend"""
    print(f"\n💻 CORRECTION DU FRONTEND")
    print("=" * 50)
    
    print("1. 🔑 AUTHENTIFICATION:")
    print("   • Le frontend doit envoyer un token d'authentification")
    print("   • Vérifiez que l'AuthContext fournit le token")
    print("   • L'API CMS nécessite un utilisateur admin")
    
    print("\n2. 📡 CONFIGURATION API:")
    print("   • Vérifiez src/services/api.ts")
    print("   • Le token doit être dans les headers: Authorization: Token <token>")
    print("   • Endpoint: http://localhost:8000/api/v1/cms/")
    
    print("\n3. 🔄 FALLBACK:")
    print("   • Le système utilise des données de fallback si l'API échoue")
    print("   • C'est normal en développement")
    print("   • Les données de fallback sont dans les composants")

def main():
    """Fonction principale"""
    print("🔧 DIAGNOSTIC ET CORRECTION CMS")
    print("=" * 60)
    
    # Diagnostic complet
    api_ok = fix_cms_issues()
    
    if api_ok:
        print(f"\n🎉 CMS OPÉRATIONNEL!")
        print(f"\n📋 PROCHAINES ÉTAPES:")
        print(f"   1. Redémarrez le serveur Django")
        print(f"   2. Connectez-vous en tant qu'admin dans le frontend")
        print(f"   3. L'API CMS devrait maintenant fonctionner")
    else:
        print(f"\n⚠️ PROBLÈMES DÉTECTÉS")
        show_frontend_fix()
    
    print(f"\n🔗 LIENS UTILES:")
    print(f"   • Admin Django: http://localhost:8000/admin/")
    print(f"   • API CMS: http://localhost:8000/api/v1/cms/")
    print(f"   • Frontend: http://localhost:3000/")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
