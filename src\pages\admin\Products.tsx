
import AdminLayout from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Package,
  Search,
  Plus,
  Edit,
  Trash2,
  Filter,
  ChevronDown,
  RefreshCw,
  Eye
} from "lucide-react";
import React, { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import { productApi, ProductListItem } from "@/services/productApi";
import { Skeleton } from "@/components/ui/skeleton";
import ProductImage from "@/components/products/ProductImage";
import { formatCurrency } from "@/utils/formatters";
import { Link } from "react-router-dom";

const Products = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;

  // Utiliser la MÊME API que ProductDetail et FeaturedProducts
  const {
    data: productsResponse,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['admin-products', currentPage, searchQuery],
    queryFn: () => productApi.getProducts({
      page: currentPage,
      page_size: pageSize,
      search: searchQuery || undefined,
      ordering: '-created_at' // Plus récents en premier
    }),
    staleTime: 30000, // 30 secondes
  });

  const products = productsResponse?.results || [];
  const totalProducts = productsResponse?.count || 0;

  // Debug logging
  React.useEffect(() => {
    if (products.length > 0) {
      console.group('🔧 Admin Products Debug');
      console.log('Total products:', products.length);
      console.log('First product:', products[0]);
      console.log('First product primary_image:', products[0].primary_image);
      console.log('First product images:', products[0].images);
      console.log('First product primary_image URL:', products[0].primary_image?.image);

      // Test de l'URL
      const imageUrl = products[0].primary_image?.image;
      if (imageUrl) {
        const fullUrl = imageUrl.startsWith('http')
          ? imageUrl
          : `http://localhost:8000${imageUrl}`;
        console.log('Full image URL:', fullUrl);

        // Test d'accessibilité
        fetch(fullUrl, { method: 'HEAD' })
          .then(response => {
            console.log(`${response.ok ? '✅' : '❌'} Image accessible: ${fullUrl} (${response.status})`);
          })
          .catch(error => {
            console.log(`❌ Image error: ${fullUrl} - ${error.message}`);
          });
      } else {
        console.log('❌ Pas d\'image primary_image trouvée');
      }
      console.groupEnd();
    }
  }, [products]);

  // Fonction de filtrage locale (en plus du filtrage serveur)
  const filteredProducts = useMemo(() => {
    if (!searchQuery) return products;

    return products.filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.categories && product.categories.some(cat =>
        cat.name.toLowerCase().includes(searchQuery.toLowerCase())
      ))
    );
  }, [products, searchQuery]);

  // Fonction pour déterminer le statut du stock
  const getStockStatus = (product: ProductListItem) => {
    if (!product.in_stock) return { text: "Rupture", class: "bg-red-100 text-red-800" };

    // Si on a des infos d'inventaire
    if (product.inventory) {
      const qty = product.inventory.available_quantity || 0;
      if (qty === 0) return { text: "Rupture", class: "bg-red-100 text-red-800" };
      if (qty <= 5) return { text: "Stock faible", class: "bg-yellow-100 text-yellow-800" };
      return { text: "En stock", class: "bg-green-100 text-green-800" };
    }

    return { text: "En stock", class: "bg-green-100 text-green-800" };
  };

  const handleEditProduct = (product: ProductListItem) => {
    toast({
      title: "Modification demandée",
      description: `Édition du produit ${product.name} initiée`,
    });
    // TODO: Rediriger vers le formulaire d'édition
    // navigate(`/admin/products/edit/${product.slug}`);
  };

  const handleDeleteProduct = (product: ProductListItem) => {
    toast({
      title: "Suppression demandée",
      description: `Suppression du produit ${product.name} initiée`,
      variant: "destructive",
    });
    // TODO: Afficher une confirmation et supprimer
  };

  const handleAddProduct = () => {
    toast({
      title: "Nouveau produit",
      description: "Formulaire de création de produit ouvert",
    });
    // TODO: Rediriger vers le formulaire de création
    // navigate('/admin/products/new');
  };

  const handleViewProduct = (product: ProductListItem) => {
    // Ouvrir la page de détail du produit
    window.open(`/product/${product.id}`, '_blank');
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Produits</h1>
          <p className="text-gray-500">Gérez votre catalogue de produits JOSNET</p>
        </div>

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher un produit..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => refetch()}
              disabled={isLoading}
              className="flex items-center gap-1"
            >
              <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
              Actualiser
            </Button>
            <Button variant="outline" className="flex items-center gap-1">
              <Filter size={16} />
              Filtrer
              <ChevronDown size={14} />
            </Button>
            <Button onClick={handleAddProduct} className="flex items-center gap-1">
              <Plus size={16} />
              Nouveau Produit
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Produits</p>
                <p className="text-2xl font-bold">{totalProducts}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <div className="h-4 w-4 bg-green-500 rounded-full"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">En Stock</p>
                <p className="text-2xl font-bold">{products.filter(p => p.in_stock).length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <div className="h-4 w-4 bg-yellow-500 rounded-full"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Stock Faible</p>
                <p className="text-2xl font-bold">
                  {products.filter(p => {
                    const status = getStockStatus(p);
                    return status.text === "Stock faible";
                  }).length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
                <div className="h-4 w-4 bg-red-500 rounded-full"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Rupture</p>
                <p className="text-2xl font-bold">
                  {products.filter(p => !p.in_stock).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tableau des produits */}
        <div className="border rounded-lg bg-white">
          {isLoading ? (
            <div className="p-6">
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-16 w-16 rounded" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-1/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                ))}
              </div>
            </div>
          ) : isError ? (
            <div className="p-6 text-center">
              <div className="text-red-500 mb-4">
                <Package className="mx-auto h-12 w-12" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur de chargement</h3>
              <p className="text-gray-600 mb-4">
                {error?.message || "Impossible de charger les produits."}
              </p>
              <Button onClick={() => refetch()} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Réessayer
              </Button>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="p-6 text-center">
              <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun produit trouvé</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery
                  ? `Aucun produit ne correspond à "${searchQuery}".`
                  : "Aucun produit disponible pour le moment."
                }
              </p>
              {!searchQuery && (
                <Button onClick={handleAddProduct}>
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un produit
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Image</TableHead>
                  <TableHead>Produit</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead className="text-right">Prix</TableHead>
                  <TableHead className="text-right">Stock</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product) => {
                  const stockStatus = getStockStatus(product);
                  return (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="h-16 w-16 rounded border overflow-hidden bg-gray-50">
                          <ProductImage
                            product={product}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {product.short_description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                      <TableCell>
                        {product.categories && product.categories.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {product.categories.slice(0, 2).map((cat) => (
                              <span
                                key={cat.id}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800"
                              >
                                {cat.name}
                              </span>
                            ))}
                            {product.categories.length > 2 && (
                              <span className="text-xs text-gray-500">
                                +{product.categories.length - 2}
                              </span>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">Non catégorisé</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div>
                          <div className="font-medium">
                            {formatCurrency(Number(product.current_price))}
                          </div>
                          {product.is_on_sale && product.sale_price && (
                            <div className="text-sm text-gray-500 line-through">
                              {formatCurrency(Number(product.price))}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {product.inventory ? (
                          <div>
                            <div className="font-medium">
                              {product.inventory.available_quantity || 0}
                            </div>
                            {product.inventory.reserved_quantity > 0 && (
                              <div className="text-sm text-yellow-600">
                                {product.inventory.reserved_quantity} réservé(s)
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">N/A</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${stockStatus.class}`}>
                          {stockStatus.text}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewProduct(product)}
                            title="Voir le produit"
                          >
                            <Eye size={16} className="text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditProduct(product)}
                            title="Modifier"
                          >
                            <Edit size={16} className="text-blue-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteProduct(product)}
                            title="Supprimer"
                          >
                            <Trash2 size={16} className="text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </div>

        {/* Pagination et informations */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Affichage de {filteredProducts.length} produits sur {totalProducts}
            {searchQuery && ` (filtré par "${searchQuery}")`}
          </div>

          {/* TODO: Ajouter la pagination */}
          {totalProducts > pageSize && (
            <div className="text-sm text-gray-500">
              Page {currentPage} sur {Math.ceil(totalProducts / pageSize)}
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default Products;
