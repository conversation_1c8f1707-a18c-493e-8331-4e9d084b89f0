#!/usr/bin/env python
"""
Test de l'email de bienvenue automatique lors de l'inscription.
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.conf import settings
from authentication.utils import send_welcome_email

User = get_user_model()


def test_welcome_email_manual():
    """Test manuel de l'email de bienvenue."""
    print("📧 TEST MANUEL DE L'EMAIL DE BIENVENUE")
    print("=" * 60)
    
    # Créer un utilisateur de test
    test_email = "<EMAIL>"
    
    # Supprimer l'utilisateur s'il existe déjà
    User.objects.filter(email=test_email).delete()
    
    # Créer un nouvel utilisateur
    user = User.objects.create(
        email=test_email,
        first_name="<PERSON>",
        last_name="Asifiwe",
        password="testpass123"
    )
    user.set_password("testpass123")
    user.save()
    
    print(f"✅ Utilisateur créé: {user.email}")
    print(f"   Nom complet: {user.get_full_name()}")
    
    # Configuration email actuelle
    print(f"\n📧 Configuration email:")
    print(f"   Backend: {settings.EMAIL_BACKEND}")
    print(f"   Host: {settings.EMAIL_HOST}")
    print(f"   Port: {settings.EMAIL_PORT}")
    print(f"   TLS: {settings.EMAIL_USE_TLS}")
    print(f"   User: {settings.EMAIL_HOST_USER}")
    print(f"   From: {getattr(settings, 'DEFAULT_FROM_EMAIL', 'Non configuré')}")
    
    # Envoyer l'email de bienvenue manuellement
    print(f"\n📤 Envoi de l'email de bienvenue à {test_email}...")
    
    try:
        success = send_welcome_email(user)
        
        if success:
            print(f"✅ Email de bienvenue envoyé avec succès !")
            print(f"📬 Vérifiez la boîte email {test_email}")
            print(f"📧 Sujet: 🎉 Bienvenue chez JosNet Network, {user.get_full_name()} !")
            return True
        else:
            print(f"❌ Échec de l'envoi de l'email")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_welcome_email_automatic():
    """Test de l'envoi automatique via signal."""
    print("\n🔄 TEST DE L'ENVOI AUTOMATIQUE VIA SIGNAL")
    print("=" * 60)
    
    test_email = "<EMAIL>"
    
    # Supprimer l'utilisateur s'il existe déjà
    User.objects.filter(email=test_email).delete()
    
    print(f"🔄 Création d'un nouvel utilisateur (déclenchement automatique)...")
    
    try:
        # Créer un utilisateur - cela devrait déclencher automatiquement l'email
        user = User.objects.create(
            email=test_email,
            first_name="Test",
            last_name="Welcome",
            password="testpass123"
        )
        user.set_password("testpass123")
        user.save()
        
        print(f"✅ Utilisateur créé: {user.email}")
        print(f"✅ Signal déclenché automatiquement")
        print(f"📧 Email de bienvenue envoyé automatiquement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création automatique: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_registration_flow():
    """Test du flux d'inscription complet."""
    print("\n🚀 TEST DU FLUX D'INSCRIPTION COMPLET")
    print("=" * 60)
    
    from rest_framework.test import APIClient
    
    client = APIClient()
    
    # Données d'inscription
    registration_data = {
        'first_name': 'Nouveau',
        'last_name': 'Utilisateur',
        'email': '<EMAIL>',
        'password': 'testpass123',
        'confirm_password': 'testpass123'
    }
    
    print(f"📝 Données d'inscription:")
    print(f"   Email: {registration_data['email']}")
    print(f"   Nom: {registration_data['first_name']} {registration_data['last_name']}")
    
    # Supprimer l'utilisateur s'il existe déjà
    User.objects.filter(email=registration_data['email']).delete()
    
    try:
        print(f"\n🔄 Envoi de la requête d'inscription...")
        
        response = client.post('/api/v1/auth/register/', registration_data, format='json')
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Inscription réussie !")
            print(f"   Message: {data.get('message', 'Aucun message')}")
            print(f"   Utilisateur: {data.get('user', {}).get('email', 'Non trouvé')}")
            print(f"📧 Email de bienvenue envoyé automatiquement lors de l'inscription")
            return True
        else:
            print(f"❌ Échec de l'inscription: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test d'inscription: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Fonction principale."""
    print("🚀 TEST COMPLET DE L'EMAIL DE BIENVENUE")
    print("=" * 80)
    print("Ce test vérifie que les emails de bienvenue sont envoyés")
    print("automatiquement lors de l'inscription des utilisateurs.")
    print("=" * 80)
    
    results = []
    
    try:
        # Test 1: Email manuel
        print("🧪 PHASE 1: Test manuel de l'email de bienvenue")
        manual_success = test_welcome_email_manual()
        results.append(("Email de bienvenue manuel", manual_success))
        
        # Test 2: Email automatique via signal
        print("\n🧪 PHASE 2: Test automatique via signal")
        auto_success = test_welcome_email_automatic()
        results.append(("Email automatique via signal", auto_success))
        
        # Test 3: Flux d'inscription complet
        print("\n🧪 PHASE 3: Test du flux d'inscription complet")
        registration_success = test_registration_flow()
        results.append(("Flux d'inscription complet", registration_success))
        
        # Résumé
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ DES TESTS D'EMAIL DE BIENVENUE")
        print("=" * 80)
        
        passed = 0
        failed = 0
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print(f"\nTotal: {len(results)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print(f"\n🎉 TOUS LES TESTS SONT PASSÉS !")
            print(f"✅ Les emails de bienvenue sont envoyés automatiquement")
            print(f"✅ Le système fonctionne parfaitement")
            print(f"✅ Vérifiez votre boî<NAME_EMAIL>")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué")
            print(f"🔍 Vérifiez la configuration SMTP")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DES TESTS: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
