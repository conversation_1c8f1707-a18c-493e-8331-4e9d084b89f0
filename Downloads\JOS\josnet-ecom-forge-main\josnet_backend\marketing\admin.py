from django.contrib import admin
from .models import Promotion, Campaign

@admin.register(Promotion)
class PromotionAdmin(admin.ModelAdmin):
    list_display = ('name', 'discount_type', 'discount_value', 'applies_to', 'target_name', 'start_date', 'end_date', 'is_active')
    list_filter = ('discount_type', 'applies_to', 'is_active')
    search_fields = ('name', 'description', 'discount_code')
    date_hierarchy = 'start_date'

@admin.register(Campaign)
class CampaignAdmin(admin.ModelAdmin):
    list_display = ('name', 'discount_percentage', 'discount_amount', 'start_date', 'end_date', 'is_active', 'product_count')
    list_filter = ('is_active',)
    search_fields = ('name', 'description', 'discount_code')
    date_hierarchy = 'start_date'
    filter_horizontal = ('products',)
