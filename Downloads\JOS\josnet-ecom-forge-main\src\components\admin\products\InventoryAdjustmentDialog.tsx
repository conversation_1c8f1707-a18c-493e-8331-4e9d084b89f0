import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Minus, Loader2 } from 'lucide-react';
import { productApi } from '@/services/productApi';
import { useToast } from '@/hooks/use-toast';

interface InventoryAdjustmentDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  inventoryId: number | null;
  onSuccess: () => void;
}

const InventoryAdjustmentDialog: React.FC<InventoryAdjustmentDialogProps> = ({
  isOpen,
  onOpenChange,
  inventoryId,
  onSuccess
}) => {
  const { toast } = useToast();
  const [quantity, setQuantity] = useState('0');
  const [reason, setReason] = useState('adjustment');
  const [reference, setReference] = useState('');
  const [notes, setNotes] = useState('');
  
  // Fetch inventory data
  const { 
    data: inventory,
    isLoading: isLoadingInventory
  } = useQuery({
    queryKey: ['inventory', inventoryId],
    queryFn: () => inventoryId ? productApi.getInventory(inventoryId) : null,
    enabled: !!inventoryId && isOpen,
  });
  
  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setQuantity('0');
      setReason('adjustment');
      setReference('');
      setNotes('');
    }
  }, [isOpen]);
  
  // Adjust inventory mutation
  const adjustInventoryMutation = useMutation({
    mutationFn: (data: any) => productApi.adjustInventory(inventoryId!, data),
    onSuccess: () => {
      toast({
        title: 'Stock ajusté',
        description: 'Le stock a été ajusté avec succès.',
      });
      onSuccess();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de l\'ajustement du stock.',
        variant: 'destructive',
      });
    },
  });
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inventoryId) return;
    
    adjustInventoryMutation.mutate({
      quantity: parseInt(quantity),
      reason,
      reference: reference || undefined,
      notes: notes || undefined
    });
  };
  
  const incrementQuantity = () => {
    setQuantity((parseInt(quantity) + 1).toString());
  };
  
  const decrementQuantity = () => {
    setQuantity((parseInt(quantity) - 1).toString());
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Ajuster le Stock</DialogTitle>
          <DialogDescription>
            Ajoutez ou retirez du stock. Utilisez des nombres positifs pour ajouter et négatifs pour retirer.
          </DialogDescription>
        </DialogHeader>
        
        {isLoadingInventory ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : inventory ? (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="bg-gray-50 p-4 rounded-md mb-4">
                <h3 className="font-medium mb-2">Informations sur le produit</h3>
                <p className="text-sm">
                  <span className="font-medium">Produit:</span> {inventory.product_name}
                  {inventory.variant_name && ` - ${inventory.variant_name}`}
                </p>
                <p className="text-sm">
                  <span className="font-medium">SKU:</span> {inventory.sku}
                </p>
                <p className="text-sm">
                  <span className="font-medium">Stock actuel:</span> {inventory.quantity} unités
                </p>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="quantity" className="text-right">
                  Quantité
                </Label>
                <div className="col-span-3 flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={decrementQuantity}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <Input
                    id="quantity"
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(e.target.value)}
                    className="text-center"
                    required
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={incrementQuantity}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="reason" className="text-right">
                  Raison
                </Label>
                <Select value={reason} onValueChange={setReason}>
                  <SelectTrigger id="reason" className="col-span-3">
                    <SelectValue placeholder="Sélectionner une raison" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="purchase">Achat</SelectItem>
                    <SelectItem value="sale">Vente</SelectItem>
                    <SelectItem value="return">Retour</SelectItem>
                    <SelectItem value="adjustment">Ajustement</SelectItem>
                    <SelectItem value="loss">Perte/Dommage</SelectItem>
                    <SelectItem value="transfer">Transfert</SelectItem>
                    <SelectItem value="other">Autre</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="reference" className="text-right">
                  Référence
                </Label>
                <Input
                  id="reference"
                  value={reference}
                  onChange={(e) => setReference(e.target.value)}
                  className="col-span-3"
                  placeholder="ex: Commande #12345"
                />
              </div>
              
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="notes" className="text-right pt-2">
                  Notes
                </Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="col-span-3"
                  placeholder="Informations supplémentaires"
                  rows={3}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Annuler
              </Button>
              <Button 
                type="submit" 
                disabled={adjustInventoryMutation.isPending || quantity === '0'}
              >
                {adjustInventoryMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Ajustement...
                  </>
                ) : (
                  'Ajuster le Stock'
                )}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="text-center py-8 text-red-500">
            Impossible de charger les données d'inventaire
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default InventoryAdjustmentDialog;
