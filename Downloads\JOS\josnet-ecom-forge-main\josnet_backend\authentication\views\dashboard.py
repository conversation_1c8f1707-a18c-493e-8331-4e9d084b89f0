from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import timedelta

from authentication.models import User
from orders.models import Order
from payments.models import Invoice

class DashboardStatsView(APIView):
    """
    View to get dashboard statistics for the authenticated user.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        
        # Get order statistics
        orders_count = Order.objects.filter(user=user).count()
        pending_orders_count = Order.objects.filter(
            user=user, 
            status__in=['pending', 'processing', 'shipped']
        ).count()
        
        # Get returns count
        returns_count = Order.objects.filter(
            user=user,
            status__in=['refunded', 'partially_refunded']
        ).count()
        
        # Get loyalty points
        loyalty_points = user.loyalty_points
        
        # Get most recent order
        recent_order = Order.objects.filter(user=user).order_by('-created_at').first()
        
        # Prepare response data
        response_data = {
            'orders_count': orders_count,
            'pending_orders_count': pending_orders_count,
            'returns_count': returns_count,
            'loyalty_points': loyalty_points,
        }
        
        # Add recent order details if available
        if recent_order:
            response_data['recent_order'] = {
                'id': str(recent_order.id),
                'order_number': recent_order.order_number,
                'date': recent_order.created_at.strftime('%d/%m/%Y'),
                'status': recent_order.status,
                'status_display': dict(Order.STATUS_CHOICES).get(recent_order.status, recent_order.status),
                'total': f"{recent_order.total} €"
            }
        
        return Response(response_data)
