import React, { useState, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { 
  DndContext, 
  closestCenter, 
  KeyboardSensor, 
  PointerSensor, 
  useSensor, 
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { 
  Image as ImageIcon, 
  Upload, 
  X, 
  Star, 
  GripVertical, 
  Trash2, 
  AlertCircle 
} from 'lucide-react';
import { productApi, ProductImage } from '@/services/productApi';

interface SortableImageItemProps {
  image: ProductImage;
  onSetPrimary: (imageId: number) => void;
  onDelete: (imageId: number) => void;
}

const SortableImageItem: React.FC<SortableImageItemProps> = ({ 
  image, 
  onSetPrimary, 
  onDelete 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: image.id.toString() });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      className="flex items-center gap-2 p-2 bg-white border rounded-md shadow-sm"
    >
      <div 
        className="cursor-move" 
        {...attributes} 
        {...listeners}
      >
        <GripVertical className="h-5 w-5 text-gray-400" />
      </div>
      
      <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border">
        {image.image_url ? (
          <img 
            src={image.image_url} 
            alt={image.alt_text || 'Product image'} 
            className="h-full w-full object-cover object-center"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-gray-100">
            <ImageIcon className="h-6 w-6 text-gray-400" />
          </div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {image.alt_text || 'No description'}
        </p>
        <p className="text-xs text-gray-500 truncate">
          ID: {image.id}
        </p>
      </div>
      
      <div className="flex gap-1">
        <Button 
          variant={image.is_primary ? "default" : "outline"} 
          size="icon" 
          onClick={() => onSetPrimary(image.id)}
          title={image.is_primary ? "Primary image" : "Set as primary"}
        >
          <Star className={`h-4 w-4 ${image.is_primary ? 'text-yellow-300 fill-yellow-300' : 'text-gray-400'}`} />
        </Button>
        
        <Button 
          variant="outline" 
          size="icon" 
          onClick={() => onDelete(image.id)}
          className="text-red-500 hover:text-red-700 hover:bg-red-50"
          title="Delete image"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

interface ProductImageManagerProps {
  productId: number;
}

const ProductImageManager: React.FC<ProductImageManagerProps> = ({ productId }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [altText, setAltText] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  // Fetch product images
  const { 
    data: images = [], 
    isLoading, 
    isError 
  } = useQuery({
    queryKey: ['productImages', productId],
    queryFn: () => productApi.getProductImages(productId),
  });
  
  // Upload single image mutation
  const uploadImageMutation = useMutation({
    mutationFn: (data: { productId: number, image: File, altText?: string }) => 
      productApi.uploadProductImage(data.productId, data.image, data.altText),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productImages', productId] });
      toast({
        title: 'Image uploaded',
        description: 'The image has been uploaded successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Upload failed',
        description: error.message || 'Failed to upload image.',
        variant: 'destructive',
      });
    },
  });
  
  // Upload multiple images mutation
  const uploadMultipleImagesMutation = useMutation({
    mutationFn: (data: { productId: number, images: File[], altText?: string }) => 
      productApi.uploadMultipleProductImages(data.productId, data.images, data.altText),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productImages', productId] });
      toast({
        title: 'Images uploaded',
        description: `${files.length} images have been uploaded successfully.`,
      });
      setFiles([]);
      setAltText('');
    },
    onError: (error: any) => {
      toast({
        title: 'Upload failed',
        description: error.message || 'Failed to upload images.',
        variant: 'destructive',
      });
    },
  });
  
  // Set primary image mutation
  const setPrimaryImageMutation = useMutation({
    mutationFn: (imageId: number) => productApi.setPrimaryProductImage(imageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productImages', productId] });
      toast({
        title: 'Primary image set',
        description: 'The primary image has been updated.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to set primary image',
        description: error.message || 'An error occurred.',
        variant: 'destructive',
      });
    },
  });
  
  // Delete image mutation
  const deleteImageMutation = useMutation({
    mutationFn: (imageId: number) => productApi.deleteProductImage(imageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productImages', productId] });
      toast({
        title: 'Image deleted',
        description: 'The image has been deleted successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Delete failed',
        description: error.message || 'Failed to delete image.',
        variant: 'destructive',
      });
    },
  });
  
  // Reorder images mutation
  const reorderImagesMutation = useMutation({
    mutationFn: (data: { productId: number, imageIds: number[] }) => 
      productApi.reorderProductImages(data.productId, data.imageIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productImages', productId] });
      toast({
        title: 'Images reordered',
        description: 'The image order has been updated.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Reorder failed',
        description: error.message || 'Failed to reorder images.',
        variant: 'destructive',
      });
    },
  });
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const fileArray = Array.from(e.target.files);
      setFiles(fileArray);
    }
  };
  
  // Handle file upload
  const handleUpload = async () => {
    if (files.length === 0) {
      toast({
        title: 'No files selected',
        description: 'Please select at least one image to upload.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsUploading(true);
    
    try {
      if (files.length === 1) {
        await uploadImageMutation.mutateAsync({
          productId,
          image: files[0],
          altText: altText || undefined
        });
      } else {
        await uploadMultipleImagesMutation.mutateAsync({
          productId,
          images: files,
          altText: altText || undefined
        });
      }
      
      // Reset form
      setFiles([]);
      setAltText('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } finally {
      setIsUploading(false);
    }
  };
  
  // Handle drag end for reordering
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const oldIndex = images.findIndex(img => img.id.toString() === active.id);
      const newIndex = images.findIndex(img => img.id.toString() === over.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        const newImages = arrayMove(images, oldIndex, newIndex);
        const imageIds = newImages.map(img => img.id);
        
        reorderImagesMutation.mutate({
          productId,
          imageIds
        });
      }
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Images</CardTitle>
        <CardDescription>
          Manage product images. Drag to reorder. The first image will be used as the primary image.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Upload section */}
        <div className="space-y-4">
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="product-images">Upload Images</Label>
            <Input
              id="product-images"
              type="file"
              accept="image/*"
              multiple
              onChange={handleFileChange}
              ref={fileInputRef}
            />
            <p className="text-sm text-muted-foreground">
              You can select multiple images. Max 5MB per image.
            </p>
          </div>
          
          {files.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="alt-text">Alt Text (optional)</Label>
              <Input
                id="alt-text"
                value={altText}
                onChange={(e) => setAltText(e.target.value)}
                placeholder="Describe the image for accessibility"
              />
              
              <div className="flex items-center gap-2">
                <Button 
                  onClick={handleUpload} 
                  disabled={isUploading}
                  className="gap-2"
                >
                  <Upload className="h-4 w-4" />
                  <span>Upload {files.length > 1 ? `${files.length} Images` : 'Image'}</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setFiles([]);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = '';
                    }
                  }}
                  disabled={isUploading}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </div>
        
        {/* Images list */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Product Images</h3>
          
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          ) : isError ? (
            <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
              <AlertCircle className="h-5 w-5" />
              <span>Failed to load images. Please try again.</span>
            </div>
          ) : images.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 bg-gray-50 border border-dashed rounded-md">
              <ImageIcon className="h-10 w-10 text-gray-400 mb-2" />
              <p className="text-sm text-gray-500">No images yet. Upload some images to get started.</p>
            </div>
          ) : (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={images.map(img => img.id.toString())}
                strategy={verticalListSortingStrategy}
              >
                <div className="space-y-2">
                  {images.map((image) => (
                    <SortableImageItem
                      key={image.id}
                      image={image}
                      onSetPrimary={(imageId) => setPrimaryImageMutation.mutate(imageId)}
                      onDelete={(imageId) => deleteImageMutation.mutate(imageId)}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductImageManager;
