import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Edit, Save, X, Refresh<PERSON><PERSON>, MessageSquare, Bookmark } from "lucide-react";
import messagingApi, { ResponseTemplate, Category } from "@/services/messagingApi";

const ResponseTemplates = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Template state
  const [isAddTemplateOpen, setIsAddTemplateOpen] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    name: "",
    content: "",
    category_id: undefined as number | undefined
  });
  const [editingTemplate, setEditingTemplate] = useState<ResponseTemplate | null>(null);

  // Fetch templates
  const {
    data: templates,
    isLoading: isLoadingTemplates,
    refetch: refetchTemplates
  } = useQuery({
    queryKey: ['responseTemplates'],
    queryFn: () => messagingApi.getResponseTemplates(),
  });

  // Fetch categories for dropdown
  const {
    data: categories
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => messagingApi.getCategories(),
  });

  // Create template mutation
  const createTemplateMutation = useMutation({
    mutationFn: (data: { name: string, content: string, category_id?: number }) =>
      messagingApi.createResponseTemplate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['responseTemplates'] });
      setIsAddTemplateOpen(false);
      setNewTemplate({
        name: "",
        content: "",
        category_id: undefined
      });
      toast({
        title: "Modèle créé",
        description: "Le modèle de réponse a été créé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création du modèle",
        variant: "destructive",
      });
      console.error("Error creating template:", error);
    }
  });

  // Update template mutation
  const updateTemplateMutation = useMutation({
    mutationFn: (data: { id: number, updates: { name?: string, content?: string, category_id?: number | null, is_active?: boolean } }) =>
      messagingApi.updateResponseTemplate(data.id, data.updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['responseTemplates'] });
      setEditingTemplate(null);
      toast({
        title: "Modèle mis à jour",
        description: "Le modèle de réponse a été mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour du modèle",
        variant: "destructive",
      });
      console.error("Error updating template:", error);
    }
  });

  // Handle template form submission
  const handleCreateTemplate = () => {
    if (!newTemplate.name.trim() || !newTemplate.content.trim()) {
      toast({
        title: "Erreur",
        description: "Le nom et le contenu du modèle sont requis",
        variant: "destructive",
      });
      return;
    }

    createTemplateMutation.mutate(newTemplate);
  };

  // Handle template update
  const handleUpdateTemplate = (template: ResponseTemplate) => {
    if (!template.name?.trim() || !template.content?.trim()) {
      toast({
        title: "Erreur",
        description: "Le nom et le contenu du modèle sont requis",
        variant: "destructive",
      });
      return;
    }

    updateTemplateMutation.mutate({
      id: template.id,
      updates: {
        name: template.name,
        content: template.content,
        category_id: template.category?.id || null
      }
    });
  };

  // Toggle template active state
  const toggleTemplateActive = (template: ResponseTemplate) => {
    updateTemplateMutation.mutate({
      id: template.id,
      updates: {
        is_active: !template.is_active
      }
    });
  };

  // Get category name by ID
  const getCategoryName = (categoryId: number | undefined | null) => {
    if (!categoryId) return "Aucune catégorie";
    const category = categories?.find(c => c.id === categoryId);
    return category ? category.name : "Aucune catégorie";
  };

  // Get category color by ID
  const getCategoryColor = (categoryId: number | undefined | null) => {
    if (!categoryId) return "#cccccc";
    const category = categories?.find(c => c.id === categoryId);
    return category ? category.color : "#cccccc";
  };

  return (
    <AdminLayout>
      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Modèles de réponse</h1>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => refetchTemplates()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualiser
            </Button>
            <Dialog open={isAddTemplateOpen} onOpenChange={setIsAddTemplateOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un modèle
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Ajouter un modèle de réponse</DialogTitle>
                  <DialogDescription>
                    Créez un nouveau modèle de réponse pour répondre rapidement aux messages.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="template-name">Nom</Label>
                    <Input
                      id="template-name"
                      placeholder="Nom du modèle"
                      value={newTemplate.name}
                      onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template-category">Catégorie (optionnel)</Label>
                    <Select
                      value={newTemplate.category_id?.toString() || "none"}
                      onValueChange={(value) => {
                        const categoryId = value === 'none' ? undefined : (value ? parseInt(value) : undefined);
                        setNewTemplate({ ...newTemplate, category_id: categoryId });
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Sélectionner une catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">Aucune catégorie</SelectItem>
                        {categories?.filter(c => c.is_active).map((category) => (
                          <SelectItem key={category.id} value={category.id.toString()}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template-content">Contenu</Label>
                    <Textarea
                      id="template-content"
                      placeholder="Contenu du modèle de réponse"
                      value={newTemplate.content}
                      onChange={(e) => setNewTemplate({ ...newTemplate, content: e.target.value })}
                      className="min-h-[200px]"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddTemplateOpen(false)}>Annuler</Button>
                  <Button
                    onClick={handleCreateTemplate}
                    disabled={createTemplateMutation.isPending}
                  >
                    {createTemplateMutation.isPending ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Créer
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {isLoadingTemplates ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : templates && templates.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {templates.filter(Boolean).map((template) => (
              <Card key={template.id} className={`${!template.is_active ? 'opacity-60' : ''}`}>
                {editingTemplate?.id === template.id ? (
                  // Edit mode - Now fully robust
                  <CardContent className="pt-6 space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor={`edit-template-name-${template.id}`}>Nom</Label>
                      <Input
                        id={`edit-template-name-${template.id}`}
                        value={editingTemplate?.name || ''}
                        onChange={(e) => editingTemplate && setEditingTemplate({ ...editingTemplate, name: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`edit-template-category-${template.id}`}>Catégorie</Label>
                      <Select
                        value={editingTemplate?.category?.id ? String(editingTemplate.category.id) : "none"}
                        onValueChange={(value) => {
                          if (editingTemplate) {
                            const categoryId = value === 'none' ? null : (value ? parseInt(value) : null);
                            const category = categoryId ? categories?.find(c => c.id === categoryId) || null : null;
                            setEditingTemplate({
                              ...editingTemplate,
                              category: category,
                            });
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une catégorie" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">Aucune catégorie</SelectItem>
                          {categories?.filter(Boolean).map((cat) => (
                            <SelectItem key={cat.id} value={String(cat.id)}>
                              {cat.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`edit-template-content-${template.id}`}>Contenu</Label>
                      <Textarea
                        id={`edit-template-content-${template.id}`}
                        value={editingTemplate?.content || ''}
                        onChange={(e) => editingTemplate && setEditingTemplate({ ...editingTemplate, content: e.target.value })}
                        className="min-h-[150px]"
                      />
                    </div>
                    <div className="flex justify-end gap-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingTemplate(null)}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Annuler
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => editingTemplate && handleUpdateTemplate(editingTemplate)}
                        disabled={updateTemplateMutation.isPending}
                      >
                        {updateTemplateMutation.isPending ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-1" />
                        )}
                        Enregistrer
                      </Button>
                    </div>
                  </CardContent>
                ) : (
                  // View mode - Now fully robust
                  <>
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-5 w-5 text-primary" />
                        <CardTitle>{template.name}</CardTitle>
                      </div>
                      {template.category && (
                        <div className="flex items-center gap-1 mt-1">
                          <Bookmark className="h-3 w-3" style={{ color: template.category.color || '#888888' }} />
                          <CardDescription>{template.category.name}</CardDescription>
                        </div>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="bg-gray-50 p-3 rounded-md text-sm whitespace-pre-wrap">
                        {template.content}
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={template.is_active}
                          onCheckedChange={() => toggleTemplateActive(template)}
                          disabled={updateTemplateMutation.isPending}
                        />
                        <span className="text-sm">
                          {template.is_active ? 'Actif' : 'Inactif'}
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingTemplate(template)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardFooter>
                  </>
                )}
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <p className="text-gray-500">Aucun modèle de réponse trouvé</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => setIsAddTemplateOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Ajouter un modèle
            </Button>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default ResponseTemplates;
