import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Zap,
  Plus,
  Edit,
  Trash2,
  Search,
  Tag,
  Clock,
  Star,
  Copy,
  Check
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface QuickResponse {
  id: number;
  name: string;
  content: string;
  category: string;
  tags: string[];
  usage_count: number;
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
}

interface QuickResponsesProps {
  onSelectResponse: (content: string) => void;
  className?: string;
}

const QuickResponses: React.FC<QuickResponsesProps> = ({
  onSelectResponse,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [copiedId, setCopiedId] = useState<number | null>(null);

  // Mock data - replace with actual API calls
  const quickResponses: QuickResponse[] = [
    {
      id: 1,
      name: "Salutation standard",
      content: "Bonjour,\n\nMerci de nous avoir contactés. Nous avons bien reçu votre message et nous vous répondrons dans les plus brefs délais.\n\nCordialement,\nL'équipe support",
      category: "salutations",
      tags: ["standard", "accueil"],
      usage_count: 45,
      is_favorite: true,
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z"
    },
    {
      id: 2,
      name: "Demande d'informations supplémentaires",
      content: "Bonjour,\n\nPour mieux vous aider, pourriez-vous nous fournir les informations suivantes :\n- Numéro de commande\n- Description détaillée du problème\n- Captures d'écran si applicable\n\nMerci de votre collaboration.\n\nCordialement,",
      category: "information",
      tags: ["demande", "détails"],
      usage_count: 32,
      is_favorite: false,
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z"
    },
    {
      id: 3,
      name: "Problème résolu",
      content: "Bonjour,\n\nNous sommes heureux de vous informer que votre problème a été résolu. Votre demande est maintenant traitée.\n\nSi vous avez d'autres questions, n'hésitez pas à nous contacter.\n\nCordialement,",
      category: "resolution",
      tags: ["résolu", "fermeture"],
      usage_count: 28,
      is_favorite: true,
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z"
    },
    {
      id: 4,
      name: "Remboursement en cours",
      content: "Bonjour,\n\nVotre demande de remboursement a été approuvée et est en cours de traitement. Vous devriez recevoir le montant sur votre compte dans 3-5 jours ouvrables.\n\nNuméro de référence : #REF{ORDER_ID}\n\nCordialement,",
      category: "remboursement",
      tags: ["remboursement", "approuvé"],
      usage_count: 15,
      is_favorite: false,
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z"
    },
    {
      id: 5,
      name: "Escalade vers technicien",
      content: "Bonjour,\n\nVotre demande nécessite l'intervention de notre équipe technique. Nous avons transféré votre dossier à un spécialiste qui vous contactera sous 24h.\n\nRéférence : #TECH{TICKET_ID}\n\nCordialement,",
      category: "escalade",
      tags: ["technique", "escalade"],
      usage_count: 12,
      is_favorite: false,
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z"
    }
  ];

  const categories = [
    { value: 'all', label: 'Toutes les catégories' },
    { value: 'salutations', label: 'Salutations' },
    { value: 'information', label: 'Demande d\'info' },
    { value: 'resolution', label: 'Résolution' },
    { value: 'remboursement', label: 'Remboursement' },
    { value: 'escalade', label: 'Escalade' }
  ];

  // Filter responses
  const filteredResponses = quickResponses.filter(response => {
    const matchesSearch = searchQuery === '' || 
      response.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      response.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      response.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || response.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Sort by favorites first, then by usage count
  const sortedResponses = [...filteredResponses].sort((a, b) => {
    if (a.is_favorite && !b.is_favorite) return -1;
    if (!a.is_favorite && b.is_favorite) return 1;
    return b.usage_count - a.usage_count;
  });

  const handleSelectResponse = (response: QuickResponse) => {
    onSelectResponse(response.content);
    setIsOpen(false);
    
    // Update usage count (in real app, this would be an API call)
    console.log(`Using template: ${response.name}`);
  };

  const handleCopyResponse = (response: QuickResponse) => {
    navigator.clipboard.writeText(response.content);
    setCopiedId(response.id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      salutations: 'bg-blue-100 text-blue-800',
      information: 'bg-orange-100 text-orange-800',
      resolution: 'bg-green-100 text-green-800',
      remboursement: 'bg-purple-100 text-purple-800',
      escalade: 'bg-red-100 text-red-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={className}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm">
            <Zap className="h-4 w-4 mr-2" />
            Réponses rapides
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-0" align="start">
          <div className="flex flex-col h-96">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold">Réponses rapides</h3>
                <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm" variant="outline">
                      <Plus className="h-3 w-3" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Nouvelle réponse rapide</DialogTitle>
                      <DialogDescription>
                        Créez un nouveau template de réponse pour gagner du temps
                      </DialogDescription>
                    </DialogHeader>
                    {/* Add form here */}
                    <div className="p-4 text-center text-gray-500">
                      Formulaire de création à implémenter
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {/* Search */}
              <div className="relative mb-3">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Category filter */}
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Responses List */}
            <div className="flex-1 overflow-y-auto">
              {sortedResponses.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <Zap className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p>Aucune réponse trouvée</p>
                </div>
              ) : (
                <AnimatePresence>
                  {sortedResponses.map((response, index) => (
                    <motion.div
                      key={response.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.05 }}
                      className="border-b hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="p-3">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium text-sm">{response.name}</h4>
                            {response.is_favorite && (
                              <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCopyResponse(response);
                              }}
                            >
                              {copiedId === response.id ? (
                                <Check className="h-3 w-3 text-green-500" />
                              ) : (
                                <Copy className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                        </div>

                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                          {response.content}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Badge className={getCategoryColor(response.category)} variant="secondary">
                              {response.category}
                            </Badge>
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <Clock className="h-3 w-3" />
                              <span>{response.usage_count} utilisations</span>
                            </div>
                          </div>
                        </div>

                        {/* Tags */}
                        {response.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {response.tags.map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}

                        {/* Action button */}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-full mt-2 text-xs"
                          onClick={() => handleSelectResponse(response)}
                        >
                          Utiliser ce template
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              )}
            </div>

            {/* Footer */}
            <div className="p-3 border-t bg-gray-50 text-center">
              <p className="text-xs text-gray-500">
                {sortedResponses.length} template{sortedResponses.length !== 1 ? 's' : ''} disponible{sortedResponses.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default QuickResponses;
