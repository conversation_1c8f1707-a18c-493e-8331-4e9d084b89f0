
import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAccountQuery } from "@/hooks/useApiQuery";
import AccountLayout from "@/components/account/AccountLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Star, Heart, ShoppingCart, Loader2, AlertCircle, RefreshCw, Plus, ChevronDown, ChevronUp } from "lucide-react";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { accountApi, ProductSuggestion, UpdateProductSuggestionRequest } from "@/services/accountApi";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ProductSuggestionForm from "@/components/account/ProductSuggestionForm";
import UserSuggestionsList from "@/components/account/UserSuggestionsList";
import EnhancedProductRecommendations from "@/components/account/EnhancedProductRecommendations";
import PurchaseBasedRecommendations from "@/components/account/PurchaseBasedRecommendations";
import RecommendationFeedback from "@/components/account/RecommendationFeedback";
import SuggestionStats from "@/components/account/SuggestionStats";
import SuggestionNotifications from "@/components/account/SuggestionNotifications";
import ErrorBoundary from "@/components/common/ErrorBoundary";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

const Suggestions = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeFilter, setActiveFilter] = useState("Tous");
  const [favorites, setFavorites] = useState<number[]>([]);
  const [showSuggestionForm, setShowSuggestionForm] = useState(false);
  const [editingSuggestion, setEditingSuggestion] = useState<ProductSuggestion | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch product categories avec fallback
  const {
    data: productCategories = [],
    isLoading: isLoadingCategories,
    isError: isErrorCategories,
    refetch: refetchCategories
  } = useAccountQuery(
    ['productCategories'],
    () => accountApi.getProductCategories(),
    ["Réseaux", "Sécurité", "Stockage", "Accessoires", "Serveurs", "Logiciels"]
  );

  // Fetch suggested products based on selected category avec fallback
  const {
    data: suggestedProducts = [],
    isLoading: isLoadingSuggestions,
    isError: isErrorSuggestions,
    refetch: refetchSuggestions
  } = useAccountQuery(
    ['suggestedProducts', activeFilter],
    () => accountApi.getSuggestedProducts(activeFilter !== "Tous" ? activeFilter : undefined),
    [] // Fallback vide, les données mock sont dans le service
  );

  // Fetch user product suggestions avec fallback
  const {
    data: userSuggestions = [],
    isLoading: isLoadingUserSuggestions,
    isError: isErrorUserSuggestions,
    refetch: refetchUserSuggestions
  } = useAccountQuery(
    ['userProductSuggestions'],
    () => accountApi.getUserProductSuggestions(),
    [] // Fallback vide, les données mock sont dans le service
  );

  // Mutation pour supprimer une suggestion
  const deleteMutation = useMutation({
    mutationFn: (id: number) => accountApi.deleteProductSuggestion(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userProductSuggestions'] });
      toast({
        description: "Suggestion supprimée avec succès",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la suppression de la suggestion",
        variant: "destructive",
      });
    }
  });

  // Mutation pour mettre à jour une suggestion
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: UpdateProductSuggestionRequest }) => 
      accountApi.updateProductSuggestion(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userProductSuggestions'] });
      setEditingSuggestion(null);
      setIsDialogOpen(false);
      toast({
        description: "Suggestion mise à jour avec succès",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de la suggestion",
        variant: "destructive",
      });
    }
  });

  // Utiliser les catégories récupérées de la base de données
  const allCategories = ["Tous", ...productCategories];
  
  // Les produits sont déjà filtrés par catégorie par l'API si activeFilter n'est pas "Tous"
  const filteredProducts = suggestedProducts;

  const addToFavorites = (productId: number) => {
    if (favorites.includes(productId)) {
      setFavorites(favorites.filter(id => id !== productId));
      toast({
        description: "Produit retiré des favoris",
      });
    } else {
      setFavorites([...favorites, productId]);
      toast({
        description: "Produit ajouté aux favoris",
      });
    }
  };

  const addToCart = (productName: string) => {
    toast({
      title: "Produit ajouté au panier",
      description: `${productName} a été ajouté à votre panier`,
    });
  };

  const handleRefresh = () => {
    refetchSuggestions();
    toast({
      title: "Rafraîchissement",
      description: "Liste des suggestions mise à jour",
    });
  };

  const handleSuggestionFormSuccess = () => {
    setShowSuggestionForm(false);
    refetchUserSuggestions();
    toast({
      title: "Suggestion envoyée",
      description: "Votre suggestion a été enregistrée avec succès",
    });
  };

  const handleEditSuggestion = (suggestion: ProductSuggestion) => {
    setEditingSuggestion(suggestion);
    setIsDialogOpen(true);
  };

  const handleUpdateSuggestion = (data: UpdateProductSuggestionRequest) => {
    if (editingSuggestion) {
      updateMutation.mutate({ id: editingSuggestion.id, data });
    }
  };

  const handleDeleteSuggestion = (id: number) => {
    deleteMutation.mutate(id);
  };

  return (
    <AccountLayout title="Suggestions personnalisées">
      {/* Section des notifications */}
      <div className="mb-6">
        <SuggestionNotifications />
      </div>

      <Tabs defaultValue="enhanced-recommendations" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="enhanced-recommendations">IA Avancée</TabsTrigger>
          <TabsTrigger value="purchase-based">Historique</TabsTrigger>
          <TabsTrigger value="recommendations">Classique</TabsTrigger>
          <TabsTrigger value="my-suggestions">Mes suggestions</TabsTrigger>
          <TabsTrigger value="stats">Statistiques</TabsTrigger>
        </TabsList>

        {/* Onglet des recommandations IA avancées */}
        <TabsContent value="enhanced-recommendations" className="space-y-6">
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Recommandations Intelligentes</h2>
              <p className="text-gray-600">
                Découvrez des produits sélectionnés par notre IA basée sur vos préférences et comportements d'achat
              </p>
            </div>

            <EnhancedProductRecommendations />

            {/* Section de feedback */}
            <div className="mt-8">
              <RecommendationFeedback
                onSubmit={(feedback) => {
                  console.log('Feedback reçu:', feedback);
                  toast({
                    title: "Merci pour votre feedback !",
                    description: "Vos commentaires nous aident à améliorer nos recommandations.",
                  });
                }}
              />
            </div>
          </div>
        </TabsContent>

        {/* Onglet des recommandations basées sur les achats */}
        <TabsContent value="purchase-based" className="space-y-6">
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Recommandations Personnalisées</h2>
              <p className="text-gray-600">
                Suggestions intelligentes basées sur votre historique d'achats et vos habitudes de consommation
              </p>
            </div>

            <PurchaseBasedRecommendations />
          </div>
        </TabsContent>

        {/* Onglet des recommandations */}
        <TabsContent value="recommendations" className="space-y-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h2 className="text-lg font-medium">Produits recommandés pour vous</h2>
              <p className="text-gray-500">Basés sur vos achats précédents et vos préférences</p>
            </div>
            <div className="flex flex-wrap gap-2 items-center">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="flex items-center gap-1"
              >
                <RefreshCw size={14} />
                Actualiser
              </Button>
              {allCategories.map((category) => (
                <Button
                  key={category}
                  variant={activeFilter === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveFilter(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {isLoadingSuggestions ? (
            <Card className="p-8 text-center">
              <CardContent className="flex flex-col items-center justify-center py-6">
                <Loader2 className="h-12 w-12 text-gray-400 mb-4 animate-spin" />
                <h3 className="text-lg font-medium mb-2">Chargement des suggestions...</h3>
                <p className="text-gray-500 max-w-sm">
                  Veuillez patienter pendant que nous récupérons vos suggestions personnalisées.
                </p>
              </CardContent>
            </Card>
          ) : isErrorSuggestions ? (
            <Card className="p-8 text-center">
              <CardContent className="flex flex-col items-center justify-center py-6">
                <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Une erreur est survenue lors du chargement de vos suggestions. Veuillez réessayer.
                </p>
                <Button variant="outline" onClick={handleRefresh}>
                  Réessayer
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProducts.map((product) => (
                <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardHeader className="p-0">
                    <AspectRatio ratio={4/3}>
                      <img
                        src={product.image}
                        alt={product.name}
                        className="object-cover w-full h-full"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder.svg';
                        }}
                      />
                    </AspectRatio>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <Link to={`/product/${product.id}`}>
                          <h3 className="font-medium hover:text-primary transition-colors">{product.name}</h3>
                        </Link>
                        <p className="text-gray-500 text-sm">{product.category}</p>
                      </div>
                      <button
                        onClick={() => addToFavorites(product.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <Heart
                          className={`h-5 w-5 ${favorites.includes(product.id) ? "fill-red-500 text-red-500" : ""}`}
                        />
                      </button>
                    </div>

                    <div className="mt-2 flex items-center">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <span className="ml-1 text-sm">{product.rating}</span>
                      </div>
                    </div>

                    <div className="mt-4 flex justify-between items-center">
                      <span className="font-bold text-lg">{product.price.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })}</span>
                      <Button
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() => addToCart(product.name)}
                      >
                        <ShoppingCart className="h-4 w-4" />
                        Ajouter
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {filteredProducts.length === 0 && (
                <Card className="p-8 text-center col-span-full">
                  <CardContent className="flex flex-col items-center justify-center py-6">
                    <h3 className="text-lg font-medium mb-2">Aucun produit trouvé</h3>
                    <p className="text-gray-500 max-w-sm">
                      Aucun produit ne correspond à la catégorie sélectionnée.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        {/* Onglet des suggestions de l'utilisateur */}
        <TabsContent value="my-suggestions" className="space-y-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div>
              <h2 className="text-lg font-medium">Mes suggestions de produits</h2>
              <p className="text-gray-500">Suggérez des produits que vous aimeriez voir dans notre catalogue</p>
            </div>
            <Button
              onClick={() => setShowSuggestionForm(!showSuggestionForm)}
              className="flex items-center gap-1"
            >
              {showSuggestionForm ? (
                <>
                  <ChevronUp size={16} />
                  Masquer le formulaire
                </>
              ) : (
                <>
                  <Plus size={16} />
                  Nouvelle suggestion
                </>
              )}
            </Button>
          </div>

          {/* Formulaire de suggestion */}
          {showSuggestionForm && (
            <div className="mb-8">
              <ProductSuggestionForm onSuccess={handleSuggestionFormSuccess} />
            </div>
          )}

          {/* Liste des suggestions de l'utilisateur */}
          {isLoadingUserSuggestions ? (
            <Card className="p-8 text-center">
              <CardContent className="flex flex-col items-center justify-center py-6">
                <Loader2 className="h-12 w-12 text-gray-400 mb-4 animate-spin" />
                <h3 className="text-lg font-medium mb-2">Chargement de vos suggestions...</h3>
                <p className="text-gray-500 max-w-sm">
                  Veuillez patienter pendant que nous récupérons vos suggestions de produits.
                </p>
              </CardContent>
            </Card>
          ) : isErrorUserSuggestions ? (
            <Card className="p-8 text-center">
              <CardContent className="flex flex-col items-center justify-center py-6">
                <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Erreur de chargement</h3>
                <p className="text-gray-500 max-w-sm mb-4">
                  Une erreur est survenue lors du chargement de vos suggestions. Veuillez réessayer.
                </p>
                <Button variant="outline" onClick={() => refetchUserSuggestions()}>
                  Réessayer
                </Button>
              </CardContent>
            </Card>
          ) : (
            <UserSuggestionsList 
              suggestions={userSuggestions} 
              onDelete={handleDeleteSuggestion}
              onEdit={handleEditSuggestion}
            />
          )}
        </TabsContent>

        {/* Onglet des statistiques */}
        <TabsContent value="stats" className="space-y-6">
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Statistiques et Analyses</h2>
              <p className="text-gray-600">
                Suivez vos performances et l'impact de vos suggestions sur notre catalogue
              </p>
            </div>

            <ErrorBoundary
              fallback={
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Impossible de charger les statistiques pour le moment.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    Recharger la page
                  </Button>
                </div>
              }
            >
              <SuggestionStats />
            </ErrorBoundary>
          </div>
        </TabsContent>
      </Tabs>

      {/* Dialogue de modification de suggestion */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Modifier la suggestion</DialogTitle>
            <DialogDescription>
              Mettez à jour les détails de votre suggestion de produit.
            </DialogDescription>
          </DialogHeader>
          {editingSuggestion && (
            <ProductSuggestionForm 
              initialValues={{
                productName: editingSuggestion.name,
                category: editingSuggestion.category,
                description: editingSuggestion.description,
                estimatedPrice: editingSuggestion.estimated_price,
                reason: editingSuggestion.reason,
              }}
              isEditing={true}
              onSuccess={() => {
                setIsDialogOpen(false);
                refetchUserSuggestions();
              }}
              onSubmit={(values) => {
                handleUpdateSuggestion({
                  name: values.productName,
                  category: values.category,
                  description: values.description,
                  estimated_price: values.estimatedPrice,
                  reason: values.reason,
                });
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </AccountLayout>
  );
};

export default Suggestions;
