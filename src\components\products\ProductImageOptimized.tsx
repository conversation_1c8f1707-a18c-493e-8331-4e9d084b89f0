import React from 'react';
import { ProductDetail, ProductListItem } from '@/services/productApi';
import { useProductImage, useProductImageUrl } from '@/hooks/useProductImage';
import { Loader2, Image as ImageIcon, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ProductImageOptimizedProps {
  product: ProductDetail | ProductListItem;
  className?: string;
  style?: React.CSSProperties;
  showPlaceholder?: boolean;
  lazy?: boolean;
  fallbackMode?: 'placeholder' | 'icon' | 'none';
}

/**
 * Composant optimisé pour afficher les images de produits
 * Version simplifiée et plus performante
 */
const ProductImageOptimized: React.FC<ProductImageOptimizedProps> = ({
  product,
  className = '',
  style = {},
  showPlaceholder = true,
  lazy = true,
  fallbackMode = 'placeholder'
}) => {
  const { imageSrc, isLoading, hasError, retry } = useProductImage(product);

  // Composant de chargement
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`} style={style}>
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  // Composant d'erreur avec retry
  if (hasError || !imageSrc) {
    if (!showPlaceholder || fallbackMode === 'none') return null;

    if (fallbackMode === 'icon') {
      return (
        <div className={`flex flex-col items-center justify-center bg-gray-100 text-gray-400 ${className}`} style={style}>
          <ImageIcon className="h-12 w-12 mb-2" />
          <span className="text-xs text-center px-2">{product.name}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={retry}
            className="mt-2 h-6 px-2 text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        </div>
      );
    }

    // Mode placeholder par défaut
    return (
      <div className={`flex flex-col items-center justify-center bg-gray-100 ${className}`} style={style}>
        <img 
          src="/placeholder-product.jpg"
          alt={product.name}
          className="max-w-full max-h-full object-contain opacity-60"
          onError={(e) => {
            // Si le placeholder échoue aussi, basculer vers l'icône
            const target = e.target as HTMLImageElement;
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="flex flex-col items-center justify-center text-gray-400 p-4">
                  <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-xs text-center">${product.name}</span>
                </div>
              `;
            }
          }}
        />
      </div>
    );
  }

  // Image principale
  return (
    <img
      src={imageSrc}
      alt={product.name}
      className={className}
      style={style}
      loading={lazy ? 'lazy' : 'eager'}
      onError={(e) => {
        console.error('Erreur de chargement de l\'image:', imageSrc);
        retry();
      }}
    />
  );
};

/**
 * Composant simple pour obtenir juste l'URL d'image
 */
export const ProductImageUrl: React.FC<{ product: ProductDetail | ProductListItem }> = ({ product }) => {
  const imageUrl = useProductImageUrl(product);
  return <span>{imageUrl || 'Aucune image'}</span>;
};

/**
 * Composant pour précharger les images
 */
export const ProductImagePreloader: React.FC<{ products: (ProductDetail | ProductListItem)[] }> = ({ products }) => {
  React.useEffect(() => {
    products.forEach(product => {
      const imageUrl = useProductImageUrl(product);
      if (imageUrl) {
        const img = new Image();
        img.src = imageUrl;
      }
    });
  }, [products]);

  return null;
};

export default ProductImageOptimized;
