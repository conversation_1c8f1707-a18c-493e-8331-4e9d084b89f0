import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Trash2, Edit, Save, X, RefreshCw } from "lucide-react";
import messagingApi, { Category, Tag } from "@/services/messagingApi";

const MessageSettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("categories");
  
  // Category state
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [newCategory, setNewCategory] = useState({
    name: "",
    description: "",
    color: "#3498db",
    icon: ""
  });
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  
  // Tag state
  const [isAddTagOpen, setIsAddTagOpen] = useState(false);
  const [newTag, setNewTag] = useState({
    name: "",
    color: "#2ecc71"
  });
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  
  // Fetch categories
  const { 
    data: categories, 
    isLoading: isLoadingCategories,
    refetch: refetchCategories
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => messagingApi.getCategories(),
  });
  
  // Fetch tags
  const { 
    data: tags, 
    isLoading: isLoadingTags,
    refetch: refetchTags
  } = useQuery({
    queryKey: ['tags'],
    queryFn: () => messagingApi.getTags(),
  });
  
  // Create category mutation
  const createCategoryMutation = useMutation({
    mutationFn: (data: { name: string, description?: string, color?: string, icon?: string }) => 
      messagingApi.createCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      setIsAddCategoryOpen(false);
      setNewCategory({
        name: "",
        description: "",
        color: "#3498db",
        icon: ""
      });
      toast({
        title: "Catégorie créée",
        description: "La catégorie a été créée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création de la catégorie",
        variant: "destructive",
      });
      console.error("Error creating category:", error);
    }
  });
  
  // Update category mutation
  const updateCategoryMutation = useMutation({
    mutationFn: (data: { id: number, updates: { name?: string, description?: string, color?: string, icon?: string, is_active?: boolean } }) => 
      messagingApi.updateCategory(data.id, data.updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      setEditingCategory(null);
      toast({
        title: "Catégorie mise à jour",
        description: "La catégorie a été mise à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de la catégorie",
        variant: "destructive",
      });
      console.error("Error updating category:", error);
    }
  });
  
  // Create tag mutation
  const createTagMutation = useMutation({
    mutationFn: (data: { name: string, color?: string }) => 
      messagingApi.createTag(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      setIsAddTagOpen(false);
      setNewTag({
        name: "",
        color: "#2ecc71"
      });
      toast({
        title: "Étiquette créée",
        description: "L'étiquette a été créée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création de l'étiquette",
        variant: "destructive",
      });
      console.error("Error creating tag:", error);
    }
  });
  
  // Update tag mutation
  const updateTagMutation = useMutation({
    mutationFn: (data: { id: number, updates: { name?: string, color?: string, is_active?: boolean } }) => 
      messagingApi.updateTag(data.id, data.updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      setEditingTag(null);
      toast({
        title: "Étiquette mise à jour",
        description: "L'étiquette a été mise à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de l'étiquette",
        variant: "destructive",
      });
      console.error("Error updating tag:", error);
    }
  });
  
  // Handle category form submission
  const handleCreateCategory = () => {
    if (!newCategory.name.trim()) {
      toast({
        title: "Erreur",
        description: "Le nom de la catégorie est requis",
        variant: "destructive",
      });
      return;
    }
    
    createCategoryMutation.mutate(newCategory);
  };
  
  // Handle tag form submission
  const handleCreateTag = () => {
    if (!newTag.name.trim()) {
      toast({
        title: "Erreur",
        description: "Le nom de l'étiquette est requis",
        variant: "destructive",
      });
      return;
    }
    
    createTagMutation.mutate(newTag);
  };
  
  // Handle category update
  const handleUpdateCategory = (category: Category) => {
    if (!category.name.trim()) {
      toast({
        title: "Erreur",
        description: "Le nom de la catégorie est requis",
        variant: "destructive",
      });
      return;
    }
    
    updateCategoryMutation.mutate({
      id: category.id,
      updates: {
        name: category.name,
        description: category.description,
        color: category.color,
        icon: category.icon
      }
    });
  };
  
  // Handle tag update
  const handleUpdateTag = (tag: Tag) => {
    if (!tag.name.trim()) {
      toast({
        title: "Erreur",
        description: "Le nom de l'étiquette est requis",
        variant: "destructive",
      });
      return;
    }
    
    updateTagMutation.mutate({
      id: tag.id,
      updates: {
        name: tag.name,
        color: tag.color
      }
    });
  };
  
  // Toggle category active state
  const toggleCategoryActive = (category: Category) => {
    updateCategoryMutation.mutate({
      id: category.id,
      updates: {
        is_active: !category.is_active
      }
    });
  };
  
  // Toggle tag active state
  const toggleTagActive = (tag: Tag) => {
    updateTagMutation.mutate({
      id: tag.id,
      updates: {
        is_active: !tag.is_active
      }
    });
  };

  return (
    <AdminLayout>
      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Paramètres de messagerie</h1>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => activeTab === "categories" ? refetchCategories() : refetchTags()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualiser
            </Button>
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="categories">Catégories</TabsTrigger>
            <TabsTrigger value="tags">Étiquettes</TabsTrigger>
          </TabsList>
          
          {/* Categories Tab */}
          <TabsContent value="categories">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Catégories de conversation</h2>
              <Dialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter une catégorie
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Ajouter une catégorie</DialogTitle>
                    <DialogDescription>
                      Créez une nouvelle catégorie pour organiser les conversations.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Nom</Label>
                      <Input
                        id="name"
                        placeholder="Nom de la catégorie"
                        value={newCategory.name}
                        onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Description de la catégorie"
                        value={newCategory.description}
                        onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="color">Couleur</Label>
                      <div className="flex gap-2">
                        <Input
                          id="color"
                          type="color"
                          className="w-12 h-10 p-1"
                          value={newCategory.color}
                          onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}
                        />
                        <Input
                          value={newCategory.color}
                          onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}
                          className="flex-1"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="icon">Icône (nom de l'icône)</Label>
                      <Input
                        id="icon"
                        placeholder="Ex: message-square"
                        value={newCategory.icon}
                        onChange={(e) => setNewCategory({ ...newCategory, icon: e.target.value })}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddCategoryOpen(false)}>Annuler</Button>
                    <Button 
                      onClick={handleCreateCategory}
                      disabled={createCategoryMutation.isPending}
                    >
                      {createCategoryMutation.isPending ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Plus className="h-4 w-4 mr-2" />
                      )}
                      Créer
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
            
            {isLoadingCategories ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : categories && categories.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.map((category) => (
                  <Card key={category.id} className={`${!category.is_active ? 'opacity-60' : ''}`}>
                    {editingCategory?.id === category.id ? (
                      // Edit mode
                      <CardContent className="pt-6 space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor={`edit-name-${category.id}`}>Nom</Label>
                          <Input
                            id={`edit-name-${category.id}`}
                            value={editingCategory.name}
                            onChange={(e) => setEditingCategory({ ...editingCategory, name: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`edit-description-${category.id}`}>Description</Label>
                          <Textarea
                            id={`edit-description-${category.id}`}
                            value={editingCategory.description}
                            onChange={(e) => setEditingCategory({ ...editingCategory, description: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`edit-color-${category.id}`}>Couleur</Label>
                          <div className="flex gap-2">
                            <Input
                              id={`edit-color-${category.id}`}
                              type="color"
                              className="w-12 h-10 p-1"
                              value={editingCategory.color}
                              onChange={(e) => setEditingCategory({ ...editingCategory, color: e.target.value })}
                            />
                            <Input
                              value={editingCategory.color}
                              onChange={(e) => setEditingCategory({ ...editingCategory, color: e.target.value })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`edit-icon-${category.id}`}>Icône</Label>
                          <Input
                            id={`edit-icon-${category.id}`}
                            value={editingCategory.icon}
                            onChange={(e) => setEditingCategory({ ...editingCategory, icon: e.target.value })}
                          />
                        </div>
                        <div className="flex justify-end gap-2 mt-4">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setEditingCategory(null)}
                          >
                            <X className="h-4 w-4 mr-1" />
                            Annuler
                          </Button>
                          <Button 
                            size="sm"
                            onClick={() => handleUpdateCategory(editingCategory)}
                            disabled={updateCategoryMutation.isPending}
                          >
                            {updateCategoryMutation.isPending ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <Save className="h-4 w-4 mr-1" />
                            )}
                            Enregistrer
                          </Button>
                        </div>
                      </CardContent>
                    ) : (
                      // View mode
                      <>
                        <CardHeader>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded-full" 
                              style={{ backgroundColor: category.color }}
                            ></div>
                            <CardTitle>{category.name}</CardTitle>
                          </div>
                          <CardDescription>{category.description}</CardDescription>
                        </CardHeader>
                        <CardFooter className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <Switch 
                              checked={category.is_active} 
                              onCheckedChange={() => toggleCategoryActive(category)}
                              disabled={updateCategoryMutation.isPending}
                            />
                            <span className="text-sm">
                              {category.is_active ? 'Actif' : 'Inactif'}
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setEditingCategory(category)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardFooter>
                      </>
                    )}
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <p className="text-gray-500">Aucune catégorie trouvée</p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => setIsAddCategoryOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter une catégorie
                </Button>
              </div>
            )}
          </TabsContent>
          
          {/* Tags Tab */}
          <TabsContent value="tags">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Étiquettes de conversation</h2>
              <Dialog open={isAddTagOpen} onOpenChange={setIsAddTagOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter une étiquette
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Ajouter une étiquette</DialogTitle>
                    <DialogDescription>
                      Créez une nouvelle étiquette pour marquer les conversations.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="tag-name">Nom</Label>
                      <Input
                        id="tag-name"
                        placeholder="Nom de l'étiquette"
                        value={newTag.name}
                        onChange={(e) => setNewTag({ ...newTag, name: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="tag-color">Couleur</Label>
                      <div className="flex gap-2">
                        <Input
                          id="tag-color"
                          type="color"
                          className="w-12 h-10 p-1"
                          value={newTag.color}
                          onChange={(e) => setNewTag({ ...newTag, color: e.target.value })}
                        />
                        <Input
                          value={newTag.color}
                          onChange={(e) => setNewTag({ ...newTag, color: e.target.value })}
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddTagOpen(false)}>Annuler</Button>
                    <Button 
                      onClick={handleCreateTag}
                      disabled={createTagMutation.isPending}
                    >
                      {createTagMutation.isPending ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Plus className="h-4 w-4 mr-2" />
                      )}
                      Créer
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
            
            {isLoadingTags ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : tags && tags.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {tags.map((tag) => (
                  <Card key={tag.id} className={`${!tag.is_active ? 'opacity-60' : ''}`}>
                    {editingTag?.id === tag.id ? (
                      // Edit mode
                      <CardContent className="pt-6 space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor={`edit-tag-name-${tag.id}`}>Nom</Label>
                          <Input
                            id={`edit-tag-name-${tag.id}`}
                            value={editingTag.name}
                            onChange={(e) => setEditingTag({ ...editingTag, name: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`edit-tag-color-${tag.id}`}>Couleur</Label>
                          <div className="flex gap-2">
                            <Input
                              id={`edit-tag-color-${tag.id}`}
                              type="color"
                              className="w-12 h-10 p-1"
                              value={editingTag.color}
                              onChange={(e) => setEditingTag({ ...editingTag, color: e.target.value })}
                            />
                            <Input
                              value={editingTag.color}
                              onChange={(e) => setEditingTag({ ...editingTag, color: e.target.value })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div className="flex justify-end gap-2 mt-4">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setEditingTag(null)}
                          >
                            <X className="h-4 w-4 mr-1" />
                            Annuler
                          </Button>
                          <Button 
                            size="sm"
                            onClick={() => handleUpdateTag(editingTag)}
                            disabled={updateTagMutation.isPending}
                          >
                            {updateTagMutation.isPending ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <Save className="h-4 w-4 mr-1" />
                            )}
                            Enregistrer
                          </Button>
                        </div>
                      </CardContent>
                    ) : (
                      // View mode
                      <>
                        <CardHeader>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded-full" 
                              style={{ backgroundColor: tag.color }}
                            ></div>
                            <CardTitle>{tag.name}</CardTitle>
                          </div>
                        </CardHeader>
                        <CardFooter className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <Switch 
                              checked={tag.is_active} 
                              onCheckedChange={() => toggleTagActive(tag)}
                              disabled={updateTagMutation.isPending}
                            />
                            <span className="text-sm">
                              {tag.is_active ? 'Actif' : 'Inactif'}
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setEditingTag(tag)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardFooter>
                      </>
                    )}
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <p className="text-gray-500">Aucune étiquette trouvée</p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => setIsAddTagOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter une étiquette
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default MessageSettings;
