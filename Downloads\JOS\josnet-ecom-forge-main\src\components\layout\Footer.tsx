
import { Facebook, Instagram, Linkedin, Mail, MapPin, Phone, Twitter } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-xl font-bold mb-4">JOSNET NETWORK</h3>
            <p className="text-gray-300 mb-4">
              Leader dans le secteur des technologies de l'information et des communications, 
              offrant des services de haute qualité et restant à la pointe de l'innovation.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-white hover:text-accent transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-white hover:text-accent transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-white hover:text-accent transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-white hover:text-accent transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-4">Liens Rapides</h3>
            <ul className="space-y-2">
              <li>
                <a href="/" className="text-gray-300 hover:text-white transition-colors">Accueil</a>
              </li>
              <li>
                <a href="/products" className="text-gray-300 hover:text-white transition-colors">Nos Produits</a>
              </li>
              <li>
                <a href="/about" className="text-gray-300 hover:text-white transition-colors">À Propos</a>
              </li>
              <li>
                <a href="/blog" className="text-gray-300 hover:text-white transition-colors">Blog</a>
              </li>
              <li>
                <a href="/contact" className="text-gray-300 hover:text-white transition-colors">Contact</a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-xl font-bold mb-4">Support</h3>
            <ul className="space-y-2">
              <li>
                <a href="/faq" className="text-gray-300 hover:text-white transition-colors">FAQ</a>
              </li>
              <li>
                <a href="/shipping" className="text-gray-300 hover:text-white transition-colors">Livraison</a>
              </li>
              <li>
                <a href="/returns" className="text-gray-300 hover:text-white transition-colors">Retours</a>
              </li>
              <li>
                <a href="/terms" className="text-gray-300 hover:text-white transition-colors">Conditions d'utilisation</a>
              </li>
              <li>
                <a href="/privacy" className="text-gray-300 hover:text-white transition-colors">Politique de confidentialité</a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-xl font-bold mb-4">Contact</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin className="mr-2 h-5 w-5 text-accent flex-shrink-0" />
                <span className="text-gray-300">123 Avenue Principale, Bujumbura, Burundi</span>
              </li>
              <li className="flex items-center">
                <Phone className="mr-2 h-5 w-5 text-accent flex-shrink-0" />
                <span className="text-gray-300">+257 12 345 678</span>
              </li>
              <li className="flex items-center">
                <Mail className="mr-2 h-5 w-5 text-accent flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8">
          <p className="text-center text-gray-400">
            &copy; {currentYear} JOSNET NETWORK. Tous droits réservés.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
