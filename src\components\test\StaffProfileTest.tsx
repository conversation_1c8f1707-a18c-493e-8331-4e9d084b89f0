import React from 'react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';

const StaffProfileTest = () => {
  // Test des fonctions de formatage de date
  const testDates = [
    '2024-01-15T00:00:00Z',
    '2024-01-10T00:00:00Z',
    new Date().toISOString(),
    '',
    null,
    undefined,
    'invalid-date'
  ];

  const formatDate = (dateString: string | null | undefined) => {
    try {
      if (!dateString) return 'Date non disponible';
      const date = parseISO(dateString);
      if (isNaN(date.getTime())) return 'Date invalide';
      return format(date, 'dd MMMM yyyy à HH:mm', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date invalide';
    }
  };

  const formatDateShort = (dateString: string | null | undefined) => {
    try {
      if (!dateString) return 'Date non définie';
      const date = parseISO(dateString);
      if (isNaN(date.getTime())) return 'Date invalide';
      return format(date, 'dd MMM yyyy', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date invalide';
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4">Test de Formatage des Dates</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold mb-2">Format Long (dd MMMM yyyy à HH:mm)</h3>
          {testDates.map((date, index) => (
            <div key={index} className="flex justify-between items-center p-2 border rounded">
              <span className="text-sm text-gray-600">
                {date === null ? 'null' : date === undefined ? 'undefined' : date || 'empty string'}
              </span>
              <span className="font-medium">
                {formatDate(date as string)}
              </span>
            </div>
          ))}
        </div>

        <div>
          <h3 className="font-semibold mb-2">Format Court (dd MMM yyyy)</h3>
          {testDates.map((date, index) => (
            <div key={index} className="flex justify-between items-center p-2 border rounded">
              <span className="text-sm text-gray-600">
                {date === null ? 'null' : date === undefined ? 'undefined' : date || 'empty string'}
              </span>
              <span className="font-medium">
                {formatDateShort(date as string)}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded">
        <h3 className="font-semibold text-green-800 mb-2">✅ Test Réussi</h3>
        <p className="text-sm text-green-700">
          Toutes les dates sont formatées correctement sans erreur "Invalid time value"
        </p>
      </div>
    </div>
  );
};

export default StaffProfileTest;
