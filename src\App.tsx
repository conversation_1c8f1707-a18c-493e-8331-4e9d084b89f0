
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import PromotionToast from "@/components/notifications/PromotionToast";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { TranslationProvider } from "@/contexts/TranslationContext";
import { CartProvider } from "@/contexts/CartContext";
import { useEffect } from "react";
import Index from "./pages/Index";
import Products from "./pages/Products";
import ProductDetail from "./pages/ProductDetail";
import Cart from "./pages/Cart";
import Checkout from "./pages/Checkout";
import OrderConfirmation from "./pages/OrderConfirmation";
import OrderTracking from "./pages/OrderTracking";
import Reviews from "./pages/Reviews";
import ProductReviews from "./pages/ProductReviews";
import Contact from "./pages/Contact";
import FAQ from "./pages/FAQ";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import Blog from "./pages/Blog";
import BlogPost from "./pages/BlogPost";
import NotFound from "./pages/NotFound";
import Unauthorized from "./pages/Unauthorized";
import Login from "./pages/Login";
import Register from "./pages/Register";
import VerifyEmail from "./pages/VerifyEmail";
import ActivateAccount from "./pages/ActivateAccount";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import Promotions from "./pages/Promotions";
import PromotionDetail from "./pages/PromotionDetail";
import About from "./pages/about/About";
import Shipping from "./pages/shipping/Shipping";
import Returns from "./pages/returns/Returns";


// Client account pages
import Dashboard from "./pages/account/Dashboard";
import OrderHistory from "./pages/account/OrderHistory";
import Profile from "./pages/account/Profile";
import AccountReturns from "./pages/account/Returns";
import ReturnDetail from "./pages/account/ReturnDetail";
import ClaimDetail from "./pages/account/ClaimDetail";
import Invoices from "./pages/account/Invoices";
import InvoiceDetail from "./pages/account/InvoiceDetail";
import AccountTransactions from "./pages/account/Transactions";
import OrderDetail from "./pages/account/OrderDetail";
import Suggestions from "./pages/account/Suggestions";
import Messages from "./pages/account/Messages";
import Notifications from "./pages/account/Notifications";

// Debug pages
import AuthDebugPage from "./pages/debug/AuthDebugPage";

// Test pages
import ConversationTestPage from "./pages/admin/ConversationTestPage";

// Messaging pages
import ConversationDetail from "./pages/messaging/ConversationDetail";



// Admin pages
import AdminDashboard from "./pages/admin/Dashboard";
import AdminProducts from "./pages/admin/AdminProducts";
import ProductForm from "./pages/admin/ProductForm";
import AdminCategories from "./pages/admin/Categories";
import AdminOrders from "./pages/admin/Orders";
import OrderDetailPage from "./pages/admin/OrderDetail";
import OrderCreatePage from "./pages/admin/OrderCreate";
import OrderEditPage from "./pages/admin/OrderEdit";
import OrderButtonTest from "./pages/admin/OrderButtonTest";
import OrderStatusUpdate from "./pages/admin/OrderStatusUpdate";
import OrderDashboardPage from "./pages/admin/OrderDashboard";
import AdminStatistics from "./pages/admin/Statistics";
import AdminUsers from "./pages/admin/Users";
import UserDetail from "./pages/admin/UserDetail";
import AdminInventory from "./pages/admin/Inventory";
import InventoryManagement from "./pages/admin/products/InventoryManagement";
import AdminPromotions from "./pages/admin/Promotions";
import ModernMessages from "./pages/admin/ModernMessages";
import StaffProfile from "./pages/admin/StaffProfile";
import MessageSettings from "./pages/admin/MessageSettings";
import ResponseTemplates from "./pages/admin/ResponseTemplates";
import AdminCMS from "./pages/admin/CMS";
import AdminLogs from "./pages/admin/Logs";
import PaymentMethods from "./pages/admin/PaymentMethods";
import PaymentMethodFormPage from "./pages/admin/PaymentMethodForm";
import AdminTransactions from "./pages/admin/Transactions";
import TransactionDetailPage from "./pages/admin/TransactionDetail";
import AdminInvoices from "./pages/admin/Invoices";
import InvoiceDetailPage from "./pages/admin/InvoiceDetail";
import GenerateInvoice from "./pages/admin/GenerateInvoice";
import PageForm from "./pages/admin/cms/PageForm";
import MediaManager from "./pages/admin/cms/MediaManager";
import AdminReturns from "./pages/admin/Returns";
import AdminClaims from "./pages/admin/Claims";
import { TranslationDemo } from "./components/demo/TranslationDemo";

// Import ProtectedRoute
import ProtectedRoute from "@/components/auth/ProtectedRoute";



// Import Session Manager
import { initSessionManager, startSessionMonitoring, stopSessionMonitoring } from "@/utils/sessionManager";

const queryClient = new QueryClient();

function App() {
  // Initialiser le gestionnaire de session avec un timeout de 30 minutes
  useEffect(() => {
    // Initialiser le gestionnaire de session
    initSessionManager(30 * 60 * 1000);
    
    // Démarrer la surveillance de session
    const intervalId = startSessionMonitoring(30 * 60 * 1000);
    
    // Nettoyer lors du démontage du composant
    return () => {
      stopSessionMonitoring(intervalId);
    };
  }, []);

  return (
  <QueryClientProvider client={queryClient}>
    <TranslationProvider>
      <CartProvider>
        <AuthProvider>
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
          {/* Composant de notification pour les nouvelles promotions */}
          <PromotionToast />
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/products" element={<Products />} />
            <Route path="/product/:id" element={<ProductDetail />} />
            <Route path="/cart" element={<Cart />} />
            <Route path="/reviews" element={<Reviews />} />
            <Route path="/reviews/:productId" element={<Reviews />} />
            <Route path="/product/:productId/reviews" element={<ProductReviews />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/faq" element={<FAQ />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/blog/:slug" element={<BlogPost />} />
            <Route path="/promotions" element={<Promotions />} />
            <Route path="/promotions/:id" element={<PromotionDetail />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/verify-email" element={<VerifyEmail />} />
            <Route path="/activate-account" element={<ActivateAccount />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password/:token" element={<ResetPassword />} />
            <Route path="/about" element={<About />} />
            <Route path="/shipping" element={<Shipping />} />
            <Route path="/returns" element={<Returns />} />
            <Route path="/unauthorized" element={<Unauthorized />} />

            {/* Translation Demo - Public route */}
            <Route path="/translation-demo" element={<TranslationDemo />} />

            {/* Protected routes that require authentication */}
            <Route path="/checkout" element={
              <ProtectedRoute>
                <Checkout />
              </ProtectedRoute>
            } />
            <Route path="/order-confirmation/:orderId" element={
              <ProtectedRoute>
                <OrderConfirmation />
              </ProtectedRoute>
            } />
            <Route path="/order-tracking" element={
              <ProtectedRoute>
                <OrderTracking />
              </ProtectedRoute>
            } />

            {/* Client account routes - require customer role */}
            <Route path="/account" element={
              <ProtectedRoute requiredRole="customer">
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/account/orders" element={
              <ProtectedRoute requiredRole="customer">
                <OrderHistory />
              </ProtectedRoute>
            } />
            <Route path="/account/orders/:id" element={
              <ProtectedRoute requiredRole="customer">
                <OrderDetail />
              </ProtectedRoute>
            } />
            <Route path="/order/:id" element={
              <ProtectedRoute requiredRole="customer">
                <OrderDetail />
              </ProtectedRoute>
            } />
            <Route path="/account/profile" element={
              <ProtectedRoute requiredRole="customer">
                <Profile />
              </ProtectedRoute>
            } />
            <Route path="/account/returns" element={
              <ProtectedRoute requiredRole="customer">
                <AccountReturns />
              </ProtectedRoute>
            } />
            <Route path="/account/returns/:id" element={
              <ProtectedRoute requiredRole="customer">
                <ReturnDetail />
              </ProtectedRoute>
            } />
            <Route path="/account/claims/:id" element={
              <ProtectedRoute requiredRole="customer">
                <ClaimDetail />
              </ProtectedRoute>
            } />
            <Route path="/account/invoices" element={
              <ProtectedRoute requiredRole="customer">
                <Invoices />
              </ProtectedRoute>
            } />
            <Route path="/account/invoices/:id" element={
              <ProtectedRoute requiredRole="customer">
                <InvoiceDetail />
              </ProtectedRoute>
            } />
            <Route path="/account/transactions" element={
              <ProtectedRoute requiredRole="customer">
                <AccountTransactions />
              </ProtectedRoute>
            } />
            <Route path="/account/suggestions" element={
              <ProtectedRoute requiredRole="customer">
                <Suggestions />
              </ProtectedRoute>
            } />
            <Route path="/account/messages" element={
              <ProtectedRoute requiredRole="customer">
                <Messages />
              </ProtectedRoute>
            } />
            <Route path="/account/notifications" element={
              <ProtectedRoute requiredRole="customer">
                <Notifications />
              </ProtectedRoute>
            } />

            {/* Debug routes - for development */}
            <Route path="/debug/auth" element={<AuthDebugPage />} />

            {/* Test routes - for development */}
            <Route path="/admin/test/conversations" element={<ConversationTestPage />} />

            {/* Messaging routes - accessible to all authenticated users */}
            <Route path="/messaging/conversation/:id" element={
              <ProtectedRoute>
                <ConversationDetail />
              </ProtectedRoute>
            } />

            {/* Alternative messaging routes for compatibility */}
            <Route path="/conversation/:id" element={
              <ProtectedRoute>
                <ConversationDetail />
              </ProtectedRoute>
            } />
            <Route path="/messages/conversation/:id" element={
              <ProtectedRoute>
                <ConversationDetail />
              </ProtectedRoute>
            } />

            {/* Admin routes - require admin or staff role */}
            <Route path="/admin/dashboard" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminDashboard />
              </ProtectedRoute>
            } />
            <Route path="/admin/products" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminProducts />
              </ProtectedRoute>
            } />
            <Route path="/admin/products/new" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <ProductForm />
              </ProtectedRoute>
            } />
            <Route path="/admin/products/edit/:slug" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <ProductForm />
              </ProtectedRoute>
            } />
            <Route path="/admin/products/duplicate/:slug" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <ProductForm mode="duplicate" />
              </ProtectedRoute>
            } />
            <Route path="/admin/categories" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminCategories />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminOrders />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/:id" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <OrderDetailPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/:id/test" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <OrderButtonTest />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/:id/status" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <OrderStatusUpdate />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/create" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <OrderCreatePage />
              </ProtectedRoute>
            } />
            <Route path="/admin/orders/:id/edit" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <OrderEditPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/transactions" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminTransactions />
              </ProtectedRoute>
            } />
            <Route path="/admin/order-dashboard" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <OrderDashboardPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/statistics" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminStatistics />
              </ProtectedRoute>
            } />
            <Route path="/admin/users" element={
              <ProtectedRoute requiredRole="admin">
                <AdminUsers />
              </ProtectedRoute>
            } />
            <Route path="/admin/users/:id" element={
              <ProtectedRoute requiredRole="admin">
                <UserDetail />
              </ProtectedRoute>
            } />
            <Route path="/admin/inventory" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminInventory />
              </ProtectedRoute>
            } />
            <Route path="/admin/products/inventory" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <InventoryManagement />
              </ProtectedRoute>
            } />
            <Route path="/admin/promotions" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminPromotions />
              </ProtectedRoute>
            } />
            <Route path="/admin/messages" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <ModernMessages />
              </ProtectedRoute>
            } />
            <Route path="/admin/staff-profile" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <StaffProfile />
              </ProtectedRoute>
            } />
            <Route path="/admin/profile" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <StaffProfile />
              </ProtectedRoute>
            } />

            <Route path="/admin/message-settings" element={
              <ProtectedRoute requiredRole="admin">
                <MessageSettings />
              </ProtectedRoute>
            } />
            <Route path="/admin/response-templates" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <ResponseTemplates />
              </ProtectedRoute>
            } />
            <Route path="/admin/cms" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminCMS />
              </ProtectedRoute>
            } />
            <Route path="/admin/cms/pages/new" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <PageForm />
              </ProtectedRoute>
            } />
            <Route path="/admin/cms/pages/edit/:slug" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <PageForm />
              </ProtectedRoute>
            } />
            <Route path="/admin/cms/media" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <MediaManager />
              </ProtectedRoute>
            } />
            <Route path="/admin/logs" element={
              <ProtectedRoute requiredRole="admin">
                <AdminLogs />
              </ProtectedRoute>
            } />

            {/* Payment routes */}
            <Route path="/admin/payment-methods" element={
              <ProtectedRoute requiredRole="admin">
                <PaymentMethods />
              </ProtectedRoute>
            } />
            <Route path="/admin/payment-methods/create" element={
              <ProtectedRoute requiredRole="admin">
                <PaymentMethodFormPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/payment-methods/:id/edit" element={
              <ProtectedRoute requiredRole="admin">
                <PaymentMethodFormPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/transactions" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminTransactions />
              </ProtectedRoute>
            } />
            <Route path="/admin/transactions/:id" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <TransactionDetailPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/invoices" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminInvoices />
              </ProtectedRoute>
            } />
            <Route path="/admin/invoices/:id" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <InvoiceDetailPage />
              </ProtectedRoute>
            } />
            <Route path="/admin/invoices/generate/:orderId?" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <GenerateInvoice />
              </ProtectedRoute>
            } />

            {/* Routes pour la gestion des retours et réclamations */}
            <Route path="/admin/returns" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminReturns />
              </ProtectedRoute>
            } />
            <Route path="/admin/claims" element={
              <ProtectedRoute requiredRole={['admin', 'staff']}>
                <AdminClaims />
              </ProtectedRoute>
            } />

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
      </CartProvider>
    </TranslationProvider>
  </QueryClientProvider>
  );
}

export default App;
