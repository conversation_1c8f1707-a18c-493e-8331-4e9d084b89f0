#!/usr/bin/env python3
"""
Script pour ajouter les nouvelles traductions du panier
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from translations.models import TranslationKey, Translation

def add_new_cart_translations():
    """Ajouter les nouvelles traductions du panier"""
    print("🛒 AJOUT DES NOUVELLES TRADUCTIONS DU PANIER")
    print("=" * 50)
    
    new_translations = {
        "cart.quantity_updated": {
            "fr": "quantité mise à jour",
            "en": "quantity updated",
            "sw": "idadi imesasishwa",
            "rn": "umubare wahinduwe"
        },
        "cart.added_to_your_cart": {
            "fr": "a été ajouté à votre panier",
            "en": "has been added to your cart",
            "sw": "imeongezwa kwenye kikapu chako",
            "rn": "cyashizwe mu gikoni cyawe"
        },
        "cart.invalid_promo_code": {
            "fr": "Code promo invalide",
            "en": "Invalid promo code",
            "sw": "Msimbo wa punguzo si sahihi",
            "rn": "Kode ya kugabanya ntiyemewe"
        },
        "cart.promo_validation_error": {
            "fr": "Erreur lors de la validation du code promo",
            "en": "Error validating promo code",
            "sw": "Hitilafu wakati wa kuthibitisha msimbo wa punguzo",
            "rn": "Ikosa mu gukemura kode ya kugabanya"
        }
    }
    
    imported_count = 0
    
    for key_name, translations_dict in new_translations.items():
        # Créer ou récupérer la clé de traduction
        translation_key, created = TranslationKey.objects.get_or_create(
            key=key_name,
            defaults={
                'category': 'cart',
                'description': f"Traduction pour: {translations_dict['fr']}",
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Nouvelle clé: {key_name}")
        
        # Ajouter les traductions pour chaque langue
        for lang_code, translation_text in translations_dict.items():
            translation_obj, created = Translation.objects.get_or_create(
                key=translation_key,
                language_code=lang_code,
                defaults={
                    'value': translation_text,
                    'is_approved': True
                }
            )
            
            if created:
                imported_count += 1
            elif translation_obj.value != translation_text:
                # Mettre à jour si différent
                translation_obj.value = translation_text
                translation_obj.save()
                print(f"🔄 Mise à jour: {key_name} ({lang_code})")
    
    print(f"\n📊 RÉSULTATS:")
    print(f"   • Nouvelles traductions importées: {imported_count}")
    print(f"   • Clés de traduction traitées: {len(new_translations)}")
    
    return imported_count

if __name__ == "__main__":
    try:
        imported_count = add_new_cart_translations()
        
        if imported_count > 0:
            print(f"\n🎉 {imported_count} nouvelles traductions ajoutées!")
        else:
            print(f"\n✅ Toutes les traductions sont déjà à jour")
            
        # Statistiques finales
        from translations.models import TranslationKey, Translation
        total_keys = TranslationKey.objects.count()
        total_translations = Translation.objects.count()
        
        print(f"\n📊 STATISTIQUES TOTALES:")
        print(f"   • Total clés: {total_keys}")
        print(f"   • Total traductions: {total_translations}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
