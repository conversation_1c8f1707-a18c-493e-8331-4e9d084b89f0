import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Star, 
  Heart, 
  ShoppingCart, 
  Filter, 
  SortAsc, 
  SortDesc,
  RefreshCw,
  TrendingUp,
  Eye,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { accountApi, SuggestedProduct } from '@/services/accountApi';
import { formatCurrency } from '@/utils/formatters';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';

interface RecommendationFilters {
  category?: string;
  priceRange: { min: number; max: number };
  minRating: number;
  sortBy: 'price' | 'rating' | 'popularity' | 'newest';
  limit: number;
}

const EnhancedProductRecommendations: React.FC = () => {
  const { toast } = useToast();
  const [filters, setFilters] = useState<RecommendationFilters>({
    priceRange: { min: 0, max: 1000000 },
    minRating: 0,
    sortBy: 'popularity',
    limit: 12
  });
  
  const [showFilters, setShowFilters] = useState(false);
  const [likedProducts, setLikedProducts] = useState<Set<number>>(new Set());
  const [dislikedProducts, setDislikedProducts] = useState<Set<number>>(new Set());

  // Récupérer les catégories
  const { data: categories = [] } = useQuery({
    queryKey: ['product-categories'],
    queryFn: accountApi.getProductCategories,
    staleTime: 300000, // 5 minutes
  });

  // Récupérer les produits recommandés
  const { 
    data: recommendations = [], 
    isLoading, 
    refetch,
    isRefetching 
  } = useQuery({
    queryKey: ['enhanced-recommendations', filters],
    queryFn: () => accountApi.getSuggestedProducts(filters.category, {
      priceRange: filters.priceRange,
      rating: filters.minRating,
      sortBy: filters.sortBy,
      limit: filters.limit
    }),
    staleTime: 60000, // 1 minute
  });

  // Charger les préférences depuis localStorage
  useEffect(() => {
    const savedLikes = localStorage.getItem('liked-products');
    const savedDislikes = localStorage.getItem('disliked-products');
    
    if (savedLikes) {
      setLikedProducts(new Set(JSON.parse(savedLikes)));
    }
    if (savedDislikes) {
      setDislikedProducts(new Set(JSON.parse(savedDislikes)));
    }
  }, []);

  // Sauvegarder les préférences
  const savePreferences = () => {
    localStorage.setItem('liked-products', JSON.stringify([...likedProducts]));
    localStorage.setItem('disliked-products', JSON.stringify([...dislikedProducts]));
  };

  const handleLike = (productId: number) => {
    const newLiked = new Set(likedProducts);
    const newDisliked = new Set(dislikedProducts);
    
    if (newLiked.has(productId)) {
      newLiked.delete(productId);
    } else {
      newLiked.add(productId);
      newDisliked.delete(productId); // Retirer des dislikes si présent
    }
    
    setLikedProducts(newLiked);
    setDislikedProducts(newDisliked);
    savePreferences();
    
    toast({
      description: newLiked.has(productId) ? "Produit ajouté aux favoris" : "Produit retiré des favoris",
    });
  };

  const handleDislike = (productId: number) => {
    const newLiked = new Set(likedProducts);
    const newDisliked = new Set(dislikedProducts);
    
    if (newDisliked.has(productId)) {
      newDisliked.delete(productId);
    } else {
      newDisliked.add(productId);
      newLiked.delete(productId); // Retirer des likes si présent
    }
    
    setLikedProducts(newLiked);
    setDislikedProducts(newDisliked);
    savePreferences();
    
    toast({
      description: newDisliked.has(productId) ? "Produit marqué comme non intéressant" : "Marquage retiré",
    });
  };

  const resetFilters = () => {
    setFilters({
      priceRange: { min: 0, max: 1000000 },
      minRating: 0,
      sortBy: 'popularity',
      limit: 12
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const filteredRecommendations = recommendations.filter(product => 
    !dislikedProducts.has(product.id)
  );

  return (
    <div className="space-y-6">
      {/* En-tête avec filtres */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                Recommandations Personnalisées
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Découvrez des produits sélectionnés pour vous
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtres
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isRefetching}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefetching ? 'animate-spin' : ''}`} />
                Actualiser
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Panneau de filtres */}
        {showFilters && (
          <CardContent className="border-t">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Catégorie */}
              <div>
                <Label htmlFor="category">Catégorie</Label>
                <Select
                  value={filters.category || ""}
                  onValueChange={(value) => setFilters(prev => ({ 
                    ...prev, 
                    category: value || undefined 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes les catégories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Toutes les catégories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Fourchette de prix */}
              <div>
                <Label>Prix (BIF)</Label>
                <div className="space-y-2">
                  <Slider
                    value={[filters.priceRange.min, filters.priceRange.max]}
                    onValueChange={([min, max]) => setFilters(prev => ({
                      ...prev,
                      priceRange: { min, max }
                    }))}
                    max={1000000}
                    step={10000}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{formatCurrency(filters.priceRange.min)}</span>
                    <span>{formatCurrency(filters.priceRange.max)}</span>
                  </div>
                </div>
              </div>

              {/* Note minimale */}
              <div>
                <Label>Note minimale</Label>
                <Select
                  value={filters.minRating.toString()}
                  onValueChange={(value) => setFilters(prev => ({ 
                    ...prev, 
                    minRating: parseFloat(value) 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Toutes les notes</SelectItem>
                    <SelectItem value="3">3+ étoiles</SelectItem>
                    <SelectItem value="4">4+ étoiles</SelectItem>
                    <SelectItem value="4.5">4.5+ étoiles</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Tri */}
              <div>
                <Label>Trier par</Label>
                <Select
                  value={filters.sortBy}
                  onValueChange={(value: any) => setFilters(prev => ({ 
                    ...prev, 
                    sortBy: value 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="popularity">Popularité</SelectItem>
                    <SelectItem value="rating">Note</SelectItem>
                    <SelectItem value="price">Prix</SelectItem>
                    <SelectItem value="newest">Nouveautés</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-between items-center mt-4">
              <Button variant="outline" size="sm" onClick={resetFilters}>
                Réinitialiser
              </Button>
              <p className="text-sm text-gray-600">
                {filteredRecommendations.length} produit(s) trouvé(s)
              </p>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Grille des produits */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <Skeleton className="h-48 w-full mb-4" />
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-2" />
                <Skeleton className="h-6 w-1/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredRecommendations.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune recommandation trouvée
            </h3>
            <p className="text-gray-600 mb-4">
              Essayez d'ajuster vos filtres ou de parcourir notre catalogue.
            </p>
            <Button onClick={resetFilters}>
              Réinitialiser les filtres
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredRecommendations.map((product) => (
            <Card key={product.id} className="group hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                {/* Image du produit */}
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-product.jpg';
                    }}
                  />
                  
                  {/* Actions rapides */}
                  <div className="absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="icon"
                      variant={likedProducts.has(product.id) ? "default" : "secondary"}
                      className="h-8 w-8"
                      onClick={() => handleLike(product.id)}
                    >
                      <Heart className={`h-4 w-4 ${likedProducts.has(product.id) ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      size="icon"
                      variant={dislikedProducts.has(product.id) ? "destructive" : "secondary"}
                      className="h-8 w-8"
                      onClick={() => handleDislike(product.id)}
                    >
                      <ThumbsDown className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Badge catégorie */}
                  <div className="absolute bottom-2 left-2">
                    <Badge variant="secondary" className="text-xs">
                      {product.category}
                    </Badge>
                  </div>
                </div>

                {/* Contenu */}
                <div className="p-4">
                  <h3 className="font-semibold text-sm mb-2 line-clamp-2">
                    {product.name}
                  </h3>
                  
                  {/* Note */}
                  <div className="flex items-center gap-1 mb-2">
                    {renderStars(product.rating)}
                    <span className="text-sm text-gray-600 ml-1">
                      ({product.rating})
                    </span>
                  </div>

                  {/* Prix */}
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-lg font-bold text-primary">
                      {formatCurrency(product.price)}
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Link to={`/product/${product.id}`} className="flex-1">
                      <Button size="sm" variant="outline" className="w-full">
                        <Eye className="h-4 w-4 mr-1" />
                        Voir
                      </Button>
                    </Link>
                    <Button size="sm" className="flex-1">
                      <ShoppingCart className="h-4 w-4 mr-1" />
                      Ajouter
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default EnhancedProductRecommendations;
