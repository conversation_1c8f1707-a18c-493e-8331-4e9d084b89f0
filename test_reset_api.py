#!/usr/bin/env python3
"""
Script de test pour l'API de réinitialisation de mot de passe
"""

import requests
import json
import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import PasswordReset

def get_latest_token():
    """Récupère le dernier token valide"""
    try:
        # Vérifier d'abord le token spécifique
        specific_token = "2708e113-d750-4f85-9380-d738b2b038bb"
        try:
            specific_reset = PasswordReset.objects.get(token=specific_token)
            print(f"🔍 Statut du token spécifique {specific_token}:")
            print(f"   📧 Utilisateur: {specific_reset.user.email}")
            print(f"   ⏰ Expire le: {specific_reset.expires_at}")
            print(f"   🔒 Utilisé: {specific_reset.is_used}")
        except PasswordReset.DoesNotExist:
            print(f"❌ Token spécifique {specific_token} non trouvé")

        # Récupérer le dernier token non utilisé
        reset = PasswordReset.objects.filter(is_used=False).order_by('-created_at').first()
        if reset:
            print(f"\n✅ Dernier token valide trouvé: {reset.token}")
            print(f"📧 Utilisateur: {reset.user.email}")
            print(f"⏰ Expire le: {reset.expires_at}")
            print(f"🔒 Utilisé: {reset.is_used}")
            return reset.token
        else:
            print("\n❌ Aucun token valide trouvé")
            return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_reset_api(token):
    """Test l'API de réinitialisation"""
    url = "http://localhost:8000/api/v1/auth/password/reset/confirm/"
    
    payload = {
        "token": token,
        "new_password": "NewPassword123!",
        "confirm_password": "NewPassword123!"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"\n🧪 Test de l'API:")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"\n📊 Réponse:")
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Body: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Body (text): {response.text}")
            
        return response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur Django")
        print("Assurez-vous que le serveur fonctionne sur http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Test de l'API de réinitialisation de mot de passe")
    print("=" * 50)
    
    # Récupérer un token
    token = get_latest_token()
    
    if token:
        # Tester l'API
        success = test_reset_api(token)
        
        if success:
            print("\n✅ Test réussi!")
        else:
            print("\n❌ Test échoué!")
    else:
        print("\n❌ Impossible de tester sans token valide")
