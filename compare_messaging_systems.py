#!/usr/bin/env python3
"""
Script pour comparer le système de messagerie principal avec le système de messages des réclamations
"""

import sys
import os
import requests

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from messaging.models import Conversation, Message
from orders.models_extension import Claim, ClaimMessage

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def analyze_main_messaging_system():
    """Analyser le système de messagerie principal"""
    print("💬 ANALYSE DU SYSTÈME DE MESSAGERIE PRINCIPAL")
    print("=" * 60)
    
    try:
        # Modèles
        conversations = Conversation.objects.all()
        messages = Message.objects.all()
        
        print(f"📊 DONNÉES EN BASE:")
        print(f"   Conversations: {conversations.count()}")
        print(f"   Messages: {messages.count()}")
        
        print(f"\n🏗️ STRUCTURE DU MODÈLE CONVERSATION:")
        conversation_fields = [field.name for field in Conversation._meta.fields]
        print(f"   Champs: {', '.join(conversation_fields)}")
        
        print(f"\n🏗️ STRUCTURE DU MODÈLE MESSAGE:")
        message_fields = [field.name for field in Message._meta.fields]
        print(f"   Champs: {', '.join(message_fields)}")
        
        print(f"\n✨ FONCTIONNALITÉS PRINCIPALES:")
        print(f"   • Conversations avec sujet")
        print(f"   • Statuts (new, in_progress, responded, resolved, closed)")
        print(f"   • Priorités (low, medium, high, urgent)")
        print(f"   • Catégories et tags")
        print(f"   • Assignment à un admin")
        print(f"   • Intégration avec les commandes")
        print(f"   • Statut de lecture (admin/client)")
        print(f"   • Messages avec expéditeur (User)")
        print(f"   • Pièces jointes")
        print(f"   • WebSocket pour temps réel")
        
        return {
            'conversations_count': conversations.count(),
            'messages_count': messages.count(),
            'has_websocket': True,
            'has_attachments': True,
            'has_categories': True,
            'has_priorities': True,
            'has_assignment': True,
            'has_read_status': True
        }
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def analyze_claim_messaging_system():
    """Analyser le système de messages des réclamations"""
    print(f"\n🎫 ANALYSE DU SYSTÈME DE MESSAGES DES RÉCLAMATIONS")
    print("=" * 60)
    
    try:
        # Modèles
        claims = Claim.objects.all()
        claim_messages = ClaimMessage.objects.all()
        
        print(f"📊 DONNÉES EN BASE:")
        print(f"   Réclamations: {claims.count()}")
        print(f"   Messages: {claim_messages.count()}")
        
        print(f"\n🏗️ STRUCTURE DU MODÈLE CLAIM:")
        claim_fields = [field.name for field in Claim._meta.fields]
        print(f"   Champs: {', '.join(claim_fields)}")
        
        print(f"\n🏗️ STRUCTURE DU MODÈLE CLAIMMESSAGE:")
        claim_message_fields = [field.name for field in ClaimMessage._meta.fields]
        print(f"   Champs: {', '.join(claim_message_fields)}")
        
        print(f"\n✨ FONCTIONNALITÉS PRINCIPALES:")
        print(f"   • Réclamations avec sujet prédéfini")
        print(f"   • Statuts (new, in_progress, waiting_customer, waiting_staff, resolved, closed)")
        print(f"   • Pas de priorités")
        print(f"   • Pas de catégories/tags")
        print(f"   • Pas d'assignment")
        print(f"   • Intégration avec les commandes")
        print(f"   • Statut de lecture basique")
        print(f"   • Messages avec type d'expéditeur (customer/support/system)")
        print(f"   • Pièces jointes")
        print(f"   • Pas de WebSocket")
        
        return {
            'claims_count': claims.count(),
            'messages_count': claim_messages.count(),
            'has_websocket': False,
            'has_attachments': True,
            'has_categories': False,
            'has_priorities': False,
            'has_assignment': False,
            'has_read_status': True
        }
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def test_main_messaging_api():
    """Tester l'API du système de messagerie principal"""
    print(f"\n🔌 TEST DE L'API MESSAGERIE PRINCIPALE")
    print("=" * 50)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Test des conversations
            conversations_response = requests.get(f"{API_BASE_URL}/messaging/conversations/", headers=headers)
            
            if conversations_response.status_code == 200:
                conversations = conversations_response.json().get('results', [])
                print(f"   ✅ API conversations accessible")
                print(f"   📊 Conversations: {len(conversations)}")
                
                if conversations:
                    conv = conversations[0]
                    print(f"   📋 Première conversation:")
                    print(f"      Sujet: {conv.get('subject')}")
                    print(f"      Statut: {conv.get('status')}")
                    print(f"      Messages: {conv.get('message_count', 0)}")
                
                return True
            else:
                print(f"   ❌ Erreur API: {conversations_response.status_code}")
                return False
        else:
            print(f"   ❌ Échec authentification: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_claim_messaging_api():
    """Tester l'API du système de messages des réclamations"""
    print(f"\n🔌 TEST DE L'API MESSAGES RÉCLAMATIONS")
    print("=" * 50)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Test des réclamations
            claims_response = requests.get(f"{API_BASE_URL}/orders/claims/", headers=headers)
            
            if claims_response.status_code == 200:
                claims = claims_response.json().get('results', [])
                print(f"   ✅ API réclamations accessible")
                print(f"   📊 Réclamations: {len(claims)}")
                
                if claims:
                    claim = claims[0]
                    print(f"   📋 Première réclamation:")
                    print(f"      Numéro: {claim.get('claim_number')}")
                    print(f"      Statut: {claim.get('status_display')}")
                    
                    # Test des messages
                    claim_id = claim.get('id')
                    messages_response = requests.get(f"{API_BASE_URL}/orders/claims/{claim_id}/messages/", headers=headers)
                    
                    if messages_response.status_code == 200:
                        messages = messages_response.json()
                        print(f"      Messages: {len(messages)}")
                        return True
                    else:
                        print(f"   ❌ Erreur messages: {messages_response.status_code}")
                        return False
                
                return True
            else:
                print(f"   ❌ Erreur API: {claims_response.status_code}")
                return False
        else:
            print(f"   ❌ Échec authentification: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def compare_systems(main_data, claim_data):
    """Comparer les deux systèmes"""
    print(f"\n⚖️ COMPARAISON DES SYSTÈMES")
    print("=" * 50)
    
    print(f"📊 FONCTIONNALITÉS:")
    features = [
        ('WebSocket temps réel', main_data.get('has_websocket'), claim_data.get('has_websocket')),
        ('Pièces jointes', main_data.get('has_attachments'), claim_data.get('has_attachments')),
        ('Catégories', main_data.get('has_categories'), claim_data.get('has_categories')),
        ('Priorités', main_data.get('has_priorities'), claim_data.get('has_priorities')),
        ('Assignment admin', main_data.get('has_assignment'), claim_data.get('has_assignment')),
        ('Statut de lecture', main_data.get('has_read_status'), claim_data.get('has_read_status')),
    ]
    
    for feature, main_has, claim_has in features:
        main_icon = '✅' if main_has else '❌'
        claim_icon = '✅' if claim_has else '❌'
        print(f"   {feature}:")
        print(f"      Messagerie principale: {main_icon}")
        print(f"      Messages réclamations: {claim_icon}")
    
    print(f"\n📈 DONNÉES:")
    print(f"   Messagerie principale:")
    print(f"      Conversations: {main_data.get('conversations_count', 0)}")
    print(f"      Messages: {main_data.get('messages_count', 0)}")
    print(f"   Messages réclamations:")
    print(f"      Réclamations: {claim_data.get('claims_count', 0)}")
    print(f"      Messages: {claim_data.get('messages_count', 0)}")

def main():
    print("🔍 COMPARAISON DES SYSTÈMES DE MESSAGERIE")
    print("=" * 70)
    
    # 1. Analyser le système principal
    main_data = analyze_main_messaging_system()
    
    # 2. Analyser le système des réclamations
    claim_data = analyze_claim_messaging_system()
    
    # 3. Tester les APIs
    main_api_ok = test_main_messaging_api()
    claim_api_ok = test_claim_messaging_api()
    
    # 4. Comparer les systèmes
    if main_data and claim_data:
        compare_systems(main_data, claim_data)
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Système principal analysé: {'✅' if main_data else '❌'}")
    print(f"   Système réclamations analysé: {'✅' if claim_data else '❌'}")
    print(f"   API principale testée: {'✅' if main_api_ok else '❌'}")
    print(f"   API réclamations testée: {'✅' if claim_api_ok else '❌'}")
    
    print(f"\n🎯 RECOMMANDATIONS:")
    if main_data and claim_data:
        print(f"   💡 Le système de réclamations pourrait bénéficier de:")
        if not claim_data.get('has_websocket'):
            print(f"      • WebSocket pour les mises à jour temps réel")
        if not claim_data.get('has_categories'):
            print(f"      • Système de catégories pour organiser les réclamations")
        if not claim_data.get('has_priorities'):
            print(f"      • Système de priorités pour traiter les urgences")
        if not claim_data.get('has_assignment'):
            print(f"      • Assignment des réclamations à des agents spécifiques")
        
        print(f"\n   ✅ Points forts du système de réclamations:")
        print(f"      • Intégration spécifique aux commandes")
        print(f"      • Workflow de statuts adapté au support")
        print(f"      • Pièces jointes fonctionnelles")
        print(f"      • API robuste et permissions correctes")

if __name__ == "__main__":
    main()
