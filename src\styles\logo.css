/* Styles pour les logos JosNet */

/* Optimisation des SVG */
.josnet-logo svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Animation hover pour les logos cliquables */
.josnet-logo-link {
  transition: all 0.2s ease-in-out;
}

.josnet-logo-link:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* Styles pour les différentes variantes */
.josnet-logo-full {
  min-width: 180px;
}

.josnet-logo-compact {
  min-width: 100px;
}

.josnet-logo-icon {
  min-width: 32px;
  min-height: 32px;
}

/* Responsive */
@media (max-width: 768px) {
  .josnet-logo-full {
    min-width: 150px;
  }
  
  .josnet-logo-compact {
    min-width: 80px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .josnet-logo-text {
    color: #ffffff;
  }
}

/* Print styles */
@media print {
  .josnet-logo svg {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }
}

/* Accessibilité */
.josnet-logo img,
.josnet-logo svg {
  alt: "JosNet Network Logo";
}

/* Animation de chargement */
.josnet-logo-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Styles pour les différents contextes */
.navbar .josnet-logo {
  height: 40px;
}

.footer .josnet-logo {
  height: 32px;
}

.hero .josnet-logo {
  height: 60px;
}

/* Optimisation pour les écrans haute résolution */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .josnet-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
