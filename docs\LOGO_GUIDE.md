# 🎨 Guide d'utilisation des Logos JosNet

## Vue d'ensemble

Ce guide explique comment utiliser les différents variants du logo JosNet dans votre application.

## 📁 Structure des fichiers

```
public/assets/images/
├── josnet-logo.svg              # Logo complet avec tagline
├── josnet-logo-compact.svg      # Logo compact pour navbar
├── josnet-favicon.svg           # Icône/favicon
└── generated/                   # Versions PNG générées
    ├── josnet-logo-sm.png
    ├── josnet-logo-md.png
    ├── josnet-logo-lg.png
    └── ...

src/components/ui/
├── Logo.tsx                     # Composant principal
└── LogoVariants.tsx            # SVG intégrés
```

## 🎯 Variants disponibles

### 1. Logo <PERSON>mplet (`full`)
- **Usage** : Page d'accueil, footer, documents officiels
- **Taille recommandée** : 200x60px minimum
- **Contient** : Icône + texte JOSNET NETWORK + tagline

```tsx
<Logo variant="full" size="lg" showTagline />
```

### 2. Logo Compact (`compact`)
- **Usage** : Navbar, sidebar, emails
- **Taille recommandée** : 120x40px
- **Contient** : Icône + texte JOSNET NETWORK

```tsx
<Logo variant="compact" size="md" linkTo="/" />
```

### 3. Icône (`icon`)
- **Usage** : Favicon, notifications, très petits espaces
- **Taille recommandée** : 32x32px
- **Contient** : Icône seule

```tsx
<Logo variant="icon" size="sm" />
```

### 4. Version Texte (`text`)
- **Usage** : Fallback, mode accessibilité
- **Contient** : Texte stylisé uniquement

```tsx
<Logo variant="text" showTagline />
```

## 📏 Tailles disponibles

| Taille | Classe CSS | Hauteur |
|--------|------------|---------|
| `sm`   | `h-8`      | 32px    |
| `md`   | `h-10`     | 40px    |
| `lg`   | `h-12`     | 48px    |

## 🎨 Couleurs du logo

### Couleurs principales
- **Bleu principal** : `#2563eb` (text-primary)
- **Orange accent** : `#f59e0b` (text-accent)
- **Gris texte** : `#6b7280` (text-gray-500)

### Variations
- **Fond blanc** : Logo coloré
- **Fond sombre** : Logo avec texte blanc
- **Monochrome** : Version en niveaux de gris

## 💻 Utilisation dans le code

### Composant Logo principal

```tsx
import Logo from '@/components/ui/Logo';

// Logo avec lien
<Logo variant="compact" size="md" linkTo="/" />

// Logo sans lien
<Logo variant="full" size="lg" showTagline />

// Logo avec classes personnalisées
<Logo variant="icon" size="sm" className="mx-auto" />
```

### SVG intégrés (pour performance)

```tsx
import { JosNetCompact, JosNetFull, JosNetIcon } from '@/components/ui/LogoVariants';

<JosNetCompact className="h-10" />
<JosNetFull className="h-12" />
<JosNetIcon className="h-8" />
```

## 🌐 Contextes d'utilisation

### Navbar
```tsx
<Logo variant="compact" size="md" linkTo="/" />
```

### Footer
```tsx
<Logo variant="compact" size="md" />
```

### Hero Section
```tsx
<Logo variant="full" size="lg" showTagline />
```

### Favicon
```html
<link rel="icon" type="image/svg+xml" href="/assets/images/josnet-favicon.svg" />
```

## 📱 Responsive Design

Le logo s'adapte automatiquement aux différentes tailles d'écran :

```css
/* Mobile */
@media (max-width: 768px) {
  .josnet-logo-full { min-width: 150px; }
  .josnet-logo-compact { min-width: 80px; }
}
```

## ♿ Accessibilité

- Tous les logos incluent un attribut `alt` approprié
- Support du mode sombre
- Contraste suffisant sur tous les fonds
- Taille minimum respectée pour la lisibilité

## 🔧 Génération d'assets

Pour générer les versions PNG :

```bash
# Installer sharp (si pas déjà fait)
npm install sharp --save-dev

# Générer les assets
node scripts/generate-logo-assets.js
```

Cela génère :
- Favicons en différentes tailles
- Versions PNG pour compatibilité
- Manifest.json mis à jour

## 📋 Checklist d'intégration

- [ ] Logo dans la navbar
- [ ] Logo dans le footer
- [ ] Favicon configuré
- [ ] Manifest.json à jour
- [ ] Meta tags Open Graph
- [ ] Tests sur fond clair/sombre
- [ ] Tests responsive
- [ ] Vérification accessibilité

## 🎨 Personnalisation

### Modifier les couleurs

```tsx
// Via les classes Tailwind
<Logo variant="text" className="text-blue-600" />

// Via CSS custom
.custom-logo {
  --logo-primary: #your-color;
  --logo-accent: #your-accent;
}
```

### Ajouter des animations

```css
.logo-hover {
  transition: transform 0.2s ease;
}

.logo-hover:hover {
  transform: scale(1.05);
}
```

## 🚀 Performance

### Optimisations appliquées
- SVG intégrés pour éviter les requêtes réseau
- Lazy loading pour les images
- Compression optimale des PNG
- Fallback automatique en cas d'erreur

### Métriques
- **Taille SVG** : ~2KB (compressé)
- **Temps de chargement** : <50ms
- **Score Lighthouse** : 100/100

## 📞 Support

Pour toute question sur l'utilisation des logos :
- Consulter la page de démonstration : `/logo-demo`
- Vérifier les exemples dans `LogoDemo.tsx`
- Contacter l'équipe design

---

**Dernière mise à jour** : [Date actuelle]  
**Version** : 1.0.0
