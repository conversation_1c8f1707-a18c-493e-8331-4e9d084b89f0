import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { 
  MessageSquare, 
  ThumbsUp, 
  ThumbsDown, 
  Star,
  Send,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface FeedbackData {
  rating: number;
  relevance: 'very_relevant' | 'relevant' | 'somewhat_relevant' | 'not_relevant';
  feedback: string;
  wouldRecommend: boolean;
}

interface RecommendationFeedbackProps {
  onSubmit?: (feedback: FeedbackData) => void;
  className?: string;
}

const RecommendationFeedback: React.FC<RecommendationFeedbackProps> = ({ 
  onSubmit,
  className 
}) => {
  const { toast } = useToast();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [feedback, setFeedback] = useState<FeedbackData>({
    rating: 0,
    relevance: 'relevant',
    feedback: '',
    wouldRecommend: true
  });

  const handleRatingClick = (rating: number) => {
    setFeedback(prev => ({ ...prev, rating }));
  };

  const handleSubmit = async () => {
    if (feedback.rating === 0) {
      toast({
        title: "Évaluation requise",
        description: "Veuillez donner une note avant de soumettre votre feedback.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Simuler l'envoi du feedback
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (onSubmit) {
        onSubmit(feedback);
      }

      setIsSubmitted(true);
      toast({
        title: "Merci pour votre feedback !",
        description: "Vos commentaires nous aident à améliorer nos recommandations.",
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'envoi de votre feedback.",
        variant: "destructive"
      });
    }
  };

  const renderStars = () => {
    return Array.from({ length: 5 }, (_, i) => (
      <button
        key={i}
        onClick={() => handleRatingClick(i + 1)}
        className="focus:outline-none"
      >
        <Star
          className={`h-6 w-6 transition-colors ${
            i < feedback.rating 
              ? 'text-yellow-400 fill-current' 
              : 'text-gray-300 hover:text-yellow-200'
          }`}
        />
      </button>
    ));
  };

  if (isSubmitted) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Feedback envoyé !
          </h3>
          <p className="text-gray-600">
            Merci d'avoir pris le temps de nous faire part de vos commentaires.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-primary" />
          Évaluez nos recommandations
        </CardTitle>
        <p className="text-sm text-gray-600">
          Aidez-nous à améliorer la qualité de nos suggestions
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Évaluation par étoiles */}
        <div>
          <Label className="text-base font-medium">
            Comment évaluez-vous nos recommandations ?
          </Label>
          <div className="flex items-center gap-1 mt-2">
            {renderStars()}
            <span className="ml-2 text-sm text-gray-600">
              {feedback.rating > 0 && (
                <>
                  {feedback.rating}/5 - {
                    feedback.rating === 1 ? 'Très mauvais' :
                    feedback.rating === 2 ? 'Mauvais' :
                    feedback.rating === 3 ? 'Correct' :
                    feedback.rating === 4 ? 'Bon' : 'Excellent'
                  }
                </>
              )}
            </span>
          </div>
        </div>

        {/* Pertinence */}
        <div>
          <Label className="text-base font-medium mb-3 block">
            Les produits recommandés correspondent-ils à vos besoins ?
          </Label>
          <RadioGroup
            value={feedback.relevance}
            onValueChange={(value: any) => setFeedback(prev => ({ 
              ...prev, 
              relevance: value 
            }))}
            className="space-y-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="very_relevant" id="very_relevant" />
              <Label htmlFor="very_relevant">Très pertinent</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="relevant" id="relevant" />
              <Label htmlFor="relevant">Pertinent</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="somewhat_relevant" id="somewhat_relevant" />
              <Label htmlFor="somewhat_relevant">Moyennement pertinent</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="not_relevant" id="not_relevant" />
              <Label htmlFor="not_relevant">Pas pertinent</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Recommandation */}
        <div>
          <Label className="text-base font-medium mb-3 block">
            Recommanderiez-vous notre système de suggestions ?
          </Label>
          <div className="flex gap-4">
            <Button
              variant={feedback.wouldRecommend ? "default" : "outline"}
              onClick={() => setFeedback(prev => ({ ...prev, wouldRecommend: true }))}
              className="flex items-center gap-2"
            >
              <ThumbsUp className="h-4 w-4" />
              Oui
            </Button>
            <Button
              variant={!feedback.wouldRecommend ? "destructive" : "outline"}
              onClick={() => setFeedback(prev => ({ ...prev, wouldRecommend: false }))}
              className="flex items-center gap-2"
            >
              <ThumbsDown className="h-4 w-4" />
              Non
            </Button>
          </div>
        </div>

        {/* Commentaires */}
        <div>
          <Label htmlFor="feedback-text" className="text-base font-medium">
            Commentaires additionnels (optionnel)
          </Label>
          <Textarea
            id="feedback-text"
            placeholder="Partagez vos suggestions pour améliorer nos recommandations..."
            value={feedback.feedback}
            onChange={(e) => setFeedback(prev => ({ 
              ...prev, 
              feedback: e.target.value 
            }))}
            className="mt-2"
            rows={4}
          />
        </div>

        {/* Statistiques de feedback */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">
            Votre contribution compte
          </h4>
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">95%</div>
              <div className="text-sm text-gray-600">Satisfaction client</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">1,247</div>
              <div className="text-sm text-gray-600">Feedbacks reçus</div>
            </div>
          </div>
        </div>

        {/* Bouton de soumission */}
        <Button 
          onClick={handleSubmit}
          className="w-full"
          disabled={feedback.rating === 0}
        >
          <Send className="h-4 w-4 mr-2" />
          Envoyer le feedback
        </Button>

        {feedback.rating === 0 && (
          <div className="flex items-center gap-2 text-amber-600 text-sm">
            <AlertCircle className="h-4 w-4" />
            Veuillez donner une note avant de soumettre
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RecommendationFeedback;
