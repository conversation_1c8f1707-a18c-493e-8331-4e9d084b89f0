import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import {
  Box, Typography, Paper, Grid, Divider, Chip, Button,
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  Tabs, Tab, CircularProgress, Dialog, DialogTitle, DialogContent,
  DialogActions, TextField, MenuItem, Alert
} from '@/components/mui-compatibility';
import {
  Truck as ShippingIcon,
  CreditCard as PaymentIcon,
  Receipt as ReceiptIcon,
  History as HistoryIcon,
  RefreshCcw as RefundIcon,
  X as CancelIcon,
  Printer as PrintIcon,
  Pencil as EditIcon
} from 'lucide-react';
import orderApi, { Order, OrderStatusHistory, OrderItem, OrderRefund } from '../../services/orderApi';
import { formatDate, formatCurrency } from '../../utils/formatters';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`order-tabpanel-${index}`}
      aria-labelledby={`order-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);

  // Status update dialog
  const [statusDialogOpen, setStatusDialogOpen] = useState<boolean>(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [statusNotes, setStatusNotes] = useState<string>('');

  // Cancel dialog
  const [cancelDialogOpen, setCancelDialogOpen] = useState<boolean>(false);
  const [cancelReason, setCancelReason] = useState<string>('');

  // Refund dialog
  const [refundDialogOpen, setRefundDialogOpen] = useState<boolean>(false);
  const [refundAmount, setRefundAmount] = useState<number>(0);
  const [refundType, setRefundType] = useState<string>('full');
  const [refundReason, setRefundReason] = useState<string>('');

  const statusColors: Record<string, string> = {
    pending: 'warning',
    processing: 'info',
    shipped: 'primary',
    delivered: 'success',
    cancelled: 'error',
    refunded: 'secondary',
    partially_refunded: 'secondary',
    on_hold: 'default',
  };

  const fetchOrder = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const data = await orderApi.getOrder(parseInt(id));
      setOrder(data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch order details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrder();
  }, [id]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleUpdateStatus = async () => {
    if (!id || !newStatus) return;

    try {
      await orderApi.addOrderStatus(parseInt(id), newStatus, statusNotes);
      setStatusDialogOpen(false);
      setNewStatus('');
      setStatusNotes('');
      fetchOrder();
    } catch (err: any) {
      setError(err.message || 'Failed to update order status');
    }
  };

  const handleCancelOrder = async () => {
    if (!id || !cancelReason) return;

    try {
      await orderApi.cancelOrder(parseInt(id), cancelReason);
      setCancelDialogOpen(false);
      setCancelReason('');
      fetchOrder();
    } catch (err: any) {
      setError(err.message || 'Failed to cancel order');
    }
  };

  const handleCreateRefund = async () => {
    if (!id || !refundReason || refundAmount <= 0) return;

    try {
      const refundData: OrderRefund = {
        order: parseInt(id),
        amount: refundAmount,
        status: 'pending',
        refund_type: refundType,
        reason: refundReason
      };

      await orderApi.createRefund(refundData);
      setRefundDialogOpen(false);
      setRefundAmount(0);
      setRefundType('full');
      setRefundReason('');
      fetchOrder();
    } catch (err: any) {
      setError(err.message || 'Failed to create refund');
    }
  };

  if (loading && !order) {
    return <CircularProgress />;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!order) {
    return <Alert severity="warning">Order not found</Alert>;
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4">
          Order #{order.order_number}
          <Chip
            label={order.status_display || order.status}
            color={statusColors[order.status || 'pending'] as any}
            size="small"
            sx={{ ml: 2 }}
          />
        </Typography>
        <Box>
          <Button
            startIcon={<PrintIcon />}
            variant="outlined"
            sx={{ mr: 1 }}
            onClick={() => window.print()}
          >
            Print
          </Button>
          <Button
            component={Link}
            to={`/admin/orders/${id}/edit`}
            startIcon={<EditIcon />}
            variant="outlined"
            sx={{ mr: 1 }}
          >
            Edit
          </Button>
          <Button
            onClick={() => setStatusDialogOpen(true)}
            variant="contained"
            color="primary"
          >
            Update Status
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Order Summary</Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">Date</Typography>
                <Typography variant="body1">{formatDate(order.created_at)}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">Payment Method</Typography>
                <Typography variant="body1">{order.payment_method_display || order.payment_method}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">Payment Status</Typography>
                <Typography variant="body1">{order.payment_status_display || order.payment_status}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">Payment Reference</Typography>
                <Typography variant="body1">{order.payment_reference || 'N/A'}</Typography>
              </Grid>
              {order.tracking_number && (
                <>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">Tracking Number</Typography>
                    <Typography variant="body1">{order.tracking_number}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">Shipping Carrier</Typography>
                    <Typography variant="body1">{order.shipping_carrier || 'N/A'}</Typography>
                  </Grid>
                </>
              )}
            </Grid>
          </Paper>

          <Paper sx={{ mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="order tabs">
              <Tab icon={<ReceiptIcon />} label="Items" />
              <Tab icon={<HistoryIcon />} label="History" />
              <Tab icon={<RefundIcon />} label="Refunds" />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell>SKU</TableCell>
                      <TableCell align="right">Price</TableCell>
                      <TableCell align="right">Quantity</TableCell>
                      <TableCell align="right">Discount</TableCell>
                      <TableCell align="right">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {order.items && order.items.map((item: OrderItem) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          {item.product_name}
                          {item.variant_name && (
                            <Typography variant="caption" display="block" color="textSecondary">
                              Variant: {item.variant_name}
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell>{item.sku}</TableCell>
                        <TableCell align="right">{formatCurrency(item.price)}</TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell align="right">{formatCurrency(item.discount_amount)}</TableCell>
                        <TableCell align="right">{formatCurrency(item.final_price || 0)}</TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={4} />
                      <TableCell align="right">Subtotal</TableCell>
                      <TableCell align="right">{formatCurrency(order.subtotal)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell colSpan={4} />
                      <TableCell align="right">Shipping</TableCell>
                      <TableCell align="right">{formatCurrency(order.shipping_cost)}</TableCell>
                    </TableRow>
                    {order.discount_amount > 0 && (
                      <TableRow>
                        <TableCell colSpan={4} />
                        <TableCell align="right">Discount</TableCell>
                        <TableCell align="right">-{formatCurrency(order.discount_amount)}</TableCell>
                      </TableRow>
                    )}
                    <TableRow>
                      <TableCell colSpan={4} />
                      <TableCell align="right">Tax</TableCell>
                      <TableCell align="right">{formatCurrency(order.tax_amount)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell colSpan={4} />
                      <TableCell align="right"><strong>Total</strong></TableCell>
                      <TableCell align="right"><strong>{formatCurrency(order.total)}</strong></TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              {order.status_history && order.status_history.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Date</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Notes</TableCell>
                        <TableCell>Updated By</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {order.status_history.map((history: OrderStatusHistory) => (
                        <TableRow key={history.id}>
                          <TableCell>{formatDate(history.created_at)}</TableCell>
                          <TableCell>
                            <Chip
                              label={history.status_display || history.status}
                              color={statusColors[history.status] as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{history.notes || 'No notes'}</TableCell>
                          <TableCell>{history.created_by_name || 'System'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography>No status history available</Typography>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    setRefundAmount(order.total);
                    setRefundDialogOpen(true);
                  }}
                >
                  Create Refund
                </Button>
              </Box>

              {order.refunds && order.refunds.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Refund #</TableCell>
                        <TableCell>Date</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Reason</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {order.refunds.map((refund: OrderRefund) => (
                        <TableRow key={refund.id}>
                          <TableCell>{refund.refund_number}</TableCell>
                          <TableCell>{formatDate(refund.created_at)}</TableCell>
                          <TableCell>{formatCurrency(refund.amount)}</TableCell>
                          <TableCell>{refund.refund_type_display || refund.refund_type}</TableCell>
                          <TableCell>
                            <Chip
                              label={refund.status_display || refund.status}
                              color={
                                refund.status === 'completed' ? 'success' :
                                refund.status === 'approved' ? 'primary' :
                                refund.status === 'rejected' ? 'error' : 'default'
                              }
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{refund.reason}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography>No refunds available</Typography>
              )}
            </TabPanel>
          </Paper>

          {order.customer_notes && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>Customer Notes</Typography>
              <Typography>{order.customer_notes}</Typography>
            </Paper>
          )}

          {order.admin_notes && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>Admin Notes</Typography>
              <Typography>{order.admin_notes}</Typography>
            </Paper>
          )}
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Customer Information</Typography>
            <Typography variant="body1">
              {order.billing_first_name} {order.billing_last_name}
            </Typography>
            <Typography variant="body2" color="textSecondary">{order.email}</Typography>
            {order.phone && (
              <Typography variant="body2" color="textSecondary">{order.phone}</Typography>
            )}
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6">Shipping Address</Typography>
              <ShippingIcon color="action" />
            </Box>
            <Box>
              <Typography variant="body1">
                {order.shipping_first_name} {order.shipping_last_name}
              </Typography>
              {order.shipping_company && (
                <Typography variant="body2">{order.shipping_company}</Typography>
              )}
              <Typography variant="body2">{order.shipping_address_line1}</Typography>
              {order.shipping_address_line2 && (
                <Typography variant="body2">{order.shipping_address_line2}</Typography>
              )}
              <Typography variant="body2">
                {order.shipping_city}, {order.shipping_state && `${order.shipping_state}, `}
                {order.shipping_postal_code}
              </Typography>
              <Typography variant="body2">{order.shipping_country}</Typography>
            </Box>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6">Billing Address</Typography>
              <PaymentIcon color="action" />
            </Box>
            <Box>
              <Typography variant="body1">
                {order.billing_first_name} {order.billing_last_name}
              </Typography>
              {order.billing_company && (
                <Typography variant="body2">{order.billing_company}</Typography>
              )}
              <Typography variant="body2">{order.billing_address_line1}</Typography>
              {order.billing_address_line2 && (
                <Typography variant="body2">{order.billing_address_line2}</Typography>
              )}
              <Typography variant="body2">
                {order.billing_city}, {order.billing_state && `${order.billing_state}, `}
                {order.billing_postal_code}
              </Typography>
              <Typography variant="body2">{order.billing_country}</Typography>
            </Box>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Actions</Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<HistoryIcon />}
                onClick={() => setStatusDialogOpen(true)}
                fullWidth
              >
                Update Status
              </Button>

              {['pending', 'processing', 'on_hold'].includes(order.status || '') && (
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<CancelIcon />}
                  onClick={() => setCancelDialogOpen(true)}
                  fullWidth
                >
                  Cancel Order
                </Button>
              )}

              {['processing', 'shipped', 'delivered'].includes(order.status || '') && (
                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<RefundIcon />}
                  onClick={() => {
                    setRefundAmount(order.total);
                    setRefundDialogOpen(true);
                  }}
                  fullWidth
                >
                  Create Refund
                </Button>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Status Update Dialog */}
      <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)}>
        <DialogTitle>Update Order Status</DialogTitle>
        <DialogContent>
          <TextField
            select
            label="Status"
            value={newStatus}
            onChange={(e) => setNewStatus(e.target.value)}
            fullWidth
            margin="normal"
          >
            <MenuItem value="pending">Pending</MenuItem>
            <MenuItem value="processing">Processing</MenuItem>
            <MenuItem value="shipped">Shipped</MenuItem>
            <MenuItem value="delivered">Delivered</MenuItem>
            <MenuItem value="cancelled">Cancelled</MenuItem>
            <MenuItem value="refunded">Refunded</MenuItem>
            <MenuItem value="partially_refunded">Partially Refunded</MenuItem>
            <MenuItem value="on_hold">On Hold</MenuItem>
          </TextField>
          <TextField
            label="Notes"
            value={statusNotes}
            onChange={(e) => setStatusNotes(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdateStatus} color="primary" variant="contained">
            Update
          </Button>
        </DialogActions>
      </Dialog>

      {/* Cancel Order Dialog */}
      <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>
        <DialogTitle>Cancel Order</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Are you sure you want to cancel this order? This action cannot be undone.
          </Typography>
          <TextField
            label="Reason for Cancellation"
            value={cancelReason}
            onChange={(e) => setCancelReason(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={3}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCancelDialogOpen(false)}>No, Keep Order</Button>
          <Button onClick={handleCancelOrder} color="error" variant="contained">
            Yes, Cancel Order
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Refund Dialog */}
      <Dialog open={refundDialogOpen} onClose={() => setRefundDialogOpen(false)}>
        <DialogTitle>Create Refund</DialogTitle>
        <DialogContent>
          <TextField
            select
            label="Refund Type"
            value={refundType}
            onChange={(e) => setRefundType(e.target.value)}
            fullWidth
            margin="normal"
          >
            <MenuItem value="full">Full Refund</MenuItem>
            <MenuItem value="partial">Partial Refund</MenuItem>
            <MenuItem value="item">Item Refund</MenuItem>
          </TextField>
          <TextField
            label="Amount"
            type="number"
            value={refundAmount}
            onChange={(e) => setRefundAmount(parseFloat(e.target.value))}
            fullWidth
            margin="normal"
            InputProps={{
              startAdornment: <Typography sx={{ mr: 1 }}>$</Typography>,
            }}
          />
          <TextField
            label="Reason for Refund"
            value={refundReason}
            onChange={(e) => setRefundReason(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={3}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRefundDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateRefund} color="primary" variant="contained">
            Create Refund
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OrderDetail;
