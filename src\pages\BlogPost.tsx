import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Calendar, 
  User, 
  ArrowLeft, 
  Clock, 
  Tag, 
  Share2,
  Facebook,
  Twitter,
  Linkedin,
  Copy
} from "lucide-react";
import { useBlogPostPage } from "@/hooks/useBlog";
import { useState } from "react";

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  const [copySuccess, setCopySuccess] = useState(false);
  
  const {
    post,
    relatedPosts,
    isLoading,
    error,
    formatDate,
    getAuthorName,
    getImageUrl,
    getReadingTime,
  } = useBlogPostPage(slug || '');

  const handleShare = async (platform: string) => {
    if (!post) return;
    
    const url = window.location.href;
    const title = post.title;
    
    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(url);
          setCopySuccess(true);
          setTimeout(() => setCopySuccess(false), 2000);
        } catch (err) {
          console.error('Erreur lors de la copie:', err);
        }
        break;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow">
          <div className="container mx-auto px-4 py-8">
            {/* Breadcrumb skeleton */}
            <div className="mb-8">
              <Skeleton className="h-4 w-64" />
            </div>
            
            {/* Article header skeleton */}
            <div className="max-w-4xl mx-auto mb-8">
              <Skeleton className="h-10 w-3/4 mb-4" />
              <div className="flex items-center space-x-4 mb-6">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-20" />
              </div>
              <Skeleton className="h-64 w-full rounded-lg" />
            </div>
            
            {/* Content skeleton */}
            <div className="max-w-4xl mx-auto">
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow">
          <div className="container mx-auto px-4 py-16 text-center">
            <h1 className="text-3xl font-bold mb-4">Article non trouvé</h1>
            <p className="text-gray-600 mb-8">
              L'article que vous recherchez n'existe pas ou a été supprimé.
            </p>
            <Link to="/blog">
              <Button>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Retour au blog
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        <div className="container mx-auto px-4 py-8">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Link to="/" className="hover:text-primary">Accueil</Link>
              <span>/</span>
              <Link to="/blog" className="hover:text-primary">Blog</Link>
              <span>/</span>
              <span className="text-gray-900">{post.title}</span>
            </div>
          </nav>
          
          {/* Article header */}
          <div className="max-w-4xl mx-auto mb-8">
            <div className="mb-6">
              <Link to="/blog">
                <Button variant="ghost" className="mb-4">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour au blog
                </Button>
              </Link>
              
              <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
                {post.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
                <span className="flex items-center">
                  <Calendar size={16} className="mr-2" />
                  {formatDate(post.created_at)}
                </span>
                <span className="flex items-center">
                  <User size={16} className="mr-2" />
                  {getAuthorName(post.author)}
                </span>
                <span className="flex items-center">
                  <Clock size={16} className="mr-2" />
                  {getReadingTime()}
                </span>
                {post.category && (
                  <span className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-medium">
                    {post.category.name}
                  </span>
                )}
              </div>
            </div>
            
            {/* Featured image */}
            {post.featured_image && (
              <div className="mb-8">
                <img 
                  src={getImageUrl(post.featured_image)} 
                  alt={post.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                />
              </div>
            )}
          </div>
          
          {/* Article content */}
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Main content */}
              <div className="lg:col-span-3">
                <div 
                  className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-primary prose-strong:text-gray-900"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />
                
                {/* Tags */}
                {post.tags && post.tags.length > 0 && (
                  <div className="mt-8 pt-8 border-t border-gray-200">
                    <h3 className="text-lg font-semibold mb-4 flex items-center">
                      <Tag className="mr-2 h-5 w-5" />
                      Tags
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map(tag => (
                        <span 
                          key={tag.id} 
                          className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors"
                        >
                          {tag.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Share buttons */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Share2 className="mr-2 h-5 w-5" />
                    Partager cet article
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleShare('facebook')}
                      className="flex items-center"
                    >
                      <Facebook className="mr-2 h-4 w-4" />
                      Facebook
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleShare('twitter')}
                      className="flex items-center"
                    >
                      <Twitter className="mr-2 h-4 w-4" />
                      Twitter
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleShare('linkedin')}
                      className="flex items-center"
                    >
                      <Linkedin className="mr-2 h-4 w-4" />
                      LinkedIn
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleShare('copy')}
                      className="flex items-center"
                    >
                      <Copy className="mr-2 h-4 w-4" />
                      {copySuccess ? 'Copié !' : 'Copier le lien'}
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Sidebar */}
              <div className="lg:col-span-1">
                {/* Related articles */}
                {relatedPosts.length > 0 && (
                  <div className="sticky top-8">
                    <h3 className="text-lg font-semibold mb-4">Articles similaires</h3>
                    <div className="space-y-4">
                      {relatedPosts.map(relatedPost => (
                        <Card key={relatedPost.id} className="overflow-hidden">
                          <Link to={`/blog/${relatedPost.slug}`}>
                            <div className="h-32 overflow-hidden">
                              <img 
                                src={getImageUrl(relatedPost.featured_image)} 
                                alt={relatedPost.title}
                                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                              />
                            </div>
                            <CardContent className="p-4">
                              <h4 className="font-medium text-sm mb-2 line-clamp-2 hover:text-primary transition-colors">
                                {relatedPost.title}
                              </h4>
                              <p className="text-xs text-gray-600">
                                {formatDate(relatedPost.created_at)}
                              </p>
                            </CardContent>
                          </Link>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default BlogPost;
