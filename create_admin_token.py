#!/usr/bin/env python3
"""
Script simple pour créer un token admin
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model

def create_admin_and_token():
    """Créer un admin et son token"""
    print("🔑 CRÉATION D'UN ADMIN ET TOKEN")
    print("=" * 50)
    
    User = get_user_model()
    
    # C<PERSON>er ou récupérer l'utilisateur admin
    admin_email = '<EMAIL>'
    admin_password = 'admin123'
    
    try:
        admin_user = User.objects.get(email=admin_email)
        print(f"✅ Utilisateur admin existant: {admin_user.email}")
    except User.DoesNotExist:
        admin_user = User.objects.create_superuser(
            email=admin_email,
            password=admin_password
        )
        print(f"✅ Utilisateur admin créé: {admin_user.email}")
    
    # C<PERSON>er le token
    try:
        from rest_framework.authtoken.models import Token
        token, created = Token.objects.get_or_create(user=admin_user)
        
        if created:
            print(f"✅ Token créé: {token.key}")
        else:
            print(f"✅ Token existant: {token.key}")
        
        return token.key
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du token: {e}")
        return None

def test_cms_with_token(token_key):
    """Tester l'API CMS avec le token"""
    print(f"\n🧪 TEST DE L'API CMS")
    print("=" * 50)
    
    try:
        import requests
        
        headers = {'Authorization': f'Token {token_key}'}
        
        # Test endpoint settings
        response = requests.get(
            'http://localhost:8000/api/v1/cms/settings/current/',
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ API CMS Settings accessible")
            data = response.json()
            print(f"   Site: {data.get('site_title', 'N/A')}")
        else:
            print(f"❌ API CMS Settings erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
        
        # Test endpoint media
        response = requests.get(
            'http://localhost:8000/api/v1/cms/media/',
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ API CMS Media accessible")
            data = response.json()
            print(f"   Médias: {len(data.get('results', []))}")
        else:
            print(f"❌ API CMS Media erreur: {response.status_code}")
            print(f"   Réponse: {response.text}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("⚠️ Serveur Django non démarré")
        print("   Démarrez avec: cd josnet_backend && python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def show_frontend_config(token_key):
    """Afficher la configuration frontend"""
    print(f"\n💻 CONFIGURATION FRONTEND")
    print("=" * 50)
    
    print(f"🔑 TOKEN ADMIN:")
    print(f"   {token_key}")
    
    print(f"\n📝 POUR CORRIGER LE FRONTEND:")
    print(f"   1. Connectez-vous avec:")
    print(f"      Email: <EMAIL>")
    print(f"      Password: admin123")
    
    print(f"\n   2. Ou utilisez directement le token dans les headers:")
    print(f"      Authorization: Token {token_key}")
    
    print(f"\n   3. Vérifiez src/services/api.ts")
    print(f"      Le token doit être ajouté automatiquement")
    
    print(f"\n🌐 ENDPOINTS CMS:")
    print(f"   • Settings: http://localhost:8000/api/v1/cms/settings/current/")
    print(f"   • Media: http://localhost:8000/api/v1/cms/media/")

def main():
    """Fonction principale"""
    print("🚀 CRÉATION D'UN TOKEN ADMIN POUR LE CMS")
    print("=" * 60)
    
    # Créer l'admin et le token
    token_key = create_admin_and_token()
    
    if token_key:
        # Tester l'API
        api_ok = test_cms_with_token(token_key)
        
        # Afficher la configuration
        show_frontend_config(token_key)
        
        if api_ok:
            print(f"\n🎉 CMS CONFIGURÉ AVEC SUCCÈS!")
            print(f"\n📋 PROCHAINES ÉTAPES:")
            print(f"   1. Le serveur Django doit être démarré")
            print(f"   2. Connectez-vous dans le <NAME_EMAIL>")
            print(f"   3. L'API CMS devrait maintenant fonctionner")
        else:
            print(f"\n⚠️ TOKEN CRÉÉ MAIS SERVEUR NON ACCESSIBLE")
            print(f"   Démarrez le serveur Django et réessayez")
    else:
        print(f"\n❌ ÉCHEC DE LA CRÉATION DU TOKEN")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
