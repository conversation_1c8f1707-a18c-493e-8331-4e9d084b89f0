from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db import transaction
from django.contrib.auth import get_user_model
from .models import Notification, NotificationPreference
from marketing.models import Promotion
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .serializers import NotificationSerializer

User = get_user_model()


@receiver(post_save, sender=User)
def create_notification_preferences(sender, instance, created, **kwargs):
    """
    Crée automatiquement un objet NotificationPreference pour chaque nouvel utilisateur.
    """
    if created:
        NotificationPreference.objects.create(user=instance)


@receiver(post_save, sender=Promotion)
def create_promotion_notification(sender, instance, created, **kwargs):
    """
    Crée automatiquement une notification lorsqu'une promotion est créée ou activée.
    """
    # Ne créer une notification que si la promotion est active
    if not instance.is_active:
        return
        
    # Vérifier si c'est une nouvelle promotion ou si elle vient d'être activée
    if created:
        # Nouvelle promotion créée et active
        title = f"Nouvelle promotion : {instance.name}"
        message = f"{instance.description or instance.name}. "
        if instance.discount_type == 'percentage':
            message += f"{instance.discount_value}% de réduction"
        else:
            message += f"FBu {int(round(instance.discount_value))} de réduction"
        
        if instance.discount_code:
            message += f" avec le code \"{instance.discount_code}\""
        
        # Créer une notification globale (pour tous les utilisateurs)
        with transaction.atomic():
            Notification.objects.create(
                user=None,  # Notification globale
                title=title,
                message=message,
                type='promotion',
                related_id=instance.id,
                link=f'/promotions/{instance.id}'
            )


@receiver(post_save, sender=Notification)
def broadcast_notification(sender, instance, created, **kwargs):
    """
    Broadcasts a notification to the appropriate user via WebSocket.
    """
    if created:
        channel_layer = get_channel_layer()
        if instance.user:
            group_name = f'notifications_{instance.user.id}'
            notification_data = NotificationSerializer(instance).data
            
            async_to_sync(channel_layer.group_send)(
                group_name,
                {
                    'type': 'send_notification',
                    'message': notification_data
                }
            )
