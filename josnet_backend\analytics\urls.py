from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import (
    ProductSalesStatsView, CustomerProductInsightView,
    ProductDashboardDataView, StatisticsView, DashboardView,
    SystemLogViewSet, LogsStatisticsView
)
from .home_stats_views import HomeStatsView, HomePageDataView, HomePageStatsOnlyView

# Create router for ViewSets
router = DefaultRouter()
router.register(r'logs', SystemLogViewSet, basename='logs')

urlpatterns = [
    path('product-sales/<int:pk>/', ProductSalesStatsView.as_view(), name='product-sales-stats'),
    path('customer-insights/product/<int:pk>/', CustomerProductInsightView.as_view(), name='customer-product-insights'),
    path('product-dashboard/<int:pk>/', ProductDashboardDataView.as_view(), name='product-dashboard-data'),
    path('statistics/', StatisticsView.as_view(), name='statistics'),
    path('dashboard/', DashboardView.as_view(), name='dashboard'),
    path('logs/statistics/', LogsStatisticsView.as_view(), name='logs-statistics'),
    path('home-stats/', HomeStatsView.as_view(), name='home-stats'),
    path('home-page-data/', HomePageDataView.as_view(), name='home-page-data'),
    path('home-page-stats/', HomePageStatsOnlyView.as_view(), name='home-page-stats'),
    path('', include(router.urls)),
]
