"""
Tests pour le système d'abonnements newsletter.
"""

import json
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock
from core.models import NewsletterSubscription
from core.services.newsletter_service import NewsletterService

User = get_user_model()


class NewsletterSubscriptionModelTest(TestCase):
    """Tests pour le modèle NewsletterSubscription."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
    
    def test_create_subscription(self):
        """Test de création d'un abonnement."""
        subscription = NewsletterSubscription.objects.create(
            user=self.user,
            email=self.user.email
        )
        
        self.assertEqual(subscription.email, '<EMAIL>')
        self.assertTrue(subscription.is_active)
        self.assertTrue(subscription.preferences_promotions)
        self.assertTrue(subscription.preferences_new_products)
        self.assertTrue(subscription.preferences_newsletters)
        self.assertTrue(subscription.preferences_order_updates)
        self.assertIsNotNone(subscription.unsubscribe_token)
    
    def test_unsubscribe(self):
        """Test de désabonnement."""
        subscription = NewsletterSubscription.objects.create(
            user=self.user,
            email=self.user.email
        )
        
        subscription.unsubscribe()
        
        self.assertFalse(subscription.is_active)
        self.assertIsNotNone(subscription.unsubscribed_at)
    
    def test_reactivate(self):
        """Test de réactivation d'un abonnement."""
        subscription = NewsletterSubscription.objects.create(
            user=self.user,
            email=self.user.email
        )
        
        subscription.unsubscribe()
        subscription.reactivate()
        
        self.assertTrue(subscription.is_active)
        self.assertIsNone(subscription.unsubscribed_at)
    
    def test_preferences_dict(self):
        """Test de la propriété preferences_dict."""
        subscription = NewsletterSubscription.objects.create(
            user=self.user,
            email=self.user.email,
            preferences_promotions=True,
            preferences_new_products=False,
            preferences_newsletters=True,
            preferences_order_updates=False
        )
        
        expected = {
            'promotions': True,
            'new_products': False,
            'newsletters': True,
            'order_updates': False,
        }
        
        self.assertEqual(subscription.preferences_dict, expected)


class NewsletterSubscriptionAPITest(APITestCase):
    """Tests pour l'API d'abonnements newsletter."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_subscribe_authenticated_user(self):
        """Test d'abonnement pour un utilisateur connecté."""
        url = reverse('newsletter-subscribe')
        data = {
            'email': self.user.email,
            'preferences': {
                'promotions': True,
                'new_products': True,
                'newsletters': False,
                'order_updates': True
            }
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        self.assertIn('subscription', response.data)
        
        # Vérifier que l'abonnement a été créé
        subscription = NewsletterSubscription.objects.get(email=self.user.email)
        self.assertEqual(subscription.user, self.user)
        self.assertTrue(subscription.preferences_promotions)
        self.assertTrue(subscription.preferences_new_products)
        self.assertFalse(subscription.preferences_newsletters)
        self.assertTrue(subscription.preferences_order_updates)
    
    def test_subscribe_anonymous_user(self):
        """Test d'abonnement pour un utilisateur anonyme."""
        self.client.force_authenticate(user=None)
        
        url = reverse('newsletter-subscribe')
        data = {
            'email': '<EMAIL>'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Vérifier que l'abonnement a été créé sans utilisateur
        subscription = NewsletterSubscription.objects.get(email='<EMAIL>')
        self.assertIsNone(subscription.user)
        self.assertTrue(subscription.is_active)
    
    def test_subscribe_duplicate_email(self):
        """Test d'abonnement avec un email déjà abonné."""
        # Créer un abonnement existant
        NewsletterSubscription.objects.create(
            email=self.user.email,
            is_active=True
        )
        
        url = reverse('newsletter-subscribe')
        data = {'email': self.user.email}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_unsubscribe(self):
        """Test de désabonnement."""
        # Créer un abonnement
        subscription = NewsletterSubscription.objects.create(
            user=self.user,
            email=self.user.email
        )
        
        url = reverse('newsletter-unsubscribe')
        data = {'email': self.user.email}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Vérifier que l'abonnement a été désactivé
        subscription.refresh_from_db()
        self.assertFalse(subscription.is_active)
        self.assertIsNotNone(subscription.unsubscribed_at)
    
    def test_get_subscription_authenticated(self):
        """Test de récupération de l'abonnement pour un utilisateur connecté."""
        subscription = NewsletterSubscription.objects.create(
            user=self.user,
            email=self.user.email
        )
        
        url = reverse('newsletter-subscription-subscription')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], self.user.email)
        self.assertTrue(response.data['is_active'])
    
    def test_update_preferences(self):
        """Test de mise à jour des préférences."""
        subscription = NewsletterSubscription.objects.create(
            user=self.user,
            email=self.user.email
        )
        
        url = reverse('newsletter-subscription-preferences')
        data = {
            'promotions': False,
            'new_products': True,
            'newsletters': False,
            'order_updates': True
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Vérifier que les préférences ont été mises à jour
        subscription.refresh_from_db()
        self.assertFalse(subscription.preferences_promotions)
        self.assertTrue(subscription.preferences_new_products)
        self.assertFalse(subscription.preferences_newsletters)
        self.assertTrue(subscription.preferences_order_updates)
    
    def test_newsletter_status(self):
        """Test de vérification du statut d'abonnement."""
        subscription = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            is_active=True
        )
        
        url = reverse('newsletter-status')
        response = self.client.get(url, {'email': '<EMAIL>'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['is_subscribed'])
        self.assertIn('preferences', response.data)
    
    def test_newsletter_status_not_subscribed(self):
        """Test de vérification du statut pour un email non abonné."""
        url = reverse('newsletter-status')
        response = self.client.get(url, {'email': '<EMAIL>'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['is_subscribed'])


class NewsletterServiceTest(TestCase):
    """Tests pour le service NewsletterService."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Créer quelques abonnements de test
        self.subscription1 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=True,
            preferences_new_products=False,
            preferences_newsletters=True,
            preferences_order_updates=True
        )
        
        self.subscription2 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=False,
            preferences_new_products=True,
            preferences_newsletters=False,
            preferences_order_updates=True
        )
        
        self.subscription3 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            is_active=False  # Désabonné
        )
    
    def test_get_active_subscribers_all(self):
        """Test de récupération de tous les abonnés actifs."""
        subscribers = NewsletterService.get_active_subscribers()
        
        self.assertEqual(len(subscribers), 2)  # subscription3 est inactif
        emails = [s.email for s in subscribers]
        self.assertIn('<EMAIL>', emails)
        self.assertIn('<EMAIL>', emails)
        self.assertNotIn('<EMAIL>', emails)
    
    def test_get_active_subscribers_by_preference(self):
        """Test de récupération des abonnés par préférence."""
        # Abonnés intéressés par les promotions
        promo_subscribers = NewsletterService.get_active_subscribers(['promotions'])
        self.assertEqual(len(promo_subscribers), 1)
        self.assertEqual(promo_subscribers[0].email, '<EMAIL>')
        
        # Abonnés intéressés par les nouveaux produits
        product_subscribers = NewsletterService.get_active_subscribers(['new_products'])
        self.assertEqual(len(product_subscribers), 1)
        self.assertEqual(product_subscribers[0].email, '<EMAIL>')
        
        # Abonnés intéressés par les newsletters
        newsletter_subscribers = NewsletterService.get_active_subscribers(['newsletters'])
        self.assertEqual(len(newsletter_subscribers), 1)
        self.assertEqual(newsletter_subscribers[0].email, '<EMAIL>')
    
    @patch('core.services.newsletter_service.EmailMultiAlternatives')
    def test_send_email_to_subscriber(self, mock_email):
        """Test d'envoi d'email à un abonné."""
        mock_email_instance = MagicMock()
        mock_email.return_value = mock_email_instance
        
        result = NewsletterService.send_email_to_subscriber(
            subscriber=self.subscription1,
            subject='Test Subject',
            content='<p>Test content</p>',
            campaign_type='promotion'
        )
        
        self.assertTrue(result)
        mock_email.assert_called_once()
        mock_email_instance.attach_alternative.assert_called_once()
        mock_email_instance.send.assert_called_once()
    
    @patch('core.services.newsletter_service.NewsletterService.send_email_to_subscriber')
    def test_send_promotion_notification(self, mock_send_email):
        """Test d'envoi de notification de promotion."""
        mock_send_email.return_value = True
        
        result = NewsletterService.send_promotion_notification(
            title='Test Promotion',
            description='Test description',
            promotion_url='http://example.com/promo',
            created_by=self.user
        )
        
        self.assertTrue(result.get('success'))
        self.assertEqual(result.get('sent_count'), 1)  # Seul user1 a les promotions activées
        
        # Vérifier qu'une campagne a été créée
        from core.models import NewsletterCampaign
        campaign = NewsletterCampaign.objects.first()
        self.assertIsNotNone(campaign)
        self.assertEqual(campaign.campaign_type, 'promotion')
        self.assertEqual(campaign.created_by, self.user)
