import { ApiError } from './api';

// API base URL
const API_BASE_URL = 'http://localhost:8000/api/v1';

// Types
// Interface pour le type de réponse paginable de l'API
export interface PaginatedResponse<T> {
  results: T[];
  count: number;
  next: string | null;
  previous: string | null;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parent?: number;
  parent_name?: string;
  image?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CategoryTree extends Pick<Category, 'id' | 'name' | 'slug'> {
  children: CategoryTree[];
}

export interface ProductAttribute {
  id: number;
  name: string;
  description?: string;
}

export interface AttributeValue {
  id: number;
  attribute: number;
  attribute_name: string;
  value: string;
}

export interface ProductImage {
  id: number;
  product: number;
  image?: string;
  alt_text?: string;
  is_primary: boolean;
  created_at: string;
}

export interface ProductAttributeValue {
  id: number;
  product: number;
  attribute: number;
  attribute_name: string;
  value: number;
  value_name: string;
}

export interface StockMovement {
  id: number;
  inventory: number;
  quantity_before: number;
  quantity_after: number;
  quantity_changed: number;
  reason: string;
  reference?: string;
  notes?: string;
  created_at: string;
  created_by?: number;
  created_by_name?: string;
}

export interface StockReservation {
  id: number;
  inventory: number;
  quantity: number;
  reference?: string;
  notes?: string;
  created_at: string;
  expires_at?: string;
  created_by?: number;
  created_by_name?: string;
}

export interface Inventory {
  id: number;
  product?: number;
  variant?: number;
  product_name?: string;
  variant_name?: string;
  sku?: string;
  quantity: number;
  reserved_quantity: number;
  available_quantity: number;
  low_stock_threshold: number;
  is_in_stock: boolean;
  is_low_stock: boolean;
  stock_status?: string;
  stock_level_percentage?: number;
  price?: number;
  last_checked: string;
  movements?: StockMovement[];
  reservations?: StockReservation[];
}

export interface ProductVariant {
  id: number;
  product: number;
  name: string;
  sku: string;
  price_adjustment: number;
  price: number;
  attributes: AttributeValue[];
  inventory?: Inventory;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProductReview {
  id: number;
  product: number;
  user: number;
  user_email: string;
  user_name: string;
  rating: number;
  title: string;
  comment: string;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProductListItem {
  id: number;
  name: string;
  slug: string;
  sku: string;
  short_description?: string;
  price: number;
  sale_price?: number;
  current_price: number;
  discount_percentage: number;
  is_on_sale: boolean;
  categories: Category[];
  primary_image?: {
    id: number;
    image: string;
    alt_text?: string;
  };
  is_featured: boolean;
  in_stock: boolean;
  status: 'draft' | 'published' | 'archived';
  created_at: string;
}

export interface ProductDetail extends ProductListItem {
  description: string;
  images: ProductImage[];
  attribute_values: ProductAttributeValue[];
  variants: ProductVariant[];
  inventory?: Inventory;
  reviews: ProductReview[];
  is_digital: boolean;
  updated_at: string;
  created_by?: number;
  updated_by?: number;
}

export interface ProductCreateUpdate {
  id?: number;
  name: string;
  slug?: string;
  sku?: string;
  description: string;
  short_description?: string;
  price: number;
  sale_price?: number;
  cost_price?: number;
  categories: number[];
  is_featured: boolean;
  is_digital: boolean;
  status: 'draft' | 'published' | 'archived';
  created_by?: number;
  updated_by?: number;
}

export interface ProductFilterParams {
  name?: string;
  min_price?: number;
  max_price?: number;
  category?: string[];
  status?: 'draft' | 'published' | 'archived';
  is_featured?: boolean;
  is_digital?: boolean;
  in_stock?: boolean;
  on_sale?: boolean;
  page?: number;
  page_size?: number;
}

export interface InventoryFilterParams {
  status?: string;
  category?: string;
  lowStock?: boolean;
  page?: number;
  page_size?: number;
}

export interface InventoryStats {
  total_products: number;
  total_variants: number;
  total_quantity: number;
  total_reserved: number;
  total_available: number;
  in_stock: number;
  low_stock: number;
  out_of_stock: number;
  total_value: number;
  reserved_value: number;
}

// Helper function to handle API errors
const handleApiError = async (response: Response): Promise<ApiError> => {
  try {
    const data = await response.json();
    return {
      message: data.detail || 'Une erreur est survenue',
      errors: data
    };
  } catch (error) {
    return {
      message: 'Une erreur est survenue lors de la communication avec le serveur'
    };
  }
};

// Helper function to get auth header
const getAuthHeader = () => {
  // Vérifier plusieurs clés possibles pour le token
  const token = localStorage.getItem('accessToken') || 
               localStorage.getItem('authToken') || 
               localStorage.getItem('token') || 
               sessionStorage.getItem('accessToken') || 
               sessionStorage.getItem('authToken');
  
  console.log('Auth token found:', token ? 'yes' : 'no');
  
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Helper function to handle fetch with timeout
const fetchWithTimeout = async (url: string, options: RequestInit, timeout = 5000) => {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    throw error;
  }
};

// Product API service
export const productApi = {
  // Get all categories
  getCategories: async (): Promise<Category[]> => {
    try {
      const url = `${API_BASE_URL}/products/categories/`;
      console.log('Fetching categories from URL:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Categories API error:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw await handleApiError(response);
      }

      const data = await response.json();
      console.log('Categories API response:', data);

      // Gérer la structure paginée
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        console.log(`Received ${data.results.length} categories from paginated response`);
        return data.results;
      }
      
      // Gérer une réponse de tableau direct
      if (Array.isArray(data)) {
        console.log(`Received ${data.length} categories from direct array response`);
        return data;
      }

      console.warn('Unexpected API response format for categories:', data);
      return [];
    } catch (error) {
      console.error('Error fetching categories:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des catégories'
      };
    }
  },

  // Create a new category
  createCategory: async (categoryData: Partial<Category>): Promise<Category> => {
    try {
      

      const response = await fetch(`${API_BASE_URL}/products/categories/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(categoryData),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating category:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création de la catégorie'
      };
    }
  },

  // Update an existing category
  updateCategory: async (id: number, categoryData: Partial<Category>): Promise<Category> => {
    const response = await fetch(`${API_BASE_URL}/products/categories/${id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(categoryData),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
  },

  // Delete a category
  deleteCategory: async (id: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/categories/${id}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la suppression de la catégorie'
      };
    }
  },

  // Get category tree
  getCategoryTree: async (): Promise<CategoryTree[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/categories/tree/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération de l\'arborescence des catégories'
      };
    }
  },

  // Get products with filtering
  getProducts: async (filters?: ProductFilterParams): Promise<{ count: number, results: ProductListItem[] }> => {
    try {
      let url = `${API_BASE_URL}/products/products/`;

      if (filters) {
        const params = new URLSearchParams();

        if (filters.name) params.append('name', filters.name);
        if (filters.min_price) params.append('min_price', filters.min_price.toString());
        if (filters.max_price) params.append('max_price', filters.max_price.toString());
        if (filters.status) params.append('status', filters.status);
        if (filters.is_featured !== undefined) params.append('is_featured', filters.is_featured.toString());
        if (filters.is_digital !== undefined) params.append('is_digital', filters.is_digital.toString());
        if (filters.in_stock !== undefined) params.append('in_stock', filters.in_stock.toString());
        if (filters.on_sale !== undefined) params.append('on_sale', filters.on_sale.toString());
        if (filters.page) params.append('page', filters.page.toString());
        if (filters.page_size) params.append('page_size', filters.page_size.toString());

        if (filters.category && filters.category.length > 0) {
          filters.category.forEach(cat => params.append('category', cat));
        }

        url += `?${params.toString()}`;
      }

      console.log('Fetching products from URL:', url);

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            ...getAuthHeader(),
          },
        });

        if (!response.ok) {
          console.error('Products API error:', response.status, response.statusText);
          const errorText = await response.text();
          console.error('Error response:', errorText);
          
          // En cas d'erreur en mode développement, retourner des produits fictifs
          if (process.env.NODE_ENV === 'development') {
            console.warn('Returning mock products as fallback');
            return productApi.getMockProducts(filters);
          }
          
          throw await handleApiError(response);
        }

        const data = await response.json();
        console.log('Products API response structure:', Object.keys(data));

        // Vérifier si la réponse est déjà au format attendu (avec count et results)
        if (data && typeof data === 'object' && 'count' in data && 'results' in data && Array.isArray(data.results)) {
          console.log(`Received ${data.results.length} products from paginated response (total: ${data.count})`);
          return data;
        }
        
        // Si la réponse est un tableau direct, la convertir au format attendu
        if (Array.isArray(data)) {
          console.log(`Received ${data.length} products from direct array response`);
          return {
            count: data.length,
            results: data
          };
        }

        console.warn('Unexpected API response format for products:', data);
        
        // En cas de format inattendu en mode développement, retourner des produits fictifs
        if (process.env.NODE_ENV === 'development') {
          console.warn('Returning mock products as fallback');
          return productApi.getMockProducts(filters);
        }
        
        return { count: 0, results: [] };
      } catch (e) {
        console.error('Error fetching products:', e);
        
        // En cas d'erreur en mode développement, retourner des produits fictifs
        if (process.env.NODE_ENV === 'development') {
          console.warn('API connection failed, returning mock product data');
          return productApi.getMockProducts(filters);
        }
        
        throw e;
      }
    } catch (error) {
      console.error('Error in getProducts:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des produits'
      };
    }
  },

  // Helper function to generate mock categories
  getMockCategories: (): Category[] => {
    return [
      {
        id: 1,
        name: "Réseaux",
        slug: "reseaux",
        description: "Équipements et accessoires réseau",
        parent: null,
        parent_name: null,
        is_active: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z"
      },
      {
        id: 2,
        name: "Sécurité",
        slug: "securite",
        description: "Équipements de sécurité informatique",
        parent: null,
        parent_name: null,
        is_active: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z"
      },
      {
        id: 3,
        name: "Stockage",
        slug: "stockage",
        description: "Solutions de stockage de données",
        parent: null,
        parent_name: null,
        is_active: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z"
      },
      {
        id: 4,
        name: "Accessoires",
        slug: "accessoires",
        description: "Accessoires informatiques divers",
        parent: null,
        parent_name: null,
        is_active: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z"
      }
    ];
  },

  // Helper function to generate mock products
  getMockProducts: (filters?: ProductFilterParams): { count: number, results: ProductListItem[] } => {
    // Generate 20 mock products
    const mockProducts = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      name: `Produit ${i + 1}`,
      slug: `produit-${i + 1}`,
      description: `Description du produit ${i + 1}`,
      price: 50 + i * 10,
      sale_price: i % 3 === 0 ? 45 + i * 9 : null,
      current_price: i % 3 === 0 ? 45 + i * 9 : 50 + i * 10,
      discount_percentage: i % 3 === 0 ? 10 : 0,
      is_on_sale: i % 3 === 0,
      sku: `SKU-${1000 + i}`,
      image: '/placeholder.svg',
      category_id: i % 4 + 1,
      category_name: `Catégorie ${i % 4 + 1}`,
      is_featured: i % 5 === 0,
      is_digital: i % 7 === 0,
      status: 'active',
      in_stock: i % 3 !== 0,
      rating: 4 + (i % 2) * 0.5,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    // Apply filters if provided
    let filteredProducts = [...mockProducts];

    if (filters) {
      if (filters.name) {
        filteredProducts = filteredProducts.filter(p =>
          p.name.toLowerCase().includes(filters.name!.toLowerCase())
        );
      }

      if (filters.min_price) {
        filteredProducts = filteredProducts.filter(p =>
          p.price >= filters.min_price!
        );
      }

      if (filters.max_price) {
        filteredProducts = filteredProducts.filter(p =>
          p.price <= filters.max_price!
        );
      }

      if (filters.category && filters.category.length > 0) {
        // Skip category filtering in mock data
        console.log('Category filtering skipped in mock data');
      }

      if (filters.is_featured !== undefined) {
        filteredProducts = filteredProducts.filter(p =>
          p.is_featured === filters.is_featured
        );
      }

      if (filters.in_stock !== undefined) {
        filteredProducts = filteredProducts.filter(p =>
          p.in_stock === filters.in_stock
        );
      }
    }

    // Apply pagination
    const page = filters?.page || 1;
    const pageSize = filters?.page_size || 10;
    const startIndex = (page - 1) * pageSize;
    const paginatedProducts = filteredProducts.slice(startIndex, startIndex + pageSize);

    // For mock data, we'll just cast to any to avoid TypeScript errors
    return {
      count: filteredProducts.length,
      results: paginatedProducts as any
    };
  },

  // Get featured products
  getFeaturedProducts: async (): Promise<ProductListItem[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/products/featured/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des produits en vedette'
      };
    }
  },

  // Get product by slug
  getProduct: async (slug: string): Promise<ProductDetail> => {
    try {
      console.log(`Fetching product details for slug: ${slug}`);
      
      // Vérifier si le slug est un nombre ou une chaîne
      const endpoint = isNaN(Number(slug)) 
        ? `${API_BASE_URL}/products/products/${slug}/` // Utiliser le slug
        : `${API_BASE_URL}/products/products/${slug}/`; // Utiliser l'ID
      
      console.log(`API endpoint: ${endpoint}`);
      
      try {
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            ...getAuthHeader(),
          },
        });

        if (!response.ok) {
          console.error(`Error fetching product: ${response.status} ${response.statusText}`);
          const errorText = await response.text();
          console.error('Error response:', errorText);
          
          throw await handleApiError(response);
        }

        const data = await response.json();
        console.log('Product data received:', Object.keys(data));
        return data;
      } catch (e) {
        console.error('Error in API call:', e);
        throw e;
      }
    } catch (error) {
      console.error('Error in getProduct:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération du produit'
      };
    }
  },

  // Create product
  createProduct: async (product: ProductCreateUpdate): Promise<ProductDetail> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/products/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(product),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création du produit'
      };
    }
  },

  // Update product
  updateProduct: async (slug: string, product: ProductCreateUpdate): Promise<ProductDetail> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/products/${slug}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(product),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la mise à jour du produit'
      };
    }
  },

  // Delete product
  deleteProduct: async (slug: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/products/${slug}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la suppression du produit'
      };
    }
  },

  // Get product images
  getProductImages: async (productId: number): Promise<ProductImage[] | PaginatedResponse<ProductImage>> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-images/?product=${productId}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Product images API error:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        
        // En cas d'erreur, retourner des images fictives
        console.warn('Affichage de données simulées - Le backend n\'est pas disponible ou n\'est pas configuré pour cet endpoint');
        return getMockProductImages(productId);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching product images:', error);
      console.warn('Affichage de données simulées - Le backend n\'est pas disponible ou n\'est pas configuré pour cet endpoint');
      return getMockProductImages(productId);
    }
  },

  // Upload a single product image
  uploadProductImage: async (productId: number, image: File, altText?: string): Promise<ProductImage> => {
    try {
      const formData = new FormData();
      formData.append('product', productId.toString());
      formData.append('image', image);
      if (altText) {
        formData.append('alt_text', altText);
      }

      console.log('Uploading image for product:', productId);
      console.log('Image file:', image.name, image.type, image.size);

      // Pour les requêtes multipart/form-data, ne pas définir Content-Type
      // Le navigateur le définira automatiquement avec le boundary correct
      const headers = getAuthHeader();
      delete headers['Content-Type'];

      const response = await fetch(`${API_BASE_URL}/products/product-images/`, {
        method: 'POST',
        headers: headers,
        body: formData,
      });

      if (!response.ok) {
        console.error('Image upload failed:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw await handleApiError(response);
      }

      const data = await response.json();
      console.log('Image upload successful:', data);
      return data;
    } catch (error) {
      console.error('Error in uploadProductImage:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors du téléchargement de l\'image'
      };
    }
  },

  // Get inventory items with filtering
  getInventoryItems: async (filters: InventoryFilterParams = {}): Promise<Inventory[]> => {
    try {
      const params = new URLSearchParams();

      if (filters.status && filters.status !== 'all') {
        params.append('status', filters.status);
      }
      if (filters.category) {
        params.append('category', filters.category);
      }
      if (filters.lowStock) {
        params.append('low_stock', 'true');
      }
      if (filters.page) {
        params.append('page', filters.page.toString());
      }
      if (filters.page_size) {
        params.append('page_size', filters.page_size.toString());
      }

      const url = `${API_BASE_URL}/products/inventory/${params.toString() ? `?${params.toString()}` : ''}`;
      console.log('Fetching inventory from URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        console.error('Inventory API error:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        return [];
      }

      const data = await response.json();
      console.log('Inventory API response:', data);

      // Gérer la structure paginée
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        console.log(`Received ${data.results.length} inventory items from paginated response`);
        return data.results;
      }
      
      // Gérer une réponse de tableau direct
      if (Array.isArray(data)) {
        console.log(`Received ${data.length} inventory items from direct array response`);
        return data;
      }

      console.warn('Unexpected API response format for inventory:', data);
      return [];
    } catch (error) {
      console.error('Error fetching inventory items:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des articles d\'inventaire'
      };
    }
  },

  // Get inventory statistics
  getInventoryStats: async (): Promise<InventoryStats> => {
    try {
      // For development/testing - return mock data if API is not available
      if (process.env.NODE_ENV === 'development') {
        try {
          const response = await fetch(`${API_BASE_URL}/products/inventory/stats/`, {
            method: 'GET',
            headers: {
              ...getAuthHeader(),
            },
          });

          if (!response.ok) {
            // If API fails in development, return mock stats
            console.warn('API failed, returning mock inventory stats');
            return {
              total_products: 42,
              total_variants: 78,
              total_quantity: 1250,
              total_reserved: 35,
              total_available: 1215,
              in_stock: 38,
              low_stock: 8,
              out_of_stock: 4,
              total_value: 24500.00,
              reserved_value: 750.00
            };
          }

          return await response.json();
        } catch (e) {
          console.warn('API connection failed, returning mock inventory stats');
          return {
            total_products: 42,
            total_variants: 78,
            total_quantity: 1250,
            total_reserved: 35,
            total_available: 1215,
            in_stock: 38,
            low_stock: 8,
            out_of_stock: 4,
            total_value: 24500.00,
            reserved_value: 750.00
          };
        }
      }

      // Production code path
      const response = await fetch(`${API_BASE_URL}/products/inventory/stats/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching inventory stats:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des statistiques d\'inventaire'
      };
    }
  },

  // Get a single inventory item
  getInventory: async (inventoryId: number): Promise<Inventory> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/${inventoryId}/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération de l\'inventaire'
      };
    }
  },

  // Adjust inventory
  adjustInventory: async (inventoryId: number, data: any): Promise<Inventory> => {
    // Définir les différentes URL possibles pour l'ajustement de stock
    const possibleUrls = [
      `${API_BASE_URL}/products/inventory/${inventoryId}/adjust_stock/`,
      `${API_BASE_URL}/products/inventory/${inventoryId}/adjust-stock/`,
      `${API_BASE_URL}/products/inventory/${inventoryId}/adjust/`
    ];

    let lastError: any = null;

    // Essayer chaque URL jusqu'à ce qu'une fonctionne
    for (const url of possibleUrls) {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...getAuthHeader(),
          },
          body: JSON.stringify(data),
        });

        if (response.ok) {
          return await response.json();
        }

        lastError = await handleApiError(response);
      } catch (error) {
        lastError = error;
        // Continuer avec l'URL suivante
      }
    }

    // Si toutes les URL ont échoué, lancer l'erreur
    if (lastError && (lastError as ApiError).message) {
      throw lastError;
    }
    throw {
      message: 'Une erreur est survenue lors de l\'ajustement de l\'inventaire. Aucune des URL d\'ajustement n\'a fonctionné.'
    };
  },

  // Bulk adjust inventory
  bulkAdjustInventory: async (data: any): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/bulk_adjust/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de l\'ajustement en masse de l\'inventaire'
      };
    }
  },

  // Bulk update threshold
  bulkUpdateThreshold: async (data: any): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/update_thresholds/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la mise à jour des seuils'
      };
    }
  },

  // Upload multiple product images
  uploadMultipleProductImages: async (productId: number, images: File[], altText?: string): Promise<ProductImage[]> => {
    try {
      const formData = new FormData();
      formData.append('product', productId.toString());
      
      // Log des informations sur les images à télécharger
      console.log(`Uploading ${images.length} images for product:`, productId);
      images.forEach((image, index) => {
        console.log(`Image ${index + 1}:`, image.name, image.type, image.size);
        formData.append('images', image);
      });
      
      if (altText) {
        formData.append('alt_text', altText);
      }

      // Pour les requêtes multipart/form-data, ne pas définir Content-Type
      // Le navigateur le définira automatiquement avec le boundary correct
      const headers = getAuthHeader();
      delete headers['Content-Type'];

      // Essayer d'abord l'URL avec bulk_upload
      let response;
      try {
        response = await fetch(`${API_BASE_URL}/products/product-images/bulk_upload/`, {
          method: 'POST',
          headers: headers,
          body: formData,
        });
      } catch (err) {
        console.error('Error with bulk_upload endpoint:', err);
        // Si la première URL échoue, essayer l'URL alternative
        response = await fetch(`${API_BASE_URL}/products/product-images/bulk/`, {
          method: 'POST',
          headers: headers,
          body: formData,
        });
      }

      if (!response.ok) {
        console.error('Multiple images upload failed:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw await handleApiError(response);
      }

      const data = await response.json();
      console.log('Multiple images upload successful:', data);
      return data;
    } catch (error) {
      console.error('Error in uploadMultipleProductImages:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors du téléchargement des images'
      };
    }
  },

  // Set primary product image
  setPrimaryProductImage: async (imageId: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-images/${imageId}/set_primary/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la définition de l\'image principale'
      };
    }
  },

  // Delete product image
  deleteProductImage: async (imageId: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-images/${imageId}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la suppression de l\'image'
      };
    }
  },

  // Reorder product images
  reorderProductImages: async (productId: number, imageIds: number[]): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-images/reorder/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify({
          product: productId,
          image_ids: imageIds,
        }),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la réorganisation des images'
      };
    }
  },

  // Get product attributes
  getProductAttributes: async (): Promise<ProductAttribute[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/attributes/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des attributs'
      };
    }
  },

  // Get attribute values
  getAttributeValues: async (): Promise<AttributeValue[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/attribute-values/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des valeurs d\'attributs'
      };
    }
  },

  // Get product variants
  getProductVariants: async (productId: number): Promise<ProductVariant[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-variants/by_product/?product_id=${productId}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des variantes'
      };
    }
  },

  // Create product variant
  createProductVariant: async (variant: any): Promise<ProductVariant> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-variants/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(variant),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création de la variante'
      };
    }
  },

  // Update product variant
  updateProductVariant: async (variantId: number, variant: any): Promise<ProductVariant> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-variants/${variantId}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(variant),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la mise à jour de la variante'
      };
    }
  },

  // Delete product variant
  deleteProductVariant: async (variantId: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-variants/${variantId}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la suppression de la variante'
      };
    }
  },

  // Generate product variants
  generateProductVariants: async (data: any): Promise<ProductVariant[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/product-variants/generate_variants/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la génération des variantes'
      };
    }
  },

  // Get inventory by ID
  getInventoryById: async (inventoryId: number): Promise<Inventory> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/${inventoryId}/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération de l\'inventaire'
      };
    }
  },

  // Get product inventory
  getProductInventory: async (productId: number): Promise<Inventory> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/?product=${productId}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const results = await response.json();
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération de l\'inventaire du produit'
      };
    }
  },

  // Get variant inventory
  getVariantInventory: async (variantId: number): Promise<Inventory> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/?variant=${variantId}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      const results = await response.json();
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération de l\'inventaire de la variante'
      };
    }
  },


  // Create inventory
  createInventory: async (data: { product_id?: number; variant_id?: number; quantity: number; low_stock_threshold?: number; reserved_quantity?: number }): Promise<Inventory> => {
    try {
      console.log('Sending inventory data to API:', data);
      const response = await fetch(`${API_BASE_URL}/products/inventory/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log('API response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error response:', errorData);
        throw await handleApiError(response);
      }

      const responseData = await response.json();
      console.log('API success response:', responseData);
      return responseData;
    } catch (error) {
      console.error('Error in createInventory:', error);
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création de l\'inventaire'
      };
    }
  },

  // Reserve inventory
  reserveInventory: async (inventoryId: number, data: any): Promise<StockReservation> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/${inventoryId}/reserve_stock/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la réservation du stock'
      };
    }
  },

  // Release reservation
  releaseReservation: async (inventoryId: number, data: any): Promise<Inventory> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/${inventoryId}/release_reservation/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la libération de la réservation'
      };
    }
  },

  // Get low stock items
  getLowStockItems: async (): Promise<Inventory[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/low_stock/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des articles à stock bas'
      };
    }
  },

  // Get out of stock items
  getOutOfStockItems: async (): Promise<Inventory[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/inventory/out_of_stock/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des articles en rupture de stock'
      };
    }
  },

  // Export data
  exportData: async (entityType: string): Promise<Blob> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/import-export/${entityType}/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.blob();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de l\'exportation des données'
      };
    }
  },

  // Import data
  importData: async (entityType: string, formData: FormData): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/import-export/${entityType}/`, {
        method: 'POST',
        headers: {
          ...getAuthHeader(),
        },
        body: formData,
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de l\'importation des données'
      };
    }
  },

  // Get product attributes
  getAttributes: async (): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/attributes/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des attributs'
      };
    }
  },

  // Get product price range
  getProductPriceRange: async (): Promise<{ min_price: number, max_price: number }> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/price-range/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération de la plage de prix'
      };
    }
  },

  // Recherche avancée de produits
  searchProducts: async (params: any): Promise<{ count: number, results: ProductListItem[] }> => {
    try {
      let url = `${API_BASE_URL}/products/products/search/`;

      if (params) {
        const queryParams = new URLSearchParams();

        if (params.query) queryParams.append('query', params.query);
        if (params.searchIn && params.searchIn.length > 0) {
          params.searchIn.forEach((field: string) => queryParams.append('search_in', field));
        }
        if (params.minPrice) queryParams.append('min_price', params.minPrice.toString());
        if (params.maxPrice) queryParams.append('max_price', params.maxPrice.toString());
        if (params.categories && params.categories.length > 0) {
          params.categories.forEach((cat: number) => queryParams.append('category', cat.toString()));
        }
        if (params.status && params.status.length > 0) {
          params.status.forEach((status: string) => queryParams.append('status', status));
        }
        if (params.inStock !== undefined) queryParams.append('in_stock', params.inStock.toString());
        if (params.onSale !== undefined) queryParams.append('on_sale', params.onSale.toString());

        url += `?${queryParams.toString()}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la recherche de produits'
      };
    }
  },

  // Recherche par facettes
  getProductFacets: async (params: any): Promise<any[]> => {
    try {
      let url = `${API_BASE_URL}/products/products/facets/`;

      if (params) {
        const queryParams = new URLSearchParams();

        if (params.query) queryParams.append('query', params.query);
        if (params.facets) {
          Object.entries(params.facets).forEach(([field, values]: [string, any]) => {
            if (Array.isArray(values) && values.length > 0) {
              values.forEach((value: string) => queryParams.append(`facet_${field}`, value));
            }
          });
        }

        url += `?${queryParams.toString()}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des facettes'
      };
    }
  },

  // Obtenir les produits liés
  getRelatedProducts: async (productId: number): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/related-products/?product=${productId}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des produits liés'
      };
    }
  },

  // Ajouter un produit lié
  addRelatedProduct: async (data: any): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/related-products/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de l\'ajout du produit lié'
      };
    }
  },

  // Supprimer un produit lié
  removeRelatedProduct: async (id: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/products/related-products/${id}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la suppression du produit lié'
      };
    }
  },

  // Obtenir les promotions
  getPromotions: async (): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/promotions/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des promotions'
      };
    }
  },

  // Créer une promotion
  createPromotion: async (data: any): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/promotions/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la création de la promotion'
      };
    }
  },

  // Mettre à jour une promotion
  updatePromotion: async (id: number, data: any): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/promotions/${id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la mise à jour de la promotion'
      };
    }
  },

  // Supprimer une promotion
  deletePromotion: async (id: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/promotions/${id}/`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la suppression de la promotion'
      };
    }
  },

  // Obtenir les campagnes marketing
  getMarketingCampaigns: async (): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/campaigns/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des campagnes marketing'
      };
    }
  },

  // Obtenir les campagnes marketing d'un produit
  getProductCampaigns: async (productId: number): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/campaigns/by_product/?product_id=${productId}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des campagnes marketing du produit'
      };
    }
  },

  // Ajouter un produit à une campagne
  addProductToCampaign: async (campaignId: number, productId: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/campaigns/${campaignId}/add_product/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify({ product_id: productId }),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de l\'ajout du produit à la campagne'
      };
    }
  },

  // Retirer un produit d'une campagne
  removeProductFromCampaign: async (campaignId: number, productId: number): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/marketing/campaigns/${campaignId}/remove_product/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        body: JSON.stringify({ product_id: productId }),
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors du retrait du produit de la campagne'
      };
    }
  },

  // Obtenir les statistiques de vente d'un produit
  getProductSalesStats: async (productId: number, period: string = 'month'): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/product-sales/${productId}/?period=${period}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des statistiques de vente'
      };
    }
  },

  // Obtenir les insights clients pour un produit
  getCustomerProductInsights: async (productId: number): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/customer-insights/product/${productId}/`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des insights clients'
      };
    }
  },

  // Obtenir les données du tableau de bord d'un produit
  getProductDashboardData: async (productId: number, period: string = '30d'): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/product-dashboard/${productId}/?period=${period}`, {
        method: 'GET',
        headers: {
          ...getAuthHeader(),
        },
      });

      if (!response.ok) {
        throw await handleApiError(response);
      }

      return await response.json();
    } catch (error) {
      if ((error as ApiError).message) {
        throw error;
      }
      throw {
        message: 'Une erreur est survenue lors de la récupération des données du tableau de bord'
      };
    }
  },


};

// Helper function to generate mock product images for development
const getMockProductImages = (productId: number): ProductImage[] => {
  const mockImages: ProductImage[] = [
    {
      id: 1,
      product: productId,
      image: '/assets/images/products/product-1.jpg',
      alt_text: 'Image principale du produit',
      is_primary: true,
      created_at: new Date().toISOString()
    },
    {
      id: 2,
      product: productId,
      image: '/assets/images/products/product-2.jpg',
      alt_text: 'Vue latérale du produit',
      is_primary: false,
      created_at: new Date().toISOString()
    },
    {
      id: 3,
      product: productId,
      image: '/assets/images/products/product-3.jpg',
      alt_text: 'Vue arrière du produit',
      is_primary: false,
      created_at: new Date().toISOString()
    }
  ];

  return mockImages;
};

// Helper function to generate mock inventory items for development
const getMockInventoryItems = (filters: InventoryFilterParams = {}): Inventory[] => {
  const mockItems: Inventory[] = [
    {
      id: 1,
      product: 1,
      product_name: "Routeur Wi-Fi 6",
      variant: null,
      variant_name: null,
      sku: "RTR-WF6-001",
      quantity: 24,
      reserved_quantity: 2,
      available_quantity: 22,
      low_stock_threshold: 10,
      is_in_stock: true,
      is_low_stock: false,
      last_checked: "2023-07-15T10:30:00Z"
    },
    {
      id: 2,
      product: 2,
      product_name: "Câble HDMI 2.1 2m",
      variant: null,
      variant_name: null,
      sku: "CBL-HD21-2M",
      quantity: 45,
      reserved_quantity: 5,
      available_quantity: 40,
      low_stock_threshold: 15,
      is_in_stock: true,
      is_low_stock: false,
      last_checked: "2023-07-14T14:20:00Z"
    },
    {
      id: 3,
      product: 3,
      product_name: "Disque SSD 1TB",
      variant: null,
      variant_name: null,
      sku: "SSD-1TB-001",
      quantity: 12,
      reserved_quantity: 3,
      available_quantity: 9,
      low_stock_threshold: 10,
      is_in_stock: true,
      is_low_stock: true,
      last_checked: "2023-07-13T09:15:00Z"
    },
    {
      id: 4,
      product: 4,
      product_name: "Adaptateur USB-C",
      variant: null,
      variant_name: null,
      sku: "ADP-USBC-001",
      quantity: 8,
      reserved_quantity: 2,
      available_quantity: 6,
      low_stock_threshold: 12,
      is_in_stock: true,
      is_low_stock: true,
      last_checked: "2023-07-12T16:45:00Z"
    },
    {
      id: 5,
      product: 5,
      product_name: "Carte graphique 8GB",
      variant: null,
      variant_name: null,
      sku: "GPU-8GB-001",
      quantity: 0,
      reserved_quantity: 0,
      available_quantity: 0,
      low_stock_threshold: 5,
      is_in_stock: false,
      is_low_stock: false,
      last_checked: "2023-07-11T11:30:00Z"
    }
  ];

  // Filter by status
  if (filters.status) {
    return mockItems.filter(item => {
      if (filters.status === 'in_stock') return item.is_in_stock && !item.is_low_stock;
      if (filters.status === 'low_stock') return item.is_low_stock;
      if (filters.status === 'out_of_stock') return !item.is_in_stock;
      return true; // 'all' or any other value
    });
  }

  return mockItems;
};
