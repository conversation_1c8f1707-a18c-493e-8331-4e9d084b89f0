/**
 * Hook pour récupérer les données de la page d'accueil depuis le CMS admin
 */

import { useQuery } from '@tanstack/react-query';
import homePageCmsApi, { HomePageCmsData } from '@/services/homePageCmsApi';

export const useHomePageCms = () => {
  const {
    data: homePageData,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery<HomePageCmsData>({
    queryKey: ['homePageCms'],
    queryFn: homePageCmsApi.getHomePageData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 2
  });

  return {
    homePageData,
    isLoading,
    isError,
    error,
    refetch,
    // Données spécifiques facilement accessibles
    siteSettings: homePageData?.siteSettings,
    heroContent: homePageData?.heroContent,
    featuredPages: homePageData?.featuredPages || [],
    blogPosts: homePageData?.blogPosts || [],
    services: homePageData?.services || [],
    testimonials: homePageData?.testimonials || [],
    stats: homePageData?.stats,
    media: homePageData?.media || [],
    menus: homePageData?.menus || []
  };
};

export const useHomePageBlog = () => {
  const {
    data: blogData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['homePageBlog'],
    queryFn: homePageCmsApi.getBlogSectionData,
    staleTime: 3 * 60 * 1000, // 3 minutes
    refetchOnWindowFocus: false
  });

  return {
    blogData,
    isLoading,
    isError,
    refetch,
    featuredPosts: blogData?.featuredPosts || [],
    recentPosts: blogData?.recentPosts || [],
    totalPosts: blogData?.totalPosts || 0
  };
};

export const useHomePageMedia = () => {
  const {
    data: mediaData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['homePageMedia'],
    queryFn: homePageCmsApi.getHomePageMedia,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  });

  return {
    mediaData,
    isLoading,
    isError,
    refetch,
    heroImages: mediaData?.heroImages || [],
    galleryImages: mediaData?.galleryImages || [],
    totalMedia: mediaData?.totalMedia || 0
  };
};

export const useSiteConfiguration = () => {
  const {
    data: configData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['siteConfiguration'],
    queryFn: homePageCmsApi.getSiteConfiguration,
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false
  });

  return {
    configData,
    isLoading,
    isError,
    refetch,
    siteSettings: configData?.siteSettings,
    menus: configData?.menus || [],
    navigation: configData?.navigation
  };
};

export const useNavigationPages = () => {
  const {
    data: pagesData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['navigationPages'],
    queryFn: homePageCmsApi.getNavigationPages,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  });

  return {
    pagesData,
    isLoading,
    isError,
    refetch,
    mainPages: pagesData?.mainPages || [],
    totalPages: pagesData?.totalPages || 0
  };
};
