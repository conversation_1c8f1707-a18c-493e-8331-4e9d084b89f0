from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.utils import timezone

from .models_extension import ReturnRequest, ReturnItem, Claim, ClaimMessage, ClaimAttachment, ClaimStatusHistory
from .serializers_extension import (
    ReturnRequestListSerializer, ReturnRequestDetailSerializer, ReturnRequestCreateSerializer,
    ReturnItemSerializer, ClaimListSerializer, ClaimDetailSerializer, ClaimCreateSerializer,
    ClaimMessageSerializer, ClaimStatusHistorySerializer, ClaimAttachmentSerializer
)
from authentication.permissions import IsOwnerOrStaff, IsStaffOrAdmin

class ReturnRequestViewSet(viewsets.ModelViewSet):
    """
    API endpoint for return requests.
    """
    queryset = ReturnRequest.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'order']
    search_fields = ['return_number', 'order__order_number']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return ReturnRequestCreateSerializer
        elif self.action == 'list':
            return ReturnRequestListSerializer
        return ReturnRequestDetailSerializer

    def get_permissions(self):
        if self.action == 'create':
            permission_classes = [IsAuthenticated]
        elif self.action in ['list', 'retrieve', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsOwnerOrStaff]
        else:
            permission_classes = [IsStaffOrAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        # ✅ OPTIMISATION CRUCIALE : select_related et prefetch_related pour éviter N+1 queries
        queryset = ReturnRequest.objects.select_related('user', 'order').prefetch_related('items').all()
        user = self.request.user

        # Filter returns for regular users to only show their own
        if not user.is_staff and not user.is_superuser:
            queryset = queryset.filter(user=user)

        return queryset

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[IsStaffOrAdmin])
    def update_status(self, request, pk=None):
        """
        Update the status of a return request.

        Example request:
        {
            "status": "approved",
            "notes": "Return approved, customer will be refunded"
        }
        """
        return_request = self.get_object()
        new_status = request.data.get('status')
        notes = request.data.get('notes', '')

        if not new_status or new_status not in dict(ReturnRequest.STATUS_CHOICES).keys():
            return Response(
                {'error': 'Invalid status. Must be one of: ' + ', '.join(dict(ReturnRequest.STATUS_CHOICES).keys())},
                status=status.HTTP_400_BAD_REQUEST
            )

        return_request.status = new_status
        return_request.save()

        # If the return is approved and completed, we could trigger a refund process here

        return Response(self.get_serializer(return_request).data)

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def my_returns(self, request):
        """
        Get the authenticated user's return requests.
        """
        queryset = self.get_queryset().filter(user=request.user)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class ClaimViewSet(viewsets.ModelViewSet):
    """
    API endpoint for customer claims/support tickets.
    """
    queryset = Claim.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'subject', 'order']
    search_fields = ['claim_number', 'description', 'order__order_number']
    ordering_fields = ['created_at', 'last_update']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return ClaimCreateSerializer
        elif self.action == 'list':
            return ClaimListSerializer
        return ClaimDetailSerializer

    def get_permissions(self):
        if self.action == 'create':
            permission_classes = [IsAuthenticated]
        elif self.action in ['list', 'retrieve', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsOwnerOrStaff]
        else:
            permission_classes = [IsStaffOrAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        # ✅ OPTIMISATION CRUCIALE : select_related et prefetch_related pour éviter N+1 queries
        queryset = Claim.objects.select_related('user', 'order').prefetch_related(
            'messages', 'status_history', 'messages__attachments'
        ).all()
        user = self.request.user

        # Filter claims for regular users to only show their own
        if not user.is_staff and not user.is_superuser:
            queryset = queryset.filter(user=user)

        return queryset

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[IsStaffOrAdmin])
    def update_status(self, request, pk=None):
        """
        Update the status of a claim.

        Example request:
        {
            "status": "in_progress",
            "comment": "Working on this issue"
        }
        """
        claim = self.get_object()
        new_status = request.data.get('status')
        comment = request.data.get('comment', '')

        if not new_status or new_status not in dict(Claim.STATUS_CHOICES).keys():
            return Response(
                {'error': 'Invalid status. Must be one of: ' + ', '.join(dict(Claim.STATUS_CHOICES).keys())},
                status=status.HTTP_400_BAD_REQUEST
            )

        old_status = claim.status
        claim.status = new_status
        claim.last_update = timezone.now()
        claim.save()

        # Create status history entry
        ClaimStatusHistory.objects.create(
            claim=claim,
            old_status=old_status,
            new_status=new_status,
            comment=comment,
            created_by=request.user
        )

        return Response(self.get_serializer(claim).data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def add_message(self, request, pk=None):
        """
        Add a message to a claim.

        Example request:
        {
            "message": "When will my issue be resolved?",
            "uploaded_files": [file1, file2]
        }
        """
        claim = self.get_object()
        
        # Check if the user is the owner or staff
        if request.user != claim.user and not request.user.is_staff and not request.user.is_superuser:
            return Response(
                {'error': 'You do not have permission to add messages to this claim.'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Determine sender type
        sender = 'customer'
        if request.user.is_staff or request.user.is_superuser:
            sender = 'support'

        # Create serializer with context
        serializer = ClaimMessageSerializer(data={
            'claim': claim.id,
            'sender': sender,
            'message': request.data.get('message', ''),
            'uploaded_files': request.FILES.getlist('uploaded_files', [])
        }, context={'request': request})

        if serializer.is_valid():
            serializer.save()
            
            # Update claim's last_update timestamp
            claim.last_update = timezone.now()
            
            # If status is 'resolved' or 'closed' and customer sends message, reopen to 'waiting_staff'
            if sender == 'customer' and claim.status in ['resolved', 'closed']:
                old_status = claim.status
                claim.status = 'waiting_staff'
                
                # Create status history entry
                ClaimStatusHistory.objects.create(
                    claim=claim,
                    old_status=old_status,
                    new_status='waiting_staff',
                    comment='Client a ajouté un nouveau message',
                    created_by=request.user
                )
            
            # If status is 'waiting_customer' and customer sends message, update to 'waiting_staff'
            elif sender == 'customer' and claim.status == 'waiting_customer':
                claim.status = 'waiting_staff'
                
                # Create status history entry
                ClaimStatusHistory.objects.create(
                    claim=claim,
                    old_status='waiting_customer',
                    new_status='waiting_staff',
                    comment='Client a répondu',
                    created_by=request.user
                )
            
            # If status is 'waiting_staff' and staff sends message, update to 'waiting_customer'
            elif sender == 'support' and claim.status == 'waiting_staff':
                claim.status = 'waiting_customer'
                
                # Create status history entry
                ClaimStatusHistory.objects.create(
                    claim=claim,
                    old_status='waiting_staff',
                    new_status='waiting_customer',
                    comment='Support a répondu',
                    created_by=request.user
                )
            
            claim.save()
            
            # Return updated claim
            return Response(ClaimDetailSerializer(claim, context={'request': request}).data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def my_claims(self, request):
        """
        Get the authenticated user's claims.
        """
        queryset = self.get_queryset().filter(user=request.user)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def messages(self, request, pk=None):
        """
        Get all messages for a specific claim.
        """
        claim = self.get_object()

        # Vérifier les permissions
        if not request.user.is_staff and claim.user != request.user:
            return Response(
                {'error': 'Vous n\'avez pas la permission d\'accéder à cette réclamation.'},
                status=status.HTTP_403_FORBIDDEN
            )

        messages = ClaimMessage.objects.filter(claim=claim).order_by('created_at')
        serializer = ClaimMessageSerializer(messages, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def send_message(self, request, pk=None):
        """
        Send a message to a claim.
        """
        claim = self.get_object()

        # Vérifier les permissions - Les staff/admin peuvent répondre à toutes les réclamations
        if not (request.user.is_staff or request.user.is_superuser or
                getattr(request.user, 'is_admin', False) or
                getattr(request.user, 'is_staff_member', False) or
                claim.user == request.user):
            return Response(
                {'error': 'Vous n\'avez pas la permission d\'envoyer un message à cette réclamation.'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Déterminer le type d'expéditeur
        sender = 'support' if (request.user.is_staff or request.user.is_superuser or
                              getattr(request.user, 'is_admin', False) or
                              getattr(request.user, 'is_staff_member', False)) else 'customer'

        # Créer le message directement pour s'assurer que le sender est correct
        message = ClaimMessage.objects.create(
            claim=claim,
            sender=sender,
            message=request.data.get('message', ''),
        )

        # Gérer les pièces jointes si présentes
        files = request.FILES.getlist('attachments')
        for file in files:
            ClaimAttachment.objects.create(
                message=message,
                    file=file,
                    file_name=file.name,
                    file_size=file.size
                )

        # Mettre à jour la date de dernière modification de la réclamation
        claim.last_update = timezone.now()
        claim.save()

        # Retourner le message créé avec les pièces jointes
        response_serializer = ClaimMessageSerializer(message, context={'request': request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def status_history(self, request, pk=None):
        """
        Get status history for a specific claim.
        """
        claim = self.get_object()

        # Vérifier les permissions
        if not request.user.is_staff and claim.user != request.user:
            return Response(
                {'error': 'Vous n\'avez pas la permission d\'accéder à cette réclamation.'},
                status=status.HTTP_403_FORBIDDEN
            )

        history = ClaimStatusHistory.objects.filter(claim=claim).order_by('created_at')
        serializer = ClaimStatusHistorySerializer(history, many=True, context={'request': request})
        return Response(serializer.data)
