<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Boutons d'Actualisation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .page-link {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.2s;
        }
        .page-link:hover {
            background: #1d4ed8;
        }
        .instructions {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
            margin: 20px 0;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 10px;
        }
        .status-success { background: #22c55e; color: white; }
        .status-warning { background: #f59e0b; color: white; }
        .status-error { background: #ef4444; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Test des Boutons d'Actualisation</h1>

        <div class="instructions">
            <h2>📋 Instructions</h2>
            <p>Cette page vous permet de tester facilement les boutons d'actualisation sur toutes les pages d'administration.</p>
            <ol>
                <li>Cliquez sur un lien de page ci-dessous</li>
                <li>Une fois sur la page, ouvrez la console du navigateur (F12)</li>
                <li>Copiez et collez le script de test dans la console</li>
                <li>Observez les résultats</li>
                <li>Revenez ici pour tester une autre page</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🔗 Pages d'Administration</h2>
            <p>Cliquez sur une page pour la tester :</p>

            <a href="/admin/logs" class="page-link" target="_blank">📊 Logs</a>
            <a href="/admin/promotions" class="page-link" target="_blank">🎯 Promotions</a>
            <a href="/admin/categories" class="page-link" target="_blank">📁 Catégories</a>
            <a href="/admin/products" class="page-link" target="_blank">📦 Produits</a>
            <a href="/admin/inventory" class="page-link" target="_blank">📋 Inventaire</a>
            <a href="/admin/users" class="page-link" target="_blank">👥 Utilisateurs</a>
            <a href="/admin/cms" class="page-link" target="_blank">📝 CMS</a>
            <a href="/admin/products/inventory" class="page-link" target="_blank">🏭 Gestion Stocks</a>
            <a href="/admin/orders" class="page-link" target="_blank">🛒 Commandes</a>
            <a href="/admin/invoices" class="page-link" target="_blank">🧾 Factures</a>
            <a href="/admin/transactions" class="page-link" target="_blank">💳 Transactions</a>
        </div>

        <div class="test-section">
            <h2>🔧 Test des Composants Select</h2>
            <p>Script pour tester les composants Select et détecter les valeurs vides :</p>

            <div class="code-block" id="selectTestScript">
// Script de test des composants Select
(function() {
    console.log('🔍 Test des composants Select');
    console.log('=============================');

    const selects = document.querySelectorAll('[data-radix-select-trigger], [role="combobox"]');
    console.log(`✅ ${selects.length} composant(s) Select trouvé(s)`);

    let problemsFound = 0;

    selects.forEach((select, index) => {
        console.log(`\n🔍 Select ${index + 1}:`);
        console.log(`   Texte: "${select.textContent.trim()}"`);

        // Simuler un clic pour ouvrir le menu
        select.click();

        setTimeout(() => {
            const options = document.querySelectorAll('[data-radix-select-item]');
            console.log(`   Options: ${options.length}`);

            options.forEach((option, i) => {
                const value = option.getAttribute('data-value') || '';
                const text = option.textContent.trim();

                if (value === '') {
                    console.log(`   ❌ Option ${i + 1} a une valeur vide: "${text}"`);
                    problemsFound++;
                } else {
                    console.log(`   ✅ Option ${i + 1}: "${value}" - "${text}"`);
                }
            });

            // Fermer le menu
            document.body.click();
        }, 300);
    });

    setTimeout(() => {
        if (problemsFound === 0) {
            console.log('\n🎉 Aucun problème détecté !');
        } else {
            console.log(`\n⚠️  ${problemsFound} problème(s) détecté(s)`);
        }
    }, 1000);
})();
            </div>

            <button onclick="copySelectScript()" style="margin-top: 10px; padding: 10px 20px; background: #ef4444; color: white; border: none; border-radius: 5px; cursor: pointer;">
                📋 Copier le Script Select
            </button>
        </div>

        <div class="test-section">
            <h2>🧪 Script de Test des Boutons</h2>
            <p>Copiez ce script et collez-le dans la console du navigateur sur chaque page :</p>

            <div class="code-block" id="testScript">
// Script de test des boutons d'actualisation
(function() {
    console.log('🔄 Test des boutons d\'actualisation');
    console.log('=====================================');

    // Sélecteurs pour les boutons d'actualisation
    const selectors = [
        'button[title*="Actualiser"]',
        'button[title*="Rafraîchir"]',
        'button:has(.lucide-refresh-cw)',
        'button:has(.lucide-refresh-ccw)',
        'button[data-testid*="refresh"]'
    ];

    // Trouver les boutons
    let buttons = [];
    selectors.forEach(selector => {
        try {
            buttons.push(...document.querySelectorAll(selector));
        } catch(e) {}
    });

    // Ajouter les boutons avec texte "Actualiser" ou "Rafraîchir"
    const textButtons = Array.from(document.querySelectorAll('button')).filter(btn =>
        btn.textContent.includes('Actualiser') || btn.textContent.includes('Rafraîchir')
    );
    buttons.push(...textButtons);

    // Supprimer les doublons
    buttons = [...new Set(buttons)];

    if (buttons.length === 0) {
        console.log('❌ Aucun bouton d\'actualisation trouvé');
        return;
    }

    console.log(`✅ ${buttons.length} bouton(s) trouvé(s)`);

    // Tester chaque bouton
    buttons.forEach((btn, index) => {
        console.log(`\n🔍 Bouton ${index + 1}:`);
        console.log(`   Texte: "${btn.textContent.trim()}"`);
        console.log(`   Visible: ${btn.offsetParent !== null}`);
        console.log(`   Activé: ${!btn.disabled}`);

        if (btn.offsetParent !== null && !btn.disabled) {
            console.log('   🖱️  Test du clic...');

            // Observer les changements
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' || mutation.type === 'attributes') {
                        const loadingElements = document.querySelectorAll('.animate-spin, [data-testid*="loading"]');
                        if (loadingElements.length > 0) {
                            console.log('   ⏳ Chargement détecté !');
                        }
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'data-testid']
            });

            // Cliquer sur le bouton
            try {
                btn.click();
                console.log('   ✅ Clic effectué');

                // Arrêter l'observation après 3 secondes
                setTimeout(() => {
                    observer.disconnect();
                    console.log('   🏁 Test terminé');
                }, 3000);

            } catch (error) {
                console.log(`   ❌ Erreur: ${error.message}`);
                observer.disconnect();
            }
        } else {
            console.log('   ⚠️  Bouton non interactable');
        }
    });

    console.log('\n🎯 Résumé: Vérifiez les messages ci-dessus pour voir si les boutons fonctionnent');
})();
            </div>

            <button onclick="copyScript()" style="margin-top: 10px; padding: 10px 20px; background: #22c55e; color: white; border: none; border-radius: 5px; cursor: pointer;">
                📋 Copier le Script
            </button>
        </div>

        <div class="test-section">
            <h2>📊 Résultats Attendus</h2>
            <p>Pour chaque bouton d'actualisation fonctionnel, vous devriez voir :</p>
            <ul>
                <li>✅ <strong>Clic effectué</strong> - Le bouton répond au clic</li>
                <li>⏳ <strong>Chargement détecté</strong> - Un indicateur de chargement apparaît</li>
                <li>📢 <strong>Notification</strong> - Un message de succès s'affiche (optionnel)</li>
                <li>🔄 <strong>Données mises à jour</strong> - Le contenu de la page se rafraîchit</li>
            </ul>
        </div>

        <div class="results" id="results" style="display: none;">
            <h2>📈 Résultats des Tests</h2>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        function copyScript() {
            const script = document.getElementById('testScript').textContent;
            navigator.clipboard.writeText(script).then(() => {
                alert('✅ Script copié dans le presse-papiers !');
            }).catch(() => {
                alert('❌ Erreur lors de la copie. Sélectionnez et copiez manuellement le script.');
            });
        }

        function copySelectScript() {
            const script = document.getElementById('selectTestScript').textContent;
            navigator.clipboard.writeText(script).then(() => {
                alert('✅ Script Select copié dans le presse-papiers !');
            }).catch(() => {
                alert('❌ Erreur lors de la copie. Sélectionnez et copiez manuellement le script.');
            });
        }

        // Fonction pour afficher les résultats (peut être appelée depuis la console)
        window.showTestResults = function(results) {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('resultsContent');

            let html = '<h3>Résultats par page :</h3>';

            Object.entries(results).forEach(([page, result]) => {
                const status = result.success ? 'success' : 'error';
                const statusText = result.success ? 'SUCCÈS' : 'ÉCHEC';

                html += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #e5e7eb; border-radius: 5px;">
                        <strong>${page}</strong>
                        <span class="status status-${status}">${statusText}</span>
                        <br>
                        <small>Boutons trouvés: ${result.buttonsFound || 0}</small>
                    </div>
                `;
            });

            contentDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        };

        console.log('🔄 Page de test des boutons d\'actualisation chargée');
        console.log('📋 Utilisez les liens ci-dessus pour tester chaque page');
        console.log('🧪 Copiez le script de test et exécutez-le sur chaque page');
    </script>
</body>
</html>
