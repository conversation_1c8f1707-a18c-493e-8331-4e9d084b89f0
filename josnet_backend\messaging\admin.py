from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import Conversation, Message, Attachment, Category, Tag, ResponseTemplate

class AttachmentInline(admin.TabularInline):
    model = Attachment
    extra = 0
    readonly_fields = ('file', 'file_name', 'file_size', 'file_type', 'created_at')
    can_delete = False
    max_num = 0  # Don't allow adding attachments from admin

class MessageInline(admin.TabularInline):
    model = Message
    extra = 0
    readonly_fields = ('sender', 'content', 'created_at', 'is_from_admin')
    can_delete = False
    max_num = 0  # Don't allow adding messages from admin
    show_change_link = True

@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ('subject', 'customer_email', 'status_display', 'priority_display', 'category_display',
                   'message_count_display', 'created_at', 'last_message_at', 'is_read_status', 'assigned_to_display')
    list_filter = ('status', 'priority', 'category', 'is_read_by_admin', 'created_at', 'last_message_at', 'assigned_to')
    search_fields = ('subject', 'customer__email', 'customer__first_name', 'customer__last_name', 'messages__content')
    readonly_fields = ('created_at', 'updated_at', 'last_message_at')
    inlines = [MessageInline]
    date_hierarchy = 'created_at'
    filter_horizontal = ('tags',)

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('subject', 'customer', 'status', 'priority', 'is_read_by_admin', 'is_read_by_customer')
        }),
        (_('Categorization'), {
            'fields': ('category', 'tags')
        }),
        (_('Integration'), {
            'fields': ('order', 'assigned_to')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at', 'last_message_at'),
            'classes': ('collapse',)
        }),
    )

    def customer_email(self, obj):
        return obj.customer.email
    customer_email.short_description = _('Customer')

    def message_count_display(self, obj):
        return obj.messages.count()
    message_count_display.short_description = _('Messages')

    def is_read_status(self, obj):
        if obj.is_read_by_admin:
            return format_html('<span style="color: green;">Read</span>')
        return format_html('<span style="color: red; font-weight: bold;">Unread</span>')
    is_read_status.short_description = _('Read Status')

    def status_display(self, obj):
        status_colors = {
            'new': '#f39c12',  # Orange
            'in_progress': '#3498db',  # Blue
            'resolved': '#2ecc71',  # Green
            'closed': '#95a5a6',  # Gray
        }
        color = status_colors.get(obj.status, '#000')
        status_text = dict(Conversation.STATUSES).get(obj.status, obj.status)
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, status_text)
    status_display.short_description = _('Status')

    def priority_display(self, obj):
        priority_colors = {
            'low': '#95a5a6',  # Gray
            'medium': '#3498db',  # Blue
            'high': '#f39c12',  # Orange
            'urgent': '#e74c3c',  # Red
        }
        color = priority_colors.get(obj.priority, '#000')
        priority_text = dict(Conversation.PRIORITIES).get(obj.priority, obj.priority)
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, priority_text)
    priority_display.short_description = _('Priority')

    def category_display(self, obj):
        if obj.category:
            return format_html(
                '<span style="background-color: {}; color: #fff; padding: 2px 8px; border-radius: 3px;">{}</span>',
                obj.category.color, obj.category.name
            )
        return '-'
    category_display.short_description = _('Category')

    def assigned_to_display(self, obj):
        if obj.assigned_to:
            return obj.assigned_to.get_full_name() or obj.assigned_to.email
        return '-'
    assigned_to_display.short_description = _('Assigned To')

    def get_queryset(self, request):
        """Optimize queryset with prefetch_related and select_related."""
        return super().get_queryset(request).select_related(
            'customer', 'category', 'assigned_to'
        ).prefetch_related('tags', 'messages')

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('truncated_content', 'conversation_subject', 'sender_email', 'is_from_admin', 'created_at')
    list_filter = ('is_from_admin', 'created_at')
    search_fields = ('content', 'conversation__subject', 'sender__email')
    readonly_fields = ('conversation', 'sender', 'content', 'created_at', 'is_from_admin')
    inlines = [AttachmentInline]
    date_hierarchy = 'created_at'

    def truncated_content(self, obj):
        if len(obj.content) > 50:
            return obj.content[:50] + '...'
        return obj.content
    truncated_content.short_description = 'Content'

    def conversation_subject(self, obj):
        return obj.conversation.subject
    conversation_subject.short_description = 'Conversation'

    def sender_email(self, obj):
        return obj.sender.email
    sender_email.short_description = 'Sender'

    def has_add_permission(self, request):
        return False  # Don't allow adding messages from admin

@admin.register(Attachment)
class AttachmentAdmin(admin.ModelAdmin):
    list_display = ('file_name', 'message_preview', 'file_size_display', 'file_type', 'created_at')
    list_filter = ('file_type', 'created_at')
    search_fields = ('file_name', 'message__content', 'message__conversation__subject')
    readonly_fields = ('message', 'file', 'file_name', 'file_size', 'file_type', 'created_at')
    date_hierarchy = 'created_at'

    def message_preview(self, obj):
        if len(obj.message.content) > 30:
            return obj.message.content[:30] + '...'
        return obj.message.content
    message_preview.short_description = 'Message'

    def file_size_display(self, obj):
        # Convert bytes to KB or MB
        size_bytes = obj.file_size
        if size_bytes < 1024:
            return f"{size_bytes} bytes"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    file_size_display.short_description = 'Size'

    def has_add_permission(self, request):
        return False  # Don't allow adding attachments from admin


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'conversation_count_display', 'color_display', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')

    def conversation_count_display(self, obj):
        count = obj.conversation_count
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    conversation_count_display.short_description = _('Conversations')

    def color_display(self, obj):
        return format_html(
            '<span style="background-color: {}; color: #fff; padding: 3px 10px; border-radius: 3px;">{}</span>',
            obj.color, obj.color
        )
    color_display.short_description = _('Color')


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ('name', 'conversation_count_display', 'color_display', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name',)

    def conversation_count_display(self, obj):
        count = obj.conversation_count
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    conversation_count_display.short_description = _('Conversations')

    def color_display(self, obj):
        return format_html(
            '<span style="background-color: {}; color: #fff; padding: 3px 10px; border-radius: 3px;">{}</span>',
            obj.color, obj.color
        )
    color_display.short_description = _('Color')


@admin.register(ResponseTemplate)
class ResponseTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'created_by', 'is_active', 'created_at')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('name', 'content')
    readonly_fields = ('created_at', 'updated_at', 'created_by')

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
