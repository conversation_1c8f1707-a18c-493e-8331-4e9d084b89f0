/**
 * Hook personnalisé pour récupérer les données dynamiques de la page d'accueil
 */

import { useState, useEffect } from 'react';
import coreApi, { HomePageData, HomePageStats, Service, Testimonial } from '@/services/coreApi';

interface UseHomePageDataReturn {
  data: HomePageData | null;
  stats: HomePageStats | null;
  services: Service[];
  testimonials: Testimonial[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useHomePageData = (): UseHomePageDataReturn => {
  const [data, setData] = useState<HomePageData | null>(null);
  const [stats, setStats] = useState<HomePageStats | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les statistiques en temps réel en priorité
      const [liveStats, featuredServices, featuredTestimonials] = await Promise.all([
        coreApi.getLiveStats(),
        coreApi.getFeaturedServices(),
        coreApi.getFeaturedTestimonials()
      ]);

      // Construire les données consolidées avec les vraies statistiques
      const consolidatedData = {
        site_settings: null,
        stats: liveStats,
        featured_services: featuredServices,
        featured_testimonials: featuredTestimonials,
        hero_banners: [],
        last_updated: new Date().toISOString()
      };

      setData(consolidatedData);
      setStats(liveStats);
      setServices(featuredServices);
      setTestimonials(featuredTestimonials);

    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return {
    data,
    stats,
    services,
    testimonials,
    loading,
    error,
    refetch: fetchData
  };
};

// Hook pour les statistiques uniquement
export const useHomePageStats = () => {
  const [stats, setStats] = useState<HomePageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      // Utiliser les statistiques en temps réel
      const statsData = await coreApi.getLiveStats();
      setStats(statsData);
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques en temps réel:', err);
      setError('Impossible de récupérer les statistiques. Vérifiez que le serveur backend est démarré.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  };
};

// Hook pour les services uniquement
export const useServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);

      const servicesData = await coreApi.getFeaturedServices();
      setServices(servicesData);
    } catch (err) {
      console.error('Erreur lors du chargement des services:', err);
      setError('Erreur lors du chargement des services');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, []);

  return {
    services,
    loading,
    error,
    refetch: fetchServices
  };
};

// Hook pour les témoignages uniquement
export const useTestimonials = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTestimonials = async () => {
    try {
      setLoading(true);
      setError(null);

      const testimonialsData = await coreApi.getFeaturedTestimonials();
      setTestimonials(testimonialsData);
    } catch (err) {
      console.error('Erreur lors du chargement des témoignages:', err);
      setError('Erreur lors du chargement des témoignages');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTestimonials();
  }, []);

  return {
    testimonials,
    loading,
    error,
    refetch: fetchTestimonials
  };
};
