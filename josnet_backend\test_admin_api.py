#!/usr/bin/env python
"""
Test pour vérifier l'API d'administration des utilisateurs.
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


def test_admin_api_endpoints():
    """Test des endpoints d'administration."""
    print("🔍 TEST DES ENDPOINTS D'ADMINISTRATION")
    print("=" * 60)
    
    # Créer un utilisateur admin pour les tests
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Admin',
            'last_name': 'Test',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
        print(f"✅ Admin créé: {admin_user.email}")
    else:
        print(f"✅ Admin existant: {admin_user.email}")
    
    # Créer un utilisateur de test
    test_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Utilisateur de test créé: {test_user.email}")
    else:
        print(f"✅ Utilisateur de test existant: {test_user.email}")
    
    # Créer un token JWT pour l'admin
    refresh = RefreshToken.for_user(admin_user)
    access_token = str(refresh.access_token)
    
    print(f"✅ Token JWT généré pour l'admin")
    
    # Tester avec APIClient Django
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"\n🔄 Test 1: Liste des utilisateurs")
    response = client.get('/api/v1/auth/admin/users/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Nombre d'utilisateurs: {len(data.get('results', []))}")
    else:
        print(f"   ❌ Erreur: {response.content}")
    
    print(f"\n🔄 Test 2: Détail de l'utilisateur {test_user.id}")
    response = client.get(f'/api/v1/auth/admin/users/{test_user.id}/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Utilisateur: {data.get('email')} (actif: {data.get('is_active')})")
    else:
        print(f"   ❌ Erreur: {response.content}")
    
    print(f"\n🔄 Test 3: Activation/désactivation de l'utilisateur {test_user.id}")
    response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/set_active/', {
        'is_active': False
    }, format='json')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Utilisateur désactivé: {data.get('email')} (actif: {data.get('is_active')})")
    else:
        print(f"   ❌ Erreur: {response.content}")
    
    # Réactiver l'utilisateur
    response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/set_active/', {
        'is_active': True
    }, format='json')
    print(f"   Status réactivation: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Utilisateur réactivé: {data.get('email')} (actif: {data.get('is_active')})")
    
    print(f"\n🔄 Test 4: Vérification de l'utilisateur {test_user.id}")
    response = client.post(f'/api/v1/auth/admin/users/{test_user.id}/verify/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Utilisateur vérifié: {data.get('email')} (vérifié: {data.get('is_verified')})")
    else:
        print(f"   ❌ Erreur: {response.content}")
    
    return True


def test_with_requests():
    """Test avec la bibliothèque requests (simulation du frontend)."""
    print(f"\n🌐 TEST AVEC REQUESTS (SIMULATION FRONTEND)")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Tenter de se connecter pour obtenir un token
    login_data = {
        'email': '<EMAIL>',
        'password': 'testpass123'
    }
    
    try:
        print(f"🔄 Tentative de connexion à {base_url}/api/v1/auth/login/")
        response = requests.post(f"{base_url}/api/v1/auth/login/", json=login_data, timeout=5)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access')
            print(f"   ✅ Token obtenu")
            
            # Tester l'endpoint set_active
            headers = {'Authorization': f'Bearer {access_token}'}
            test_user = User.objects.get(email='<EMAIL>')
            
            print(f"\n🔄 Test set_active avec requests")
            response = requests.post(
                f"{base_url}/api/v1/auth/admin/users/{test_user.id}/set_active/",
                json={'is_active': False},
                headers=headers,
                timeout=5
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ Endpoint fonctionne avec requests")
            else:
                print(f"   ❌ Erreur: {response.text}")
        else:
            print(f"   ❌ Échec de connexion: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Impossible de se connecter au serveur Django")
        print(f"   ⚠️  Assurez-vous que le serveur Django fonctionne sur le port 8000")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    
    return True


def check_urls_configuration():
    """Vérifier la configuration des URLs."""
    print(f"\n⚙️  VÉRIFICATION DE LA CONFIGURATION DES URLS")
    print("=" * 60)
    
    from django.urls import reverse
    from django.core.exceptions import NoReverseMatch
    
    try:
        # Tenter de résoudre l'URL
        url = reverse('admin-user-set-active', kwargs={'pk': 1})
        print(f"✅ URL résolue: {url}")
    except NoReverseMatch:
        print(f"❌ Impossible de résoudre l'URL 'admin-user-set-active'")
    
    # Vérifier les URLs disponibles
    from django.conf import settings
    from django.urls import get_resolver
    
    resolver = get_resolver()
    
    print(f"\n📋 URLs d'authentification disponibles:")
    for pattern in resolver.url_patterns:
        if hasattr(pattern, 'pattern') and 'auth' in str(pattern.pattern):
            print(f"   - {pattern.pattern}")
    
    return True


def main():
    """Fonction principale."""
    print("🚀 DIAGNOSTIC DE L'API D'ADMINISTRATION")
    print("=" * 80)
    
    try:
        # Test 1: Configuration des URLs
        check_urls_configuration()
        
        # Test 2: API avec Django test client
        test_admin_api_endpoints()
        
        # Test 3: API avec requests (simulation frontend)
        test_with_requests()
        
        print(f"\n" + "=" * 80)
        print("📊 RÉSUMÉ DU DIAGNOSTIC")
        print("=" * 80)
        print("✅ Si tous les tests passent, l'API fonctionne correctement")
        print("❌ Si des tests échouent, vérifiez:")
        print("   1. Le serveur Django fonctionne sur le port 8000")
        print("   2. Les URLs sont correctement configurées")
        print("   3. Les permissions sont correctes")
        print("   4. Le frontend utilise la bonne URL de base")
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU DIAGNOSTIC: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
