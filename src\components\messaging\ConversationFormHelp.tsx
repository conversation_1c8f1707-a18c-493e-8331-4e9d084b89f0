import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  HelpCircle,
  User,
  MessageSquare,
  Tag,
  Upload,
  Package,
  CheckCircle,
  AlertTriangle,
  Info,
  Search,
  Plus
} from 'lucide-react';

const ConversationFormHelp: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <HelpCircle className="h-4 w-4 mr-2" />
          Aide
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <HelpCircle className="h-5 w-5 mr-2" />
            Guide du formulaire de conversation
          </DialogTitle>
          <DialogDescription>
            Apprenez à créer efficacement une nouvelle conversation avec un client
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Accordion type="single" collapsible className="w-full">
            {/* Client Selection */}
            <AccordionItem value="client-selection">
              <AccordionTrigger className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                Sélection du client
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center">
                    <Search className="h-4 w-4 mr-2" />
                    Rechercher un client existant
                  </h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-6">
                    <li>• Tapez l'email ou le nom du client</li>
                    <li>• La recherche démarre automatiquement après 3 caractères</li>
                    <li>• Cliquez sur le client souhaité dans les résultats</li>
                    <li>• Les informations du client s'affichent (commandes, statut)</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center">
                    <Plus className="h-4 w-4 mr-2" />
                    Créer un nouveau client
                  </h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-6">
                    <li>• Cliquez sur "Créer un nouveau client"</li>
                    <li>• Remplissez l'email (obligatoire) et le nom complet (obligatoire)</li>
                    <li>• Le téléphone est optionnel mais recommandé</li>
                    <li>• Le client sera créé automatiquement avec la conversation</li>
                  </ul>
                </div>

                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-700">
                    <Info className="h-4 w-4 inline mr-1" />
                    <strong>Astuce :</strong> Vérifiez toujours si le client existe avant d'en créer un nouveau pour éviter les doublons.
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Conversation Details */}
            <AccordionItem value="conversation-details">
              <AccordionTrigger className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                Détails de la conversation
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium">Champs obligatoires</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• <strong>Sujet :</strong> Titre descriptif de la conversation</li>
                    <li>• <strong>Message initial :</strong> Premier message de la conversation</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Niveaux de priorité</h4>
                  <div className="space-y-2 ml-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="text-sm"><strong>Faible :</strong> Questions générales, demandes d'information</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                      <span className="text-sm"><strong>Moyenne :</strong> Demandes standard, support technique</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                      <span className="text-sm"><strong>Élevée :</strong> Problèmes importants, réclamations</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                      <span className="text-sm"><strong>Urgente :</strong> Situations critiques, pannes</span>
                    </div>
                  </div>
                </div>

                <div className="p-3 bg-yellow-50 rounded-lg">
                  <p className="text-sm text-yellow-700">
                    <AlertTriangle className="h-4 w-4 inline mr-1" />
                    <strong>Important :</strong> Utilisez les priorités élevées avec parcimonie pour maintenir leur efficacité.
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Categories and Tags */}
            <AccordionItem value="categories-tags">
              <AccordionTrigger className="flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                Catégories et étiquettes
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium">Catégories disponibles</h4>
                  <div className="grid grid-cols-2 gap-2 ml-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                      <span className="text-sm">Support technique</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="text-sm">Ventes</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                      <span className="text-sm">Facturation</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                      <span className="text-sm">Livraison</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Utilisation des étiquettes</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• Cliquez sur une étiquette pour la sélectionner/désélectionner</li>
                    <li>• Plusieurs étiquettes peuvent être sélectionnées</li>
                    <li>• Les étiquettes sélectionnées changent de couleur</li>
                    <li>• Utilisez les étiquettes pour faciliter le tri et la recherche</li>
                  </ul>
                </div>

                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="text-xs">Urgent</Badge>
                  <Badge variant="outline" className="text-xs">Bug</Badge>
                  <Badge variant="outline" className="text-xs">Fonctionnalité</Badge>
                  <Badge variant="outline" className="text-xs">Question</Badge>
                  <Badge variant="outline" className="text-xs">Retour</Badge>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* File Upload */}
            <AccordionItem value="file-upload">
              <AccordionTrigger className="flex items-center">
                <Upload className="h-4 w-4 mr-2" />
                Pièces jointes
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium">Fichiers acceptés</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• <strong>Images :</strong> JPG, PNG, GIF, WebP</li>
                    <li>• <strong>Documents :</strong> PDF, DOC, DOCX, TXT</li>
                    <li>• <strong>Archives :</strong> ZIP, RAR</li>
                    <li>• <strong>Taille max :</strong> 10 MB par fichier</li>
                    <li>• <strong>Nombre max :</strong> 5 fichiers par conversation</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Comment ajouter des fichiers</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• Cliquez sur la zone de téléchargement</li>
                    <li>• Ou glissez-déposez les fichiers directement</li>
                    <li>• Les fichiers s'affichent avec leur statut d'upload</li>
                    <li>• Vous pouvez supprimer un fichier avant l'envoi</li>
                  </ul>
                </div>

                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-700">
                    <CheckCircle className="h-4 w-4 inline mr-1" />
                    <strong>Conseil :</strong> Ajoutez des captures d'écran pour les problèmes techniques ou des documents pour les demandes complexes.
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Order Reference */}
            <AccordionItem value="order-reference">
              <AccordionTrigger className="flex items-center">
                <Package className="h-4 w-4 mr-2" />
                Référence commande
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium">Quand utiliser une référence commande</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• Questions sur une commande spécifique</li>
                    <li>• Problèmes de livraison</li>
                    <li>• Demandes de modification ou d'annulation</li>
                    <li>• Support après-vente</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Avantages</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• Contexte immédiat pour l'agent</li>
                    <li>• Accès rapide aux détails de la commande</li>
                    <li>• Historique complet des interactions</li>
                    <li>• Résolution plus efficace</li>
                  </ul>
                </div>

                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-700">
                    <Info className="h-4 w-4 inline mr-1" />
                    <strong>Note :</strong> Ce champ est optionnel. Laissez vide si la conversation n'est pas liée à une commande spécifique.
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Best Practices */}
            <AccordionItem value="best-practices">
              <AccordionTrigger className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Bonnes pratiques
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium">Rédaction efficace</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• Utilisez un sujet clair et descriptif</li>
                    <li>• Incluez toutes les informations pertinentes dans le message initial</li>
                    <li>• Soyez professionnel et courtois</li>
                    <li>• Structurez votre message avec des paragraphes</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Organisation</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• Assignez toujours une catégorie appropriée</li>
                    <li>• Utilisez des étiquettes pour faciliter le suivi</li>
                    <li>• Définissez la priorité selon l'urgence réelle</li>
                    <li>• Ajoutez une référence commande si pertinente</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Suivi</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• Vérifiez le résumé avant de créer la conversation</li>
                    <li>• Assurez-vous que toutes les informations sont correctes</li>
                    <li>• Préparez-vous à répondre rapidement au client</li>
                  </ul>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="flex justify-end pt-4 border-t">
            <Button onClick={() => setIsOpen(false)}>
              Compris
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ConversationFormHelp;
