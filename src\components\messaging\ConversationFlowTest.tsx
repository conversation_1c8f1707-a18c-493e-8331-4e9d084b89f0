import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  MessageSquare,
  Mail,
  Phone,
  CheckCircle,
  XCircle,
  Clock,
  Send,
  User,
  ArrowRight
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import notificationService from '@/services/notificationService';
import messagingApi from '@/services/messagingApi';

const ConversationFlowTest: React.FC = () => {
  const { toast } = useToast();
  const [testData, setTestData] = useState({
    customerEmail: '<EMAIL>',
    customerName: 'Client Test',
    customerPhone: '+257 79 123 456',
    subject: 'Test de communication - Demande d\'information',
    message: 'Bonjour, je souhaiterais obtenir des informations sur vos services de support technique. Pouvez-vous me contacter ? Merci.'
  });

  const [testResults, setTestResults] = useState<{
    step: number;
    conversationCreated: boolean;
    emailSent: boolean;
    smsSent: boolean;
    error: string | null;
    conversationId: number | null;
  }>({
    step: 0,
    conversationCreated: false,
    emailSent: false,
    smsSent: false,
    error: null,
    conversationId: null
  });

  const [isTestRunning, setIsTestRunning] = useState(false);

  const runCompleteTest = async () => {
    setIsTestRunning(true);
    setTestResults({
      step: 0,
      conversationCreated: false,
      emailSent: false,
      smsSent: false,
      error: null,
      conversationId: null
    });

    try {
      // Étape 1: Créer la conversation
      setTestResults(prev => ({ ...prev, step: 1 }));
      
      const conversationData = {
        subject: testData.subject,
        initial_message: testData.message,
        customer_email: testData.customerEmail,
        customer_name: testData.customerName,
        customer_phone: testData.customerPhone,
        priority: 'medium'
      };

      console.log('🔄 Étape 1: Création de la conversation...');
      const conversation = await messagingApi.createConversation(conversationData);
      
      setTestResults(prev => ({ 
        ...prev, 
        step: 2, 
        conversationCreated: true,
        conversationId: conversation.id 
      }));

      console.log('✅ Conversation créée:', conversation);

      // Étape 2: Envoyer notification email
      setTestResults(prev => ({ ...prev, step: 3 }));
      
      console.log('🔄 Étape 2: Envoi de notification email...');
      const emailResult = await notificationService.notifyNewConversation(
        testData.customerEmail,
        testData.customerName,
        testData.subject,
        testData.message,
        conversation.id,
        'Équipe Support Test'
      );

      setTestResults(prev => ({ 
        ...prev, 
        step: 4, 
        emailSent: emailResult.success 
      }));

      console.log('📧 Résultat email:', emailResult);

      // Étape 3: Envoyer notification SMS (si numéro disponible)
      if (testData.customerPhone) {
        setTestResults(prev => ({ ...prev, step: 5 }));
        
        console.log('🔄 Étape 3: Envoi de notification SMS...');
        const smsResult = await notificationService.sendSMSNotification({
          recipient_phone: testData.customerPhone,
          recipient_name: testData.customerName,
          message: `Nouvelle conversation JosNet: "${testData.subject}". Consultez votre email pour plus de détails.`,
          conversation_id: conversation.id
        });

        setTestResults(prev => ({ 
          ...prev, 
          step: 6, 
          smsSent: smsResult.success 
        }));

        console.log('📱 Résultat SMS:', smsResult);
      }

      // Test terminé avec succès
      setTestResults(prev => ({ ...prev, step: 7 }));
      
      toast({
        title: "Test réussi !",
        description: "Le flux de communication complet fonctionne correctement.",
      });

    } catch (error: any) {
      console.error('❌ Erreur lors du test:', error);
      
      setTestResults(prev => ({ 
        ...prev, 
        error: error.message || 'Erreur inconnue',
        step: -1 
      }));

      toast({
        title: "Test échoué",
        description: error.message || "Une erreur est survenue lors du test.",
        variant: "destructive",
      });
    } finally {
      setIsTestRunning(false);
    }
  };

  const getStepStatus = (stepNumber: number) => {
    if (testResults.step > stepNumber) return 'completed';
    if (testResults.step === stepNumber) return 'current';
    return 'pending';
  };

  const getStepIcon = (stepNumber: number) => {
    const status = getStepStatus(stepNumber);
    if (status === 'completed') return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (status === 'current') return <Clock className="h-5 w-5 text-blue-500 animate-pulse" />;
    return <div className="h-5 w-5 rounded-full border-2 border-gray-300"></div>;
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="h-6 w-6 mr-2" />
            Test du flux de communication complet
          </CardTitle>
          <CardDescription>
            Testez la création d'une conversation et l'envoi automatique de notifications au client
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration du test */}
          <div className="space-y-4">
            <h3 className="font-medium">Configuration du test</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Email du client</label>
                <Input
                  value={testData.customerEmail}
                  onChange={(e) => setTestData(prev => ({ ...prev, customerEmail: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Nom du client</label>
                <Input
                  value={testData.customerName}
                  onChange={(e) => setTestData(prev => ({ ...prev, customerName: e.target.value }))}
                  placeholder="Nom du client"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Téléphone (optionnel)</label>
                <Input
                  value={testData.customerPhone}
                  onChange={(e) => setTestData(prev => ({ ...prev, customerPhone: e.target.value }))}
                  placeholder="+257 79 123 456"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Sujet</label>
                <Input
                  value={testData.subject}
                  onChange={(e) => setTestData(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="Sujet de la conversation"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Message</label>
              <Textarea
                value={testData.message}
                onChange={(e) => setTestData(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Message initial de la conversation"
                rows={3}
              />
            </div>
          </div>

          {/* Bouton de test */}
          <Button 
            onClick={runCompleteTest}
            disabled={isTestRunning}
            className="w-full"
            size="lg"
          >
            {isTestRunning ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Test en cours...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Lancer le test complet
              </>
            )}
          </Button>

          {/* Progression du test */}
          {testResults.step > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium">Progression du test</h3>
              
              <div className="space-y-3">
                {/* Étape 1: Création conversation */}
                <div className="flex items-center space-x-3">
                  {getStepIcon(2)}
                  <div className="flex-1">
                    <div className="font-medium">Création de la conversation</div>
                    <div className="text-sm text-gray-500">
                      Création d'une nouvelle conversation avec les données client
                    </div>
                  </div>
                  {testResults.conversationCreated && (
                    <Badge variant="default">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Créée
                    </Badge>
                  )}
                </div>

                {/* Étape 2: Email */}
                <div className="flex items-center space-x-3">
                  {getStepIcon(4)}
                  <div className="flex-1">
                    <div className="font-medium flex items-center">
                      <Mail className="h-4 w-4 mr-2" />
                      Notification email
                    </div>
                    <div className="text-sm text-gray-500">
                      Envoi d'un email de notification au client
                    </div>
                  </div>
                  {testResults.emailSent && (
                    <Badge variant="default">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Envoyé
                    </Badge>
                  )}
                </div>

                {/* Étape 3: SMS */}
                {testData.customerPhone && (
                  <div className="flex items-center space-x-3">
                    {getStepIcon(6)}
                    <div className="flex-1">
                      <div className="font-medium flex items-center">
                        <Phone className="h-4 w-4 mr-2" />
                        Notification SMS
                      </div>
                      <div className="text-sm text-gray-500">
                        Envoi d'un SMS de notification au client
                      </div>
                    </div>
                    {testResults.smsSent && (
                      <Badge variant="default">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Envoyé
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              {/* Résultat final */}
              {testResults.step === 7 && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>✅ Test réussi !</strong>
                    <br />
                    La conversation a été créée (ID: {testResults.conversationId}) et le client a été notifié.
                    <br />
                    📧 Email envoyé à: {testData.customerEmail}
                    {testData.customerPhone && (
                      <>
                        <br />
                        📱 SMS envoyé à: {testData.customerPhone}
                      </>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              {/* Erreur */}
              {testResults.error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>❌ Test échoué</strong>
                    <br />
                    {testResults.error}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Informations sur le flux */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Flux de communication testé :</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                Admin crée une conversation
              </div>
              <div className="flex items-center">
                <ArrowRight className="h-4 w-4 mr-2" />
                Conversation enregistrée en base
              </div>
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                Email automatique envoyé au client
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-2" />
                SMS automatique envoyé (si numéro disponible)
              </div>
              <div className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                Client peut répondre et continuer la conversation
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConversationFlowTest;
