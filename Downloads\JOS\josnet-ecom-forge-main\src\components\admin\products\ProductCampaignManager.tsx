import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Megaphone, 
  Calendar, 
  Percent, 
  Tag, 
  AlertCircle, 
  RefreshCw, 
  Plus, 
  Search, 
  X, 
  Check 
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { formatPrice } from '@/lib/utils';

// Types
interface MarketingCampaign {
  id: number;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  discount_percentage: number;
  discount_amount?: number;
  discount_code?: string;
  products: number[];
  product_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Mock API service (replace with actual API calls)
const marketingApi = {
  getActiveCampaigns: async (): Promise<MarketingCampaign[]> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock data
    return [
      {
        id: 1,
        name: 'Soldes d\'été',
        description: 'Remises sur les produits d\'été',
        start_date: '2023-06-01T00:00:00Z',
        end_date: '2023-08-31T23:59:59Z',
        discount_percentage: 20,
        products: [1, 2, 3, 4, 5, 6, 7, 8],
        product_count: 8,
        is_active: true,
        created_at: '2023-05-15T10:00:00Z',
        updated_at: '2023-05-15T10:00:00Z'
      },
      {
        id: 2,
        name: 'Black Friday',
        description: 'Offres spéciales Black Friday',
        start_date: '2023-11-24T00:00:00Z',
        end_date: '2023-11-27T23:59:59Z',
        discount_percentage: 30,
        discount_code: 'BLACKFRIDAY30',
        products: [5, 10, 15, 20, 25],
        product_count: 5,
        is_active: true,
        created_at: '2023-10-15T14:30:00Z',
        updated_at: '2023-10-15T14:30:00Z'
      },
      {
        id: 3,
        name: 'Nouveaux clients',
        description: 'Remise pour les nouveaux clients',
        start_date: '2023-01-01T00:00:00Z',
        end_date: '2023-12-31T23:59:59Z',
        discount_percentage: 10,
        discount_code: 'WELCOME10',
        products: [1, 2, 3],
        product_count: 3,
        is_active: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }
    ];
  },
  
  getProductCampaigns: async (productId: number): Promise<MarketingCampaign[]> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock data filtered by product ID
    const allCampaigns = await marketingApi.getActiveCampaigns();
    return allCampaigns.filter(campaign => campaign.products.includes(productId));
  },
  
  addProductToCampaign: async (campaignId: number, productId: number): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real implementation, this would update the campaign on the server
    return Promise.resolve();
  },
  
  removeProductFromCampaign: async (campaignId: number, productId: number): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real implementation, this would update the campaign on the server
    return Promise.resolve();
  }
};

interface ProductCampaignManagerProps {
  productId: number;
}

const ProductCampaignManager: React.FC<ProductCampaignManagerProps> = ({ productId }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch all active campaigns
  const { 
    data: allCampaigns = [], 
    isLoading: isLoadingCampaigns,
    isError: isCampaignsError
  } = useQuery({
    queryKey: ['marketingCampaigns'],
    queryFn: () => marketingApi.getActiveCampaigns(),
  });
  
  // Fetch campaigns for this product
  const { 
    data: productCampaigns = [], 
    isLoading: isLoadingProductCampaigns,
    isError: isProductCampaignsError
  } = useQuery({
    queryKey: ['productCampaigns', productId],
    queryFn: () => marketingApi.getProductCampaigns(productId),
  });
  
  // Add product to campaign mutation
  const addToCampaignMutation = useMutation({
    mutationFn: (campaignId: number) => 
      marketingApi.addProductToCampaign(campaignId, productId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productCampaigns', productId] });
      toast({
        title: 'Produit ajouté à la campagne',
        description: 'Le produit a été ajouté à la campagne avec succès.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de l\'ajout du produit à la campagne.',
        variant: 'destructive',
      });
    },
  });
  
  // Remove product from campaign mutation
  const removeFromCampaignMutation = useMutation({
    mutationFn: (campaignId: number) => 
      marketingApi.removeProductFromCampaign(campaignId, productId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productCampaigns', productId] });
      toast({
        title: 'Produit retiré de la campagne',
        description: 'Le produit a été retiré de la campagne avec succès.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors du retrait du produit de la campagne.',
        variant: 'destructive',
      });
    },
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };
  
  // Check if product is in campaign
  const isProductInCampaign = (campaignId: number) => {
    return productCampaigns.some(campaign => campaign.id === campaignId);
  };
  
  // Filter campaigns by search term
  const filteredCampaigns = searchTerm
    ? allCampaigns.filter(campaign => 
        campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : allCampaigns;
  
  // Toggle product in campaign
  const toggleProductInCampaign = (campaignId: number) => {
    if (isProductInCampaign(campaignId)) {
      removeFromCampaignMutation.mutate(campaignId);
    } else {
      addToCampaignMutation.mutate(campaignId);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Campagnes marketing</CardTitle>
        <CardDescription>
          Gérez les campagnes marketing associées à ce produit
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current campaigns */}
        <div>
          <h3 className="text-lg font-medium mb-4">Campagnes actives pour ce produit</h3>
          
          {isLoadingProductCampaigns ? (
            <div className="space-y-2">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          ) : isProductCampaignsError ? (
            <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
              <AlertCircle className="h-5 w-5" />
              <span>Une erreur est survenue lors du chargement des campagnes.</span>
            </div>
          ) : productCampaigns.length === 0 ? (
            <div className="text-center py-8 text-gray-500 border border-dashed rounded-md">
              <Megaphone className="h-12 w-12 mx-auto mb-2 text-gray-400" />
              <p>Ce produit n'est associé à aucune campagne marketing.</p>
              <p className="text-sm">Ajoutez-le à une campagne pour bénéficier de promotions.</p>
            </div>
          ) : (
            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Campagne</TableHead>
                    <TableHead>Période</TableHead>
                    <TableHead>Remise</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {productCampaigns.map((campaign) => (
                    <TableRow key={campaign.id}>
                      <TableCell>
                        <div className="font-medium">{campaign.name}</div>
                        <div className="text-sm text-gray-500">{campaign.description}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span>
                            {formatDate(campaign.start_date)} - {formatDate(campaign.end_date)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className="bg-green-500">
                          <Percent className="h-3 w-3 mr-1" />
                          {campaign.discount_percentage}%
                        </Badge>
                        {campaign.discount_code && (
                          <Badge variant="outline" className="ml-2">
                            <Tag className="h-3 w-3 mr-1" />
                            {campaign.discount_code}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeFromCampaignMutation.mutate(campaign.id)}
                          disabled={removeFromCampaignMutation.isPending}
                        >
                          {removeFromCampaignMutation.isPending ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <X className="h-4 w-4 mr-1" />
                          )}
                          <span>Retirer</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
        
        {/* Add to campaign */}
        <div className="pt-4 border-t">
          <h3 className="text-lg font-medium mb-4">Ajouter à une campagne</h3>
          
          <div className="relative mb-4">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Rechercher des campagnes..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {isLoadingCampaigns ? (
            <div className="space-y-2">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          ) : isCampaignsError ? (
            <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
              <AlertCircle className="h-5 w-5" />
              <span>Une erreur est survenue lors du chargement des campagnes.</span>
            </div>
          ) : filteredCampaigns.length === 0 ? (
            <div className="text-center py-8 text-gray-500 border border-dashed rounded-md">
              <p>Aucune campagne trouvée pour "{searchTerm}".</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredCampaigns.map((campaign) => (
                <div 
                  key={campaign.id}
                  className={`flex items-center justify-between p-3 border rounded-md ${
                    isProductInCampaign(campaign.id) ? 'bg-green-50 border-green-200' : 'bg-white'
                  }`}
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{campaign.name}</span>
                      {campaign.is_active ? (
                        <Badge className="bg-green-500">Actif</Badge>
                      ) : (
                        <Badge variant="outline">Inactif</Badge>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      <div>{campaign.description}</div>
                      <div className="flex items-center gap-4 mt-1">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>
                            {formatDate(campaign.start_date)} - {formatDate(campaign.end_date)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Percent className="h-3 w-3" />
                          <span>{campaign.discount_percentage}% de remise</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          <span>{campaign.product_count} produits</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant={isProductInCampaign(campaign.id) ? "outline" : "default"}
                    size="sm"
                    onClick={() => toggleProductInCampaign(campaign.id)}
                    disabled={addToCampaignMutation.isPending || removeFromCampaignMutation.isPending}
                  >
                    {addToCampaignMutation.isPending || removeFromCampaignMutation.isPending ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-1" />
                    ) : isProductInCampaign(campaign.id) ? (
                      <Check className="h-4 w-4 mr-1" />
                    ) : (
                      <Plus className="h-4 w-4 mr-1" />
                    )}
                    <span>
                      {isProductInCampaign(campaign.id) ? 'Ajouté' : 'Ajouter'}
                    </span>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="border-t pt-4">
        <Button variant="outline" className="ml-auto">
          <Megaphone className="mr-2 h-4 w-4" />
          Voir toutes les campagnes
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ProductCampaignManager;
