
import { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import { Card } from "@/components/ui/card";
import { 
  BarChart as Bar<PERSON>hartIcon, 
  TrendingUp, 
  ShoppingCart, 
  Users,
  ChevronDown
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";

const salesData = [
  { month: "Jan", revenue: 12500, orders: 145 },
  { month: "Fév", revenue: 9800, orders: 132 },
  { month: "Mar", revenue: 16200, orders: 187 },
  { month: "Avr", revenue: 10900, orders: 151 },
  { month: "Mai", revenue: 17500, orders: 205 },
  { month: "Juin", revenue: 14200, orders: 176 }
];

const categoryData = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: 28 },
  { name: "Composan<PERSON>", value: 64 },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", value: 42 },
  { name: "Smartphones", value: 35 },
  { name: "<PERSON><PERSON><PERSON>", value: 19 }
];

const trafficData = [
  { day: "Lun", visits: 520 },
  { day: "Mar", visits: 610 },
  { day: "Mer", visits: 590 },
  { day: "Jeu", visits: 780 },
  { day: "Ven", visits: 850 },
  { day: "Sam", visits: 690 },
  { day: "Dim", visits: 450 }
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const Statistics = () => {
  const [timeRange, setTimeRange] = useState("6mois");

  // Statistiques globales
  const stats = [
    {
      title: "Ventes totales",
      value: "81,100 €",
      change: "+14%",
      changeType: "positive",
      icon: <TrendingUp size={24} className="text-green-500" />,
    },
    {
      title: "Commandes",
      value: "996",
      change: "+8%",
      changeType: "positive",
      icon: <ShoppingCart size={24} className="text-blue-500" />,
    },
    {
      title: "Clients",
      value: "1,805",
      change: "+16%",
      changeType: "positive",
      icon: <Users size={24} className="text-violet-500" />,
    },
  ];

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    // Dans une app réelle, cette fonction chargerait des données différentes
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Statistiques de Vente</h1>
          <p className="text-gray-500">Analysez les performances de votre boutique</p>
        </div>

        {/* Filtres de période */}
        <div className="flex items-center justify-end gap-2">
          <Button 
            variant={timeRange === "30jours" ? "default" : "outline"} 
            size="sm"
            onClick={() => handleTimeRangeChange("30jours")}
          >
            30 jours
          </Button>
          <Button 
            variant={timeRange === "3mois" ? "default" : "outline"} 
            size="sm"
            onClick={() => handleTimeRangeChange("3mois")}
          >
            3 mois
          </Button>
          <Button 
            variant={timeRange === "6mois" ? "default" : "outline"} 
            size="sm"
            onClick={() => handleTimeRangeChange("6mois")}
          >
            6 mois
          </Button>
          <Button 
            variant={timeRange === "1an" ? "default" : "outline"} 
            size="sm"
            onClick={() => handleTimeRangeChange("1an")}
          >
            1 an
          </Button>
        </div>

        {/* Statistiques générales */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                  <h3 className="text-2xl font-bold mt-1">{stat.value}</h3>
                  <p className={`text-sm mt-1 ${
                    stat.changeType === "positive" ? "text-green-600" : "text-red-600"
                  }`}>
                    {stat.change} depuis la période précédente
                  </p>
                </div>
                <div className="bg-gray-100 p-3 rounded-full">
                  {stat.icon}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Graphique des ventes */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-medium flex items-center">
              <BarChartIcon size={18} className="mr-2 text-gray-500" /> 
              Évolution des ventes
            </h2>
            <Button variant="outline" className="flex items-center gap-1 text-sm">
              Mensuel
              <ChevronDown size={14} />
            </Button>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={salesData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                <Tooltip />
                <Legend />
                <Bar yAxisId="left" dataKey="revenue" name="Revenus (€)" fill="#8884d8" radius={[4, 4, 0, 0]} />
                <Bar yAxisId="right" dataKey="orders" name="Commandes" fill="#82ca9d" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Graphiques secondaires */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Répartition des ventes par catégorie */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium">Ventes par catégorie</h2>
            </div>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, name) => [`${value} produits`, name]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>

          {/* Trafic du site */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium">Trafic du site</h2>
            </div>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={trafficData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="visits" stroke="#8884d8" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Statistics;
