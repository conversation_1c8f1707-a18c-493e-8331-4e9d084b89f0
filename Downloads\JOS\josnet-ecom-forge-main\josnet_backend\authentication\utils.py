"""
Utility functions for the authentication app.
"""
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.contrib.auth.tokens import default_token_generator
from django.urls import reverse

def send_verification_email(user):
    """
    Send a verification email to the user.
    
    Args:
        user: The user to send the verification email to.
    """
    # Generate verification token
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)
    verification_token = f"{uid}-{token}"
    
    # Build verification URL
    verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
    
    # Prepare email content
    subject = "Vérifiez votre adresse email"
    message = f"""
    Bonjour {user.first_name or 'utilisateur'},
    
    Merci de vous être inscrit sur notre plateforme. Veuillez vérifier votre adresse email en cliquant sur le lien ci-dessous :
    
    {verification_url}
    
    Ce lien expirera dans 24 heures.
    
    Si vous n'avez pas créé de compte, vous pouvez ignorer cet email.
    
    Cordialement,
    L'équipe JosNet
    """
    
    # Send email
    send_mail(
        subject,
        message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        fail_silently=False,
    )

def send_password_reset_email(user):
    """
    Send a password reset email to the user.
    
    Args:
        user: The user to send the password reset email to.
    """
    # Generate password reset token
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)
    reset_token = f"{uid}-{token}"
    
    # Build reset URL
    reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
    
    # Prepare email content
    subject = "Réinitialisation de votre mot de passe"
    message = f"""
    Bonjour {user.first_name or 'utilisateur'},
    
    Vous avez demandé la réinitialisation de votre mot de passe. Veuillez cliquer sur le lien ci-dessous pour définir un nouveau mot de passe :
    
    {reset_url}
    
    Ce lien expirera dans 24 heures.
    
    Si vous n'avez pas demandé la réinitialisation de votre mot de passe, vous pouvez ignorer cet email.
    
    Cordialement,
    L'équipe JosNet
    """
    
    # Send email
    send_mail(
        subject,
        message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        fail_silently=False,
    )
