/**
 * Gestionnaire de session avec déconnexion automatique
 * Ce module gère la session utilisateur et implémente une déconnexion automatique
 * lorsque l'utilisateur quitte l'application sans se déconnecter manuellement.
 */

import { logout } from './auth';

// Clé pour stocker le timestamp de dernière activité
const LAST_ACTIVITY_KEY = 'lastActivityTime';

// Durée d'inactivité avant déconnexion automatique (en millisecondes)
// Par défaut: 30 minutes
const DEFAULT_SESSION_TIMEOUT = 30 * 60 * 1000;

/**
 * Initialise le gestionnaire de session
 * @param sessionTimeout Durée d'inactivité avant déconnexion (en ms)
 */
export const initSessionManager = (sessionTimeout = DEFAULT_SESSION_TIMEOUT): void => {
  // Enregistre le timestamp actuel comme dernière activité
  updateLastActivity();

  // Ajoute des écouteurs d'événements pour détecter l'activité utilisateur
  window.addEventListener('click', updateLastActivity);
  window.addEventListener('keypress', updateLastActivity);
  window.addEventListener('scroll', updateLastActivity);
  window.addEventListener('mousemove', updateLastActivity);
  
  // Ajoute un écouteur pour l'événement beforeunload (fermeture de page)
  window.addEventListener('beforeunload', () => {
    // Stocke le timestamp actuel pour vérifier à la prochaine visite
    sessionStorage.setItem('lastPageExit', Date.now().toString());
  });

  // Vérifie la session au chargement de la page
  checkSessionOnLoad(sessionTimeout);
};

/**
 * Met à jour le timestamp de dernière activité
 */
export const updateLastActivity = (): void => {
  localStorage.setItem(LAST_ACTIVITY_KEY, Date.now().toString());
};

/**
 * Vérifie si la session a expiré
 * @param sessionTimeout Durée d'inactivité avant déconnexion (en ms)
 * @returns true si la session a expiré, false sinon
 */
export const isSessionExpired = (sessionTimeout = DEFAULT_SESSION_TIMEOUT): boolean => {
  const lastActivity = localStorage.getItem(LAST_ACTIVITY_KEY);
  if (!lastActivity) return false;
  
  const now = Date.now();
  const lastActivityTime = parseInt(lastActivity, 10);
  
  return now - lastActivityTime > sessionTimeout;
};

/**
 * Vérifie la session au chargement de la page
 * @param sessionTimeout Durée d'inactivité avant déconnexion (en ms)
 */
export const checkSessionOnLoad = (sessionTimeout = DEFAULT_SESSION_TIMEOUT): void => {
  // Vérifie si l'utilisateur a quitté la page précédemment
  const lastPageExit = sessionStorage.getItem('lastPageExit');
  
  if (lastPageExit) {
    const exitTime = parseInt(lastPageExit, 10);
    const now = Date.now();
    
    // Si l'utilisateur a quitté la page depuis plus longtemps que le timeout
    if (now - exitTime > sessionTimeout) {
      console.log('Session expirée après fermeture de page - Déconnexion automatique');
      logout();
      // Nettoie le stockage de session
      sessionStorage.removeItem('lastPageExit');
    }
  }
  
  // Vérifie également si la session a expiré par inactivité
  if (isSessionExpired(sessionTimeout)) {
    console.log('Session expirée par inactivité - Déconnexion automatique');
    logout();
  }
};

/**
 * Vérifie périodiquement si la session a expiré
 * @param sessionTimeout Durée d'inactivité avant déconnexion (en ms)
 * @returns ID de l'intervalle pour pouvoir l'arrêter si nécessaire
 */
export const startSessionMonitoring = (sessionTimeout = DEFAULT_SESSION_TIMEOUT): number => {
  // Vérifie toutes les minutes si la session a expiré
  const intervalId = window.setInterval(() => {
    if (isSessionExpired(sessionTimeout)) {
      console.log('Session expirée par inactivité - Déconnexion automatique');
      logout();
      window.location.href = '/login';
    }
  }, 60 * 1000); // Vérifie chaque minute
  
  return intervalId;
};

/**
 * Arrête la surveillance de session
 * @param intervalId ID de l'intervalle retourné par startSessionMonitoring
 */
export const stopSessionMonitoring = (intervalId: number): void => {
  window.clearInterval(intervalId);
};
