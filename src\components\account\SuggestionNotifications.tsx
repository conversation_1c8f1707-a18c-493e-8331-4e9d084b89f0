import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  CheckCircle, 
  Clock, 
  XCircle,
  Star,
  Gift,
  TrendingUp,
  X,
  Eye,
  EyeOff
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { accountApi } from '@/services/accountApi';

interface Notification {
  id: number;
  type: 'suggestion_approved' | 'suggestion_rejected' | 'suggestion_implemented' | 'new_recommendation' | 'reward_earned';
  title: string;
  message: string;
  date: string;
  isRead: boolean;
  actionUrl?: string;
  metadata?: {
    productName?: string;
    rewardAmount?: number;
    category?: string;
  };
}

const SuggestionNotifications: React.FC = () => {
  const [filter, setFilter] = useState<'all' | 'unread' | 'suggestions' | 'recommendations'>('all');

  // Récupérer les notifications depuis l'API Django
  const { data: notifications = [], isLoading } = useQuery({
    queryKey: ['suggestion-notifications'],
    queryFn: async (): Promise<Notification[]> => {
      try {
        // Appel à l'API Django pour les notifications
        const data = await accountApi.getUserNotifications();
        return data;
      } catch (error) {
        console.error('Erreur lors du chargement des notifications:', error);

        // Fallback vers des données simulées en cas d'erreur
        return [
        {
          id: 1,
          type: 'suggestion_implemented',
          title: 'Suggestion implémentée !',
          message: 'Votre suggestion "Kit de démarrage réseau domestique" a été ajoutée à notre catalogue.',
          date: '2024-01-15T10:30:00Z',
          isRead: false,
          actionUrl: '/product/kit-reseau-domestique',
          metadata: {
            productName: 'Kit de démarrage réseau domestique'
          }
        },
        {
          id: 2,
          type: 'reward_earned',
          title: 'Récompense gagnée !',
          message: 'Vous avez gagné 500 points pour votre suggestion approuvée.',
          date: '2024-01-15T10:30:00Z',
          isRead: false,
          metadata: {
            rewardAmount: 500
          }
        },
        {
          id: 3,
          type: 'suggestion_approved',
          title: 'Suggestion approuvée',
          message: 'Votre suggestion "Routeur Wi-Fi 7 Enterprise" a été approuvée par notre équipe.',
          date: '2024-01-10T14:20:00Z',
          isRead: true,
          metadata: {
            productName: 'Routeur Wi-Fi 7 Enterprise'
          }
        },
        {
          id: 4,
          type: 'new_recommendation',
          title: 'Nouvelles recommandations',
          message: 'De nouveaux produits correspondant à vos préférences sont disponibles.',
          date: '2024-01-08T09:15:00Z',
          isRead: true,
          actionUrl: '/account/suggestions?tab=enhanced-recommendations',
          metadata: {
            category: 'Réseaux'
          }
        },
        {
          id: 5,
          type: 'suggestion_rejected',
          title: 'Suggestion non retenue',
          message: 'Votre suggestion "Adaptateur USB-C vers Ethernet" n\'a pas été retenue car nous proposons déjà des produits similaires.',
          date: '2024-01-05T16:45:00Z',
          isRead: true,
          metadata: {
            productName: 'Adaptateur USB-C vers Ethernet'
          }
        }
      ];
      }
    },
    staleTime: 60000, // 1 minute
  });

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'suggestion_approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'suggestion_rejected':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'suggestion_implemented':
        return <Star className="h-5 w-5 text-purple-500" />;
      case 'new_recommendation':
        return <TrendingUp className="h-5 w-5 text-blue-500" />;
      case 'reward_earned':
        return <Gift className="h-5 w-5 text-orange-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'suggestion_approved':
        return 'border-l-green-500 bg-green-50';
      case 'suggestion_rejected':
        return 'border-l-red-500 bg-red-50';
      case 'suggestion_implemented':
        return 'border-l-purple-500 bg-purple-50';
      case 'new_recommendation':
        return 'border-l-blue-500 bg-blue-50';
      case 'reward_earned':
        return 'border-l-orange-500 bg-orange-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.isRead;
      case 'suggestions':
        return ['suggestion_approved', 'suggestion_rejected', 'suggestion_implemented'].includes(notification.type);
      case 'recommendations':
        return ['new_recommendation', 'reward_earned'].includes(notification.type);
      default:
        return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const markAsRead = (id: number) => {
    // Ici, on ferait un appel API pour marquer comme lu
    console.log('Marquer comme lu:', id);
  };

  const markAllAsRead = () => {
    // Ici, on ferait un appel API pour marquer tout comme lu
    console.log('Marquer tout comme lu');
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </CardTitle>
          {unreadCount > 0 && (
            <Button variant="outline" size="sm" onClick={markAllAsRead}>
              <Eye className="h-4 w-4 mr-2" />
              Tout marquer comme lu
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {/* Filtres */}
        <div className="flex flex-wrap gap-2 mb-6">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            Toutes ({notifications.length})
          </Button>
          <Button
            variant={filter === 'unread' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('unread')}
          >
            Non lues ({unreadCount})
          </Button>
          <Button
            variant={filter === 'suggestions' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('suggestions')}
          >
            Suggestions
          </Button>
          <Button
            variant={filter === 'recommendations' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('recommendations')}
          >
            Recommandations
          </Button>
        </div>

        {/* Liste des notifications */}
        <div className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucune notification
              </h3>
              <p className="text-gray-600">
                {filter === 'unread' 
                  ? "Vous n'avez aucune notification non lue."
                  : "Aucune notification ne correspond à ce filtre."
                }
              </p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 rounded-lg border-l-4 ${getNotificationColor(notification.type)} ${
                  !notification.isRead ? 'ring-2 ring-blue-100' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900">
                          {notification.title}
                        </h4>
                        {!notification.isRead && (
                          <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                      <p className="text-sm text-gray-700 mb-2">
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>
                          {formatDistanceToNow(new Date(notification.date), {
                            addSuffix: true,
                            locale: fr
                          })}
                        </span>
                        {notification.metadata?.productName && (
                          <Badge variant="outline" className="text-xs">
                            {notification.metadata.productName}
                          </Badge>
                        )}
                        {notification.metadata?.rewardAmount && (
                          <Badge variant="outline" className="text-xs">
                            +{notification.metadata.rewardAmount} points
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    {notification.actionUrl && (
                      <Button size="sm" variant="outline">
                        Voir
                      </Button>
                    )}
                    {!notification.isRead && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => markAsRead(notification.id)}
                      >
                        <EyeOff className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SuggestionNotifications;
