from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from .models import TranslationKey, Translation, UserLanguagePreference


class TranslationSerializer(serializers.ModelSerializer):
    """Sérializer pour les traductions individuelles"""
    
    class Meta:
        model = Translation
        fields = ['language_code', 'value', 'is_approved']


class TranslationKeySerializer(serializers.ModelSerializer):
    """Sérializer pour les clés de traduction avec leurs traductions"""
    translations = TranslationSerializer(many=True, read_only=True)
    
    class Meta:
        model = TranslationKey
        fields = ['key', 'description', 'category', 'translations', 'is_active']


class TranslationKeyCreateSerializer(serializers.ModelSerializer):
    """Sérializer pour créer des clés de traduction"""
    translations = serializers.DictField(
        child=serializers.CharField(),
        write_only=True,
        required=False,
        help_text=_("Dictionnaire des traductions par langue: {'fr': 'valeur', 'en': 'value'}")
    )
    
    class Meta:
        model = TranslationKey
        fields = ['key', 'description', 'category', 'is_active', 'translations']
    
    def create(self, validated_data):
        translations_data = validated_data.pop('translations', {})
        translation_key = TranslationKey.objects.create(**validated_data)
        
        # Créer les traductions
        for language_code, value in translations_data.items():
            if language_code in [lang[0] for lang in Translation.LANGUAGE_CHOICES]:
                Translation.objects.create(
                    key=translation_key,
                    language_code=language_code,
                    value=value,
                    is_approved=True
                )
        
        return translation_key


class TranslationUpdateSerializer(serializers.ModelSerializer):
    """Sérializer pour mettre à jour une traduction"""
    
    class Meta:
        model = Translation
        fields = ['value', 'is_approved']
    
    def update(self, instance, validated_data):
        instance.value = validated_data.get('value', instance.value)
        instance.is_approved = validated_data.get('is_approved', instance.is_approved)
        instance.save()
        return instance


class UserLanguagePreferenceSerializer(serializers.ModelSerializer):
    """Sérializer pour les préférences de langue utilisateur"""
    
    class Meta:
        model = UserLanguagePreference
        fields = ['language_code']
    
    def create(self, validated_data):
        user = self.context['request'].user
        preference, created = UserLanguagePreference.objects.get_or_create(
            user=user,
            defaults=validated_data
        )
        if not created:
            preference.language_code = validated_data['language_code']
            preference.save()
        return preference


class BulkTranslationSerializer(serializers.Serializer):
    """Sérializer pour l'import en masse de traductions"""
    translations = serializers.ListField(
        child=serializers.DictField(),
        help_text=_("Liste des traductions à importer")
    )
    
    def validate_translations(self, value):
        """Valider la structure des traductions"""
        required_fields = ['key', 'translations']
        
        for item in value:
            for field in required_fields:
                if field not in item:
                    raise serializers.ValidationError(
                        f"Le champ '{field}' est requis pour chaque traduction"
                    )
            
            if not isinstance(item['translations'], dict):
                raise serializers.ValidationError(
                    "Le champ 'translations' doit être un dictionnaire"
                )
        
        return value


class LanguageListSerializer(serializers.Serializer):
    """Sérializer pour la liste des langues disponibles"""
    code = serializers.CharField()
    name = serializers.CharField()
    is_default = serializers.BooleanField()


class TranslationExportSerializer(serializers.Serializer):
    """Sérializer pour l'export de traductions"""
    language_code = serializers.CharField(
        required=False,
        help_text=_("Code de langue à exporter (vide pour toutes)")
    )
    format = serializers.ChoiceField(
        choices=['json', 'csv'],
        default='json',
        help_text=_("Format d'export")
    )
    category = serializers.CharField(
        required=False,
        help_text=_("Catégorie à exporter (vide pour toutes)")
    )
    approved_only = serializers.BooleanField(
        default=True,
        help_text=_("Exporter seulement les traductions approuvées")
    )
