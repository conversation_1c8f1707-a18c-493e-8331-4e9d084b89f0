import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ChatInterface from '@/components/messaging/ChatInterface';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  MessageSquare,
  User,
  Calendar,
  Tag,
  AlertCircle,
  Loader2
} from 'lucide-react';
import messagingApi from '@/services/messagingApi';

const ConversationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();

  const conversationId = id ? parseInt(id, 10) : null;

  // Fetch conversation details
  const { 
    data: conversation, 
    isLoading, 
    error,
    refetch 
  } = useQuery({
    queryKey: ['conversation', conversationId],
    queryFn: () => messagingApi.getConversation(conversationId!),
    enabled: !!conversationId,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });

  // Redirect to appropriate messages page based on user role
  const handleBackToMessages = () => {
    if (user?.role === 'admin' || user?.role === 'staff') {
      navigate('/admin/messages');
    } else {
      navigate('/account/messages');
    }
  };

  // Auto-refresh conversation every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 30000);

    return () => clearInterval(interval);
  }, [refetch]);

  if (!conversationId) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-1 bg-gray-50 flex items-center justify-center">
          <Alert variant="destructive" className="max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              ID de conversation invalide.
            </AlertDescription>
          </Alert>
        </main>
        <Footer />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-1 bg-gray-50 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Chargement de la conversation...</span>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !conversation) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-1 bg-gray-50 flex items-center justify-center">
          <div className="text-center space-y-4">
            <Alert variant="destructive" className="max-w-md">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error ? 'Erreur lors du chargement de la conversation.' : 'Conversation non trouvée.'}
              </AlertDescription>
            </Alert>
            <Button onClick={handleBackToMessages} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour aux messages
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-6">
          {/* Header */}
          <div className="mb-6">
            <Button 
              onClick={handleBackToMessages}
              variant="ghost" 
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour aux messages
            </Button>
            
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <MessageSquare className="h-6 w-6 mr-2" />
                  {conversation.subject}
                </h1>
                <p className="text-gray-600 mt-1">
                  Conversation #{conversation.id}
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  conversation.status === 'new' ? 'bg-blue-100 text-blue-800' :
                  conversation.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                  conversation.status === 'resolved' ? 'bg-green-100 text-green-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {conversation.status === 'new' ? 'Nouveau' :
                   conversation.status === 'in_progress' ? 'En cours' :
                   conversation.status === 'resolved' ? 'Résolu' :
                   conversation.status === 'closed' ? 'Fermé' : conversation.status}
                </span>
                
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  conversation.priority === 'low' ? 'bg-green-100 text-green-800' :
                  conversation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  conversation.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {conversation.priority === 'low' ? 'Faible' :
                   conversation.priority === 'medium' ? 'Moyenne' :
                   conversation.priority === 'high' ? 'Élevée' :
                   'Urgente'}
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Conversation Details Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Détails</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Customer Info */}
                  <div>
                    <h4 className="font-medium text-gray-900 flex items-center mb-2">
                      <User className="h-4 w-4 mr-2" />
                      Client
                    </h4>
                    <div className="text-sm text-gray-600">
                      <div className="font-medium">{conversation.customer.full_name}</div>
                      <div>{conversation.customer.email}</div>
                    </div>
                  </div>

                  {/* Date Info */}
                  <div>
                    <h4 className="font-medium text-gray-900 flex items-center mb-2">
                      <Calendar className="h-4 w-4 mr-2" />
                      Dates
                    </h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>
                        <span className="font-medium">Créée:</span> {' '}
                        {new Date(conversation.created_at).toLocaleDateString('fr-FR')}
                      </div>
                      {conversation.last_message_at && (
                        <div>
                          <span className="font-medium">Dernier message:</span> {' '}
                          {new Date(conversation.last_message_at).toLocaleDateString('fr-FR')}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Category */}
                  {conversation.category && (
                    <div>
                      <h4 className="font-medium text-gray-900 flex items-center mb-2">
                        <Tag className="h-4 w-4 mr-2" />
                        Catégorie
                      </h4>
                      <div className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-2" 
                          style={{ backgroundColor: conversation.category.color }}
                        ></div>
                        <span className="text-sm text-gray-600">
                          {conversation.category.name}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Tags */}
                  {conversation.tags && conversation.tags.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Étiquettes</h4>
                      <div className="flex flex-wrap gap-1">
                        {conversation.tags.map((tag) => (
                          <span
                            key={tag.id}
                            className="px-2 py-1 text-xs rounded-full"
                            style={{
                              backgroundColor: tag.color + '20',
                              color: tag.color,
                              border: `1px solid ${tag.color}40`
                            }}
                          >
                            {tag.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Order Reference */}
                  {conversation.order && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Commande</h4>
                      <div className="text-sm text-gray-600">
                        <div className="font-medium">#{conversation.order.order_number}</div>
                        <div>{conversation.order.status}</div>
                        <div>{conversation.order.total} FBU</div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Chat Interface */}
            <div className="lg:col-span-3">
              <Card className="h-[calc(100vh-200px)]">
                <ChatInterface
                  conversationId={conversation.id}
                  conversation={conversation}
                  className="h-full"
                />
              </Card>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ConversationDetail;
