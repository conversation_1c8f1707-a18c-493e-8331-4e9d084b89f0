# Generated by Django 5.2.1 on 2025-06-05 22:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0003_relatedproduct'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductSuggestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Nom du produit suggéré')),
                ('category', models.CharField(max_length=100, verbose_name='Catégorie')),
                ('description', models.TextField(verbose_name='Description détaillée')),
                ('estimated_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Prix estimé (BIF)')),
                ('reason', models.TextField(verbose_name='<PERSON><PERSON> de la suggestion')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('approved', 'Approuvé'), ('rejected', 'Rejeté'), ('implemented', 'Implémenté')], default='pending', max_length=20, verbose_name='Statut')),
                ('priority', models.CharField(choices=[('low', 'Basse'), ('medium', 'Moyenne'), ('high', 'Haute'), ('urgent', 'Urgente')], default='medium', max_length=20, verbose_name='Priorité')),
                ('admin_response', models.TextField(blank=True, null=True, verbose_name="Réponse de l'administrateur")),
                ('response_date', models.DateTimeField(blank=True, null=True, verbose_name='Date de réponse')),
                ('votes_count', models.PositiveIntegerField(default=0, verbose_name='Nombre de votes')),
                ('implementation_notes', models.TextField(blank=True, null=True, verbose_name="Notes d'implémentation")),
                ('estimated_implementation_time', models.PositiveIntegerField(blank=True, help_text='Temps estimé en jours', null=True, verbose_name="Temps d'implémentation estimé (jours)")),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_suggestions', to=settings.AUTH_USER_MODEL, verbose_name='Examiné par')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_suggestions', to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
            ],
            options={
                'verbose_name': 'Suggestion de produit',
                'verbose_name_plural': 'Suggestions de produits',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductSuggestionAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='suggestions/attachments/%Y/%m/', verbose_name='Fichier')),
                ('filename', models.CharField(max_length=255, verbose_name='Nom du fichier')),
                ('file_size', models.PositiveIntegerField(verbose_name='Taille du fichier (bytes)')),
                ('content_type', models.CharField(max_length=100, verbose_name='Type de contenu')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de téléchargement')),
                ('suggestion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='products.productsuggestion', verbose_name='Suggestion')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Téléchargé par')),
            ],
            options={
                'verbose_name': 'Pièce jointe de suggestion',
                'verbose_name_plural': 'Pièces jointes de suggestions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductSuggestionComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='Contenu du commentaire')),
                ('is_admin_comment', models.BooleanField(default=False, verbose_name='Commentaire administrateur')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('suggestion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='products.productsuggestion', verbose_name='Suggestion')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suggestion_comments', to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
            ],
            options={
                'verbose_name': 'Commentaire de suggestion',
                'verbose_name_plural': 'Commentaires de suggestions',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductSuggestionVote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de vote')),
                ('suggestion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='products.productsuggestion', verbose_name='Suggestion')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suggestion_votes', to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
            ],
            options={
                'verbose_name': 'Vote de suggestion',
                'verbose_name_plural': 'Votes de suggestions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='productsuggestion',
            index=models.Index(fields=['status', 'created_at'], name='products_pr_status_1ed61d_idx'),
        ),
        migrations.AddIndex(
            model_name='productsuggestion',
            index=models.Index(fields=['user', 'status'], name='products_pr_user_id_e6f2f3_idx'),
        ),
        migrations.AddIndex(
            model_name='productsuggestion',
            index=models.Index(fields=['category', 'status'], name='products_pr_categor_9092fd_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='productsuggestionvote',
            unique_together={('suggestion', 'user')},
        ),
    ]
