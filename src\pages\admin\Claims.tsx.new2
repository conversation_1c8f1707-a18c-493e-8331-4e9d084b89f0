  // Fetch claims data
  useEffect(() => {
    const fetchClaims = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/claims/', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
          }
        });

        if (!response.ok) {
          throw new Error(`Erreur ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        setClaims(data);
        setFilteredClaims(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
        console.error('Error fetching claims:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchClaims();
  }, []);

  // Filter claims based on search term, status filter and subject filter
  useEffect(() => {
    let result = [...claims];
    
    if (statusFilter !== 'all') {
      result = result.filter(item => item.status === statusFilter);
    }
    
    if (subjectFilter !== 'all') {
      result = result.filter(item => item.subject === subjectFilter);
    }
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(item => 
        item.claim_number.toLowerCase().includes(term) ||
        item.user.email.toLowerCase().includes(term) ||
        item.user.full_name.toLowerCase().includes(term) ||
        (item.order?.order_number.toLowerCase().includes(term) || false)
      );
    }

    setFilteredClaims(result);
  }, [claims, searchTerm, statusFilter, subjectFilter]);

  return (
    <AdminLayout>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5">Gestion des réclamations</Typography>
        </Box>
        
        <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="status-filter-label">Statut</InputLabel>
            <Select
              labelId="status-filter-label"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              label="Statut"
            >
              <MenuItem value="all">Tous les statuts</MenuItem>
              <MenuItem value="new">Nouveau</MenuItem>
              <MenuItem value="in_progress">En cours</MenuItem>
              <MenuItem value="waiting_customer">En attente du client</MenuItem>
              <MenuItem value="waiting_staff">En attente du support</MenuItem>
              <MenuItem value="resolved">Résolu</MenuItem>
              <MenuItem value="closed">Fermé</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="subject-filter-label">Sujet</InputLabel>
            <Select
              labelId="subject-filter-label"
              value={subjectFilter}
              onChange={(e) => setSubjectFilter(e.target.value)}
              label="Sujet"
            >
              <MenuItem value="all">Tous les sujets</MenuItem>
              <MenuItem value="order_issue">Problème avec une commande</MenuItem>
              <MenuItem value="product_issue">Problème avec un produit</MenuItem>
              <MenuItem value="shipping_issue">Problème de livraison</MenuItem>
              <MenuItem value="account_issue">Problème de compte</MenuItem>
              <MenuItem value="website_issue">Problème avec le site web</MenuItem>
              <MenuItem value="other">Autre</MenuItem>
            </Select>
          </FormControl>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <TextField
              size="small"
              variant="outlined"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Box>
        </Box>
        {/* Error message */}
        {error && (
          <Box sx={{ mb: 2 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}

        {/* Claims Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ mb: 4 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Numéro</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Sujet</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Date de création</TableCell>
                  <TableCell>Dernière mise à jour</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredClaims.length > 0 ? (
                  filteredClaims.map((claim) => (
                    <TableRow key={claim.id}>
                      <TableCell>{claim.claim_number}</TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">{claim.user.full_name}</Typography>
                          <Typography variant="caption" color="text.secondary">{claim.user.email}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {subjectLabels[claim.subject as keyof typeof subjectLabels] || claim.subject}
                        {claim.order && (
                          <Typography variant="caption" display="block" color="text.secondary">
                            Commande: {claim.order.order_number}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={statusLabels[claim.status as keyof typeof statusLabels]}
                          sx={{ 
                            backgroundColor: statusColors[claim.status as keyof typeof statusColors],
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatDate(claim.created_at)}</TableCell>
                      <TableCell>
                        {formatDate(claim.last_update)}
                        <Typography variant="caption" display="block" color="text.secondary">
                          {formatTime(claim.last_update)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Voir les détails">
                          <IconButton size="small" onClick={() => handleViewDetails(claim)}>
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      Aucune réclamation trouvée
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
