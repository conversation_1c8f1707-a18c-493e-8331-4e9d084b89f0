from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import PaymentMethod, Transaction, Invoice, InvoiceItem, CustomerPaymentMethod

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'provider', 'is_active', 'is_default', 'fee_fixed', 'fee_percentage', 'position')
    list_filter = ('is_active', 'is_default', 'provider')
    search_fields = ('name', 'description')
    ordering = ('position', 'name')
    fieldsets = (
        (None, {
            'fields': ('name', 'provider', 'description', 'icon')
        }),
        ('Statut', {
            'fields': ('is_active', 'is_default', 'position')
        }),
        ('Frais', {
            'fields': ('fee_fixed', 'fee_percentage')
        }),
        ('Configuration', {
            'fields': ('config',),
            'classes': ('collapse',)
        }),
    )

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('id', 'order_link', 'client_info', 'payment_method', 'amount', 'currency', 'status', 'created_at', 'completed_at')
    list_filter = ('status', 'payment_method', 'currency', 'created_at', 'order__user')
    search_fields = ('transaction_id', 'order__order_number', 'order__user__email', 'order__user__first_name', 'order__user__last_name')
    readonly_fields = ('created_at', 'updated_at', 'completed_at', 'client_info')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    
    def order_link(self, obj):
        url = reverse('admin:orders_order_change', args=[obj.order.id])
        return format_html('<a href="{}">{}</a>', url, obj.order.order_number if hasattr(obj.order, 'order_number') else f'Commande #{obj.order.id}')
    order_link.short_description = 'Commande'
    
    def client_info(self, obj):
        if not obj.order or not obj.order.user:
            return "-"
        user = obj.order.user
        url = reverse('admin:authentication_user_change', args=[user.id])
        return format_html('<a href="{}">{} {} ({})</a>', url, user.first_name, user.last_name, user.email)
    client_info.short_description = 'Client'
    
    fieldsets = (
        (None, {
            'fields': ('order', 'payment_method', 'transaction_id')
        }),
        ('Détails du paiement', {
            'fields': ('amount', 'currency', 'status')
        }),
        ('Dates', {
            'fields': ('created_at', 'updated_at', 'completed_at')
        }),
        ('Informations supplémentaires', {
            'fields': ('provider_response', 'error_message'),
            'classes': ('collapse',)
        }),
    )

class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 0
    readonly_fields = ('created_at', 'updated_at')
    fields = ('product_name', 'variant_name', 'sku', 'price', 'quantity', 'subtotal', 'discount_amount', 'final_price')
    can_delete = False
    max_num = 0
    verbose_name = "Élément de facture"
    verbose_name_plural = "Éléments de facture"

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    inlines = [InvoiceItemInline]
    list_display = ('invoice_number', 'order_link', 'client_info', 'status', 'issue_date', 'due_date', 'total', 'pdf_link')
    list_filter = ('status', 'issue_date', 'due_date', 'paid_date', 'order__user')
    search_fields = ('invoice_number', 'order__order_number', 'order__user__email', 'order__user__first_name', 'order__user__last_name')
    readonly_fields = ('created_at', 'updated_at', 'invoice_number', 'client_info')
    ordering = ('-issue_date', '-id')
    date_hierarchy = 'issue_date'
    
    def order_link(self, obj):
        url = reverse('admin:orders_order_change', args=[obj.order.id])
        return format_html('<a href="{}">{}</a>', url, obj.order.order_number if hasattr(obj.order, 'order_number') else f'Commande #{obj.order.id}')
    order_link.short_description = 'Commande'
    
    def client_info(self, obj):
        if not obj.order or not obj.order.user:
            return "-"
        user = obj.order.user
        url = reverse('admin:authentication_user_change', args=[user.id])
        return format_html('<a href="{}">{} {} ({})</a>', url, user.first_name, user.last_name, user.email)
    client_info.short_description = 'Client'
    
    def pdf_link(self, obj):
        if obj.pdf_file:
            return format_html('<a href="{}" target="_blank">Télécharger PDF</a>', obj.pdf_file.url)
        return "-"
    pdf_link.short_description = 'Facture PDF'
    
    fieldsets = (
        (None, {
            'fields': ('order', 'invoice_number', 'status')
        }),
        ('Dates', {
            'fields': ('due_date', 'paid_date')
        }),
        ('Montants', {
            'fields': ('subtotal', 'tax_amount', 'shipping_amount', 'discount_amount', 'total')
        }),
        ('Document', {
            'fields': ('pdf_file', 'notes')
        }),
        ('Métadonnées', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'invoice_link', 'product_name', 'variant_name', 'sku', 'price', 'quantity', 'subtotal', 'discount_amount', 'final_price')
    list_filter = ('invoice__status', 'created_at')
    search_fields = ('product_name', 'sku', 'invoice__invoice_number')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)
    
    def invoice_link(self, obj):
        url = reverse('admin:payments_invoice_change', args=[obj.invoice.id])
        return format_html('<a href="{}">{}</a>', url, obj.invoice.invoice_number)
    invoice_link.short_description = 'Facture'

@admin.register(CustomerPaymentMethod)
class CustomerPaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'payment_method', 'is_default', 'created_at')
    list_filter = ('is_default', 'payment_method', 'created_at')
    search_fields = ('user__email', 'user__username', 'token_id')
    ordering = ('-created_at',)
