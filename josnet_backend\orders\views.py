from django.db.models import Q, Count, Sum
from django.utils import timezone
from django.db import transaction
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from .models import Order, OrderItem, OrderStatusHistory, OrderRefund, ShippingMethod
from .serializers import (
    OrderListSerializer, OrderDetailSerializer, OrderCreateSerializer, OrderUpdateSerializer,
    OrderItemSerializer, OrderStatusHistorySerializer, OrderRefundSerializer, ShippingMethodSerializer
)
from .signals import check_stock_availability
from authentication.permissions import IsStaffOrAdmin, IsOwnerOrStaff
from products.models import Product, ProductVariant

class OrderViewSet(viewsets.ModelViewSet):
    """
    API endpoint for orders.
    """
    queryset = Order.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'payment_status', 'payment_method']
    search_fields = ['order_number', 'email', 'billing_first_name', 'billing_last_name', 'shipping_first_name', 'shipping_last_name']
    ordering_fields = ['created_at', 'updated_at', 'total']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return OrderCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return OrderUpdateSerializer
        elif self.action == 'list':
            return OrderListSerializer
        return OrderDetailSerializer

    def get_permissions(self):
        if self.action in ['create', 'my_orders']:
            permission_classes = [IsAuthenticated]
        elif self.action in ['list', 'retrieve', 'update', 'partial_update', 'destroy', 'cancel']:
            permission_classes = [IsOwnerOrStaff]
        else:
            permission_classes = [IsStaffOrAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # Regular users can only see their own orders
        if not user.is_staff and not user.is_superuser:
            queryset = queryset.filter(user=user)

        return queryset

    def perform_create(self, serializer):
        # Vérifier la disponibilité du stock avant de créer la commande
        order_data = serializer.validated_data
        items_data = order_data.get('items', [])

        # Préparer les données pour la vérification de stock
        stock_check_items = []
        for item_data in items_data:
            stock_check_items.append({
                'product': item_data.get('product'),
                'variant': item_data.get('variant'),
                'quantity': item_data.get('quantity', 0)
            })

        # Vérifier la disponibilité du stock
        stock_check = check_stock_availability(stock_check_items)

        if not stock_check['available']:
            # Stock insuffisant - retourner une erreur détaillée
            error_details = []
            for issue in stock_check['issues']:
                error_details.append(
                    f"{issue['item']}: demandé {issue['requested']}, disponible {issue['available']}"
                )

            from rest_framework.exceptions import ValidationError
            raise ValidationError({
                'stock_error': 'Stock insuffisant pour certains articles',
                'details': error_details
            })

        # Stock disponible - créer la commande
        with transaction.atomic():
            # Créer la commande
            if self.request.user.is_authenticated:
                order = serializer.save(user=self.request.user)
            else:
                order = serializer.save()
            
            # Créer la facture et la transaction dans un bloc séparé après la création de la commande
            try:
                self.create_invoice_and_transaction(order)
            except Exception as e:
                # Log l'erreur mais ne bloque pas la création de la commande
                print(f"Erreur lors de la création de la facture/transaction: {str(e)}")
                # Continuer l'exécution sans bloquer

    def create_invoice_and_transaction(self, order):
        """
        Crée une facture et une transaction pour une commande donnée.
        Cette méthode est appelée après la création réussie d'une commande.
        Met également à jour les données de ventes pour le tableau de bord d'administration.
        """
        from payments.models import Invoice, Transaction, PaymentMethod
        import traceback
        
        print(f"Début de création de facture/transaction pour la commande #{order.order_number}")
        
        # Créer automatiquement une facture pour cette commande
        try:
            # Utiliser le service de création de facture qui inclut les InvoiceItem
            from payments.services.invoice_service import create_invoice_for_order
            invoice = create_invoice_for_order(order)
            print(f"Facture #{invoice.invoice_number} créée avec succès pour la commande #{order.order_number}")
        except Exception as e:
            print(f"ERREUR lors de la création de la facture: {str(e)}")
            traceback.print_exc()
            # Continuer malgré l'erreur
            return
        
        # Si un paiement a été effectué, créer une transaction
        transaction = None
        if hasattr(order, 'payment_method') and order.payment_method and getattr(order, 'payment_status', '') in ['paid', 'partially_paid']:
            try:
                # Récupérer ou créer la méthode de paiement
                payment_method, created = PaymentMethod.objects.get_or_create(
                    provider=order.payment_method,
                    defaults={
                        'name': str(order.payment_method).capitalize(),
                        'is_active': True
                    }
                )
                print(f"Méthode de paiement {'créée' if created else 'récupérée'}: {payment_method.provider}")
                
                # Créer la transaction
                transaction = Transaction.objects.create(
                    order=order,
                    payment_method=payment_method,
                    amount=order.total,
                    currency=getattr(order, 'currency', 'EUR'),
                    status='completed' if order.payment_status == 'paid' else 'pending',
                    transaction_id=f"TRX-{order.order_number}",
                    completed_at=timezone.now() if order.payment_status == 'paid' else None
                )
                print(f"Transaction #{transaction.id} créée avec succès pour la commande #{order.order_number}")
                
                # Si le paiement est complet, mettre à jour la facture
                if order.payment_status == 'paid':
                    invoice.status = 'paid'
                    invoice.paid_date = timezone.now()
                    invoice.save()
                    print(f"Facture #{invoice.id} marquée comme payée")
            except Exception as e:
                print(f"ERREUR lors de la création de la transaction: {str(e)}")
                traceback.print_exc()
                # Ne pas propager l'erreur pour ne pas bloquer le processus
        
        # Créer des entrées dans SalesData pour chaque article de la commande
        try:
            from analytics.models import SalesData
            from products.models import Product
            
            # Récupérer les articles de la commande
            order_items = order.items.all()
            print(f"Création de données de ventes pour {order_items.count()} articles de la commande #{order.order_number}")
            
            sales_data_created = 0
            for item in order_items:
                # Vérifier que le produit existe
                if not hasattr(item, 'product') or not item.product:
                    print(f"Produit manquant pour l'article {item.id} de la commande {order.id}")
                    continue
                
                # Vérifier que l'utilisateur existe
                if not hasattr(order, 'user') or not order.user:
                    print(f"Utilisateur manquant pour la commande {order.id}")
                    continue
                
                try:
                    # Créer une entrée SalesData pour cet article
                    sales_data = SalesData.objects.create(
                        date=timezone.now().date(),
                        order_id=str(order.id),  # Convertir en chaîne car order_id est CharField
                        product=item.product,     # Utiliser l'objet produit directement
                        variant=item.variant if hasattr(item, 'variant') else None,
                        customer_id=order.user.id,
                        quantity=item.quantity,
                        unit_price=item.price,
                        total_price=item.subtotal
                    )
                    sales_data_created += 1
                    print(f"Entrée SalesData #{sales_data.id} créée pour le produit {item.product.name} (commande #{order.order_number})")
                except Exception as item_e:
                    print(f"ERREUR lors de la création de SalesData pour l'article {item.id}: {str(item_e)}")
                    traceback.print_exc()
                    continue
            
            print(f"{sales_data_created} entrées SalesData créées avec succès pour la commande #{order.order_number}")
            
            # Vérifier que les données ont bien été créées
            from django.db.models import Sum
            total_sales = SalesData.objects.filter(order_id=str(order.id)).aggregate(total=Sum('total_price'))['total'] or 0
            print(f"Vérification: Total des ventes pour la commande #{order.order_number} = {total_sales}")
            
        except Exception as e:
            print(f"ERREUR lors de la création des données de ventes: {str(e)}")
            traceback.print_exc()
            # Ne pas propager l'erreur pour ne pas bloquer le processus
    
    @action(detail=True, methods=['post'])
    def add_status(self, request, pk=None):
        """
        Add a new status to the order.

        Example request:
        {
            "status": "processing",
            "notes": "Order is being processed",
            "update_payment_status": true
        }
        """
        order = self.get_object()
        status_value = request.data.get('status')
        notes = request.data.get('notes', '')
        update_payment_status = request.data.get('update_payment_status', False)

        if not status_value:
            return Response({"error": "Status is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Validate status value
        valid_statuses = dict(Order.STATUS_CHOICES).keys()
        if status_value not in valid_statuses:
            return Response(
                {"error": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create status history
        status_history = OrderStatusHistory.objects.create(
            order=order,
            status=status_value,
            notes=notes,
            created_by=request.user
        )

        # Update order status
        old_status = order.status
        order.status = status_value

        # Update timestamp fields based on status
        if status_value == 'paid' and not order.paid_at:
            order.paid_at = timezone.now()
        elif status_value == 'shipped' and not order.shipped_at:
            order.shipped_at = timezone.now()
        elif status_value == 'delivered' and not order.delivered_at:
            order.delivered_at = timezone.now()
        elif status_value == 'cancelled' and not order.cancelled_at:
            order.cancelled_at = timezone.now()
            
        # Update payment status based on order status if requested
        if update_payment_status:
            if status_value == 'paid' or status_value == 'delivered':
                order.payment_status = 'paid'
            elif status_value == 'processing' or status_value == 'confirmed':
                order.payment_status = 'processing'
            elif status_value == 'cancelled':
                order.payment_status = 'cancelled'
            elif status_value == 'refunded':
                order.payment_status = 'refunded'
            elif status_value == 'partially_refunded':
                order.payment_status = 'partially_refunded'

        order.save()

        serializer = OrderStatusHistorySerializer(status_history)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an order.

        Example request:
        {
            "reason": "Customer requested cancellation"
        }
        """
        order = self.get_object()
        reason = request.data.get('reason', 'No reason provided')

        # Check if order can be cancelled
        if order.status in ['delivered', 'refunded', 'cancelled']:
            return Response(
                {"error": f"Cannot cancel order with status '{order.get_status_display()}'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create status history
        status_history = OrderStatusHistory.objects.create(
            order=order,
            status='cancelled',
            notes=f"Order cancelled. Reason: {reason}",
            created_by=request.user
        )

        # Update order
        order.status = 'cancelled'
        order.cancelled_at = timezone.now()
        order.save()

        serializer = self.get_serializer(order)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """Get all items for an order."""
        order = self.get_object()
        items = order.items.all()
        serializer = OrderItemSerializer(items, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def status_history(self, request, pk=None):
        """Get status history for an order."""
        order = self.get_object()
        history = order.status_history.all()
        serializer = OrderStatusHistorySerializer(history, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def refunds(self, request, pk=None):
        """Get refunds for an order."""
        order = self.get_object()
        refunds = order.refunds.all()
        serializer = OrderRefundSerializer(refunds, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def my_orders(self, request):
        """Get orders for the current user."""
        if not request.user.is_authenticated:
            return Response({"error": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

        orders = Order.objects.filter(user=request.user)
        page = self.paginate_queryset(orders)

        if page is not None:
            serializer = OrderListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = OrderListSerializer(orders, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get order statistics for the dashboard."""
        if not request.user.is_staff and not request.user.is_superuser:
            return Response({"error": "Staff privileges required"}, status=status.HTTP_403_FORBIDDEN)

        # Get counts by status
        status_counts = Order.objects.values('status').annotate(count=Count('id'))
        status_data = {item['status']: item['count'] for item in status_counts}

        # Get total revenue
        total_revenue = Order.objects.filter(status__in=['processing', 'shipped', 'delivered']).aggregate(total=Sum('total'))['total'] or 0

        # Get recent orders
        recent_orders = Order.objects.order_by('-created_at')[:5]
        recent_orders_data = OrderListSerializer(recent_orders, many=True).data

        # Get counts by payment method
        payment_method_counts = Order.objects.values('payment_method').annotate(count=Count('id'))
        payment_method_data = {item['payment_method']: item['count'] for item in payment_method_counts}

        return Response({
            'status_counts': status_data,
            'total_revenue': total_revenue,
            'recent_orders': recent_orders_data,
            'payment_method_counts': payment_method_data,
            'total_orders': Order.objects.count()
        })

    @action(detail=False, methods=['post'])
    def check_stock(self, request):
        """
        Vérifier la disponibilité du stock pour une liste d'articles.

        Example request:
        {
            "items": [
                {"product_id": 1, "quantity": 2},
                {"variant_id": 3, "quantity": 1}
            ]
        }
        """
        items_data = request.data.get('items', [])

        if not items_data:
            return Response(
                {"error": "Items list is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Préparer les données pour la vérification de stock
        stock_check_items = []
        for item_data in items_data:
            product = None
            variant = None

            if 'product_id' in item_data:
                try:
                    product = Product.objects.get(id=item_data['product_id'])
                except Product.DoesNotExist:
                    return Response(
                        {"error": f"Product with ID {item_data['product_id']} not found"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            if 'variant_id' in item_data:
                try:
                    variant = ProductVariant.objects.get(id=item_data['variant_id'])
                    if not product:
                        product = variant.product
                except ProductVariant.DoesNotExist:
                    return Response(
                        {"error": f"Variant with ID {item_data['variant_id']} not found"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            if not product and not variant:
                return Response(
                    {"error": "Either product_id or variant_id is required for each item"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            stock_check_items.append({
                'product': product,
                'variant': variant,
                'quantity': item_data.get('quantity', 0)
            })

        # Vérifier la disponibilité du stock
        stock_check = check_stock_availability(stock_check_items)

        return Response({
            'available': stock_check['available'],
            'issues': stock_check['issues']
        })

class OrderItemViewSet(viewsets.ModelViewSet):
    """
    API endpoint for order items.
    """
    queryset = OrderItem.objects.all()
    serializer_class = OrderItemSerializer
    permission_classes = [IsStaffOrAdmin]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['order', 'product', 'variant']

class OrderRefundViewSet(viewsets.ModelViewSet):
    """
    API endpoint for order refunds.
    """
    queryset = OrderRefund.objects.all()
    serializer_class = OrderRefundSerializer
    permission_classes = [IsStaffOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['order', 'status', 'refund_type']
    ordering_fields = ['created_at', 'amount']
    ordering = ['-created_at']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """
        Process a refund.

        Example request:
        {
            "status": "approved",
            "transaction_id": "REF123456",
            "notes": "Refund processed through payment gateway"
        }
        """
        refund = self.get_object()
        status_value = request.data.get('status')
        transaction_id = request.data.get('transaction_id')
        notes = request.data.get('notes')

        if not status_value:
            return Response({"error": "Status is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Validate status value
        valid_statuses = dict(OrderRefund.REFUND_STATUS_CHOICES).keys()
        if status_value not in valid_statuses:
            return Response(
                {"error": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update refund
        refund.status = status_value
        refund.transaction_id = transaction_id

        if notes:
            refund.notes = notes if not refund.notes else f"{refund.notes}\n\n{notes}"

        if status_value in ['approved', 'completed']:
            refund.processed_by = request.user
            refund.processed_at = timezone.now()

            # Update order status if refund is completed
            if status_value == 'completed':
                order = refund.order

                # Check if this is a full refund
                if refund.refund_type == 'full':
                    order.status = 'refunded'
                    order.payment_status = 'refunded'
                else:
                    order.status = 'partially_refunded'
                    order.payment_status = 'partially_refunded'

                order.save()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    status=order.status,
                    notes=f"Order {order.get_status_display()} due to refund #{refund.refund_number}",
                    created_by=request.user
                )

        refund.save()

        serializer = self.get_serializer(refund)
        return Response(serializer.data)

class ShippingMethodViewSet(viewsets.ModelViewSet):
    """
    API endpoint for shipping methods.
    """
    queryset = ShippingMethod.objects.all()
    serializer_class = ShippingMethodSerializer
    permission_classes = [IsStaffOrAdmin]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active']

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active shipping methods."""
        shipping_methods = ShippingMethod.objects.filter(is_active=True)
        serializer = self.get_serializer(shipping_methods, many=True)
        return Response(serializer.data)
