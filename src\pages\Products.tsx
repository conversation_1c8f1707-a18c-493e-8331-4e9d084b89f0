
import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import ProductCard from "@/components/products/ProductCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Search, Filter, SlidersHorizontal, Loader2 } from "lucide-react";
import { Pagination, PaginationContent, PaginationItem, PaginationLink } from "@/components/ui/pagination";
import { formatCurrency } from '@/utils/currency';
import { productApi, ProductListItem, Category, ProductFilterParams } from "@/services/productApi";

const Products = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [activeCategory, setActiveCategory] = useState<string>("Tous");
  const [searchTerm, setSearchTerm] = useState("");
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [inStockOnly, setInStockOnly] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [maxPrice, setMaxPrice] = useState(1000);

  // Récupérer les catégories depuis l'API
  const {
    data: categoriesData = [],
    isLoading: isLoadingCategories,
    isError: isCategoriesError
  } = useQuery({
    queryKey: ['categories'],
    queryFn: productApi.getCategories,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Ajouter "Tous" aux catégories
  const categories = ["Tous", ...categoriesData.map(cat => cat.name)];

  // Construire les filtres pour l'API
  const buildFilters = (): ProductFilterParams => {
    const filters: ProductFilterParams = {
      page: currentPage,
      page_size: pageSize,
      min_price: priceRange[0],
      max_price: priceRange[1],
    };

    if (searchTerm) {
      filters.name = searchTerm;
    }

    if (inStockOnly) {
      filters.in_stock = true;
    }

    if (activeCategory !== "Tous") {
      const categoryId = categoriesData.find(cat => cat.name === activeCategory)?.id;
      if (categoryId) {
        filters.category = [categoryId.toString()];
      }
    }

    return filters;
  };

  // Récupérer les produits depuis l'API avec les filtres
  const {
    data: productsData,
    isLoading: isLoadingProducts,
    isError: isProductsError,
    refetch: refetchProducts
  } = useQuery({
    queryKey: ['products', currentPage, pageSize, activeCategory, searchTerm, priceRange, inStockOnly],
    queryFn: () => productApi.getProducts(buildFilters()),
    staleTime: 60 * 1000, // 1 minute
  });

  // Calculer le nombre total de pages
  const totalPages = productsData ? Math.ceil(productsData.count / pageSize) : 0;

  // Mettre à jour le prix maximum en fonction des produits disponibles
  useEffect(() => {
    if (productsData && productsData.results.length > 0) {
      const highestPrice = Math.max(...productsData.results.map(product => product.price));
      setMaxPrice(Math.ceil(highestPrice / 100) * 100); // Arrondir au 100 supérieur
      
      // Mettre à jour le filtre de prix si nécessaire
      if (priceRange[1] < highestPrice) {
        setPriceRange([priceRange[0], highestPrice]);
      }
    }
  }, [productsData]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Catalogue de produits</h1>
          <p className="text-gray-600">
            Découvrez notre large gamme de produits informatiques et de télécommunication.
          </p>
        </div>

        {/* Search and Filter Bar */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-grow">
            <Input 
              type="text" 
              placeholder="Rechercher des produits..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          </div>
          <Button 
            onClick={() => setShowFilters(!showFilters)}
            variant="outline" 
            className="flex items-center gap-2"
          >
            <SlidersHorizontal size={18} />
            Filtres
          </Button>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="bg-gray-50 p-6 rounded-lg mb-8 animate-fade-in shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Categories */}
              <div>
                <h3 className="font-medium mb-3 text-gray-700">Catégories</h3>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      size="sm"
                      variant={activeCategory === category ? "default" : "outline"}
                      onClick={() => setActiveCategory(category)}
                      className="transition-all duration-200"
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Price Range */}
              <div className="col-span-1 md:col-span-2">
                <h3 className="font-medium mb-3 text-gray-700">Prix</h3>
                <div className="space-y-6">
                  <Slider
                    defaultValue={[0, maxPrice]}
                    max={maxPrice}
                    step={10}
                    value={priceRange}
                    onValueChange={(value) => setPriceRange(value as [number, number])}
                    className="my-6"
                  />
                  <div className="flex items-center justify-between">
                    <div className="px-4 py-2 bg-white rounded-md border border-gray-200 shadow-sm">
                      <span className="text-sm text-gray-500">Min</span>
                      <p className="font-medium">{formatCurrency(priceRange[0])}</p>
                    </div>
                    <div className="px-4 py-2 bg-white rounded-md border border-gray-200 shadow-sm">
                      <span className="text-sm text-gray-500">Max</span>
                      <p className="font-medium">{formatCurrency(priceRange[1])}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Stock Filter */}
              <div>
                <h3 className="font-medium mb-3 text-gray-700">Disponibilité</h3>
                <div className="flex items-center bg-white p-3 rounded-md border border-gray-200">
                  <input
                    type="checkbox"
                    id="inStock"
                    checked={inStockOnly}
                    onChange={() => setInStockOnly(!inStockOnly)}
                    className="mr-3 h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="inStock" className="text-gray-700">En stock uniquement</label>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Category Tabs - Simplified version for mobile */}
        <div className="flex overflow-x-auto pb-2 mb-6 hide-scrollbar">
          {categories.map((category) => (
            <Button
              key={category}
              variant="ghost"
              className={`whitespace-nowrap ${
                activeCategory === category ? "text-primary border-b-2 border-primary" : ""
              }`}
              onClick={() => setActiveCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Products Grid */}
        {isLoadingProducts ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Chargement des produits...</span>
          </div>
        ) : isProductsError ? (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium mb-2 text-red-600">Erreur de chargement</h3>
            <p className="text-gray-600 mb-4">Une erreur est survenue lors du chargement des produits.</p>
            <Button onClick={() => refetchProducts()} className="gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Réessayer
            </Button>
          </div>
        ) : productsData && productsData.results.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {productsData.results.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium mb-2">Aucun produit trouvé</h3>
            <p className="text-gray-600">Veuillez modifier vos filtres pour voir plus de produits.</p>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 0 && (
          <div className="mt-12">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    aria-label="Page précédente"
                  >
                    «
                  </Button>
                </PaginationItem>
                
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  // Logique pour afficher les pages autour de la page courante
                  let pageNum;
                  if (totalPages <= 5) {
                    // Si moins de 5 pages, afficher toutes les pages
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    // Si on est au début, afficher les 5 premières pages
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    // Si on est à la fin, afficher les 5 dernières pages
                    pageNum = totalPages - 4 + i;
                  } else {
                    // Sinon, afficher 2 pages avant et 2 pages après la page courante
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <PaginationItem key={pageNum}>
                      <Button 
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="icon"
                        onClick={() => setCurrentPage(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    </PaginationItem>
                  );
                })}
                
                <PaginationItem>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    aria-label="Page suivante"
                  >
                    »
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default Products;
