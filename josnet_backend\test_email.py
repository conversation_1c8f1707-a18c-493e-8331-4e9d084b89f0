#!/usr/bin/env python
"""
Script de test pour vérifier la configuration email Django
"""
import os
import django
from django.conf import settings

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

def test_email_config():
    """Teste la configuration email"""
    print("=== DIAGNOSTIC DE LA CONFIGURATION EMAIL ===\n")
    
    print("📧 Configuration Email Django:")
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"EMAIL_USE_TLS: {settings.EMAIL_USE_TLS}")
    print(f"EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"EMAIL_HOST_PASSWORD: {'*' * len(settings.EMAIL_HOST_PASSWORD) if settings.EMAIL_HOST_PASSWORD else 'NON CONFIGURÉ'}")
    print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print()
    
    # Vérifier les variables d'environnement
    print("🔧 Variables d'environnement:")
    env_vars = [
        'EMAIL_BACKEND', 'EMAIL_HOST', 'EMAIL_PORT', 'EMAIL_USE_TLS',
        'EMAIL_HOST_USER', 'EMAIL_HOST_PASSWORD', 'DEFAULT_FROM_EMAIL'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, 'NON DÉFINIE')
        if 'PASSWORD' in var and value != 'NON DÉFINIE':
            value = '*' * len(value)
        print(f"{var}: {value}")
    print()

def test_email_sending():
    """Teste l'envoi d'email"""
    from django.core.mail import send_mail
    from django.core.mail import get_connection
    
    print("📨 Test d'envoi d'email...")
    
    try:
        # Test de connexion SMTP
        print("🔗 Test de connexion SMTP...")
        connection = get_connection()
        connection.open()
        print("✅ Connexion SMTP réussie!")
        connection.close()
        
        # Test d'envoi d'email
        print("📧 Envoi d'email de test...")
        result = send_mail(
            subject='Test Email JosNet - Configuration',
            message='''
Bonjour,

Ceci est un email de test pour vérifier la configuration du système JosNet.

Si vous recevez ce message, la configuration email fonctionne correctement !

Détails du test:
- Serveur SMTP: Gmail
- Configuration: Production
- Timestamp: ''' + str(django.utils.timezone.now()) + '''

Cordialement,
L'équipe JosNet
            ''',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>', '<EMAIL>'],
            fail_silently=False,
        )
        
        if result:
            print(f"✅ Email envoyé avec succès! ({result} email(s) envoyé(s))")
        else:
            print("❌ Échec de l'envoi d'email")
            
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi: {e}")
        print(f"Type d'erreur: {type(e).__name__}")
        
        # Diagnostic spécifique selon le type d'erreur
        if "authentication" in str(e).lower():
            print("\n🔧 SOLUTION POSSIBLE:")
            print("- Vérifiez que l'authentification à 2 facteurs est activée sur Gmail")
            print("- Utilisez un mot de passe d'application, pas votre mot de passe Gmail")
            print("- Générez un nouveau mot de passe d'application: https://myaccount.google.com/apppasswords")
            
        elif "connection" in str(e).lower():
            print("\n🔧 SOLUTION POSSIBLE:")
            print("- Vérifiez votre connexion internet")
            print("- Vérifiez que le port 587 n'est pas bloqué par votre firewall")
            
        elif "permission" in str(e).lower():
            print("\n🔧 SOLUTION POSSIBLE:")
            print("- Activez 'Accès aux applications moins sécurisées' (non recommandé)")
            print("- Ou utilisez un mot de passe d'application (recommandé)")

def test_django_email_utils():
    """Teste les utilitaires email Django"""
    print("🛠️ Test des utilitaires email Django...")
    
    try:
        from authentication.utils import send_password_reset_email
        from authentication.models import User
        
        # Chercher un utilisateur existant
        user = User.objects.filter(email='<EMAIL>').first()
        if user:
            print(f"👤 Utilisateur trouvé: {user.email}")
            print("📧 Envoi d'email de récupération...")
            send_password_reset_email(user)
            print("✅ Email de récupération envoyé!")
        else:
            print("❌ Aucun utilisateur trouvé avec l'email <EMAIL>")
            
    except Exception as e:
        print(f"❌ Erreur avec les utilitaires Django: {e}")

if __name__ == "__main__":
    print("🚀 DÉMARRAGE DU TEST EMAIL JOSNET\n")
    
    # Test 1: Configuration
    test_email_config()
    
    # Test 2: Envoi d'email
    test_email_sending()
    
    # Test 3: Utilitaires Django
    test_django_email_utils()
    
    print("\n✅ Tests terminés!")
    print("\n💡 CONSEILS:")
    print("1. Si les emails n'arrivent pas, vérifiez le dossier spam")
    print("2. Redémarrez le serveur Django après modification du .env")
    print("3. Vérifiez que le mot de passe d'application Gmail est correct")
    print("4. Testez avec un autre fournisseur email si Gmail pose problème")
