/**
 * Service pour connecter la page d'accueil aux données du CMS admin
 */

import cmsApi from './cmsApi';
import coreApi from './coreApi';

export interface HomePageCmsData {
  siteSettings: any;
  heroContent: any;
  featuredPages: any[];
  blogPosts: any[];
  services: any[];
  testimonials: any[];
  stats: any;
  media: any[];
  menus: any[];
}

const homePageCmsApi = {
  /**
   * Récupérer toutes les données nécessaires pour la page d'accueil depuis le CMS
   */
  getHomePageData: async (): Promise<HomePageCmsData> => {
    try {
      console.log('🏠 Récupération des données de la page d\'accueil depuis le CMS...');

      // Récupérer toutes les données en parallèle
      const [
        siteSettings,
        pages,
        blogPosts,
        media,
        menus,
        coreStats,
        coreServices,
        coreTestimonials
      ] = await Promise.all([
        cmsApi.getCurrentSiteSettings(),
        cmsApi.getPages(),
        cmsApi.getBlogPosts(),
        cmsApi.getMedia(),
        cmsApi.getMenus(),
        coreApi.getLiveStats().catch(() => null),
        coreApi.getFeaturedServices().catch(() => []),
        coreApi.getFeaturedTestimonials().catch(() => [])
      ]);

      // Filtrer les pages pour la page d'accueil
      const featuredPages = pages?.data?.filter((page: any) => 
        page.status === 'published' && 
        (page.template === 'homepage' || page.featured)
      ) || [];

      // Filtrer les articles de blog récents et mis en avant
      const recentBlogPosts = blogPosts?.data?.filter((post: any) => 
        post.status === 'published'
      ).slice(0, 6) || [];

      // Préparer les données consolidées
      const homePageData: HomePageCmsData = {
        siteSettings: siteSettings || {},
        heroContent: {
          title: siteSettings?.site_title || "JOSNET NETWORK",
          description: siteSettings?.site_description || "Solutions IT et télécommunications au Burundi",
          backgroundImage: media?.data?.find((m: any) => m.title?.includes('hero') || m.title?.includes('banner'))?.file || null
        },
        featuredPages,
        blogPosts: recentBlogPosts,
        services: coreServices || [],
        testimonials: coreTestimonials || [],
        stats: coreStats || {
          total_customers: 1000,
          total_projects: 500,
          years_experience: 10,
          total_products: 150
        },
        media: media?.data || [],
        menus: menus?.data || []
      };

      console.log('✅ Données de la page d\'accueil récupérées:', {
        siteSettings: !!homePageData.siteSettings,
        featuredPages: homePageData.featuredPages.length,
        blogPosts: homePageData.blogPosts.length,
        services: homePageData.services.length,
        testimonials: homePageData.testimonials.length,
        media: homePageData.media.length,
        menus: homePageData.menus.length
      });

      return homePageData;

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des données de la page d\'accueil:', error);
      
      // Retourner des données de fallback
      return {
        siteSettings: {
          id: 1,
          site_title: "JOSNET NETWORK",
          site_description: "Solutions IT et télécommunications au Burundi",
          contact_email: "<EMAIL>",
          contact_phone: "+257 12 345 678"
        },
        heroContent: {
          title: "JOSNET NETWORK",
          description: "Solutions IT et télécommunications au Burundi",
          backgroundImage: null
        },
        featuredPages: [],
        blogPosts: [],
        services: [],
        testimonials: [],
        stats: {
          total_customers: 1000,
          total_projects: 500,
          years_experience: 10,
          total_products: 150
        },
        media: [],
        menus: []
      };
    }
  },

  /**
   * Récupérer les articles de blog pour la section blog de la page d'accueil
   */
  getBlogSectionData: async () => {
    try {
      const blogPosts = await cmsApi.getBlogPosts();
      
      // Filtrer et trier les articles
      const featuredPosts = blogPosts?.data?.filter((post: any) => 
        post.status === 'published' && post.featured
      ).slice(0, 3) || [];

      const recentPosts = blogPosts?.data?.filter((post: any) => 
        post.status === 'published'
      ).sort((a: any, b: any) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      ).slice(0, 6) || [];

      return {
        featuredPosts,
        recentPosts,
        totalPosts: blogPosts?.data?.length || 0
      };

    } catch (error) {
      console.error('Erreur récupération données blog:', error);
      return {
        featuredPosts: [],
        recentPosts: [],
        totalPosts: 0
      };
    }
  },

  /**
   * Récupérer les paramètres du site pour l'en-tête et le pied de page
   */
  getSiteConfiguration: async () => {
    try {
      const [siteSettings, menus] = await Promise.all([
        cmsApi.getCurrentSiteSettings(),
        cmsApi.getMenus()
      ]);

      return {
        siteSettings,
        menus: menus?.data || [],
        navigation: {
          header: menus?.data?.find((menu: any) => menu.location === 'header') || null,
          footer: menus?.data?.find((menu: any) => menu.location === 'footer') || null
        }
      };

    } catch (error) {
      console.error('Erreur récupération configuration site:', error);
      return {
        siteSettings: {
          site_title: "JOSNET NETWORK",
          site_description: "Solutions IT et télécommunications au Burundi"
        },
        menus: [],
        navigation: {
          header: null,
          footer: null
        }
      };
    }
  },

  /**
   * Récupérer les médias pour la galerie de la page d'accueil
   */
  getHomePageMedia: async () => {
    try {
      const media = await cmsApi.getMedia();
      
      // Filtrer les médias pour la page d'accueil
      const heroImages = media?.data?.filter((m: any) => 
        m.title?.toLowerCase().includes('hero') || 
        m.title?.toLowerCase().includes('banner') ||
        m.title?.toLowerCase().includes('accueil')
      ) || [];

      const galleryImages = media?.data?.filter((m: any) => 
        m.file_type === 'image' && 
        !m.title?.toLowerCase().includes('hero') &&
        !m.title?.toLowerCase().includes('banner')
      ).slice(0, 8) || [];

      return {
        heroImages,
        galleryImages,
        totalMedia: media?.data?.length || 0
      };

    } catch (error) {
      console.error('Erreur récupération médias page d\'accueil:', error);
      return {
        heroImages: [],
        galleryImages: [],
        totalMedia: 0
      };
    }
  },

  /**
   * Récupérer les pages importantes pour la navigation
   */
  getNavigationPages: async () => {
    try {
      const pages = await cmsApi.getPages();
      
      // Filtrer les pages importantes pour la navigation
      const mainPages = pages?.data?.filter((page: any) => 
        page.status === 'published' && 
        ['homepage', 'services', 'about', 'contact'].includes(page.template)
      ) || [];

      return {
        mainPages,
        totalPages: pages?.data?.length || 0
      };

    } catch (error) {
      console.error('Erreur récupération pages navigation:', error);
      return {
        mainPages: [],
        totalPages: 0
      };
    }
  }
};

export default homePageCmsApi;
