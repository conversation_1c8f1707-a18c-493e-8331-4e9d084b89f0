
import { useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import Hero from "@/components/home/<USER>";
import FeaturedProducts from "@/components/home/<USER>";
import AboutSection from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import { Button } from "@/components/ui/button";
import { ChevronRight, Headphones, Truck, ShieldCheck, RefreshCw, ArrowRight, MapPin, CheckCircle } from "lucide-react";

const Index = () => {
  const [email, setEmail] = useState("");
  
  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement newsletter signup logic here
    alert(`Merci de vous être inscrit avec ${email}`);
    setEmail("");
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero Section */}
        <Hero />
        
        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Nos avantages</h2>
              <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
              <p className="text-gray-600 max-w-2xl mx-auto">
                JOSNET NETWORK vous propose des solutions innovantes avec un service exceptionnel pour répondre à tous vos besoins technologiques.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Feature 1 */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-lg transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-1">
                <div className="w-16 h-16 flex items-center justify-center bg-primary/10 rounded-full mb-6 group-hover:bg-primary/20 transition-all duration-300">
                  <Truck className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Livraison rapide</h3>
                <p className="text-gray-600 text-center">Livraison disponible partout au Burundi avec un service fiable et ponctuel</p>
              </div>
              
              {/* Feature 2 */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-lg transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-1">
                <div className="w-16 h-16 flex items-center justify-center bg-primary/10 rounded-full mb-6 group-hover:bg-primary/20 transition-all duration-300">
                  <ShieldCheck className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Produits garantis</h3>
                <p className="text-gray-600 text-center">Tous nos produits sont garantis authentiques et testés pour une qualité optimale</p>
              </div>
              
              {/* Feature 3 */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-lg transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-1">
                <div className="w-16 h-16 flex items-center justify-center bg-primary/10 rounded-full mb-6 group-hover:bg-primary/20 transition-all duration-300">
                  <RefreshCw className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Retour facile</h3>
                <p className="text-gray-600 text-center">Politique de retour de 30 jours sans tracas pour votre tranquillité d'esprit</p>
              </div>
              
              {/* Feature 4 */}
              <div className="group flex flex-col items-center p-8 border border-gray-100 rounded-xl hover:shadow-lg transition-all duration-300 hover:border-primary/30 bg-white hover:-translate-y-1">
                <div className="w-16 h-16 flex items-center justify-center bg-primary/10 rounded-full mb-6 group-hover:bg-primary/20 transition-all duration-300">
                  <Headphones className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Support 24/7</h3>
                <p className="text-gray-600 text-center">Assistance technique disponible à tout moment pour répondre à vos questions</p>
              </div>
            </div>
          </div>
        </section>
        
        {/* Featured Products */}
        <FeaturedProducts />
        
        {/* Why Choose Us Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">Pourquoi choisir JOSNET NETWORK?</h2>
                <div className="w-24 h-1 bg-accent mb-8"></div>
                <p className="text-gray-700 mb-8">
                  Depuis plus de 10 ans, nous fournissons des solutions technologiques innovantes 
                  à nos clients au Burundi. Notre engagement envers l'excellence et la satisfaction 
                  client nous distingue de la concurrence.
                </p>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-accent mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-1">Expertise technique</h3>
                      <p className="text-gray-600">Notre équipe possède une expertise approfondie dans tous les domaines IT et télécom.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-accent mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-1">Solutions personnalisées</h3>
                      <p className="text-gray-600">Nous adaptons nos services pour répondre précisément à vos besoins spécifiques.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-accent mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-1">Service après-vente</h3>
                      <p className="text-gray-600">Un suivi continu pour assurer la performance optimale de vos systèmes.</p>
                    </div>
                  </div>
                </div>
                
                <Button 
                  className="mt-8 bg-accent hover:bg-accent/90 text-white px-6 py-3 rounded-md flex items-center gap-2"
                >
                  Nos services
                  <ArrowRight size={16} />
                </Button>
              </div>
              
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1573164713988-8665fc963095?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80" 
                  alt="JOSNET Services" 
                  className="rounded-lg shadow-xl object-cover w-full h-[500px]"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent rounded-lg flex items-end">
                  <div className="p-8 text-white">
                    <p className="text-2xl font-semibold">Technologies de pointe</p>
                    <p>Découvrez nos solutions innovantes</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Testimonials Section */}
        <Testimonials />
        
        {/* About Section */}
        <AboutSection />
        
        {/* Stats Section */}
        <section className="py-20 bg-gradient-to-r from-primary-dark to-primary text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')] bg-cover bg-center opacity-10"></div>
          <div className="container mx-auto px-4 relative">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">JOSNET en chiffres</h2>
              <div className="w-24 h-1 bg-accent mx-auto mb-6"></div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
                <div className="text-5xl font-bold mb-3 text-accent">1000+</div>
                <p className="text-xl text-white/90">Clients satisfaits</p>
              </div>
              <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
                <div className="text-5xl font-bold mb-3 text-accent">500+</div>
                <p className="text-xl text-white/90">Projets réalisés</p>
              </div>
              <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
                <div className="text-5xl font-bold mb-3 text-accent">10+</div>
                <p className="text-xl text-white/90">Années d'expérience</p>
              </div>
              <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/15 transition-all">
                <div className="text-5xl font-bold mb-3 text-accent">24/7</div>
                <p className="text-xl text-white/90">Support technique</p>
              </div>
            </div>
          </div>
        </section>
        
        {/* Locations Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Nos bureaux</h2>
              <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Retrouvez-nous dans nos différents bureaux à travers le Burundi
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all group">
                <div className="h-48 overflow-hidden">
                  <img 
                    src="https://images.unsplash.com/photo-1531273420860-7c755ee4a16a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80" 
                    alt="Bujumbura" 
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <MapPin className="h-6 w-6 text-accent mr-2" />
                    <h3 className="text-xl font-bold">Bujumbura</h3>
                  </div>
                  <p className="mb-2">123 Avenue Principale</p>
                  <p className="mb-2">Bujumbura, Burundi</p>
                  <p>+257 12 345 678</p>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all group">
                <div className="h-48 overflow-hidden">
                  <img 
                    src="https://images.unsplash.com/photo-1595841696677-6489ff3f8cd1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80" 
                    alt="Gitega" 
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <MapPin className="h-6 w-6 text-accent mr-2" />
                    <h3 className="text-xl font-bold">Gitega</h3>
                  </div>
                  <p className="mb-2">45 Rue du Commerce</p>
                  <p className="mb-2">Gitega, Burundi</p>
                  <p>+257 23 456 789</p>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all group">
                <div className="h-48 overflow-hidden">
                  <img 
                    src="https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" 
                    alt="Ngozi" 
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <MapPin className="h-6 w-6 text-accent mr-2" />
                    <h3 className="text-xl font-bold">Ngozi</h3>
                  </div>
                  <p className="mb-2">78 Boulevard Central</p>
                  <p className="mb-2">Ngozi, Burundi</p>
                  <p>+257 34 567 890</p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Call to Action */}
        <section className="py-20 bg-gradient-to-r from-primary-dark to-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Prêt à améliorer votre infrastructure IT?</h2>
            <p className="text-xl max-w-2xl mx-auto mb-8 text-white/90">
              Contactez-nous dès aujourd'hui pour discuter de vos besoins technologiques et découvrir comment JOSNET NETWORK peut vous aider.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button 
                size="lg" 
                className="bg-white text-primary hover:bg-gray-100 px-8"
              >
                Nous contacter
                <ChevronRight size={16} className="ml-2" />
              </Button>
              <Button 
                size="lg" 
                className="bg-transparent border-2 border-white text-white hover:bg-white/10 px-8"
              >
                Voir nos solutions
              </Button>
            </div>
          </div>
        </section>
        
        {/* Newsletter */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-xl mx-auto text-center">
              <h2 className="text-2xl md:text-3xl font-bold mb-4">Restez informé</h2>
              <div className="w-16 h-1 bg-accent mx-auto mb-6"></div>
              <p className="text-gray-600 mb-8">
                Abonnez-vous à notre newsletter pour recevoir les dernières nouvelles et offres spéciales.
              </p>
              <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-3">
                <input
                  type="email"
                  placeholder="Votre adresse email"
                  className="flex-grow px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                <Button 
                  type="submit"
                  className="bg-accent hover:bg-accent/90 text-white px-8"
                >
                  S'abonner
                </Button>
              </form>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Index;
