#!/usr/bin/env python3
"""
Script pour vérifier les avis du super admin
"""

import sys
import os
import requests

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from products.models import Product, ProductReview

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def check_admin_user():
    """Vérifier l'utilisateur admin"""
    print("👤 Vérification de l'utilisateur admin...")
    
    try:
        admin_user = User.objects.get(email=ADMIN_EMAIL)
        print(f"   ✅ Utilisateur trouvé: {admin_user.email}")
        print(f"   📝 Nom: {admin_user.first_name} {admin_user.last_name}")
        print(f"   🔑 Superuser: {admin_user.is_superuser}")
        print(f"   👨‍💼 Staff: {admin_user.is_staff}")
        print(f"   🏢 Admin: {getattr(admin_user, 'is_admin', 'N/A')}")
        print(f"   👨‍💼 Staff Member: {getattr(admin_user, 'is_staff_member', 'N/A')}")
        return admin_user
    except User.DoesNotExist:
        print(f"   ❌ Utilisateur admin non trouvé")
        return None

def check_admin_reviews(admin_user):
    """Vérifier les avis de l'admin"""
    print(f"\n📝 Vérification des avis de l'admin...")
    
    try:
        # Récupérer tous les avis de l'admin
        admin_reviews = ProductReview.objects.filter(user=admin_user)
        print(f"   📊 Total avis de l'admin: {admin_reviews.count()}")
        
        if admin_reviews.exists():
            print(f"   📋 Liste des avis:")
            for review in admin_reviews:
                print(f"      - Produit: {review.product.name}")
                print(f"        Note: {review.rating}⭐")
                print(f"        Titre: {review.title}")
                print(f"        Approuvé: {review.is_approved}")
                print(f"        Date: {review.created_at}")
                print()
        else:
            print(f"   ✅ Aucun avis trouvé pour l'admin")
        
        return admin_reviews
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def check_product_1_reviews():
    """Vérifier les avis pour le produit 1"""
    print(f"\n📦 Vérification des avis pour le produit 1...")
    
    try:
        product = Product.objects.get(id=1)
        print(f"   📝 Produit: {product.name}")
        
        # Récupérer tous les avis pour ce produit
        product_reviews = ProductReview.objects.filter(product=product)
        print(f"   📊 Total avis pour ce produit: {product_reviews.count()}")
        
        # Vérifier si l'admin a un avis pour ce produit
        admin_user = User.objects.get(email=ADMIN_EMAIL)
        admin_review_for_product = ProductReview.objects.filter(
            product=product,
            user=admin_user
        ).first()
        
        if admin_review_for_product:
            print(f"   ❌ L'admin a déjà un avis pour ce produit:")
            print(f"      - ID: {admin_review_for_product.id}")
            print(f"      - Note: {admin_review_for_product.rating}⭐")
            print(f"      - Titre: {admin_review_for_product.title}")
            print(f"      - Approuvé: {admin_review_for_product.is_approved}")
            return admin_review_for_product
        else:
            print(f"   ✅ L'admin n'a pas d'avis pour ce produit")
            return None
            
    except Product.DoesNotExist:
        print(f"   ❌ Produit 1 non trouvé")
        return None
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def test_can_review_api():
    """Tester l'API can-review pour l'admin"""
    print(f"\n🔍 Test de l'API can-review pour l'admin...")
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            print(f"   ✅ Authentification réussie")
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Tester l'API can-review
            response = requests.get(f"{API_BASE_URL}/products/reviews/can-review/", 
                params={'product': 1}, 
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ API can-review accessible")
                print(f"   🔍 Peut écrire un avis: {data.get('can_review')}")
                print(f"   📝 Raison: {data.get('reason')}")
                if 'existing_review_id' in data:
                    print(f"   🆔 ID avis existant: {data.get('existing_review_id')}")
                return data
            else:
                print(f"   ❌ Erreur API: {response.status_code}")
                print(f"   Réponse: {response.text}")
                return None
        else:
            print(f"   ❌ Échec de l'authentification: {login_response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def delete_admin_review_if_exists():
    """Supprimer l'avis de l'admin s'il existe (pour permettre de tester)"""
    print(f"\n🗑️ Option: Supprimer l'avis de l'admin pour permettre un nouveau test...")
    
    try:
        admin_user = User.objects.get(email=ADMIN_EMAIL)
        product = Product.objects.get(id=1)
        
        admin_review = ProductReview.objects.filter(
            product=product,
            user=admin_user
        ).first()
        
        if admin_review:
            print(f"   ⚠️ Avis trouvé (ID: {admin_review.id})")
            
            # Demander confirmation (simulation)
            print(f"   🤔 Voulez-vous supprimer cet avis pour permettre un nouveau test ?")
            print(f"   💡 Décommentez la ligne suivante pour supprimer:")
            print(f"   # admin_review.delete()")
            
            # Décommentez cette ligne pour supprimer l'avis
            # admin_review.delete()
            # print(f"   ✅ Avis supprimé")
            
            return True
        else:
            print(f"   ✅ Aucun avis à supprimer")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🔍 VÉRIFICATION DES AVIS DU SUPER ADMIN")
    print("=" * 60)
    
    # 1. Vérifier l'utilisateur admin
    admin_user = check_admin_user()
    
    if not admin_user:
        print("❌ Impossible de continuer sans utilisateur admin")
        return
    
    # 2. Vérifier les avis de l'admin
    admin_reviews = check_admin_reviews(admin_user)
    
    # 3. Vérifier spécifiquement pour le produit 1
    admin_review_product_1 = check_product_1_reviews()
    
    # 4. Tester l'API can-review
    can_review_data = test_can_review_api()
    
    # 5. Option de suppression
    delete_admin_review_if_exists()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Utilisateur admin trouvé: {'✅' if admin_user else '❌'}")
    print(f"   Avis admin pour produit 1: {'❌ Existe' if admin_review_product_1 else '✅ N\'existe pas'}")
    print(f"   API can-review: {'✅' if can_review_data else '❌'}")
    
    if admin_review_product_1:
        print(f"\n🎯 CONCLUSION:")
        print(f"   ❌ L'admin a déjà écrit un avis pour le produit 1")
        print(f"   📝 C'est pourquoi le message 'Impossible d'écrire un avis' s'affiche")
        print(f"   🔧 SOLUTIONS:")
        print(f"      1. Modifier l'avis existant au lieu d'en créer un nouveau")
        print(f"      2. Supprimer l'avis existant pour en créer un nouveau")
        print(f"      3. Utiliser un autre compte utilisateur")
        
        if admin_review_product_1:
            print(f"\n📋 DÉTAILS DE L'AVIS EXISTANT:")
            print(f"   🆔 ID: {admin_review_product_1.id}")
            print(f"   ⭐ Note: {admin_review_product_1.rating}")
            print(f"   📝 Titre: {admin_review_product_1.title}")
            print(f"   💬 Commentaire: {admin_review_product_1.comment[:100]}...")
            print(f"   ✅ Approuvé: {admin_review_product_1.is_approved}")
            print(f"   📅 Date: {admin_review_product_1.created_at}")
    else:
        print(f"\n🎯 CONCLUSION:")
        print(f"   ✅ L'admin n'a pas d'avis pour le produit 1")
        print(f"   🤔 Le problème pourrait venir d'ailleurs")
        print(f"   🔍 Vérifiez les permissions ou l'API can-review")

if __name__ == "__main__":
    main()
