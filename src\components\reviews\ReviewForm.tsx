import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import reviewApi, { Review, CreateReviewData, UpdateReviewData } from '@/services/reviewApi';

interface ReviewFormProps {
  productId: number;
  productName: string;
  existingReview?: Review;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ReviewForm: React.FC<ReviewFormProps> = ({
  productId,
  productName,
  existingReview,
  onSuccess,
  onCancel
}) => {
  const [rating, setRating] = useState(existingReview?.rating || 0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [title, setTitle] = useState(existingReview?.title || '');
  const [comment, setComment] = useState(existingReview?.comment || '');
  const [pros, setPros] = useState(existingReview?.pros || '');
  const [cons, setCons] = useState(existingReview?.cons || '');

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Vérifier si l'utilisateur peut écrire un avis (seulement pour les nouveaux avis)
  const { data: canReviewData, isLoading: isCheckingPermission } = useQuery({
    queryKey: ['canReview', productId],
    queryFn: () => reviewApi.canUserReview(productId),
    enabled: !existingReview, // Seulement pour les nouveaux avis
    retry: false,
  });

  const createMutation = useMutation({
    mutationFn: (data: CreateReviewData) => reviewApi.createReview(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productReviews', productId] });
      queryClient.invalidateQueries({ queryKey: ['reviewStats', productId] });
      toast({
        title: 'Avis créé',
        description: 'Votre avis a été soumis et sera publié après modération.',
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      let errorMessage = 'Impossible de créer l\'avis.';

      if (error.message && error.message.includes('déjà écrit un avis')) {
        errorMessage = error.message;
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      toast({
        title: 'Erreur',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: UpdateReviewData) => 
      reviewApi.updateReview(existingReview!.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productReviews', productId] });
      queryClient.invalidateQueries({ queryKey: ['reviewStats', productId] });
      toast({
        title: 'Avis modifié',
        description: 'Votre avis a été mis à jour.',
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.response?.data?.error || 'Impossible de modifier l\'avis.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      toast({
        title: 'Note requise',
        description: 'Veuillez attribuer une note au produit.',
        variant: 'destructive',
      });
      return;
    }

    if (!title.trim() || !comment.trim()) {
      toast({
        title: 'Champs requis',
        description: 'Veuillez remplir le titre et le commentaire.',
        variant: 'destructive',
      });
      return;
    }

    const data = {
      rating,
      title: title.trim(),
      comment: comment.trim(),
      pros: pros.trim() || undefined,
      cons: cons.trim() || undefined,
    };

    if (existingReview) {
      updateMutation.mutate(data);
    } else {
      createMutation.mutate({
        product: productId,
        ...data,
      });
    }
  };

  const renderStars = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className="focus:outline-none"
            onMouseEnter={() => setHoveredRating(star)}
            onMouseLeave={() => setHoveredRating(0)}
            onClick={() => setRating(star)}
          >
            <Star
              className={`h-8 w-8 transition-colors ${
                star <= (hoveredRating || rating)
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300 hover:text-yellow-200'
              }`}
            />
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600">
          {rating > 0 && (
            <>
              {rating}/5 - {
                rating === 1 ? 'Très décevant' :
                rating === 2 ? 'Décevant' :
                rating === 3 ? 'Correct' :
                rating === 4 ? 'Bien' :
                'Excellent'
              }
            </>
          )}
        </span>
      </div>
    );
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Affichage pendant la vérification des permissions
  if (isCheckingPermission) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p>Vérification des permissions...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Si l'utilisateur ne peut pas écrire d'avis (pour les nouveaux avis seulement)
  if (!existingReview && canReviewData && !canReviewData.can_review) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Impossible d'écrire un avis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">{canReviewData.reason}</p>
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              Fermer
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {existingReview ? 'Modifier votre avis' : 'Écrire un avis'}
        </CardTitle>
        <p className="text-sm text-gray-600">
          Produit : {productName}
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Note */}
          <div>
            <Label className="text-base font-medium">Note *</Label>
            <div className="mt-2">
              {renderStars()}
            </div>
          </div>

          {/* Titre */}
          <div>
            <Label htmlFor="title" className="text-base font-medium">
              Titre de votre avis *
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Résumez votre expérience en quelques mots"
              className="mt-1"
              maxLength={255}
            />
            <p className="text-xs text-gray-500 mt-1">
              {title.length}/255 caractères
            </p>
          </div>

          {/* Commentaire */}
          <div>
            <Label htmlFor="comment" className="text-base font-medium">
              Votre avis *
            </Label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Partagez votre expérience avec ce produit..."
              className="mt-1 min-h-[120px]"
              maxLength={2000}
            />
            <p className="text-xs text-gray-500 mt-1">
              {comment.length}/2000 caractères
            </p>
          </div>

          {/* Points positifs */}
          <div>
            <Label htmlFor="pros" className="text-base font-medium">
              Points positifs (optionnel)
            </Label>
            <Textarea
              id="pros"
              value={pros}
              onChange={(e) => setPros(e.target.value)}
              placeholder="Qu'avez-vous aimé dans ce produit ?"
              className="mt-1"
              maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">
              {pros.length}/500 caractères
            </p>
          </div>

          {/* Points négatifs */}
          <div>
            <Label htmlFor="cons" className="text-base font-medium">
              Points à améliorer (optionnel)
            </Label>
            <Textarea
              id="cons"
              value={cons}
              onChange={(e) => setCons(e.target.value)}
              placeholder="Qu'est-ce qui pourrait être amélioré ?"
              className="mt-1"
              maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">
              {cons.length}/500 caractères
            </p>
          </div>

          {/* Informations */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">À savoir :</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Votre avis sera modéré avant publication</li>
              <li>• Seuls les avis constructifs et respectueux sont acceptés</li>
              <li>• Vous pouvez modifier votre avis après publication</li>
              <li>• Les achats vérifiés sont mis en évidence</li>
            </ul>
          </div>

          {/* Boutons */}
          <div className="flex justify-end space-x-3">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                Annuler
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Envoi...' : existingReview ? 'Modifier' : 'Publier'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ReviewForm;
