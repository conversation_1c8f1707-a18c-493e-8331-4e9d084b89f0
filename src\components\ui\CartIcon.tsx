
import { ShoppingCart } from "lucide-react";

interface CartIconProps {
  count?: number;
}

const CartIcon = ({ count = 0 }: CartIconProps) => {
  return (
    <div className="relative">
      <ShoppingCart className="h-6 w-6" />
      {count > 0 && (
        <span className="absolute -top-2 -right-2 bg-accent text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
          {count > 9 ? '9+' : count}
        </span>
      )}
    </div>
  );
};

export default CartIcon;
