<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Reset Password - JosNet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f9f9f9;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            background: #007bff;
            color: white;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Reset Password System</h1>
        
        <div class="test-section">
            <h3>Étape 1: Générer un token de reset</h3>
            <div class="form-group">
                <label for="emailRequest">Email:</label>
                <input type="email" id="emailRequest" value="<EMAIL>">
            </div>
            <button onclick="requestPasswordReset()">Demander reset</button>
            <div id="tokenResult" style="margin-top: 10px; font-family: monospace; background: #e9ecef; padding: 10px; border-radius: 3px; display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Étape 2: Tester la réinitialisation</h3>
            <div class="form-group">
                <label for="resetToken">Token:</label>
                <input type="text" id="resetToken" placeholder="Token généré à l'étape 1">
            </div>
            <div class="form-group">
                <label for="newPassword">Nouveau mot de passe:</label>
                <input type="password" id="newPassword" value="NewPassword123!">
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirmer mot de passe:</label>
                <input type="password" id="confirmPassword" value="NewPassword123!">
            </div>
            <button onclick="testPasswordReset()">Tester reset</button>
        </div>

        <div class="test-section">
            <h3>Étape 3: Test direct API</h3>
            <button onclick="testApiDirectly()">Test API direct avec curl</button>
        </div>

        <div class="log" id="logContainer">
            <strong>📋 Journal de debug:</strong><br>
            Prêt à déboguer le système de reset...<br>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let currentToken = '';

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logContainer.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span><br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        async function requestPasswordReset() {
            const email = document.getElementById('emailRequest').value;
            log(`🔄 Demande de reset pour: ${email}`);

            try {
                const response = await fetch(`${API_BASE}/auth/password/reset/request/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email })
                });

                const responseText = await response.text();
                log(`📡 Réponse brute: ${responseText}`, 'info');

                if (response.ok) {
                    const data = JSON.parse(responseText);
                    log(`✅ Reset demandé avec succès`, 'success');
                    
                    if (data.token) {
                        currentToken = data.token;
                        document.getElementById('resetToken').value = currentToken;
                        document.getElementById('tokenResult').style.display = 'block';
                        document.getElementById('tokenResult').innerHTML = `<strong>Token généré:</strong><br>${currentToken}`;
                        log(`🔑 Token: ${currentToken}`, 'info');
                    }
                    
                    if (data.reset_link) {
                        log(`🔗 Lien: ${data.reset_link}`, 'info');
                    }
                } else {
                    log(`❌ Erreur ${response.status}: ${responseText}`, 'error');
                }
            } catch (error) {
                log(`❌ Erreur réseau: ${error.message}`, 'error');
            }
        }

        async function testPasswordReset() {
            const token = document.getElementById('resetToken').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!token || !newPassword || !confirmPassword) {
                log('⚠️ Veuillez remplir tous les champs', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                log('⚠️ Les mots de passe ne correspondent pas', 'error');
                return;
            }

            log(`🔄 Test de reset avec token: ${token.substring(0, 10)}...`);

            const payload = {
                token: token,
                new_password: newPassword,
                confirm_password: confirmPassword
            };

            log(`📤 Payload envoyé: ${JSON.stringify(payload, null, 2)}`, 'info');

            try {
                const response = await fetch(`${API_BASE}/auth/password/reset/confirm/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                const responseText = await response.text();
                log(`📡 Réponse brute: ${responseText}`, 'info');
                log(`📊 Status: ${response.status} ${response.statusText}`, 'info');

                // Afficher tous les headers de réponse
                for (let [key, value] of response.headers.entries()) {
                    log(`📋 Header ${key}: ${value}`, 'info');
                }

                if (response.ok) {
                    const data = JSON.parse(responseText);
                    log(`✅ Reset réussi: ${data.message}`, 'success');
                } else {
                    try {
                        const errorData = JSON.parse(responseText);
                        log(`❌ Erreur détaillée: ${JSON.stringify(errorData, null, 2)}`, 'error');
                    } catch {
                        log(`❌ Erreur ${response.status}: ${responseText}`, 'error');
                    }
                }
            } catch (error) {
                log(`❌ Erreur réseau: ${error.message}`, 'error');
                log(`❌ Stack trace: ${error.stack}`, 'error');
            }
        }

        async function testApiDirectly() {
            log('🧪 Test API direct...', 'info');
            
            // Test de connectivité de base
            try {
                const response = await fetch(`${API_BASE}/auth/password/reset/request/`, {
                    method: 'OPTIONS'
                });
                log(`✅ Endpoint accessible (OPTIONS): ${response.status}`, 'success');
            } catch (error) {
                log(`❌ Endpoint non accessible: ${error.message}`, 'error');
                return;
            }

            // Test avec données invalides pour voir la réponse d'erreur
            try {
                const response = await fetch(`${API_BASE}/auth/password/reset/confirm/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: 'invalid-token',
                        new_password: 'test',
                        confirm_password: 'test'
                    })
                });

                const responseText = await response.text();
                log(`🧪 Test avec token invalide - Status: ${response.status}`, 'info');
                log(`🧪 Réponse: ${responseText}`, 'info');
            } catch (error) {
                log(`❌ Erreur test API: ${error.message}`, 'error');
            }
        }

        // Initialisation
        window.onload = function() {
            log('🚀 Page de debug chargée');
            log('💡 Commencez par générer un token à l\'étape 1');
        };
    </script>
</body>
</html>
