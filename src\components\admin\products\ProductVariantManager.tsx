import React, { useState, useEffect } from 'react';
import { getImageUrl, handleImageError } from '@/utils/imageUtils';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/components/ui/use-toast';
import { 
  Plus, 
  Wand2, 
  Edit, 
  Trash2, 
  Check, 
  X, 
  AlertCircle,
  Image as ImageIcon,
  RefreshCw
} from 'lucide-react';
import { productApi } from '@/services/productApi';
import { formatPrice } from '@/lib/utils';

interface ProductVariantManagerProps {
  productId: number;
  productPrice: number;
}

interface VariantAttribute {
  id: number;
  attribute: number;
  attribute_name: string;
  value: string;
}

interface ProductAttribute {
  id: number;
  name: string;
  description?: string;
}

interface AttributeValue {
  id: number;
  attribute: number;
  value: string;
}

interface ProductVariant {
  id: number;
  name: string;
  sku: string;
  price: number;
  price_adjustment: number;
  is_active: boolean;
  image?: string;
  attributes: VariantAttribute[];
  inventory?: {
    available_quantity: number;
    is_low_stock: boolean;
  };
}

const ProductVariantManager: React.FC<ProductVariantManagerProps> = ({ 
  productId, 
  productPrice 
}) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch product variants
  const { 
    data: variants = [], 
    isLoading: isLoadingVariants, 
    isError: isVariantsError 
  } = useQuery({
    queryKey: ['productVariants', productId],
    queryFn: () => productApi.getProductVariants(productId),
  });
  
  // Fetch product attributes
  const { 
    data: attributes = [], 
    isLoading: isLoadingAttributes 
  } = useQuery({
    queryKey: ['productAttributes'],
    queryFn: () => productApi.getProductAttributes(),
  });
  
  // Fetch attribute values
  const { 
    data: attributeValues = [], 
    isLoading: isLoadingAttributeValues 
  } = useQuery({
    queryKey: ['attributeValues'],
    queryFn: () => productApi.getAttributeValues(),
  });
  
  // Create variant mutation
  const createVariantMutation = useMutation({
    mutationFn: (data: any) => productApi.createProductVariant(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variante créée',
        description: 'La variante de produit a été créée avec succès.',
      });
      setIsCreateDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Échec de création de la variante',
        description: error.message || 'Une erreur est survenue.',
        variant: 'destructive',
      });
    },
  });
  
  // Generate variants mutation
  const generateVariantsMutation = useMutation({
    mutationFn: (data: any) => productApi.generateProductVariants(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variantes générées',
        description: 'Les variantes de produit ont été générées avec succès.',
      });
      setIsGenerateDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Échec de génération des variantes',
        description: error.message || 'Une erreur est survenue.',
        variant: 'destructive',
      });
    },
  });
  
  // Update variant mutation
  const updateVariantMutation = useMutation({
    mutationFn: (data: { id: number, variant: any }) => 
      productApi.updateProductVariant(data.id, data.variant),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variante mise à jour',
        description: 'La variante de produit a été mise à jour avec succès.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Échec de mise à jour de la variante',
        description: error.message || 'Une erreur est survenue.',
        variant: 'destructive',
      });
    },
  });
  
  // Delete variant mutation
  const deleteVariantMutation = useMutation({
    mutationFn: (variantId: number) => productApi.deleteProductVariant(variantId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productVariants', productId] });
      toast({
        title: 'Variante supprimée',
        description: 'La variante de produit a été supprimée avec succès.',
      });
      setIsDeleteDialogOpen(false);
      setSelectedVariant(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Échec de suppression de la variante',
        description: error.message || 'Une erreur est survenue.',
        variant: 'destructive',
      });
    },
  });
  
  // Handle variant deletion
  const handleDeleteVariant = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle variant status toggle
  const handleToggleStatus = (variant: ProductVariant) => {
    updateVariantMutation.mutate({
      id: variant.id,
      variant: {
        ...variant,
        is_active: !variant.is_active
      }
    });
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Variantes de produit</CardTitle>
        <CardDescription>
          Gérez les variantes de produit avec différents attributs, prix et niveaux de stock.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={(e) => {
              e.preventDefault(); // Empêche la propagation de l'événement
              e.stopPropagation(); // Arrête la propagation vers le formulaire parent
              setIsCreateDialogOpen(true);
            }}
            className="gap-2"
            type="button" // Spécifie explicitement que c'est un bouton et non un submit
          >
            <Plus className="h-4 w-4" />
            <span>Ajouter une variante</span>
          </Button>
          
          <Button 
            variant="outline" 
            onClick={(e) => {
              e.preventDefault(); // Empêche la propagation de l'événement
              e.stopPropagation(); // Arrête la propagation vers le formulaire parent
              setIsGenerateDialogOpen(true);
            }}
            className="gap-2"
            type="button" // Spécifie explicitement que c'est un bouton et non un submit
          >
            <Wand2 className="h-4 w-4" />
            <span>Générer des variantes</span>
          </Button>
        </div>
        
        {/* Variants table */}
        {isLoadingVariants ? (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : isVariantsError ? (
          <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span>Impossible de charger les variantes. Veuillez réessayer.</span>
          </div>
        ) : variants.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 bg-gray-50 border border-dashed rounded-md">
            <p className="text-sm text-gray-500 mb-4">Aucune variante pour le moment. Ajoutez des variantes ou générez-les automatiquement.</p>
            <div className="flex gap-2">
              <Button 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsCreateDialogOpen(true);
                }}
                size="sm"
                type="button"
              >
                Ajouter une variante
              </Button>
              <Button 
                variant="outline" 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsGenerateDialogOpen(true);
                }}
                size="sm"
                type="button"
              >
                Générer des variantes
              </Button>
            </div>
          </div>
        ) : (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Image</TableHead>
                  <TableHead>Nom</TableHead>
                  <TableHead>Référence</TableHead>
                  <TableHead>Attributs</TableHead>
                  <TableHead>Prix</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {variants.map((variant) => (
                  <TableRow key={variant.id}>
                    <TableCell>
                      {(variant as any).image ? (
                        <img 
                          src={getImageUrl((variant as any).image)} 
                          alt={variant.name}
                          className="h-10 w-10 object-cover rounded-md"
                          onError={handleImageError}
                        />
                      ) : (
                        <div className="h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                          <ImageIcon className="h-5 w-5 text-gray-400" />
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{variant.name}</TableCell>
                    <TableCell>{variant.sku}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {variant.attributes.map((attr) => (
                          <span 
                            key={attr.id} 
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100"
                          >
                            {attr.attribute_name}: {attr.value}
                          </span>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      {variant.price_adjustment !== 0 ? (
                        <div>
                          <div>{formatPrice(variant.price)}</div>
                          <div className="text-xs text-gray-500">
                            {variant.price_adjustment > 0 ? '+' : ''}{formatPrice(variant.price_adjustment)}
                          </div>
                        </div>
                      ) : (
                        formatPrice(variant.price)
                      )}
                    </TableCell>
                    <TableCell>
                      {variant.inventory ? (
                        <div>
                          <div>{variant.inventory.available_quantity} disponible{variant.inventory.available_quantity > 1 ? 's' : ''}</div>
                          {variant.inventory.is_low_stock && (
                            <div className="text-xs text-amber-600">Stock faible</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500">Pas de stock</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`gap-2 ${variant.is_active ? 'text-green-600' : 'text-red-600'}`}
                        onClick={() => handleToggleStatus(variant)}
                      >
                        {variant.is_active ? (
                          <>
                            <Check className="h-4 w-4" />
                            <span>Actif</span>
                          </>
                        ) : (
                          <>
                            <X className="h-4 w-4" />
                            <span>Inactif</span>
                          </>
                        )}
                      </Button>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedVariant(variant);
                            setIsCreateDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-600"
                          onClick={() => handleDeleteVariant(variant)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      
      {/* Create/Edit Variant Dialog */}
      <VariantFormDialog 
        isOpen={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        productId={productId}
        productPrice={productPrice}
        variant={selectedVariant}
        attributes={attributes}
        attributeValues={attributeValues}
        onSubmit={(data) => {
          if (selectedVariant) {
            updateVariantMutation.mutate({
              id: selectedVariant.id,
              variant: data
            });
          } else {
            createVariantMutation.mutate(data);
          }
        }}
        isLoading={createVariantMutation.isPending || updateVariantMutation.isPending}
        onClose={() => setSelectedVariant(null)}
      />
      
      {/* Generate Variants Dialog */}
      <GenerateVariantsDialog 
        isOpen={isGenerateDialogOpen}
        onOpenChange={setIsGenerateDialogOpen}
        productId={productId}
        attributes={attributes}
        attributeValues={attributeValues}
        onSubmit={(data) => {
          generateVariantsMutation.mutate(data);
        }}
        isLoading={generateVariantsMutation.isPending}
      />
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
            <AlertDialogDescription>
              Cette action supprimera définitivement la variante "{selectedVariant?.name}".
              Cette action ne peut pas être annulée.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedVariant(null)}>Annuler</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedVariant && deleteVariantMutation.mutate(selectedVariant.id)}
              className="bg-red-600 hover:bg-red-700"
            >
              Supprimer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

interface VariantFormDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  productId: number;
  productPrice: number;
  variant: ProductVariant | null;
  attributes: ProductAttribute[];
  attributeValues: AttributeValue[];
  onSubmit: (data: any) => void;
  isLoading: boolean;
  onClose: () => void;
}

const VariantFormDialog: React.FC<VariantFormDialogProps> = ({
  isOpen,
  onOpenChange,
  productId,
  productPrice,
  variant,
  attributes,
  attributeValues,
  onSubmit,
  isLoading,
  onClose
}) => {
  const [name, setName] = useState('');
  const [sku, setSku] = useState('');
  const [priceAdjustment, setPriceAdjustment] = useState('0');
  const [isActive, setIsActive] = useState(true);
  const [selectedAttributes, setSelectedAttributes] = useState<Record<number, number>>({});
  const [image, setImage] = useState('');
  
  // Reset form when dialog opens or variant changes
  useEffect(() => {
    if (isOpen) {
      if (variant) {
        setName(variant.name);
        setSku(variant.sku);
        setPriceAdjustment(variant.price_adjustment.toString());
        setIsActive(variant.is_active);
        setImage(variant.image || '');
        
        // Set selected attributes
        const attrMap: Record<number, number> = {};
        variant.attributes.forEach(attr => {
          attrMap[attr.attribute] = attr.id;
        });
        setSelectedAttributes(attrMap);
      } else {
        setName('');
        setSku('');
        setPriceAdjustment('0');
        setIsActive(true);
        setImage('');
        setSelectedAttributes({});
      }
    }
  }, [isOpen, variant]);
  
  const handleClose = () => {
    onOpenChange(false);
    onClose();
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const attributeValueIds = Object.values(selectedAttributes);
    
    onSubmit({
      product: productId,
      name,
      sku: sku || undefined,
      price_adjustment: parseFloat(priceAdjustment) || 0,
      is_active: isActive,
      image: image || null,
      attribute_values: attributeValueIds
    });
  };
  
  const filteredAttributeValues = (attributeId: number) => {
    return attributeValues.filter(av => av.attribute === attributeId);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{variant ? 'Modifier la variante' : 'Ajouter une variante'}</DialogTitle>
          <DialogDescription>
            {variant 
              ? 'Modifiez les détails de cette variante de produit.' 
              : 'Créez une nouvelle variante pour ce produit avec différents attributs.'}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Nom
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder="Nom de la variante"
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="image" className="text-right">
                Image URL
              </Label>
              <Input
                id="image"
                value={image}
                onChange={(e) => setImage(e.target.value)}
                className="col-span-3"
                placeholder="URL de l'image de la variante"
              />
            </div>
            
            {image && (
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="text-right">
                  <span className="text-sm text-gray-500">Aperçu</span>
                </div>
                <div className="col-span-3">
                  <img 
                    src={getImageUrl(image)} 
                    alt="Aperçu de l'image"
                    className="h-20 w-20 object-cover rounded-md border"
                    onError={handleImageError}
                  />
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="sku" className="text-right">
                Référence
              </Label>
              <Input
                id="sku"
                value={sku}
                onChange={(e) => setSku(e.target.value)}
                className="col-span-3"
                placeholder="Laissez vide pour générer automatiquement"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="price-adjustment" className="text-right">
                Ajustement de prix
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="price-adjustment"
                  type="number"
                  step="0.01"
                  value={priceAdjustment}
                  onChange={(e) => setPriceAdjustment(e.target.value)}
                />
                <div className="text-sm text-gray-500">
                  Prix final: {formatPrice(productPrice + parseFloat(priceAdjustment || '0'))}
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Status
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Checkbox
                  id="is-active"
                  checked={isActive}
                  onCheckedChange={(checked) => setIsActive(checked as boolean)}
                />
                <label
                  htmlFor="is-active"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Active
                </label>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <Label className="text-right pt-2">
                Attributes
              </Label>
              <div className="col-span-3 space-y-4">
                {!Array.isArray(attributes) ? (
                  <p className="text-sm text-gray-500">No attributes available.</p>
                ) : attributes.length === 0 ? (
                  <p className="text-sm text-gray-500">No attributes available.</p>
                ) : (
                  attributes.map((attribute) => (
                    <div key={attribute.id} className="space-y-2">
                      <Label htmlFor={`attribute-${attribute.id}`}>
                        {attribute.name}
                      </Label>
                      <Select
                        value={selectedAttributes[attribute.id]?.toString() || ''}
                        onValueChange={(value) => {
                          setSelectedAttributes({
                            ...selectedAttributes,
                            [attribute.id]: parseInt(value)
                          });
                        }}
                      >
                        <SelectTrigger id={`attribute-${attribute.id}`}>
                          <SelectValue placeholder={`Select ${attribute.name}`} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none" >None</SelectItem>
                          {filteredAttributeValues(attribute.id).map((value) => (
                            <SelectItem key={value.id} value={value.id.toString()}>
                              {value.value}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  {variant ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                variant ? 'Mettre à jour' : 'Créer la variante'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

interface GenerateVariantsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  productId: number;
  attributes: ProductAttribute[];
  attributeValues: AttributeValue[];
  onSubmit: (data: any) => void;
  isLoading: boolean;
}

const GenerateVariantsDialog: React.FC<GenerateVariantsDialogProps> = ({
  isOpen,
  onOpenChange,
  productId,
  attributes,
  attributeValues,
  onSubmit,
  isLoading
}) => {
  const [selectedAttributeGroups, setSelectedAttributeGroups] = useState<Record<number, number[]>>({});
  const [priceAdjustment, setPriceAdjustment] = useState('0');
  const [isActive, setIsActive] = useState(true);
  
  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedAttributeGroups({});
      setPriceAdjustment('0');
      setIsActive(true);
    }
  }, [isOpen]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Convert selected attribute groups to the format expected by the API
    const attributeValueGroups = Object.values(selectedAttributeGroups)
      .filter(group => group.length > 0);
    
    if (attributeValueGroups.length === 0) {
      return;
    }
    
    onSubmit({
      product: productId,
      attribute_values: attributeValueGroups,
      price_adjustment: parseFloat(priceAdjustment) || 0,
      is_active: isActive
    });
  };
  
  const handleAttributeChange = (attributeId: number, valueId: number, checked: boolean) => {
    setSelectedAttributeGroups(prev => {
      const currentValues = prev[attributeId] || [];
      
      if (checked) {
        return {
          ...prev,
          [attributeId]: [...currentValues, valueId]
        };
      } else {
        return {
          ...prev,
          [attributeId]: currentValues.filter(id => id !== valueId)
        };
      }
    });
  };
  
  const filteredAttributeValues = (attributeId: number) => {
    return attributeValues.filter(av => av.attribute === attributeId);
  };
  
  // Calculate total number of variants that will be generated
  const calculateTotalVariants = () => {
    const groups = Object.values(selectedAttributeGroups)
      .filter(group => group.length > 0);
    
    if (groups.length === 0) return 0;
    
    return groups.reduce((total, group) => total * group.length, 1);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Générer des variantes</DialogTitle>
          <DialogDescription>
            Générez toutes les combinaisons possibles de variantes basées sur les attributs sélectionnés.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="price-adjustment" className="text-right">
                Ajustement de prix
              </Label>
              <Input
                id="price-adjustment"
                type="number"
                step="0.01"
                value={priceAdjustment}
                onChange={(e) => setPriceAdjustment(e.target.value)}
                className="col-span-3"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Status
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Checkbox
                  id="is-active-generate"
                  checked={isActive}
                  onCheckedChange={(checked) => setIsActive(checked as boolean)}
                />
                <label
                  htmlFor="is-active-generate"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Active
                </label>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <Label className="text-right pt-2">
                Attributes
              </Label>
              <div className="col-span-3">
                <Tabs defaultValue={attributes && attributes.length > 0 ? attributes[0]?.id.toString() : undefined}>
                  <TabsList className="mb-2">
                    {Array.isArray(attributes) ? attributes.map((attribute) => (
                      <TabsTrigger key={attribute.id} value={attribute.id.toString()}>
                        {attribute.name}
                      </TabsTrigger>
                    )) : <div>Aucun attribut disponible</div>}
                  </TabsList>
                  
                  {Array.isArray(attributes) ? attributes.map((attribute) => (
                    <TabsContent key={attribute.id} value={attribute.id.toString()} className="space-y-4">
                      <div className="grid grid-cols-2 gap-2">
                        {Array.isArray(filteredAttributeValues(attribute.id)) ? filteredAttributeValues(attribute.id).map((value) => (
                          <div key={value.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`value-${value.id}`}
                              checked={(selectedAttributeGroups[attribute.id] || []).includes(value.id)}
                              onCheckedChange={(checked) => 
                                handleAttributeChange(attribute.id, value.id, checked as boolean)
                              }
                            />
                            <label
                              htmlFor={`value-${value.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {value.value}
                            </label>
                          </div>
                        )) : <div>Aucune valeur d'attribut disponible</div>}
                      </div>
                    </TabsContent>
                  )) : <div className="p-4 text-center text-gray-500">Aucun attribut disponible pour générer des variantes</div>}
                </Tabs>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <div></div>
              <div className="col-span-3">
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm font-medium">
                    Cela va générer {calculateTotalVariants()} variante{calculateTotalVariants() > 1 ? 's' : ''}.
                  </p>
                  {calculateTotalVariants() > 20 && (
                    <p className="text-xs text-amber-600 mt-1">
                      Attention : La génération d'un grand nombre de variantes peut prendre du temps.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Annuler
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || calculateTotalVariants() === 0}
            >
              {isLoading ? (
                <>
                  <Wand2 className="h-4 w-4 animate-spin mr-2" />
                  Génération en cours...
                </>
              ) : (
                'Générer les variantes'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ProductVariantManager;

