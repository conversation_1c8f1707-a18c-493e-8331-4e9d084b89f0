from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    Page, BlogCategory, BlogPost, Comment,
    Media, Menu, MenuItem, SiteSetting
)

@admin.register(Page)
class PageAdmin(admin.ModelAdmin):
    list_display = ('title', 'slug', 'template', 'status', 'updated_at')
    list_filter = ('status', 'template')
    search_fields = ('title', 'content')
    prepopulated_fields = {'slug': ('title',)}
    fieldsets = (
        (None, {
            'fields': ('title', 'slug', 'content')
        }),
        (_('SEO'), {
            'fields': ('meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
        (_('Settings'), {
            'fields': ('template', 'status', 'author')
        }),
    )

@admin.register(BlogCategory)
class BlogCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug')
    search_fields = ('name',)
    prepopulated_fields = {'slug': ('name',)}

@admin.register(BlogPost)
class BlogPostAdmin(admin.ModelAdmin):
    list_display = ('title', 'slug', 'status', 'created_at')
    list_filter = ('status', 'categories')
    search_fields = ('title', 'content')
    prepopulated_fields = {'slug': ('title',)}
    filter_horizontal = ('categories',)
    fieldsets = (
        (None, {
            'fields': ('title', 'slug', 'content', 'excerpt', 'featured_image')
        }),
        (_('Categories'), {
            'fields': ('categories',)
        }),
        (_('SEO'), {
            'fields': ('meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
        (_('Settings'), {
            'fields': ('status', 'author')
        }),
    )

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('author_name', 'post', 'status', 'created_at')
    list_filter = ('status',)
    search_fields = ('author_name', 'author_email', 'content')
    actions = ['approve_comments', 'reject_comments']

    def approve_comments(self, request, queryset):
        queryset.update(status='approved')
    approve_comments.short_description = _("Mark selected comments as approved")

    def reject_comments(self, request, queryset):
        queryset.update(status='rejected')
    reject_comments.short_description = _("Mark selected comments as rejected")

@admin.register(Media)
class MediaAdmin(admin.ModelAdmin):
    list_display = ('title', 'file_type', 'file_name', 'file_size', 'created_at')
    list_filter = ('file_type',)
    search_fields = ('title', 'description')
    readonly_fields = ('file_url', 'file_size', 'file_name')

class MenuItemInline(admin.TabularInline):
    model = MenuItem
    extra = 1
    fk_name = 'menu'

@admin.register(Menu)
class MenuAdmin(admin.ModelAdmin):
    list_display = ('name', 'location')
    list_filter = ('location',)
    search_fields = ('name',)
    inlines = [MenuItemInline]

@admin.register(MenuItem)
class MenuItemAdmin(admin.ModelAdmin):
    list_display = ('title', 'menu', 'url', 'page', 'order')
    list_filter = ('menu',)
    search_fields = ('title',)
    list_editable = ('order',)

@admin.register(SiteSetting)
class SiteSettingAdmin(admin.ModelAdmin):
    list_display = ('site_title', 'contact_email', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('site_title', 'site_description', 'logo', 'favicon')
        }),
        (_('Contact Information'), {
            'fields': ('contact_email', 'contact_phone', 'contact_address')
        }),
        (_('Social Media'), {
            'fields': ('facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url')
        }),
    )
