from django.db import models
from products.models import Product, Category

class Promotion(models.Model):
    """
    Model for product promotions and discounts.
    """
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    discount_type = models.CharField(max_length=20, choices=[
        ('percentage', 'Percentage'),
        ('fixed_amount', 'Fixed Amount'),
    ])
    discount_value = models.DecimalField(max_digits=10, decimal_places=2)
    discount_code = models.CharField(max_length=50, blank=True, null=True)
    applies_to = models.CharField(max_length=20, choices=[
        ('product', 'Product'),
        ('category', 'Category'),
        ('cart', 'Cart'),
    ])
    target_id = models.PositiveIntegerField(blank=True, null=True)  # Product or Category ID
    min_purchase_amount = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
    
    @property
    def target_name(self):
        """Get the name of the target product or category."""
        if self.applies_to == 'product' and self.target_id:
            try:
                return Product.objects.get(id=self.target_id).name
            except Product.DoesNotExist:
                return None
        elif self.applies_to == 'category' and self.target_id:
            try:
                return Category.objects.get(id=self.target_id).name
            except Category.DoesNotExist:
                return None
        return None

class Campaign(models.Model):
    """
    Model for marketing campaigns.
    """
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(blank=True, null=True)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    discount_code = models.CharField(max_length=50, blank=True, null=True)
    products = models.ManyToManyField(Product, related_name='campaigns')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
    
    @property
    def product_count(self):
        """Get the number of products in the campaign."""
        return self.products.count()
