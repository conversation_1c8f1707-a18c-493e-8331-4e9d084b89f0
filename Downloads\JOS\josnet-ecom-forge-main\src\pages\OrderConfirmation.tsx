
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Check, FileText, ArrowRight, Clock, Package, Truck } from "lucide-react";

interface OrderData {
  id: string;
  date: string;
  items: any[];
  shipping: {
    firstName: string;
    lastName: string;
    address: string;
    city: string;
    province: string;
    postcode: string;
  };
  paymentMethod: string;
  subtotal: number;
  shippingCost: number;  // Renamed from shipping to shippingCost to avoid duplicate
  total: number;
}

const OrderConfirmation = () => {
  const { orderId } = useParams();
  const [order, setOrder] = useState<OrderData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real app, this would fetch order data from an API
    const savedOrder = localStorage.getItem("lastOrder");
    
    if (savedOrder) {
      try {
        const parsedOrder = JSON.parse(savedOrder);
        // Check if this is the right order
        if (parsedOrder.id === orderId) {
          setOrder({
            ...parsedOrder,
            date: new Date(parsedOrder.date).toLocaleDateString("fr-FR", {
              year: "numeric",
              month: "long",
              day: "numeric"
            })
          });
        }
      } catch (e) {
        console.error("Error parsing order data:", e);
      }
    }
    setLoading(false);
  }, [orderId]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <h1 className="text-2xl font-bold mb-4">Commande introuvable</h1>
            <p className="text-gray-600 mb-6">
              Nous n'avons pas pu trouver les détails de la commande #{orderId}.
            </p>
            <Button asChild>
              <Link to="/account/orders">Voir mes commandes</Link>
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Order confirmation header with success icon */}
        <div className="max-w-4xl mx-auto mb-8 text-center">
          <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-4">
            <Check className="h-8 w-8" />
          </div>
          <h1 className="text-3xl font-bold">Merci pour votre commande!</h1>
          <p className="text-xl text-gray-600 mt-2">
            Votre commande #{order.id} a été confirmée.
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          {/* Order details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div className="flex justify-between items-center border-b border-gray-200 pb-4 mb-4">
              <div>
                <h2 className="font-semibold text-lg">Détails de la commande</h2>
                <p className="text-gray-600">Passée le {order.date}</p>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  Télécharger la facture
                </Button>
              </div>
            </div>
            
            <div className="space-y-6">
              {/* Delivery address */}
              <div>
                <h3 className="font-medium mb-2">Adresse de livraison</h3>
                <div className="bg-gray-50 rounded p-3 text-sm">
                  <p className="font-medium">{order.shipping.firstName} {order.shipping.lastName}</p>
                  <p>{order.shipping.address}</p>
                  <p>{order.shipping.postcode} {order.shipping.city}</p>
                  <p>{order.shipping.province}</p>
                </div>
              </div>
              
              {/* Payment info */}
              <div>
                <h3 className="font-medium mb-2">Mode de paiement</h3>
                <div className="bg-gray-50 rounded p-3 text-sm">
                  {order.paymentMethod === "card" && <p>Carte bancaire</p>}
                  {order.paymentMethod === "bank" && <p>Virement bancaire</p>}
                  {order.paymentMethod === "mobile" && <p>Mobile Money</p>}
                </div>
              </div>
              
              {/* Order summary */}
              <div>
                <h3 className="font-medium mb-2">Résumé de la commande</h3>
                <div className="border rounded-md divide-y">
                  {order.items.map((item) => (
                    <div key={item.product.id} className="flex p-3">
                      <div className="w-16 h-16 flex-shrink-0 mr-4">
                        <img 
                          src={item.product.image} 
                          alt={item.product.name} 
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                      <div className="flex-grow">
                        <h4 className="font-medium">{item.product.name}</h4>
                        <p className="text-sm text-gray-500">Qté: {item.quantity}</p>
                      </div>
                      <div className="font-medium">
                        {(item.product.price * item.quantity).toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Sous-total</span>
                    <span>{order.subtotal.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Frais de livraison</span>
                    <span>{order.shippingCost === 0 ? "Gratuit" : order.shippingCost.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}</span>
                  </div>
                  <div className="flex justify-between font-medium text-lg pt-2 border-t">
                    <span>Total</span>
                    <span>{order.total.toLocaleString("fr-FR", { style: "currency", currency: "EUR" })}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Order status timeline */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h2 className="font-semibold text-lg mb-4">Suivi de commande</h2>
            
            <div className="relative">
              {/* Status timeline line */}
              <div className="absolute left-2.5 top-0 bottom-0 w-0.5 bg-gray-200"></div>
              
              {/* Status points */}
              <div className="space-y-6 relative">
                <div className="flex">
                  <div className="bg-green-500 rounded-full h-5 w-5 flex-shrink-0 z-10"></div>
                  <div className="ml-4">
                    <p className="font-medium">Commande confirmée</p>
                    <p className="text-sm text-gray-500">{order.date}</p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="bg-gray-200 rounded-full h-5 w-5 flex-shrink-0 z-10 flex items-center justify-center">
                    <Clock className="h-3 w-3 text-gray-600" />
                  </div>
                  <div className="ml-4">
                    <p className="font-medium text-gray-600">Commande en préparation</p>
                    <p className="text-sm text-gray-500">Estimation: demain</p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="bg-gray-200 rounded-full h-5 w-5 flex-shrink-0 z-10 flex items-center justify-center">
                    <Package className="h-3 w-3 text-gray-600" />
                  </div>
                  <div className="ml-4">
                    <p className="font-medium text-gray-600">Commande expédiée</p>
                    <p className="text-sm text-gray-500">En attente</p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="bg-gray-200 rounded-full h-5 w-5 flex-shrink-0 z-10 flex items-center justify-center">
                    <Truck className="h-3 w-3 text-gray-600" />
                  </div>
                  <div className="ml-4">
                    <p className="font-medium text-gray-600">En cours de livraison</p>
                    <p className="text-sm text-gray-500">En attente</p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="bg-gray-200 rounded-full h-5 w-5 flex-shrink-0 z-10 flex items-center justify-center">
                    <Check className="h-3 w-3 text-gray-600" />
                  </div>
                  <div className="ml-4">
                    <p className="font-medium text-gray-600">Commande livrée</p>
                    <p className="text-sm text-gray-500">En attente</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Bottom actions */}
          <div className="flex flex-wrap gap-4">
            <Button asChild variant="outline">
              <Link to="/account/orders">
                Voir mes commandes
              </Link>
            </Button>
            <Button asChild>
              <Link to="/products">
                Continuer mes achats
                <ArrowRight size={16} className="ml-2" />
              </Link>
            </Button>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default OrderConfirmation;
