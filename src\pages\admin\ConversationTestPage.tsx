import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ConversationFlowTest from '@/components/messaging/ConversationFlowTest';
import RouteTest from '@/components/test/RouteTest';



const ConversationTestPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Test du système de conversation</h1>
            <p className="text-gray-600 mt-2">
              Testez le flux complet de création de conversation et de notification client
            </p>
          </div>
          
          <div className="space-y-8">
            <ConversationFlowTest />



            <div className="border-t pt-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Test des routes</h2>
              <p className="text-gray-600 mb-6">
                Vérifiez que toutes les routes de conversation fonctionnent correctement
              </p>
              <RouteTest />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ConversationTestPage;
