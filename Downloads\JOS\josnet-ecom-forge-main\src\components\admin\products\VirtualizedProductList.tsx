import React, { useState, useEffect } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Edit, 
  Trash2, 
  MoreVertical, 
  Eye, 
  Copy, 
  Archive, 
  CheckCircle2, 
  XCircle, 
  Tag 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';
import { ProductListItem } from '@/services/productApi';

interface VirtualizedProductListProps {
  products: ProductListItem[];
  isLoading?: boolean;
  onEdit?: (product: ProductListItem) => void;
  onDelete?: (product: ProductListItem) => void;
  onView?: (product: ProductListItem) => void;
  onDuplicate?: (product: ProductListItem) => void;
  onArchive?: (product: ProductListItem) => void;
  onSelect?: (productIds: number[]) => void;
  selectable?: boolean;
}

const VirtualizedProductList: React.FC<VirtualizedProductListProps> = ({
  products,
  isLoading = false,
  onEdit,
  onDelete,
  onView,
  onDuplicate,
  onArchive,
  onSelect,
  selectable = false,
}) => {
  const [selectedProducts, setSelectedProducts] = useState<number[]>([]);
  const [parentRef, setParentRef] = useState<HTMLDivElement | null>(null);
  
  // Reset selection when products change
  useEffect(() => {
    setSelectedProducts([]);
  }, [products]);
  
  // Notify parent component when selection changes
  useEffect(() => {
    if (onSelect) {
      onSelect(selectedProducts);
    }
  }, [selectedProducts, onSelect]);
  
  // Set up virtualizer
  const rowVirtualizer = useVirtualizer({
    count: isLoading ? 10 : products.length,
    getScrollElement: () => parentRef,
    estimateSize: () => 80, // estimated row height
    overscan: 5,
  });
  
  // Toggle selection of a product
  const toggleProductSelection = (productId: number) => {
    setSelectedProducts(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  };
  
  // Toggle selection of all products
  const toggleSelectAll = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(p => p.id));
    }
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return (
          <Badge className="bg-green-500 hover:bg-green-600">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            Publié
          </Badge>
        );
      case 'draft':
        return (
          <Badge variant="outline" className="text-amber-500 border-amber-200 bg-amber-50">
            <Tag className="mr-1 h-3 w-3" />
            Brouillon
          </Badge>
        );
      case 'archived':
        return (
          <Badge variant="outline" className="text-gray-500 border-gray-200 bg-gray-50">
            <Archive className="mr-1 h-3 w-3" />
            Archivé
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">{status}</Badge>
        );
    }
  };
  
  return (
    <Card>
      <CardHeader className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Produits</CardTitle>
            <CardDescription>
              {isLoading 
                ? 'Chargement des produits...' 
                : `${products.length} produits au total`}
            </CardDescription>
          </div>
          {selectable && selectedProducts.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {selectedProducts.length} produit{selectedProducts.length > 1 ? 's' : ''} sélectionné{selectedProducts.length > 1 ? 's' : ''}
              </span>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setSelectedProducts([])}
              >
                Annuler
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm">Actions</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions groupées</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onArchive && selectedProducts.forEach(id => {
                    const product = products.find(p => p.id === id);
                    if (product) onArchive(product);
                  })}>
                    <Archive className="mr-2 h-4 w-4" />
                    <span>Archiver</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onDelete && selectedProducts.forEach(id => {
                      const product = products.find(p => p.id === id);
                      if (product) onDelete(product);
                    })}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    <span>Supprimer</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </CardHeader>
      
      <div 
        ref={setParentRef} 
        className="relative overflow-auto border-t"
        style={{ height: '600px' }}
      >
        {/* Header */}
        <div className="sticky top-0 z-10 bg-gray-50 border-b">
          <div className="grid grid-cols-12 gap-4 px-6 py-3 text-sm font-medium text-gray-500">
            {selectable && (
              <div className="col-span-1 flex items-center">
                <Checkbox 
                  checked={selectedProducts.length === products.length && products.length > 0}
                  onCheckedChange={toggleSelectAll}
                />
              </div>
            )}
            <div className={`${selectable ? 'col-span-5' : 'col-span-6'}`}>Produit</div>
            <div className="col-span-2">Prix</div>
            <div className="col-span-2">Statut</div>
            <div className="col-span-2">Stock</div>
            <div className="col-span-1 text-right">Actions</div>
          </div>
        </div>
        
        {/* Virtualized list */}
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {rowVirtualizer.getVirtualItems().map((virtualRow) => {
            const product = isLoading ? null : products[virtualRow.index];
            
            return (
              <div
                key={virtualRow.index}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: `${virtualRow.size}px`,
                  transform: `translateY(${virtualRow.start}px)`,
                }}
              >
                {isLoading ? (
                  <div className="grid grid-cols-12 gap-4 px-6 py-4 border-b">
                    <Skeleton className="h-12 col-span-6" />
                    <Skeleton className="h-12 col-span-2" />
                    <Skeleton className="h-12 col-span-2" />
                    <Skeleton className="h-12 col-span-2" />
                  </div>
                ) : product ? (
                  <div className="grid grid-cols-12 gap-4 px-6 py-4 border-b hover:bg-gray-50">
                    {selectable && (
                      <div className="col-span-1 flex items-center">
                        <Checkbox 
                          checked={selectedProducts.includes(product.id)}
                          onCheckedChange={() => toggleProductSelection(product.id)}
                        />
                      </div>
                    )}
                    <div className={`${selectable ? 'col-span-5' : 'col-span-6'} flex items-center gap-3`}>
                      <div className="w-12 h-12 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
                        {product.primary_image ? (
                          <img 
                            src={product.primary_image.image} 
                            alt={product.name} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            <Tag className="h-6 w-6" />
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">SKU: {product.sku}</div>
                      </div>
                    </div>
                    <div className="col-span-2 flex flex-col justify-center">
                      <div className="font-medium">{formatPrice(product.current_price)}</div>
                      {product.sale_price && product.sale_price < product.price && (
                        <div className="text-sm text-gray-500 line-through">
                          {formatPrice(product.price)}
                        </div>
                      )}
                    </div>
                    <div className="col-span-2 flex items-center">
                      {getStatusBadge(product.status)}
                    </div>
                    <div className="col-span-2 flex items-center">
                      {product.inventory ? (
                        <div>
                          {product.inventory.is_in_stock ? (
                            <div className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                              <span>{product.inventory.available_quantity} en stock</span>
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                              <span>Rupture de stock</span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500">Non géré</span>
                      )}
                    </div>
                    <div className="col-span-1 flex justify-end">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          {onView && (
                            <DropdownMenuItem onClick={() => onView(product)}>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>Voir</span>
                            </DropdownMenuItem>
                          )}
                          {onEdit && (
                            <DropdownMenuItem onClick={() => onEdit(product)}>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Modifier</span>
                            </DropdownMenuItem>
                          )}
                          {onDuplicate && (
                            <DropdownMenuItem onClick={() => onDuplicate(product)}>
                              <Copy className="mr-2 h-4 w-4" />
                              <span>Dupliquer</span>
                            </DropdownMenuItem>
                          )}
                          {onArchive && (
                            <DropdownMenuItem onClick={() => onArchive(product)}>
                              <Archive className="mr-2 h-4 w-4" />
                              <span>Archiver</span>
                            </DropdownMenuItem>
                          )}
                          {onDelete && (
                            <DropdownMenuItem 
                              onClick={() => onDelete(product)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>Supprimer</span>
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ) : null}
              </div>
            );
          })}
        </div>
      </div>
      
      <CardFooter className="flex justify-between px-6 py-4 border-t">
        <div className="text-sm text-gray-500">
          {isLoading 
            ? 'Chargement...' 
            : `Affichage de ${products.length} produit${products.length !== 1 ? 's' : ''}`}
        </div>
      </CardFooter>
    </Card>
  );
};

export default VirtualizedProductList;
