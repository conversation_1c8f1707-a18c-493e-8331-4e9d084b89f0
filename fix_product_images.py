#!/usr/bin/env python3
"""
Script pour corriger les images des produits
"""

import sys
import os
import shutil
from pathlib import Path

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from products.models import Product, ProductImage
from django.core.files.base import ContentFile
from django.conf import settings

def create_placeholder_image():
    """Créer une image placeholder si elle n'existe pas"""
    print("🖼️ Création d'une image placeholder...")
    
    media_root = Path(settings.MEDIA_ROOT)
    products_dir = media_root / 'products'
    products_dir.mkdir(parents=True, exist_ok=True)
    
    # Créer un fichier placeholder simple
    placeholder_path = products_dir / 'placeholder.jpg'
    
    if not placeholder_path.exists():
        # Copier l'image placeholder du dossier public si elle existe
        public_placeholder = Path('public/placeholder-product.jpg')
        if public_placeholder.exists():
            shutil.copy2(public_placeholder, placeholder_path)
            print(f"   ✅ Image placeholder copiée: {placeholder_path}")
        else:
            # Créer un fichier vide comme placeholder
            placeholder_path.touch()
            print(f"   ✅ Fichier placeholder créé: {placeholder_path}")
    else:
        print(f"   ✅ Image placeholder existe déjà: {placeholder_path}")
    
    return str(placeholder_path.relative_to(media_root))

def fix_product_images():
    """Corriger les images des produits"""
    print("🔧 Correction des images des produits...")
    
    try:
        # Créer l'image placeholder
        placeholder_path = create_placeholder_image()
        
        # Récupérer tous les produits
        products = Product.objects.all()
        print(f"   📦 {products.count()} produits trouvés")
        
        fixed_count = 0
        
        for product in products:
            print(f"\n   📝 Produit: {product.name}")
            
            # Vérifier les images du produit
            product_images = ProductImage.objects.filter(product=product)
            if not product_images.exists():
                print(f"      ⚠️ Pas d'images pour ce produit")
                # Créer une image par défaut
                ProductImage.objects.create(
                    product=product,
                    image=placeholder_path,
                    is_primary=True,
                    alt_text=f"Image de {product.name}"
                )
                print(f"      ✅ Image placeholder créée")
                fixed_count += 1
            else:
                print(f"      📷 {product_images.count()} images trouvées")
            
            # Vérifier chaque image du produit
            for img in product_images:
                if img.image:
                    img_path = Path(settings.MEDIA_ROOT) / str(img.image)
                    if not img_path.exists():
                        print(f"      ❌ Image manquante: {img.image}")
                        img.image = placeholder_path
                        img.save()
                        print(f"      ✅ Image corrigée avec placeholder")
                        fixed_count += 1
                    else:
                        print(f"      ✅ Image OK: {img.image}")
        
        print(f"\n   📊 {fixed_count} images corrigées")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_image_access():
    """Tester l'accès aux images via l'API"""
    print(f"\n🧪 Test d'accès aux images...")
    
    import requests
    
    try:
        # Tester l'API des produits
        response = requests.get("http://localhost:8000/api/v1/products/products/1/")
        
        if response.status_code == 200:
            product = response.json()
            print(f"   ✅ Produit récupéré: {product.get('name')}")
            
            # Tester l'accès à l'image
            image_url = product.get('image')
            if image_url:
                if image_url.startswith('http'):
                    full_url = image_url
                else:
                    full_url = f"http://localhost:8000{image_url}"
                
                print(f"   🔗 URL image: {full_url}")
                
                img_response = requests.get(full_url)
                if img_response.status_code == 200:
                    print(f"   ✅ Image accessible")
                else:
                    print(f"   ❌ Image non accessible: {img_response.status_code}")
            else:
                print(f"   ⚠️ Pas d'URL d'image dans la réponse")
            
            return True
        else:
            print(f"   ❌ Erreur API: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🖼️ CORRECTION DES IMAGES DES PRODUITS")
    print("=" * 50)
    
    # 1. Corriger les images des produits
    images_fixed = fix_product_images()
    
    # 2. Tester l'accès aux images
    access_ok = test_image_access()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Images corrigées: {'✅' if images_fixed else '❌'}")
    print(f"   Accès aux images: {'✅' if access_ok else '❌'}")
    
    if images_fixed and access_ok:
        print(f"\n🎉 IMAGES CORRIGÉES!")
        print(f"   ✅ Les images manquantes ont été remplacées par des placeholders")
        print(f"   ✅ L'accès aux images fonctionne")
        print(f"   🌐 Testez la page: http://localhost:8080/product/1")
    else:
        print(f"\n❌ PROBLÈMES DÉTECTÉS")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
