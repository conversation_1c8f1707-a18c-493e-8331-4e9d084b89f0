import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Search, 
  Plus, 
  X, 
  AlertCircle, 
  RefreshCw, 
  MoveHorizontal, 
  ArrowUpDown 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';
import { productApi, ProductListItem } from '@/services/productApi';

// Types
interface RelatedProduct {
  id: number;
  product_id: number;
  related_product_id: number;
  relation_type: 'related' | 'upsell' | 'cross_sell' | 'accessory';
  position: number;
}

// Mock API service (replace with actual API calls)
const relatedProductsApi = {
  getRelatedProducts: async (productId: number): Promise<RelatedProduct[]> => {
    // Simulate API call
    return [
      {
        id: 1,
        product_id: productId,
        related_product_id: 2,
        relation_type: 'related',
        position: 1
      },
      {
        id: 2,
        product_id: productId,
        related_product_id: 3,
        relation_type: 'upsell',
        position: 1
      },
      {
        id: 3,
        product_id: productId,
        related_product_id: 4,
        relation_type: 'cross_sell',
        position: 1
      }
    ];
  },
  addRelatedProduct: async (data: Omit<RelatedProduct, 'id'>): Promise<RelatedProduct> => {
    // Simulate API call
    return {
      id: Math.floor(Math.random() * 1000) + 10,
      ...data
    };
  },
  removeRelatedProduct: async (id: number): Promise<void> => {
    // Simulate API call
    return Promise.resolve();
  },
  updateRelatedProductPositions: async (updates: { id: number, position: number }[]): Promise<void> => {
    // Simulate API call
    return Promise.resolve();
  }
};

interface RelatedProductsManagerProps {
  productId: number;
}

const RelatedProductsManager: React.FC<RelatedProductsManagerProps> = ({ productId }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRelationType, setSelectedRelationType] = useState<'related' | 'upsell' | 'cross_sell' | 'accessory'>('related');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch related products
  const { 
    data: relatedProducts = [], 
    isLoading: isLoadingRelated, 
    isError: isRelatedError 
  } = useQuery({
    queryKey: ['relatedProducts', productId],
    queryFn: () => relatedProductsApi.getRelatedProducts(productId),
  });
  
  // Fetch all products for selection
  const { 
    data: allProducts = [], 
    isLoading: isLoadingProducts 
  } = useQuery({
    queryKey: ['products'],
    queryFn: () => productApi.getProducts(),
  });
  
  // Add related product mutation
  const addRelatedProductMutation = useMutation({
    mutationFn: (data: Omit<RelatedProduct, 'id'>) => 
      relatedProductsApi.addRelatedProduct(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['relatedProducts', productId] });
      toast({
        title: 'Produit ajouté',
        description: 'Le produit a été ajouté avec succès.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de l\'ajout du produit.',
        variant: 'destructive',
      });
    },
  });
  
  // Remove related product mutation
  const removeRelatedProductMutation = useMutation({
    mutationFn: (id: number) => relatedProductsApi.removeRelatedProduct(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['relatedProducts', productId] });
      toast({
        title: 'Produit retiré',
        description: 'Le produit a été retiré avec succès.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors du retrait du produit.',
        variant: 'destructive',
      });
    },
  });
  
  // Update positions mutation
  const updatePositionsMutation = useMutation({
    mutationFn: (updates: { id: number, position: number }[]) => 
      relatedProductsApi.updateRelatedProductPositions(updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['relatedProducts', productId] });
      toast({
        title: 'Positions mises à jour',
        description: 'L\'ordre des produits a été mis à jour avec succès.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de la mise à jour des positions.',
        variant: 'destructive',
      });
    },
  });
  
  // Filter products by search term
  const filteredProducts = Array.isArray(allProducts) ? allProducts.filter(product => 
    product.id !== productId && // Exclude current product
    !relatedProducts.some(rp => rp.related_product_id === product.id) && // Exclude already related products
    (
      searchTerm === '' || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase())
    )
  ) : [];
  
  // Group related products by type
  const relatedByType = relatedProducts.reduce((acc, rel) => {
    if (!acc[rel.relation_type]) {
      acc[rel.relation_type] = [];
    }
    acc[rel.relation_type].push(rel);
    return acc;
  }, {} as Record<string, RelatedProduct[]>);
  
  // Get product details by ID
  const getProductDetails = (productId: number): ProductListItem | undefined => {
    return Array.isArray(allProducts) ? allProducts.find(p => p.id === productId) : undefined;
  };
  
  // Handle adding a related product
  const handleAddRelatedProduct = (relatedProductId: number) => {
    // Find max position for this relation type
    const maxPosition = relatedByType[selectedRelationType]?.reduce(
      (max, rel) => Math.max(max, rel.position), 0
    ) || 0;
    
    addRelatedProductMutation.mutate({
      product_id: productId,
      related_product_id: relatedProductId,
      relation_type: selectedRelationType,
      position: maxPosition + 1
    });
  };
  
  // Handle removing a related product
  const handleRemoveRelatedProduct = (relationId: number) => {
    removeRelatedProductMutation.mutate(relationId);
  };
  
  // Handle moving a product up or down in the list
  const handleMoveProduct = (relationId: number, direction: 'up' | 'down') => {
    const relationType = relatedProducts.find(rp => rp.id === relationId)?.relation_type;
    if (!relationType) return;
    
    const typeProducts = [...(relatedByType[relationType] || [])].sort((a, b) => a.position - b.position);
    const index = typeProducts.findIndex(rp => rp.id === relationId);
    
    if (index === -1) return;
    
    if (direction === 'up' && index > 0) {
      // Swap with previous item
      const updates = [
        { id: typeProducts[index].id, position: typeProducts[index - 1].position },
        { id: typeProducts[index - 1].id, position: typeProducts[index].position }
      ];
      updatePositionsMutation.mutate(updates);
    } else if (direction === 'down' && index < typeProducts.length - 1) {
      // Swap with next item
      const updates = [
        { id: typeProducts[index].id, position: typeProducts[index + 1].position },
        { id: typeProducts[index + 1].id, position: typeProducts[index].position }
      ];
      updatePositionsMutation.mutate(updates);
    }
  };
  
  // Get relation type label
  const getRelationTypeLabel = (type: string): string => {
    switch (type) {
      case 'related':
        return 'Produits similaires';
      case 'upsell':
        return 'Produits supérieurs (upsell)';
      case 'cross_sell':
        return 'Ventes croisées';
      case 'accessory':
        return 'Accessoires';
      default:
        return type;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Produits associés</CardTitle>
        <CardDescription>
          Gérez les produits associés, les upsells, les ventes croisées et les accessoires
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="related">
          <TabsList className="mb-4">
            <TabsTrigger value="related">Similaires</TabsTrigger>
            <TabsTrigger value="upsell">Upsell</TabsTrigger>
            <TabsTrigger value="cross_sell">Ventes croisées</TabsTrigger>
            <TabsTrigger value="accessory">Accessoires</TabsTrigger>
          </TabsList>
          
          {['related', 'upsell', 'cross_sell', 'accessory'].map((type) => (
            <TabsContent key={type} value={type} className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="text-sm font-medium mb-1">{getRelationTypeLabel(type)}</h3>
                <p className="text-sm text-gray-500">
                  {type === 'related' && 'Produits similaires qui peuvent intéresser les clients.'}
                  {type === 'upsell' && 'Produits de gamme supérieure que les clients pourraient préférer.'}
                  {type === 'cross_sell' && 'Produits complémentaires qui se vendent bien ensemble.'}
                  {type === 'accessory' && 'Accessoires ou pièces qui fonctionnent avec ce produit.'}
                </p>
              </div>
              
              {/* Current related products of this type */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Produits actuellement associés</h3>
                
                {isLoadingRelated ? (
                  <div className="space-y-2">
                    <Skeleton className="h-16 w-full" />
                    <Skeleton className="h-16 w-full" />
                  </div>
                ) : isRelatedError ? (
                  <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
                    <AlertCircle className="h-5 w-5" />
                    <span>Une erreur est survenue lors du chargement des produits associés.</span>
                  </div>
                ) : !relatedByType[type] || relatedByType[type].length === 0 ? (
                  <div className="text-center py-4 text-gray-500 border border-dashed rounded-md">
                    Aucun produit associé de ce type.
                  </div>
                ) : (
                  <div className="space-y-2">
                    {[...relatedByType[type]]
                      .sort((a, b) => a.position - b.position)
                      .map((relation) => {
                        const product = getProductDetails(relation.related_product_id);
                        if (!product) return null;
                        
                        return (
                          <div 
                            key={relation.id} 
                            className="flex items-center justify-between p-3 bg-white border rounded-md"
                          >
                            <div className="flex items-center gap-3">
                              {product.primary_image ? (
                                <img 
                                  src={product.primary_image.image} 
                                  alt={product.name} 
                                  className="w-12 h-12 object-cover rounded-md"
                                />
                              ) : (
                                <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center text-gray-400">
                                  <MoveHorizontal className="h-6 w-6" />
                                </div>
                              )}
                              <div>
                                <div className="font-medium">{product.name}</div>
                                <div className="text-sm text-gray-500">
                                  {product.sku} • {formatPrice(product.current_price)}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleMoveProduct(relation.id, 'up')}
                                disabled={
                                  relatedByType[type].length <= 1 || 
                                  relation.position === Math.min(...relatedByType[type].map(r => r.position))
                                }
                              >
                                <ArrowUpDown className="h-4 w-4 rotate-90" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveRelatedProduct(relation.id)}
                                className="text-red-500"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
              
              {/* Add new related products */}
              <div className="space-y-2 pt-4 border-t">
                <h3 className="text-sm font-medium">Ajouter des produits</h3>
                
                <div className="flex gap-2 mb-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      type="search"
                      placeholder="Rechercher des produits..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select
                    value={selectedRelationType}
                    onValueChange={(value: any) => setSelectedRelationType(value)}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Type de relation" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="related">Similaires</SelectItem>
                      <SelectItem value="upsell">Upsell</SelectItem>
                      <SelectItem value="cross_sell">Ventes croisées</SelectItem>
                      <SelectItem value="accessory">Accessoires</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {isLoadingProducts ? (
                  <div className="space-y-2">
                    <Skeleton className="h-16 w-full" />
                    <Skeleton className="h-16 w-full" />
                  </div>
                ) : filteredProducts.length === 0 ? (
                  <div className="text-center py-4 text-gray-500 border border-dashed rounded-md">
                    Aucun produit trouvé. Essayez une autre recherche.
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-[300px] overflow-y-auto p-1">
                    {filteredProducts.slice(0, 10).map((product) => (
                      <div 
                        key={product.id} 
                        className="flex items-center justify-between p-3 bg-white border rounded-md"
                      >
                        <div className="flex items-center gap-3">
                          {product.primary_image ? (
                            <img 
                              src={product.primary_image.image} 
                              alt={product.name} 
                              className="w-10 h-10 object-cover rounded-md"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-gray-100 rounded-md" />
                          )}
                          <div>
                            <div className="font-medium truncate max-w-[150px]">{product.name}</div>
                            <div className="text-sm text-gray-500">{formatPrice(product.current_price)}</div>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleAddRelatedProduct(product.id)}
                          disabled={addRelatedProductMutation.isPending}
                        >
                          {addRelatedProductMutation.isPending ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Plus className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default RelatedProductsManager;
