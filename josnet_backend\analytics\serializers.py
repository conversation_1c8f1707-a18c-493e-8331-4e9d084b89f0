from rest_framework import serializers
from .models import (
    ProductView, CartAction, WishlistAction, SalesData, SystemLog,
    HomePageContent, HomePageStats, HomePageService, HomePageTestimonial, HomePageFeature
)

class ProductViewSerializer(serializers.ModelSerializer):
    """Serializer for the ProductView model."""

    class Meta:
        model = ProductView
        fields = [
            'id', 'product', 'user', 'session_id', 'ip_address',
            'user_agent', 'referrer', 'device_type', 'created_at'
        ]
        read_only_fields = ['created_at']

class CartActionSerializer(serializers.ModelSerializer):
    """Serializer for the CartAction model."""

    class Meta:
        model = CartAction
        fields = [
            'id', 'product', 'variant', 'user', 'session_id',
            'action', 'quantity', 'created_at'
        ]
        read_only_fields = ['created_at']

class WishlistActionSerializer(serializers.ModelSerializer):
    """Serializer for the WishlistAction model."""

    class Meta:
        model = WishlistAction
        fields = [
            'id', 'product', 'user', 'action', 'created_at'
        ]
        read_only_fields = ['created_at']

class SalesDataSerializer(serializers.ModelSerializer):
    """Serializer for the SalesData model."""

    class Meta:
        model = SalesData
        fields = [
            'id', 'product', 'variant', 'order_id', 'customer_id',
            'quantity', 'unit_price', 'total_price', 'date', 'created_at'
        ]
        read_only_fields = ['created_at']

class ProductSalesStatsSerializer(serializers.Serializer):
    """Serializer for product sales statistics."""

    product_id = serializers.IntegerField()
    product_name = serializers.CharField()
    total_orders = serializers.IntegerField()
    total_quantity_sold = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    last_order_date = serializers.DateTimeField(allow_null=True)
    sales_trend = serializers.FloatField()
    sales_by_period = serializers.ListField(child=serializers.DictField())
    sales_by_variant = serializers.ListField(child=serializers.DictField(), required=False)
    top_customers = serializers.ListField(child=serializers.DictField())
    related_products_sales = serializers.ListField(child=serializers.DictField())

class CustomerProductInsightSerializer(serializers.Serializer):
    """Serializer for customer insights about a product."""

    product_id = serializers.IntegerField()
    product_name = serializers.CharField()
    views_count = serializers.IntegerField()
    unique_viewers_count = serializers.IntegerField()
    add_to_cart_count = serializers.IntegerField()
    add_to_wishlist_count = serializers.IntegerField()
    purchase_count = serializers.IntegerField()
    conversion_rate = serializers.FloatField()
    cart_abandonment_rate = serializers.FloatField()
    average_rating = serializers.FloatField()
    reviews_count = serializers.IntegerField()
    demographic_data = serializers.DictField()
    customer_segments = serializers.ListField(child=serializers.DictField())
    similar_interests = serializers.ListField(child=serializers.DictField())

class ProductDashboardDataSerializer(serializers.Serializer):
    """Serializer for product dashboard data."""

    metrics = serializers.DictField()
    sales_over_time = serializers.ListField(child=serializers.DictField())
    views_over_time = serializers.ListField(child=serializers.DictField())
    conversion_over_time = serializers.ListField(child=serializers.DictField())
    funnel_data = serializers.ListField(child=serializers.DictField())
    top_referrers = serializers.ListField(child=serializers.DictField())
    device_breakdown = serializers.ListField(child=serializers.DictField())

class SystemLogSerializer(serializers.ModelSerializer):
    """Serializer for the SystemLog model."""

    user_display = serializers.SerializerMethodField()
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    module_display = serializers.CharField(source='get_module_display', read_only=True)
    formatted_timestamp = serializers.SerializerMethodField()

    class Meta:
        model = SystemLog
        fields = [
            'id', 'log_id', 'timestamp', 'formatted_timestamp', 'type', 'type_display',
            'module', 'module_display', 'message', 'ip_address', 'user', 'user_email',
            'user_display', 'additional_data'
        ]
        read_only_fields = ['id', 'log_id', 'timestamp']

    def get_user_display(self, obj):
        """Get user display name."""
        if obj.user:
            return obj.user.email
        elif obj.user_email:
            return obj.user_email
        return "Système"

    def get_formatted_timestamp(self, obj):
        """Get formatted timestamp."""
        return obj.timestamp.strftime('%d/%m/%Y %H:%M:%S')


class HomePageContentSerializer(serializers.ModelSerializer):
    """Serializer for the HomePageContent model."""

    section_display = serializers.CharField(source='get_section_display', read_only=True)

    class Meta:
        model = HomePageContent
        fields = [
            'id', 'section', 'section_display', 'title', 'subtitle', 'description',
            'button_text', 'button_link', 'image_url', 'background_color', 'text_color',
            'is_active', 'order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class HomePageStatsSerializer(serializers.ModelSerializer):
    """Serializer for the HomePageStats model."""

    display_value = serializers.CharField(source='get_display_value', read_only=True)

    class Meta:
        model = HomePageStats
        fields = [
            'id', 'name', 'display_name', 'value', 'suffix', 'display_value',
            'icon', 'description', 'is_active', 'order', 'auto_update',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class HomePageServiceSerializer(serializers.ModelSerializer):
    """Serializer for the HomePageService model."""

    class Meta:
        model = HomePageService
        fields = [
            'id', 'name', 'title', 'description', 'icon', 'color', 'link',
            'is_active', 'order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class HomePageTestimonialSerializer(serializers.ModelSerializer):
    """Serializer for the HomePageTestimonial model."""

    class Meta:
        model = HomePageTestimonial
        fields = [
            'id', 'client_name', 'client_title', 'client_company', 'testimonial',
            'rating', 'client_image', 'is_featured', 'is_active', 'order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class HomePageFeatureSerializer(serializers.ModelSerializer):
    """Serializer for the HomePageFeature model."""

    class Meta:
        model = HomePageFeature
        fields = [
            'id', 'title', 'description', 'icon', 'color', 'is_active', 'order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class HomePageDataSerializer(serializers.Serializer):
    """Serializer for complete home page data."""

    content = HomePageContentSerializer(many=True)
    stats = HomePageStatsSerializer(many=True)
    services = HomePageServiceSerializer(many=True)
    testimonials = HomePageTestimonialSerializer(many=True)
    features = HomePageFeatureSerializer(many=True)
