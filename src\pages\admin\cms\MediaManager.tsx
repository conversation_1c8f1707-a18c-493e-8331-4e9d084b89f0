import { useState, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AdminLayout from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Loader2,
  Search,
  Plus,
  Image,
  FileText,
  Video,
  Music,
  File,
  X,
  Copy,
  Info,
  Trash2,
  Upload,
  RefreshCw,
  AlertCircle,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import cmsApi, { Media } from "@/services/cmsApi";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { formatBytes } from "@/lib/utils";

// Schéma de validation pour le formulaire d'upload
const uploadSchema = z.object({
  title: z.string().min(1, "Le titre est requis"),
  file_type: z.string().min(1, "Le type de fichier est requis"),
  description: z.string().optional(),
  alt_text: z.string().optional(),
});

type UploadFormValues = z.infer<typeof uploadSchema>;

const MediaManager = () => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMedia, setSelectedMedia] = useState<Media | null>(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Récupérer la liste des médias
  const {
    data: mediaItems,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["media"],
    queryFn: cmsApi.getMedia,
  });

  // Formulaire d'upload
  const form = useForm<UploadFormValues>({
    resolver: zodResolver(uploadSchema),
    defaultValues: {
      title: "",
      file_type: "",
      description: "",
      alt_text: "",
    },
  });

  // Mutation pour uploader un média
  const uploadMedia = useMutation({
    mutationFn: (data: FormData) => cmsApi.uploadMedia(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["media"] });
      toast({
        title: "Média uploadé",
        description: "Le fichier a été uploadé avec succès",
      });
      setIsUploadDialogOpen(false);
      form.reset();
      setUploadFile(null);
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible d'uploader le fichier",
        variant: "destructive",
      });
    },
  });

  // Mutation pour supprimer un média
  const deleteMedia = useMutation({
    mutationFn: (id: number) => cmsApi.deleteMedia(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["media"] });
      toast({
        title: "Média supprimé",
        description: "Le fichier a été supprimé avec succès",
      });
      setIsDetailsDialogOpen(false);
      setSelectedMedia(null);
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le fichier",
        variant: "destructive",
      });
    },
  });

  // Gérer la soumission du formulaire d'upload
  const onSubmit = (data: UploadFormValues) => {
    if (!uploadFile) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un fichier",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    formData.append("title", data.title);
    formData.append("file_type", data.file_type);
    formData.append("file", uploadFile);

    if (data.description) {
      formData.append("description", data.description);
    }

    if (data.alt_text) {
      formData.append("alt_text", data.alt_text);
    }

    uploadMedia.mutate(formData);
  };

  // Gérer la sélection de fichier
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setUploadFile(file);

      // Déterminer automatiquement le type de fichier
      let fileType = "other";
      if (file.type.startsWith("image/")) {
        fileType = "image";
      } else if (file.type.startsWith("video/")) {
        fileType = "video";
      } else if (file.type.startsWith("audio/")) {
        fileType = "audio";
      } else if (file.type.includes("pdf") || file.type.includes("document") || file.type.includes("text")) {
        fileType = "document";
      }

      form.setValue("file_type", fileType);
      
      // Utiliser le nom du fichier comme titre par défaut
      const fileName = file.name.split(".")[0];
      form.setValue("title", fileName);
    }
  };

  // Filtrer les médias selon la recherche
  const filteredMedia = mediaItems?.data?.filter(
    (media) =>
      media.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      media.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      media.file_type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Copier l'URL du média dans le presse-papier
  const copyMediaUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    toast({
      title: "URL copiée",
      description: "L'URL du média a été copiée dans le presse-papier",
    });
  };

  // Afficher l'icône correspondant au type de média
  const getMediaIcon = (type: string) => {
    switch (type) {
      case "image":
        return <Image className="h-6 w-6" />;
      case "document":
        return <FileText className="h-6 w-6" />;
      case "video":
        return <Video className="h-6 w-6" />;
      case "audio":
        return <Music className="h-6 w-6" />;
      default:
        return <File className="h-6 w-6" />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Gestionnaire de médias</h1>
            <p className="text-gray-500">
              Gérez les images, documents et autres fichiers médias
            </p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
            <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-1">
                  <Plus size={16} />
                  Ajouter un média
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Uploader un nouveau média</DialogTitle>
                  <DialogDescription>
                    Sélectionnez un fichier et remplissez les informations
                  </DialogDescription>
                </DialogHeader>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="file">Fichier</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="file"
                          type="file"
                          ref={fileInputRef}
                          onChange={handleFileChange}
                          className="hidden"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="w-full"
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Sélectionner un fichier
                        </Button>
                        {uploadFile && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setUploadFile(null);
                              if (fileInputRef.current) {
                                fileInputRef.current.value = "";
                              }
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      {uploadFile && (
                        <p className="text-sm text-gray-500">
                          {uploadFile.name} ({formatBytes(uploadFile.size)})
                        </p>
                      )}
                    </div>

                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Titre</FormLabel>
                          <FormControl>
                            <Input placeholder="Titre du média" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="file_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Type de fichier</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Sélectionner un type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="image">Image</SelectItem>
                              <SelectItem value="document">Document</SelectItem>
                              <SelectItem value="video">Vidéo</SelectItem>
                              <SelectItem value="audio">Audio</SelectItem>
                              <SelectItem value="other">Autre</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Description du média"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="alt_text"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Texte alternatif</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Texte alternatif (pour les images)"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Utilisé pour l'accessibilité et le SEO
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <DialogFooter>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsUploadDialogOpen(false)}
                      >
                        Annuler
                      </Button>
                      <Button
                        type="submit"
                        disabled={uploadMedia.isPending || !uploadFile}
                      >
                        {uploadMedia.isPending ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Upload en cours...
                          </>
                        ) : (
                          "Uploader"
                        )}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : isError ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <AlertCircle className="h-8 w-8 text-destructive mb-2" />
              <p className="text-lg font-medium">
                Erreur lors du chargement des médias
              </p>
              <Button
                variant="outline"
                onClick={() => refetch()}
                className="mt-4"
              >
                Réessayer
              </Button>
            </CardContent>
          </Card>
        ) : (
          <>
            {filteredMedia && filteredMedia.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {filteredMedia.map((media) => (
                  <Card
                    key={media.id}
                    className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => {
                      setSelectedMedia(media);
                      setIsDetailsDialogOpen(true);
                    }}
                  >
                    <div className="aspect-square bg-muted flex items-center justify-center relative">
                      {media.file_type === "image" ? (
                        <img
                          src={media.file_url}
                          alt={media.alt_text || media.title}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="flex flex-col items-center justify-center p-4">
                          {getMediaIcon(media.file_type)}
                          <span className="text-xs mt-2 text-center">
                            {media.file_name}
                          </span>
                        </div>
                      )}
                    </div>
                    <CardContent className="p-3">
                      <h3 className="font-medium text-sm truncate">
                        {media.title}
                      </h3>
                      <p className="text-xs text-gray-500">
                        {formatBytes(media.file_size)}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <p className="text-lg font-medium">Aucun média trouvé</p>
                  <p className="text-gray-500">
                    {searchQuery
                      ? "Essayez une autre recherche"
                      : "Commencez par ajouter un média"}
                  </p>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>

      {/* Dialog de détails du média */}
      {selectedMedia && (
        <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Détails du média</DialogTitle>
            </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-muted rounded-md flex items-center justify-center p-4">
                {selectedMedia.file_type === "image" ? (
                  <img
                    src={selectedMedia.file_url}
                    alt={selectedMedia.alt_text || selectedMedia.title}
                    className="max-w-full max-h-[200px] object-contain"
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    {getMediaIcon(selectedMedia.file_type)}
                    <span className="text-sm mt-2 text-center">
                      {selectedMedia.file_name}
                    </span>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">Titre</h3>
                  <p>{selectedMedia.title}</p>
                </div>

                {selectedMedia.description && (
                  <div>
                    <h3 className="font-medium">Description</h3>
                    <p className="text-sm">{selectedMedia.description}</p>
                  </div>
                )}

                <div>
                  <h3 className="font-medium">Type</h3>
                  <p>{selectedMedia.file_type}</p>
                </div>

                <div>
                  <h3 className="font-medium">Taille</h3>
                  <p>{formatBytes(selectedMedia.file_size)}</p>
                </div>

                {selectedMedia.alt_text && (
                  <div>
                    <h3 className="font-medium">Texte alternatif</h3>
                    <p className="text-sm">{selectedMedia.alt_text}</p>
                  </div>
                )}

                <div>
                  <h3 className="font-medium">Date d'ajout</h3>
                  <p>
                    {new Date(selectedMedia.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4">
              <h3 className="font-medium mb-2">URL du fichier</h3>
              <div className="flex items-center gap-2">
                <Input
                  value={selectedMedia.file_url}
                  readOnly
                  className="font-mono text-xs"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyMediaUrl(selectedMedia.file_url)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <DialogFooter className="gap-2 sm:gap-0 mt-4">
              <Button
                variant="destructive"
                onClick={() => {
                  if (
                    window.confirm(
                      "Êtes-vous sûr de vouloir supprimer ce média ?"
                    )
                  ) {
                    deleteMedia.mutate(selectedMedia.id);
                  }
                }}
                disabled={deleteMedia.isPending}
              >
                {deleteMedia.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="mr-2 h-4 w-4" />
                )}
                Supprimer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </AdminLayout>
  );
};

export default MediaManager;
