#!/usr/bin/env python3
"""
Test de la correction de l'API account pour les retours et réclamations
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_return_details_access():
    """Tester l'accès aux détails des retours"""
    print("📦 TEST D'ACCÈS AUX DÉTAILS DES RETOURS")
    print("=" * 50)
    
    # Authentification admin
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            print("✅ Authentification admin réussie")
            
            # D'abord, récupérer la liste des retours via l'API admin
            admin_returns_response = requests.get(f"{API_BASE_URL}/orders/returns/", headers=headers)
            
            if admin_returns_response.status_code == 200:
                admin_returns = admin_returns_response.json().get('results', [])
                print(f"📊 Retours via API admin: {len(admin_returns)}")
                
                if admin_returns:
                    return_id = admin_returns[0]['id']
                    return_number = admin_returns[0]['return_number']
                    
                    print(f"🎯 Test avec retour: {return_number} (ID: {return_id})")
                    
                    # Tester l'accès via l'API account (celle qui causait l'erreur 404)
                    print(f"\n🔍 TEST API ACCOUNT:")
                    account_detail_response = requests.get(
                        f"{API_BASE_URL}/account/returns/{return_id}/",
                        headers=headers
                    )
                    
                    print(f"   📊 Statut: {account_detail_response.status_code}")
                    
                    if account_detail_response.status_code == 200:
                        account_detail = account_detail_response.json()
                        print(f"   ✅ Accès réussi via API account")
                        print(f"   📝 Numéro: {account_detail.get('return_number')}")
                        print(f"   👤 Utilisateur: {account_detail.get('user', {}).get('email', 'N/A')}")
                        print(f"   📊 Articles: {len(account_detail.get('items', []))}")
                        
                        # Comparer avec l'API admin
                        print(f"\n🔍 TEST API ADMIN:")
                        admin_detail_response = requests.get(
                            f"{API_BASE_URL}/orders/returns/{return_id}/",
                            headers=headers
                        )
                        
                        if admin_detail_response.status_code == 200:
                            admin_detail = admin_detail_response.json()
                            print(f"   ✅ Accès réussi via API admin")
                            print(f"   📝 Numéro: {admin_detail.get('return_number')}")
                            print(f"   👤 Utilisateur: {admin_detail.get('user', {}).get('email', 'N/A')}")
                            
                            # Vérifier la cohérence
                            if account_detail.get('return_number') == admin_detail.get('return_number'):
                                print(f"\n   ✅ COHÉRENCE CONFIRMÉE")
                                print(f"      Les deux APIs retournent les mêmes données")
                                return True
                            else:
                                print(f"\n   ⚠️ INCOHÉRENCE DÉTECTÉE")
                                print(f"      Les APIs retournent des données différentes")
                                return False
                        else:
                            print(f"   ❌ Erreur API admin: {admin_detail_response.status_code}")
                            return False
                    elif account_detail_response.status_code == 404:
                        print(f"   ❌ ERREUR 404 PERSISTANTE")
                        print(f"   📝 L'endpoint account n'est toujours pas accessible")
                        print(f"   🔍 Vérifiez les URLs et les permissions")
                        return False
                    else:
                        print(f"   ❌ Erreur: {account_detail_response.status_code}")
                        print(f"   📝 Réponse: {account_detail_response.text}")
                        return False
                else:
                    print(f"   ⚠️ Aucun retour trouvé pour tester")
                    return False
            else:
                print(f"   ❌ Erreur API admin: {admin_returns_response.status_code}")
                return False
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_claim_details_access():
    """Tester l'accès aux détails des réclamations"""
    print(f"\n🎫 TEST D'ACCÈS AUX DÉTAILS DES RÉCLAMATIONS")
    print("=" * 50)
    
    # Authentification admin
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Récupérer les réclamations via l'API admin
            admin_claims_response = requests.get(f"{API_BASE_URL}/orders/claims/", headers=headers)
            
            if admin_claims_response.status_code == 200:
                admin_claims = admin_claims_response.json().get('results', [])
                print(f"📊 Réclamations via API admin: {len(admin_claims)}")
                
                if admin_claims:
                    claim_id = admin_claims[0]['id']
                    claim_number = admin_claims[0]['claim_number']
                    
                    print(f"🎯 Test avec réclamation: {claim_number} (ID: {claim_id})")
                    
                    # Tester l'accès via l'API account
                    print(f"\n🔍 TEST API ACCOUNT:")
                    account_claim_response = requests.get(
                        f"{API_BASE_URL}/account/claims/{claim_id}/",
                        headers=headers
                    )
                    
                    print(f"   📊 Statut: {account_claim_response.status_code}")
                    
                    if account_claim_response.status_code == 200:
                        account_claim = account_claim_response.json()
                        print(f"   ✅ Accès réussi via API account")
                        print(f"   📝 Numéro: {account_claim.get('claim_number')}")
                        print(f"   👤 Utilisateur: {account_claim.get('user', {}).get('email', 'N/A')}")
                        print(f"   💬 Messages: {len(account_claim.get('messages', []))}")
                        return True
                    else:
                        print(f"   ❌ Erreur: {account_claim_response.status_code}")
                        print(f"   📝 Réponse: {account_claim_response.text}")
                        return False
                else:
                    print(f"   ⚠️ Aucune réclamation trouvée pour tester")
                    return False
            else:
                print(f"   ❌ Erreur API admin: {admin_claims_response.status_code}")
                return False
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_url_endpoints():
    """Tester les endpoints disponibles"""
    print(f"\n🔗 TEST DES ENDPOINTS DISPONIBLES")
    print("=" * 50)
    
    # Authentification admin
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            endpoints_to_test = [
                ("Account Returns List", f"{API_BASE_URL}/account/returns/"),
                ("Account Claims List", f"{API_BASE_URL}/account/claims/"),
                ("Admin Returns List", f"{API_BASE_URL}/orders/returns/"),
                ("Admin Claims List", f"{API_BASE_URL}/orders/claims/"),
            ]
            
            results = {}
            
            for name, url in endpoints_to_test:
                try:
                    response = requests.get(url, headers=headers)
                    status = response.status_code
                    results[name] = status
                    
                    icon = "✅" if status == 200 else "❌"
                    print(f"   {icon} {name}: {status}")
                    
                    if status == 200:
                        data = response.json()
                        if 'results' in data:
                            count = len(data['results'])
                            print(f"      📊 Éléments: {count}")
                        elif isinstance(data, list):
                            count = len(data)
                            print(f"      📊 Éléments: {count}")
                    
                except Exception as e:
                    results[name] = f"Error: {e}"
                    print(f"   ❌ {name}: Erreur - {e}")
            
            return all(isinstance(status, int) and status == 200 for status in results.values())
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    print("🔧 TEST DE LA CORRECTION DE L'API ACCOUNT")
    print("=" * 70)
    
    # 1. Tester les endpoints
    endpoints_ok = test_url_endpoints()
    
    # 2. Tester l'accès aux détails des retours
    returns_ok = test_return_details_access()
    
    # 3. Tester l'accès aux détails des réclamations
    claims_ok = test_claim_details_access()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Endpoints disponibles: {'✅' if endpoints_ok else '❌'}")
    print(f"   Détails retours: {'✅' if returns_ok else '❌'}")
    print(f"   Détails réclamations: {'✅' if claims_ok else '❌'}")
    
    if endpoints_ok and returns_ok and claims_ok:
        print(f"\n🎉 CORRECTION RÉUSSIE!")
        print(f"   ✅ L'erreur 404 est corrigée")
        print(f"   ✅ Les admins peuvent accéder aux détails")
        print(f"   ✅ Les APIs account et admin sont cohérentes")
        print(f"   ✅ Toutes les permissions fonctionnent")
        
        print(f"\n🌐 ENDPOINTS FONCTIONNELS:")
        print(f"   • /api/v1/account/returns/ (liste)")
        print(f"   • /api/v1/account/returns/{{id}}/ (détails)")
        print(f"   • /api/v1/account/claims/ (liste)")
        print(f"   • /api/v1/account/claims/{{id}}/ (détails)")
        
    elif endpoints_ok:
        print(f"\n✅ CORRECTION PARTIELLE")
        print(f"   Les endpoints de base fonctionnent")
        print(f"   Certains détails peuvent encore poser problème")
        
    else:
        print(f"\n❌ PROBLÈMES PERSISTANTS")
        print(f"   Vérifiez les URLs et les permissions")

if __name__ == "__main__":
    main()
