import React from 'react';
import { useParams, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import AccountLayout from '@/components/account/AccountLayout';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react';
import { ClaimDetails } from '@/components/account/claims/ClaimDetails';
import { accountApi } from '@/services/accountApi';

const ClaimDetail = () => {
  const { id } = useParams<{ id: string }>();
  
  // Récupérer les détails de la réclamation pour le titre
  const {
    data: claim,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['claim', id],
    queryFn: () => accountApi.getClaimDetails(id || ''),
    enabled: !!id
  });
  
  // Titre de la page
  const pageTitle = isLoading
    ? 'Chargement de la réclamation...'
    : isError
      ? 'Erreur de chargement'
      : claim
        ? `Réclamation #${claim.id}`
        : 'Détail de la réclamation';
  
  return (
    <AccountLayout title={pageTitle}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" asChild>
            <Link to="/account/returns" className="flex items-center gap-1">
              <ArrowLeft className="h-4 w-4" />
              Retour aux réclamations
            </Link>
          </Button>
        </div>
        
        {!id ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Réclamation non trouvée</h3>
            <p className="text-gray-500 max-w-sm mb-4">
              L'identifiant de la réclamation est manquant ou invalide.
            </p>
            <Button asChild>
              <Link to="/account/returns">Voir toutes les réclamations</Link>
            </Button>
          </div>
        ) : (
          <ClaimDetails claimId={id} onRefresh={refetch} />
        )}
      </div>
    </AccountLayout>
  );
};

export default ClaimDetail;
