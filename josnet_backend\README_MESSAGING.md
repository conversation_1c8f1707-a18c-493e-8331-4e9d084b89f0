# Guide de dépannage du module de messagerie

Ce document fournit des instructions pour résoudre les problèmes courants avec le module de messagerie.

## Problèmes connus

1. **Erreurs 500 sur les API de messagerie** : Les API de messagerie peuvent renvoyer des erreurs 500 en raison de problèmes avec la base de données ou les relations entre modèles.

2. **Messages non visibles** : Les messages envoyés par les clients peuvent ne pas apparaître dans l'interface administrateur en raison de problèmes de synchronisation ou d'erreurs d'API.

## Solutions

### 1. Utiliser les API v2 (plus robustes)

Nous avons créé une version améliorée des API de messagerie avec une meilleure gestion des erreurs. Pour utiliser ces API, remplacez `/api/v1/messaging/` par `/api/v1/messaging/v2/` dans vos requêtes.

Exemple :
- Ancienne URL : `http://localhost:8000/api/v1/messaging/conversations/`
- Nouvelle URL : `http://localhost:8000/api/v1/messaging/v2/conversations/`

### 2. Exécuter le script de correction de la base de données

Nous avons créé un script pour vérifier et corriger les problèmes courants dans la base de données de messagerie.

```bash
cd josnet_backend
python fix_messaging_db.py
```

Ce script :
- Vérifie l'intégrité de la base de données
- Identifie les messages orphelins
- Vérifie l'intégrité des conversations et des messages
- Corrige les problèmes courants

### 3. Vérifier les logs pour identifier les erreurs

Nous avons amélioré la journalisation pour mieux comprendre les erreurs. Consultez le fichier `debug.log` dans le répertoire racine du backend pour voir les erreurs détaillées.

```bash
tail -f josnet_backend/debug.log
```

### 4. Mettre à jour les migrations

Si vous rencontrez des problèmes avec la structure de la base de données, essayez de mettre à jour les migrations :

```bash
python manage.py makemigrations messaging
python manage.py migrate messaging
```

## Modifications apportées

1. **Amélioration de la gestion des erreurs** : Les vues ont été modifiées pour mieux gérer les erreurs et fournir des messages d'erreur plus détaillés.

2. **Journalisation améliorée** : Des logs détaillés ont été ajoutés pour faciliter le débogage.

3. **Gestion des cas limites** : Le code a été modifié pour gérer les cas où les relations entre modèles sont problématiques.

4. **API alternatives** : Des points d'accès API alternatifs (v2) ont été créés pour une meilleure robustesse.

## Prochaines étapes

Si les problèmes persistent, envisagez les actions suivantes :

1. **Vérifier les relations entre modèles** : Assurez-vous que les relations entre `Conversation`, `Message`, `Order` et autres modèles sont correctement définies.

2. **Vérifier les données** : Utilisez l'interface d'administration Django pour vérifier directement les données dans la base de données.

3. **Reconstruire la base de données** : En dernier recours, vous pouvez reconstruire la base de données à partir de zéro.

```bash
python manage.py flush
python manage.py migrate
python manage.py createsuperuser
```

## Contact

Si vous avez des questions ou besoin d'aide supplémentaire, contactez l'équipe de développement.
