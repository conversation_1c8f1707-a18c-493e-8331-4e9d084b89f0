#!/usr/bin/env python3
"""
Script pour générer un nouveau token de réinitialisation et le tester
"""

import sys
import os
import requests
import json

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User, PasswordReset
from django.utils import timezone
from datetime import timedelta
import uuid

def generate_fresh_token(email="<EMAIL>"):
    """Génère un nouveau token de réinitialisation"""
    try:
        user = User.objects.get(email=email)
        
        # Générer un nouveau token
        token = str(uuid.uuid4())
        expires_at = timezone.now() + timedelta(hours=24)
        
        # Créer l'entrée dans la base de données
        reset = PasswordReset.objects.create(
            user=user,
            token=token,
            expires_at=expires_at
        )
        
        print(f"✅ Nouveau token généré: {token}")
        print(f"📧 Utilisateur: {user.email}")
        print(f"⏰ Expire le: {expires_at}")
        print(f"🔗 URL: http://localhost:8080/reset-password/{token}")
        
        return token
        
    except User.DoesNotExist:
        print(f"❌ Utilisateur {email} non trouvé")
        return None
    except Exception as e:
        print(f"❌ Erreur lors de la génération: {e}")
        return None

def test_token_immediately(token):
    """Test le token immédiatement après génération"""
    url = "http://localhost:8000/api/v1/auth/password/reset/confirm/"
    
    payload = {
        "token": token,
        "new_password": "TestPassword123!",
        "confirm_password": "TestPassword123!"
    }
    
    print(f"\n🧪 Test immédiat du token: {token}")
    
    try:
        response = requests.post(url, json=payload)
        
        print(f"📊 Status: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"📄 Réponse: {json.dumps(response_data, indent=2)}")
        except:
            print(f"📄 Réponse (text): {response.text}")
            
        return response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print("❌ Serveur Django non accessible")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🔄 Génération d'un nouveau token de réinitialisation")
    print("=" * 60)
    
    # Générer un nouveau token
    token = generate_fresh_token()
    
    if token:
        # Tester immédiatement
        success = test_token_immediately(token)
        
        if success:
            print("\n✅ Le token fonctionne parfaitement!")
        else:
            print("\n❌ Le token ne fonctionne pas")
            
        print(f"\n💡 Pour tester dans le frontend, utilisez ce token:")
        print(f"   {token}")
        print(f"\n🔗 URL complète pour le frontend:")
        print(f"   http://localhost:8080/reset-password/{token}")
    else:
        print("\n❌ Impossible de générer un token")
