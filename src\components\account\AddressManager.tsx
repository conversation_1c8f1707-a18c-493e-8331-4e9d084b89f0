import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Edit, Trash2, Star, MapPin, Phone, Building } from "lucide-react";
import userApi, { UserAddress, AddressCreateUpdate } from "@/services/userApi";

const AddressManager = () => {
  const [isAddressDialogOpen, setIsAddressDialogOpen] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<UserAddress | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<AddressCreateUpdate>({
    address_type: 'both',
    is_default: false,
    first_name: '',
    last_name: '',
    address_line1: '',
    city: '',
    postal_code: '',
    country: 'Burundi' // Valeur par défaut pour le Burundi
  });

  // Villes principales du Burundi
  const burundiCities = [
    'Bujumbura',
    'Gitega',
    'Muyinga',
    'Ruyigi',
    'Cankuzo',
    'Rutana',
    'Makamba',
    'Bururi',
    'Rumonge',
    'Cibitoke',
    'Bubanza',
    'Muramvya',
    'Mwaro',
    'Kayanza',
    'Ngozi',
    'Kirundo',
    'Karuzi'
  ];

  // Pays disponibles (Burundi en premier)
  const availableCountries = [
    'Burundi',
    'Rwanda',
    'République démocratique du Congo',
    'Tanzanie',
    'Kenya',
    'Ouganda',
    'France',
    'Belgique',
    'Canada',
    'États-Unis'
  ];

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fonction pour synchroniser le profil avec l'adresse par défaut
  const syncProfileWithDefaultAddress = async (addressData: AddressCreateUpdate) => {
    if (addressData.is_default) {
      try {
        // Mettre à jour le profil utilisateur avec les informations de l'adresse
        await userApi.updateProfile({
          first_name: addressData.first_name,
          last_name: addressData.last_name,
          phone_number: addressData.phone || null
        });

        console.log('✅ Profil synchronisé avec l\'adresse par défaut');
      } catch (error) {
        console.error('❌ Erreur lors de la synchronisation du profil:', error);
        // Ne pas afficher d'erreur à l'utilisateur car c'est une fonctionnalité secondaire
      }
    }
  };

  // Fetch addresses
  const { data: addresses, isLoading } = useQuery({
    queryKey: ['userAddresses'],
    queryFn: userApi.getAddresses
  });

  // Create address mutation
  const createAddressMutation = useMutation({
    mutationFn: userApi.createAddress,
    onSuccess: async (newAddress, variables) => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });

      // Synchroniser le profil si c'est l'adresse par défaut
      await syncProfileWithDefaultAddress(variables);

      toast({
        title: "Adresse créée",
        description: "Votre adresse a été ajoutée avec succès.",
      });
      setIsAddressDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      console.error('❌ Erreur création adresse:', error);

      let errorMessage = "Impossible de créer l'adresse. Veuillez réessayer.";
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Erreur de création",
        description: errorMessage,
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  });

  // Update address mutation
  const updateAddressMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<AddressCreateUpdate> }) =>
      userApi.updateAddress(id, data),
    onSuccess: async (updatedAddress, variables) => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });

      // Synchroniser le profil si c'est l'adresse par défaut
      if (variables.data) {
        await syncProfileWithDefaultAddress(variables.data as AddressCreateUpdate);
      }

      toast({
        title: "Adresse mise à jour",
        description: "Votre adresse a été mise à jour avec succès.",
      });
      setIsAddressDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      console.error('❌ Erreur mise à jour adresse:', error);

      let errorMessage = "Impossible de mettre à jour l'adresse. Veuillez réessayer.";
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Erreur de mise à jour",
        description: errorMessage,
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  });

  // Delete address mutation
  const deleteAddressMutation = useMutation({
    mutationFn: userApi.deleteAddress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      toast({
        title: "Adresse supprimée",
        description: "Votre adresse a été supprimée avec succès.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer l'adresse. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  });

  // Set default address mutation
  const setDefaultAddressMutation = useMutation({
    mutationFn: userApi.setDefaultAddress,
    onSuccess: async (result, addressId) => {
      queryClient.invalidateQueries({ queryKey: ['userAddresses'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });

      // Récupérer l'adresse qui vient d'être définie par défaut pour synchroniser le profil
      try {
        const address = await userApi.getAddress(addressId);
        await syncProfileWithDefaultAddress({
          address_type: address.address_type,
          is_default: true,
          first_name: address.first_name,
          last_name: address.last_name,
          address_line1: address.address_line1,
          city: address.city,
          postal_code: address.postal_code,
          country: address.country,
          phone: address.phone,
          company: address.company,
          address_line2: address.address_line2,
          state: address.state
        });
      } catch (error) {
        console.error('❌ Erreur lors de la synchronisation après définition par défaut:', error);
      }

      toast({
        title: "Adresse par défaut",
        description: "Votre adresse par défaut et profil ont été mis à jour.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erreur",
        description: "Impossible de définir l'adresse par défaut. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, is_default: checked }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation des champs requis
    if (!formData.first_name || !formData.last_name) {
      toast({
        title: "Champs requis",
        description: "Le prénom et le nom sont obligatoires.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.address_line1) {
      toast({
        title: "Adresse requise",
        description: "L'adresse principale est obligatoire.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.city) {
      toast({
        title: "Ville requise",
        description: "Veuillez sélectionner ou saisir une ville.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.country) {
      toast({
        title: "Pays requis",
        description: "Veuillez sélectionner un pays.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    if (currentAddress) {
      updateAddressMutation.mutate({
        id: currentAddress.id,
        data: formData
      });
    } else {
      createAddressMutation.mutate(formData);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      address_type: 'both',
      is_default: false,
      first_name: '',
      last_name: '',
      address_line1: '',
      city: '',
      postal_code: '',
      country: 'Burundi' // Garder Burundi par défaut
    });
    setCurrentAddress(null);
    setIsSubmitting(false);
  };

  // Open dialog for editing
  const handleEditAddress = (address: UserAddress) => {
    setCurrentAddress(address);
    setFormData({
      address_type: address.address_type,
      is_default: address.is_default,
      first_name: address.first_name,
      last_name: address.last_name,
      company: address.company || undefined,
      address_line1: address.address_line1,
      address_line2: address.address_line2 || undefined,
      city: address.city,
      state: address.state || undefined,
      postal_code: address.postal_code,
      country: address.country,
      phone: address.phone || undefined
    });
    setIsAddressDialogOpen(true);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsAddressDialogOpen(false);
    resetForm();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Mes adresses</h2>
        <Button onClick={() => setIsAddressDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Ajouter une adresse
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : addresses && addresses.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map((address) => (
            <Card key={address.id} className="relative">
              {address.is_default && (
                <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-md text-xs">
                  Par défaut
                </div>
              )}
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-primary" />
                  {address.first_name} {address.last_name}
                </CardTitle>
                <CardDescription className="flex items-center gap-1">
                  {address.address_type === 'shipping' ? (
                    <>📦 Adresse de livraison</>
                  ) : address.address_type === 'billing' ? (
                    <>💳 Adresse de facturation</>
                  ) : (
                    <>📦💳 Livraison et facturation</>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {address.company && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Building className="h-3 w-3" />
                    {address.company}
                  </div>
                )}
                <div className="space-y-1">
                  <p className="text-sm font-medium">{address.address_line1}</p>
                  {address.address_line2 && <p className="text-sm text-muted-foreground">{address.address_line2}</p>}
                  <p className="text-sm">
                    {address.postal_code && `${address.postal_code} `}
                    <span className="font-medium">{address.city}</span>
                  </p>
                  {address.state && <p className="text-sm text-muted-foreground">{address.state}</p>}
                  <p className="text-sm font-medium">
                    {address.country === 'Burundi' ? '🇧🇮 ' : ''}
                    {address.country}
                  </p>
                </div>
                {address.phone && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground pt-2 border-t">
                    <Phone className="h-3 w-3" />
                    {address.phone}
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleEditAddress(address)}
                  >
                    <Edit className="h-4 w-4 mr-1" /> Modifier
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="text-destructive" 
                    onClick={() => deleteAddressMutation.mutate(address.id)}
                  >
                    <Trash2 className="h-4 w-4 mr-1" /> Supprimer
                  </Button>
                </div>
                {!address.is_default && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setDefaultAddressMutation.mutate(address.id)}
                  >
                    <Star className="h-4 w-4 mr-1" /> Définir par défaut
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">Vous n'avez pas encore d'adresse enregistrée.</p>
            <Button className="mt-4" onClick={() => setIsAddressDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Ajouter une adresse
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Address Dialog */}
      <Dialog open={isAddressDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="space-y-3">
            <DialogTitle className="flex items-center gap-3 text-xl">
              {currentAddress ? (
                <>
                  <Edit className="h-5 w-5 text-primary" />
                  Modifier l'adresse
                </>
              ) : (
                <>
                  <Plus className="h-5 w-5 text-primary" />
                  Ajouter une nouvelle adresse
                </>
              )}
            </DialogTitle>
            <DialogDescription className="text-base">
              {currentAddress
                ? "Modifiez les informations de votre adresse ci-dessous. Tous les champs marqués d'un * sont obligatoires."
                : "Remplissez les informations pour ajouter une nouvelle adresse. Nous livrons partout au Burundi ! 🇧🇮"}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="space-y-6 py-4">

              {/* Section 1: Informations personnelles */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <h3 className="font-semibold text-lg">Informations personnelles</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="first_name" className="text-sm font-medium">
                      Prénom <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="first_name"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleInputChange}
                      placeholder="Ex: Jean"
                      required
                      className="focus:ring-2 focus:ring-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="last_name" className="text-sm font-medium">
                      Nom <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="last_name"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleInputChange}
                      placeholder="Ex: Dupont"
                      required
                      className="focus:ring-2 focus:ring-primary"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company" className="text-sm font-medium">
                    Entreprise (optionnel)
                  </Label>
                  <Input
                    id="company"
                    name="company"
                    value={formData.company || ''}
                    onChange={handleInputChange}
                    placeholder="Ex: JosNet SARL, Ministère du Commerce..."
                    className="focus:ring-2 focus:ring-primary"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-sm font-medium">
                    Téléphone (recommandé pour la livraison)
                  </Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone || ''}
                    onChange={handleInputChange}
                    placeholder="Ex: +257 22 123 456 ou 79 123 456"
                    className="focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>

              {/* Section 2: Adresse */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <h3 className="font-semibold text-lg">Adresse de livraison</h3>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address_line1" className="text-sm font-medium">
                    Adresse principale <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="address_line1"
                    name="address_line1"
                    value={formData.address_line1}
                    onChange={handleInputChange}
                    placeholder="Ex: Avenue de l'Indépendance, 123 ou Quartier Rohero, Rue 15"
                    required
                    className="focus:ring-2 focus:ring-primary"
                  />
                  <p className="text-xs text-muted-foreground">
                    💡 Conseil: Indiquez le nom de la rue, le numéro ou des points de repère connus
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address_line2" className="text-sm font-medium">
                    Complément d'adresse (optionnel)
                  </Label>
                  <Input
                    id="address_line2"
                    name="address_line2"
                    value={formData.address_line2 || ''}
                    onChange={handleInputChange}
                    placeholder="Ex: Bâtiment A, 2ème étage, Porte 15, Près de..."
                    className="focus:ring-2 focus:ring-primary"
                  />
                  <p className="text-xs text-muted-foreground">
                    💡 Ajoutez des détails pour faciliter la livraison (étage, bâtiment, points de repère)
                  </p>
                </div>
              </div>
              
              {/* Section 3: Localisation */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                  <h3 className="font-semibold text-lg">Localisation</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city" className="text-sm font-medium">
                      Ville <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      name="city"
                      value={formData.city}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, city: value }))}
                    >
                      <SelectTrigger className="focus:ring-2 focus:ring-primary">
                        <SelectValue placeholder="🏙️ Sélectionnez votre ville" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Bujumbura">🏛️ Bujumbura (Capitale économique)</SelectItem>
                        <SelectItem value="Gitega">🏛️ Gitega (Capitale politique)</SelectItem>
                        {burundiCities.filter(city => !['Bujumbura', 'Gitega'].includes(city)).map((city) => (
                          <SelectItem key={city} value={city}>🏘️ {city}</SelectItem>
                        ))}
                        <SelectItem value="autre">📍 Autre ville...</SelectItem>
                      </SelectContent>
                    </Select>
                    {formData.city === 'autre' && (
                      <Input
                        name="city"
                        placeholder="Saisissez le nom de votre ville"
                        value={formData.city === 'autre' ? '' : formData.city}
                        onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                        className="mt-2 focus:ring-2 focus:ring-primary"
                      />
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postal_code" className="text-sm font-medium">
                      Code postal <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="postal_code"
                      name="postal_code"
                      value={formData.postal_code}
                      onChange={handleInputChange}
                      placeholder="Ex: 1000 (Bujumbura)"
                      className="focus:ring-2 focus:ring-primary"
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      💡 1000 pour Bujumbura
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="state" className="text-sm font-medium">
                      Province (optionnel)
                    </Label>
                    <Input
                      id="state"
                      name="state"
                      value={formData.state || ''}
                      onChange={handleInputChange}
                      placeholder="Ex: Bujumbura Mairie, Gitega..."
                      className="focus:ring-2 focus:ring-primary"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="country" className="text-sm font-medium">
                      Pays <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      name="country"
                      value={formData.country}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, country: value }))}
                    >
                      <SelectTrigger className="focus:ring-2 focus:ring-primary">
                        <SelectValue placeholder="🌍 Sélectionnez un pays" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Burundi">🇧🇮 Burundi</SelectItem>
                        <div className="px-2 py-1 text-xs text-muted-foreground border-t">Autres pays (frais de livraison supplémentaires)</div>
                        {availableCountries.filter(country => country !== 'Burundi').map((country) => (
                          <SelectItem key={country} value={country}>
                            {country}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              

              {/* Section 4: Préférences */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                  <h3 className="font-semibold text-lg">Préférences d'utilisation</h3>
                </div>

                <div className="space-y-4">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      Type d'adresse <span className="text-red-500">*</span>
                    </Label>
                    <div className="grid grid-cols-1 gap-3">
                      <div
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          formData.address_type === 'both'
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setFormData(prev => ({ ...prev, address_type: 'both' }))}
                      >
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">📦💳</div>
                          <div>
                            <div className="font-medium">Livraison et facturation</div>
                            <div className="text-sm text-muted-foreground">Utiliser cette adresse pour les livraisons et la facturation (recommandé)</div>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div
                          className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                            formData.address_type === 'shipping'
                              ? 'border-primary bg-primary/5'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setFormData(prev => ({ ...prev, address_type: 'shipping' }))}
                        >
                          <div className="flex items-center gap-2">
                            <div className="text-xl">📦</div>
                            <div>
                              <div className="font-medium text-sm">Livraison uniquement</div>
                              <div className="text-xs text-muted-foreground">Pour recevoir mes commandes</div>
                            </div>
                          </div>
                        </div>

                        <div
                          className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                            formData.address_type === 'billing'
                              ? 'border-primary bg-primary/5'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setFormData(prev => ({ ...prev, address_type: 'billing' }))}
                        >
                          <div className="flex items-center gap-2">
                            <div className="text-xl">💳</div>
                            <div>
                              <div className="font-medium text-sm">Facturation uniquement</div>
                              <div className="text-xs text-muted-foreground">Pour mes factures</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <Checkbox
                      id="is_default"
                      checked={formData.is_default}
                      onCheckedChange={handleCheckboxChange}
                      className="mt-1"
                    />
                    <div className="space-y-1">
                      <Label htmlFor="is_default" className="text-sm font-medium cursor-pointer">
                        ⭐ Définir comme adresse par défaut
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Cette adresse sera automatiquement sélectionnée lors de vos prochaines commandes
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Section d'aide */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-medium text-green-800 mb-2 flex items-center gap-2">
                  💡 Conseils pour une livraison réussie
                </h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Indiquez des points de repère connus (école, église, marché...)</li>
                  <li>• Ajoutez votre numéro de téléphone pour que le livreur puisse vous contacter</li>
                  <li>• Précisez l'étage ou le bâtiment si nécessaire</li>
                  <li>• Nous livrons partout au Burundi, généralement sous 24-48h 🚚</li>
                </ul>
              </div>
            </div>
            <DialogFooter className="gap-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleDialogClose}
                className="flex-1 sm:flex-none"
                disabled={isSubmitting}
              >
                ❌ Annuler
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 sm:flex-none bg-primary hover:bg-primary/90"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Enregistrement...
                  </>
                ) : currentAddress ? (
                  <>
                    <Edit className="mr-2 h-4 w-4" />
                    ✅ Mettre à jour l'adresse
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    ✅ Ajouter l'adresse
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AddressManager;
