#!/usr/bin/env python
"""
Debug spécifique pour les signaux de nouveaux produits.
"""

import os
import sys
import django
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category

User = get_user_model()


def debug_product_signals():
    """Debug des signaux pour les nouveaux produits."""
    print("🔍 DEBUG: Signaux pour nouveaux produits")
    print("=" * 50)
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Debug',
            'last_name': 'Test',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Debug Category',
        defaults={'slug': 'debug-category'}
    )
    
    # Créer un abonné aux nouveaux produits
    subscription, created = NewsletterSubscription.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'preferences_promotions': False,
            'preferences_new_products': True,
            'preferences_newsletters': True,
            'preferences_order_updates': True
        }
    )
    
    print(f"✅ Admin: {admin_user.email}")
    print(f"✅ Catégorie: {category.name}")
    print(f"✅ Abonné produits: {subscription.email} (Nouveaux produits: {subscription.preferences_new_products})")
    
    # Compter les abonnés aux nouveaux produits
    product_subscribers = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_new_products=True
    ).count()
    print(f"📊 Total abonnés nouveaux produits: {product_subscribers}")
    
    # Nettoyer les anciens produits de debug
    Product.objects.filter(name__contains='Debug Product').delete()
    NewsletterCampaign.objects.filter(title__contains='Debug Product').delete()
    
    print("\n🔄 ÉTAPE 1: Création d'un produit en brouillon")
    
    # Vérifier les signaux en important explicitement
    try:
        import products.signals
        print("✅ Module signals importé")
    except ImportError as e:
        print(f"❌ Erreur import signals: {e}")
    
    # Créer le produit en brouillon
    product = Product.objects.create(
        name='Debug Product Test',
        slug='debug-product-test',
        sku='DPT001',
        description='Produit de debug pour tester les signaux.',
        short_description='Produit de debug',
        price=Decimal('99.99'),
        status='draft',
        created_by=admin_user
    )
    product.categories.add(category)
    
    print(f"✅ Produit créé: {product.name}")
    print(f"📊 Statut: {product.status}")
    print(f"👤 Créé par: {product.created_by}")
    
    # Vérifier qu'aucune campagne n'existe
    campaigns_before = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Debug Product'
    ).count()
    print(f"📨 Campagnes avant publication: {campaigns_before}")
    
    print("\n🚀 ÉTAPE 2: Publication du produit")
    
    # Publier le produit
    old_status = product.status
    product.status = 'published'
    
    print(f"🔄 Changement de statut: {old_status} → {product.status}")
    
    # Sauvegarder et déclencher les signaux
    product.save()
    
    print(f"✅ Produit sauvegardé avec statut: {product.status}")
    
    # Vérifier les campagnes après publication
    campaigns_after = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Debug Product'
    )
    print(f"📨 Campagnes après publication: {campaigns_after.count()}")
    
    if campaigns_after.exists():
        campaign = campaigns_after.first()
        print(f"\n📧 CAMPAGNE TROUVÉE:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        print(f"   📅 Créé le: {campaign.created_at}")
        return True
    else:
        print(f"\n❌ AUCUNE CAMPAGNE CRÉÉE!")
        
        # Debug supplémentaire
        print(f"\n🔍 DEBUG SUPPLÉMENTAIRE:")
        print(f"   📊 Produit ID: {product.id}")
        print(f"   📊 Produit PK: {product.pk}")
        print(f"   📊 Produit status: {product.status}")
        print(f"   📊 Produit created_by: {product.created_by}")
        
        # Vérifier si le signal est connecté
        from django.db.models.signals import post_save
        
        print(f"   🔗 Signaux connectés à Product:")
        for receiver in post_save._live_receivers(sender=product.__class__):
            print(f"      - {receiver}")
        
        # Vérifier manuellement le service
        try:
            from core.services.newsletter_service import NewsletterService
            
            print(f"\n🧪 TEST MANUEL DU SERVICE:")
            result = NewsletterService.send_new_product_notification(
                product_name=product.name,
                product_description=product.short_description,
                product_url=f"http://example.com/products/{product.slug}",
                product_image=None,
                created_by=product.created_by
            )
            
            print(f"   📧 Résultat envoi manuel: {result}")
            
            if result.get('success'):
                print(f"   ✅ Envoi manuel réussi: {result.get('sent_count')} emails")
                return True
            else:
                print(f"   ❌ Envoi manuel échoué: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur test manuel: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Fonction principale de debug."""
    print("🔍 DEBUG DES SIGNAUX PRODUITS")
    print("=" * 60)
    
    try:
        success = debug_product_signals()
        
        if success:
            print(f"\n✅ DEBUG RÉUSSI: Les signaux produits fonctionnent")
        else:
            print(f"\n❌ DEBUG ÉCHOUÉ: Problème avec les signaux produits")
            
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU DEBUG: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
