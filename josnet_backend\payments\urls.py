from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import webhooks
from .views import (
    InvoiceListView,
    InvoiceDetailView,
    PaymentMethodViewSet,
    TransactionViewSet,
    InvoiceViewSet,
    CustomerPaymentMethodViewSet
)

router = DefaultRouter()
router.register(r'payment-methods', PaymentMethodViewSet)
router.register(r'transactions', TransactionViewSet)
router.register(r'invoices', InvoiceViewSet)
router.register(r'customer-payment-methods', CustomerPaymentMethodViewSet)

urlpatterns = [
    path('', include(router.urls)),

    # Account invoice endpoints
    path('account/invoices/', InvoiceListView.as_view(), name='account-invoices'),
    path('account/invoices/<str:invoice_id>/', InvoiceDetailView.as_view(), name='account-invoice-detail'),

    # Webhook endpoints
    path('webhooks/stripe/', webhooks.stripe_webhook, name='stripe-webhook'),
    path('webhooks/paypal/', webhooks.paypal_webhook, name='paypal-webhook'),
]
