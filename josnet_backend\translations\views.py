from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.utils.translation import gettext_lazy as _, get_language, activate
from django.conf import settings
from django.http import HttpResponse
from .models import TranslationKey, Translation, UserLanguagePreference
from .serializers import (
    TranslationKeySerializer, TranslationKeyCreateSerializer,
    TranslationUpdateSerializer, UserLanguagePreferenceSerializer,
    BulkTranslationSerializer, LanguageListSerializer,
    TranslationExportSerializer
)
from .utils import (
    get_all_translations, refresh_translation_cache,
    bulk_create_translations, export_translations
)


class TranslationKeyViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des clés de traduction"""
    queryset = TranslationKey.objects.all()
    permission_classes = [IsAdminUser]
    
    def get_serializer_class(self):
        if self.action == 'create':
            return TranslationKeyCreateSerializer
        return TranslationKeySerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        category = self.request.query_params.get('category')
        search = self.request.query_params.get('search')
        
        if category:
            queryset = queryset.filter(category=category)
        
        if search:
            queryset = queryset.filter(
                key__icontains=search
            ) | queryset.filter(
                description__icontains=search
            )
        
        return queryset.prefetch_related('translations')
    
    @action(detail=True, methods=['post'])
    def update_translation(self, request, pk=None):
        """Mettre à jour une traduction spécifique"""
        translation_key = self.get_object()
        language_code = request.data.get('language_code')
        
        if not language_code:
            return Response(
                {'error': _('Le code de langue est requis')},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            translation = translation_key.translations.get(language_code=language_code)
        except Translation.DoesNotExist:
            translation = Translation(key=translation_key, language_code=language_code)
        
        serializer = TranslationUpdateSerializer(translation, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def bulk_import(self, request):
        """Import en masse de traductions"""
        serializer = BulkTranslationSerializer(data=request.data)
        if serializer.is_valid():
            stats = bulk_create_translations(serializer.validated_data['translations'])
            return Response({
                'message': _('Import terminé avec succès'),
                'stats': stats
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def refresh_cache(self, request):
        """Actualiser le cache des traductions"""
        language_code = request.data.get('language_code')
        refresh_translation_cache(language_code)
        return Response({'message': _('Cache actualisé avec succès')})


@api_view(['GET'])
@permission_classes([])
def get_translations(request):
    """
    Récupérer toutes les traductions pour une langue
    Endpoint public pour le frontend
    """
    language_code = request.GET.get('lang', get_language())
    
    # Valider le code de langue
    valid_languages = [lang[0] for lang in settings.LANGUAGES]
    if language_code not in valid_languages:
        language_code = settings.LANGUAGE_CODE
    
    translations = get_all_translations(language_code)
    
    return Response({
        'language': language_code,
        'translations': translations,
        'count': len(translations)
    })


@api_view(['GET'])
@permission_classes([])
def get_languages(request):
    """Récupérer la liste des langues disponibles"""
    languages = []
    default_lang = settings.LANGUAGE_CODE
    
    for code, name in settings.LANGUAGES:
        languages.append({
            'code': code,
            'name': name,
            'is_default': code == default_lang
        })
    
    return Response({
        'languages': languages,
        'default': default_lang
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def set_user_language(request):
    """Définir la préférence de langue de l'utilisateur"""
    serializer = UserLanguagePreferenceSerializer(
        data=request.data,
        context={'request': request}
    )
    
    if serializer.is_valid():
        preference = serializer.save()
        
        # Activer la langue pour cette session
        activate(preference.language_code)
        
        return Response({
            'message': _('Préférence de langue mise à jour'),
            'language_code': preference.language_code
        })
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_language(request):
    """Récupérer la préférence de langue de l'utilisateur"""
    try:
        preference = UserLanguagePreference.objects.get(user=request.user)
        return Response({
            'language_code': preference.language_code
        })
    except UserLanguagePreference.DoesNotExist:
        return Response({
            'language_code': settings.LANGUAGE_CODE
        })


@api_view(['POST'])
@permission_classes([IsAdminUser])
def export_translations_view(request):
    """Exporter les traductions"""
    serializer = TranslationExportSerializer(data=request.data)
    
    if serializer.is_valid():
        language_code = serializer.validated_data.get('language_code')
        format_type = serializer.validated_data.get('format', 'json')
        
        exported_data = export_translations(language_code, format_type)
        
        if format_type == 'json':
            response = HttpResponse(
                exported_data,
                content_type='application/json'
            )
            filename = f'translations_{language_code or "all"}.json'
        else:  # csv
            response = HttpResponse(
                exported_data,
                content_type='text/csv'
            )
            filename = f'translations_{language_code or "all"}.csv'
        
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([])
def translation_stats(request):
    """Statistiques des traductions"""
    stats = {}
    
    for lang_code, lang_name in settings.LANGUAGES:
        total_keys = TranslationKey.objects.filter(is_active=True).count()
        translated = Translation.objects.filter(
            language_code=lang_code,
            key__is_active=True,
            is_approved=True
        ).count()
        
        stats[lang_code] = {
            'name': lang_name,
            'total_keys': total_keys,
            'translated': translated,
            'percentage': round((translated / total_keys * 100) if total_keys > 0 else 0, 2)
        }
    
    return Response(stats)


@api_view(['POST'])
@permission_classes([])
def detect_missing_translations(request):
    """Détecter les traductions manquantes depuis le frontend"""
    missing_keys = request.data.get('keys', [])
    language_code = request.data.get('language', get_language())
    
    if not missing_keys:
        return Response({'error': _('Aucune clé fournie')}, status=status.HTTP_400_BAD_REQUEST)
    
    created_count = 0
    
    for key in missing_keys:
        # Créer la clé si elle n'existe pas
        translation_key, created = TranslationKey.objects.get_or_create(
            key=key,
            defaults={
                'description': f'Auto-generated from frontend: {key}',
                'category': 'frontend-auto',
                'is_active': True
            }
        )
        
        if created:
            # Créer les traductions par défaut
            for lang_code, lang_name in settings.LANGUAGES:
                Translation.objects.create(
                    key=translation_key,
                    language_code=lang_code,
                    value=key,  # Utiliser la clé comme valeur par défaut
                    is_approved=False
                )
            created_count += 1
    
    return Response({
        'message': f'{created_count} nouvelles clés créées',
        'created_count': created_count
    })
