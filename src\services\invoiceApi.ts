import api from './api';

/**
 * Service pour gérer les factures
 */
const invoiceApi = {
  /**
   * Récupère toutes les factures de l'utilisateur connecté
   */
  getMyInvoices: async () => {
    const response = await api.get<any[]>('/payments/invoices/my_invoices/');
    return response;
  },

  /**
   * Récupère une facture spécifique
   * @param id Identifiant de la facture
   */
  getInvoice: async (id: string) => {
    const response = await api.get<any>(`/payments/invoices/${id}/`);
    return response;
  },

  /**
   * Télécharge une facture au format PDF
   * @param id Identifiant de la facture
   * @returns URL Blob pour le téléchargement
   */
  downloadInvoice: async (id: string) => {
    // Pour les réponses de type blob, nous devons utiliser fetch directement
    const token = localStorage.getItem('accessToken');
    const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'}/payments/invoices/${id}/download/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to download invoice');
    }
    
    // Récupérer le blob
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    
    return url;
  },

  /**
   * Récupère la facture d'une commande (si elle existe)
   * @param orderId Identifiant de la commande
   */
  getInvoiceForOrder: async (orderId: string) => {
    const response = await api.get<any>(`/payments/invoices/?order_id=${orderId}`);
    return response.results && response.results.length > 0 ? response.results[0] : null;
  },

  /**
   * Génère une facture pour une commande (admin seulement)
   * @param orderId Identifiant de la commande
   */
  generateInvoiceForOrder: async (orderId: string) => {
    const response = await api.post<any>('/payments/invoices/generate_for_order/', { order_id: orderId });
    return response;
  }
};

export default invoiceApi;
