#!/usr/bin/env python
"""
Test simple pour vérifier l'envoi automatique d'emails
lors de la publication de promotions et nouveaux produits.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import override_settings
from core.models import NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category, Promotion

User = get_user_model()


def setup_test_data():
    """Créer les données de test."""
    print("🔧 Configuration des données de test...")
    
    # Créer un admin
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'testpass123',
            'first_name': 'Admin',
            'last_name': 'TestAutoEmail',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
        print(f"   ✅ Admin créé: {admin_user.email}")
    
    # Nettoyer les anciens abonnements de test
    NewsletterSubscription.objects.filter(email__contains='testautoemail.com').delete()
    
    # Créer des abonnés de test
    test_subscribers = [
        ('<EMAIL>', True, False),   # Promotions seulement
        ('<EMAIL>', False, True),     # Nouveaux produits seulement  
        ('<EMAIL>', True, True),  # Tout
    ]
    
    for email, promo_pref, product_pref in test_subscribers:
        subscription = NewsletterSubscription.objects.create(
            email=email,
            preferences_promotions=promo_pref,
            preferences_new_products=product_pref,
            preferences_newsletters=True,
            preferences_order_updates=True
        )
        print(f"   ✅ Abonné créé: {email} (Promos: {promo_pref}, Produits: {product_pref})")
    
    # Créer une catégorie
    category, created = Category.objects.get_or_create(
        name='Test Auto Email',
        defaults={'slug': 'test-auto-email'}
    )
    
    return admin_user, category


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def test_promotion_auto_email():
    """Test d'envoi automatique d'email pour promotion."""
    print("\n🎉 TEST: Email automatique pour promotion")
    print("=" * 50)
    
    admin_user, category = setup_test_data()
    
    # Compter les abonnés aux promotions
    promo_subscribers = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_promotions=True
    ).count()
    print(f"📊 Abonnés aux promotions: {promo_subscribers}")
    
    # Nettoyer les anciennes données de test
    Promotion.objects.filter(title__contains='Test Auto Email').delete()
    NewsletterCampaign.objects.filter(title__contains='Test Auto Email').delete()
    
    print("\n🔄 Création d'une promotion en brouillon...")
    promotion = Promotion.objects.create(
        title='🔥 Test Auto Email Promotion - 50% OFF',
        description='Promotion de test pour vérifier l\'envoi automatique d\'emails.',
        promotion_type='percentage',
        discount_percentage=Decimal('50.00'),
        start_date=timezone.now(),
        end_date=timezone.now() + timedelta(days=7),
        status='draft',
        created_by=admin_user,
        send_email_notification=True
    )
    
    print(f"   ✅ Promotion créée: {promotion.title}")
    print(f"   📧 Notifications activées: {promotion.send_email_notification}")
    print(f"   📧 Email déjà envoyé: {promotion.email_sent}")
    
    # Vérifier qu'aucune campagne n'existe encore
    campaigns_before = NewsletterCampaign.objects.filter(
        campaign_type='promotion',
        title__contains='Test Auto Email'
    ).count()
    print(f"   📨 Campagnes avant activation: {campaigns_before}")
    
    print("\n🚀 Activation de la promotion (déclenchement automatique)...")
    promotion.status = 'active'
    promotion.save()
    
    print(f"   ✅ Promotion activée")
    
    # Vérifier si l'email a été marqué comme envoyé
    promotion.refresh_from_db()
    print(f"   📧 Email marqué comme envoyé: {promotion.email_sent}")
    if promotion.email_sent:
        print(f"   📅 Email envoyé à: {promotion.email_sent_at}")
    
    # Vérifier les campagnes créées
    campaigns_after = NewsletterCampaign.objects.filter(
        campaign_type='promotion',
        title__contains='Test Auto Email'
    )
    print(f"   📨 Campagnes après activation: {campaigns_after.count()}")
    
    if campaigns_after.exists():
        campaign = campaigns_after.first()
        print(f"\n📧 CAMPAGNE CRÉÉE:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        
        if campaign.total_recipients == promo_subscribers:
            print(f"   ✅ Nombre de destinataires correct!")
            return True
        else:
            print(f"   ❌ Nombre incorrect: {campaign.total_recipients} vs {promo_subscribers}")
            return False
    else:
        print(f"   ❌ Aucune campagne créée!")
        return False


@override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
def test_product_auto_email():
    """Test d'envoi automatique d'email pour nouveau produit."""
    print("\n✨ TEST: Email automatique pour nouveau produit")
    print("=" * 50)
    
    admin_user, category = setup_test_data()
    
    # Compter les abonnés aux nouveaux produits
    product_subscribers = NewsletterSubscription.objects.filter(
        is_active=True,
        preferences_new_products=True
    ).count()
    print(f"📊 Abonnés aux nouveaux produits: {product_subscribers}")
    
    # Nettoyer les anciennes données de test
    Product.objects.filter(name__contains='Test Auto Email').delete()
    NewsletterCampaign.objects.filter(title__contains='Test Auto Email Product').delete()
    
    print("\n🔄 Création d'un produit en brouillon...")
    product = Product.objects.create(
        name='🚀 Test Auto Email Product - MacBook Pro',
        slug='test-auto-email-product-macbook-pro',
        sku='TAEP001',
        description='Produit de test pour vérifier l\'envoi automatique d\'emails.',
        short_description='MacBook Pro de test pour emails automatiques',
        price=Decimal('2499.99'),
        status='draft',
        created_by=admin_user
    )
    product.categories.add(category)
    
    print(f"   ✅ Produit créé: {product.name}")
    print(f"   📊 Statut: {product.status}")
    
    # Vérifier qu'aucune campagne n'existe encore
    campaigns_before = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Test Auto Email Product'
    ).count()
    print(f"   📨 Campagnes avant publication: {campaigns_before}")
    
    print("\n🚀 Publication du produit (déclenchement automatique)...")
    product.status = 'published'
    product.save()
    
    print(f"   ✅ Produit publié")
    
    # Vérifier les campagnes créées
    campaigns_after = NewsletterCampaign.objects.filter(
        campaign_type='new_product',
        title__contains='Test Auto Email Product'
    )
    print(f"   📨 Campagnes après publication: {campaigns_after.count()}")
    
    if campaigns_after.exists():
        campaign = campaigns_after.first()
        print(f"\n📧 CAMPAGNE CRÉÉE:")
        print(f"   📝 Titre: {campaign.title}")
        print(f"   📊 Destinataires: {campaign.total_recipients}")
        print(f"   ✅ Envoyés: {campaign.sent_count}")
        print(f"   📈 Statut: {campaign.get_status_display()}")
        
        if campaign.total_recipients == product_subscribers:
            print(f"   ✅ Nombre de destinataires correct!")
            return True
        else:
            print(f"   ❌ Nombre incorrect: {campaign.total_recipients} vs {product_subscribers}")
            return False
    else:
        print(f"   ❌ Aucune campagne créée!")
        return False


def show_current_subscribers():
    """Afficher les abonnés actuels."""
    print("\n👥 ABONNÉS ACTUELS:")
    print("=" * 50)
    
    total = NewsletterSubscription.objects.filter(is_active=True).count()
    promo = NewsletterSubscription.objects.filter(is_active=True, preferences_promotions=True).count()
    product = NewsletterSubscription.objects.filter(is_active=True, preferences_new_products=True).count()
    
    print(f"📊 Total abonnés actifs: {total}")
    print(f"🎉 Abonnés promotions: {promo}")
    print(f"✨ Abonnés nouveaux produits: {product}")
    
    print(f"\n📋 Détail des abonnés:")
    for sub in NewsletterSubscription.objects.filter(is_active=True):
        print(f"   📧 {sub.email}")
        print(f"      🎉 Promotions: {'✅' if sub.preferences_promotions else '❌'}")
        print(f"      ✨ Nouveaux produits: {'✅' if sub.preferences_new_products else '❌'}")


def main():
    """Fonction principale."""
    print("🚀 TEST D'ENVOI AUTOMATIQUE D'EMAILS")
    print("=" * 80)
    print("Ce test vérifie que les emails sont envoyés automatiquement")
    print("lorsque l'admin publie des promotions et nouveaux produits.")
    
    try:
        # Afficher les abonnés actuels
        show_current_subscribers()
        
        # Test 1: Email automatique pour promotion
        promotion_success = test_promotion_auto_email()
        
        # Test 2: Email automatique pour nouveau produit
        product_success = test_product_auto_email()
        
        # Résumé
        print("\n" + "=" * 80)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 80)
        
        tests = [
            ("Email automatique promotion", promotion_success),
            ("Email automatique nouveau produit", product_success)
        ]
        
        passed = sum(1 for _, success in tests if success)
        failed = len(tests) - passed
        
        for test_name, success in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\nTotal: {len(tests)} tests")
        print(f"Réussis: {passed}")
        print(f"Échoués: {failed}")
        
        if failed == 0:
            print("\n🎉 TOUS LES TESTS SONT PASSÉS !")
            print("✅ Les emails sont envoyés automatiquement")
            print("✅ Le filtrage selon les préférences fonctionne")
            print("✅ Le système est opérationnel")
        else:
            print(f"\n⚠️  {failed} test(s) ont échoué")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
