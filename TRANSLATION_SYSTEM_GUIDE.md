# 🌐 Système de Traduction Complet - JosNet E-commerce

## 📋 Vue d'ensemble

Ce système de traduction avancé permet de traduire toute la plateforme JosNet en 4 langues :
- **🇫🇷 Français** (langue par défaut)
- **🇬🇧 English**
- **🇹🇿 Kiswahili**
- **🇧🇮 Kirundi**

## 🏗️ Architecture

### Backend Django
- **App `translations`** : Gestion des traductions dynamiques
- **Modèles** : `TranslationKey`, `Translation`, `TranslationCache`, `UserLanguagePreference`
- **API REST** : Endpoints pour le frontend
- **Interface Admin** : Gestion via Django Admin + Rosetta
- **Cache** : Optimisation des performances

### Frontend React
- **Context `TranslationProvider`** : Gestion globale des traductions
- **Hook `useTranslation`** : Accès facile aux traductions
- **Composant `LanguageSelector`** : Sélecteur de langue
- **Détection automatique** : Traductions manquantes signalées au backend

## 🚀 Installation

### 1. Exécuter le script d'installation
```bash
python create_translation_system.py
```

### 2. Importer les traductions initiales
```bash
cd josnet_backend
python manage.py import_translations
```

### 3. Redémarrer le serveur
```bash
python manage.py runserver
```

## 📖 Utilisation

### Frontend React

#### Utilisation de base
```typescript
import { useTranslation } from '@/contexts/TranslationContext';

function MyComponent() {
  const { t, currentLanguage, changeLanguage } = useTranslation();
  
  return (
    <div>
      <h1>{t('page.title', 'Titre par défaut')}</h1>
      <p>{t('page.description')}</p>
      <button onClick={() => changeLanguage('en')}>
        English
      </button>
    </div>
  );
}
```

#### Traductions avec paramètres
```typescript
const { t } = useTranslation();

// Avec paramètres
const message = t('welcome.message', 'Bonjour {name}!', { name: 'Jean' });

// Pluralisation
const { pluralize } = useFormattedTranslation();
const itemText = pluralize(count, 'item.single', 'item.plural');
```

#### Formatage avancé
```typescript
import { useFormattedTranslation } from '@/components/common/LanguageSelector';

function ProductCard({ product }) {
  const { t, formatCurrency, formatDate } = useFormattedTranslation();
  
  return (
    <div>
      <h3>{t('product.name')}</h3>
      <p>{formatCurrency(product.price)}</p>
      <span>{formatDate(product.created_at)}</span>
    </div>
  );
}
```

### Backend Django

#### Utilisation dans les vues
```python
from translations.utils import get_translation
from django.utils.translation import get_language

def my_view(request):
    current_lang = get_language()
    message = get_translation('success.message', current_lang, 'Opération réussie')
    return JsonResponse({'message': message})
```

#### Traduction de modèles
```python
from translations.utils import translate_model_field

class ProductSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    
    def get_name(self, obj):
        return translate_model_field(obj, 'name', self.context.get('language'))
```

## 🔧 Administration

### Interface Django Admin
- **URL** : `http://localhost:8000/admin/translations/`
- **Gestion des clés** : Créer, modifier, supprimer
- **Gestion des traductions** : Approuver, modifier
- **Statistiques** : Progression par langue

### Interface Rosetta
- **URL** : `http://localhost:8000/rosetta/`
- **Traduction en ligne** : Interface conviviale
- **Fichiers .po** : Gestion des traductions Django standard

## 📡 API REST

### Endpoints publics

#### Récupérer les traductions
```http
GET /api/v1/translations/translations/?lang=fr
```

#### Récupérer les langues disponibles
```http
GET /api/v1/translations/languages/
```

#### Statistiques des traductions
```http
GET /api/v1/translations/stats/
```

### Endpoints utilisateur (authentifié)

#### Définir la préférence de langue
```http
POST /api/v1/translations/user/language/set/
Content-Type: application/json

{
  "language_code": "en"
}
```

#### Récupérer la préférence de langue
```http
GET /api/v1/translations/user/language/
```

### Endpoints admin

#### Import en masse
```http
POST /api/v1/translations/keys/bulk_import/
Content-Type: application/json

{
  "translations": [
    {
      "key": "my.key",
      "category": "interface",
      "description": "Ma clé",
      "translations": {
        "fr": "Ma valeur",
        "en": "My value"
      }
    }
  ]
}
```

#### Export des traductions
```http
POST /api/v1/translations/export/
Content-Type: application/json

{
  "language_code": "fr",
  "format": "json"
}
```

## 🎯 Bonnes Pratiques

### Nommage des clés
```
category.subcategory.element
```

Exemples :
- `nav.menu.home` → Navigation > Menu > Accueil
- `form.validation.required` → Formulaire > Validation > Requis
- `product.details.price` → Produit > Détails > Prix

### Organisation par catégories
- **interface** : Éléments d'interface générale
- **navigation** : Menus et liens
- **forms** : Formulaires et validation
- **ecommerce** : Éléments e-commerce
- **auth** : Authentification
- **errors** : Messages d'erreur

### Fallbacks
Toujours fournir un fallback :
```typescript
t('my.key', 'Texte par défaut si traduction manquante')
```

## 🔄 Workflow de traduction

### 1. Développement
- Utiliser `t('key', 'fallback')` dans le code
- Les clés manquantes sont automatiquement créées

### 2. Traduction
- Accéder à l'interface admin ou Rosetta
- Traduire les nouvelles clés
- Approuver les traductions

### 3. Déploiement
- Les traductions sont mises en cache
- Actualisation automatique du cache

## 🚀 Fonctionnalités Avancées

### Cache intelligent
- Cache Redis/Memcached pour les performances
- Invalidation automatique lors des modifications
- Cache par langue et par clé

### Détection automatique
- Traductions manquantes signalées automatiquement
- Création automatique des clés depuis le frontend
- Logs de débogage en mode développement

### Préférences utilisateur
- Sauvegarde de la langue préférée
- Synchronisation entre sessions
- Détection de la langue du navigateur

### Interface multilingue
- Sélecteur de langue dans la navigation
- Drapeaux et noms natifs
- Transition fluide entre langues

## 🛠️ Maintenance

### Commandes utiles

#### Actualiser le cache
```bash
python manage.py shell
>>> from translations.utils import refresh_translation_cache
>>> refresh_translation_cache()
```

#### Exporter toutes les traductions
```bash
python manage.py shell
>>> from translations.utils import export_translations
>>> data = export_translations(format='json')
>>> with open('backup.json', 'w') as f:
...     f.write(data)
```

#### Statistiques
```bash
python manage.py shell
>>> from translations.models import Translation, TranslationKey
>>> total_keys = TranslationKey.objects.filter(is_active=True).count()
>>> translated = Translation.objects.filter(is_approved=True).count()
>>> print(f"Progression: {translated}/{total_keys*4} ({translated/(total_keys*4)*100:.1f}%)")
```

## 🐛 Dépannage

### Traductions non affichées
1. Vérifier que le `TranslationProvider` entoure l'app
2. Vérifier la console pour les erreurs API
3. Vérifier que les traductions sont approuvées

### Performance lente
1. Vérifier le cache Redis/Memcached
2. Actualiser le cache des traductions
3. Optimiser les requêtes de traduction

### Traductions manquantes
1. Vérifier les logs de la console
2. Utiliser l'endpoint `detect-missing`
3. Créer manuellement via l'admin

## 📈 Métriques

Le système fournit des métriques détaillées :
- Nombre de clés par catégorie
- Progression de traduction par langue
- Traductions les plus utilisées
- Traductions manquantes fréquentes

## 🎉 Résultat

Avec ce système, votre plateforme JosNet sera entièrement multilingue avec :
- ✅ Interface utilisateur traduite
- ✅ Contenu dynamique multilingue
- ✅ Gestion facile des traductions
- ✅ Performance optimisée
- ✅ Expérience utilisateur fluide
