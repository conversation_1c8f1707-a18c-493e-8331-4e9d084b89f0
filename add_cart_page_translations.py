#!/usr/bin/env python3
"""
Script pour ajouter les traductions de la page Cart.tsx
"""
import os
import sys
import django

# Configuration Django
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from translations.models import TranslationKey, Translation

def add_cart_page_translations():
    """Ajouter les traductions de la page Cart.tsx"""
    print("🛒 AJOUT DES TRADUCTIONS DE LA PAGE CART.TSX")
    print("=" * 50)
    
    new_translations = {
        # Messages de livraison gratuite
        "cart.add_amount_for_free_shipping": {
            "fr": "Ajoutez",
            "en": "Add",
            "sw": "Ongeza",
            "rn": "Shira"
        },
        "cart.items_for_free_shipping": {
            "fr": "d'articles pour bénéficier de la livraison gratuite!",
            "en": "worth of items to get free shipping!",
            "sw": "ya bidhaa kupata usafirishaji bure!",
            "rn": "by'i<PERSON><PERSON><PERSON><PERSON> kugira ngo ubone ubwohereza bwubusa!"
        },
        
        # Titre et panier vide
        "cart.your_cart": {
            "fr": "Votre Panier",
            "en": "Your Cart",
            "sw": "Kikapu Chako",
            "rn": "Igikoni Cyawe"
        },
        "cart.empty_cart": {
            "fr": "Votre panier est vide",
            "en": "Your cart is empty",
            "sw": "Kikapu chako ni tupu",
            "rn": "Igikoni cyawe nticyuzuye"
        },
        "cart.no_products_added": {
            "fr": "Vous n'avez pas encore ajouté de produits à votre panier.",
            "en": "You haven't added any products to your cart yet.",
            "sw": "Bado hujaweka bidhaa yoyote kwenye kikapu chako.",
            "rn": "Ntukaba warashoye igicuruzwa mu gikoni cyawe."
        },
        "cart.continue_shopping": {
            "fr": "Continuer vos achats",
            "en": "Continue shopping",
            "sw": "Endelea ununuzi",
            "rn": "Komeza kugura"
        },
        
        # Articles du panier
        "cart.cart_items": {
            "fr": "Articles du panier",
            "en": "Cart items",
            "sw": "Vitu vya kikapu",
            "rn": "Ibintu by'igikoni"
        },
        "cart.remove_item": {
            "fr": "Supprimer l'article",
            "en": "Remove item",
            "sw": "Ondoa kipengee",
            "rn": "Gukuraho ikintu"
        },
        
        # Récapitulatif de commande
        "cart.order_summary": {
            "fr": "Récapitulatif de la commande",
            "en": "Order summary",
            "sw": "Muhtasari wa agizo",
            "rn": "Incamake y'itumizo"
        },
        "cart.subtotal": {
            "fr": "Sous-total",
            "en": "Subtotal",
            "sw": "Jumla ndogo",
            "rn": "Igiteranyo gito"
        },
        "cart.shipping_cost": {
            "fr": "Frais de livraison",
            "en": "Shipping cost",
            "sw": "Gharama za usafirishaji",
            "rn": "Ikiguzi cy'ubwohereza"
        },
        "cart.free": {
            "fr": "Gratuit",
            "en": "Free",
            "sw": "Bure",
            "rn": "Ubusa"
        },
        "cart.discount": {
            "fr": "Réduction",
            "en": "Discount",
            "sw": "Punguzo",
            "rn": "Kugabanya"
        },
        
        # Code promo
        "cart.promo_code": {
            "fr": "Code promo",
            "en": "Promo code",
            "sw": "Msimbo wa punguzo",
            "rn": "Kode ya kugabanya"
        },
        "cart.enter_promo_code": {
            "fr": "Entrez votre code",
            "en": "Enter your code",
            "sw": "Ingiza msimbo wako",
            "rn": "Shyiramo kode yawe"
        },
        "cart.apply": {
            "fr": "Appliquer",
            "en": "Apply",
            "sw": "Tumia",
            "rn": "Koresha"
        },
        
        # Modes de paiement
        "cart.accepted_payment_methods": {
            "fr": "Modes de paiement acceptés",
            "en": "Accepted payment methods",
            "sw": "Njia za malipo zinazokubaliwa",
            "rn": "Uburyo bw'kwishyura bwemewe"
        }
    }
    
    imported_count = 0
    
    for key_name, translations_dict in new_translations.items():
        # Créer ou récupérer la clé de traduction
        translation_key, created = TranslationKey.objects.get_or_create(
            key=key_name,
            defaults={
                'category': 'cart',
                'description': f"Traduction pour: {translations_dict['fr']}",
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Nouvelle clé: {key_name}")
        
        # Ajouter les traductions pour chaque langue
        for lang_code, translation_text in translations_dict.items():
            translation_obj, created = Translation.objects.get_or_create(
                key=translation_key,
                language_code=lang_code,
                defaults={
                    'value': translation_text,
                    'is_approved': True
                }
            )
            
            if created:
                imported_count += 1
            elif translation_obj.value != translation_text:
                # Mettre à jour si différent
                translation_obj.value = translation_text
                translation_obj.save()
                print(f"🔄 Mise à jour: {key_name} ({lang_code})")
    
    print(f"\n📊 RÉSULTATS:")
    print(f"   • Nouvelles traductions importées: {imported_count}")
    print(f"   • Clés de traduction traitées: {len(new_translations)}")
    
    return imported_count

def test_cart_page_translations():
    """Tester les traductions de la page Cart"""
    print(f"\n🧪 TEST DES TRADUCTIONS DE LA PAGE CART")
    print("=" * 50)
    
    # Clés importantes à tester
    important_keys = [
        "cart.your_cart",
        "cart.empty_cart", 
        "cart.continue_shopping",
        "cart.order_summary",
        "cart.promo_code",
        "order.checkout"
    ]
    
    languages = ['fr', 'en', 'sw', 'rn']
    all_good = True
    
    for key in important_keys:
        try:
            translation_key = TranslationKey.objects.get(key=key)
            print(f"\n✅ {key}:")
            
            for lang in languages:
                try:
                    translation = Translation.objects.get(
                        key=translation_key, 
                        language_code=lang
                    )
                    flag = {'fr': '🇫🇷', 'en': '🇬🇧', 'sw': '🇹🇿', 'rn': '🇧🇮'}[lang]
                    print(f"   {flag} {lang}: \"{translation.value}\"")
                except Translation.DoesNotExist:
                    print(f"   ❌ {lang}: MANQUANT")
                    all_good = False
                    
        except TranslationKey.DoesNotExist:
            print(f"❌ Clé manquante: {key}")
            all_good = False
    
    return all_good

def show_usage_examples():
    """Afficher des exemples d'utilisation"""
    print(f"\n💡 EXEMPLES D'UTILISATION DANS CART.TSX")
    print("=" * 50)
    
    examples = [
        {
            "situation": "Titre de la page",
            "code": '<h1>{t("cart.your_cart", "Votre Panier")}</h1>',
            "description": "Titre principal de la page panier"
        },
        {
            "situation": "Panier vide",
            "code": '<h2>{t("cart.empty_cart", "Votre panier est vide")}</h2>',
            "description": "Message affiché quand le panier est vide"
        },
        {
            "situation": "Bouton continuer",
            "code": '<Link>{t("cart.continue_shopping", "Continuer vos achats")}</Link>',
            "description": "Bouton pour retourner aux produits"
        },
        {
            "situation": "Code promo",
            "code": 'placeholder={t("cart.enter_promo_code", "Entrez votre code")}',
            "description": "Placeholder du champ code promo"
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['situation']}:")
        print(f"   Code: {example['code']}")
        print(f"   Usage: {example['description']}")

def main():
    """Fonction principale"""
    print("🛒 TRADUCTIONS DE LA PAGE CART.TSX")
    print("=" * 60)
    
    # Ajouter les traductions
    imported_count = add_cart_page_translations()
    
    if imported_count > 0:
        print(f"\n🎉 {imported_count} nouvelles traductions ajoutées!")
    else:
        print(f"\n✅ Toutes les traductions sont déjà à jour")
    
    # Tester les traductions
    all_good = test_cart_page_translations()
    
    if all_good:
        print(f"\n🎉 TOUTES LES TRADUCTIONS DE LA PAGE CART SONT DISPONIBLES!")
    else:
        print(f"\n⚠️ Certaines traductions sont manquantes")
    
    # Afficher des exemples
    show_usage_examples()
    
    # Statistiques finales
    total_keys = TranslationKey.objects.count()
    total_translations = Translation.objects.count()
    
    print(f"\n📊 STATISTIQUES TOTALES:")
    print(f"   • Total clés: {total_keys}")
    print(f"   • Total traductions: {total_translations}")
    
    print(f"\n✅ PAGE CART.TSX TRADUITE AVEC SUCCÈS!")
    print(f"\n📊 PROGRESSION PHASE 1:")
    print(f"   ✅ CartContext.tsx - Terminé")
    print(f"   ✅ Cart.tsx - Terminé")
    print(f"   🔄 Navigation - À faire")
    print(f"   🔄 Auth components - À faire")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
