import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import AccountLayout from "@/components/account/AccountLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Eye, Download, Loader2, LogIn, CreditCard, RefreshCw } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useUserSync } from "@/hooks/useUserSync";
import paymentApi from "@/services/paymentApi";
import { getAuthToken } from "@/utils/auth";

// Status color mapping
const statusColors: Record<string, string> = {
  completed: "bg-green-100 text-green-800",
  pending: "bg-yellow-100 text-yellow-800",
  failed: "bg-red-100 text-red-800",
  cancelled: "bg-gray-100 text-gray-800",
  refunded: "bg-blue-100 text-blue-800",
  partially_refunded: "bg-purple-100 text-purple-800",
};

const Transactions = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  
  // Synchroniser les données utilisateur
  useUserSync();
  
  // Debug authentication state
  const token = getAuthToken();

  // Fetch user transactions only if user is authenticated
  const {
    data: transactionsData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['userTransactions'],
    queryFn: () => {
      // Double check authentication before making the call
      if (!isAuthenticated || !user) {
        throw new Error('User not authenticated');
      }
      return paymentApi.getTransactions({ user_only: true });
    },
    enabled: isAuthenticated && !!user && !authLoading && !!token,
    retry: (failureCount, error: any) => {
      // Don't retry on 401 errors or authentication errors
      if (error?.response?.status === 401 || error?.message === 'User not authenticated') {
        return false;
      }
      return failureCount < 3;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: fr });
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', { 
      style: 'currency', 
      currency: 'BIF', 
      minimumFractionDigits: 0, 
      maximumFractionDigits: 0 
    }).format(price);
  };

  // Filter transactions
  const filteredTransactions = transactionsData?.results?.filter((transaction: any) =>
    transaction.transaction_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.order?.toString().includes(searchTerm.toLowerCase())
  ) || [];

  // Loading authentication state
  if (authLoading) {
    return (
      <AccountLayout title="Historique des transactions">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Vérification de l'authentification...</span>
        </div>
      </AccountLayout>
    );
  }

  // Not authenticated state
  if (!isAuthenticated || !user || !token) {
    return (
      <AccountLayout title="Historique des transactions">
        <Card>
          <CardContent className="py-8 text-center">
            <LogIn className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">Connexion requise</h3>
            <p className="text-gray-600 mb-4">
              Vous devez être connecté pour voir votre historique de transactions.
            </p>
            <Link to="/login">
              <Button>
                <LogIn className="h-4 w-4 mr-2" />
                Se connecter
              </Button>
            </Link>
          </CardContent>
        </Card>
      </AccountLayout>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <AccountLayout title="Historique des transactions">
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AccountLayout>
    );
  }

  // Error state
  if (isError) {
    return (
      <AccountLayout title="Historique des transactions">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-destructive">Une erreur est survenue lors du chargement de vos transactions.</p>
            <Button className="mt-4" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Réessayer
            </Button>
          </CardContent>
        </Card>
      </AccountLayout>
    );
  }

  return (
    <AccountLayout title="Historique des transactions">
      <div className="space-y-6">
        {/* Header avec statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Total des transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{transactionsData?.count || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Transactions réussies</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {filteredTransactions.filter((t: any) => t.status === 'completed').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">En attente</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {filteredTransactions.filter((t: any) => t.status === 'pending').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recherche */}
        <div className="flex items-center justify-between">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Rechercher une transaction..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
        </div>

        {/* Table des transactions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Mes transactions
            </CardTitle>
            <CardDescription>
              Historique de tous vos paiements et transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Transaction</TableHead>
                    <TableHead>Commande</TableHead>
                    <TableHead>Méthode</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Montant</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.length > 0 ? (
                    filteredTransactions.map((transaction: any) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium">
                          {transaction.transaction_id || `TXN-${transaction.id}`}
                        </TableCell>
                        <TableCell>
                          <Link 
                            to={`/order/${transaction.order}`}
                            className="text-primary hover:underline"
                          >
                            #{transaction.order}
                          </Link>
                        </TableCell>
                        <TableCell>{transaction.payment_method_name || 'N/A'}</TableCell>
                        <TableCell>{formatDate(transaction.created_at)}</TableCell>
                        <TableCell className="font-medium">
                          {formatPrice(transaction.amount)}
                        </TableCell>
                        <TableCell>
                          <Badge className={statusColors[transaction.status] || 'bg-gray-100 text-gray-800'}>
                            {transaction.status_display || transaction.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/account/transactions/${transaction.id}`}>
                              <Eye className="h-4 w-4 mr-1" />
                              <span className="hidden sm:inline">Détails</span>
                            </Link>
                          </Button>
                          {transaction.status === 'completed' && (
                            <Button variant="outline" size="sm" asChild>
                              <Link to={`/account/invoices/${transaction.order}`}>
                                <Download className="h-4 w-4 mr-1" />
                                <span className="hidden sm:inline">Reçu</span>
                              </Link>
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        {searchTerm ? 'Aucune transaction trouvée' : 'Vous n\'avez pas encore de transaction'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AccountLayout>
  );
};

export default Transactions;
