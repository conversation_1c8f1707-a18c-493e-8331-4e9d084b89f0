from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from payments.models import CustomerPaymentMethod
from payments.serializers import CustomerPaymentMethodSerializer, CustomerPaymentMethodCreateSerializer


class CustomerPaymentMethodViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing customer payment methods.
    """
    queryset = CustomerPaymentMethod.objects.all()
    serializer_class = CustomerPaymentMethodSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['payment_method', 'is_default', 'card_type']
    ordering_fields = ['created_at', 'is_default']
    ordering = ['-is_default', '-created_at']

    def get_serializer_class(self):
        if self.action in ['create']:
            return CustomerPaymentMethodCreateSerializer
        return CustomerPaymentMethodSerializer

    def get_queryset(self):
        """Filter payment methods based on user."""
        user = self.request.user
        if user.is_staff or user.is_superuser:
            return CustomerPaymentMethod.objects.all()
        return CustomerPaymentMethod.objects.filter(user=user)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set a payment method as default."""
        payment_method = self.get_object()

        # Check if user owns this payment method
        if payment_method.user != request.user and not request.user.is_staff:
            return Response(
                {"detail": "You do not have permission to perform this action."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Set as default
        payment_method.is_default = True
        payment_method.save()

        serializer = self.get_serializer(payment_method)
        return Response(serializer.data)
