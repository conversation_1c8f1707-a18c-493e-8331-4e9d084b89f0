"""
Administration pour les modèles du core.
"""

from django.contrib import admin
from .models import (
    SiteSettings, HomePageStats, Service, Testimonial,
    NewsletterSubscriber, ContactMessage, FAQ, Banner
)


@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    """Administration des paramètres du site."""
    
    list_display = ['site_name', 'is_active', 'updated_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['site_name', 'site_description']
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('site_name', 'site_description', 'site_logo', 'is_active')
        }),
        ('Contact', {
            'fields': ('phone_primary', 'phone_secondary', 'email_primary', 'email_secondary')
        }),
        ('Adresse', {
            'fields': ('address_line1', 'address_line2', 'city', 'country', 'business_hours')
        }),
        ('Réseaux sociaux', {
            'fields': ('facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url'),
            'classes': ('collapse',)
        }),
        ('SEO', {
            'fields': ('meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
    )


@admin.register(HomePageStats)
class HomePageStatsAdmin(admin.ModelAdmin):
    """Administration des statistiques de la page d'accueil."""
    
    list_display = ['clients_count', 'projects_count', 'experience_years', 'is_active', 'updated_at']
    list_filter = ['is_active', 'created_at']
    
    fieldsets = (
        ('Statistiques principales', {
            'fields': ('clients_count', 'projects_count', 'experience_years', 'team_members')
        }),
        ('Statistiques secondaires', {
            'fields': ('products_sold', 'cities_served', 'support_hours', 'satisfaction_rate')
        }),
        ('Paramètres', {
            'fields': ('is_active',)
        }),
    )


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """Administration des services."""
    
    list_display = ['name', 'featured', 'order', 'is_active', 'updated_at']
    list_filter = ['featured', 'is_active', 'color', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['featured', 'order']
    prepopulated_fields = {'slug': ('name',)}
    
    fieldsets = (
        ('Informations de base', {
            'fields': ('name', 'slug', 'description', 'short_description')
        }),
        ('Apparence', {
            'fields': ('icon', 'color')
        }),
        ('Affichage', {
            'fields': ('featured', 'order', 'is_active')
        }),
    )


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    """Administration des témoignages."""
    
    list_display = ['client_name', 'client_company', 'rating', 'featured', 'order', 'is_active']
    list_filter = ['rating', 'featured', 'is_active', 'created_at']
    search_fields = ['client_name', 'client_company', 'content']
    list_editable = ['featured', 'order', 'rating']
    
    fieldsets = (
        ('Client', {
            'fields': ('client_name', 'client_title', 'client_company', 'client_photo')
        }),
        ('Témoignage', {
            'fields': ('content', 'rating')
        }),
        ('Affichage', {
            'fields': ('featured', 'order', 'is_active')
        }),
    )


@admin.register(NewsletterSubscriber)
class NewsletterSubscriberAdmin(admin.ModelAdmin):
    """Administration des abonnés newsletter."""
    
    list_display = ['email', 'name', 'is_active', 'confirmed', 'frequency', 'subscribed_at']
    list_filter = ['is_active', 'confirmed', 'frequency', 'subscribed_at']
    search_fields = ['email', 'name']
    list_editable = ['is_active', 'confirmed']
    readonly_fields = ['subscribed_at', 'updated_at']
    
    actions = ['mark_as_active', 'mark_as_inactive', 'mark_as_confirmed']
    
    def mark_as_active(self, request, queryset):
        queryset.update(is_active=True)
    mark_as_active.short_description = "Marquer comme actif"
    
    def mark_as_inactive(self, request, queryset):
        queryset.update(is_active=False)
    mark_as_inactive.short_description = "Marquer comme inactif"
    
    def mark_as_confirmed(self, request, queryset):
        queryset.update(confirmed=True)
    mark_as_confirmed.short_description = "Marquer comme confirmé"


@admin.register(ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    """Administration des messages de contact."""
    
    list_display = ['name', 'email', 'subject', 'service_requested', 'status', 'created_at']
    list_filter = ['status', 'service_requested', 'created_at']
    search_fields = ['name', 'email', 'subject', 'message']
    list_editable = ['status']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Expéditeur', {
            'fields': ('name', 'email', 'phone', 'company')
        }),
        ('Message', {
            'fields': ('subject', 'message', 'service_requested')
        }),
        ('Gestion', {
            'fields': ('status', 'replied_at')
        }),
        ('Métadonnées', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['mark_as_replied', 'mark_as_closed']
    
    def mark_as_replied(self, request, queryset):
        from django.utils import timezone
        queryset.update(status='replied', replied_at=timezone.now())
    mark_as_replied.short_description = "Marquer comme répondu"
    
    def mark_as_closed(self, request, queryset):
        queryset.update(status='closed')
    mark_as_closed.short_description = "Marquer comme fermé"


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    """Administration des FAQ."""
    
    list_display = ['question', 'category', 'featured', 'order', 'is_active']
    list_filter = ['category', 'featured', 'is_active', 'created_at']
    search_fields = ['question', 'answer']
    list_editable = ['featured', 'order', 'category']
    
    fieldsets = (
        ('Question/Réponse', {
            'fields': ('question', 'answer', 'category')
        }),
        ('Affichage', {
            'fields': ('featured', 'order', 'is_active')
        }),
    )


@admin.register(Banner)
class BannerAdmin(admin.ModelAdmin):
    """Administration des bannières."""
    
    list_display = ['title', 'position', 'is_visible_display', 'order', 'start_date', 'end_date']
    list_filter = ['position', 'is_active', 'start_date', 'end_date']
    search_fields = ['title', 'subtitle', 'description']
    list_editable = ['order', 'position']
    
    fieldsets = (
        ('Contenu', {
            'fields': ('title', 'subtitle', 'description', 'image')
        }),
        ('Action', {
            'fields': ('button_text', 'button_url')
        }),
        ('Affichage', {
            'fields': ('position', 'order', 'is_active')
        }),
        ('Planification', {
            'fields': ('start_date', 'end_date')
        }),
    )
    
    def is_visible_display(self, obj):
        return obj.is_visible()
    is_visible_display.boolean = True
    is_visible_display.short_description = 'Visible'


# Personnalisation de l'admin
admin.site.site_header = "JOSNET NETWORK - Administration"
admin.site.site_title = "JOSNET Admin"
admin.site.index_title = "Tableau de bord administrateur"
