import React from 'react';
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { CheckCircle, XCircle, CalendarClock, AlertCircle } from "lucide-react";
import { format, parseISO, isAfter, isBefore } from "date-fns";
import { fr } from "date-fns/locale";

interface PromotionStatusBadgeProps {
  isActive: boolean;
  startDate: string | Date;
  endDate?: string | Date | null;
}

const PromotionStatusBadge: React.FC<PromotionStatusBadgeProps> = ({
  isActive,
  startDate,
  endDate
}) => {
  const now = new Date();
  const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
  const end = endDate ? (typeof endDate === 'string' ? parseISO(endDate) : endDate) : null;
  
  // Déterminer le statut réel de la promotion
  let status: 'active' | 'inactive' | 'scheduled' | 'expired' = 'inactive';
  
  if (isActive) {
    if (isBefore(now, start)) {
      status = 'scheduled';
    } else if (end && isBefore(now, end)) {
      status = 'active';
    } else if (!end) {
      status = 'active';
    } else if (end && isAfter(now, end)) {
      status = 'expired';
    }
  }
  
  // Configurer l'apparence en fonction du statut
  const getStatusConfig = () => {
    switch (status) {
      case 'active':
        return {
          variant: 'default' as const,
          icon: <CheckCircle className="h-3.5 w-3.5 mr-1 text-white" />,
          label: 'Active',
          tooltip: end 
            ? `Active jusqu'au ${format(end, 'dd MMMM yyyy', { locale: fr })}`
            : 'Active sans date d\'expiration'
        };
      case 'scheduled':
        return {
          variant: 'secondary' as const,
          icon: <CalendarClock className="h-3.5 w-3.5 mr-1" />,
          label: 'Programmée',
          tooltip: `Débute le ${format(start, 'dd MMMM yyyy', { locale: fr })}`
        };
      case 'expired':
        return {
          variant: 'destructive' as const,
          icon: <AlertCircle className="h-3.5 w-3.5 mr-1" />,
          label: 'Expirée',
          tooltip: `Expirée depuis le ${format(end!, 'dd MMMM yyyy', { locale: fr })}`
        };
      case 'inactive':
        return {
          variant: 'outline' as const,
          icon: <XCircle className="h-3.5 w-3.5 mr-1" />,
          label: 'Inactive',
          tooltip: 'Cette promotion est désactivée'
        };
    }
  };
  
  const config = getStatusConfig();
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant={config.variant} className="flex items-center">
            {config.icon}
            {config.label}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{config.tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default PromotionStatusBadge;
