from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class Category(models.Model):
    """
    Model for conversation categories.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default="#3498db")  # Format HEX
    icon = models.CharField(max_length=50, blank=True)  # Icon name (for frontend)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def conversation_count(self):
        """Get the number of conversations in this category."""
        return self.conversations.count()


class Tag(models.Model):
    """
    Model for conversation tags.
    """
    name = models.Char<PERSON>ield(max_length=50)
    color = models.Char<PERSON><PERSON>(max_length=7, default="#2ecc71")  # Format HEX
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('Tag')
        verbose_name_plural = _('Tags')
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def conversation_count(self):
        """Get the number of conversations with this tag."""
        return self.conversations.count()


class Conversation(models.Model):
    """
    Model for conversations between users and administrators.
    """
    STATUSES = (
        ('new', _('New')),
        ('in_progress', _('In Progress')),
        ('responded', _('Responded')),
        ('resolved', _('Resolved')),
        ('closed', _('Closed')),
    )

    PRIORITIES = (
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    )

    subject = models.CharField(max_length=255)
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='conversations'
    )
    status = models.CharField(max_length=20, choices=STATUSES, default='new')
    priority = models.CharField(max_length=20, choices=PRIORITIES, default='medium')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_message_at = models.DateTimeField(auto_now_add=True)

    # Flags for tracking read status
    is_read_by_admin = models.BooleanField(default=False)
    is_read_by_customer = models.BooleanField(default=True)  # Customer creates it, so they've read it

    # Category and tags
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='conversations'
    )
    tags = models.ManyToManyField(
        Tag,
        blank=True,
        related_name='conversations'
    )

    # Integration with orders
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='conversations'
    )

    # Admin assignment
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_conversations'
    )

    class Meta:
        ordering = ['-last_message_at']
        verbose_name = _('Conversation')
        verbose_name_plural = _('Conversations')

    def __str__(self):
        return f"{self.subject} - {self.customer.email}"

    @property
    def last_message(self):
        """Get the last message in the conversation."""
        return self.messages.order_by('-created_at').first()

    @property
    def message_count(self):
        """Get the number of messages in the conversation."""
        return self.messages.count()

    def mark_as_read_by_admin(self):
        """Mark the conversation as read by admin."""
        self.is_read_by_admin = True
        self.save(update_fields=['is_read_by_admin'])

    def mark_as_read_by_customer(self):
        """Mark the conversation as read by customer."""
        self.is_read_by_customer = True
        self.save(update_fields=['is_read_by_customer'])

    def update_status(self, status):
        """Update the status of the conversation."""
        self.status = status
        self.save(update_fields=['status'])


class Message(models.Model):
    """
    Model for individual messages within a conversation.
    """
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='messages'
    )
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_messages'
    )
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_from_admin = models.BooleanField(default=False)

    class Meta:
        ordering = ['created_at']
        verbose_name = _('Message')
        verbose_name_plural = _('Messages')

    def __str__(self):
        return f"Message from {self.sender.email} at {self.created_at}"

    # La logique de mise à jour de la conversation est maintenant gérée par les signaux


class Attachment(models.Model):
    """
    Model for file attachments to messages.
    """
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='attachments'
    )
    file = models.FileField(upload_to='message_attachments/')
    file_name = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()  # Size in bytes
    file_type = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('Attachment')
        verbose_name_plural = _('Attachments')

    def __str__(self):
        return self.file_name


class ResponseTemplate(models.Model):
    """
    Model for predefined response templates.
    """
    name = models.CharField(max_length=100)
    content = models.TextField()
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='response_templates'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_templates'
    )

    class Meta:
        verbose_name = _('Response Template')
        verbose_name_plural = _('Response Templates')
        ordering = ['name']

    def __str__(self):
        return self.name
