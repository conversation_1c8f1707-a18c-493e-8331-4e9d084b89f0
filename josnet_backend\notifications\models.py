from django.db import models
from django.conf import settings

class Notification(models.Model):
    """
    Modèle pour les notifications utilisateur.
    """
    NOTIFICATION_TYPES = (
        ('promotion', 'Promotion'),
        ('order', 'Commande'),
        ('system', 'Système'),
        ('info', 'Information'),
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notifications',
        null=True,
        blank=True,
    )
    title = models.CharField(max_length=255)
    message = models.TextField()
    type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    is_read = models.BooleanField(default=False)
    link = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(blank=True, null=True)
    
    # Pour les notifications liées à des objets spécifiques
    related_id = models.PositiveIntegerField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'
    
    def __str__(self):
        return f"{self.title} - {self.user.email if self.user else 'All users'}"


class NotificationPreference(models.Model):
    """
    Modèle pour les préférences de notification des utilisateurs.
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notification_preferences'
    )
    email_promotions = models.BooleanField(default=True)
    push_promotions = models.BooleanField(default=True)
    email_orders = models.BooleanField(default=True)
    push_orders = models.BooleanField(default=True)
    email_system = models.BooleanField(default=True)
    push_system = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = 'Préférence de notification'
        verbose_name_plural = 'Préférences de notification'
    
    def __str__(self):
        return f"Préférences de {self.user.email}"
