from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, UserVerification, PasswordReset, UserAddress, LoyaltyTransaction

class UserAdmin(BaseUserAdmin):
    """Custom admin for User model."""

    list_display = ('email', 'first_name', 'last_name', 'role', 'loyalty_points', 'loyalty_tier', 'is_active', 'is_verified', 'is_staff')
    list_filter = ('role', 'is_active', 'is_verified', 'is_staff', 'created_at')
    search_fields = ('email', 'first_name', 'last_name')
    ordering = ('email',)

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'phone_number', 'date_of_birth', 'profile_picture')}),
        (_('Loyalty Program'), {'fields': ('loyalty_points', 'loyalty_tier')}),
        (_('Permissions'), {'fields': ('role', 'is_active', 'is_verified', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'created_at', 'updated_at')}),
    )

    readonly_fields = ('created_at', 'updated_at')

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'role', 'is_staff', 'is_superuser'),
        }),
    )

class UserVerificationAdmin(admin.ModelAdmin):
    """Admin for UserVerification model."""

    list_display = ('user', 'created_at', 'expires_at', 'is_expired')
    list_filter = ('created_at',)
    search_fields = ('user__email',)
    readonly_fields = ('token', 'created_at', 'expires_at')

class PasswordResetAdmin(admin.ModelAdmin):
    """Admin for PasswordReset model."""

    list_display = ('user', 'created_at', 'expires_at', 'is_used', 'is_expired')
    list_filter = ('created_at', 'is_used')
    search_fields = ('user__email',)
    readonly_fields = ('token', 'created_at', 'expires_at')

class UserAddressAdmin(admin.ModelAdmin):
    """Admin for UserAddress model."""

    list_display = ('user', 'address_type', 'is_default', 'city', 'country', 'created_at')
    list_filter = ('address_type', 'is_default', 'country', 'created_at')
    search_fields = ('user__email', 'first_name', 'last_name', 'city', 'country')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {'fields': ('user', 'address_type', 'is_default')}),
        ('Contact Information', {'fields': ('first_name', 'last_name', 'company', 'phone')}),
        ('Address', {'fields': ('address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country')}),
        ('Timestamps', {'fields': ('created_at', 'updated_at')}),
    )

class LoyaltyTransactionAdmin(admin.ModelAdmin):
    """Admin for LoyaltyTransaction model."""

    list_display = ('user', 'points', 'transaction_type', 'description', 'created_at', 'expires_at', 'is_expired')
    list_filter = ('transaction_type', 'created_at')
    search_fields = ('user__email', 'description')
    readonly_fields = ('created_at',)
    fieldsets = (
        (None, {'fields': ('user', 'points', 'transaction_type', 'description')}),
        ('Related', {'fields': ('order',)}),
        ('Expiration', {'fields': ('created_at', 'expires_at')}),
    )

# Register models
admin.site.register(User, UserAdmin)
admin.site.register(UserVerification, UserVerificationAdmin)
admin.site.register(PasswordReset, PasswordResetAdmin)
admin.site.register(UserAddress, UserAddressAdmin)
admin.site.register(LoyaltyTransaction, LoyaltyTransactionAdmin)
