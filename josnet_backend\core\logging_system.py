"""
Système de journalisation avancé pour JosNet Network.
"""

import logging
import os
from datetime import datetime
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

# Import du modèle ActivityLog depuis models.py
from .models import ActivityLog



class SystemLogger:
    """
    Classe utilitaire pour la journalisation système.
    """
    
    @staticmethod
    def log_activity(action_type, description, user=None, severity='info', 
                    details=None, request=None, object_type=None, object_id=None):
        """
        Enregistre une activité dans le système.
        
        Args:
            action_type: Type d'action (voir ActivityLog.ACTION_TYPES)
            description: Description de l'action
            user: Utilisateur concerné (optionnel)
            severity: Niveau de gravité (info, warning, error, critical)
            details: Détails supplémentaires (dict)
            request: Objet request Django (pour IP, user-agent, etc.)
            object_type: Type d'objet concerné
            object_id: ID de l'objet concerné
        """
        try:
            # Préparer les données
            log_data = {
                'action_type': action_type,
                'description': description,
                'severity': severity,
                'details': details or {},
                'object_type': object_type or '',
                'object_id': str(object_id) if object_id else '',
            }
            
            # Informations utilisateur
            if user:
                log_data['user'] = user
                log_data['user_email'] = user.email
            
            # Informations de la requête
            if request:
                log_data['ip_address'] = SystemLogger._get_client_ip(request)
                log_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')
                log_data['request_path'] = request.path
                
                # Si pas d'utilisateur fourni, essayer de le récupérer de la requête
                if not user and hasattr(request, 'user') and request.user.is_authenticated:
                    log_data['user'] = request.user
                    log_data['user_email'] = request.user.email
            
            # Créer l'entrée de log
            ActivityLog.objects.create(**log_data)
            
            # Log également dans les fichiers Django
            logger = logging.getLogger('josnet.activity')
            log_message = f"{action_type}: {description}"
            if user:
                log_message += f" (User: {user.email})"
            
            if severity == 'info':
                logger.info(log_message)
            elif severity == 'warning':
                logger.warning(log_message)
            elif severity == 'error':
                logger.error(log_message)
            elif severity == 'critical':
                logger.critical(log_message)
                
        except Exception as e:
            # En cas d'erreur de logging, utiliser le logger standard
            logging.error(f"Erreur lors de l'enregistrement de l'activité: {e}")
    
    @staticmethod
    def _get_client_ip(request):
        """Récupère l'adresse IP du client."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def log_user_login(user, request=None):
        """Log une connexion utilisateur."""
        SystemLogger.log_activity(
            action_type='user_login',
            description=f"Connexion réussie de l'utilisateur {user.email}",
            user=user,
            request=request,
            details={'login_time': timezone.now().isoformat()}
        )
    
    @staticmethod
    def log_user_logout(user, request=None):
        """Log une déconnexion utilisateur."""
        SystemLogger.log_activity(
            action_type='user_logout',
            description=f"Déconnexion de l'utilisateur {user.email}",
            user=user,
            request=request,
            details={'logout_time': timezone.now().isoformat()}
        )
    
    @staticmethod
    def log_user_registration(user, request=None):
        """Log une inscription utilisateur."""
        SystemLogger.log_activity(
            action_type='user_register',
            description=f"Nouvel utilisateur inscrit: {user.email}",
            user=user,
            request=request,
            details={
                'registration_time': timezone.now().isoformat(),
                'user_name': user.get_full_name()
            }
        )
    
    @staticmethod
    def log_email_sent(email_type, recipient, subject, success=True, error=None):
        """Log l'envoi d'un email."""
        action_type = 'email_sent' if success else 'email_failed'
        severity = 'info' if success else 'error'
        description = f"Email {email_type} {'envoyé' if success else 'échoué'} à {recipient}"
        
        details = {
            'email_type': email_type,
            'recipient': recipient,
            'subject': subject,
            'timestamp': timezone.now().isoformat()
        }
        
        if error:
            details['error'] = str(error)
        
        SystemLogger.log_activity(
            action_type=action_type,
            description=description,
            severity=severity,
            details=details
        )
    
    @staticmethod
    def log_promotion_activity(promotion, action, user=None, request=None):
        """Log les activités liées aux promotions."""
        action_map = {
            'create': 'promotion_create',
            'activate': 'promotion_activate',
            'deactivate': 'promotion_deactivate'
        }
        
        action_type = action_map.get(action, 'promotion_update')
        description = f"Promotion '{promotion.title}' {action}"
        
        SystemLogger.log_activity(
            action_type=action_type,
            description=description,
            user=user,
            request=request,
            object_type='Promotion',
            object_id=promotion.id,
            details={
                'promotion_title': promotion.title,
                'discount_percentage': str(promotion.discount_percentage),
                'start_date': promotion.start_date.isoformat() if promotion.start_date else None,
                'end_date': promotion.end_date.isoformat() if promotion.end_date else None
            }
        )
    
    @staticmethod
    def log_product_activity(product, action, user=None, request=None):
        """Log les activités liées aux produits."""
        action_map = {
            'create': 'product_create',
            'update': 'product_update',
            'delete': 'product_delete'
        }
        
        action_type = action_map.get(action, 'product_update')
        description = f"Produit '{product.name}' {action}"
        
        SystemLogger.log_activity(
            action_type=action_type,
            description=description,
            user=user,
            request=request,
            object_type='Product',
            object_id=product.id,
            details={
                'product_name': product.name,
                'product_sku': product.sku,
                'price': str(product.price),
                'status': product.status
            }
        )
    
    @staticmethod
    def log_cms_activity(content_object, action, user=None, request=None):
        """Log les activités CMS."""
        object_type = content_object.__class__.__name__
        action_type = f"cms_{object_type.lower()}_{action}"
        
        if hasattr(content_object, 'title'):
            title = content_object.title
        elif hasattr(content_object, 'name'):
            title = content_object.name
        else:
            title = str(content_object)
        
        description = f"{object_type} '{title}' {action}"
        
        SystemLogger.log_activity(
            action_type=action_type,
            description=description,
            user=user,
            request=request,
            object_type=object_type,
            object_id=content_object.id,
            details={
                'title': title,
                'status': getattr(content_object, 'status', 'N/A')
            }
        )
    
    @staticmethod
    def log_system_error(error_message, details=None, severity='error'):
        """Log une erreur système."""
        SystemLogger.log_activity(
            action_type='system_error',
            description=f"Erreur système: {error_message}",
            severity=severity,
            details=details or {}
        )
    
    @staticmethod
    def log_security_alert(alert_message, user=None, request=None, details=None):
        """Log une alerte de sécurité."""
        SystemLogger.log_activity(
            action_type='security_alert',
            description=f"Alerte sécurité: {alert_message}",
            user=user,
            request=request,
            severity='warning',
            details=details or {}
        )
