import { ProductListItem } from "@/services/productApi";
import { But<PERSON> } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useCart } from "@/contexts/CartContext";
import { Link } from "react-router-dom";
import React, { useState, useEffect } from "react";
import { formatImageUrl, handleImageError, getImageUrl } from "@/utils/imageUtils";
import { formatCurrency } from "@/utils/formatters";
import { API_BASE_URL } from "@/services/apiConfig";
import ProductImage from "./ProductImage";

interface ProductCardProps {
  product: ProductListItem;
}

const ProductCard = ({ product }: ProductCardProps) => {
  const { addToCart } = useCart();
  const [isAdding, setIsAdding] = useState(false);

  // Inspecter la structure complète du produit pour le débogage
  useEffect(() => {
    console.log('Structure complète du produit:', product);
    console.log('primary_image:', product.primary_image);
    
    if (product.primary_image) {
      console.log('image URL:', product.primary_image.image);
      
      // Tester différentes combinaisons d'URLs pour trouver celle qui fonctionne
      const testImageUrls = [
        product.primary_image.image, // URL originale
        `http://localhost:8000${product.primary_image.image}`, // Préfixe avec l'URL de l'API
        `http://localhost:8000/media${product.primary_image.image}`, // Préfixe avec l'URL de l'API + /media
        `http://localhost:8000/media/products${product.primary_image.image}`, // Préfixe avec l'URL de l'API + /media/products
        `http://localhost:8000/media/products/${product.id}/${product.primary_image.id}.jpg`, // Construction basée sur les IDs
      ];
      
      console.log('URLs d\'image à tester:', testImageUrls);
      
      // Tester chaque URL avec une requête fetch pour voir si elle est accessible
      testImageUrls.forEach((url, index) => {
        const img = new Image();
        img.onload = () => console.log(`URL #${index} fonctionne:`, url);
        img.onerror = () => console.log(`URL #${index} ne fonctionne pas:`, url);
        img.src = url;
      });
    }
  }, [product]);

  const handleAddToCart = async () => {
    setIsAdding(true);
    try {
      await addToCart(product.id, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg">
      <Link to={`/product/${product.id}`}>
        <div className="h-48 overflow-hidden relative">
          <ProductImage
            product={product}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          />
          {product.is_featured && (
            <span className="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">
              Populaire
            </span>
          )}
          {product.in_stock === false && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <span className="text-white font-medium">Indisponible</span>
            </div>
          )}
        </div>
      </Link>
      <CardContent className="p-4">
        <div className="mb-2">
          <span className="text-sm text-gray-500">
            {product.categories && product.categories.length > 0 
              ? product.categories[0].name 
              : 'Non catégorisé'}
          </span>
        </div>
        <Link to={`/product/${product.id}`}>
          <h3 className="font-medium text-lg mb-2 line-clamp-1 hover:text-primary transition-colors">
            {product.name}
          </h3>
        </Link>
        <div 
          className="text-gray-600 text-sm mb-4 line-clamp-2" 
          dangerouslySetInnerHTML={{ __html: product.short_description || '' }}
        />
        <div className="flex items-center justify-between">
          <span className="text-lg font-bold text-primary-dark">
            {formatCurrency(product.current_price !== undefined ? product.current_price : product.price || 0)}
          </span>
          <Button
            size="sm"
            className="bg-primary hover:bg-primary-dark text-white"
            disabled={product.in_stock === false || isAdding}
            onClick={handleAddToCart}
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            {isAdding ? "Ajout..." : "Ajouter"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
