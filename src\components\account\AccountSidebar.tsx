
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { LayoutDashboard, FileText, User, Package, Download, Star, MessageSquare, Bell } from "lucide-react";

const sidebarItems = [
  {
    title: "Tableau de bord",
    href: "/account",
    icon: LayoutDashboard
  },
  {
    title: "Historique des commandes",
    href: "/account/orders",
    icon: FileText
  },
  {
    title: "Messages",
    href: "/account/messages",
    icon: MessageSquare
  },
  {
    title: "Notifications",
    href: "/account/notifications",
    icon: Bell
  },
  {
    title: "Profil et adresses",
    href: "/account/profile",
    icon: User
  },
  {
    title: "Retours / Réclamations",
    href: "/account/returns",
    icon: Package
  },
  {
    title: "Factures",
    href: "/account/invoices",
    icon: Download
  },
  {
    title: "Suggestions",
    href: "/account/suggestions",
    icon: Star
  }
];

const AccountSidebar = () => {
  const location = useLocation();

  return (
    <div className="w-full md:w-64 bg-white p-4 border-r border-gray-200 h-full">
      <h2 className="text-xl font-semibold mb-6 text-primary">Mon Compte</h2>

      <nav className="space-y-2">
        {sidebarItems.map((item) => {
          const isActive = location.pathname === item.href;
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center gap-3 px-4 py-3 rounded-md transition-colors hover:bg-gray-100",
                isActive ? "bg-primary/10 text-primary font-medium" : "text-gray-700"
              )}
            >
              <item.icon className="h-5 w-5" />
              <span>{item.title}</span>
            </Link>
          );
        })}
      </nav>
    </div>
  );
};

export default AccountSidebar;
