import { getAuthToken } from '@/utils/auth';
import axios from 'axios';
import { API_URL } from '../config';

// Types
export interface AdminUserListParams {
  page?: number;
  page_size?: number;
  search?: string;
  role?: string;
  is_active?: boolean;
  is_verified?: boolean;
  ordering?: string;
}

export interface AdminUserListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: AdminUser[];
}

export interface AdminUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  role: string;
  phone_number: string | null;
  date_of_birth: string | null;
  profile_picture: string | null;
  is_active: boolean;
  is_verified: boolean;
  loyalty_points: number;
  loyalty_tier: string;
  created_at: string;
  updated_at: string;
  order_count: number;
  total_spent: number;
}

export interface AdminUserCreateUpdate {
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  phone_number?: string | null;
  date_of_birth?: string | null;
  profile_picture?: File | null;
  password?: string;
  confirm_password?: string;
  is_active?: boolean;
  is_verified?: boolean;
}

export interface AdminUserStats {
  total_users: number;
  active_users: number;
  inactive_users: number;
  verified_users: number;
  unverified_users: number;
  role_distribution: {
    customer: number;
    staff: number;
    admin: number;
  };
  recent_users: AdminUser[];
  top_customers: {
    user: AdminUser;
    order_count: number;
    total_spent: number;
  }[];
}

// Admin User API service
const adminUserApi = {
  // Get users list with filtering and pagination
  getUsers: async (params: AdminUserListParams = {}): Promise<AdminUserListResponse> => {
    try {
      // Convertir les paramètres pour l'API
      const apiParams: Record<string, any> = {
        ...params,
        // Si role est 'all', ne pas l'inclure dans les paramètres
        role: params.role && params.role !== 'all' ? params.role : undefined,
      };

      // Supprimer les paramètres undefined
      Object.keys(apiParams).forEach(key => {
        if (apiParams[key] === undefined) {
          delete apiParams[key];
        }
      });

      const response = await axios.get(`${API_URL}/auth/admin/users/`, {
        params: apiParams,
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Simulation de données pour le développement frontend
      const mockUsers: AdminUser[] = Array.from({ length: 10 }, (_, i) => ({
        id: i + 1,
        email: `user${i + 1}@example.com`,
        first_name: `Prénom${i + 1}`,
        last_name: `Nom${i + 1}`,
        full_name: `Prénom${i + 1} Nom${i + 1}`,
        role: i % 3 === 0 ? 'admin' : i % 3 === 1 ? 'staff' : 'customer',
        phone_number: `+33 6 12 34 56 ${i < 10 ? '0' + i : i}`,
        date_of_birth: `1990-01-${i < 9 ? '0' + (i + 1) : (i + 1)}`,
        profile_picture: null,
        is_active: i % 4 !== 0,
        is_verified: i % 3 !== 0,
        loyalty_points: i * 100,
        loyalty_tier: i > 7 ? 'Gold' : i > 4 ? 'Silver' : 'Bronze',
        created_at: `2023-0${(i % 9) + 1}-${i < 9 ? '0' + (i + 1) : (i + 1)}T10:00:00Z`,
        updated_at: `2023-0${(i % 9) + 1}-${i < 9 ? '0' + (i + 1) : (i + 1)}T10:00:00Z`,
        order_count: i * 2,
        total_spent: i * 100,
      }));

      // Filtrage simulé
      let filteredUsers = [...mockUsers];

      if (params.search) {
        const searchLower = params.search.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.email.toLowerCase().includes(searchLower) ||
          user.first_name.toLowerCase().includes(searchLower) ||
          user.last_name.toLowerCase().includes(searchLower) ||
          user.full_name.toLowerCase().includes(searchLower)
        );
      }

      if (params.role && params.role !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.role === params.role);
      }

      if (params.is_active !== undefined) {
        filteredUsers = filteredUsers.filter(user => user.is_active === params.is_active);
      }

      if (params.is_verified !== undefined) {
        filteredUsers = filteredUsers.filter(user => user.is_verified === params.is_verified);
      }

      // Pagination simulée
      const page = params.page || 1;
      const pageSize = params.page_size || 10;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

      return {
        count: filteredUsers.length,
        next: endIndex < filteredUsers.length ? `?page=${page + 1}` : null,
        previous: page > 1 ? `?page=${page - 1}` : null,
        results: paginatedUsers,
      };
    }
  },

  // Get a single user by ID
  getUser: async (id: number): Promise<AdminUser> => {
    try {
      const response = await axios.get(`${API_URL}/auth/admin/users/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error fetching user ${id}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Simuler la récupération d'un utilisateur spécifique
      const mockUser: AdminUser = {
        id,
        email: `user${id}@example.com`,
        first_name: `Prénom${id}`,
        last_name: `Nom${id}`,
        full_name: `Prénom${id} Nom${id}`,
        role: id % 3 === 0 ? 'admin' : id % 3 === 1 ? 'staff' : 'customer',
        phone_number: `+33 6 12 34 56 ${id < 10 ? '0' + id : id}`,
        date_of_birth: `1990-01-${id < 9 ? '0' + (id + 1) : (id + 1)}`,
        profile_picture: null,
        is_active: id % 4 !== 0,
        is_verified: id % 3 !== 0,
        loyalty_points: id * 100,
        loyalty_tier: id > 7 ? 'Gold' : id > 4 ? 'Silver' : 'Bronze',
        created_at: `2023-0${(id % 9) + 1}-${id < 9 ? '0' + (id + 1) : (id + 1)}T10:00:00Z`,
        updated_at: `2023-0${(id % 9) + 1}-${id < 9 ? '0' + (id + 1) : (id + 1)}T10:00:00Z`,
        order_count: id * 2,
        total_spent: id * 100,
      };

      return mockUser;
    }
  },

  // Create a new user
  createUser: async (userData: AdminUserCreateUpdate): Promise<AdminUser> => {
    try {
      // Handle file upload if profile_picture is a File
      let formData = null;
      if (userData.profile_picture instanceof File) {
        formData = new FormData();
        Object.entries(userData).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (key === 'profile_picture' && value instanceof File) {
              formData!.append(key, value);
            } else {
              formData!.append(key, String(value));
            }
          }
        });
      }

      const response = await axios.post(
        `${API_URL}/auth/admin/users/`,
        formData || userData,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            ...(formData ? { 'Content-Type': 'multipart/form-data' } : {})
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Retourner un utilisateur simulé
      return {
        id: Math.floor(Math.random() * 1000) + 100,
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        full_name: `${userData.first_name} ${userData.last_name}`,
        role: userData.role,
        phone_number: userData.phone_number || null,
        date_of_birth: userData.date_of_birth || null,
        profile_picture: null,
        is_active: userData.is_active !== undefined ? userData.is_active : true,
        is_verified: userData.is_verified !== undefined ? userData.is_verified : false,
        loyalty_points: 0,
        loyalty_tier: 'Bronze',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        order_count: 0,
        total_spent: 0,
      };
    }
  },

  // Update an existing user
  updateUser: async (id: number, userData: Partial<AdminUserCreateUpdate>): Promise<AdminUser> => {
    try {
      // Handle file upload if profile_picture is a File
      let formData = null;
      if (userData.profile_picture instanceof File) {
        formData = new FormData();
        Object.entries(userData).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (key === 'profile_picture' && value instanceof File) {
              formData!.append(key, value);
            } else {
              formData!.append(key, String(value));
            }
          }
        });
      }

      const response = await axios.patch(
        `${API_URL}/auth/admin/users/${id}/`,
        formData || userData,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            ...(formData ? { 'Content-Type': 'multipart/form-data' } : {})
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error updating user ${id}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Récupérer l'utilisateur actuel
      const currentUser = await adminUserApi.getUser(id);

      // Retourner l'utilisateur mis à jour avec gestion du type profile_picture
      const updatedUser: AdminUser = {
        ...currentUser,
        email: userData.email || currentUser.email,
        first_name: userData.first_name || currentUser.first_name,
        last_name: userData.last_name || currentUser.last_name,
        role: userData.role || currentUser.role,
        phone_number: userData.phone_number !== undefined ? userData.phone_number : currentUser.phone_number,
        date_of_birth: userData.date_of_birth !== undefined ? userData.date_of_birth : currentUser.date_of_birth,
        is_active: userData.is_active !== undefined ? userData.is_active : currentUser.is_active,
        is_verified: userData.is_verified !== undefined ? userData.is_verified : currentUser.is_verified,
        full_name: `${userData.first_name || currentUser.first_name} ${userData.last_name || currentUser.last_name}`,
        updated_at: new Date().toISOString(),
      };

      return updatedUser;
    }
  },

  // Delete a user
  deleteUser: async (id: number): Promise<void> => {
    try {
      await axios.delete(`${API_URL}/auth/admin/users/${id}/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
    } catch (error) {
      console.error(`Error deleting user ${id}:`, error);

      // En cas d'erreur, simuler la suppression
      console.warn('Using mock deletion as fallback');

      // Simuler un délai réseau
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Pas de retour pour une suppression
    return;
  },

  // Get user addresses
  getUserAddresses: async (userId: number): Promise<any[]> => {
    try {
      const response = await axios.get(`${API_URL}/auth/admin/users/${userId}/addresses/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching addresses for user ${userId}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Retourner des adresses simulées
      return Array.from({ length: 2 }, (_, i) => ({
        id: i + 1,
        user: userId,
        address_type: i === 0 ? 'shipping' : 'billing',
        is_default: i === 0,
        first_name: `Prénom${userId}`,
        last_name: `Nom${userId}`,
        address_line1: `${i + 1} Rue de la Paix`,
        address_line2: i === 0 ? 'Apt 42' : null,
        city: 'Paris',
        state: 'Île-de-France',
        postal_code: '75001',
        country: 'France',
        phone_number: `+33 6 12 34 56 ${userId < 10 ? '0' + userId : userId}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));
    }
  },

  // Get user loyalty transactions
  getUserLoyaltyTransactions: async (userId: number): Promise<any[]> => {
    try {
      const response = await axios.get(`${API_URL}/auth/admin/users/${userId}/loyalty-transactions/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching loyalty transactions for user ${userId}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Retourner des transactions simulées
      return Array.from({ length: 5 }, (_, i) => ({
        id: i + 1,
        user: userId,
        points: (i + 1) * 50,
        transaction_type: i % 2 === 0 ? 'earned' : 'redeemed',
        description: i % 2 === 0 ? `Achat de produit #${i + 100}` : `Utilisation pour réduction`,
        order_id: i % 2 === 0 ? i + 100 : null,
        created_at: new Date(Date.now() - i * 86400000).toISOString(), // Jours précédents
      }));
    }
  },

  // Add loyalty points to a user
  addUserLoyaltyPoints: async (userId: number, points: number, description: string): Promise<any> => {
    try {
      const response = await axios.post(
        `${API_URL}/auth/admin/users/${userId}/add-loyalty-points/`,
        { points, description },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error adding loyalty points to user ${userId}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Retourner la transaction simulée
      return {
        id: Math.floor(Math.random() * 1000) + 100,
        user: userId,
        points: points,
        transaction_type: 'earned',
        description: description,
        order_id: null,
        created_at: new Date().toISOString(),
      };
    }
  },

  // Get user orders
  getUserOrders: async (userId: number): Promise<any> => {
    try {
      const response = await axios.get(`${API_URL}/orders/orders/`, {
        params: { user: userId },
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching orders for user ${userId}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Retourner des commandes simulées
      const orders = Array.from({ length: 3 }, (_, i) => ({
        id: i + 100,
        order_number: `ORD-${userId}-${i + 100}`,
        user: userId,
        status: i === 0 ? 'delivered' : i === 1 ? 'processing' : 'pending',
        status_display: i === 0 ? 'Livré' : i === 1 ? 'En traitement' : 'En attente',
        total: (i + 1) * 100,
        shipping_address: 1,
        billing_address: 2,
        payment_method: 'card',
        shipping_method: 'standard',
        created_at: new Date(Date.now() - i * 86400000 * 7).toISOString(), // Semaines précédentes
        updated_at: new Date(Date.now() - i * 86400000 * 7).toISOString(),
      }));

      return {
        count: orders.length,
        next: null,
        previous: null,
        results: orders
      };
    }
  },

  // Get user statistics
  getUserStats: async (): Promise<AdminUserStats> => {
    try {
      const response = await axios.get(`${API_URL}/auth/admin/users/stats/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user statistics:', error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Retourner des statistiques simulées
      return {
        total_users: 150,
        active_users: 120,
        inactive_users: 30,
        verified_users: 100,
        unverified_users: 50,
        role_distribution: {
          customer: 130,
          staff: 15,
          admin: 5,
        },
        recent_users: Array.from({ length: 5 }, (_, i) => ({
          id: i + 1,
          email: `recent${i + 1}@example.com`,
          first_name: `Prénom${i + 1}`,
          last_name: `Nom${i + 1}`,
          full_name: `Prénom${i + 1} Nom${i + 1}`,
          role: 'customer',
          phone_number: null,
          date_of_birth: null,
          profile_picture: null,
          is_active: true,
          is_verified: false,
          loyalty_points: 0,
          loyalty_tier: 'Bronze',
          created_at: new Date(Date.now() - i * 86400000).toISOString(),
          updated_at: new Date(Date.now() - i * 86400000).toISOString(),
          order_count: 0,
          total_spent: 0,
        })),
        top_customers: Array.from({ length: 5 }, (_, i) => ({
          user: {
            id: i + 10,
            email: `top${i + 1}@example.com`,
            first_name: `TopPrénom${i + 1}`,
            last_name: `TopNom${i + 1}`,
            full_name: `TopPrénom${i + 1} TopNom${i + 1}`,
            role: 'customer',
            phone_number: null,
            date_of_birth: null,
            profile_picture: null,
            is_active: true,
            is_verified: true,
            loyalty_points: (5 - i) * 500,
            loyalty_tier: i === 0 ? 'Gold' : i <= 2 ? 'Silver' : 'Bronze',
            created_at: new Date(Date.now() - 100 * 86400000).toISOString(),
            updated_at: new Date(Date.now() - 10 * 86400000).toISOString(),
            order_count: (5 - i) * 5,
            total_spent: (5 - i) * 1000,
          },
          order_count: (5 - i) * 5,
          total_spent: (5 - i) * 1000,
        })),
      };
    }
  },

  // Verify a user's email
  verifyUser: async (userId: number): Promise<AdminUser> => {
    try {
      const response = await axios.post(
        `${API_URL}/auth/admin/users/${userId}/verify/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error verifying user ${userId}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Récupérer l'utilisateur actuel
      const user = await adminUserApi.getUser(userId);

      // Retourner l'utilisateur mis à jour
      return {
        ...user,
        is_verified: true,
        updated_at: new Date().toISOString(),
      };
    }
  },

  // Activate or deactivate a user
  setUserActiveStatus: async (userId: number, isActive: boolean): Promise<AdminUser> => {
    try {
      const response = await axios.post(
        `${API_URL}/auth/admin/users/${userId}/set-active/`,
        { is_active: isActive },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error setting active status for user ${userId}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Récupérer l'utilisateur actuel
      const user = await adminUserApi.getUser(userId);

      // Retourner l'utilisateur mis à jour
      return {
        ...user,
        is_active: isActive,
        updated_at: new Date().toISOString(),
      };
    }
  },

  // Reset a user's password
  resetUserPassword: async (userId: number): Promise<{ password: string }> => {
    try {
      const response = await axios.post(
        `${API_URL}/auth/admin/users/${userId}/reset-password/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error resetting password for user ${userId}:`, error);

      // En cas d'erreur, utiliser des données simulées comme fallback
      console.warn('Using mock data as fallback');

      // Générer un mot de passe aléatoire
      const password = Math.random().toString(36).slice(-10);

      // Retourner le nouveau mot de passe
      return { password };
    }
  }
};

export default adminUserApi;
