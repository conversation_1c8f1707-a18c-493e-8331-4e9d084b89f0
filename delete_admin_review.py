#!/usr/bin/env python3
"""
Script pour supprimer l'avis de l'admin et permettre d'en créer un nouveau
"""

import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User
from products.models import Product, ProductReview

def delete_admin_review():
    """Supprimer l'avis de l'admin pour le produit 1"""
    print("🗑️ SUPPRESSION DE L'AVIS DE L'ADMIN")
    print("=" * 50)
    
    try:
        # Récupérer l'admin et le produit
        admin_user = User.objects.get(email="<EMAIL>")
        product = Product.objects.get(id=1)
        
        # Trouver l'avis de l'admin pour ce produit
        admin_review = ProductReview.objects.filter(
            product=product,
            user=admin_user
        ).first()
        
        if admin_review:
            print(f"📋 Avis trouvé:")
            print(f"   🆔 ID: {admin_review.id}")
            print(f"   ⭐ Note: {admin_review.rating}")
            print(f"   📝 Titre: {admin_review.title}")
            print(f"   📅 Date: {admin_review.created_at}")
            
            # Supprimer l'avis
            admin_review.delete()
            
            print(f"\n✅ AVIS SUPPRIMÉ AVEC SUCCÈS!")
            print(f"   🎉 Vous pouvez maintenant écrire un nouvel avis")
            print(f"   🌐 Allez sur: http://localhost:8080/product/1")
            print(f"   ✍️ Cliquez sur 'Écrire un avis'")
            
            return True
        else:
            print(f"❌ Aucun avis trouvé pour l'admin sur ce produit")
            return False
            
    except User.DoesNotExist:
        print(f"❌ Utilisateur admin non trouvé")
        return False
    except Product.DoesNotExist:
        print(f"❌ Produit non trouvé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    delete_admin_review()
