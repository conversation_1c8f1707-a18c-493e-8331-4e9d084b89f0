import { useEffect } from "react";
import { Link } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, ArrowRight, ShoppingBag } from "lucide-react";
import { useCart } from "@/contexts/CartContext";
import { toast } from "@/hooks/use-toast";
import React from "react"; // Added the missing React import
import { formatCurrency } from "@/utils/formatters";
import { useTranslation } from "@/contexts/TranslationContext";

const Cart = () => {
  const { t } = useTranslation();
  const {
    cartItems,
    removeFromCart,
    updateQuantity,
    cartTotal,
    cartCount,
    applyPromoCode,
    promoApplied,
    promoDiscount
  } = useCart();
  
  const [promoCode, setPromoCode] = React.useState("");
  
  // Calculate shipping and total
  const subtotal = cartTotal;
  const shippingCost = subtotal > 100 ? 0 : 15; // Renamed from shipping to shippingCost
  const discount = promoApplied ? subtotal * promoDiscount : 0;
  const total = subtotal + shippingCost - discount;

  // Apply promo code
  const handleApplyPromo = async () => {
    // Désactiver temporairement le bouton pendant la validation
    const button = document.querySelector('button[disabled]');
    if (button) button.setAttribute('disabled', 'true');
    
    try {
      const success = await applyPromoCode(promoCode);
      
      if (success) {
        toast({
          title: "Code promo appliqué",
          description: "La réduction a été appliquée à votre panier",
        });
      } else {
        toast({
          title: "Code promo invalide",
          description: "Le code promo que vous avez saisi n'est pas valide",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Erreur lors de l\'application du code promo:', error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'application du code promo",
        variant: "destructive",
      });
    } finally {
      // Réactiver le bouton après la validation
      if (button) button.removeAttribute('disabled');
    }
  };

  // Message about shipping cost
  const ShippingMessage = () => {
    if (shippingCost === 0) return null;
    
    const amountToFreeShipping = 100 - subtotal;
    if (amountToFreeShipping <= 0) return null;
    
    return (
      <div className="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md text-sm">
        {t("cart.add_amount_for_free_shipping", "Ajoutez")} {amountToFreeShipping.toLocaleString("fr-FR", { style: "currency", currency: "BIF", maximumFractionDigits: 0 })} {t("cart.items_for_free_shipping", "d'articles pour bénéficier de la livraison gratuite!")}
      </div>
    );
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">{t("cart.your_cart", "Votre Panier")}</h1>

        {cartItems.length === 0 ? (
          <div className="text-center py-16">
            <ShoppingBag className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h2 className="text-2xl font-medium mb-2">{t("cart.empty_cart", "Votre panier est vide")}</h2>
            <p className="text-gray-600 mb-8">
              {t("cart.no_products_added", "Vous n'avez pas encore ajouté de produits à votre panier.")}
            </p>
            <Button asChild size="lg">
              <Link to="/products">{t("cart.continue_shopping", "Continuer vos achats")}</Link>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-4 border-b border-gray-200">
                  <h2 className="font-medium">{t("cart.cart_items", "Articles du panier")} ({cartCount})</h2>
                </div>
                
                {/* Cart item list */}
                <ul>
                  {cartItems.map((item) => (
                    <li 
                      key={item.product.id} 
                      className="p-4 border-b border-gray-200 last:border-b-0 flex flex-col sm:flex-row gap-4"
                    >
                      <div className="sm:w-24 flex-shrink-0">
                        <img 
                          src={item.product.image} 
                          alt={item.product.name} 
                          className="w-full h-24 object-cover rounded-md"
                        />
                      </div>
                      
                      <div className="flex-grow">
                        <Link to={`/product/${item.product.id}`} className="font-medium hover:text-primary">
                          {item.product.name}
                        </Link>
                        <p className="text-sm text-gray-600">{item.product.category}</p>
                        <p className="mt-1 font-bold">
                          {formatCurrency(item.product.price)}
                        </p>
                        
                        <div className="flex flex-wrap justify-between items-center mt-3">
                          <div className="flex items-center border border-gray-300 rounded">
                            <button
                              onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                              className="px-3 py-1 border-r border-gray-300"
                              aria-label={t("cart.decrease_quantity", "Diminuer la quantité")}
                            >
                              -
                            </button>
                            <span className="px-4 py-1">{item.quantity}</span>
                            <button
                              onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                              className="px-3 py-1 border-l border-gray-300"
                              aria-label={t("cart.increase_quantity", "Augmenter la quantité")}
                            >
                              +
                            </button>
                          </div>
                          
                          <button
                            onClick={() => removeFromCart(item.product.id)}
                            className="text-red-500 hover:text-red-700 flex items-center"
                            aria-label={t("cart.remove_item", "Supprimer l'article")}
                          >
                            <Trash2 size={16} className="mr-1" />
                            {t("common.delete", "Supprimer")}
                          </button>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="mt-6 flex justify-between">
                <Button variant="outline" asChild>
                  <Link to="/products">{t("cart.continue_shopping", "Continuer vos achats")}</Link>
                </Button>
                <Button asChild>
                  <Link to="/checkout">
                    {t("order.checkout", "Passer à la caisse")}
                    <ArrowRight size={16} className="ml-2" />
                  </Link>
                </Button>
              </div>
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <h2 className="font-medium text-lg mb-4">{t("cart.order_summary", "Récapitulatif de la commande")}</h2>

                <div className="space-y-2 pb-4 border-b border-gray-200">
                  <div className="flex justify-between">
                    <span className="text-gray-600">{t("cart.subtotal", "Sous-total")}</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">{t("cart.shipping_cost", "Frais de livraison")}</span>
                    <span>{shippingCost === 0 ? t("cart.free", "Gratuit") : formatCurrency(shippingCost)}</span>
                  </div>
                  {promoApplied && (
                    <div className="flex justify-between text-green-600">
                      <span>{t("cart.discount", "Réduction")}</span>
                      <span>-{formatCurrency(discount)}</span>
                    </div>
                  )}
                </div>

                <div className="py-4 border-b border-gray-200">
                  <div className="flex justify-between font-bold text-lg">
                    <span>{t("order.total", "Total")}</span>
                    <span>{formatCurrency(total)}</span>
                  </div>
                </div>
                
                {/* Shipping message */}
                <ShippingMessage />
                
                {/* Promo code section */}
                <div className="pt-4">
                  <label htmlFor="promo" className="block text-sm font-medium mb-2">
                    {t("cart.promo_code", "Code promo")}
                  </label>
                  <div className="flex gap-2">
                    <Input
                      id="promo"
                      placeholder={t("cart.enter_promo_code", "Entrez votre code")}
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      disabled={promoApplied}
                    />
                    <Button
                      onClick={handleApplyPromo}
                      disabled={promoApplied || !promoCode}
                      variant="outline"
                    >
                      {t("cart.apply", "Appliquer")}
                    </Button>
                  </div>
                  {promoApplied && (
                    <p className="text-green-600 text-sm mt-2">{t("cart.promo_applied", "Code promo appliqué!")}</p>
                  )}
                </div>
                
                <Button className="w-full mt-6" asChild>
                  <Link to="/checkout">
                    {t("order.checkout", "Passer à la caisse")}
                  </Link>
                </Button>
              </div>
              
              {/* Payment methods */}
              <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <h3 className="font-medium mb-2">{t("cart.accepted_payment_methods", "Modes de paiement acceptés")}</h3>
                <div className="flex gap-2">
                  <div className="bg-gray-100 p-2 rounded">Visa</div>
                  <div className="bg-gray-100 p-2 rounded">MasterCard</div>
                  <div className="bg-gray-100 p-2 rounded">PayPal</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default Cart;
