
import AdminLayout from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Tag, 
  Search, 
  Plus, 
  Edit, 
  Trash2,
  CheckCircle,
  XCircle,
  CalendarClock
} from "lucide-react";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

// Données fictives pour les promotions
const mockPromotions = [
  { 
    id: "PROMO001", 
    code: "BIENVENUE10", 
    description: "10% de réduction pour les nouveaux clients", 
    type: "Pourcentage",
    value: 10,
    minOrder: 0,
    startDate: "01/05/2025",
    endDate: "31/05/2025",
    usageLimit: 100,
    usageCount: 42,
    active: true
  },
  { 
    id: "PROMO002", 
    code: "LIVRAISON", 
    description: "Livraison gratuite dès 50€ d'achat", 
    type: "Livraison",
    value: 100,
    minOrder: 50,
    startDate: "01/05/2025",
    endDate: "15/06/2025",
    usageLimit: 200,
    usageCount: 87,
    active: true
  },
  { 
    id: "PROMO003", 
    code: "ETE25", 
    description: "25€ de réduction sur les commandes de plus de 150€", 
    type: "Montant fixe",
    value: 25,
    minOrder: 150,
    startDate: "01/06/2025",
    endDate: "31/07/2025",
    usageLimit: 300,
    usageCount: 0,
    active: false
  },
  { 
    id: "PROMO004", 
    code: "FIDÉLITÉ15", 
    description: "15% de réduction pour les clients fidèles", 
    type: "Pourcentage",
    value: 15,
    minOrder: 0,
    startDate: "01/05/2025",
    endDate: "31/05/2025",
    usageLimit: 200,
    usageCount: 128,
    active: true
  },
  { 
    id: "PROMO005", 
    code: "SOLDES2025", 
    description: "20% sur tous les produits soldés", 
    type: "Pourcentage",
    value: 20,
    minOrder: 0,
    startDate: "15/04/2025",
    endDate: "15/05/2025",
    usageLimit: 0,
    usageCount: 523,
    active: true
  },
];

const Promotions = () => {
  const [searchQuery, setSearchQuery] = useState("");

  // Fonction de filtrage pour la recherche
  const filteredPromotions = mockPromotions.filter(promo => 
    promo.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
    promo.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleEditPromotion = (id: string) => {
    toast({
      title: "Modification demandée",
      description: `Édition de la promotion ${id} initiée`,
    });
    // Dans une app réelle, cette fonction ouvrirait un formulaire d'édition
  };

  const handleDeletePromotion = (id: string) => {
    toast({
      title: "Suppression demandée",
      description: `Suppression de la promotion ${id} initiée`,
      variant: "destructive",
    });
    // Dans une app réelle, cette fonction afficherait une confirmation
  };

  const handleAddPromotion = () => {
    toast({
      title: "Nouvelle promotion",
      description: "Formulaire de création de promotion ouvert",
    });
    // Dans une app réelle, cette fonction ouvrirait un formulaire de création
  };

  const togglePromoActive = (id: string, currentState: boolean) => {
    toast({
      title: currentState ? "Promotion désactivée" : "Promotion activée",
      description: `La promotion ${id} a été ${currentState ? "désactivée" : "activée"}`,
    });
    // Dans une app réelle, cette fonction activerait/désactiverait la promo
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Promotions</h1>
          <p className="text-gray-500">Gérez vos codes promotionnels et réductions</p>
        </div>

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher une promotion..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={handleAddPromotion} className="flex items-center gap-1">
            <Plus size={16} />
            Nouvelle Promotion
          </Button>
        </div>

        {/* Tableau des promotions */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Code</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Valeur</TableHead>
                <TableHead>Période</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Actif</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPromotions.map((promo) => (
                <TableRow key={promo.id}>
                  <TableCell className="font-medium">
                    <code className="bg-gray-100 px-2 py-0.5 rounded text-xs">{promo.code}</code>
                  </TableCell>
                  <TableCell className="max-w-xs truncate">{promo.description}</TableCell>
                  <TableCell>{promo.type}</TableCell>
                  <TableCell>
                    {promo.type === "Pourcentage" 
                      ? `${promo.value}%` 
                      : promo.type === "Montant fixe"
                      ? `${promo.value}€`
                      : "Gratuite"}
                  </TableCell>
                  <TableCell className="whitespace-nowrap">
                    <div className="flex items-center gap-1">
                      <CalendarClock size={14} className="text-gray-500" />
                      <span>{promo.startDate} - {promo.endDate}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {promo.usageCount}/{promo.usageLimit === 0 ? "∞" : promo.usageLimit}
                  </TableCell>
                  <TableCell>
                    {promo.active ? (
                      <CheckCircle size={18} className="text-green-500" />
                    ) : (
                      <XCircle size={18} className="text-red-500" />
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-xs"
                        onClick={() => togglePromoActive(promo.id, promo.active)}
                      >
                        {promo.active ? "Désactiver" : "Activer"}
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleEditPromotion(promo.id)}
                      >
                        <Edit size={16} className="text-blue-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleDeletePromotion(promo.id)}
                      >
                        <Trash2 size={16} className="text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Indication du nombre de promotions */}
        <div className="text-sm text-gray-500">
          Affichage de {filteredPromotions.length} promotions sur {mockPromotions.length}
        </div>
      </div>
    </AdminLayout>
  );
};

export default Promotions;
