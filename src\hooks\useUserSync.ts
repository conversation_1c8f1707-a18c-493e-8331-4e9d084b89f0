import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import userApi from '@/services/userApi';

/**
 * Hook personnalisé pour synchroniser les données utilisateur
 * entre React Query et AuthContext
 */
export const useUserSync = () => {
  const { user, updateUser, isAuthenticated } = useAuth();
  
  // Récupérer le profil utilisateur via React Query
  const { data: profile } = useQuery({
    queryKey: ['userProfile'],
    queryFn: userApi.getProfile,
    enabled: isAuthenticated, // Seulement si l'utilisateur est connecté
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Synchroniser automatiquement quand le profil change
  useEffect(() => {
    if (profile && user) {
      // Vérifier si les données ont changé
      const hasChanged = 
        profile.first_name !== user.first_name ||
        profile.last_name !== user.last_name ||
        profile.phone_number !== user.phone_number ||
        profile.date_of_birth !== user.date_of_birth;
      
      if (hasChanged) {
        console.log('🔄 Synchronisation des données utilisateur avec AuthContext');
        updateUser({
          first_name: profile.first_name,
          last_name: profile.last_name,
          phone_number: profile.phone_number,
          date_of_birth: profile.date_of_birth,
        });
      }
    }
  }, [profile, user, updateUser]);
  
  return {
    profile,
    isProfileLoading: !profile && isAuthenticated,
  };
};
