from django.db.models import Count, Sum, Avg, F
from rest_framework import viewsets, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from datetime import datetime, timedelta
from .models import ProductView, CartAction, WishlistAction, SalesData
from .serializers import (
    ProductViewSerializer, CartActionSerializer, WishlistActionSerializer,
    SalesDataSerializer, ProductSalesStatsSerializer, CustomerProductInsightSerializer,
    ProductDashboardDataSerializer
)
from products.models import Product, ProductVariant
from authentication.permissions import IsStaffOrAdmin

class ProductSalesStatsView(APIView):
    """
    API endpoint for product sales statistics.
    """
    permission_classes = [IsStaffOrAdmin]
    
    def get(self, request, pk):
        """
        Get sales statistics for a product.
        
        Parameters:
        - pk: Product ID
        - period: Time period for data aggregation (day, week, month)
        """
        period = request.query_params.get('period', 'month')
        
        try:
            product = Product.objects.get(pk=pk)
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get sales data by period
        sales_data = SalesData.get_sales_by_period(pk, period)
        
        # Calculate overall statistics
        total_sales = SalesData.objects.filter(product_id=pk)
        total_orders = total_sales.values('order_id').distinct().count()
        total_quantity = total_sales.aggregate(total=Sum('quantity'))['total'] or 0
        total_revenue = total_sales.aggregate(total=Sum('total_price'))['total'] or 0
        avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
        
        # Get last order date
        last_order = total_sales.order_by('-date').first()
        last_order_date = last_order.date if last_order else None
        
        # Calculate sales trend
        now = datetime.now()
        if period == 'day':
            current_period_start = now - timedelta(days=14)
            previous_period_start = current_period_start - timedelta(days=14)
        elif period == 'week':
            current_period_start = now - timedelta(weeks=12)
            previous_period_start = current_period_start - timedelta(weeks=12)
        else:  # month
            current_period_start = now - timedelta(days=365)
            previous_period_start = current_period_start - timedelta(days=365)
        
        current_period_sales = SalesData.objects.filter(
            product_id=pk,
            date__gte=current_period_start.date()
        ).aggregate(total=Sum('total_price'))['total'] or 0
        
        previous_period_sales = SalesData.objects.filter(
            product_id=pk,
            date__gte=previous_period_start.date(),
            date__lt=current_period_start.date()
        ).aggregate(total=Sum('total_price'))['total'] or 0
        
        sales_trend = 0
        if previous_period_sales > 0:
            sales_trend = ((current_period_sales - previous_period_sales) / previous_period_sales) * 100
        
        # Get sales by variant
        sales_by_variant = []
        variants = ProductVariant.objects.filter(product_id=pk)
        for variant in variants:
            variant_sales = SalesData.objects.filter(variant_id=variant.id)
            variant_quantity = variant_sales.aggregate(total=Sum('quantity'))['total'] or 0
            variant_revenue = variant_sales.aggregate(total=Sum('total_price'))['total'] or 0
            
            sales_by_variant.append({
                'variant_id': variant.id,
                'variant_name': variant.name,
                'quantity_sold': variant_quantity,
                'revenue': variant_revenue
            })
        
        # Get top customers
        top_customers = SalesData.objects.filter(product_id=pk).values('customer_id').annotate(
            total_orders=Count('order_id', distinct=True),
            total_quantity=Sum('quantity'),
            total_spent=Sum('total_price')
        ).order_by('-total_spent')[:5]
        
        # Get related products sales
        related_products_sales = []
        related_products = Product.objects.filter(
            related_to_products__product_id=pk
        ).values('id', 'name')
        
        for related_product in related_products:
            related_sales = SalesData.objects.filter(product_id=related_product['id'])
            related_quantity = related_sales.aggregate(total=Sum('quantity'))['total'] or 0
            related_revenue = related_sales.aggregate(total=Sum('total_price'))['total'] or 0
            
            related_products_sales.append({
                'product_id': related_product['id'],
                'product_name': related_product['name'],
                'quantity_sold': related_quantity,
                'revenue': related_revenue
            })
        
        # Prepare response data
        data = {
            'product_id': product.id,
            'product_name': product.name,
            'total_orders': total_orders,
            'total_quantity_sold': total_quantity,
            'total_revenue': total_revenue,
            'average_order_value': avg_order_value,
            'last_order_date': last_order_date,
            'sales_trend': sales_trend,
            'sales_by_period': list(sales_data),
            'sales_by_variant': sales_by_variant,
            'top_customers': list(top_customers),
            'related_products_sales': related_products_sales
        }
        
        serializer = ProductSalesStatsSerializer(data)
        return Response(serializer.data)

class CustomerProductInsightView(APIView):
    """
    API endpoint for customer insights about a product.
    """
    permission_classes = [IsStaffOrAdmin]
    
    def get(self, request, pk):
        """
        Get customer insights for a product.
        
        Parameters:
        - pk: Product ID
        """
        try:
            product = Product.objects.get(pk=pk)
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get view statistics
        views = ProductView.objects.filter(product_id=pk)
        views_count = views.count()
        unique_viewers = views.values('user_id', 'session_id').distinct().count()
        
        # Get cart statistics
        cart_actions = CartAction.objects.filter(product_id=pk)
        add_to_cart_count = cart_actions.filter(action='add').count()
        
        # Get wishlist statistics
        wishlist_actions = WishlistAction.objects.filter(product_id=pk)
        add_to_wishlist_count = wishlist_actions.filter(action='add').count()
        
        # Get purchase statistics
        sales = SalesData.objects.filter(product_id=pk)
        purchase_count = sales.values('order_id').distinct().count()
        
        # Calculate conversion rates
        conversion_rate = (purchase_count / views_count) * 100 if views_count > 0 else 0
        cart_abandonment_rate = ((add_to_cart_count - purchase_count) / add_to_cart_count) * 100 if add_to_cart_count > 0 else 0
        
        # Get review statistics
        reviews = product.reviews.all()
        reviews_count = reviews.count()
        average_rating = reviews.aggregate(avg=Avg('rating'))['avg'] or 0
        
        # Prepare response data
        data = {
            'product_id': product.id,
            'product_name': product.name,
            'views_count': views_count,
            'unique_viewers_count': unique_viewers,
            'add_to_cart_count': add_to_cart_count,
            'add_to_wishlist_count': add_to_wishlist_count,
            'purchase_count': purchase_count,
            'conversion_rate': conversion_rate,
            'cart_abandonment_rate': cart_abandonment_rate,
            'average_rating': average_rating,
            'reviews_count': reviews_count,
            'demographic_data': {
                'age_groups': [
                    {'group': '18-24', 'percentage': 15},
                    {'group': '25-34', 'percentage': 35},
                    {'group': '35-44', 'percentage': 25},
                    {'group': '45-54', 'percentage': 15},
                    {'group': '55+', 'percentage': 10}
                ],
                'gender': [
                    {'gender': 'Homme', 'percentage': 55},
                    {'gender': 'Femme', 'percentage': 45}
                ],
                'locations': [
                    {'location': 'Paris', 'percentage': 30},
                    {'location': 'Lyon', 'percentage': 15},
                    {'location': 'Marseille', 'percentage': 10},
                    {'location': 'Bordeaux', 'percentage': 8},
                    {'location': 'Autres', 'percentage': 37}
                ]
            },
            'customer_segments': [
                {'segment': 'Nouveaux clients', 'percentage': 40},
                {'segment': 'Clients fidèles', 'percentage': 35},
                {'segment': 'Clients occasionnels', 'percentage': 25}
            ],
            'similar_interests': [
                {'category': 'Électronique', 'percentage': 75},
                {'category': 'Informatique', 'percentage': 60},
                {'category': 'Gadgets', 'percentage': 45}
            ]
        }
        
        serializer = CustomerProductInsightSerializer(data)
        return Response(serializer.data)

class ProductDashboardDataView(APIView):
    """
    API endpoint for product dashboard data.
    """
    permission_classes = [IsStaffOrAdmin]
    
    def get(self, request, pk):
        """
        Get dashboard data for a product.
        
        Parameters:
        - pk: Product ID
        - period: Time period (7d, 30d, 90d, 1y)
        """
        period = request.query_params.get('period', '30d')
        
        try:
            product = Product.objects.get(pk=pk)
        except Product.DoesNotExist:
            return Response(
                {"error": "Product not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Determine the period
        now = datetime.now()
        if period == '7d':
            start_date = now - timedelta(days=7)
        elif period == '30d':
            start_date = now - timedelta(days=30)
        elif period == '90d':
            start_date = now - timedelta(days=90)
        elif period == '1y':
            start_date = now - timedelta(days=365)
        else:
            start_date = now - timedelta(days=30)  # Default: 30 days
        
        # Get key metrics
        views = ProductView.objects.filter(product_id=pk, created_at__gte=start_date)
        views_count = views.count()
        unique_viewers = views.values('user_id', 'session_id').distinct().count()
        
        cart_actions = CartAction.objects.filter(product_id=pk, created_at__gte=start_date)
        add_to_cart_count = cart_actions.filter(action='add').count()
        
        sales = SalesData.objects.filter(product_id=pk, date__gte=start_date.date())
        orders_count = sales.values('order_id').distinct().count()
        revenue = sales.aggregate(total=Sum('total_price'))['total'] or 0
        units_sold = sales.aggregate(total=Sum('quantity'))['total'] or 0
        
        conversion_rate = (orders_count / views_count) * 100 if views_count > 0 else 0
        
        # Prepare response data
        data = {
            'metrics': {
                'views': views_count,
                'unique_viewers': unique_viewers,
                'add_to_cart': add_to_cart_count,
                'orders': orders_count,
                'revenue': revenue,
                'units_sold': units_sold,
                'conversion_rate': conversion_rate
            },
            'sales_over_time': [],  # To be implemented with real data
            'views_over_time': [],  # To be implemented with real data
            'conversion_over_time': [],  # To be implemented with real data
            'funnel_data': [
                {'stage': 'Vues', 'count': views_count},
                {'stage': 'Ajouts au panier', 'count': add_to_cart_count},
                {'stage': 'Commandes', 'count': orders_count}
            ],
            'top_referrers': [
                {'referrer': 'Google', 'count': 120, 'percentage': 45},
                {'referrer': 'Direct', 'count': 80, 'percentage': 30},
                {'referrer': 'Facebook', 'count': 40, 'percentage': 15},
                {'referrer': 'Instagram', 'count': 20, 'percentage': 7},
                {'referrer': 'Autres', 'count': 8, 'percentage': 3}
            ],
            'device_breakdown': [
                {'device': 'Desktop', 'count': 150, 'percentage': 55},
                {'device': 'Mobile', 'count': 100, 'percentage': 37},
                {'device': 'Tablet', 'count': 20, 'percentage': 8}
            ]
        }
        
        serializer = ProductDashboardDataSerializer(data)
        return Response(serializer.data)
