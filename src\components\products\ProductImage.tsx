import React, { useState, useEffect } from 'react';
import { ProductDetail, ProductListItem } from '@/services/productApi';
import { getImageUrl, handleImageError } from '@/utils/imageUtils';
import { API_BASE_URL } from '@/services/apiConfig';
import { Loader2 } from 'lucide-react';

interface ProductImageProps {
  product: ProductDetail | ProductListItem;
  className?: string;
  style?: React.CSSProperties;
  showPlaceholder?: boolean;
}

/**
 * Composant robuste pour afficher les images de produits
 * Tente plusieurs stratégies pour trouver l'image correcte
 */
const ProductImage: React.FC<ProductImageProps> = ({
  product,
  className = '',
  style = {},
  showPlaceholder = true
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!product) return;

    // Essayer de trouver l'URL d'image la plus appropriée
    const findBestImageUrl = async () => {
      setIsLoading(true);
      setHasError(false);
      
      console.log('Recherche d\'image pour le produit:', product.id, product.name);

      // Liste des stratégies à essayer dans l'ordre
      const strategies = [
        // 1. Utiliser primary_image si disponible (priorité absolue)
        () => {
          if (product.primary_image?.image) {
            let imageUrl = product.primary_image.image;

            // Si l'URL est relative, la convertir en URL absolue
            if (imageUrl.startsWith('/media/')) {
              imageUrl = `${API_BASE_URL}${imageUrl}`;
            } else if (!imageUrl.startsWith('http')) {
              imageUrl = `${API_BASE_URL}/media/${imageUrl}`;
            }

            console.log('Primary image trouvée:', imageUrl);
            return imageUrl;
          }
          return null;
        },

        // 2. Utiliser la première image de la liste des images si disponible
        () => {
          if ('images' in product && Array.isArray(product.images) && product.images.length > 0) {
            let imageUrl = product.images[0].image;

            // Si l'URL est relative, la convertir en URL absolue
            if (imageUrl.startsWith('/media/')) {
              imageUrl = `${API_BASE_URL}${imageUrl}`;
            } else if (!imageUrl.startsWith('http')) {
              imageUrl = `${API_BASE_URL}/media/${imageUrl}`;
            }

            console.log('Première image de la liste trouvée:', imageUrl);
            return imageUrl;
          }
          return null;
        },

        // 3. Utiliser l'image directe du produit si disponible (fallback)
        () => {
          if ('image' in product && typeof product.image === 'string' && product.image !== '/placeholder.svg') {
            let imageUrl = product.image;

            // Si l'URL est relative, la convertir en URL absolue
            if (imageUrl.startsWith('/media/')) {
              imageUrl = `${API_BASE_URL}${imageUrl}`;
            } else if (!imageUrl.startsWith('http')) {
              imageUrl = `${API_BASE_URL}/media/${imageUrl}`;
            }

            console.log('Image directe trouvée:', imageUrl);
            return imageUrl;
          }
          return null;
        },
      ];

      // Essayer chaque stratégie jusqu'à ce qu'une fonctionne
      for (const strategy of strategies) {
        const imageUrl = strategy();
        if (!imageUrl) continue;

        // Pour les URLs d'API valides, les utiliser directement sans test
        if (imageUrl.includes('/media/') && (imageUrl.startsWith('http') || imageUrl.startsWith(API_BASE_URL))) {
          console.log('✅ URL d\'image API valide trouvée:', imageUrl);
          setImageSrc(imageUrl);
          setIsLoading(false);
          return;
        }

        // Pour les autres URLs, tester leur accessibilité
        try {
          const result = await testImageUrl(imageUrl);
          if (result) {
            console.log('✅ Stratégie d\'image réussie:', imageUrl);
            setImageSrc(imageUrl);
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.log('❌ Échec de la stratégie d\'image:', imageUrl);
        }
      }

      // Si aucune stratégie ne fonctionne, utiliser le placeholder
      console.warn('⚠️ Aucune image trouvée pour le produit:', product.id, product.name);
      setHasError(true);
      setIsLoading(false);
    };

    findBestImageUrl();
  }, [product]);

  // Fonction pour tester si une URL d'image est valide
  const testImageUrl = (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    });
  };

  // Gérer l'erreur de chargement d'image
  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error('Erreur de chargement de l\'image:', e);
    setHasError(true);
    handleImageError(e);
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`} style={style}>
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (hasError || !imageSrc) {
    if (!showPlaceholder) return null;

    return (
      <div className={`flex flex-col items-center justify-center bg-gray-100 ${className}`} style={style}>
        <img
          src="/placeholder-product.jpg"
          alt={product.name}
          className="max-w-full max-h-full object-contain opacity-60"
          onError={(e) => {
            // Si même le placeholder échoue, afficher une icône
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="flex flex-col items-center justify-center text-gray-400 p-4">
                  <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-xs text-center">${product.name}</span>
                </div>
              `;
            }
          }}
        />
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={product.name}
      className={className}
      style={style}
      onError={handleError}
    />
  );
};

export default ProductImage;
