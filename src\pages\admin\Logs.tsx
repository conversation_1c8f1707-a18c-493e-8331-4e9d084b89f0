
import { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Search,
  AlertCircle,
  Info,
  CheckCircle,
  Download
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { logsApi, SystemLog, LogsStatistics, LogsFilters } from '@/services/logsApi';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { LogsRefreshButton } from '@/components/ui/refresh-button';

const Logs = () => {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [statistics, setStatistics] = useState<LogsStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState<string>("");
  const [selectedModule, setSelectedModule] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);

  // Charger les logs et les statistiques
  const loadLogs = async () => {
    try {
      setLoading(true);

      const filters: LogsFilters = {
        search: searchQuery || undefined,
        type: selectedType || undefined,
        module: (selectedModule && selectedModule !== "all") ? selectedModule : undefined,
        page: currentPage,
        page_size: 50
      };

      const [logsResponse, statsResponse] = await Promise.all([
        logsApi.getLogs(filters),
        logsApi.getLogsStatistics()
      ]);

      setLogs(logsResponse.results);
      setStatistics(statsResponse);
    } catch (error) {
      console.error('Error loading logs:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les logs",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Charger les logs au montage du composant et lors des changements de filtres
  useEffect(() => {
    loadLogs();
  }, [searchQuery, selectedType, selectedModule, currentPage]);



  const handleDownloadLogs = () => {
    toast({
      title: "Téléchargement des logs",
      description: "Les logs sont en cours d'export au format CSV",
    });
    // TODO: Implémenter l'export CSV
  };

  const handleFilterByType = (type: string) => {
    setSelectedType(type === selectedType ? "" : type);
    setCurrentPage(1);
  };

  const getLogIcon = (type: string) => {
    switch (type) {
      case "info":
        return <Info size={16} className="text-blue-500" />;
      case "success":
        return <CheckCircle size={16} className="text-green-500" />;
      case "warning":
        return <AlertCircle size={16} className="text-amber-500" />;
      case "error":
        return <AlertCircle size={16} className="text-red-500" />;
      default:
        return <Info size={16} className="text-gray-500" />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Journalisation des Activités</h1>
          <p className="text-gray-500">Consultez les journaux d'activité et les événements du système</p>
        </div>

        {/* Statistiques rapides */}
        {loading ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : statistics ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(statistics.type_counts).map(([type, count]) => {
              const colors = {
                info: "bg-blue-100 text-blue-800 border-blue-200",
                success: "bg-green-100 text-green-800 border-green-200",
                warning: "bg-amber-100 text-amber-800 border-amber-200",
                error: "bg-red-100 text-red-800 border-red-200"
              };

              return (
                <Card
                  key={type}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedType === type ? colors[type as keyof typeof colors] : "bg-white hover:bg-gray-50"
                  }`}
                  onClick={() => handleFilterByType(type)}
                >
                  <CardContent className="p-4 flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium capitalize">{type}</p>
                      <p className="text-2xl font-bold">{count}</p>
                    </div>
                    <div>{getLogIcon(type)}</div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : null}

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex gap-4 flex-1">
            <div className="relative w-full sm:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Rechercher dans les logs..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={selectedModule} onValueChange={setSelectedModule}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par module" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les modules</SelectItem>
                {statistics && Object.keys(statistics.module_counts).map(module => (
                  <SelectItem key={module} value={module}>
                    {module.charAt(0).toUpperCase() + module.slice(1)} ({statistics.module_counts[module]})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <LogsRefreshButton
              refetchFn={loadLogs}
              successMessage="Logs mis à jour"
              className="flex items-center gap-1"
            >
              Rafraîchir
            </LogsRefreshButton>
            <Button onClick={handleDownloadLogs} className="flex items-center gap-1">
              <Download size={16} />
              Exporter
            </Button>
          </div>
        </div>

        {/* Tableau des logs */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Horodatage</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Module</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>IP</TableHead>
                <TableHead>Utilisateur</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                // Skeleton loading
                [...Array(10)].map((_, i) => (
                  <TableRow key={i}>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                  </TableRow>
                ))
              ) : logs.length > 0 ? (
                logs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="font-mono text-xs">{log.log_id}</TableCell>
                    <TableCell className="whitespace-nowrap">{log.formatted_timestamp}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1.5">
                        {getLogIcon(log.type)}
                        <span className="capitalize">{log.type_display}</span>
                      </div>
                    </TableCell>
                    <TableCell>{log.module_display}</TableCell>
                    <TableCell className="max-w-xs truncate" title={log.message}>
                      {log.message}
                    </TableCell>
                    <TableCell className="font-mono text-xs">{log.ip_address || '-'}</TableCell>
                    <TableCell>{log.user_display}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    Aucun log trouvé avec les filtres actuels
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Indication du nombre de logs */}
        <div className="flex justify-between items-center text-sm text-gray-500">
          <div>
            {loading ? (
              <div className="h-4 bg-gray-200 rounded animate-pulse w-48"></div>
            ) : (
              <>
                Affichage de {logs.length} logs
                {statistics && ` sur ${statistics.total_logs} total`}
                {statistics && ` • ${statistics.recent_activity_24h} dans les dernières 24h`}
                {statistics && statistics.error_logs_7d > 0 && (
                  <span className="text-red-600 ml-2">
                    • {statistics.error_logs_7d} erreurs cette semaine
                  </span>
                )}
              </>
            )}
          </div>
          {!loading && logs.length > 0 && (
            <div className="text-xs">
              Dernière mise à jour: {new Date().toLocaleTimeString('fr-FR')}
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default Logs;
