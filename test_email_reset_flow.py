#!/usr/bin/env python3
"""
Script pour tester le flux complet d'email de réinitialisation
"""

import sys
import os
import requests
import json

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from authentication.models import User, PasswordReset
from authentication.utils import send_password_reset_email
from django.utils import timezone

def test_email_reset_flow(email="<EMAIL>"):
    """Teste le flux complet de réinitialisation par email"""
    
    print("🔄 Test du flux complet de réinitialisation par email")
    print("=" * 60)
    
    try:
        # 1. Vérifier que l'utilisateur existe
        print(f"1. Vérification de l'utilisateur {email}...")
        user = User.objects.get(email=email)
        print(f"   ✅ Utilisateur trouvé: {user.first_name} {user.last_name}")
        
        # 2. Compter les tokens existants avant
        tokens_before = PasswordReset.objects.filter(user=user).count()
        print(f"   📊 Tokens existants: {tokens_before}")
        
        # 3. Envoyer l'email de réinitialisation
        print(f"\n2. Envoi de l'email de réinitialisation...")
        send_password_reset_email(user)
        print(f"   ✅ Email envoyé (fonction send_password_reset_email appelée)")
        
        # 4. Vérifier qu'un nouveau token a été créé
        print(f"\n3. Vérification du nouveau token...")
        tokens_after = PasswordReset.objects.filter(user=user).count()
        print(f"   📊 Tokens après envoi: {tokens_after}")
        
        if tokens_after > tokens_before:
            print(f"   ✅ Nouveau token créé!")
            
            # Récupérer le dernier token
            latest_token = PasswordReset.objects.filter(user=user).order_by('-created_at').first()
            print(f"   🔑 Token: {latest_token.token}")
            print(f"   ⏰ Créé le: {latest_token.created_at}")
            print(f"   ⏰ Expire le: {latest_token.expires_at}")
            print(f"   🔒 Utilisé: {latest_token.is_used}")
            print(f"   🔗 URL: http://localhost:8080/reset-password/{latest_token.token}")
            
            # 5. Tester le token via l'API
            print(f"\n4. Test du token via l'API...")
            success = test_token_via_api(latest_token.token)
            
            if success:
                print(f"   ✅ Token fonctionnel!")
                return latest_token.token
            else:
                print(f"   ❌ Token non fonctionnel")
                return None
        else:
            print(f"   ❌ Aucun nouveau token créé")
            return None
            
    except User.DoesNotExist:
        print(f"   ❌ Utilisateur {email} non trouvé")
        return None
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

def test_token_via_api(token):
    """Teste un token via l'API"""
    url = "http://localhost:8000/api/v1/auth/password/reset/confirm/"
    
    payload = {
        "token": token,
        "new_password": "TestPassword123!",
        "confirm_password": "TestPassword123!"
    }
    
    try:
        response = requests.post(url, json=payload)
        
        print(f"   📊 Status: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"   📄 Réponse: {json.dumps(response_data, indent=6)}")
        except:
            print(f"   📄 Réponse (text): {response.text}")
            
        return response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print("   ❌ Serveur Django non accessible")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_api_request_reset(email="<EMAIL>"):
    """Teste la demande de réinitialisation via l'API"""
    
    print(f"\n🌐 Test de la demande de réinitialisation via l'API")
    print("=" * 60)
    
    url = "http://localhost:8000/api/v1/auth/password/reset/request/"
    
    payload = {
        "email": email
    }
    
    try:
        print(f"1. Envoi de la requête à {url}...")
        print(f"   📧 Email: {email}")
        
        response = requests.post(url, json=payload)
        
        print(f"   📊 Status: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"   📄 Réponse: {json.dumps(response_data, indent=6)}")
        except:
            print(f"   📄 Réponse (text): {response.text}")
            
        if response.status_code == 200:
            print(f"   ✅ Demande de réinitialisation réussie!")
            
            # Récupérer le dernier token créé
            print(f"\n2. Récupération du token créé...")
            user = User.objects.get(email=email)
            latest_token = PasswordReset.objects.filter(user=user).order_by('-created_at').first()
            
            if latest_token:
                print(f"   🔑 Token: {latest_token.token}")
                print(f"   🔗 URL: http://localhost:8080/reset-password/{latest_token.token}")
                
                # Tester le token
                print(f"\n3. Test du token...")
                success = test_token_via_api(latest_token.token)
                return latest_token.token if success else None
            else:
                print(f"   ❌ Aucun token trouvé")
                return None
        else:
            print(f"   ❌ Demande de réinitialisation échouée")
            return None
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return None

if __name__ == "__main__":
    print("🧪 TEST COMPLET DU SYSTÈME DE RÉINITIALISATION PAR EMAIL")
    print("=" * 70)
    
    # Test 1: Via la fonction send_password_reset_email directement
    token1 = test_email_reset_flow()
    
    # Test 2: Via l'API de demande de réinitialisation
    token2 = test_api_request_reset()
    
    print(f"\n📋 RÉSUMÉ:")
    print(f"   Test fonction directe: {'✅ Réussi' if token1 else '❌ Échoué'}")
    print(f"   Test API: {'✅ Réussi' if token2 else '❌ Échoué'}")
    
    if token1 or token2:
        working_token = token1 or token2
        print(f"\n💡 Token fonctionnel pour test frontend:")
        print(f"   {working_token}")
        print(f"\n🔗 URL pour test frontend:")
        print(f"   http://localhost:8080/reset-password/{working_token}")
    else:
        print(f"\n❌ Aucun token fonctionnel trouvé")
