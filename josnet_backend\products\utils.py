import os
import uuid
from io import BytesIO
from PIL import Image
from django.core.files.base import ContentFile
from django.conf import settings

def resize_image(image, size, format='JPEG', quality=85):
    """
    Resize an image to the specified size.
    
    Args:
        image: PIL Image object
        size: Tuple of (width, height)
        format: Output format (JPEG, PNG, etc.)
        quality: Quality for JPEG compression
        
    Returns:
        ContentFile object with the resized image
    """
    img_io = BytesIO()
    
    # Preserve aspect ratio
    img_ratio = image.width / image.height
    target_ratio = size[0] / size[1]
    
    if img_ratio > target_ratio:
        # Image is wider than target, crop width
        new_width = int(target_ratio * image.height)
        offset = (image.width - new_width) // 2
        resize_area = (offset, 0, offset + new_width, image.height)
    else:
        # Image is taller than target, crop height
        new_height = int(image.width / target_ratio)
        offset = (image.height - new_height) // 2
        resize_area = (0, offset, image.width, offset + new_height)
    
    image = image.crop(resize_area)
    image = image.resize(size, Image.LANCZOS)
    
    if format == 'JPEG':
        image = image.convert('RGB')
        image.save(img_io, format=format, quality=quality, optimize=True)
    else:
        image.save(img_io, format=format, optimize=True)
    
    img_content = ContentFile(img_io.getvalue())
    return img_content

def generate_thumbnail(image_file, size=(300, 300), format='JPEG'):
    """
    Generate a thumbnail from an image file.
    
    Args:
        image_file: Django UploadedFile or ImageField
        size: Tuple of (width, height)
        format: Output format (JPEG, PNG, etc.)
        
    Returns:
        ContentFile object with the thumbnail
    """
    img = Image.open(image_file)
    return resize_image(img, size, format)

def process_product_image(image_file, product_slug):
    """
    Process a product image: generate a unique filename and create thumbnails.
    
    Args:
        image_file: Django UploadedFile
        product_slug: Slug of the product
        
    Returns:
        Dict with original image and thumbnail paths
    """
    # Generate a unique filename
    ext = os.path.splitext(image_file.name)[1].lower()
    unique_id = uuid.uuid4().hex[:8]
    filename = f"{product_slug}_{unique_id}{ext}"
    
    # Create thumbnail filename
    thumb_filename = f"{product_slug}_{unique_id}_thumb{ext}"
    
    # Generate thumbnail
    thumbnail = generate_thumbnail(image_file)
    
    return {
        'original': {
            'name': filename,
            'content': image_file,
        },
        'thumbnail': {
            'name': thumb_filename,
            'content': thumbnail,
        }
    }

def optimize_image(image_file, max_size=(1920, 1080), quality=85):
    """
    Optimize an image by resizing it if it's too large and compressing it.
    
    Args:
        image_file: Django UploadedFile or ImageField
        max_size: Maximum size (width, height)
        quality: JPEG compression quality
        
    Returns:
        ContentFile object with the optimized image
    """
    img = Image.open(image_file)
    
    # Check if resizing is needed
    if img.width > max_size[0] or img.height > max_size[1]:
        # Calculate new size preserving aspect ratio
        img_ratio = img.width / img.height
        
        if img_ratio > (max_size[0] / max_size[1]):
            # Width is the limiting factor
            new_width = max_size[0]
            new_height = int(new_width / img_ratio)
        else:
            # Height is the limiting factor
            new_height = max_size[1]
            new_width = int(new_height * img_ratio)
        
        img = img.resize((new_width, new_height), Image.LANCZOS)
    
    # Determine format
    if img.format == 'PNG' and 'A' in img.getbands():  # Has alpha channel
        format = 'PNG'
    else:
        format = 'JPEG'
        img = img.convert('RGB')
    
    # Save optimized image
    img_io = BytesIO()
    if format == 'JPEG':
        img.save(img_io, format=format, quality=quality, optimize=True)
    else:
        img.save(img_io, format=format, optimize=True)
    
    return ContentFile(img_io.getvalue())
