{"instructions": "Guide pour remplacer les textes en dur par des appels de traduction", "import_statement": "import { useTranslation } from \"@/contexts/TranslationContext\";", "hook_usage": "const { t } = useTranslation();", "replacement_patterns": [{"pattern": "Texte en dur entre guillemets", "example_before": "\"A<PERSON>ter au panier\"", "example_after": "t(\"product.add_to_cart\", \"Ajouter au panier\")", "explanation": "Remplacer par t(key, fallback) où key est la clé de traduction et fallback le texte original"}, {"pattern": "Texte dans les attributs", "example_before": "placeholder=\"Rechercher...\"", "example_after": "placeholder={t(\"common.search\", \"Rechercher...\")}", "explanation": "Utiliser des accolades pour les expressions JSX"}, {"pattern": "Messages conditionnels", "example_before": "error ? \"Erreur\" : \"<PERSON><PERSON><PERSON>\"", "example_after": "error ? t(\"common.error\", \"Erreur\") : t(\"common.success\", \"Succès\")", "explanation": "Appliquer la traduction à chaque branche conditionnelle"}], "critical_files": ["src/contexts/CartContext.tsx", "src/components/cart/Cart.tsx", "src/components/navigation/", "src/components/auth/", "src/pages/products/"]}