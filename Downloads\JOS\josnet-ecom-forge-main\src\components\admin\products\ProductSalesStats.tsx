import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { 
  ShoppingBag, 
  DollarSign, 
  Package, 
  TrendingUp, 
  TrendingDown, 
  AlertCircle, 
  Calendar, 
  Users, 
  ArrowRight 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';

// Types
interface ProductSalesStats {
  product_id: number;
  product_name: string;
  total_orders: number;
  total_quantity_sold: number;
  total_revenue: number;
  average_order_value: number;
  last_order_date: string;
  sales_trend: number; // percentage change
  sales_by_period: {
    period: string;
    quantity: number;
    revenue: number;
  }[];
  sales_by_variant?: {
    variant_id: number;
    variant_name: string;
    quantity: number;
    revenue: number;
  }[];
  top_customers: {
    customer_id: number;
    customer_name: string;
    orders_count: number;
    total_spent: number;
  }[];
  related_products_sales: {
    product_id: number;
    product_name: string;
    quantity: number;
    revenue: number;
  }[];
}

// Mock API service (replace with actual API calls)
const orderApi = {
  getProductSalesStats: async (productId: number, period: string = 'month'): Promise<ProductSalesStats> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Generate mock data
    const salesByPeriod = [];
    const now = new Date();
    
    if (period === 'day') {
      // Last 14 days
      for (let i = 13; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        salesByPeriod.push({
          period: date.toISOString().split('T')[0],
          quantity: Math.floor(Math.random() * 10),
          revenue: Math.floor(Math.random() * 1000)
        });
      }
    } else if (period === 'week') {
      // Last 12 weeks
      for (let i = 11; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - (i * 7));
        const weekNum = Math.floor((date.getTime() - new Date(date.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
        salesByPeriod.push({
          period: `S${weekNum}`,
          quantity: Math.floor(Math.random() * 50),
          revenue: Math.floor(Math.random() * 5000)
        });
      }
    } else {
      // Last 12 months
      for (let i = 11; i >= 0; i--) {
        const date = new Date(now);
        date.setMonth(date.getMonth() - i);
        salesByPeriod.push({
          period: date.toISOString().split('T')[0].substring(0, 7),
          quantity: Math.floor(Math.random() * 200),
          revenue: Math.floor(Math.random() * 20000)
        });
      }
    }
    
    // Calculate totals
    const totalQuantity = salesByPeriod.reduce((sum, item) => sum + item.quantity, 0);
    const totalRevenue = salesByPeriod.reduce((sum, item) => sum + item.revenue, 0);
    
    return {
      product_id: productId,
      product_name: 'Product Name',
      total_orders: Math.floor(totalQuantity * 0.8), // Assume some orders have multiple items
      total_quantity_sold: totalQuantity,
      total_revenue: totalRevenue,
      average_order_value: Math.round(totalRevenue / Math.max(1, Math.floor(totalQuantity * 0.8))),
      last_order_date: new Date().toISOString(),
      sales_trend: Math.random() > 0.5 ? Math.floor(Math.random() * 30) : -Math.floor(Math.random() * 30),
      sales_by_period: salesByPeriod,
      sales_by_variant: [
        {
          variant_id: 1,
          variant_name: 'Variant A',
          quantity: Math.floor(totalQuantity * 0.4),
          revenue: Math.floor(totalRevenue * 0.4)
        },
        {
          variant_id: 2,
          variant_name: 'Variant B',
          quantity: Math.floor(totalQuantity * 0.3),
          revenue: Math.floor(totalRevenue * 0.3)
        },
        {
          variant_id: 3,
          variant_name: 'Variant C',
          quantity: Math.floor(totalQuantity * 0.2),
          revenue: Math.floor(totalRevenue * 0.2)
        },
        {
          variant_id: 4,
          variant_name: 'Variant D',
          quantity: Math.floor(totalQuantity * 0.1),
          revenue: Math.floor(totalRevenue * 0.1)
        }
      ],
      top_customers: [
        {
          customer_id: 1,
          customer_name: 'John Doe',
          orders_count: 5,
          total_spent: Math.floor(totalRevenue * 0.2)
        },
        {
          customer_id: 2,
          customer_name: 'Jane Smith',
          orders_count: 3,
          total_spent: Math.floor(totalRevenue * 0.15)
        },
        {
          customer_id: 3,
          customer_name: 'Bob Johnson',
          orders_count: 2,
          total_spent: Math.floor(totalRevenue * 0.1)
        }
      ],
      related_products_sales: [
        {
          product_id: 101,
          product_name: 'Related Product 1',
          quantity: Math.floor(Math.random() * 100),
          revenue: Math.floor(Math.random() * 10000)
        },
        {
          product_id: 102,
          product_name: 'Related Product 2',
          quantity: Math.floor(Math.random() * 100),
          revenue: Math.floor(Math.random() * 10000)
        },
        {
          product_id: 103,
          product_name: 'Related Product 3',
          quantity: Math.floor(Math.random() * 100),
          revenue: Math.floor(Math.random() * 10000)
        }
      ]
    };
  }
};

interface ProductSalesStatsProps {
  productId: number;
}

const ProductSalesStats: React.FC<ProductSalesStatsProps> = ({ productId }) => {
  const [period, setPeriod] = useState<'day' | 'week' | 'month'>('month');
  
  // Fetch sales stats
  const { 
    data: salesStats, 
    isLoading, 
    isError 
  } = useQuery({
    queryKey: ['productSales', productId, period],
    queryFn: () => orderApi.getProductSalesStats(productId, period),
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };
  
  // Format period label
  const formatPeriodLabel = (period: string) => {
    if (period.startsWith('S')) {
      // Week format
      return `Semaine ${period.substring(1)}`;
    } else if (period.length === 7) {
      // Month format (YYYY-MM)
      const [year, month] = period.split('-');
      const date = new Date(parseInt(year), parseInt(month) - 1, 1);
      return date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });
    } else {
      // Day format (YYYY-MM-DD)
      return formatDate(period);
    }
  };
  
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Statistiques de vente</CardTitle>
            <CardDescription>
              Analyse des ventes et performances du produit
            </CardDescription>
          </div>
          <Select value={period} onValueChange={(value: 'day' | 'week' | 'month') => setPeriod(value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sélectionner une période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Jours</SelectItem>
              <SelectItem value="week">Semaines</SelectItem>
              <SelectItem value="month">Mois</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
            <Skeleton className="h-80 w-full" />
          </div>
        ) : isError ? (
          <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span>Une erreur est survenue lors du chargement des statistiques de vente.</span>
          </div>
        ) : (
          <>
            {/* Key metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Commandes</p>
                      <h3 className="text-2xl font-bold">{salesStats.total_orders}</h3>
                    </div>
                    <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                      <ShoppingBag className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-gray-500">Dernière: {formatDate(salesStats.last_order_date)}</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Quantité vendue</p>
                      <h3 className="text-2xl font-bold">{salesStats.total_quantity_sold}</h3>
                    </div>
                    <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center text-green-600">
                      <Package className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    {salesStats.sales_trend > 0 ? (
                      <div className="flex items-center text-green-600">
                        <TrendingUp className="h-4 w-4 mr-1" />
                        <span>+{salesStats.sales_trend}% vs période précédente</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-red-600">
                        <TrendingDown className="h-4 w-4 mr-1" />
                        <span>{salesStats.sales_trend}% vs période précédente</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Chiffre d'affaires</p>
                      <h3 className="text-2xl font-bold">{formatPrice(salesStats.total_revenue)}</h3>
                    </div>
                    <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600">
                      <DollarSign className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-gray-500">Panier moyen: {formatPrice(salesStats.average_order_value)}</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Clients</p>
                      <h3 className="text-2xl font-bold">{salesStats.top_customers.length}</h3>
                    </div>
                    <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center text-amber-600">
                      <Users className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-gray-500">Top client: {salesStats.top_customers[0]?.customer_name}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Sales charts */}
            <Tabs defaultValue="revenue">
              <TabsList>
                <TabsTrigger value="revenue">Chiffre d'affaires</TabsTrigger>
                <TabsTrigger value="quantity">Quantité</TabsTrigger>
                <TabsTrigger value="variants">Variantes</TabsTrigger>
              </TabsList>
              
              <TabsContent value="revenue" className="pt-4">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={salesStats.sales_by_period}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="period" 
                        tickFormatter={formatPeriodLabel}
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis 
                        tickFormatter={(value) => `${value}€`}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip 
                        formatter={(value) => [`${formatPrice(value as number)}`, 'Chiffre d\'affaires']}
                        labelFormatter={formatPeriodLabel}
                      />
                      <Bar dataKey="revenue" fill="#8884d8" name="Chiffre d'affaires" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
              
              <TabsContent value="quantity" className="pt-4">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={salesStats.sales_by_period}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="period" 
                        tickFormatter={formatPeriodLabel}
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis 
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip 
                        formatter={(value) => [`${value} unités`, 'Quantité vendue']}
                        labelFormatter={formatPeriodLabel}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="quantity" 
                        stroke="#82ca9d" 
                        name="Quantité vendue"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
              
              <TabsContent value="variants" className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={salesStats.sales_by_variant}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="quantity"
                          nameKey="variant_name"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {salesStats.sales_by_variant?.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip 
                          formatter={(value) => [`${value} unités`, 'Quantité vendue']}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-4">Ventes par variante</h3>
                    <div className="border rounded-md overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Variante</TableHead>
                            <TableHead className="text-right">Quantité</TableHead>
                            <TableHead className="text-right">CA</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {salesStats.sales_by_variant?.map((variant) => (
                            <TableRow key={variant.variant_id}>
                              <TableCell>{variant.variant_name}</TableCell>
                              <TableCell className="text-right">{variant.quantity}</TableCell>
                              <TableCell className="text-right">{formatPrice(variant.revenue)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
            
            {/* Top customers */}
            <div>
              <h3 className="text-lg font-medium mb-4">Meilleurs clients</h3>
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Client</TableHead>
                      <TableHead className="text-right">Commandes</TableHead>
                      <TableHead className="text-right">Montant total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {salesStats.top_customers.map((customer) => (
                      <TableRow key={customer.customer_id}>
                        <TableCell>{customer.customer_name}</TableCell>
                        <TableCell className="text-right">{customer.orders_count}</TableCell>
                        <TableCell className="text-right">{formatPrice(customer.total_spent)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </>
        )}
      </CardContent>
      
      <CardFooter className="border-t pt-4">
        <Button variant="outline" className="ml-auto">
          <Calendar className="mr-2 h-4 w-4" />
          Voir le rapport complet
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ProductSalesStats;
