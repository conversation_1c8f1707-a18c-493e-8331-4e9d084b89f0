
import { useState, useMemo } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, Calendar, User, ArrowRight, Tag, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import { Pagination, PaginationContent, PaginationItem, PaginationLink } from "@/components/ui/pagination";
import { useBlogPage } from "@/hooks/useBlog";

// Les données sont maintenant récupérées dynamiquement via l'API CMS

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState("Tous");
  const [currentPage, setCurrentPage] = useState(1);

  // Utiliser le hook personnalisé pour récupérer les données
  const {
    posts,
    categories,
    featuredPosts,
    pagination,
    isLoading,
    hasError,
    formatDate,
    getAuthorName,
    getCategoryName,
    getImageUrl,
    getReadingTime,
    getTags,
  } = useBlogPage({
    page: currentPage,
    page_size: 9,
    search: searchTerm.trim() || undefined,
    // Ne pas filtrer par catégorie dans la requête initiale
    // Le filtrage se fera côté client si nécessaire
  });

  // Préparer les catégories pour l'affichage (ajouter "Tous")
  const displayCategories = useMemo(() => {
    if (!categories || categories.length === 0) {
      return ["Tous"];
    }
    const allCategories = ["Tous", ...categories.map(cat => cat.name)];
    return allCategories;
  }, [categories]);

  // Filtrer les posts côté client par catégorie si nécessaire
  const filteredPosts = useMemo(() => {
    if (!posts || posts.length === 0) return [];

    if (activeCategory === "Tous") {
      return posts;
    }

    return posts.filter(post => {
      if (!post.categories || post.categories.length === 0) return false;
      return post.categories.some(cat => cat.name === activeCategory);
    });
  }, [posts, activeCategory]);

  // Gérer la recherche avec debounce
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset à la première page lors d'une recherche
  };

  // Gérer le changement de catégorie
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setCurrentPage(1); // Reset à la première page lors d'un changement de catégorie
  };

  // Gérer la pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Calculer les pages pour la pagination
  const totalPages = Math.ceil((pagination?.count || filteredPosts.length) / 9);
  const hasNextPage = pagination?.hasNext || false;
  const hasPreviousPage = pagination?.hasPrevious || false;

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow">
        {/* Hero section */}
        <section className="bg-primary-dark text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Blog & Actualités</h1>
            <p className="text-xl max-w-2xl mx-auto mb-8">
              Découvrez nos derniers articles, conseils et actualités sur le monde de la technologie.
            </p>

            {/* Search bar */}
            <div className="max-w-xl mx-auto relative">
              <Input
                type="text"
                placeholder="Rechercher un article..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 py-6 bg-white/90 border-0 text-gray-800"
              />
              <Search className="absolute left-3 top-3 text-gray-500" />
              {isLoading && (
                <Loader2 className="absolute right-3 top-3 text-gray-500 animate-spin" size={20} />
              )}
            </div>
          </div>
        </section>

        {/* Blog Content */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            {/* Category filters */}
            <div className="mb-10 overflow-x-auto">
              <div className="flex space-x-2 pb-2">
                {isLoading ? (
                  // Skeleton pour les catégories
                  Array.from({ length: 6 }).map((_, index) => (
                    <Skeleton key={index} className="h-10 w-24 rounded-md" />
                  ))
                ) : (
                  displayCategories.map(category => (
                    <Button
                      key={category}
                      variant={activeCategory === category ? "default" : "outline"}
                      className={`whitespace-nowrap ${
                        activeCategory === category
                          ? "bg-primary"
                          : ""
                      }`}
                      onClick={() => handleCategoryChange(category)}
                    >
                      {category}
                    </Button>
                  ))
                )}
              </div>
            </div>

            {/* Featured post */}
            {isLoading ? (
              <div className="mb-16">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 bg-white rounded-lg shadow-sm overflow-hidden">
                  <Skeleton className="h-64 lg:h-80" />
                  <div className="p-6 lg:p-8 flex flex-col justify-center space-y-4">
                    <div className="flex items-center space-x-4">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <div className="flex items-center justify-between mt-auto">
                      <div className="flex gap-2">
                        <Skeleton className="h-6 w-16" />
                        <Skeleton className="h-6 w-12" />
                        <Skeleton className="h-6 w-20" />
                      </div>
                      <Skeleton className="h-6 w-24" />
                    </div>
                  </div>
                </div>
              </div>
            ) : filteredPosts.length > 0 && (
              <div className="mb-16">
                <Link to={`/blog/${filteredPosts[0].slug}`} className="group block">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 bg-white rounded-lg shadow-sm overflow-hidden">
                    <div className="h-64 lg:h-auto overflow-hidden">
                      <img
                        src={getImageUrl(filteredPosts[0].featured_image)}
                        alt={filteredPosts[0].title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-6 lg:p-8 flex flex-col justify-center">
                      <div className="flex items-center text-sm text-gray-500 mb-3">
                        <span className="flex items-center mr-4">
                          <Calendar size={14} className="mr-1" />
                          {formatDate(filteredPosts[0].created_at)}
                        </span>
                        <span className="flex items-center mr-4">
                          <User size={14} className="mr-1" />
                          {getAuthorName(filteredPosts[0].author)}
                        </span>
                        <span className="text-xs bg-gray-100 text-primary-dark px-2 py-1 rounded">
                          {getCategoryName(filteredPosts[0].category)}
                        </span>
                      </div>
                      <h2 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">
                        {filteredPosts[0].title}
                      </h2>
                      <p className="text-gray-600 mb-6">
                        {filteredPosts[0].excerpt}
                      </p>
                      <div className="flex items-center justify-between mt-auto">
                        <div className="flex flex-wrap gap-2">
                          {getTags(filteredPosts[0]).slice(0, 3).map(tag => (
                            <span key={tag} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                        <Button
                          variant="link"
                          className="text-primary group-hover:text-primary-dark flex items-center p-0"
                        >
                          Lire la suite <ArrowRight size={16} className="ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            )}

            {/* Blog posts grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {isLoading ? (
                // Skeleton pour les articles
                Array.from({ length: 6 }).map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <Skeleton className="h-48" />
                    <CardContent className="p-6 space-y-3">
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-3 w-20" />
                        <Skeleton className="h-5 w-16 rounded" />
                      </div>
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-3 w-24" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : filteredPosts.length > 0 ? (
                filteredPosts.slice(1).map(post => (
                  <Card key={post.id} className="overflow-hidden group">
                    <Link to={`/blog/${post.slug}`} className="block">
                      <div className="h-48 overflow-hidden">
                        <img
                          src={getImageUrl(post.featured_image)}
                          alt={post.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                          <span className="flex items-center">
                            <Calendar size={12} className="mr-1" />
                            {formatDate(post.created_at)}
                          </span>
                          <span className="bg-gray-100 text-primary-dark px-2 py-1 rounded">
                            {getCategoryName(post.category)}
                          </span>
                        </div>
                        <h3 className="font-bold text-lg mb-2 group-hover:text-primary transition-colors">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">
                          {post.excerpt}
                        </p>
                        <div className="flex items-center justify-between mt-4">
                          <span className="text-xs text-gray-500 flex items-center">
                            <User size={12} className="mr-1" />
                            {getAuthorName(post.author)}
                          </span>
                          <Button
                            variant="link"
                            className="text-primary group-hover:text-primary-dark flex items-center p-0 text-sm"
                          >
                            Lire <ArrowRight size={14} className="ml-1" />
                          </Button>
                        </div>
                      </CardContent>
                    </Link>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  {hasError ? (
                    <>
                      <h3 className="text-xl font-medium mb-2">Erreur de chargement</h3>
                      <p className="text-gray-600 mb-4">
                        Impossible de charger les articles. Veuillez réessayer plus tard.
                      </p>
                      <Button onClick={() => window.location.reload()}>
                        Réessayer
                      </Button>
                    </>
                  ) : (
                    <>
                      <h3 className="text-xl font-medium mb-2">Aucun article trouvé</h3>
                      <p className="text-gray-600">
                        Essayez de modifier votre recherche ou de sélectionner une autre catégorie.
                      </p>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Pagination */}
            {!isLoading && filteredPosts.length > 0 && totalPages > 1 && (
              <div className="mt-12">
                <Pagination>
                  <PaginationContent>
                    {/* Bouton précédent */}
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        aria-label="Previous page"
                        onClick={(e) => {
                          e.preventDefault();
                          if (hasPreviousPage) handlePageChange(currentPage - 1);
                        }}
                        className={!hasPreviousPage ? 'opacity-50 cursor-not-allowed' : ''}
                      >
                        «
                      </PaginationLink>
                    </PaginationItem>

                    {/* Pages */}
                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                      let pageNumber;
                      if (totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }

                      return (
                        <PaginationItem key={pageNumber}>
                          <PaginationLink
                            href="#"
                            isActive={currentPage === pageNumber}
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(pageNumber);
                            }}
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    {/* Bouton suivant */}
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        aria-label="Next page"
                        onClick={(e) => {
                          e.preventDefault();
                          if (hasNextPage) handlePageChange(currentPage + 1);
                        }}
                        className={!hasNextPage ? 'opacity-50 cursor-not-allowed' : ''}
                      >
                        »
                      </PaginationLink>
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>

                {/* Informations de pagination */}
                <div className="text-center mt-4 text-sm text-gray-600">
                  Affichage de {Math.min((currentPage - 1) * 9 + 1, pagination?.count || filteredPosts.length)} à {Math.min(currentPage * 9, pagination?.count || filteredPosts.length)} sur {pagination?.count || filteredPosts.length} articles
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="bg-gray-100 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-xl mx-auto text-center">
              <h2 className="text-2xl md:text-3xl font-bold mb-4">Restez informé</h2>
              <p className="text-gray-600 mb-6">
                Abonnez-vous à notre newsletter pour recevoir les derniers articles et actualités technologiques.
              </p>
              <form className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="email"
                  placeholder="Votre adresse email"
                  className="flex-grow"
                  required
                />
                <Button type="submit">S'abonner</Button>
              </form>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Blog;
