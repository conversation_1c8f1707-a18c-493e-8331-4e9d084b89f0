#!/usr/bin/env python3
"""
Script pour créer les paramètres de site par défaut
"""

import sys
import os

# Ajouter le répertoire du backend au path
sys.path.append('josnet_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')

import django
django.setup()

from cms.models import SiteSetting

def create_default_settings():
    """Créer les paramètres de site par défaut"""
    print("🔧 Création des paramètres de site par défaut...")
    
    try:
        # Vérifier s'il existe déjà des paramètres
        existing_settings = SiteSetting.objects.first()
        
        if existing_settings:
            print(f"   ✅ Paramètres existants trouvés: {existing_settings.site_name}")
            return True
        
        # Créer les paramètres par défaut
        default_settings = SiteSetting.objects.create(
            site_title="JosNet",
            site_description="Votre marketplace de confiance au Burundi",
            contact_email="<EMAIL>",
            contact_phone="+257 22 123 456",
            contact_address="Bujumbura, Burundi",
            facebook_url="https://facebook.com/josnet",
            twitter_url="https://twitter.com/josnet",
            instagram_url="https://instagram.com/josnet",
            linkedin_url="https://linkedin.com/company/josnet"
        )
        
        print(f"   ✅ Paramètres de site créés: {default_settings.site_title}")
        print(f"   📧 Email de contact: {default_settings.contact_email}")
        print(f"   📱 Téléphone: {default_settings.contact_phone}")
        print(f"   🌍 Adresse: {default_settings.contact_address}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_api_endpoint():
    """Tester l'endpoint API"""
    print(f"\n🧪 Test de l'endpoint API...")
    
    import requests
    
    try:
        response = requests.get("http://localhost:8000/api/v1/cms/settings/current/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API accessible")
            print(f"   🏢 Site: {data.get('site_title')}")
            print(f"   📝 Description: {data.get('site_description')}")
            return True
        else:
            print(f"   ❌ Erreur API: {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    print("🏗️ CONFIGURATION DES PARAMÈTRES DE SITE")
    print("=" * 50)
    
    # 1. Créer les paramètres par défaut
    settings_created = create_default_settings()
    
    # 2. Tester l'endpoint API
    api_working = test_api_endpoint()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Paramètres créés: {'✅' if settings_created else '❌'}")
    print(f"   API fonctionnelle: {'✅' if api_working else '❌'}")
    
    if settings_created and api_working:
        print(f"\n🎉 CONFIGURATION RÉUSSIE!")
        print(f"   ✅ Les paramètres de site sont configurés")
        print(f"   ✅ L'API CMS fonctionne correctement")
        print(f"   🌐 Endpoint: http://localhost:8000/api/v1/cms/settings/current/")
    else:
        print(f"\n❌ PROBLÈMES DÉTECTÉS")
        print(f"   Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
