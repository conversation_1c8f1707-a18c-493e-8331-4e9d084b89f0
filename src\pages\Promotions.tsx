import React from 'react';
import { Link } from 'react-router-dom';
import { Tag } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';

// Utiliser le composant ActivePromotions existant dans le dossier home
import ActivePromotions from '@/components/home/<USER>';
import PromotionSubscribe from '@/components/notifications/PromotionSubscribe';

const PromotionsPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
            <Link to="/" className="hover:underline">Accueil</Link>
            <span>/</span>
            <span>Offres et promotions</span>
          </div>
          
          <div className="flex items-center gap-2 mb-8">
            <Tag className="h-6 w-6" />
            <h1 className="text-3xl font-bold tracking-tight">Offres et promotions</h1>
          </div>
        </div>
        
        <div className="grid gap-8">
          {/* Promotions actives */}
          <section>
            <ActivePromotions />
          </section>
          
          {/* Comment utiliser les codes promo */}
          <section className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Comment utiliser nos codes promo ?</CardTitle>
                <CardDescription>
                  Suivez ces étapes simples pour bénéficier de nos offres
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ol className="list-decimal pl-5 space-y-3">
                  <li>
                    <strong>Trouvez un code promo</strong>
                    <p className="text-sm text-gray-600">
                      Consultez cette page régulièrement pour découvrir nos dernières offres et codes promo.
                    </p>
                  </li>
                  <li>
                    <strong>Ajoutez des produits à votre panier</strong>
                    <p className="text-sm text-gray-600">
                      Parcourez notre catalogue et ajoutez les articles qui vous intéressent à votre panier.
                    </p>
                  </li>
                  <li>
                    <strong>Entrez le code promo lors du paiement</strong>
                    <p className="text-sm text-gray-600">
                      Dans la page de paiement, vous trouverez un champ pour entrer votre code promo.
                    </p>
                  </li>
                  <li>
                    <strong>Profitez de votre remise !</strong>
                    <p className="text-sm text-gray-600">
                      La remise sera automatiquement appliquée à votre commande.
                    </p>
                  </li>
                </ol>
              </CardContent>
            </Card>
          </section>
          
          {/* S'abonner aux notifications de promotions */}
          <section className="mt-8">
            <PromotionSubscribe className="w-full" />
          </section>
          
          {/* FAQ sur les promotions */}
          <section className="mt-8">
            <h2 className="text-2xl font-bold tracking-tight mb-4">Questions fréquentes</h2>
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Puis-je cumuler plusieurs codes promo ?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Non, vous ne pouvez utiliser qu'un seul code promo par commande. Choisissez celui qui vous offre la meilleure remise !
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quelle est la durée de validité des codes promo ?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Chaque code promo a sa propre date d'expiration, indiquée sur cette page. Assurez-vous de l'utiliser avant cette date.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Les promotions s'appliquent-elles aux frais de livraison ?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Sauf mention contraire, les promotions s'appliquent uniquement au montant des produits, hors frais de livraison.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Comment être informé des nouvelles promotions ?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Inscrivez-vous à notre newsletter pour recevoir en avant-première nos offres exclusives et codes promo.
                  </p>
                </CardContent>
              </Card>
            </div>
          </section>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default PromotionsPage;
