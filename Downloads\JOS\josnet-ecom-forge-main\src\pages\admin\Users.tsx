import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { Link } from "react-router-dom";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import {
  Users as UsersIcon,
  Search,
  Edit,
  Trash2,
  Mail,
  Filter,
  Loader2,
  AlertCircle,
  Eye,
  Award,
  ShoppingBag,
  UserPlus,
  RefreshCw,
} from "lucide-react";
import adminUserApi, { AdminUser, AdminUserListParams } from "@/services/adminUserApi";
import UserForm from "@/components/admin/users/UserForm";

const ROLES_MAP: Record<string, { label: string, color: string }> = {
  'admin': { label: 'Administrateur', color: 'bg-purple-100 text-purple-800' },
  'staff': { label: 'Personnel', color: 'bg-blue-100 text-blue-800' },
  'customer': { label: 'Client', color: 'bg-green-100 text-green-800' }
};

const STATUS_MAP: Record<string, { label: string, color: string }> = {
  'active': { label: 'Actif', color: 'bg-green-100 text-green-800' },
  'inactive': { label: 'Inactif', color: 'bg-gray-100 text-gray-800' },
  'verified': { label: 'Vérifié', color: 'bg-blue-100 text-blue-800' },
  'unverified': { label: 'Non vérifié', color: 'bg-yellow-100 text-yellow-800' }
};

const Users = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isUserFormOpen, setIsUserFormOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [userToDelete, setUserToDelete] = useState<AdminUser | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Prepare query parameters
  const queryParams: AdminUserListParams = {
    page: currentPage,
    page_size: pageSize,
    search: searchQuery || undefined,
    role: roleFilter && roleFilter !== 'all' ? roleFilter : undefined,
    is_active: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
    is_verified: statusFilter === 'verified' ? true : statusFilter === 'unverified' ? false : undefined,
    ordering: '-created_at' // Default ordering: newest first
  };

  // Fetch users
  const {
    data: usersData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['adminUsers', queryParams],
    queryFn: () => adminUserApi.getUsers(queryParams)
  });

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: fr });
  };

  // Handle edit user
  const handleEditUser = (user: AdminUser) => {
    setSelectedUser(user);
    setIsUserFormOpen(true);
  };

  // Handle delete user
  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    setIsDeleting(true);
    try {
      await adminUserApi.deleteUser(userToDelete.id);
      toast({
        title: "Utilisateur supprimé",
        description: `L'utilisateur ${userToDelete.full_name || userToDelete.email} a été supprimé avec succès.`,
      });
      refetch();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la suppression de l'utilisateur.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setUserToDelete(null);
    }
  };

  // Handle add user
  const handleAddUser = () => {
    setSelectedUser(null);
    setIsUserFormOpen(true);
  };

  // Handle contact user
  const handleContactUser = (email: string) => {
    toast({
      title: "Contact utilisateur",
      description: `Envoi d'un message à ${email}`,
    });
    // Dans une app réelle, cette fonction ouvrirait une interface de messagerie
  };

  // Handle user form submit
  const handleUserFormSubmit = () => {
    setIsUserFormOpen(false);
    refetch();
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Calculate total pages
  const totalPages = usersData ? Math.ceil(usersData.count / pageSize) : 0;

  // Generate pagination items
  const renderPaginationItems = () => {
    const items = [];
    const maxVisiblePages = 5;

    // Previous button
    items.push(
      <PaginationItem key="prev">
        <PaginationPrevious
          onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
          className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
        />
      </PaginationItem>
    );

    // Page numbers
    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              isActive={currentPage === i}
              onClick={() => handlePageChange(i)}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      // Show limited pages with ellipsis
      const leftSiblingIndex = Math.max(currentPage - 1, 1);
      const rightSiblingIndex = Math.min(currentPage + 1, totalPages);

      // Show first page
      items.push(
        <PaginationItem key={1}>
          <PaginationLink
            isActive={currentPage === 1}
            onClick={() => handlePageChange(1)}
          >
            1
          </PaginationLink>
        </PaginationItem>
      );

      // Show ellipsis if needed
      if (leftSiblingIndex > 2) {
        items.push(
          <PaginationItem key="left-ellipsis">
            <PaginationLink className="cursor-default">...</PaginationLink>
          </PaginationItem>
        );
      }

      // Show pages around current page
      for (let i = leftSiblingIndex; i <= rightSiblingIndex; i++) {
        if (i !== 1 && i !== totalPages) {
          items.push(
            <PaginationItem key={i}>
              <PaginationLink
                isActive={currentPage === i}
                onClick={() => handlePageChange(i)}
              >
                {i}
              </PaginationLink>
            </PaginationItem>
          );
        }
      }

      // Show ellipsis if needed
      if (rightSiblingIndex < totalPages - 1) {
        items.push(
          <PaginationItem key="right-ellipsis">
            <PaginationLink className="cursor-default">...</PaginationLink>
          </PaginationItem>
        );
      }

      // Show last page
      items.push(
        <PaginationItem key={totalPages}>
          <PaginationLink
            isActive={currentPage === totalPages}
            onClick={() => handlePageChange(totalPages)}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Next button
    items.push(
      <PaginationItem key="next">
        <PaginationNext
          onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
          className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
        />
      </PaginationItem>
    );

    return items;
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Utilisateurs</h1>
          <p className="text-gray-500">Administrez les utilisateurs de votre plateforme</p>
        </div>

        {/* Barre d'actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Rechercher un utilisateur..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
            />
          </div>
          <div className="flex gap-2">
            <Select
              value={roleFilter}
              onValueChange={(value) => {
                setRoleFilter(value);
                setCurrentPage(1); // Reset to first page on filter change
              }}
            >
              <SelectTrigger className="w-[150px]">
                <div className="flex items-center gap-1">
                  <Filter size={16} />
                  <span>{roleFilter ? ROLES_MAP[roleFilter]?.label : "Rôle"}</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les rôles</SelectItem>
                <SelectItem value="customer">Client</SelectItem>
                <SelectItem value="staff">Personnel</SelectItem>
                <SelectItem value="admin">Administrateur</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={statusFilter}
              onValueChange={(value) => {
                setStatusFilter(value);
                setCurrentPage(1); // Reset to first page on filter change
              }}
            >
              <SelectTrigger className="w-[150px]">
                <div className="flex items-center gap-1">
                  <Filter size={16} />
                  <span>{statusFilter ? STATUS_MAP[statusFilter]?.label : "Statut"}</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="active">Actif</SelectItem>
                <SelectItem value="inactive">Inactif</SelectItem>
                <SelectItem value="verified">Vérifié</SelectItem>
                <SelectItem value="unverified">Non vérifié</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={handleAddUser} className="flex items-center gap-1">
              <UserPlus size={16} />
              Nouvel Utilisateur
            </Button>
          </div>
        </div>

        {/* Tableau des utilisateurs */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Utilisateur</TableHead>
                <TableHead>Rôle</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Points</TableHead>
                <TableHead>Commandes</TableHead>
                <TableHead>Inscrit le</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Loading state
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={`skeleton-${index}`}>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </TableCell>
                    <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-8" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <Skeleton className="h-8 w-8 rounded-full" />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : isError ? (
                // Error state
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center text-destructive">
                      <AlertCircle className="h-8 w-8 mb-2" />
                      <p>Une erreur est survenue lors du chargement des utilisateurs.</p>
                      <Button
                        variant="outline"
                        onClick={() => refetch()}
                        className="mt-2"
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Réessayer
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : usersData?.results.length === 0 ? (
                // Empty state
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <UsersIcon className="h-8 w-8 mb-2" />
                      <p>Aucun utilisateur trouvé.</p>
                      {(searchQuery || roleFilter || statusFilter) && (
                        <Button
                          variant="link"
                          onClick={() => {
                            setSearchQuery("");
                            setRoleFilter("");
                            setStatusFilter("");
                          }}
                        >
                          Effacer les filtres
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                // Data state
                usersData?.results.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">#{user.id}</TableCell>
                    <TableCell>
                      <div>
                        <div>{user.full_name || "Sans nom"}</div>
                        <div className="text-xs text-gray-500">{user.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        ROLES_MAP[user.role]?.color || "bg-gray-100 text-gray-800"
                      }`}>
                        {ROLES_MAP[user.role]?.label || user.role}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          user.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}>
                          {user.is_active ? "Actif" : "Inactif"}
                        </span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          user.is_verified
                            ? "bg-blue-100 text-blue-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}>
                          {user.is_verified ? "Vérifié" : "Non vérifié"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Award className="h-4 w-4 text-amber-500" />
                        <span>{user.loyalty_points}</span>
                        <span className="text-xs text-gray-500 ml-1">({user.loyalty_tier})</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <ShoppingBag className="h-4 w-4 text-primary" />
                        <span>{user.order_count || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(user.created_at)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Link to={`/admin/users/${user.id}`}>
                          <Button
                            variant="ghost"
                            size="icon"
                            title="Voir les détails"
                          >
                            <Eye size={16} className="text-primary" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleContactUser(user.email)}
                          title="Contacter"
                        >
                          <Mail size={16} className="text-green-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditUser(user)}
                          title="Modifier"
                        >
                          <Edit size={16} className="text-blue-500" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setUserToDelete(user)}
                          title="Supprimer"
                        >
                          <Trash2 size={16} className="text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {!isLoading && !isError && usersData && usersData.count > 0 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Affichage de {(currentPage - 1) * pageSize + 1} à {Math.min(currentPage * pageSize, usersData.count)} sur {usersData.count} utilisateurs
            </div>
            <Pagination>
              <PaginationContent>
                {renderPaginationItems()}
              </PaginationContent>
            </Pagination>
          </div>
        )}

        {/* User Form Dialog */}
        <Dialog open={isUserFormOpen} onOpenChange={setIsUserFormOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{selectedUser ? "Modifier l'utilisateur" : "Nouvel utilisateur"}</DialogTitle>
              <DialogDescription>
                {selectedUser
                  ? "Modifiez les informations de l'utilisateur ci-dessous."
                  : "Remplissez les informations pour créer un nouvel utilisateur."}
              </DialogDescription>
            </DialogHeader>
            <UserForm
              user={selectedUser}
              onSubmit={handleUserFormSubmit}
              onCancel={() => setIsUserFormOpen(false)}
            />
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!userToDelete} onOpenChange={(open) => !open && setUserToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer cet utilisateur ?</AlertDialogTitle>
              <AlertDialogDescription>
                Cette action est irréversible. Toutes les données associées à cet utilisateur seront supprimées.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting} onClick={() => setUserToDelete(null)}>Annuler</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteUser}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Suppression...
                  </>
                ) : (
                  "Supprimer"
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AdminLayout>
  );
};

export default Users;
