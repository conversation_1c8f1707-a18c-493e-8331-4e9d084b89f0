from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.db.models import Q, Count, Avg
from products.models import Product, Category
from orders.models import Order, OrderItem
from products.serializers import ProductListSerializer
import logging

logger = logging.getLogger(__name__)

class SuggestionsView(APIView):
    """
    API endpoint for personalized product suggestions.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get personalized product suggestions for the authenticated user.
        
        The algorithm considers:
        1. Products from categories the user has purchased from
        2. Highly rated products
        3. Popular products (most ordered)
        4. Featured products
        """
        try:
            user = request.user
            suggestions = []
            
            # Get user's purchase history
            user_orders = Order.objects.filter(user=user, status__in=['delivered', 'processing'])
            user_order_items = OrderItem.objects.filter(order__in=user_orders)
            
            # Get categories from user's purchase history
            purchased_categories = set()
            for item in user_order_items:
                if item.product and item.product.categories.exists():
                    purchased_categories.update(item.product.categories.all())
            
            # Strategy 1: Products from purchased categories (40% weight)
            if purchased_categories:
                category_suggestions = Product.objects.filter(
                    categories__in=purchased_categories,
                    status='published'
                ).exclude(
                    # Exclude products already purchased
                    id__in=user_order_items.values_list('product_id', flat=True)
                ).annotate(
                    avg_rating=Avg('reviews__rating'),
                    order_count=Count('orderitem')
                ).order_by('-avg_rating', '-order_count')[:8]
                
                suggestions.extend(category_suggestions)
            
            # Strategy 2: Highly rated products (30% weight)
            highly_rated = Product.objects.filter(
                status='published'
            ).exclude(
                id__in=user_order_items.values_list('product_id', flat=True)
            ).exclude(
                id__in=[p.id for p in suggestions]
            ).annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).filter(
                avg_rating__gte=4.0,
                review_count__gte=3
            ).order_by('-avg_rating', '-review_count')[:6]
            
            suggestions.extend(highly_rated)
            
            # Strategy 3: Popular products (20% weight)
            popular_products = Product.objects.filter(
                status='published'
            ).exclude(
                id__in=user_order_items.values_list('product_id', flat=True)
            ).exclude(
                id__in=[p.id for p in suggestions]
            ).annotate(
                order_count=Count('orderitem')
            ).filter(
                order_count__gte=2
            ).order_by('-order_count')[:4]
            
            suggestions.extend(popular_products)
            
            # Strategy 4: Featured products (10% weight)
            featured_products = Product.objects.filter(
                status='published',
                is_featured=True
            ).exclude(
                id__in=user_order_items.values_list('product_id', flat=True)
            ).exclude(
                id__in=[p.id for p in suggestions]
            ).order_by('-created_at')[:2]
            
            suggestions.extend(featured_products)
            
            # If we don't have enough suggestions, fill with random published products
            if len(suggestions) < 12:
                additional_products = Product.objects.filter(
                    status='published'
                ).exclude(
                    id__in=user_order_items.values_list('product_id', flat=True)
                ).exclude(
                    id__in=[p.id for p in suggestions]
                ).order_by('?')[:12 - len(suggestions)]
                
                suggestions.extend(additional_products)
            
            # Remove duplicates and limit to 12 suggestions
            unique_suggestions = []
            seen_ids = set()
            for product in suggestions:
                if product.id not in seen_ids:
                    unique_suggestions.append(product)
                    seen_ids.add(product.id)
                if len(unique_suggestions) >= 12:
                    break
            
            # Serialize the suggestions
            serializer = ProductListSerializer(unique_suggestions, many=True, context={'request': request})
            
            # Transform data to match frontend expectations
            suggestions_data = []
            for product_data in serializer.data:
                # Calculate average rating
                avg_rating = 0
                if hasattr(product_data, 'reviews') and product_data.get('reviews'):
                    ratings = [review.get('rating', 0) for review in product_data['reviews']]
                    avg_rating = sum(ratings) / len(ratings) if ratings else 0
                else:
                    # Get rating from database if not in serializer
                    product = Product.objects.get(id=product_data['id'])
                    avg_rating = product.reviews.aggregate(avg=Avg('rating'))['avg'] or 4.0
                
                # Get primary category
                category_name = "Général"
                if product_data.get('categories'):
                    category_name = product_data['categories'][0].get('name', 'Général')
                
                # Get primary image
                image_url = "/placeholder.svg"
                if product_data.get('images') and len(product_data['images']) > 0:
                    image_url = product_data['images'][0].get('image', "/placeholder.svg")
                
                suggestion = {
                    'id': product_data['id'],
                    'name': product_data['name'],
                    'price': float(product_data['price']),
                    'image': image_url,
                    'rating': round(avg_rating, 1),
                    'category': category_name
                }
                suggestions_data.append(suggestion)
            
            logger.info(f"Generated {len(suggestions_data)} suggestions for user {user.email}")
            
            return Response(suggestions_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error generating suggestions for user {request.user.email}: {str(e)}")
            
            # Return fallback suggestions in case of error
            fallback_products = Product.objects.filter(
                status='published'
            ).order_by('-created_at')[:6]
            
            fallback_data = []
            for product in fallback_products:
                # Get primary category
                category_name = "Général"
                if product.categories.exists():
                    category_name = product.categories.first().name
                
                # Get primary image
                image_url = "/placeholder.svg"
                if product.images.exists():
                    image_url = product.images.first().image.url if product.images.first().image else "/placeholder.svg"
                
                fallback = {
                    'id': product.id,
                    'name': product.name,
                    'price': float(product.price),
                    'image': image_url,
                    'rating': 4.0,  # Default rating
                    'category': category_name
                }
                fallback_data.append(fallback)
            
            return Response(fallback_data, status=status.HTTP_200_OK)
