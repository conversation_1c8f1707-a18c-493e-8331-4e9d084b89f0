from django.core.cache import cache
from django.conf import settings
from django.utils.translation import get_language, activate
from .models import TranslationKey, Translation, TranslationCache
import json


def get_translation(key, language_code=None, fallback=None):
    """
    Récupérer une traduction pour une clé donnée
    
    Args:
        key (str): La clé de traduction
        language_code (str): Le code de langue (par défaut: langue actuelle)
        fallback (str): Valeur de fallback si aucune traduction trouvée
    
    Returns:
        str: La traduction ou la valeur de fallback
    """
    if language_code is None:
        language_code = get_language() or settings.LANGUAGE_CODE
    
    # Essayer de récupérer depuis le cache
    cache_key = f"translation_{key}_{language_code}"
    cached_translation = cache.get(cache_key)
    
    if cached_translation is not None:
        return cached_translation
    
    try:
        # Récupérer depuis la base de données
        translation_key = TranslationKey.objects.get(key=key, is_active=True)
        translation_value = translation_key.get_translation(language_code)
        
        # Mettre en cache
        cache.set(cache_key, translation_value, settings.TRANSLATION_CACHE_TIMEOUT)
        return translation_value
        
    except TranslationKey.DoesNotExist:
        # Si la clé n'existe pas, la créer automatiquement
        create_translation_key(key, fallback or key)
        return fallback or key


def create_translation_key(key, default_value=None, description=None, category=None):
    """
    Créer une nouvelle clé de traduction
    
    Args:
        key (str): La clé de traduction
        default_value (str): Valeur par défaut
        description (str): Description de la clé
        category (str): Catégorie de la clé
    
    Returns:
        TranslationKey: L'objet clé créé
    """
    if default_value is None:
        default_value = key
    
    # Créer la clé
    translation_key, created = TranslationKey.objects.get_or_create(
        key=key,
        defaults={
            'description': description,
            'category': category or 'auto-generated',
            'is_active': True
        }
    )
    
    if created:
        # Créer les traductions par défaut pour toutes les langues
        for lang_code, lang_name in settings.LANGUAGES:
            Translation.objects.create(
                key=translation_key,
                language_code=lang_code,
                value=default_value,
                is_approved=False
            )
    
    return translation_key


def get_all_translations(language_code=None):
    """
    Récupérer toutes les traductions pour une langue
    
    Args:
        language_code (str): Le code de langue
    
    Returns:
        dict: Dictionnaire des traductions {clé: valeur}
    """
    if language_code is None:
        language_code = get_language() or settings.LANGUAGE_CODE
    
    # Essayer de récupérer depuis le cache
    try:
        cache_obj = TranslationCache.objects.get(language_code=language_code)
        return cache_obj.get_translations()
    except TranslationCache.DoesNotExist:
        pass
    
    # Récupérer depuis la base de données
    translations = {}
    translation_objects = Translation.objects.filter(
        language_code=language_code,
        key__is_active=True,
        is_approved=True
    ).select_related('key')
    
    for translation in translation_objects:
        translations[translation.key.key] = translation.value
    
    # Mettre en cache
    cache_obj, created = TranslationCache.objects.get_or_create(
        language_code=language_code
    )
    cache_obj.set_translations(translations)
    
    return translations


def refresh_translation_cache(language_code=None):
    """
    Actualiser le cache des traductions
    
    Args:
        language_code (str): Le code de langue (None pour toutes les langues)
    """
    if language_code:
        languages = [language_code]
    else:
        languages = [lang[0] for lang in settings.LANGUAGES]
    
    for lang in languages:
        # Supprimer le cache existant
        try:
            cache_obj = TranslationCache.objects.get(language_code=lang)
            cache_obj.delete()
        except TranslationCache.DoesNotExist:
            pass
        
        # Recréer le cache
        get_all_translations(lang)
        
        # Supprimer les caches individuels
        translation_keys = TranslationKey.objects.filter(is_active=True)
        for key_obj in translation_keys:
            cache_key = f"translation_{key_obj.key}_{lang}"
            cache.delete(cache_key)


def translate_model_field(instance, field_name, language_code=None):
    """
    Traduire un champ de modèle
    
    Args:
        instance: Instance du modèle
        field_name (str): Nom du champ
        language_code (str): Code de langue
    
    Returns:
        str: Valeur traduite
    """
    if language_code is None:
        language_code = get_language() or settings.LANGUAGE_CODE
    
    # Construire la clé de traduction
    model_name = instance._meta.model_name
    app_label = instance._meta.app_label
    key = f"{app_label}.{model_name}.{field_name}.{instance.pk}"
    
    # Récupérer la valeur originale
    original_value = getattr(instance, field_name, '')
    
    # Récupérer la traduction
    return get_translation(key, language_code, original_value)


def bulk_create_translations(translations_data):
    """
    Créer des traductions en masse
    
    Args:
        translations_data (list): Liste de dictionnaires avec les données de traduction
            Format: [
                {
                    'key': 'ma.cle',
                    'category': 'interface',
                    'description': 'Description',
                    'translations': {
                        'fr': 'Valeur française',
                        'en': 'English value',
                        'sw': 'Kiswahili value',
                        'rn': 'Kirundi value'
                    }
                }
            ]
    
    Returns:
        dict: Statistiques de création
    """
    stats = {
        'keys_created': 0,
        'keys_updated': 0,
        'translations_created': 0,
        'translations_updated': 0
    }
    
    for data in translations_data:
        key = data['key']
        category = data.get('category', 'bulk-import')
        description = data.get('description', '')
        translations = data.get('translations', {})
        
        # Créer ou récupérer la clé
        translation_key, key_created = TranslationKey.objects.get_or_create(
            key=key,
            defaults={
                'category': category,
                'description': description,
                'is_active': True
            }
        )
        
        if key_created:
            stats['keys_created'] += 1
        else:
            stats['keys_updated'] += 1
        
        # Créer ou mettre à jour les traductions
        for lang_code, value in translations.items():
            if lang_code in [lang[0] for lang in settings.LANGUAGES]:
                translation, trans_created = Translation.objects.get_or_create(
                    key=translation_key,
                    language_code=lang_code,
                    defaults={
                        'value': value,
                        'is_approved': True
                    }
                )
                
                if trans_created:
                    stats['translations_created'] += 1
                elif translation.value != value:
                    translation.value = value
                    translation.is_approved = True
                    translation.save()
                    stats['translations_updated'] += 1
    
    # Actualiser le cache
    refresh_translation_cache()
    
    return stats


def export_translations(language_code=None, format='json'):
    """
    Exporter les traductions
    
    Args:
        language_code (str): Code de langue (None pour toutes)
        format (str): Format d'export ('json', 'csv')
    
    Returns:
        str: Données exportées
    """
    if language_code:
        languages = [language_code]
    else:
        languages = [lang[0] for lang in settings.LANGUAGES]
    
    export_data = {}
    
    for lang in languages:
        translations = get_all_translations(lang)
        export_data[lang] = translations
    
    if format == 'json':
        return json.dumps(export_data, ensure_ascii=False, indent=2)
    elif format == 'csv':
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # En-têtes
        headers = ['key'] + languages
        writer.writerow(headers)
        
        # Récupérer toutes les clés
        all_keys = set()
        for lang_translations in export_data.values():
            all_keys.update(lang_translations.keys())
        
        # Écrire les données
        for key in sorted(all_keys):
            row = [key]
            for lang in languages:
                row.append(export_data.get(lang, {}).get(key, ''))
            writer.writerow(row)
        
        return output.getvalue()
    
    return str(export_data)
