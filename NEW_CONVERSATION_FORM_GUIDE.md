# 📝 Guide du Formulaire de Nouvelle Conversation

## 🎯 **Vue d'ensemble**

Le formulaire de création de nouvelle conversation permet aux administrateurs de démarrer une conversation avec un client depuis la page admin des messages (`http://localhost:8080/admin/messages`).

## 🚀 **Fonctionnalités principales**

### **1. 👤 Gestion des clients**
- **Recherche de clients existants** par email ou nom
- **Création de nouveaux clients** directement dans le formulaire
- **Affichage des informations client** (commandes, statut, etc.)
- **Sélection rapide** depuis les résultats de recherche

### **2. 💬 Configuration de la conversation**
- **Sujet** de la conversation (obligatoire)
- **Message initial** (obligatoire)
- **Niveau de priorité** (faible, moyenne, élevée, urgente)
- **Catégorisation** avec couleurs
- **Étiquettes multiples** pour l'organisation

### **3. 📎 Pièces jointes**
- **Upload de fichiers** (images, PDF, documents)
- **Limite de taille** : 10MB par fichier
- **Maximum 5 fichiers** par conversation
- **Prévisualisation** des fichiers sélectionnés

### **4. 🔗 Référence commande**
- **Lien optionnel** avec une commande existante
- **Numéro de commande** pour le contexte

## 📋 **Guide d'utilisation étape par étape**

### **Étape 1 : Accéder au formulaire**
1. Aller sur `http://localhost:8080/admin/messages`
2. Cliquer sur le bouton **"Nouvelle conversation"**
3. Le formulaire s'ouvre dans une modal

### **Étape 2 : Sélectionner ou créer un client**

#### **Option A : Client existant**
1. Taper l'email ou le nom dans le champ de recherche
2. Attendre les résultats (recherche automatique après 3 caractères)
3. Cliquer sur le client souhaité dans la liste
4. Les informations du client s'affichent (commandes, statut, etc.)

#### **Option B : Nouveau client**
1. Cliquer sur **"Créer un nouveau client"**
2. Remplir les informations :
   - **Email** (obligatoire)
   - **Nom complet** (obligatoire)
   - **Téléphone** (optionnel)
3. Le client sera créé automatiquement lors de la création de la conversation

### **Étape 3 : Configurer la conversation**
1. **Sujet** : Entrer un titre descriptif (ex: "Question sur la commande #12345")
2. **Message initial** : Rédiger le premier message de la conversation
3. **Priorité** : Choisir le niveau d'urgence
   - 🟢 **Faible** : Questions générales
   - 🟡 **Moyenne** : Demandes standard
   - 🟠 **Élevée** : Problèmes importants
   - 🔴 **Urgente** : Situations critiques

### **Étape 4 : Organiser avec catégories et étiquettes**
1. **Catégorie** : Sélectionner une catégorie (Support, Ventes, etc.)
2. **Étiquettes** : Cliquer sur les étiquettes pertinentes
   - Les étiquettes sélectionnées changent de couleur
   - Plusieurs étiquettes peuvent être sélectionnées

### **Étape 5 : Ajouter des pièces jointes (optionnel)**
1. Cliquer sur la zone de téléchargement
2. Sélectionner les fichiers (max 5, 10MB chacun)
3. Les fichiers s'affichent avec leur statut d'upload

### **Étape 6 : Référence commande (optionnel)**
1. Entrer le numéro de commande si la conversation est liée à une commande
2. Cela aide à contextualiser la conversation

### **Étape 7 : Vérifier et créer**
1. **Résumé automatique** : Un aperçu s'affiche avec toutes les informations
2. **Vérification** : S'assurer que tout est correct
3. **Création** : Cliquer sur "Créer la conversation"

## 🎨 **Interface utilisateur**

### **Indicateurs visuels**
- **🔍 Recherche en cours** : Spinner pendant la recherche de clients
- **✅ Client sélectionné** : Bordure bleue autour du client choisi
- **🎯 Priorité** : Couleurs pour chaque niveau de priorité
- **🏷️ Étiquettes** : Couleurs personnalisées pour chaque étiquette
- **📊 Statut client** : Indicateur vert/gris pour actif/inactif

### **Informations client affichées**
- **Nom complet** et email
- **Téléphone** (si disponible)
- **Nombre de commandes** total
- **Montant total** dépensé
- **Dernière commande** (date)
- **Statut** (actif/inactif)

## ⚡ **Fonctionnalités avancées**

### **Recherche intelligente**
- Recherche par **email partiel**
- Recherche par **nom** (prénom ou nom de famille)
- Recherche par **numéro de téléphone**
- **Résultats en temps réel** (après 3 caractères)

### **Validation automatique**
- **Vérification des champs obligatoires**
- **Validation de l'email** pour les nouveaux clients
- **Messages d'erreur clairs** en cas de problème

### **Gestion d'état**
- **Sauvegarde automatique** des données saisies
- **Annulation propre** sans perte de données
- **Gestion des erreurs** avec messages informatifs

## 🔧 **Configuration technique**

### **APIs utilisées**
- **`customerApi.searchCustomers()`** : Recherche de clients
- **`customerApi.createCustomer()`** : Création de nouveaux clients
- **`messagingApi.createConversation()`** : Création de conversations
- **`messagingApi.getCategories()`** : Liste des catégories
- **`messagingApi.getTags()`** : Liste des étiquettes

### **Données mock disponibles**
Le système utilise des données de démonstration si l'API n'est pas disponible :
- **5 clients de test** avec informations complètes
- **Catégories** : Support, Ventes, Technique, Facturation
- **Étiquettes** : Urgent, Bug, Fonctionnalité, Question, etc.

## 📱 **Responsive Design**

Le formulaire s'adapte à toutes les tailles d'écran :
- **Desktop** : Affichage en colonnes pour optimiser l'espace
- **Tablet** : Adaptation des grilles et espacement
- **Mobile** : Empilement vertical pour la lisibilité

## 🎯 **Bonnes pratiques**

### **Pour les administrateurs**
1. **Sujet descriptif** : Utiliser des sujets clairs et spécifiques
2. **Catégorisation** : Toujours assigner une catégorie appropriée
3. **Priorité réaliste** : Ne pas abuser des priorités élevées
4. **Message initial complet** : Inclure toutes les informations nécessaires

### **Gestion des clients**
1. **Vérifier l'existence** : Toujours rechercher avant de créer un nouveau client
2. **Informations complètes** : Remplir le maximum d'informations pour les nouveaux clients
3. **Cohérence** : Utiliser des formats standardisés (téléphone, adresse)

## 🚨 **Gestion d'erreurs**

### **Erreurs courantes et solutions**
- **"Client non trouvé"** : Créer un nouveau client ou vérifier l'orthographe
- **"Sujet requis"** : Remplir le champ sujet obligatoire
- **"Message requis"** : Rédiger un message initial
- **"Erreur de création"** : Vérifier la connexion et réessayer

### **Messages d'aide**
- **Tooltips** sur les champs complexes
- **Validation en temps réel** pour les erreurs
- **Messages de succès** pour confirmer les actions

## 🎉 **Résultat**

Après création réussie :
1. **Notification de succès** affichée
2. **Modal fermée** automatiquement
3. **Conversation sélectionnée** dans la liste
4. **Interface de chat** ouverte pour continuer la conversation
5. **Listes mises à jour** (conversations et clients)

## 🔄 **Intégration**

Le formulaire s'intègre parfaitement avec :
- **Liste des conversations** : Mise à jour automatique
- **Interface de chat** : Ouverture directe de la nouvelle conversation
- **Gestion des clients** : Synchronisation avec la base de données
- **Système de notifications** : Alertes pour le client et l'admin

---

**Le formulaire de nouvelle conversation offre une expérience complète et intuitive pour démarrer efficacement des conversations avec les clients ! 🚀**
