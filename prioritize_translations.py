#!/usr/bin/env python3
"""
Script pour analyser et prioriser les traductions par importance
"""
import json
import re
from collections import defaultdict, Counter
from pathlib import Path

def load_detected_texts():
    """Charger les textes détectés"""
    try:
        with open('new_translations_template.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ Fichier new_translations_template.json non trouvé")
        print("   Exécutez d'abord: python detect_hardcoded_texts.py")
        return []

def analyze_by_priority():
    """Analyser les textes par priorité"""
    texts = load_detected_texts()
    if not texts:
        return
    
    print("🎯 ANALYSE PAR PRIORITÉ")
    print("=" * 50)
    
    # Définir les priorités basées sur les fichiers et contenus
    priority_rules = {
        'CRITIQUE': {
            'files': ['nav', 'header', 'auth', 'login', 'cart', 'checkout', 'payment'],
            'keywords': ['erreur', 'error', 'connexion', 'login', 'panier', 'cart', 'commander', 'payer', 'total'],
            'weight': 10
        },
        'HAUTE': {
            'files': ['product', 'search', 'filter', 'home', 'dashboard'],
            'keywords': ['produit', 'product', 'recherche', 'search', 'prix', 'price', 'stock', 'disponible'],
            'weight': 7
        },
        'MOYENNE': {
            'files': ['profile', 'account', 'order', 'notification', 'message'],
            'keywords': ['profil', 'profile', 'compte', 'account', 'commande', 'order', 'notification'],
            'weight': 5
        },
        'BASSE': {
            'files': ['admin', 'cms', 'blog', 'about', 'contact', 'help'],
            'keywords': ['administration', 'admin', 'blog', 'aide', 'help', 'contact'],
            'weight': 3
        }
    }
    
    categorized = defaultdict(list)
    
    for text_item in texts:
        file_path = text_item['source_file'].lower()
        text_content = text_item['translations']['fr'].lower()
        
        priority = 'BASSE'  # Par défaut
        max_weight = 0
        
        for priority_level, rules in priority_rules.items():
            weight = 0
            
            # Vérifier les fichiers
            if any(keyword in file_path for keyword in rules['files']):
                weight += rules['weight']
            
            # Vérifier les mots-clés dans le texte
            if any(keyword in text_content for keyword in rules['keywords']):
                weight += rules['weight'] // 2
            
            if weight > max_weight:
                max_weight = weight
                priority = priority_level
        
        categorized[priority].append(text_item)
    
    # Afficher les résultats
    priority_order = ['CRITIQUE', 'HAUTE', 'MOYENNE', 'BASSE']
    total_texts = len(texts)
    
    for priority in priority_order:
        count = len(categorized[priority])
        percentage = (count / total_texts * 100) if total_texts > 0 else 0
        
        print(f"\n🔴 {priority}: {count} textes ({percentage:.1f}%)")
        
        # Afficher quelques exemples
        for i, item in enumerate(categorized[priority][:5]):
            file_name = Path(item['source_file']).name
            text = item['translations']['fr'][:50]
            print(f"   {i+1}. {file_name}: \"{text}...\"")
        
        if count > 5:
            print(f"   ... et {count - 5} autres")
    
    return categorized

def analyze_by_component():
    """Analyser les textes par composant/page"""
    texts = load_detected_texts()
    if not texts:
        return
    
    print(f"\n📱 ANALYSE PAR COMPOSANT")
    print("=" * 50)
    
    by_component = defaultdict(list)
    
    for text_item in texts:
        file_path = text_item['source_file']
        component_name = Path(file_path).stem
        by_component[component_name].append(text_item)
    
    # Trier par nombre de textes
    sorted_components = sorted(by_component.items(), key=lambda x: len(x[1]), reverse=True)
    
    print(f"📊 TOP 15 COMPOSANTS AVEC LE PLUS DE TEXTES:")
    for i, (component, texts_list) in enumerate(sorted_components[:15], 1):
        print(f"   {i:2d}. {component}: {len(texts_list)} textes")
    
    return by_component

def generate_priority_plan():
    """Générer un plan de traduction priorisé"""
    print(f"\n📋 PLAN DE TRADUCTION PRIORISÉ")
    print("=" * 50)
    
    categorized = analyze_by_priority()
    if not categorized:
        return
    
    plan = {
        "phase_1_critique": {
            "name": "Phase 1 - Éléments Critiques (1-2 jours)",
            "description": "Navigation, authentification, panier, checkout",
            "texts": categorized.get('CRITIQUE', []),
            "estimated_hours": 16
        },
        "phase_2_haute": {
            "name": "Phase 2 - Interface Principale (2-3 jours)", 
            "description": "Produits, recherche, accueil",
            "texts": categorized.get('HAUTE', []),
            "estimated_hours": 24
        },
        "phase_3_moyenne": {
            "name": "Phase 3 - Fonctionnalités Secondaires (2-3 jours)",
            "description": "Profil, commandes, notifications",
            "texts": categorized.get('MOYENNE', []),
            "estimated_hours": 20
        },
        "phase_4_basse": {
            "name": "Phase 4 - Contenu Éditorial (1-2 jours)",
            "description": "CMS, blog, pages d'aide",
            "texts": categorized.get('BASSE', []),
            "estimated_hours": 12
        }
    }
    
    total_hours = sum(phase['estimated_hours'] for phase in plan.values())
    total_texts = sum(len(phase['texts']) for phase in plan.values())
    
    print(f"⏱️ ESTIMATION TOTALE: {total_hours} heures ({total_hours//8} jours)")
    print(f"📝 TOTAL TEXTES: {total_texts}")
    
    for phase_key, phase in plan.items():
        count = len(phase['texts'])
        hours = phase['estimated_hours']
        print(f"\n✅ {phase['name']}")
        print(f"   📊 {count} textes à traduire")
        print(f"   ⏱️ Estimation: {hours} heures")
        print(f"   📝 {phase['description']}")
    
    # Sauvegarder le plan
    with open('translation_priority_plan.json', 'w', encoding='utf-8') as f:
        # Convertir pour la sérialisation JSON
        json_plan = {}
        for phase_key, phase in plan.items():
            json_plan[phase_key] = {
                'name': phase['name'],
                'description': phase['description'],
                'estimated_hours': phase['estimated_hours'],
                'text_count': len(phase['texts']),
                'sample_texts': [
                    {
                        'key': text['key'],
                        'text': text['translations']['fr'],
                        'file': text['source_file']
                    }
                    for text in phase['texts'][:10]  # Échantillon de 10 textes
                ]
            }
        
        json.dump(json_plan, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Plan sauvegardé dans: translation_priority_plan.json")
    return plan

def create_phase_1_translations():
    """Créer un fichier de traductions pour la Phase 1 (critique)"""
    texts = load_detected_texts()
    if not texts:
        return
    
    print(f"\n🚀 CRÉATION DU FICHIER PHASE 1")
    print("=" * 50)
    
    # Filtrer les textes critiques
    critical_keywords = ['nav', 'header', 'auth', 'login', 'cart', 'checkout', 'payment']
    critical_texts = []
    
    for text_item in texts:
        file_path = text_item['source_file'].lower()
        if any(keyword in file_path for keyword in critical_keywords):
            critical_texts.append(text_item)
    
    # Limiter à 50 textes les plus importants pour commencer
    phase_1_texts = critical_texts[:50]
    
    with open('phase_1_critical_translations.json', 'w', encoding='utf-8') as f:
        json.dump(phase_1_texts, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Fichier Phase 1 créé: phase_1_critical_translations.json")
    print(f"📊 {len(phase_1_texts)} traductions critiques à traiter en priorité")
    
    # Afficher quelques exemples
    print(f"\n📝 EXEMPLES DE TRADUCTIONS CRITIQUES:")
    for i, text in enumerate(phase_1_texts[:10], 1):
        file_name = Path(text['source_file']).name
        original = text['translations']['fr']
        print(f"   {i:2d}. {file_name}: \"{original}\"")
    
    return phase_1_texts

def show_recommendations():
    """Afficher les recommandations finales"""
    print(f"\n💡 RECOMMANDATIONS")
    print("=" * 50)
    
    print(f"🎯 STRATÉGIE RECOMMANDÉE:")
    print(f"   1. Commencez par la Phase 1 (éléments critiques)")
    print(f"   2. Utilisez l'admin Django pour traduire rapidement")
    print(f"   3. Testez chaque phase avant de passer à la suivante")
    print(f"   4. Impliquez des locuteurs natifs pour la qualité")
    
    print(f"\n🛠️ OUTILS À UTILISER:")
    print(f"   • Admin Django: http://localhost:8000/admin/translations/")
    print(f"   • Rosetta: http://localhost:8000/rosetta/")
    print(f"   • Google Translate API (pour l'aide)")
    print(f"   • DeepL (pour une meilleure qualité)")
    
    print(f"\n⚡ OPTIMISATIONS:")
    print(f"   • Groupez les traductions similaires")
    print(f"   • Réutilisez les traductions communes")
    print(f"   • Automatisez avec des scripts")
    print(f"   • Validez avec des utilisateurs natifs")

def main():
    """Fonction principale"""
    print("🎯 ANALYSE ET PRIORISATION DES TRADUCTIONS")
    print("=" * 60)
    
    # Analyser par priorité
    analyze_by_priority()
    
    # Analyser par composant
    analyze_by_component()
    
    # Générer le plan priorisé
    generate_priority_plan()
    
    # Créer le fichier Phase 1
    create_phase_1_translations()
    
    # Afficher les recommandations
    show_recommendations()
    
    print(f"\n🎉 ANALYSE TERMINÉE!")
    print(f"\n📁 FICHIERS GÉNÉRÉS:")
    print(f"   • translation_priority_plan.json")
    print(f"   • phase_1_critical_translations.json")

if __name__ == "__main__":
    main()
