#!/usr/bin/env python
"""
Test d'intégration complète Backend + Frontend.
Vérifie que tout le système fonctionne ensemble.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'josnet_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from cms.models import BlogPost, BlogCategory
from core.models import SiteSettings, NewsletterSubscription, NewsletterCampaign
from products.models import Product, Category, Promotion
from notifications.models import Notification

User = get_user_model()


def test_backend_frontend_integration():
    """Test d'intégration complète backend-frontend."""
    print("🔗 TEST: INTÉGRATION BACKEND ↔ FRONTEND")
    print("=" * 80)
    
    # Créer un utilisateur test
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Integration',
            'last_name': 'Test',
            'password': 'testpass123'
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
    
    # Client authentifié
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    auth_client = APIClient()
    auth_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    # Client public
    public_client = APIClient()
    
    print(f"✅ Utilisateur test créé: {user.email}")
    
    # Test 1: API publique pour le frontend
    print(f"\n🧪 TEST 1: APIs publiques pour le frontend")
    
    endpoints_to_test = [
        ('/api/v1/cms/blog/posts/', 'Articles de blog'),
        ('/api/v1/cms/blog/categories/', 'Catégories de blog'),
        ('/api/v1/core/site-settings/', 'Paramètres du site'),
        ('/api/v1/products/categories/', 'Catégories de produits'),
        ('/api/v1/products/products/', 'Produits'),
        ('/api/v1/promotions/', 'Promotions actives'),
    ]
    
    api_results = []
    
    for endpoint, description in endpoints_to_test:
        response = public_client.get(endpoint)
        success = response.status_code == 200
        api_results.append(success)
        
        status = "✅" if success else "❌"
        print(f"   {status} {description}: {response.status_code}")
    
    api_success = all(api_results)
    
    # Test 2: Authentification pour le frontend
    print(f"\n🧪 TEST 2: Authentification utilisateur")
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'testpass123'
    }
    
    response = public_client.post('/api/v1/auth/login/', login_data, format='json')
    
    if response.status_code == 200:
        login_data = response.json()
        print(f"   ✅ Connexion réussie")
        print(f"   🔑 Token reçu: {login_data.get('access', 'N/A')[:20]}...")
        auth_success = True
    else:
        print(f"   ❌ Échec de connexion: {response.status_code}")
        auth_success = False
    
    # Test 3: Notifications pour le frontend
    print(f"\n🧪 TEST 3: Système de notifications")
    
    # Créer une notification test
    notification = Notification.objects.create(
        user=user,
        title='Test d\'intégration - Nouvelle promotion disponible',
        message='Une promotion exceptionnelle vous attend ! Découvrez nos offres spéciales.',
        type='promotion',
        is_read=False
    )
    
    print(f"   ✅ Notification créée: {notification.title}")
    
    # Tester l'API des notifications
    response = auth_client.get('/api/v1/notifications/notifications/')
    
    if response.status_code == 200:
        notifications_data = response.json()
        notifications = notifications_data.get('results', [])
        
        # Chercher notre notification
        test_notification = None
        for notif in notifications:
            if 'Test d\'intégration' in notif['title']:
                test_notification = notif
                break
        
        if test_notification:
            print(f"   ✅ Notification accessible via API")
            print(f"   📝 Titre: {test_notification['title']}")
            print(f"   📊 Type: {test_notification['type']}")
            print(f"   📖 Lue: {test_notification['is_read']}")
            notifications_success = True
        else:
            print(f"   ❌ Notification non trouvée dans l'API")
            notifications_success = False
    else:
        print(f"   ❌ API notifications non accessible: {response.status_code}")
        notifications_success = False
    
    # Test 4: Données CMS pour le frontend
    print(f"\n🧪 TEST 4: Contenu CMS pour le frontend")
    
    # Vérifier qu'il y a du contenu CMS
    response = public_client.get('/api/v1/cms/blog/posts/')
    
    if response.status_code == 200:
        blog_data = response.json()
        posts = blog_data.get('results', [])
        
        if posts:
            latest_post = posts[0]
            print(f"   ✅ Articles de blog disponibles: {len(posts)}")
            print(f"   📝 Dernier article: {latest_post['title']}")
            print(f"   📄 Contenu: {len(latest_post['content'])} caractères")
            cms_success = True
        else:
            print(f"   ⚠️  Aucun article de blog trouvé")
            cms_success = False
    else:
        print(f"   ❌ API blog non accessible: {response.status_code}")
        cms_success = False
    
    # Test 5: Abonnements newsletter pour le frontend
    print(f"\n🧪 TEST 5: Gestion des abonnements newsletter")
    
    # Créer un abonnement
    subscription_data = {
        'email': '<EMAIL>',
        'preferences_promotions': True,
        'preferences_new_products': True,
        'preferences_newsletters': True
    }
    
    response = public_client.post('/api/v1/core/newsletter-subscribe/', subscription_data, format='json')
    
    if response.status_code in [200, 201]:
        print(f"   ✅ Abonnement newsletter créé/mis à jour")
        
        # Vérifier l'abonnement
        subscription = NewsletterSubscription.objects.filter(email='<EMAIL>').first()
        if subscription:
            print(f"   ✅ Abonnement confirmé en base")
            print(f"   🎉 Promotions: {subscription.preferences_promotions}")
            print(f"   ✨ Nouveaux produits: {subscription.preferences_new_products}")
            newsletter_success = True
        else:
            print(f"   ❌ Abonnement non trouvé en base")
            newsletter_success = False
    else:
        print(f"   ❌ Échec création abonnement: {response.status_code}")
        newsletter_success = False
    
    # Résumé final
    print(f"\n" + "=" * 80)
    print("📊 RÉSUMÉ INTÉGRATION BACKEND ↔ FRONTEND")
    print("=" * 80)
    
    tests = [
        ("APIs publiques pour frontend", api_success),
        ("Authentification utilisateur", auth_success),
        ("Système de notifications", notifications_success),
        ("Contenu CMS pour frontend", cms_success),
        ("Gestion abonnements newsletter", newsletter_success)
    ]
    
    passed = sum(1 for _, success in tests if success)
    failed = len(tests) - passed
    
    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nTotal: {len(tests)} tests")
    print(f"Réussis: {passed}")
    print(f"Échoués: {failed}")
    print(f"Pourcentage de réussite: {(passed/len(tests)*100):.1f}%")
    
    if failed == 0:
        print(f"\n🎉 INTÉGRATION BACKEND ↔ FRONTEND 100% OPÉRATIONNELLE !")
        print(f"✅ Toutes les APIs sont accessibles depuis le frontend")
        print(f"✅ L'authentification fonctionne")
        print(f"✅ Les notifications sont disponibles")
        print(f"✅ Le contenu CMS est accessible")
        print(f"✅ Les abonnements newsletter fonctionnent")
        print(f"\n🚀 LE SYSTÈME COMPLET EST PRÊT !")
    elif failed <= 1:
        print(f"\n🎉 INTÉGRATION QUASI-OPÉRATIONNELLE !")
        print(f"✅ {passed}/{len(tests)} composants fonctionnent")
        print(f"🚀 Le système est utilisable")
    else:
        print(f"\n⚠️  {failed} composants nécessitent des ajustements")
    
    print("=" * 80)
    
    return failed == 0


def test_email_notifications_integration():
    """Test d'intégration des notifications email."""
    print("\n📧 TEST: INTÉGRATION NOTIFICATIONS EMAIL")
    print("=" * 80)
    
    # Compter les abonnés actifs
    active_subscribers = NewsletterSubscription.objects.filter(is_active=True).count()
    promo_subscribers = NewsletterSubscription.objects.filter(
        is_active=True, preferences_promotions=True
    ).count()
    
    print(f"📊 Abonnés actifs: {active_subscribers}")
    print(f"🎉 Abonnés promotions: {promo_subscribers}")
    
    # Compter les campagnes récentes
    recent_campaigns = NewsletterCampaign.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=1)
    ).count()
    
    print(f"📧 Campagnes récentes (1h): {recent_campaigns}")
    
    # Vérifier les vraies adresses
    real_emails = ['<EMAIL>', '<EMAIL>']
    
    print(f"\n🎯 VÉRIFICATION DES VRAIES ADRESSES:")
    for email in real_emails:
        subscription = NewsletterSubscription.objects.filter(
            email=email, is_active=True
        ).first()
        
        if subscription:
            print(f"   ✅ {email} - Abonné actif")
            print(f"      🎉 Promotions: {subscription.preferences_promotions}")
            print(f"      ✨ Nouveaux produits: {subscription.preferences_new_products}")
        else:
            print(f"   ❌ {email} - Non abonné")
    
    if recent_campaigns > 0 and promo_subscribers > 0:
        print(f"\n✅ SYSTÈME EMAIL INTÉGRÉ ET FONCTIONNEL")
        print(f"📬 Vérifiez vos boîtes email pour voir les résultats")
        return True
    else:
        print(f"\n⚠️  Système email partiellement configuré")
        return False


def main():
    """Test d'intégration principal."""
    print("🚀 TEST D'INTÉGRATION COMPLÈTE - JOSNET NETWORK")
    print("=" * 80)
    print("Test de l'intégration complète Backend ↔ Frontend")
    print("Vérification que tous les composants fonctionnent ensemble")
    print("=" * 80)
    
    try:
        # Test d'intégration principal
        integration_success = test_backend_frontend_integration()
        
        # Test d'intégration des emails
        email_success = test_email_notifications_integration()
        
        # Résumé final
        print(f"\n" + "=" * 80)
        print("🎯 RÉSUMÉ FINAL D'INTÉGRATION")
        print("=" * 80)
        
        if integration_success and email_success:
            print(f"🎉 SYSTÈME JOSNET NETWORK 100% INTÉGRÉ !")
            print(f"✅ Backend ↔ Frontend : Parfaitement intégrés")
            print(f"✅ Emails automatiques : Fonctionnels")
            print(f"✅ CMS : Admin → Client opérationnel")
            print(f"✅ Notifications : Temps réel")
            print(f"✅ API : Toutes accessibles")
            print(f"\n🚀 LE SYSTÈME EST PRÊT POUR LA PRODUCTION !")
        elif integration_success:
            print(f"🎉 INTÉGRATION BACKEND ↔ FRONTEND RÉUSSIE !")
            print(f"✅ Toutes les APIs fonctionnent")
            print(f"⚠️  Emails en cours de configuration")
            print(f"🚀 Le système est utilisable")
        else:
            print(f"⚠️  Intégration partielle")
            print(f"🔧 Quelques ajustements nécessaires")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU TEST D'INTÉGRATION: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
