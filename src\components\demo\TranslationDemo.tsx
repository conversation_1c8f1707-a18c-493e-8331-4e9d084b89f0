import React from 'react';
import { useTranslation } from '@/contexts/TranslationContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Globe, Check, ShoppingCart, User, Home, Package } from 'lucide-react';

export const TranslationDemo: React.FC = () => {
  const { t, currentLanguage, availableLanguages, changeLanguage } = useTranslation();

  const demoSections = [
    {
      title: t('demo.navigation', 'Navigation'),
      icon: <Home className="h-4 w-4" />,
      items: [
        { key: 'nav.home', fallback: 'Accueil' },
        { key: 'nav.products', fallback: 'Produits' },
        { key: 'nav.cart', fallback: 'Panier' },
        { key: 'nav.account', fallback: 'Compte' },
        { key: 'nav.contact', fallback: 'Contact' },
      ]
    },
    {
      title: t('demo.authentication', 'Authentification'),
      icon: <User className="h-4 w-4" />,
      items: [
        { key: 'auth.login', fallback: 'Connexion' },
        { key: 'auth.register', fallback: 'Inscription' },
        { key: 'auth.logout', fallback: 'Déconnexion' },
        { key: 'auth.welcome', fallback: 'Bienvenue' },
        { key: 'auth.forgot_password', fallback: 'Mot de passe oublié ?' },
      ]
    },
    {
      title: t('demo.ecommerce', 'E-commerce'),
      icon: <ShoppingCart className="h-4 w-4" />,
      items: [
        { key: 'product.price', fallback: 'Prix' },
        { key: 'product.quantity', fallback: 'Quantité' },
        { key: 'product.add_to_cart', fallback: 'Ajouter au panier' },
        { key: 'cart.total', fallback: 'Total' },
        { key: 'cart.checkout', fallback: 'Passer commande' },
      ]
    },
    {
      title: t('demo.common', 'Interface commune'),
      icon: <Package className="h-4 w-4" />,
      items: [
        { key: 'common.loading', fallback: 'Chargement...' },
        { key: 'common.save', fallback: 'Enregistrer' },
        { key: 'common.cancel', fallback: 'Annuler' },
        { key: 'common.search', fallback: 'Rechercher...' },
        { key: 'common.success', fallback: 'Succès' },
      ]
    }
  ];

  const languageFlags: Record<string, string> = {
    fr: '🇫🇷',
    en: '🇬🇧',
    sw: '🇹🇿',
    rn: '🇧🇮',
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2">
          <Globe className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">
            {t('demo.title', 'Démonstration du Système de Traduction')}
          </h1>
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          {t('demo.description', 'Testez le système de traduction en temps réel. Changez de langue et voyez tous les textes se traduire instantanément.')}
        </p>
      </div>

      {/* Sélecteur de langue */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            {t('demo.language_selector', 'Sélecteur de Langue')}
          </CardTitle>
          <CardDescription>
            {t('demo.current_language', 'Langue actuelle')}: <Badge variant="secondary">{currentLanguage.toUpperCase()}</Badge>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {availableLanguages.map((language) => (
              <Button
                key={language.code}
                variant={language.code === currentLanguage ? "default" : "outline"}
                size="sm"
                onClick={() => changeLanguage(language.code)}
                className="flex items-center gap-2"
              >
                <span>{languageFlags[language.code]}</span>
                <span>{language.name}</span>
                {language.code === currentLanguage && <Check className="h-3 w-3" />}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sections de démonstration */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {demoSections.map((section, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {section.icon}
                {section.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {section.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-center justify-between p-2 rounded bg-muted/50">
                    <code className="text-xs text-muted-foreground">{item.key}</code>
                    <span className="font-medium">{t(item.key, item.fallback)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Informations techniques */}
      <Card>
        <CardHeader>
          <CardTitle>{t('demo.technical_info', 'Informations Techniques')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 rounded bg-muted/50">
              <div className="text-2xl font-bold text-primary">{availableLanguages.length}</div>
              <div className="text-sm text-muted-foreground">
                {t('demo.languages_supported', 'Langues supportées')}
              </div>
            </div>
            <div className="text-center p-4 rounded bg-muted/50">
              <div className="text-2xl font-bold text-primary">36+</div>
              <div className="text-sm text-muted-foreground">
                {t('demo.translations_available', 'Traductions disponibles')}
              </div>
            </div>
            <div className="text-center p-4 rounded bg-muted/50">
              <div className="text-2xl font-bold text-primary">100%</div>
              <div className="text-sm text-muted-foreground">
                {t('demo.coverage', 'Couverture de traduction')}
              </div>
            </div>
          </div>

          <div className="text-sm text-muted-foreground space-y-2">
            <p>
              <strong>{t('demo.features', 'Fonctionnalités')} :</strong>
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>{t('demo.feature_1', 'Traduction en temps réel')}</li>
              <li>{t('demo.feature_2', 'Cache intelligent pour les performances')}</li>
              <li>{t('demo.feature_3', 'Détection automatique des traductions manquantes')}</li>
              <li>{t('demo.feature_4', 'Préférences utilisateur sauvegardées')}</li>
              <li>{t('demo.feature_5', 'Interface d\'administration complète')}</li>
            </ul>
          </div>

          <div className="text-xs text-muted-foreground bg-muted p-3 rounded">
            <strong>{t('demo.usage_example', 'Exemple d\'utilisation')} :</strong>
            <pre className="mt-2 font-mono">
{`const { t } = useTranslation();
<h1>{t('nav.home', 'Accueil')}</h1>`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
