import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { fr } from "date-fns/locale";
import { formatCurrency } from '@/utils/currency';
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Eye, 
  Users, 
  AlertCircle, 
  Download, 
  Calendar, 
  ArrowRight 
} from 'lucide-react';
import { formatPrice } from '@/lib/utils';

// Types
interface ProductMetric {
  name: string;
  value: number;
  change: number;
  period: string;
}

interface ProductSalesData {
  period: string;
  revenue: number;
  orders: number;
  units_sold: number;
}

interface ProductViewsData {
  period: string;
  views: number;
  unique_visitors: number;
  add_to_cart: number;
}

interface ProductConversionData {
  period: string;
  conversion_rate: number;
  cart_abandonment_rate: number;
}

interface ProductDashboardData {
  metrics: {
    revenue: ProductMetric;
    orders: ProductMetric;
    views: ProductMetric;
    conversion: ProductMetric;
  };
  sales_over_time: ProductSalesData[];
  views_over_time: ProductViewsData[];
  conversion_over_time: ProductConversionData[];
  funnel_data: {
    stage: string;
    value: number;
  }[];
  top_referrers: {
    source: string;
    visits: number;
    conversion_rate: number;
  }[];
  device_breakdown: {
    device: string;
    value: number;
  }[];
}

// Mock API service (replace with actual API calls)
const analyticsApi = {
  getProductDashboardData: async (productId: number, period: string = '30d'): Promise<ProductDashboardData> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Generate time series data
    const generateTimeSeries = (days: number, baseValue: number, volatility: number) => {
      const result = [];
      const now = new Date();
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const value = baseValue + (Math.random() * volatility * 2) - volatility;
        result.push({
          date: date.toISOString().split('T')[0],
          value: Math.max(0, value)
        });
      }
      
      return result;
    };
    
    // Determine number of days based on period
    let days = 30;
    if (period === '7d') days = 7;
    if (period === '90d') days = 90;
    if (period === '1y') days = 365;
    
    // Generate sales data
    const revenueData = generateTimeSeries(days, 500, 200);
    const ordersData = generateTimeSeries(days, 10, 5);
    const unitsData = generateTimeSeries(days, 15, 7);
    
    // Generate views data
    const viewsData = generateTimeSeries(days, 200, 50);
    const visitorsData = generateTimeSeries(days, 150, 40);
    const cartAddsData = generateTimeSeries(days, 30, 10);
    
    // Generate conversion data
    const conversionData = generateTimeSeries(days, 5, 2);
    const abandonmentData = generateTimeSeries(days, 70, 15);
    
    // Combine data
    const salesOverTime = revenueData.map((item, index) => ({
      period: item.date,
      revenue: item.value,
      orders: ordersData[index].value,
      units_sold: unitsData[index].value
    }));
    
    const viewsOverTime = viewsData.map((item, index) => ({
      period: item.date,
      views: item.value,
      unique_visitors: visitorsData[index].value,
      add_to_cart: cartAddsData[index].value
    }));
    
    const conversionOverTime = conversionData.map((item, index) => ({
      period: item.date,
      conversion_rate: item.value,
      cart_abandonment_rate: abandonmentData[index].value
    }));
    
    // Calculate totals and changes
    const calculateTotal = (data: any[], key: string) => 
      data.reduce((sum, item) => sum + item[key], 0);
    
    const calculateChange = (data: any[], key: string) => {
      if (data.length < 2) return 0;
      
      const midpoint = Math.floor(data.length / 2);
      const firstHalf = data.slice(0, midpoint);
      const secondHalf = data.slice(midpoint);
      
      const firstHalfTotal = calculateTotal(firstHalf, key);
      const secondHalfTotal = calculateTotal(secondHalf, key);
      
      if (firstHalfTotal === 0) return 100;
      
      return ((secondHalfTotal - firstHalfTotal) / firstHalfTotal) * 100;
    };
    
    return {
      metrics: {
        revenue: {
          name: 'Chiffre d\'affaires',
          value: calculateTotal(salesOverTime, 'revenue'),
          change: calculateChange(salesOverTime, 'revenue'),
          period: period
        },
        orders: {
          name: 'Commandes',
          value: calculateTotal(salesOverTime, 'orders'),
          change: calculateChange(salesOverTime, 'orders'),
          period: period
        },
        views: {
          name: 'Vues produit',
          value: calculateTotal(viewsOverTime, 'views'),
          change: calculateChange(viewsOverTime, 'views'),
          period: period
        },
        conversion: {
          name: 'Taux de conversion',
          value: calculateTotal(conversionOverTime, 'conversion_rate') / conversionOverTime.length,
          change: calculateChange(conversionOverTime, 'conversion_rate'),
          period: period
        }
      },
      sales_over_time: salesOverTime,
      views_over_time: viewsOverTime,
      conversion_over_time: conversionOverTime,
      funnel_data: [
        { stage: 'Vues', value: calculateTotal(viewsOverTime, 'views') },
        { stage: 'Ajouts au panier', value: calculateTotal(viewsOverTime, 'add_to_cart') },
        { stage: 'Commandes', value: calculateTotal(salesOverTime, 'orders') }
      ],
      top_referrers: [
        { source: 'Google', visits: 450, conversion_rate: 5.2 },
        { source: 'Direct', visits: 320, conversion_rate: 7.8 },
        { source: 'Facebook', visits: 280, conversion_rate: 4.1 },
        { source: 'Instagram', visits: 210, conversion_rate: 6.3 },
        { source: 'Email', visits: 180, conversion_rate: 8.5 }
      ],
      device_breakdown: [
        { device: 'Desktop', value: 45 },
        { device: 'Mobile', value: 40 },
        { device: 'Tablet', value: 15 }
      ]
    };
  }
};

interface ProductDashboardProps {
  productId: number;
}

const ProductDashboard: React.FC<ProductDashboardProps> = ({ productId }) => {
  const [period, setPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  
  // Fetch dashboard data
  const { 
    data, 
    isLoading, 
    isError 
  } = useQuery({
    queryKey: ['productDashboard', productId, period],
    queryFn: () => analyticsApi.getProductDashboardData(productId, period),
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      month: 'short',
      day: 'numeric',
    }).format(date);
  };
  
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Tableau de bord produit</CardTitle>
            <CardDescription>
              Analyse des performances et des statistiques du produit
            </CardDescription>
          </div>
          <Select value={period} onValueChange={(value: '7d' | '30d' | '90d' | '1y') => setPeriod(value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sélectionner une période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 derniers jours</SelectItem>
              <SelectItem value="30d">30 derniers jours</SelectItem>
              <SelectItem value="90d">90 derniers jours</SelectItem>
              <SelectItem value="1y">12 derniers mois</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
            <Skeleton className="h-80 w-full" />
          </div>
        ) : isError ? (
          <div className="flex items-center gap-2 p-4 bg-red-50 text-red-700 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span>Une erreur est survenue lors du chargement des données du tableau de bord.</span>
          </div>
        ) : (
          <>
            {/* Key metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <MetricCard
                title={data.metrics.revenue.name}
                value={formatPrice(data.metrics.revenue.value)}
                change={data.metrics.revenue.change}
                icon={<DollarSign className="h-6 w-6" />}
                iconColor="text-green-600"
                iconBgColor="bg-green-100"
              />
              
              <MetricCard
                title={data.metrics.orders.name}
                value={data.metrics.orders.value.toFixed(0)}
                change={data.metrics.orders.change}
                icon={<ShoppingCart className="h-6 w-6" />}
                iconColor="text-blue-600"
                iconBgColor="bg-blue-100"
              />
              
              <MetricCard
                title={data.metrics.views.name}
                value={data.metrics.views.value.toFixed(0)}
                change={data.metrics.views.change}
                icon={<Eye className="h-6 w-6" />}
                iconColor="text-purple-600"
                iconBgColor="bg-purple-100"
              />
              
              <MetricCard
                title={data.metrics.conversion.name}
                value={`${data.metrics.conversion.value.toFixed(2)}%`}
                change={data.metrics.conversion.change}
                icon={<Users className="h-6 w-6" />}
                iconColor="text-amber-600"
                iconBgColor="bg-amber-100"
              />
            </div>
            
            {/* Charts */}
            <Tabs defaultValue="sales">
              <TabsList>
                <TabsTrigger value="sales">Ventes</TabsTrigger>
                <TabsTrigger value="traffic">Trafic</TabsTrigger>
                <TabsTrigger value="conversion">Conversion</TabsTrigger>
                <TabsTrigger value="sources">Sources</TabsTrigger>
              </TabsList>
              
              <TabsContent value="sales" className="pt-4">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={data.sales_over_time}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="period" 
                        tickFormatter={formatDate}
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis 
                        yAxisId="left"
                        tickFormatter={(value) => formatCurrency(value)}
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis 
                        yAxisId="right"
                        orientation="right"
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip 
                        formatter={(value, name) => {
                          if (name === 'revenue') return [`${formatPrice(value as number)}`, 'Chiffre d\'affaires'];
                          if (name === 'orders') return [`${value}`, 'Commandes'];
                          if (name === 'units_sold') return [`${value}`, 'Unités vendues'];
                          return [value, name];
                        }}
                        labelFormatter={formatDate}
                      />
                      <Legend />
                      <Area 
                        type="monotone" 
                        dataKey="revenue" 
                        yAxisId="left"
                        stroke="#8884d8" 
                        fill="#8884d8" 
                        fillOpacity={0.3}
                        name="Chiffre d'affaires"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="orders" 
                        yAxisId="right"
                        stroke="#82ca9d" 
                        fill="#82ca9d" 
                        fillOpacity={0.3}
                        name="Commandes"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
              
              <TabsContent value="traffic" className="pt-4">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={data.views_over_time}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="period" 
                        tickFormatter={formatDate}
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip 
                        formatter={(value) => [`${value}`, '']}
                        labelFormatter={formatDate}
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="views" 
                        stroke="#8884d8" 
                        name="Vues"
                        strokeWidth={2}
                        dot={{ r: 3 }}
                        activeDot={{ r: 5 }}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="unique_visitors" 
                        stroke="#82ca9d" 
                        name="Visiteurs uniques"
                        strokeWidth={2}
                        dot={{ r: 3 }}
                        activeDot={{ r: 5 }}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="add_to_cart" 
                        stroke="#ffc658" 
                        name="Ajouts au panier"
                        strokeWidth={2}
                        dot={{ r: 3 }}
                        activeDot={{ r: 5 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </TabsContent>
              
              <TabsContent value="conversion" className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={data.conversion_over_time}
                        margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="period" 
                          tickFormatter={formatDate}
                          tick={{ fontSize: 12 }}
                        />
                        <YAxis 
                          tickFormatter={(value) => `${value}%`}
                          tick={{ fontSize: 12 }}
                        />
                        <Tooltip 
                          formatter={(value) => [`${value}%`, '']}
                          labelFormatter={formatDate}
                        />
                        <Legend />
                        <Line 
                          type="monotone" 
                          dataKey="conversion_rate" 
                          stroke="#8884d8" 
                          name="Taux de conversion"
                          strokeWidth={2}
                          dot={{ r: 3 }}
                          activeDot={{ r: 5 }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="cart_abandonment_rate" 
                          stroke="#ff7300" 
                          name="Taux d'abandon"
                          strokeWidth={2}
                          dot={{ r: 3 }}
                          activeDot={{ r: 5 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        layout="vertical"
                        data={data.funnel_data}
                        margin={{ top: 10, right: 30, left: 100, bottom: 10 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis 
                          dataKey="stage" 
                          type="category" 
                          tick={{ fontSize: 12 }}
                        />
                        <Tooltip />
                        <Legend />
                        <Bar 
                          dataKey="value" 
                          fill="#8884d8" 
                          name="Nombre"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="sources" className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Top sources de trafic</h3>
                    <div className="space-y-4">
                      {data.top_referrers.map((referrer, index) => (
                        <div key={referrer.source} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                              COLORS[index % COLORS.length].replace('#', 'bg-[') + ']'
                            } text-white`}>
                              {index + 1}
                            </div>
                            <div>
                              <div className="font-medium">{referrer.source}</div>
                              <div className="text-sm text-gray-500">{referrer.visits} visites</div>
                            </div>
                          </div>
                          <Badge className="bg-green-500">
                            {referrer.conversion_rate.toFixed(1)}%
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-4">Répartition par appareil</h3>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={data.device_breakdown}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="device"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {data.device_breakdown.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value) => [`${value}%`, 'Pourcentage']} />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
      
      <CardFooter className="border-t pt-4 flex justify-between">
        <Button variant="outline">
          <Calendar className="mr-2 h-4 w-4" />
          Planifier un rapport
        </Button>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Exporter les données
        </Button>
      </CardFooter>
    </Card>
  );
};

interface MetricCardProps {
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
  iconColor: string;
  iconBgColor: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  iconColor,
  iconBgColor
}) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <h3 className="text-2xl font-bold">{value}</h3>
          </div>
          <div className={`h-12 w-12 ${iconBgColor} rounded-full flex items-center justify-center ${iconColor}`}>
            {icon}
          </div>
        </div>
        <div className="mt-2 flex items-center text-sm">
          {change > 0 ? (
            <div className="flex items-center text-green-600">
              <TrendingUp className="h-4 w-4 mr-1" />
              <span>+{change.toFixed(1)}% vs période précédente</span>
            </div>
          ) : (
            <div className="flex items-center text-red-600">
              <TrendingDown className="h-4 w-4 mr-1" />
              <span>{change.toFixed(1)}% vs période précédente</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductDashboard;
