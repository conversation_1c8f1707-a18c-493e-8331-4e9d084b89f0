// Configuration de l'API
// Utiliser une constante directement au lieu de process.env qui n'est pas disponible
export const API_BASE_URL = 'http://localhost:8000';

// Timeout par défaut pour les requêtes (en millisecondes)
export const DEFAULT_TIMEOUT = 30000;

// Headers par défaut pour les requêtes
export const getDefaultHeaders = (includeAuth = true): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Ajouter le token d'authentification s'il existe et si demandé
  if (includeAuth) {
    const token = localStorage.getItem('authToken');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }

  return headers;
};

// Fonction pour construire une URL complète avec l'URL de base de l'API
export const buildApiUrl = (endpoint: string): string => {
  // Supprimer les slashes au début de l'endpoint si présents
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
  
  // Ajouter un slash à la fin de l'URL de base si nécessaire
  const baseUrl = API_BASE_URL.endsWith('/') ? API_BASE_URL : `${API_BASE_URL}/`;
  
  return `${baseUrl}${cleanEndpoint}`;
};

// Fonction pour construire une URL complète pour les médias
export const buildMediaUrl = (mediaPath: string): string => {
  // Si l'URL est déjà absolue, la retourner telle quelle
  if (mediaPath.startsWith('http://') || mediaPath.startsWith('https://')) {
    return mediaPath;
  }
  
  // Supprimer le slash au début du chemin si présent
  const cleanPath = mediaPath.startsWith('/') ? mediaPath.substring(1) : mediaPath;
  
  // Construire l'URL complète
  return `${API_BASE_URL}/media/${cleanPath}`;
};
