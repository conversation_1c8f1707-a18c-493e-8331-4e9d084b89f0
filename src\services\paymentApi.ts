import axios from 'axios';
import { API_BASE_URL } from '@/config/api';
import { CURRENCY } from '@/config';
import { getAuthToken } from '@/utils/auth';

// Taux de conversion EUR vers BIF
export const EUR_TO_BIF_RATE = 2200;

// Types
export interface PaymentMethod {
  id: number;
  name: string;
  provider: string;
  is_active: boolean;
  is_default: boolean;
  icon: string | null;
  description: string | null;
  position: number;
  fee_fixed: number;
  fee_percentage: number;
  created_at: string;
  updated_at: string;
}

export interface PaymentMethodDetail extends PaymentMethod {
  config: any;
}

export interface Transaction {
  id: number;
  order: number | null;
  payment_method: number;
  payment_method_name: string;
  transaction_id: string;
  amount: number;
  currency: string;
  status: string;
  status_display: string;
  error_message: string | null;
  created_at: string;
  updated_at: string;
  completed_at: string | null;
}

export interface TransactionDetail extends Transaction {
  provider_response: any;
  order_details: any;
}

export interface Invoice {
  id: number;
  order: number;
  invoice_number: string;
  status: string;
  status_display: string;
  issue_date: string;
  due_date: string | null;
  paid_date: string | null;
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total: number;
  pdf_file: string | null;
  created_at: string;
  updated_at: string;
}

export interface InvoiceDetail extends Invoice {
  notes: string | null;
  order_details: any;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  id: number;
  product_name: string;
  variant_name: string | null;
  sku: string;
  price: number;
  quantity: number;
  subtotal: number;
  discount_amount: number;
  final_price: number;
}

export interface CustomerPaymentMethod {
  id: number;
  user: number;
  payment_method: number;
  payment_method_name: string;
  is_default: boolean;
  card_type: string | null;
  last_four: string | null;
  expiry_month: string | null;
  expiry_year: string | null;
  created_at: string;
  updated_at: string;
}

// API service
const paymentApi = {
  // Payment Methods
  getPaymentMethods: async (): Promise<PaymentMethod[]> => {
    const response = await axios.get(`${API_BASE_URL}/payments/payment-methods/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  getActivePaymentMethods: async (): Promise<PaymentMethod[]> => {
    const response = await axios.get(`${API_BASE_URL}/payments/payment-methods/active/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  getPaymentMethod: async (id: number): Promise<PaymentMethodDetail> => {
    const response = await axios.get(`${API_BASE_URL}/payments/payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  createPaymentMethod: async (data: Partial<PaymentMethodDetail>): Promise<PaymentMethodDetail> => {
    const response = await axios.post(`${API_BASE_URL}/payments/payment-methods/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  updatePaymentMethod: async (id: number, data: Partial<PaymentMethodDetail>): Promise<PaymentMethodDetail> => {
    const response = await axios.patch(`${API_BASE_URL}/payments/payment-methods/${id}/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  deletePaymentMethod: async (id: number): Promise<void> => {
    await axios.delete(`${API_BASE_URL}/payments/payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Transactions
  getTransactions: async (params?: any): Promise<{ count: number, results: Transaction[] }> => {
    try {
      const endpoint = params?.user_only
        ? `${API_BASE_URL}/payments/transactions/my_transactions/`
        : `${API_BASE_URL}/payments/transactions/`;

      const { user_only, ...apiParams } = params || {};

      const response = await axios.get(endpoint, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        },
        params: apiParams
      });

      if (Array.isArray(response.data)) {
        return { count: response.data.length, results: response.data };
      } else {
        return response.data;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des transactions:', error);
      throw error;
    }
  },

  getMyTransactions: async (params?: any): Promise<{ count: number, results: Transaction[] }> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/payments/transactions/my_transactions/`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        },
        params
      });

      if (Array.isArray(response.data)) {
        return { count: response.data.length, results: response.data };
      } else {
        return response.data;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des transactions utilisateur:', error);
      throw error;
    }
  },

  getTransaction: async (id: number): Promise<TransactionDetail> => {
    const response = await axios.get(`${API_BASE_URL}/payments/transactions/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  refundTransaction: async (id: number, data: { amount?: number, reason?: string }): Promise<Transaction> => {
    const response = await axios.post(`${API_BASE_URL}/payments/transactions/${id}/refund/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Invoices
  getInvoices: async (params?: any): Promise<{ count: number, results: Invoice[] }> => {
    const response = await axios.get(`${API_BASE_URL}/payments/invoices/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      params
    });
    return response.data;
  },

  getMyInvoices: async (params?: any): Promise<{ count: number, results: Invoice[] }> => {
    const response = await axios.get(`${API_BASE_URL}/payments/invoices/my_invoices/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      params
    });
    return response.data;
  },

  getInvoice: async (id: number): Promise<InvoiceDetail> => {
    const response = await axios.get(`${API_BASE_URL}/payments/invoices/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  downloadInvoice: async (id: number): Promise<Blob> => {
    const response = await axios.get(`${API_BASE_URL}/payments/invoices/${id}/download/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      },
      responseType: 'blob'
    });
    return response.data;
  },

  generateInvoiceForOrder: async (orderId: number): Promise<Invoice> => {
    const response = await axios.post(`${API_BASE_URL}/payments/invoices/generate_for_order/`, { order_id: orderId }, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  // Customer Payment Methods
  getCustomerPaymentMethods: async (): Promise<CustomerPaymentMethod[]> => {
    const response = await axios.get(`${API_BASE_URL}/payments/customer-payment-methods/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  getCustomerPaymentMethod: async (id: number): Promise<CustomerPaymentMethod> => {
    const response = await axios.get(`${API_BASE_URL}/payments/customer-payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  createCustomerPaymentMethod: async (data: Partial<CustomerPaymentMethod>): Promise<CustomerPaymentMethod> => {
    const response = await axios.post(`${API_BASE_URL}/payments/customer-payment-methods/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  setDefaultCustomerPaymentMethod: async (id: number): Promise<CustomerPaymentMethod> => {
    const response = await axios.post(`${API_BASE_URL}/payments/customer-payment-methods/${id}/set_default/`, {}, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  deleteCustomerPaymentMethod: async (id: number): Promise<void> => {
    await axios.delete(`${API_BASE_URL}/payments/customer-payment-methods/${id}/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
  },

  // Payment Processing
  processStripePayment: async (data: any): Promise<Transaction> => {
    const response = await axios.post(`${API_BASE_URL}/payments/process/stripe/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  },

  processPayPalPayment: async (data: any): Promise<Transaction> => {
    const response = await axios.post(`${API_BASE_URL}/payments/process/paypal/`, data, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`
      }
    });
    return response.data;
  }
};

export default paymentApi;
