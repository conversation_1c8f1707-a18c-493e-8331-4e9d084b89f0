/**
 * Authentication utilities for the application
 */

// Get the authentication token from local storage
export const getAuthToken = (): string | null => {
  return localStorage.getItem('accessToken');
};

// Set the authentication token in local storage
export const setAuthToken = (token: string): void => {
  localStorage.setItem('accessToken', token);
};

// Remove the authentication token from local storage
export const removeAuthToken = (): void => {
  localStorage.removeItem('accessToken');
};

// Check if the user is authenticated
export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

// Get the user information from local storage
export const getUserInfo = (): any => {
  const userInfo = localStorage.getItem('user');
  return userInfo ? JSON.parse(userInfo) : null;
};

// Set the user information in local storage
export const setUserInfo = (userInfo: any): void => {
  localStorage.setItem('user', JSON.stringify(userInfo));
};

// Remove the user information from local storage
export const removeUserInfo = (): void => {
  localStorage.removeItem('user');
};

// Logout the user
export const logout = (): void => {
  removeAuthToken();
  removeUserInfo();
};

// Check if the user has a specific role
export const hasRole = (role: string): boolean => {
  const userInfo = getUserInfo();
  return userInfo?.roles?.includes(role) || false;
};

// Check if the user has admin role
export const isAdmin = (): boolean => {
  return hasRole('admin');
};
