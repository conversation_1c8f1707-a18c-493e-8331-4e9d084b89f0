from django.contrib import admin
from .models import Order, OrderItem, OrderStatusHistory, OrderRefund, ShippingMethod

# Import admin extensions for claims and returns
from .admin_extension import ReturnRequestAdmin, ClaimAdmin, ClaimMessageAdmin

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['subtotal', 'final_price']

class OrderStatusHistoryInline(admin.TabularInline):
    model = OrderStatusHistory
    extra = 0
    readonly_fields = ['created_at']

class OrderRefundInline(admin.TabularInline):
    model = OrderRefund
    extra = 0
    readonly_fields = ['refund_number', 'created_at', 'updated_at']

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'full_name', 'email', 'status', 'payment_status', 'total', 'created_at']
    list_filter = ['status', 'payment_status', 'payment_method', 'created_at']
    search_fields = ['order_number', 'email', 'billing_first_name', 'billing_last_name']
    readonly_fields = ['order_number', 'created_at', 'updated_at', 'paid_at', 'shipped_at', 'delivered_at', 'cancelled_at']
    fieldsets = [
        ('Order Information', {
            'fields': ['order_number', 'user', 'email', 'phone', 'status', 'payment_status', 'payment_method', 'payment_reference']
        }),
        ('Billing Information', {
            'fields': [
                'billing_first_name', 'billing_last_name', 'billing_company',
                'billing_address_line1', 'billing_address_line2', 'billing_city',
                'billing_state', 'billing_postal_code', 'billing_country'
            ]
        }),
        ('Shipping Information', {
            'fields': [
                'shipping_first_name', 'shipping_last_name', 'shipping_company',
                'shipping_address_line1', 'shipping_address_line2', 'shipping_city',
                'shipping_state', 'shipping_postal_code', 'shipping_country'
            ]
        }),
        ('Pricing', {
            'fields': ['subtotal', 'shipping_cost', 'tax_amount', 'discount_amount', 'total']
        }),
        ('Tracking', {
            'fields': ['tracking_number', 'shipping_carrier', 'estimated_delivery_date']
        }),
        ('Notes', {
            'fields': ['customer_notes', 'admin_notes']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'updated_at', 'paid_at', 'shipped_at', 'delivered_at', 'cancelled_at']
        }),
    ]
    inlines = [OrderItemInline, OrderStatusHistoryInline, OrderRefundInline]

@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ['order', 'product_name', 'variant_name', 'sku', 'price', 'quantity', 'final_price']
    list_filter = ['created_at']
    search_fields = ['product_name', 'sku', 'order__order_number']
    readonly_fields = ['subtotal', 'final_price', 'created_at', 'updated_at']

@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ['order', 'status', 'created_by', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['order__order_number', 'notes']
    readonly_fields = ['created_at']

@admin.register(OrderRefund)
class OrderRefundAdmin(admin.ModelAdmin):
    list_display = ['refund_number', 'order', 'amount', 'status', 'refund_type', 'created_at']
    list_filter = ['status', 'refund_type', 'created_at']
    search_fields = ['refund_number', 'order__order_number', 'reason']
    readonly_fields = ['refund_number', 'created_at', 'updated_at']
    filter_horizontal = ['items']

@admin.register(ShippingMethod)
class ShippingMethodAdmin(admin.ModelAdmin):
    list_display = ['name', 'price', 'estimated_delivery_days', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
