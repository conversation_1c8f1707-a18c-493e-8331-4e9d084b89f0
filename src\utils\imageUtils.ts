// Constante pour l'URL de base de l'API
import { API_BASE_URL } from '@/config/api';
import { ProductImage } from '@/services/productApi';

// Chemin vers l'image placeholder par défaut
const DEFAULT_PLACEHOLDER = '/placeholder.svg';

/**
 * Formate une URL d'image pour s'assurer qu'elle est complète et valide
 * @param imageUrl URL de l'image à formater
 * @param productId ID du produit (optionnel) pour générer des URL alternatives
 * @returns URL complète de l'image
 */
export const formatImageUrl = (imageUrl?: string, productId?: string | number): string => {
  // Gérer le cas où l'URL est undefined ou null
  if (!imageUrl) {
    return DEFAULT_PLACEHOLDER;
  }
  
  // Gérer les placeholders connus
  if (imageUrl === '/placeholder.svg' || imageUrl === 'placeholder.svg') {
    return DEFAULT_PLACEHOLDER;
  }
  
  // Si l'URL est déjà une URL complète (http:// ou https://)
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }
  
  // Si l'URL est un chemin absolu (commence par /)
  if (imageUrl.startsWith('/')) {
    return imageUrl;
  }
  
  // Sinon, construire l'URL complète avec l'API_BASE_URL
  return `${API_BASE_URL}/media/${imageUrl}`;
};

/**
 * Vérifie si une URL d'image est valide
 * @param url URL à vérifier
 * @returns Promise<boolean> indiquant si l'image est valide
 */
export const isImageValid = async (url: string): Promise<boolean> => {
  // Si l'URL est vide ou le placeholder par défaut, la considérer comme valide
  if (!url || url === DEFAULT_PLACEHOLDER) {
    return true;
  }
  
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok && response.headers.get('Content-Type')?.startsWith('image/');
  } catch (error) {
    console.error(`Erreur lors de la vérification de l'image ${url}:`, error);
    return false;
  }
};

/**
 * Obtient une URL d'image sécurisée, avec fallback sur le placeholder si l'image est invalide
 * @param imageUrl URL de l'image à sécuriser
 * @param productId ID du produit (optionnel)
 * @returns URL d'image sécurisée
 */
export const getSafeImageUrl = (imageUrl?: string, productId?: string | number): string => {
  const formattedUrl = formatImageUrl(imageUrl, productId);
  
  // Retourner l'URL formatée, la validation se fera au niveau du composant si nécessaire
  return formattedUrl;
};

/**
 * Gère les erreurs de chargement d'images en remplaçant par une image par défaut
 * @param event Événement d'erreur de l'élément image
 */
export const handleImageError = (event: React.SyntheticEvent<HTMLImageElement, Event>): void => {
  const target = event.target as HTMLImageElement;
  const originalSrc = target.src;
  
  // Utiliser le chemin correct pour l'image placeholder
  target.src = DEFAULT_PLACEHOLDER;
  
  // Ajouter une classe pour indiquer que l'image est un placeholder
  target.classList.add('placeholder-image');
  
  // Log pour le débogage
  console.warn(`Erreur de chargement de l'image: ${originalSrc}`);
};

/**
 * Extrait l'URL d'image à partir de différentes structures de données d'images
 * @param imageData Données d'image qui peuvent être sous différentes formes
 * @returns URL formatée de l'image
 */
export const getImageUrl = (imageData: any): string => {
  // Log pour le débogage
  console.log('Données d\'image reçues:', imageData);
  
  // Vérification des cas où imageData est undefined ou null
  if (!imageData) {
    console.warn('Données d\'image nulles ou non définies');
    return DEFAULT_PLACEHOLDER;
  }
  
  try {
    // Si imageData est une chaîne, c'est probablement déjà une URL
    if (typeof imageData === 'string') {
      console.log('imageData est une chaîne:', imageData);
      return formatImageUrl(imageData);
    }
    
    // Cas spécifique pour les commandes - vérifier product_details
    if (imageData.product_details && imageData.product_details.image) {
      console.log('Détection d\'image dans product_details:', imageData.product_details.image);
      return formatImageUrl(imageData.product_details.image);
    }
    
    // Si imageData est un objet ProductImage
    if (imageData.image && typeof imageData.image === 'string') {
      console.log('imageData contient une propriété image:', imageData.image);
      return formatImageUrl(imageData.image, imageData.product_id || imageData.product);
    }
    
    // Si imageData a une propriété image_url (certaines API peuvent utiliser ce format)
    if (imageData.image_url && typeof imageData.image_url === 'string') {
      console.log('imageData contient une propriété image_url:', imageData.image_url);
      return formatImageUrl(imageData.image_url);
    }
    
    // Si imageData a une propriété url
    if (imageData.url && typeof imageData.url === 'string') {
      console.log('imageData contient une propriété url:', imageData.url);
      return formatImageUrl(imageData.url);
    }
    
    // Si imageData a une propriété src
    if (imageData.src && typeof imageData.src === 'string') {
      console.log('imageData contient une propriété src:', imageData.src);
      return formatImageUrl(imageData.src);
    }
    
    // Essayons de construire directement l'URL si nous avons un ID de produit et d'image
    if (imageData.id && imageData.product) {
      const directApiUrl = `${API_BASE_URL}/media/products/${imageData.product}/${imageData.id}.jpg`;
      console.log('Essai avec URL construite à partir des IDs:', directApiUrl);
      return directApiUrl;
    }
    
    // Cas spécifique pour les commandes - utiliser le SKU si disponible
    if (imageData.sku) {
      console.log('Utilisation du SKU pour construire l\'URL d\'image:', imageData.sku);
      const skuBasedUrl = `${API_BASE_URL}/media/products/sku/${imageData.sku.toLowerCase().replace(/\s+/g, '-')}.jpg`;
      return skuBasedUrl;
    }
    
    // Cas spécifique pour les commandes - utiliser le nom du produit si disponible
    if (imageData.product_name) {
      console.log('Utilisation du nom du produit pour construire l\'URL d\'image:', imageData.product_name);
      const nameBasedUrl = `${API_BASE_URL}/media/products/name/${imageData.product_name.toLowerCase().replace(/\s+/g, '-')}.jpg`;
      return nameBasedUrl;
    }
  } catch (error) {
    console.error('Erreur lors de l\'extraction de l\'URL d\'image:', error);
    return DEFAULT_PLACEHOLDER;
  }
  
  // Si on ne trouve pas d'URL d'image, utiliser le placeholder
  console.warn('Format d\'image non reconnu:', imageData);
  return DEFAULT_PLACEHOLDER;
};
