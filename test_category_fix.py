#!/usr/bin/env python3
"""
Test de la correction de l'erreur de catégorie dans les retours
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_return_details_after_fix():
    """Tester l'accès aux détails des retours après correction"""
    print("🔧 TEST DE LA CORRECTION DE L'ERREUR DE CATÉGORIE")
    print("=" * 60)
    
    # Authentification
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data, timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json().get('access')
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            print("✅ Authentification réussie")
            
            # Récupérer la liste des retours
            print(f"\n📋 RÉCUPÉRATION DE LA LISTE DES RETOURS:")
            returns_response = requests.get(f"{API_BASE_URL}/orders/returns/", headers=headers, timeout=10)
            
            if returns_response.status_code == 200:
                returns_data = returns_response.json()
                returns = returns_data.get('results', [])
                print(f"   ✅ {len(returns)} retours trouvés")
                
                if returns:
                    return_id = returns[0]['id']
                    return_number = returns[0]['return_number']
                    
                    print(f"   🎯 Test avec retour: {return_number} (ID: {return_id})")
                    
                    # Tester l'endpoint account qui causait l'erreur 500
                    print(f"\n🔍 TEST ENDPOINT ACCOUNT (qui causait l'erreur 500):")
                    account_response = requests.get(f"{API_BASE_URL}/account/returns/{return_id}/", headers=headers, timeout=10)
                    
                    print(f"   📊 Statut: {account_response.status_code}")
                    
                    if account_response.status_code == 200:
                        account_data = account_response.json()
                        print(f"   🎉 SUCCÈS! Plus d'erreur 500")
                        
                        # Analyser les détails des articles
                        items = account_data.get('items', [])
                        print(f"   📦 Articles dans le retour: {len(items)}")
                        
                        if items:
                            print(f"\n   📋 DÉTAILS DES ARTICLES:")
                            for i, item in enumerate(items[:2], 1):  # Afficher les 2 premiers
                                print(f"      {i}. Nom: {item.get('product_name', 'N/A')}")
                                print(f"         Quantité: {item.get('quantity', 'N/A')}")
                                print(f"         Prix: {item.get('price', 'N/A')}")
                                print(f"         Raison: {item.get('reason_display', 'N/A')}")
                                
                                # Vérifier les détails du produit (la partie qui causait l'erreur)
                                product_details = item.get('product_details', {})
                                if product_details:
                                    print(f"         ✅ Détails produit:")
                                    print(f"            ID: {product_details.get('id', 'N/A')}")
                                    print(f"            Nom: {product_details.get('name', 'N/A')}")
                                    print(f"            SKU: {product_details.get('sku', 'N/A')}")
                                    print(f"            Catégorie principale: {product_details.get('category', 'N/A')}")
                                    print(f"            Toutes catégories: {product_details.get('categories', [])}")
                                else:
                                    print(f"         ⚠️ Pas de détails produit")
                                print()
                        
                        return True
                    elif account_response.status_code == 500:
                        print(f"   ❌ ERREUR 500 PERSISTANTE")
                        try:
                            error_text = account_response.text
                            if "AttributeError" in error_text and "category" in error_text:
                                print(f"   📝 L'erreur de catégorie persiste")
                                print(f"   🔧 Vérifiez que le serveur Django a été redémarré")
                            else:
                                print(f"   📝 Autre erreur 500: {error_text[:200]}...")
                        except:
                            print(f"   📝 Impossible de lire les détails de l'erreur")
                        return False
                    else:
                        print(f"   ❌ Autre erreur: {account_response.status_code}")
                        print(f"   📝 Réponse: {account_response.text[:200]}...")
                        return False
                else:
                    print(f"   ⚠️ Aucun retour trouvé pour tester")
                    return False
            else:
                print(f"   ❌ Erreur liste retours: {returns_response.status_code}")
                return False
        else:
            print(f"❌ Échec authentification: {login_response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ SERVEUR NON ACCESSIBLE")
        print(f"   Démarrez le serveur Django: python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def explain_fix():
    """Expliquer la correction apportée"""
    print(f"\n💡 EXPLICATION DE LA CORRECTION:")
    print("=" * 50)
    
    print(f"🐛 PROBLÈME ORIGINAL:")
    print(f"   • Erreur: AttributeError: 'Product' object has no attribute 'category'")
    print(f"   • Ligne: product.category.name if product.category else None")
    print(f"   • Cause: Le modèle Product utilise 'categories' (pluriel) pas 'category'")
    
    print(f"\n🔧 CORRECTION APPORTÉE:")
    print(f"   • Changé: product.category → product.categories.all()")
    print(f"   • Ajouté: Gestion de la relation Many-to-Many")
    print(f"   • Résultat: 'category' (première catégorie) + 'categories' (toutes)")
    
    print(f"\n✅ STRUCTURE CORRIGÉE:")
    print(f"   product_details: {{")
    print(f"     'id': product.id,")
    print(f"     'name': product.name,")
    print(f"     'sku': product.sku,")
    print(f"     'category': 'Première catégorie',  # ✅ Catégorie principale")
    print(f"     'categories': ['Cat1', 'Cat2']     # ✅ Toutes les catégories")
    print(f"   }}")

def main():
    print("🧪 TEST DE LA CORRECTION DE L'ERREUR DE CATÉGORIE")
    print("=" * 70)
    
    # 1. Expliquer la correction
    explain_fix()
    
    # 2. Tester la correction
    fix_ok = test_return_details_after_fix()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Correction de l'erreur: {'✅' if fix_ok else '❌'}")
    
    if fix_ok:
        print(f"\n🎉 CORRECTION RÉUSSIE!")
        print(f"   ✅ Plus d'erreur 500 'AttributeError: category'")
        print(f"   ✅ Les détails des retours sont accessibles")
        print(f"   ✅ Les informations produit sont complètes")
        print(f"   ✅ Les catégories sont correctement récupérées")
        
        print(f"\n🌐 TESTEZ MAINTENANT:")
        print(f"   • Allez sur http://localhost:8080/admin/returns")
        print(f"   • Cliquez sur un retour pour voir les détails")
        print(f"   • L'erreur 500 devrait être résolue")
        print(f"   • Les informations produit devraient s'afficher")
        
    else:
        print(f"\n❌ PROBLÈME PERSISTANT")
        print(f"   🔧 Actions à faire:")
        print(f"      1. Redémarrez le serveur Django")
        print(f"      2. Vérifiez que la correction est bien appliquée")
        print(f"      3. Relancez ce test")

if __name__ == "__main__":
    main()
