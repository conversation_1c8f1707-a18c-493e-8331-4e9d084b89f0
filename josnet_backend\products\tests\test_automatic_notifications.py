"""
Tests pour le système automatique d'envoi d'emails lors de la publication de promotions et produits.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from unittest.mock import patch, MagicMock
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from products.models import Product, Category, Promotion
from core.models import NewsletterSubscription, NewsletterCampaign
from core.services.newsletter_service import NewsletterService

User = get_user_model()


class PromotionEmailNotificationTest(TestCase):
    """Tests pour l'envoi automatique d'emails lors de la publication de promotions."""
    
    def setUp(self):
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )
        
        # Créer des abonnés de test
        self.subscriber1 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=True,
            preferences_new_products=False
        )
        
        self.subscriber2 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=False,
            preferences_new_products=True
        )
        
        self.subscriber3 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=True,
            preferences_new_products=True
        )
    
    @patch('core.services.newsletter_service.NewsletterService.send_promotion_notification')
    def test_promotion_email_sent_on_activation(self, mock_send_notification):
        """Test que l'email est envoyé quand une promotion devient active."""
        mock_send_notification.return_value = {'success': True, 'sent_count': 2}
        
        # Créer une promotion en brouillon
        promotion = Promotion.objects.create(
            title='Test Promotion',
            description='Une super promotion de test',
            promotion_type='percentage',
            discount_percentage=Decimal('20.00'),
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=7),
            status='draft',
            created_by=self.admin_user,
            send_email_notification=True
        )
        
        # Activer la promotion
        promotion.status = 'active'
        promotion.save()
        
        # Vérifier que l'email a été envoyé
        mock_send_notification.assert_called_once()
        
        # Vérifier que la promotion est marquée comme email envoyé
        promotion.refresh_from_db()
        self.assertTrue(promotion.email_sent)
        self.assertIsNotNone(promotion.email_sent_at)
    
    @patch('core.services.newsletter_service.NewsletterService.send_promotion_notification')
    def test_promotion_email_not_sent_if_disabled(self, mock_send_notification):
        """Test que l'email n'est pas envoyé si désactivé."""
        # Créer une promotion avec email désactivé
        promotion = Promotion.objects.create(
            title='Test Promotion',
            description='Une promotion sans email',
            promotion_type='percentage',
            discount_percentage=Decimal('20.00'),
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=7),
            status='active',
            created_by=self.admin_user,
            send_email_notification=False  # Email désactivé
        )
        
        # Vérifier que l'email n'a pas été envoyé
        mock_send_notification.assert_not_called()
        self.assertFalse(promotion.email_sent)
    
    @patch('core.services.newsletter_service.NewsletterService.send_promotion_notification')
    def test_promotion_email_not_sent_twice(self, mock_send_notification):
        """Test que l'email n'est pas envoyé deux fois."""
        mock_send_notification.return_value = {'success': True, 'sent_count': 2}
        
        # Créer une promotion active
        promotion = Promotion.objects.create(
            title='Test Promotion',
            description='Une promotion de test',
            promotion_type='percentage',
            discount_percentage=Decimal('20.00'),
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=7),
            status='active',
            created_by=self.admin_user,
            send_email_notification=True
        )
        
        # Première sauvegarde - email envoyé
        mock_send_notification.assert_called_once()
        
        # Réinitialiser le mock
        mock_send_notification.reset_mock()
        
        # Modifier et sauvegarder à nouveau
        promotion.description = 'Description modifiée'
        promotion.save()
        
        # Vérifier que l'email n'est pas envoyé à nouveau
        mock_send_notification.assert_not_called()
    
    def test_promotion_properties(self):
        """Test des propriétés de la promotion."""
        now = timezone.now()
        
        # Promotion active
        active_promotion = Promotion.objects.create(
            title='Active Promotion',
            description='Promotion active',
            promotion_type='percentage',
            discount_percentage=Decimal('15.00'),
            start_date=now - timedelta(hours=1),
            end_date=now + timedelta(days=5),
            status='active',
            created_by=self.admin_user
        )
        
        self.assertTrue(active_promotion.is_active)
        self.assertFalse(active_promotion.is_expired)
        
        # Promotion expirée
        expired_promotion = Promotion.objects.create(
            title='Expired Promotion',
            description='Promotion expirée',
            promotion_type='percentage',
            discount_percentage=Decimal('25.00'),
            start_date=now - timedelta(days=10),
            end_date=now - timedelta(days=1),
            status='active',
            created_by=self.admin_user
        )
        
        self.assertFalse(expired_promotion.is_active)
        self.assertTrue(expired_promotion.is_expired)
    
    def test_promotion_discount_calculation(self):
        """Test du calcul de réduction."""
        promotion = Promotion.objects.create(
            title='Discount Test',
            description='Test de calcul de réduction',
            promotion_type='percentage',
            discount_percentage=Decimal('20.00'),
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=7),
            status='active',
            created_by=self.admin_user
        )
        
        # Test calcul pourcentage
        discount = promotion.calculate_discount(Decimal('100.00'))
        self.assertEqual(discount, Decimal('20.00'))
        
        # Test avec montant fixe
        promotion.promotion_type = 'fixed_amount'
        promotion.discount_amount = Decimal('15.00')
        promotion.save()
        
        discount = promotion.calculate_discount(Decimal('100.00'))
        self.assertEqual(discount, Decimal('15.00'))
        
        # Test avec montant minimum
        promotion.minimum_order_amount = Decimal('50.00')
        promotion.save()
        
        # Commande en dessous du minimum
        discount = promotion.calculate_discount(Decimal('30.00'))
        self.assertEqual(discount, Decimal('0.00'))
        
        # Commande au-dessus du minimum
        discount = promotion.calculate_discount(Decimal('80.00'))
        self.assertEqual(discount, Decimal('15.00'))


class ProductEmailNotificationTest(TestCase):
    """Tests pour l'envoi automatique d'emails lors de la publication de nouveaux produits."""
    
    def setUp(self):
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )
        
        self.category = Category.objects.create(
            name='Test Category',
            slug='test-category'
        )
        
        # Créer des abonnés de test
        self.subscriber1 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=False,
            preferences_new_products=True
        )
        
        self.subscriber2 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            preferences_promotions=True,
            preferences_new_products=False
        )
    
    @patch('core.services.newsletter_service.NewsletterService.send_new_product_notification')
    def test_new_product_email_sent_on_publish(self, mock_send_notification):
        """Test que l'email est envoyé quand un nouveau produit est publié."""
        mock_send_notification.return_value = {'success': True, 'sent_count': 1}
        
        # Créer un produit en brouillon
        product = Product.objects.create(
            name='Nouveau Produit',
            slug='nouveau-produit',
            sku='NP001',
            description='Description du nouveau produit',
            short_description='Courte description',
            price=Decimal('99.99'),
            status='draft',
            created_by=self.admin_user
        )
        product.categories.add(self.category)
        
        # Publier le produit
        product.status = 'published'
        product.save()
        
        # Vérifier que l'email a été envoyé
        mock_send_notification.assert_called_once()
        
        # Vérifier les arguments de l'appel
        call_args = mock_send_notification.call_args[1]
        self.assertEqual(call_args['product_name'], 'Nouveau Produit')
        self.assertEqual(call_args['product_description'], 'Courte description')
        self.assertEqual(call_args['created_by'], self.admin_user)
    
    @patch('core.services.newsletter_service.NewsletterService.send_new_product_notification')
    def test_product_email_not_sent_for_draft(self, mock_send_notification):
        """Test que l'email n'est pas envoyé pour un produit en brouillon."""
        # Créer un produit en brouillon
        Product.objects.create(
            name='Produit Brouillon',
            slug='produit-brouillon',
            sku='PB001',
            description='Description du produit brouillon',
            price=Decimal('49.99'),
            status='draft',
            created_by=self.admin_user
        )
        
        # Vérifier que l'email n'a pas été envoyé
        mock_send_notification.assert_not_called()
    
    @patch('core.services.newsletter_service.NewsletterService.send_new_product_notification')
    def test_product_email_not_sent_for_existing_published(self, mock_send_notification):
        """Test que l'email n'est pas envoyé pour un produit déjà publié."""
        # Créer un produit déjà publié
        product = Product.objects.create(
            name='Produit Existant',
            slug='produit-existant',
            sku='PE001',
            description='Description du produit existant',
            price=Decimal('79.99'),
            status='published',
            created_by=self.admin_user
        )
        
        # Réinitialiser le mock (car il peut avoir été appelé lors de la création)
        mock_send_notification.reset_mock()
        
        # Modifier le produit (sans changer le statut)
        product.description = 'Description modifiée'
        product.save()
        
        # Vérifier que l'email n'a pas été envoyé
        mock_send_notification.assert_not_called()


class NewsletterServiceIntegrationTest(TestCase):
    """Tests d'intégration pour le service de newsletter."""
    
    def setUp(self):
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )
        
        # Créer des abonnés avec différentes préférences
        self.subscribers = [
            NewsletterSubscription.objects.create(
                email=f'user{i}@example.com',
                preferences_promotions=(i % 2 == 0),
                preferences_new_products=(i % 3 == 0),
                preferences_newsletters=True,
                preferences_order_updates=True
            )
            for i in range(5)
        ]
    
    @patch('core.services.newsletter_service.EmailMultiAlternatives')
    def test_send_campaign_integration(self, mock_email):
        """Test d'intégration pour l'envoi de campagne."""
        mock_email_instance = MagicMock()
        mock_email.return_value = mock_email_instance
        mock_email_instance.send.return_value = True
        
        # Créer une campagne
        campaign = NewsletterService.create_campaign(
            title='Test Campaign',
            subject='Test Subject',
            content='<p>Test content</p>',
            campaign_type='promotion',
            target_preferences=['promotions'],
            created_by=self.admin_user
        )
        
        # Envoyer la campagne
        result = NewsletterService.send_campaign(campaign)
        
        # Vérifier les résultats
        self.assertTrue(result['success'])
        self.assertGreater(result['sent_count'], 0)
        
        # Vérifier que la campagne a été mise à jour
        campaign.refresh_from_db()
        self.assertEqual(campaign.status, 'sent')
        self.assertIsNotNone(campaign.sent_at)
        self.assertGreater(campaign.sent_count, 0)
    
    def test_get_subscribers_by_preferences(self):
        """Test de récupération des abonnés par préférences."""
        # Test pour les promotions (indices pairs: 0, 2, 4)
        promo_subscribers = NewsletterService.get_active_subscribers(['promotions'])
        self.assertEqual(len(promo_subscribers), 3)
        
        # Test pour les nouveaux produits (indices multiples de 3: 0, 3)
        product_subscribers = NewsletterService.get_active_subscribers(['new_products'])
        self.assertEqual(len(product_subscribers), 2)
        
        # Test pour tous les abonnés
        all_subscribers = NewsletterService.get_active_subscribers()
        self.assertEqual(len(all_subscribers), 5)
    
    @patch('core.services.newsletter_service.EmailMultiAlternatives')
    def test_promotion_notification_end_to_end(self, mock_email):
        """Test de bout en bout pour les notifications de promotion."""
        mock_email_instance = MagicMock()
        mock_email.return_value = mock_email_instance
        mock_email_instance.send.return_value = True
        
        # Envoyer une notification de promotion
        result = NewsletterService.send_promotion_notification(
            title='Super Promotion',
            description='Une promotion incroyable !',
            promotion_url='http://example.com/promo',
            created_by=self.admin_user
        )
        
        # Vérifier les résultats
        self.assertTrue(result['success'])
        self.assertEqual(result['sent_count'], 3)  # 3 abonnés aux promotions
        
        # Vérifier qu'une campagne a été créée
        campaign = NewsletterCampaign.objects.filter(campaign_type='promotion').first()
        self.assertIsNotNone(campaign)
        self.assertEqual(campaign.title, 'Promotion: Super Promotion')
        self.assertEqual(campaign.status, 'sent')
