
import { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MessageSquare,
  Search,
  Send,
  Filter,
  ChevronDown,
  User,
  Clock
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

// Données fictives pour les messages
const mockMessages = [
  {
    id: 1,
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>"
    },
    subject: "Question sur ma commande #ORD-12345",
    messages: [
      {
        sender: "customer",
        text: "<PERSON><PERSON><PERSON>, je n'ai pas reçu de confirmation pour ma commande #ORD-12345. Pouvez-vous vérifier son statut ?",
        timestamp: "14/05/2025 14:32"
      },
      {
        sender: "admin",
        text: "Bonjour <PERSON><PERSON>, votre commande a bien été confirmée et est en cours de préparation. Elle sera expédiée demain. Cordialement, l'équipe JOSNET.",
        timestamp: "14/05/2025 15:45"
      }
    ],
    status: "Répondu",
    lastUpdate: "14/05/2025 15:45"
  },
  {
    id: 2,
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>"
    },
    subject: "Problème avec mon produit",
    messages: [
      {
        sender: "customer",
        text: "Bonjour, le routeur que j'ai acheté la semaine dernière ne fonctionne pas correctement. Que puis-je faire ?",
        timestamp: "15/05/2025 09:12"
      }
    ],
    status: "En attente",
    lastUpdate: "15/05/2025 09:12"
  },
  {
    id: 3,
    customer: {
      name: "Pierre Durand",
      email: "<EMAIL>"
    },
    subject: "Demande d'information",
    messages: [
      {
        sender: "customer",
        text: "Bonjour, j'aimerais savoir si vous proposez des installations de réseaux pour les petites entreprises ?",
        timestamp: "14/05/2025 11:20"
      },
      {
        sender: "admin",
        text: "Bonjour M. Durand, nous proposons effectivement des services d'installation et de maintenance réseau pour les professionnels. Je vous invite à consulter notre offre sur notre site ou à nous contacter au 01 23 45 67 89 pour un devis personnalisé.",
        timestamp: "14/05/2025 12:05"
      },
      {
        sender: "customer",
        text: "Merci pour ces informations. Je vais vous contacter par téléphone pour discuter de mon projet.",
        timestamp: "14/05/2025 14:30"
      }
    ],
    status: "En cours",
    lastUpdate: "14/05/2025 14:30"
  },
  {
    id: 4,
    customer: {
      name: "Marie Lambert",
      email: "<EMAIL>"
    },
    subject: "Retour produit",
    messages: [
      {
        sender: "customer",
        text: "Bonjour, je souhaite retourner un article qui ne me convient pas. Comment dois-je procéder ?",
        timestamp: "13/05/2025 16:45"
      },
      {
        sender: "admin",
        text: "Bonjour Mme Lambert, pour effectuer un retour, il vous suffit de vous connecter à votre espace client, section 'Mes commandes', puis de sélectionner la commande et l'article concernés. Vous pourrez ensuite générer une étiquette de retour prépayée. N'hésitez pas si vous avez d'autres questions.",
        timestamp: "13/05/2025 17:30"
      }
    ],
    status: "Répondu",
    lastUpdate: "13/05/2025 17:30"
  },
];

const Messages = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedConversation, setSelectedConversation] = useState<number | null>(1); // Premier message sélectionné par défaut
  const [newMessage, setNewMessage] = useState("");

  // Fonction de filtrage pour la recherche
  const filteredMessages = mockMessages.filter(message => 
    message.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    message.customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    message.subject.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Trouver la conversation sélectionnée
  const selectedConvo = mockMessages.find(msg => msg.id === selectedConversation);

  const handleSendMessage = () => {
    if (newMessage.trim() === "" || !selectedConversation) return;
    
    toast({
      title: "Message envoyé",
      description: "Votre réponse a été envoyée au client",
    });
    
    // Dans une app réelle, cette fonction enverrait le message
    setNewMessage("");
  };

  const handleStatusChange = (status: string) => {
    if (!selectedConversation) return;
    
    toast({
      title: "Statut mis à jour",
      description: `La conversation a été marquée comme "${status}"`,
    });
    
    // Dans une app réelle, cette fonction mettrait à jour le statut
  };

  const formatStatusBadge = (status: string) => {
    switch (status) {
      case "En attente":
        return "bg-yellow-100 text-yellow-800";
      case "En cours":
        return "bg-blue-100 text-blue-800";
      case "Répondu":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Messagerie Client</h1>
          <p className="text-gray-500">Gérez les conversations avec vos clients</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Liste des conversations */}
          <div className="lg:col-span-1">
            <div className="bg-white border rounded-lg">
              <div className="p-4 border-b">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Rechercher une conversation..."
                    className="pl-9"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <div className="px-2 py-2 border-b flex justify-between items-center">
                <span className="text-sm font-medium">
                  {filteredMessages.length} conversation{filteredMessages.length !== 1 ? 's' : ''}
                </span>
                <Button variant="outline" size="sm" className="text-xs flex items-center gap-1">
                  <Filter size={14} />
                  Filtrer
                  <ChevronDown size={14} />
                </Button>
              </div>
              <div className="h-[calc(100vh-400px)] overflow-y-auto">
                {filteredMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`p-3 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedConversation === message.id ? "bg-primary/5 border-l-4 border-l-primary" : ""
                    }`}
                    onClick={() => setSelectedConversation(message.id)}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <h3 className={`font-medium ${selectedConversation === message.id ? "text-primary" : ""}`}>
                        {message.customer.name}
                      </h3>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${formatStatusBadge(message.status)}`}>
                        {message.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-1 line-clamp-1">{message.subject}</p>
                    <div className="text-xs text-gray-500 flex items-center gap-1">
                      <Clock size={12} />
                      <span>{message.lastUpdate}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Conversation active */}
          <div className="lg:col-span-2">
            {selectedConvo ? (
              <div className="bg-white border rounded-lg h-full flex flex-col">
                {/* Entête de la conversation */}
                <div className="p-4 border-b">
                  <div className="flex justify-between items-center">
                    <div>
                      <h2 className="font-medium">{selectedConvo.subject}</h2>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <User size={14} />
                        <span>{selectedConvo.customer.name} ({selectedConvo.customer.email})</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="text-xs"
                        onClick={() => handleStatusChange("Répondu")}
                      >
                        Marquer comme répondu
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Messages de la conversation */}
                <div className="flex-grow p-4 overflow-y-auto bg-gray-50 h-[calc(100vh-500px)]">
                  {selectedConvo.messages.map((msg, idx) => (
                    <div 
                      key={idx}
                      className={`mb-4 ${msg.sender === "admin" ? "flex justify-end" : "flex justify-start"}`}
                    >
                      <div 
                        className={`max-w-[80%] p-3 rounded-lg ${
                          msg.sender === "admin" 
                            ? "bg-primary text-white" 
                            : "bg-white border"
                        }`}
                      >
                        <p className="text-sm">{msg.text}</p>
                        <div className={`text-xs mt-1 text-right ${
                          msg.sender === "admin" ? "text-primary-foreground/80" : "text-gray-500"
                        }`}>
                          {msg.timestamp}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Formulaire de réponse */}
                <div className="p-4 border-t">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Votre réponse..."
                      className="flex-grow"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                    />
                    <Button 
                      onClick={handleSendMessage}
                      className="flex-shrink-0"
                      disabled={newMessage.trim() === ""}
                    >
                      <Send size={16} className="mr-2" />
                      Envoyer
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white border rounded-lg h-full flex items-center justify-center p-8">
                <div className="text-center">
                  <MessageSquare size={48} className="text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium">Aucune conversation sélectionnée</h3>
                  <p className="text-gray-500 mt-1">Sélectionnez une conversation pour afficher les messages.</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Messages;
