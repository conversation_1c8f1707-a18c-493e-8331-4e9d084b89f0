import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { accountApi } from '@/services/accountApi';
import { ProductNameAutocomplete } from '@/components/ui/product-name-autocomplete';

// Définir le schéma de validation
const suggestionFormSchema = z.object({
  productName: z.string()
    .min(1, { message: 'Le nom du produit est requis' })
    .min(3, { message: 'Le nom du produit doit contenir au moins 3 caractères' }),
  category: z.string().min(1, { message: 'Veuillez sélectionner une catégorie' }),
  description: z.string()
    .min(1, { message: 'La description est requise' })
    .min(10, { message: 'La description doit contenir au moins 10 caractères' }),
  estimatedPrice: z.string().optional(),
  reason: z.string()
    .min(1, { message: 'La raison est requise' })
    .min(10, { message: 'Veuillez expliquer en détail pourquoi vous souhaitez ce produit' }),
});

type SuggestionFormValues = z.infer<typeof suggestionFormSchema>;

interface ProductSuggestionFormProps {
  onSuccess?: () => void;
  isEditing?: boolean;
  initialValues?: {
    productName: string;
    category: string;
    description: string;
    estimatedPrice?: string;
    reason: string;
  };
  onSubmit?: (values: SuggestionFormValues) => void;
}

const ProductSuggestionForm: React.FC<ProductSuggestionFormProps> = ({ 
  onSuccess, 
  isEditing = false,
  initialValues,
  onSubmit: externalSubmit
}) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Récupérer les catégories dynamiquement depuis la base de données
  const { data: productCategories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['productCategories'],
    queryFn: () => accountApi.getProductCategories(),
    retry: 1,
    retryDelay: 1000,
  });

  // Catégories disponibles (avec fallback)
  const categories = productCategories.length > 0 ? [...productCategories, 'Autre'] : [
    'Réseaux',
    'Sécurité',
    'Stockage',
    'Accessoires',
    'Serveurs',
    'Logiciels',
    'Autre'
  ];

  // Initialiser le formulaire
  const form = useForm<SuggestionFormValues>({
    resolver: zodResolver(suggestionFormSchema),
    mode: 'onBlur', // Validation seulement quand l'utilisateur quitte le champ
    reValidateMode: 'onBlur', // Re-validation aussi en onBlur
    defaultValues: initialValues || {
      productName: '',
      category: '',
      description: '',
      estimatedPrice: '',
      reason: '',
    },
  });

  // Gérer la soumission du formulaire
  const onSubmit = async (values: SuggestionFormValues) => {
    console.log('Form submission values:', values);
    console.log('Form errors:', form.formState.errors);

    setIsSubmitting(true);
    try {
      // Si un gestionnaire de soumission externe est fourni, l'utiliser
      if (externalSubmit) {
        externalSubmit(values);
      } else {
        // Sinon, appeler l'API pour soumettre la suggestion
        await accountApi.createProductSuggestion({
          name: values.productName,
          category: values.category,
          description: values.description,
          estimated_price: values.estimatedPrice || '0',
          reason: values.reason,
        });

        // Afficher un message de succès
        toast({
          title: 'Suggestion envoyée',
          description: 'Merci pour votre suggestion ! Nous l\'examinerons prochainement.',
        });
      }

      // Réinitialiser le formulaire si ce n'est pas en mode édition
      if (!isEditing) {
        form.reset();
      }

      // Appeler le callback de succès si fourni
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la suggestion:', error);
      toast({
        title: 'Erreur',
        description: 'Une erreur est survenue lors de l\'envoi de votre suggestion. Veuillez réessayer.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Suggérer un produit</CardTitle>
        <CardDescription>
          Vous ne trouvez pas un produit que vous aimeriez acheter ? Faites-nous part de votre suggestion !
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="productName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom du produit</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <Input
                        {...field}
                        placeholder="Tapez le nom du produit que vous souhaitez suggérer..."
                        className={form.formState.errors.productName ? "border-red-500" : ""}
                        onChange={(e) => {
                          console.log('Input onChange:', e.target.value);
                          field.onChange(e.target.value);
                        }}
                      />
                      {/* Optionnel: Autocomplétion en dessous */}
                      {field.value && field.value.length >= 2 && (
                        <div className="text-xs text-gray-500">
                          💡 Suggestion: Vous pouvez aussi utiliser l'autocomplétion ci-dessous
                        </div>
                      )}
                      <ProductNameAutocomplete
                        value=""
                        onValueChange={(value, category) => {
                          console.log('ProductNameAutocomplete onValueChange:', { value, category });
                          if (value) {
                            field.onChange(value);
                            // Si une catégorie est fournie et que le champ catégorie est vide, la définir automatiquement
                            if (category && !form.getValues('category')) {
                              form.setValue('category', category);
                            }
                          }
                        }}
                        placeholder="Ou recherchez dans nos produits existants..."
                        error={false}
                        allowFreeInput={true}
                        className="mt-2"
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Tapez directement le nom du produit souhaité, ou utilisez la recherche pour voir s'il existe déjà.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catégorie</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoadingCategories}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={isLoadingCategories ? "Chargement des catégories..." : "Sélectionnez une catégorie"} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description du produit</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Décrivez le produit que vous souhaitez voir dans notre catalogue" 
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="estimatedPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prix estimé en BIF (optionnel)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Ex: 220,000 BIF"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Indiquez le prix approximatif en Francs Burundais (BIF) que vous seriez prêt à payer pour ce produit
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pourquoi souhaitez-vous ce produit ?</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Expliquez pourquoi vous souhaitez que nous ajoutions ce produit à notre catalogue" 
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditing ? 'Mise à jour en cours...' : 'Envoi en cours...'}
                </>
              ) : (
                isEditing ? 'Mettre à jour ma suggestion' : 'Envoyer ma suggestion'
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default ProductSuggestionForm;
